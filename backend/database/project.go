package database

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func GetProject(sctx mongo.SessionContext, id string) (models.Project, error) {
	OID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return models.Project{}, errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": OID}).Decode(&project)
	if err != nil {
		return models.Project{}, errors.WithStack(err)
	}
	return project, nil
}
