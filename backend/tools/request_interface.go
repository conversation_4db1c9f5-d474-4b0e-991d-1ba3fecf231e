package tools

import (
	"clinflash-irt/models"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strings"
)

// post请求（异步请求） v2.11作废
func HttpPost(url string, parameter models.Content) (string, models.HTTPRspBody, error) {
	var (
		errMsg string
	)

	// unmarshal: 解析HTTP返回的结果
	var httpRspBody models.HTTPRspBody

	// 换个对象数据组装
	var pushData []models.PushData
	for _, sd := range parameter.SubjectData {
		pushData = append(pushData, models.PushData{
			Field:            sd.Field,
			Value:            sd.Value,
			Visit:            sd.Visit,
			InstanceRepeatNo: sd.InstanceRepeatNo,
			BlockRepeatNo:    sd.BlockRepeatNo,
		})
	}
	// json.Marshal
	reqParam, err := json.Marshal(&models.PushContent{
		Env:              parameter.Env,
		Project:          parameter.Project,
		Sign:             parameter.Sign,
		Site:             parameter.Site,
		IrtSubjectID:     parameter.IrtSubjectID,
		ReplaceSubjectID: parameter.ReplaceSubjectID,
		ReplaceSubjectNo: parameter.ReplaceSubjectNo,
		EdcPushId:        parameter.EdcPushId,
		EdcPushLogId:     parameter.EdcPushLogId,
		SubjectNoPrefix:  parameter.SubjectNoPrefix,
		SubjectNo:        parameter.SubjectNo,
		Timestamp:        parameter.Timestamp,
		Type:             parameter.Type,
		//AsyncFlag:    parameter.AsyncFlag,
		Remarks:     parameter.Remarks,
		Cohort:      parameter.Cohort,
		SubjectData: pushData,
	})

	if err != nil {
		return "", httpRspBody, err
	}

	// url处理
	var requestUrl strings.Builder
	strArray := strings.Split(url, "/")
	for index, value := range strArray {
		if index == 0 {
			requestUrl.WriteString(value)
			requestUrl.WriteString("//")
		} else if index == 2 {
			requestUrl.WriteString(value)
			requestUrl.WriteString("/api/")
		} else if index == 3 {
			requestUrl.WriteString(value)
		}
	}
	requestUrl.WriteString("/openapi/clinflashIrt")
	reqBody := strings.NewReader(string(reqParam))

	httpReq, err := http.NewRequest("POST", requestUrl.String(), reqBody)
	if err != nil {
		return "", httpRspBody, err
	}
	httpReq.Header.Add("Content-Type", "application/json")

	// DO: HTTP请求
	httpRsp, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		return "", httpRspBody, err
	}
	defer httpRsp.Body.Close()

	// Read: HTTP结果
	rspBody, err := ioutil.ReadAll(httpRsp.Body)
	if err != nil {
		return "", httpRspBody, err
	}
	if err = json.Unmarshal(rspBody, &httpRspBody); err != nil {
		return "", httpRspBody, err
	}
	return fmt.Sprintf("url: %s, reqBody: %s, body: %s %s", requestUrl.String(), reqBody, string(rspBody), errMsg), httpRspBody, nil
}

// TODO post请求（同步请求） v2.11新增
func HttpPostNew(url string, parameter models.Content) (string, models.HTTPRspBodyNew, error) {
	var (
		errMsg string
	)

	// unmarshal: 解析HTTP返回的结果
	var httpRspBodyNew models.HTTPRspBodyNew

	// 换个对象数据组装
	var pushData []models.PushData
	for _, sd := range parameter.SubjectData {
		pushData = append(pushData, models.PushData{
			Field:            sd.Field,
			Value:            sd.Value,
			Visit:            sd.Visit,
			InstanceRepeatNo: sd.InstanceRepeatNo,
			BlockRepeatNo:    sd.BlockRepeatNo,
		})
	}
	// json.Marshal
	reqParam, err := json.Marshal(&models.PushContent{
		Env:              parameter.Env,
		Project:          parameter.Project,
		Sign:             parameter.Sign,
		Site:             parameter.Site,
		IrtSubjectID:     parameter.IrtSubjectID,
		ReplaceSubjectID: parameter.ReplaceSubjectID,
		ReplaceSubjectNo: parameter.ReplaceSubjectNo,
		EdcPushId:        parameter.EdcPushId,
		EdcPushLogId:     parameter.EdcPushLogId,
		SubjectNoPrefix:  parameter.SubjectNoPrefix,
		SubjectNo:        parameter.SubjectNo,
		Timestamp:        parameter.Timestamp,
		Type:             parameter.Type,
		//AsyncFlag:    parameter.AsyncFlag,
		Remarks:     parameter.Remarks,
		Cohort:      parameter.Cohort,
		SubjectData: pushData,
		OldCohort:   parameter.OldCohort,
	})

	if err != nil {
		return "", httpRspBodyNew, err
	}

	// url处理
	var requestUrl strings.Builder
	strArray := strings.Split(url, "/")
	for index, value := range strArray {
		if index == 0 {
			requestUrl.WriteString(value)
			requestUrl.WriteString("//")
		} else if index == 2 {
			requestUrl.WriteString(value)
			requestUrl.WriteString("/api/")
		} else if index == 3 {
			requestUrl.WriteString(value)
		}
	}
	requestUrl.WriteString("/openapi/clinflashIrt/sync")
	reqBody := strings.NewReader(string(reqParam))

	httpReq, err := http.NewRequest("POST", requestUrl.String(), reqBody)
	if err != nil {
		return "", httpRspBodyNew, err
	}
	httpReq.Header.Add("Content-Type", "application/json")

	// DO: HTTP请求
	httpRsp, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		return "", httpRspBodyNew, err
	}
	defer httpRsp.Body.Close()

	// Read: HTTP结果
	rspBody, err := ioutil.ReadAll(httpRsp.Body)
	if err != nil {
		return "", httpRspBodyNew, err
	}
	if err = json.Unmarshal(rspBody, &httpRspBodyNew); err != nil {
		return "", httpRspBodyNew, err
	}
	return fmt.Sprintf("url: %s, reqBody: %s, body: %s %s", requestUrl.String(), reqBody, string(rspBody), errMsg), httpRspBodyNew, nil
}

// post请求获取EDC的受试者状态
func HttpPostStatus(url string, parameter map[string]interface{}) (models.HTTPObtainEdcStatus, string, string, error) {
	var (
		errMsg string
	)

	// unmarshal: 解析HTTP返回的结果
	var httpObtainEdcStatus models.HTTPObtainEdcStatus
	// json.Marshal
	reqParam, err := json.Marshal(&parameter)
	if err != nil {
		log.Printf("%+v\n", err)
		return httpObtainEdcStatus, "", "", err
	}
	// url处理
	var requestUrl strings.Builder
	strArray := strings.Split(url, "/")
	for index, value := range strArray {
		if index == 0 {
			requestUrl.WriteString(value)
			requestUrl.WriteString("//")
		} else if index == 2 {
			requestUrl.WriteString(value)
			requestUrl.WriteString("/api/")
		} else if index == 3 {
			requestUrl.WriteString(value)
		}
	}
	requestUrl.WriteString("/openapi/clinflashIrt/status")
	reqBody := strings.NewReader(string(reqParam))
	log.Printf("%+v\n", reqBody)
	httpReq, err := http.NewRequest("POST", requestUrl.String(), reqBody)
	if err != nil {
		log.Printf("%+v\n", err)
		return httpObtainEdcStatus, string(reqParam), "", err
	}
	httpReq.Header.Add("Content-Type", "application/json")

	// DO: HTTP请求
	httpRsp, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		log.Printf("%+v\n", err)
		return httpObtainEdcStatus, string(reqParam), "", err
	}
	defer httpRsp.Body.Close()

	// Read: HTTP结果
	rspBody, err := ioutil.ReadAll(httpRsp.Body)
	if err != nil {
		log.Printf("%+v\n", err)
		return httpObtainEdcStatus, string(reqParam), string(rspBody), err
	}
	log.Printf("%+v\n", fmt.Sprintf("url: %s, reqBody: %s, body: %s %s", requestUrl.String(), reqBody, string(rspBody), errMsg))

	if err = json.Unmarshal(rspBody, &httpObtainEdcStatus); err != nil {
		log.Printf("%+v\n", err)
		return httpObtainEdcStatus, string(reqParam), string(rspBody), err
	}

	return httpObtainEdcStatus, string(reqParam), string(rspBody), nil
}
