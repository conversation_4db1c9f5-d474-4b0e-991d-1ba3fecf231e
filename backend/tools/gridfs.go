package tools

import (
	"bytes"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/gridfs"
	"time"
)

type gridFS struct {
	Bucket *gridfs.Bucket
}

type GridFS interface {
	Upload(filename string, content []byte) (primitive.ObjectID, error)
	GetFilesInfo(filter bson.M) ([]*GridFSFile, error)
	Download(fileID primitive.ObjectID) ([]byte, error)
	Delete(fileID primitive.ObjectID) error
}

func NewGridFS() GridFS {
	return &gridFS{
		Bucket: Bucket,
	}
}

type GridFSFile struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	Name       string             `bson:"filename"`
	Length     int64              `bson:"length"`
	ChunkSize  int64              `json:"chunkSize" bson:"chunkSize"`
	UploadDate time.Time          `json:"uploadDate" bson:"uploadDate"`
}

func (g *gridFS) Upload(filename string, content []byte) (primitive.ObjectID, error) {
	id, err := g.Bucket.UploadFromStream(filename, bytes.NewBuffer(content))
	if err != nil {
		return [12]byte{}, err
	}

	return id, nil
}

func (g *gridFS) GetFilesInfo(filter bson.M) ([]*GridFSFile, error) {
	cursor, err := g.Bucket.Find(filter)
	if err != nil {
		return nil, err
	}

	var files []*GridFSFile
	if err = cursor.All(context.Background(), &files); err != nil {
		return nil, err
	}

	return files, nil
}

func (g *gridFS) Download(fileID primitive.ObjectID) ([]byte, error) {
	fileBuffer := bytes.NewBuffer(nil)
	_, err := g.Bucket.DownloadToStream(fileID, fileBuffer)
	if err != nil {
		return nil, err
	}

	return fileBuffer.Bytes(), nil
}

func (g *gridFS) Delete(fileID primitive.ObjectID) error {
	if err := g.Bucket.Delete(fileID); err != nil {
		return err
	}

	return nil
}
