package convert

import (
	"clinflash-irt/models"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CloudUserToUser(user *models.User, cloudUser *models.UserData) (models.User, error) {
	user.UserInfo.Name = cloudUser.Info.Name
	user.UserInfo.Email = cloudUser.Info.Email
	user.UserInfo.Phone = cloudUser.Info.Mobile
	user.UserInfo.Company = cloudUser.Info.Company
	user.UserInfo.Description = cloudUser.Info.Description
	uid, err := primitive.ObjectIDFromHex(cloudUser.Id)
	if err != nil {
		return models.User{}, errors.WithStack(err)
	}
	user.CloudId = uid
	return *user, nil
}
