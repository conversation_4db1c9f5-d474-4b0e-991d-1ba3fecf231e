[email]
alarm.storehouse.content = <p>The number of available IP is lower than the warning value, please re-supply in time.IP information ([warehouse name / IP name / Alert quantity / remaining quantity]):{{.drugInfo}}</p>
alarm.storehouse.title = Clinflash IRT {{.projectNumber}} {{.envName}} Depot Inventory Alert {{.deportName}}
shipmentMode.status.set = Quantity
shipmentMode.status.reSupply = Re-supply
shipmentMode.status.max = Maximum Buffer
shipmentMode.status.supplyRatio = Supply Ratio
cross.check.error = Clinflash IRT Exception Notification
dispensing.plan = <p>{{.labelEn}}:{{.subjectNumber}}</p><p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Remark:{{.remark}}</p>
dispensing.unscheduled-plan = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Unscheduled dispensation reason:{{.reason}}</p>
dispensing.plan-logistics = <p>{{.labelEn}}:{{.subjectNumber}}</p><p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Remark:{{.remark}}</p>
dispensing.unscheduled-plan-logistics = <p>{{.labelEn}}:{{.subjectNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Unscheduled dispensation reason:{{.reason}}</p>
dispensing.plan-title = Clinflash IRT  {{.projectNumber}} {{.envName}}   IP Dispensation notificaiton {{.siteNumber}} {{.siteNameEn}}
dispensing.unscheduled-plan-title = Clinflash IRT  {{.projectNumber}} {{.envName}}  IP Dispensation {{.unscheduledEn}}  {{.siteNumber}} {{.siteNameEn}}
dispensing.reissue = <p>{{.labelEn}}:{{.subjectNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Re-dispensation reason:{{.remark}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>
dispensing.reissue-logistics = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Dispensation Time:{{.dispensingDate}}</p> <p>Re-dispensation reason:{{.remark}}</p>
dispensing.reissue-title = Clinflash IRT {{.projectNumber}} {{.envName}}  IP Re-dispensation {{.siteNumber}} {{.siteNameEn}}
dispensing.replace = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.replaceNumber}}</p> <p>IP Number(Replaced):{{.drugNumber}}</p> <p>Replace Time:{{.dispensingDate}}</p> <p>Reason for Replacement:{{.reason}}</p>
dispensing.replace-title = Clinflash IRT {{.projectNumber}} {{.envName}}  IP Replacement {{.siteNumber}} {{.siteNameEn}}
dispensing.apply = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Remark:{{.remark}}</p>  <p>Dispense Application Time:{{.dispensingDate}}</p>
dispensing.apply-title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Dispense application {{.siteNumber}} {{.siteNameEn}}
dispensing.reissue-dtp = <p>{{.labelEn}}:{{.subjectNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Re-dispensation Reason:{{.remark}}</p>  <p>Re-Dispensation Application Time:{{.dispensingDate}}</p>
dispensing.reissue-dtp-title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Re-dispensation application {{.siteNumber}} {{.siteNameEn}}
dispensing.unscheduled-apply = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Reason:{{.reason}}</p>  <p>Re-Dispensation Application Time:{{.dispensingDate}}</p>
dispensing.unscheduled-apply-title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Dispense application Unscheduled Visit {{.siteNumber}} {{.siteNameEn}}
dispensing.replace-dtp = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.replaceNumber}}</p> <p>IP Number(Replaced):{{.drugNumber}}</p>  <p>Shipment Number: {{.orderNumber}}</p>   <p>Replace Time:{{.dispensingDate}}</p> <p>Reason for Replacement:{{.reason}}</p>
dispensing.replace-dtp-title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Replacement {{.siteNumber}} {{.siteNameEn}}
dispensing.register-title = Clinflash IRT {{.projectNumber}} {{.envName}} Registration of Actually Used IP Notification {{.siteNumber}} {{.siteNameEn}}
dispensing.register = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Dispensation Time:{{.dispensingTime}}</p> <p>Remark:{{.remark}}</p> <p>Actual IP:{{.registerNumber}}</p> <p>Operation Time:{{.dispensingDate}}</p>
dispensing.retrieval-title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Retrieval {{.siteNumber}} {{.siteNameEn}}
dispensing.retrieval = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Dispensation Time:{{.dispensingTime}}</p> <p>Remark:{{.remark}}</p> <p>Retrieve IP:{{.retrievalNumber}}</p> <p>Operation Time:{{.dispensingDate}}</p>
dispensing.group = <p>Group:{{.group}}</p>
dispensing.subGroup = <p>Sub Group:{{.subGroup}}</p>
dispensing.number = <p>Randomization ID :{{.random_number}}</p>
dispensing.not-attend-title = Clinflash IRT {{.projectNumber}} {{.envName}} Do Not Attend Visit {{.siteNumber}} {{.siteNameEn}}
dispensing.single.subject = <p>{{.labelEn}}:{{.subjectNumber}}</p>
dispensing.single.drugNumber = <p>IP Number:{{.drugNumber}}</p>
dispensing.single.dispensingDate = <p>Dispensation Time:{{.dispensingDate}}</p>
dispensing.single.replaceTime = <p>Replace Time:{{.replaceTime}}</p>
dispensing.single.orderNumber = <p>Order Number:{{.orderNumber}}</p>
dispensing.single.visitCycle = <p>Visit Cycle:{{.visitCycleEn}}</p>
dispensing.single.remark = <p>Remark:{{.remark}}</p>
dispensing.single.registerNumber = <p>Actual IP:{{.registerNumber}}</p>
dispensing.single.retrievalNumber = <p>Retrieve IP:{{.retrievalNumber}}</p>
dispensing.single.dispensingTime = <p>Operation Time:{{.dispensingTime}}</p>
dispensing.single.replaceNumber = <p>IP Number:{{.replaceNumber}}</p>
dispensing.single.beReplaceNumber = <p>IP Number(Replaced):{{.beReplaceNumber}}</p>
dispensing.single.unscheduled-dispensing-reason = <p>Unscheduled dispensation reason:{{.reason}}</p>
dispensing.single.unscheduled-dispensing-reason-customer = <p>{{.dispensingTypeEn}} Reason:{{.reason}}</p>
dispensing.single.re-dispensing-reason = <p>Re-dispensation reason:{{.reason}}</p>
dispensing.single.replace-reason = <p>Reason for Replacement:{{.reason}}</p>
batch-group.alert-site-title = Clinflash IRT  {{.projectNumber}} {{.envName}} Subject Site Alert {{.siteNumber}} {{.siteNameEn}}
batch-group.alert-depot-title = Clinflash IRT  {{.projectNumber}} {{.envName}} Subject Depot Alert {{.depotName}}
batch-group.limit-site-title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Reminder  {{.siteNumber}} {{.siteNameEn}}
batch-group.limit-depot-title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Reminder {{.depotName}}
batch-group.depotName = Deport:{{.depotName}}
batch-group.siteName = Site Name:{{.siteNameEn}}
batch-group.alarm = <p>The number of subjects enrolled in different batches reached the alert value.</p><p>Batch/Alert Value/Actual Number:  {{.alarm}}</p>
batch-group.limit = <p>The number of subjects enrolled in different batches reached the alert value.</p><p>Batch/Alert Value/Actual Number:  {{.limit}}</p>
footer = This email is delivered by system automatically. Please do not reply to the email since the mailbox is not monitored..<br>
mail.test = Test
mail.text = Test {{.Name}}
medicine.expire_title = Clinflash IRT  {{.projectNumber}} {{.envName}} IP Expiry Alert {{.instituteNumber}} {{.instituteName}}
medicine.freeze.title = Clinflash IRT  {{.projectNumber}} ({{.envName}}) IP Quarantine Notification {{.instituteInfoEn}} {{.freezeNumber}}
medicine.release.title = Clinflash IRT  {{.projectNumber}} ({{.envName}}) IP Release Notification {{.instituteInfoEn}} {{.freezeNumber}}
order.automatic_error = <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Failed to create automatic shipment. Please check whether the automatic IP Dispensation Quantity matches the stock IP quantity.</p>
order.automatic_error_dual = <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Failed to generate an automatic IP shipment. Please check if the depot inventory is sufficient to cover the re-supply value.</p> <p></p>
order.automatic_error_title = Clinflash IRT {{.projectNumber}} {{.envName}} Automatic shipment creation failed alert {{.destinationEn}}
order.automatic_alarm_title = Clinflash IRT {{.projectNumber}}({{.envName}}) Automatic Shipment Alert
order.automatic_success_title = Clinflash IRT {{.projectNumber}}({{.envName}}) Automatic Shipment Notification {{.destinationEn}} {{.orderNumber}}
order.cancel =  <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Cancellation Reanson:{{.reason}}</p> <p>This shipment has been cancelled.</p>
order.cancel-logistics = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Cancellation Reason:{{.reason}}</p> <p>This shipment has been cancelled.</p>
order.cancel_dtp = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Cancellation Reanson:{{.reason}}</p> <p>This shipment has been cancelled.</p>
order.cancel_dtp_sub = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Cancellation Reanson:{{.reason}}</p> <p>This shipment has been cancelled.</p>
order.close = <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Close Reason:{{.reason}}</p> <p>This shipment has been closed.</p>
order.close-logistics = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Close Reason:{{.reason}}</p> <p>This shipment has been closed.</p>
order.close_dtp = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Close Reason:{{.reason}}</p> <p>This shipment has been closed.</p>
order.close_dtp_sub = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Visit Name:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Close Reason:{{.reason}}</p> <p>This shipment has been closed.</p>
order.end = <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Terminate Reason:{{.reason}}</p> <p>This shipment has been terminated.</p>
order.end-logistics = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express Logistics:{{.logistics}}</p> <p>Terminate Reason:{{.reason}}</p> <p>This shipment has been terminated.</p>
order.lost_dtp = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Order Number:{{.orderNumber}}</p> <p>Loss Reason:{{.reason}}</p> <p>{{.userName}} confirmed this shipment was lost.</p>
order.lost_dtp_sub = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p <p>Visit Cycle:{{.visitEn}}</p> <p>Order Number:{{.orderNumber}}</p> <p>Loss Reason:{{.reason}}</p> <p>{{.userName}} confirmed this shipment was lost.</p>
order.cancel_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Cancellation Notification {{.destinationEn}} {{.orderNumber}}
order.close_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Closure Notification {{.destinationEn}} {{.orderNumber}}
order.end_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Terminated Notification {{.destinationEn}} {{.orderNumber}}
order.cancel_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} Shipment Cancellation Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.close_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Closure Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.change_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP  Update  {{.destinationEn}} {{.orderNumber}}
order.change_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} IP  Update  {{.subject}} {{.visit}} {{.orderNumber}}
order.batch_expiration_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} IP Expiry Update {{.orderNumber}}
order.lost = <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Loss Reason:{{.reason}}</p> <p>{{.userName}} confirmed this shipment was lost.</p>
order.lost_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Lost Notification {{.destinationEn}} {{.orderNumber}}
order.lost_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Lost Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.no_automatic = <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site number:{{.siteNumber}}</p> <p>Site name:{{.siteNameEn}}</p> <p>The IP in stock are lower than the warning value, please replenish them in time</p>
order.no_automatic_dual = <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>中心编号:{{.siteNumber}}</p> <p>中心名称:{{.siteName}}</p> <p>库存研究产品低于警戒值,请及时补充.</p> <p></p> <p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site Number:{{.siteNumber}}</p> <p>Site Name:{{.siteNameEn}}</p> <p>The IP quantity in stock is below the alert value, please replenish in time.</p>
order.no_automatic_success_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Confirmation Notification {{.destinationEn}} {{.orderNumber}}
order.medicine_order_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Creation Notification {{.destinationEn}} {{.orderNumber}}
order.medicine_order_dtp_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Creation Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.forecast_title = Clinflash IRT {{.projectNumber}} {{.envName}} Site Inventory Usage Time Forecast
order.no_automatic_title = Clinflash IRT {{.projectNumber}} {{.envName}} Site Inventory Alert Notification {{.siteNumber}} {{.siteNameEn}}
order.over_title_depot = Clinflash IRT {{.projectNumber}} {{.envName}}  {{.siteNameEn}} Shipment Timeout Reminder {{.origination}} {{.orderNumber}}
order.over_title_site = Clinflash IRT {{.projectNumber}} {{.envName}} Shipment Timeout Reminder {{.destinationEn}} {{.orderNumber}}
order.overtime_depot = <p>Project number:{{.projectNumber}}</p> <p>Project name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Receiving Site No:{{.siteNameEn}}</p> <p>Shipment Number :{{.orderNumber}}</p> <p>There is an Shipment that has not been confirmed to be received, and the status is {{.statusItem}},Shipment generation time {{.generateDate}},Please confirm.</p>
order.overtime_site = <p>Project number:{{.projectNumber}}</p> <p>Project name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Receiving Site No:{{.siteNumber}}</p> <p>Name of Receiving Site:{{.siteNameEn}}</p> <p>Shipment Number :{{.orderNumber}}</p> <p>There is an Shipment that has not been confirmed to be received, and the status is {{.statusItem}},Shipment generation time {{.generateDate}},Please confirm.</p>
order.receive = <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>
order.receive-logistics = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express Logistics:{{.logistics}}</p> <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>
order.receive_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment  Receipt Notification {{.destinationEn}} {{.orderNumber}}
order.receive_dtp = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Visit Name:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express Logistics:{{.logistics}}</p> <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>
order.receive_dtp_sub = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Visit Name:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express Logistics:{{.logistics}}</p> <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>
order.receive_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment  Receipt Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.recovery_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Return Shipment Creation Notification {{.destinationEn}} {{.orderNumber}}
order.recovery_confirm_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Return Shipment Confirmation Notification {{.destinationEn}} {{.orderNumber}}
order.send = <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Shipment is in delivery.</p>
order.send_dtp = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Visit Name:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express logistics:{{.logistics}}</p> <p>Shipment is in delivery.</p>
order.send_dtp_sub = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Visit Name:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express logistics:{{.logistics}}</p> <p>Shipment is in delivery.</p>
order.send-logistics = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express logistics:{{.logistics}}</p> <p>Shipment is in delivery.</p>
order.send_title = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Delivery Notification {{.destinationEn}} {{.orderNumber}}
order.send_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Delivery Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.create_title_logistics = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Confirmation Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.create_logistics = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>IP shipment is created.</p>
order.create_logistics_dtp = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>IP shipment is confirmed.</p>
order.confrim_logistics = <p>Origin:{{.startEn}}</p> <p>{{.labelEn}}:{{.subject}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>IP shipment is confirmed.</p>
order.end_title_dtp = Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Terminated Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
order.end_dtp = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express Logistics:{{.logistics}}</p> <p>Terminate Reason:{{.reason}}</p> <p>This shipment has been terminated.</p>
order.end_dtp_sub = <p>{{.labelEn}}:{{.subject}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Visit Cycle:{{.visitEn}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Express Logistics:{{.logistics}}</p> <p>Terminate Reason:{{.reason}}</p> <p>This shipment has been terminated.</p>
order.approval.add-title = Clinflash IRT {{.projectNumber}} {{.envName}} Site Shipment Application
order.approval.failed-title = Clinflash IRT {{.projectNumber}} {{.envName}} Site Shipment Application Approval Failed
order.single.start = <p>Origin:{{.startEn}}</p>
order.single.subject = <p>{{.labelEn}}:{{.subject}}</p>
order.single.visit = <p>Visit Cycle:{{.visitEn}}</p>
order.single.orderNumber = <p>Order Number:{{.orderNumber}}</p>
order.single.expectedArrivalTime = <p>Expected Arrival Time:{{.expectedArrivalTime}}</p>
order.single.logistics = <p>Express Logistics:{{.logistics}}</p>
order.single.lostReason = <p>Loss Reason:{{.reason}}</p>
order.single.userName = <p>{{.userName}} confirmed that this order was lost.</p>
project.env = <p>Project Environment:{{.envName}}</p>
project.name = <p>Project Name:{{.projectName}}</p>
project.number = <p>Project Number:{{.projectNumber}}</p>
project.cohort = <p>Name:{{.cohortName}}</p>
project.order.expectedArrivalTime = <p>Expected Arrival Time:{{.expectedArrivalTime}}</p>
project.site.number = <p>Site Number:{{.siteNumber}}</p>
project.site.name = <p>Site Name:{{.siteNameEn}}</p>
subject.alarm.content = <p>The number of subjects enrolled in different layers reached the alert value</p> <p>Layer/Alert Value/Actual Number:{{.info}}</p>
subject.alarm.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Alert
subject.alert_threshold.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Setting Reminder
subject.alert_threshold.cohort_title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Setting Reminder
subject.pvUnblinding.content = <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site number:{{.siteNumber}}</p> <p>Site name:{{.siteNameEn}}</p> <p>Subjects {{.subjectNumber}} Uncover the blind of PV,time:{{.time}}.reason： {{.reason}}</p>
subject.pvUnblinding.title = Clinflash IRT Subjects PvUnblinding
subject.random.content_no_group = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Randomization Time:{{.time}}</p>
subject.random.content = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Randomization Time:{{.time}}</p>
subject.random.content_sub = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Randomization Time:{{.time}}</p>
subject.random.content_no_random_number_no_group = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Randomization Time:{{.time}}</p>
subject.random.content_no_random_number = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Randomization Time:{{.time}}</p>
subject.random.content_no_random_number_sub = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Randomization Time:{{.time}}</p>
subject.random.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Randomization {{.siteNumber}} {{.siteNameEn}}
subject.add.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Registration {{.siteNumber}} {{.siteNameEn}}
subject.signOut.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Stop {{.siteNumber}} {{.siteNameEn}}
subject.signOut.content = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Random Number:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Stop Time:{{.stopTime}}</p> <p>Reason:{{.reason}}</p>
subject.signOut.content_no_group = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Random Number:{{.randomNumber}}</p> <p>Stop Time:{{.stopTime}}</p> <p>Reason:{{.reason}}</p>
subject.signOut.content_sub = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Random Number:{{.randomNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Stop Time:{{.stopTime}}</p> <p>Reason:{{.reason}}</p>
subject.signOut.content_no_random_number_no_group = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Stop Time:{{.stopTime}}</p> <p>Reason:{{.reason}}</p>
subject.signOut.content_no_random_number = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Stop Time:{{.stopTime}}</p> <p>Reason:{{.reason}}</p>
subject.signOut.content_no_random_number_sub = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Stop Time:{{.stopTime}}</p> <p>Reason:{{.reason}}</p>
subject.replacement.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Replacement {{.siteNumber}} {{.siteNameEn}}
subject.modify.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Modification {{.siteNumber}} {{.siteNameEn}}
subject.screen.title = Clinflash IRT {{.projectNumber}} {{.envName}} Subject Screening {{.siteNumber}} {{.siteNameEn}}
subject.unblinding-approval.title = Clinflash IRT {{.projectNumber}} {{.envName}} Emergency Unblinding Approval Result {{.siteNumber}} {{.siteNameEn}}
subject.unblinding-approval.content = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Emergency Unblinding Time:{{.time}} </p> <p>Emergency Unblinding Reason:{{.reason}}</p> <p>Remark:{{.remark}}</p> <p>Approval Code:{{.approvalNumber}}</p> <p>Approval Result:{{.approvalResultEn}}</p> <p>Reason:{{.rejectReason}}</p>
subject.ordinary-unblinding-approval.title = Clinflash IRT {{.projectNumber}} {{.envName}} Emergency Unblinding Succeed {{.siteNumber}} {{.siteNameEn}}
subject.ordinary-unblinding-approval.content = <p>Project Number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site number:{{.siteNumber}}</p> <p>Site name:{{.siteNameEn}}</p> <p>{{.label}}:{{.subjectNumber}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Emergency Unblinding Time:{{.time}} </p> <p>Emergency Unblinding Reason:{{.reason}}</p>
subject.pv-unblinding-approval.title = Clinflash IRT {{.projectNumber}} {{.envName}} PV Unblinding Approval Result {{.siteNumber}} {{.siteNameEn}}
subject.pv-unblinding-approval.content = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Random Number:{{.randomNumber}}</p> <p>Pv Unblinding Time:{{.time}} </p> <p>Pv Unblinding Reason:{{.reason}}</p> <p>Remark:{{.remark}}</p> <p>Approval Code:{{.approvalNumber}}</p> <p>Approval Result:{{.approvalResultEn}}</p> <p>Reason:{{.rejectReason}}</p>
subject.unblinding.controlContent = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Emergency Unblinding Time:{{.time}}</p> <p>Emergency Unblinding Reason:{{.reason}}</p>
subject.unblinding.content = <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Emergency Unblinding Time:{{.time}}</p> <p>Emergency Unblinding Reason:{{.reason}}</p> <p>Whether the Sponsor has been notified or not:{{.isSponsor}}</p> <p>Remark:{{.remark}}</p>
subject.unblinding.title = Clinflash IRT {{.projectNumber}} {{.envName}} Emergency Unblinding Succeed {{.siteNumber}} {{.siteNameEn}}
user.accept = The user{{.userName}}, email:{{.email}} you invited has accepted the invitation
user.invite = <p> You have been invited to join the Clinflash randomized trial study,<a href={{.url}}>System link</a>,Your initial password is:{{.password}},</p> <p>{{.url}}</p> <p>If the page does not open after clicking the link, Please copy the link to the browser address bar to access.</p>
user.join = . <p> {{customerName}} {{.userName}} to invite you to join the randomization study. <a href = {{.url}} > accept the invitation</a> </p> <p> {{.url}} </p> <p> this link is valid for two days, please complete the operation ASAP. If the page does not open properly after clicking the link, manually copy the link and paste it into the address bar of the browser.</p>
user.locking = Your account{{.email}}enter the wrong password 5 times,the account has been locked,please contact the administrator or click forget password.
user.notice.customer.bind.title = 「Customer Authorization Notification」
user.notice.project.bind.title = 「Project authorization notification」
user.notice.return.login = Return to login
user.notice.title = Project authorization notification
user.notice.customer-title = Customer Authorization Notification
user.notice_customer = Your account {{.email}} has been successfully authorized to [{{.customer}}] customers, please log in to the system in time to check.
user.notice_project = Your account {{.email}} is assigned to the new clinical trial project [{{.project}}], please log in to the system in time to check.
user.resend_invite = Clinflash Cloud has created an account for you, please visit this link {{.link}} in 7 days to activate your account.If not operated by yourself, please ignore！
user.resend_invite_title = Clinflash Cloud - Activate your account
user.reset = <p> your password has been reset by the administrator, the new password is {{.Password}}. <a href = {.{url}} > system link </a> </p > <p> {{.Url}} < / p > <p> If the page does not open properly after clicking on the link, please manually copy the link and paste it into the address bar of your browser to visit the website.</p>
user.retrieve = <p> You apply to reset your password via email, please ignore it unless it was operated by yourself. <a href = {{.url}} > reset password </a> </p> <p> {{.url}} </p> <p> this link is valid for 7 days, please complete the operation ASAP. If the page does not open properly after clicking the link, manually copy the link and paste it into the address bar of the browser.</p>
subject_urgentUnblindingApproval_agree = Passed
subject_urgentUnblindingApproval_reject = Refused
mail_security = This email contains a secure link. Please do not share this email, link, or access code with others.
mail_noreply = This email is sent automatically by the system, please do not reply directly.
mail_copyright = Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved
mail_upper_half = <!DOCTYPE html> <html lang="en"> <head> <meta charset="utf-8"/> <title>Clinflash IRT</title> <style> .text-body { font-size: 14px; color: dimgrey; } </style> </head> <body> <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="font-size: 14px; background-color: #fff; table-layout: fixed; border-top: #0A47ED solid 6px;"> <tbody> <tr> <td colspan="12" style="padding: 40px 0 40px 50px;"> <img style="width: 238px; height: 40px;" src="{{.irt_url}}/api/img/mail"/> </td> </tr> <tr> <td colspan="12" style="padding: 5px 50px;"> <div class="text-body">
mail_lower_half =  </div> </td> </tr> </tbody> <tfoot> <tr> <td colspan="12" style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3"> <div>This email contains a secure link. Please do not share this email, link, or access code with others.</div> <div>This email is sent automatically by the system, please do not reply directly.</div> <div>Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved</div> </td> </tr> </tfoot> </table> </body>
mail_lower_half_zh_en =  </div> </td> </tr> </tbody> <tfoot> <tr> <td colspan="12" style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3"> <div>此电子邮件包含安全链接，请不要与他人分享此电子邮件、链接或访问代码。</div> <div>本电子邮件由系统自动发送，请勿直接回复。</div> <div>Copyright ©2020 易迪希医药科技（嘉兴）有限公司 版权所有</div> </td> </tr> <tr> <td colspan="12" style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3"> <div>This email contains a secure link. Please do not share this email, link, or access code with others.</div> <div>This email is sent automatically by the system, please do not reply directly.</div> <div>Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved</div> </td> </tr> </tfoot> </table> </body>

[export]
dispense_list_download_name = dispensation report
export.project = Project
export.projects.number = Project Number
export.projects.name = Project Name
export.projects.cohort = Cohort Name
export.projects.stage = Stage
export.barcode = Barcode
export.random.register = Register
export.random.random = Randomize
export.random.exit = Stop
export.random.unBlind = Emergency unblinded
export.random.pv = PV unblinded
export.random.screenSuccess = Screening successful
export.random.screenFail = Screening failed
export.random.finish = Complete Study
export.random.toBeRandom = To be randomized
export.random.number = Randomization Number
export.dispensing.auto = auto
export.dispensing.first = first
export.dispensing.medicine = IP number
export.dispensing.realMedicine = Actually Used IP Number
export.dispensing.medicineName = IP Name
export.dispensing.otherMedicineCount = quantity of unnumbered IP
export.dispensing.outVisit = Unscheduled
export.dispensing.reissue = Re-dispense
export.dispensing.replace = Replace
export.dispensing.retrieve = Retrieve
export.dispensing.register = Register
export.dispensing.cancel = Withdraw
export.dispensing.invalid = Do Not Attend the Visit
export.dispensing.recover = Recover Dispensation
export.dispensing.replaceMedicine = Replaced IP number
export.dispensing.room = room number
export.dispensing.siteName = SiteName
export.dispensing.siteNumber = SiteNumber
export.dispensing.subject = subject
export.dispensing.time = operation time
export.dispensing.type = operation type
export.dispensing.visit = Visit name
export.dispensing.visit_number = Visit number
export.dispensing.visitSign = Unscheduled Dispense
export.dispensing.visit_apply = Visit Application
export.dispensing.out_visit_apply = Unscheduled Dispensation Application
export.dispensing.reissue_apply = Re-dispensation application
export.dispensing.replace_apply = Replacement application
export.dispensing.is_replace = Is it comfirmed to replace
export.dispensing.is_real = Whether the IP is actually dispensed
export.dispensing.operate_time = Dispensation Operation Time
export.dispensing.realMedicineNumber = Actually Used IP Number
export.dispensing.weight = Weight
export.dispensing.height = Height
export.dispensing.age = Age
export.random_config.SubjectReplaceText = Label (replacement text)：
export.random_config.accuracy = Exact number of digits：
export.random_config.attribute = Project properties
export.random_config.blind = Blind or not：
export.random_config.countryLayered = Set country as stratification factor or not：
export.random_config.regionLayered = Set Regional as stratification factor or not：
export.random_config.createBy = Producer：
export.random_config.createDate = Generation time：
export.random_config.runningTime = Start time of simulation run：
export.random_config.generationTime = Simulation report generation time：
export.random_config.digit = Digit：
export.random_config.dispensing = Dispensing or not：
export.random_config.export = Configuration Report
export.random_config.simulation_pdf = Randomization Simulation Report
export.random_config.factor = Stratification factors：
export.random_config.no = No
export.random_config.group = Group：
export.random_config.instituteLayered = Set site as stratification factor：
export.random_config.isFreeze = When running the delivery algorithm, count quarantined items as part of the available inventory in the sites
export.random_config.isRandom = The center has not assigned a random number and cannot be enrolled：
export.random_config.list = Randomization List：
export.random_config.prefix = Use prefix：
export.random_config.random = Randomization or not：
export.random_config.random_design = Random configuration
export.random_config.ratio = Group ratio：
export.random_config.report = Clinflash IRT is a random and trial IP supply management system, which can be used for randomization, dispensation and trial research products supply management.This configuration report includes project properties, randomization design and treatment  design, which can be used to quickly review and approve project settings,and archive project files.
export.random_config.configure_report = Clinflash IRT is a random and trial IP supply management system, which can be used f|or random, dispensing and trial research products supply management.This configurat|ion report includes project properties, random design and treatment  design, which ca|n be used to quickly review and approve project settings,and archive project files.
export.random_config.report_pdf = Clinflash IRT is a random and trial IP supply management system, which can be used for randomization, dispensation and trial research products supply management.This configuration report includes project properties, randomization design and treatment  design, which can be used to quickly review and approve project settings,and archive project files.
export.random_config.directory = Directory
export.random_config.summary = Summary
export.random_config.unitCapacity = Unit capacity:
export.random_config.unitStandard = Unit Calculation Standard:
export.random_config.ageType = Age
export.random_config.weightType = Weight
export.random_config.bsaType = Simple body surface area BSA
export.random_config.customerBsaType = Other body surface area BSA
export.random_config.table.code = Group code
export.random_config.table.count = The number of IP dispensed
export.random_config.table.countMax = Dispensation quantity
export.random_config.table.formulas = Dispensation quantity range
export.random_config.table.label = (Combined) Dispensation label
export.random_config.table.formula = Custom formula
export.random_config.table.medicine = IP Name
export.random_config.table.spec = IP Specification
export.random_config.table.formulasType = Formula
export.random_config.total = Total：
export.random_config.treatment_design = Treatment design
export.random_config.yes = Yes
export.random_config.type = Random type：
export.room.history.room = the history of viewing room number
export.room.history.time = time
export.room.history.user = user
export.room.project = project
export.unblinding.remark = Unblinding Remark
export.unblinding.reason = Unblinding Reason
export.unblinding.reason_mark = Unblinding Reason Remark
export.unblinding.operator = Unblinding Operator
export.unblinding.operate_time = Unblinding Operation Time
export.user.name = Name
export.user.email = Email
export.user.role = Role
export.user.create_time = Created Time
export.user.status_effective = Valid
export.user.status_invalid = Invalid
export.medicine.serial_number = Sequence Number
export.medicine.ip_name = IP Name
export.medicine.ip_number = IP Number
export.medicine.expiration_date = Expiration
export.medicine.batch_number = Batch Number
export.medicine.package_number = Package Number
export.medicine.package_number_serialNumber = Package Sequence Number
export.subject.number = Subject Number
medicine.status.available = Available
medicine.status.delivered = Confirmed
medicine.status.destroy = Destroy
medicine.status.expired = Expired
medicine.status.inStorage = In the warehouse process
medicine.status.lose = Lose/Wasted
medicine.status.quarantine = Quarantine
medicine.status.receive = Received
medicine.status.return = Returned
medicine.status.sending = In Delivery
medicine.status.stockPending = To be warehoused
medicine.status.toBeConfirmed = To be confirmed
medicine.status.toBeWarehoused = To be warehoused
medicine.status.transit = In Delivery
medicine.status.used = Used
medicine.status.apply = Applied
medicine.status.frozen = Frozen
medicine.status.locked = Locked
medicineOrder_download_packageMethod = Deliver Mode
medicineOrder_download_packageMethodSingle = Item
medicineOrder_download_packageMethodPackage = Package
medicineOrder_download_packageNumber = Package Number
medicineOrder_download_batchNumber = Batch Number
medicineOrder_download_cancelDate = Cancellation Time
medicineOrder_download_cancelUser = Canceller
medicineOrder_download_count = IP Quantity
medicineOrder_download_createDate = Created Time
medicineOrder_download_createUser = Created By
medicineOrder_download_expiredDate = Expiration
medicineOrder_download_fileName = Order report
medicineOrder_download_medicineNumber = IP Number
medicineOrder_download_medicine = IP
medicineOrder_download_number = Shipment Number
medicineOrder_download_orderInfo = Shipment details
medicineOrder_download_other = Number
medicineOrder_download_receiveDate = Receiver Time
medicineOrder_download_expectedArrivalTime = Expected Arrival Time
medicineOrder_download_actualReceiptTime = Actual Receipt Time
medicineOrder_download_receiveInstitute = Destination
medicineOrder_download_receiveUser = Receiver
medicineOrder_download_sendInstitute = Origin
medicineOrder_download_status = Shipment Status
medicineOrder_download_cancelReason = Cancellation Reanson
medicineOrder_download_confirmUser = Confirmed By
medicineOrder_download_confirmDate = Confirm Time
medicineOrder_download_closeUser = Closed By
medicineOrder_download_closeDate = Closed Time
medicineOrder_download_closeReason = Reason for Closure
medicineOrder_download_sendUser = Delivered By
medicineOrder_download_sendDate = Delivery Time
medicineOrder_download_lostUser = Lost By
medicineOrder_download_lostDate = Lost/Wasted Operation Time
medicineOrder_download_lostReason = Reason for Loss
medicineOrder_download_endUser = Terminated By
medicineOrder_download_endDate = Terminated Time
medicineOrder_download_endReason = Reason for Termination
medicineOrder_download_supplier = Logistics Vendor
medicineOrder_download_supplierOther = Other Vendor
medicineOrder_download_supplierNumber = Tracking Number
medicine_batch_number = Batch Number
medicine_download_batch = Batch Number
medicine_download_spec = Specification
medicine_download_packageNumber = Package Number
medicine_download_depot_name = Depot Statistics Report
medicine_download_expiredDate = Expiration
medicine_download_location = Location
medicine_download_name = IP name
medicine_download_number = IP Number
medicine_download_orderNumber = Shipment Number
medicine_download_site = Site Name
medicine_download_site_name = Site Item Report
medicine_download_dtp_sku = IP item report
medicine_download_status = Status
medicine_download_storehouse = Depots
medicine_download_reason = Reason
medicine_download_operFree = Quarantine
medicine_download_operRelease = Lift
medicine_download_operLost = Lost/Wasted
medicine_download_operUse = Make Available
medicine_download_operator = Operator
medicine_download_time = Time
medicine_download_country = Country (Stratification Property)
medicine_download_region = Region (Stratification Property)
medicine_download_site_country = Country
medicine_download_site_region = Region
medicine_download_freeze_reason = Freeze Reason
medicine_download_freeze_operator = Quarantine Operator
medicine_download_freeze_time = Quarantine Operation Time
medicine_download_release_reason = Lifting Quarantine Reason
medicine_download_release_operator = Lifting Quarantine Operator
medicine_download_release_time = Lifting Quarantine Time
medicine_download_lost_reason = Lost/Wasted Reason
medicine_download_lost_operator = Lost/Wasted Operator
medicine_download_lost_time = Lost/Wasted Time
medicine_download_use_reason = Setting as available reason
medicine_download_use_operator = setting as available operator
medicine_download_use_time = Setting as available time
medicine_download_site_number = Site Number
medicine_expiration_date = Expiration
medicine_list_download_name = IP list
medicine_name = IP name
medicine_number = IP number
medicine_package = Package Number
medicine_package_barcode = Package Number-Barcode
medicine_package_barcode_short = Package Number & Barcode
medicine_code = Short Code
medicine_examine_uccess = Approved
medicine_examine_fail = Approval failed
medicine_examine_update = Modify
medicine_examine_release = Release
medicine_barcode_code = IP-Barcode
medicine_barcode_code_short = Barcode & Short Code
medicine_serial_number = Sequence Number
medicine_status = Status
operator.people = Operator
operator.reason = Reason
operator.time = Operation Time
operator.content = Operate Content
order_status_cancelled = Cancelled
order_status_not_cancelled = Current shipment status cannot be terminated, please refresh the page
order_status_wms_not_cancelled = 不可以取消订单
order_status_lose = Lost
order_status_received = Received
order_status_requested = Confirmed
order_status_toBeConfirmed = To be Confirmed
order_status_transit = In Delivery
order_status_apply = Applied
order_status_terminated = Terminated
order_status_close = Closed
random.number = Randomization ID
randomList.export.available = available
randomList.export.block = block
randomList.export.center = site
randomList.export.delete = Wasted
randomList.export.inactivate = Invalid
randomList.export.group = group
randomList.export.number = randomization ID
randomList.export.status = status
randomList.export.subject = subject number
randomList.export.used = used
randomNumber.export.status.invalid = Invalid
randomNumber.export.status.used = Used
randomNumber.export.status.unused = Available
randomNumber.export.status.unavailable = Unavailable
randomization.accuracy.ones = Less than or equal to
randomization.accuracy.twos = equal to
randomization.type.ones = Block randomization
randomization.type.twos = Minimization random
simulated.random.list.factor = Stratification Factor
simulated.random.list.group = Group
simulated.random.list.name = SimulationResult
simulated.random.list.number = Randomization ID
simulated.random.list.only = Only one random list is allowed to start
simulated.random.list.runCount = Number of runs
simulated.random.list.site = Site
simulated.random.list.subject = Subject
simulated.random.number.not.enough = The number of subjects must be less than the number of randomization ID
site.name = Site name
site.number = Site number
subject.group = group
subject.number = Subject ID
subject.replace = Replace
subject.replace_subject = Replace subject ID
subject.replace_number = replace subject Randomization ID
subject.random.fileName = Randomization Report
subject.status = Status
subject.status.registered = Registered
subject.status.random = Randomized
subject.status.sign.out = Deactivated
subject.status.unblinding = Emergency Unblinded
subject.status.screen.success = Screening successful
subject.status.screen.fail = Screening failed
subject.status.finish = Complete Study
subject.status.to.be.random = To be randomized
subject.status.join = Enrollment
subject.unblinding.fileName = Unblinding Report
subject.unblinding.sponsor = Whether the Sponsor has been notified or not
user.createDate = Creation time
user.depot = Depots
user.email = email
user.name = name
user.roles = roles
user.site = sites
user.status = status
user.status.activited = Activated
user.status.not.active = Inactived
user.status.open = Enabled
user.status.unauthorized = Unauthorized
user.status.enable = Activate
user.status.disable = Disabled
system_suggest_value = System suggest value
plan_number = Planned Randomization Number
source_ip_upload_history_name = Name
source_ip_upload_history_rows = Rows
source_ip_upload_history_rows_succeeded = Succeeded
source_ip_upload_history_rows_failed = Failed

[error]
app.unauthorized = App Unauthorized
auth.fail = Authentication failed
customer.not.exist = The customer does not exist
customers.delete.admin = The current operator does not have admin permission under the customer and has no right to operate
customers.delete.message = There is a user under the customer and cannot be deleted
customers.duplicated.names = The customer name is duplicated
edc.error = Request failed, the date push mode is active push from IRT.
edc.version = The EDC interface version is incorrect. please contact EDC personnel.
edc.add.relation.site.error = Site association failed, please associate site again.
edc.block.is.not.nil.error = blockRepeatNo parameter is empty, dispensation failed, please confirm configuration again.
edc.check.matching.value.error = Value matching failed, please re confirm the value of the check box
edc.configure.drug.error = Treatment design is empty, please confirm treatment design in EDC again.
edc.configure.visit.error = Visit cycle is empty, please confirm configuration of visit cycle in EDC again.
edc.drug.number.error = IP Number search failed. Please search again.
edc.drug.number.mismatch = IP number search result does not match, please confirm replacing IP information.
edc.drug.reissue.error = Re-dispensation failed, please contact unblinded personnel to complete operation.
edc.drug.replace.error = Replacement failed, unable to replace the IP for the last completed visit, please re select.
edc.drug.replace.nil.nil = IP number is empty, please operate after confirm again.
edc.drug.replace.nil.error = Failed to search the Replaced IP , please contact IRT engineer.
edc.drug.type.error = type parameter is empty, dispensation failed, please confirm configuration again.
edc.env.error = Project environment information does not exist, please contact IRT engineer to check.
edc.factor.error = Cohort factor is empty, please confirm again.
edc.instance.is.not.nil.error = instanceRepeatNo parameter is empty, dispensation failed, please confirm configuration again.
edc.matching.value.error = Value matching failed, please confirm the value of radio button or drop-down box again.
edc.multiple.subject = Subject ID search result duplicated, please confirm subject number information
edc.no.subject = Edit failed, please contact IRT engineer to deal with.
edc.parameter.error = Project number/environment/site number/site name/Subject ID is empty, please confirm EDC configuration.
edc.project.env.number.error = Project number/project environment is empty, synchronization failed, please confirm configuration again.
edc.query.factor.error = Cohort search failed, please confirm cohort factor configuration again.
edc.query.project.error = Project search failed, please contact IRT engineer to check.
edc.query.project.number.error = Project search failed, please contact IRT engineer to check.
edc.query.project.dtp.error = DTP project does not support docking with EDC
edc.query.site.number.error = This site is not unique, please check if multiple sites exist in IRT system
edc.query.site.number.relation.site.error = This site is not unique, please check if multiple sites exist in IRT system
edc.register.synchronization.error = Current interface does not support one-time full synchronization, interface call failed. Please confirm EDC synchronization function configuration.
edc.relation.site.error = Site association failed, please contact IRT engineer to check.
edc.site.error = Site search failed, please contact IRT engineer to check.
edc.standard.lost.site.error = Site information incomplete, please contact IRT engineer to edit.
edc.standard.site.error = Site information lost, please contact IRT engineer to add.
edc.start.site.error = Site enable/activate failed, please operate again.
edc.subject.after.dispensing.error = Subject has not been randomized, dispensation failed, please confirm again.
edc.subject.dispensing.error = Subject has not been randomized, dispensation failed, please confirm again.
edc.subject.existence = Subject already exists, please do not add repeatedly.
edc.subject.random.number.error = Subject ID is empty, please confirm EDC configuration.
edc.subject.register.error = Subject is not registered, please confirm again.
edc.subject.status.dispensing.error = Subject has been randomized, dispensation before randomization failed, please confirm again.
edc.subject.site.error = The site information returned by IRT does not match the site where the subject is located. Please perform the subject transfer in IRT before dispensation.
edc.unable.at.random = Current fuction is not applicable in randomized project, please confirm randomization design.
edc.visit.error = Visit cycle is empty, please confirm configuration between visit number and visit cycle in EDC again.
edc.visit.no.dispensing.error = Dispensation failed at current visit, please confirm property settings before/after randomization.
edc.visit.number.drug.configure.error = Treatment design search duplicated, please confirm configuration between visit number and treatment design in EDC again.
edc.visit.number.error = Visit number is empty. The sync failed. Please reconfirm the configuration.
environment.duplicated.names = Duplicated environment name
environment.alertThresholds.limitError = Dispensation projects only, threshold control conditions only allow configuration registration/screening or enrollment, please reconfirm.
environment.alertThresholds.attributeError = The project attribute is “dispensation only”, the current threshold limit condition configuration is invalid, please reconfigure it.
file_emtpy = The file list is empty
col_empty_error: The uploaded randomization list contains empty columns (including headers). Please correct them before re-uploading.
medicine.errorSupplyMode = The way of supplement is inconsistent, Commit failed, Please modify again.
medicine.errorAutoSupplyMode = Wrong! The Auto-Ration Mode is inconsistent.
medicine.errorSupplyModeHint = The way of supplement is inconsistent.
medicine.errorAutoSupplyModeHint = The Auto-Ration Mode is inconsistent.
medicine_duplicated = The IP already exists
medicine_duplicated_number = The IP number already exists
medicine_duplicated_package_number = The package number already exists
medicine_duplicated_serial_package_number = The package sequence number already exists
medicine_duplicated_serial_number = The sequence number already exists
medicine_upload_package_number = The packaging number has not been uploaded, please upload it again.
medicine_upload_package_serial_number = The package sequence number has not been uploaded, please upload it again.
medicine_upload_package_count = Error. Please re-upload.
medicine_upload_drug_name = 请选择正确的研究产品。
medicine_upload_package_serial_count = The quantity of package sequence number is incorrect, please re-upload.
medicine_not_exist = The IP does not exist
medicine_packlist_upload_check = IP number does not exist. Please upload IP number first
medicine_packlist_upload_firstPack = The first layer of packaging cannot be empty
minimize_bias_probability_tips = Bias probability cannot be empty
minimize_layered_tips = Group stratification bias not captured, please confirm again.
project_edc_irt_return = Subject number prefix rules is inconsistent with EDC configuration, please reconfirm.
variable_duplicated = Variable ID is duplicate, please confirm again.
form_name_duplicated = Form name is duplicate, please confirm again.
option_label_duplicated = Add duplicate, please confirm again.
combined_dispensation_label_duplicated = The IP within the combined dispensation label needs to be needs to be consistent, please reconfirm.
form_invalid_error = Invalid Failed, field has been applied in the IP configuration.
update_medicine_status_error = The status of some IP products has changed, please select again.
page_notice.mail.send_fail = Fail in send
page_notice.mail.send_success = Sent successfully
page_notice.system_update.email_error = Email address error
page_notice.system_update.others = Other
page_notice.system_update.quota_exceeded = The daily send quota of the main account has been exceeded
page_notice.system_update.timeout = Mail server timed out
project.site.delete = The site has been used and cannot be disabled
project.storehouse.delete = The warehouse has been used and cannot be deleted
project.storehouse.had.add = The depot already exists.
project.storehouse.unConnected = The depot is not connected to the logistics. Please do not push the data
project.user.join = <p>{{.customerName}}invites you to join the randomized experimental study</p> <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p>
project.user.title = Clinflash IRT Project invitation
projects.duplicated.names = The project name is already on this system
projects.duplicated.numbers = Duplicated number, please re-enter
randomNumber.exist.used = Error, there is a used randomization ID in this block group, and other layers cannot be assigned, please confirm again.
randomNumber.exist.used.clean = Error, there is a used randomization ID in this block , and the stratification factor cannot be removed, please confirm again.
random_filed_error = Duplicated field, please confirm again.
random_attribute_error = Minimized randomization has been configured as "Not Applicable", please modify and re-create.
random_length_error = The generated randomization ID length has exceeded the set number length set number length
random_list_download_name = Source Randomization List Report
random_list_name_duplicated = Name already exists
random_number_block_error = Block must be unique, please confirm again.
random_number_error = Error, duplicated randomization ID, please confirm again.
random_number_format_error = Upload list format error, please confirm again.
random_number_format_error_trim = There are spaces in the uploaded data, please upload again
random_total_error = Error, The total number of Randomization IDs must be divisible by the sum of the group ratios, please confirm again.
random_duplicated_factor = The stratification factor already exists, please do not repeat the configuration
randomNumber_status_error = Random sequence number status has been changed. Please reconfirm.
randomization.upload.blockSize = The block length in the randomization list is inconsistent with the acceptable block size configured in the system, please confirm again.
randomization.upload.group = Error, the group of the list is inconsistent with the configured treatment group
randomization.upload.group_error = Upload failed, the upload group does not match the system configuration, please reconfirm.
randomization.upload.group_tip_error = The number of source blinded groups uploaded does not match the system configuration, please confirm.
roles.delete.message = Role has been used, cannot be deleted
roles.duplicated.names = Added repeatedly, please modify
roles.duplicated.sys_check = The menu permissions of the system administrator role cannot be modified
roles.duplicated.project_admin_check = It is not allowed to modify the project administrator's project viewing permission
shipment_order_add_info = Because it is a blind project, please select 2 groups of IP names
shipment_order_buffer_info = The inventory quantity of the current site is greater than the maximum buffer quantity
shipment_order_cancel_not_exist = This outbound tracking number does not exist at the logistics site.
shipment_order_cancel_not_status = Logistics has delivered goods, cannot cancel the waybill
shipment_order_initial_info = Current Site Status does not allow repeated creation of  innitial shipment
shipment_order_create_error = No IP need to be created, please re-select
shipment_order_mode_info = When the Origin is the site, the supplement mode can only be the quantity supplement mode
shipment_order_over_warning_info = The available inventory of the current site is higher than the warning value
shipment_order_sendAndReceive_info = The origin and destination cannot be duplicated
shipment_order_dtp_info = DTP shipments do not allow partial confirmation or receipt
shipment_order_check_packageNumber = The packing quantity of the whole box is inconsistent with the configuration, please confirm again.
shipment_order_supply_info = The destination is not bound to a supply plan, please contact the project administrator to configure.
shipment_order_supply_page_error = 无法创建订单,“{{.currentName}}”为包装运输,同包装研究产品“{{.otherName}}”所属群组状态为”已完成/草稿/已终止“,请重新确认。
shipment_order_create_modeMax_info = The destination available inventory has exceeded the maximum buffer, and IP shipment is not created.
shipment_order_no_supply = Please configure the supply plan first.
shipment_order_ratio_err = 目的地中心未开启供应比例配置。
shipment_out_of_stock = Shipment create unsuccessfully due to Insufficient IP inventory, please contact administrator for supplyment.
shipment_change_out_of_stock = Shipment change unsuccessfully due to Insufficient IP inventory, please contact administrator for supplyment.
shipment_order_supplyRatio_create_fail = Shipment generated failed, IP quantity is not match with ratio, please reconfirm.
site.disabled = This site is disabled
site.had.bind = Started site cannot delete
sites.duplicated.number = Duplicate number, please re-enter
storehouse.had.bind = The depot had been activated and can not be deleted.
storehouses.duplicated.number = Duplicated depot number
subject_cohort_check = Randomization is not allowed when not enrolled at current stage
subject_cohort_check_register = Register failed,draft status is not allowed to register.
subject_register_number_fail = Registration failed, subject number has reached the max bit length.
subject_cohort_last_group = At the upper stage, the subject was not randomly enrolled
subject_factor_check_error = Randomization ID has exceeded the upper limit of the project or the project is not in the group status, please contact the system configuration personnel in time to make adjustments before operating the random number.
simulate_random_site_cont_error = Save failed, number of regions/countries must be smaller than or equal to number of sites.
subject_factor_no_null = Stratification factor cannot be empty
subject_medicine_count = Dispensation failed, the inventory of site is insufficient.
subject_medicine_batch_count = 发放失败，无可用有效期内发放的研究产品，请确认。
subject_medicine_count_store = Dispensation failed, the inventory of depot is insufficient.
subject_medicine_alarm_count = Dispensation failed, the inventory of site is insufficient.
subject_medicine_dose_error = Dispensation failed, unable to match the visit to the corresponding dose.
subject_medicine_dtp_error = The IP within the combined dispensation label needs to be needs to be consistent, please reconfirm.
subject_medicine_label_select = Please select IP label or IP
subject_medicine_label_select_outsize = Dispensation failed, no independent unscheduled visit dispensation rules configured, please confirm.
subject_medicine_count_real = There is no IP in stock, please re-enter
subject_medicine_count_real_same = The same batch of registered IP is not allowed, please re-enter
subject_medicine_other = Unnumbered IP
subject_no_random = There is no matched randomization ID, please confirm again.
subject_site_no_depot = Unable to randomize/dispense, site is unbound to the depot, please contact the administrator to configure.
subject_no_drug_configure = Current inventory is empty or ip is not configured,please contact system configurator to revise it and then operate again.
subject_no_enough_drug = Randomization failed, IP inventory is insufficient, please confirmed.
subject_no_register = Subjects were not registered
subject_replace_no_register_screenFail = Replacement failed, replacement subject was not registered/screening failed, please enter again.
subject_replace_no_site_fail = Replacement failed, replace subject number is not in current site, please confirm again.
subject_replace_no_site_fail_cohort = The replacement subject ID is not in the current group, please reconfirm.
subject_replace_no_site_fail_stage = The replacement subject ID is not in the current Stage, please reconfirm.
subject_replace_no_cohort_site_fail = Replacement failed, replace subject number is not in current cohort site, please confirm again.
subject_replace_no_stage_site_fail = Replacement failed, replace subject number is not in current stage site, please confirm again.
subject_replace_random = Replacement failed, replacement subject has been randomized, please enter again.
subject_replace_sign_out = Replacement failed, replacement subject has been stopped, please enter again.
subject_replace_register = Replacement failed, replacement subject has been registered, please enter again.
subject_no_replace = The subject number you entered has been randomly selected and cannot be replaced
subject_no_visit = Please perform registration after visit cycle configuration
subject_number_repeat = Subject already exists, please confirm again.
subject_transfer_fail = Transfer failed, new site subject number:{{.subjectNo}} is repeated, please confirm again.
subject_switch_cohort_fail1 = Cohort switching is only supported for subjects whose status is pre-randomization.
subject_switch_cohort_fail2 = The current project has established data integration with the EDC for pre-randomization modifications and does not support cohort switching
subject_switch_cohort_fail3 = Cohort switching is not supported for subjects who have already had medication dispensed
subject_switch_cohort_fail4 = The stratification configuration of the switched cohort differs from that of the current cohort's form. Please confirm
subject_random_error = The IP dispensation before randomization is not completed, can not randomize.
subject_random_number_existence = The Randomization ID already exists in the system and cannot be used
subject_status_no_cancel = Current subject status cannot be cancelled
subject_status_no_delete = Current subject status does not allow deleting.
subject_status_no_dispensing = Current subject status does not allow IP dispensation.
subject_status_no_join = Current subject status does not allow non-attending visit, please confirm again.
subject_status_no_random = Current subject status does not allow randomization
subject_status_no_reissue = Current subject status does not allow IP re-dispensation.
subject_status_no_replace = Current subject status does not allow subject replacement.
subject_status_no_replace_dispensing = Current subject status does not allow IP replacement.
subject_status_no_retrieval = Current subject status does not allow IP retrieving.
subject_status_no_sign_out = Current subject status does not allow stop.
subject_status_no_unblinding = Current subject status cannot be unblinded
subject_status_no_update = Current subject status does not allow modification.
subject_visit_dispensing = IP has been dispensed at current visit
subject_visit_dispensing_no_join = Current visit set as non-dispensation.
subject_visit_dispensing_no_order = Please dispense the IP in the order of visit
subject_visit_dispensing_no_order_confrim = Last visit shipment confirmation in pending.
subject_visit_dispensing_no_order_dtp = Current visit configuration cannot use Site  / Depo t(Send the IP directly to the patient), please check the configuration
subject_visit_dispensing_store = Site has not be assigned to the depot
subject_visit_dispensing_set_no_join = Current visit does not allow to perform 'Do Not Attend the Visit', please refresh the page.
subject_visit_dispensing_no_reissue = Re-dispensation failed. Re-dispensation cannot be made for current unscheduled visit. Please select again.
subject_visit_dispensing_no_reissue_dose = Re-dispensation failed, the option corresponding to visit judgment/dose level has been deleted, please reconfirm.
subject_visit_dispensing_no_site = The site has not been assigned a depot yet
subject_visit_dispensing_order_status = Current shipment status does not allow registration of actually dispensed IP.
subject_visit_dispensing_order_status_last = The shipment status of the last visit was not delivered, and the current visit cannot be applied
subject_visit_cannot_cancel = Current project property does not allow IP retrieval and withdrawal
subject_visit_cannot_replace = The current IP status does not allow replacement
medicine_approval_err = Current task has not been approved for release, Please contact the approver to complete the approval before applying for release.
medicine_release_err = Select at least one item or input quantity
upload_medicines_cell = Please delete the blank line data of Excel file
upload_medicines_drugName = IP names do not match
upload_medicines_info = Do not upload empty template data
upload_translate_info = Do not upload empty template data
random_type_error = The random type of the randomization list is inconsistent with the system configuration
random_group_error = The group of randomization list is inconsistent with system configuration
random_factor_error = The stratification factors/options for random list  is inconsistent with the system configuration
medicine_delete_error = The IP has been used by the shipment and cannot be deleted
factor_calc_not_match = The calculation result does not match any corresponding stratification, please reconfirm.
factor_not_match = There is no matched Stratification Factor, please confirm again
subject_visit_err = Template does not match, the current date has exceeded the minimum window period date, please confirm again.
subject.register.enrollment.full = Registration failed and the current status is enrollment full.
subject.dispensing.enrollment.full = Dispensation failed, the enrollment is full, please contact the administrator to confirm.
subject.register.screen.fail.enrollment.full = Screening failed and the current status is enrollment full.
user.customer.authorization.success = Authorization succeeded
user.customer.bind.error = There is no such user in the customer group. Please contact the administrator to add the user
user.customer.bind.error1 = There is no such user in the customer group
user.customer.bind.success = Binding succeeded
user.exist.env = This user already exists in the environment
user.no.exist = User does not exist
user.no.exist.customer = User does not belong to the current customer
user.password.error = Password error
user.phone.exist = The mobile phone number is bound to the user
user.resend.email.info = User status is active,don't need to send him/her an activation email
users.authentication.failed = Authentication failed, please login again
users.duplicated.customer = User already exists
users.duplicated.emails = Duplicated user emails
users.duplicated.cloud-customer = The user is disabled in the Cloud and cannot be added.
users.duplicated.cloud-disable = Disabled in the cloud
users.duplicated.cloud-delete = 用户已删除，请联系Cloud Admin确认。
users.duplicated.cloud-exist = The email already exists
users.identify.code.incorrect = The verification code is incorrect
users.missing.phone = The mobile phone number is incomplete. Please guide the user to log in to cloud and modify it in personal information
users.phone.not.exist = The account does not exist or has no permission. Please contact the project administrator to add it
user_no_permission = 账号无操作权限，请联系项目管理员添加
visit_cycle_duplicated_number = Duplicated visit cycle number
visit_cycle_dtp_required = Please select DTP mode
visit_cycle_duplicated_random1 = Random visit already exists, please do not repeat the configuration
visit_cycle_duplicated_random2 = For random items, only 2 random visits can be configured
visit_cycle_duplicated_random3 = The visit has randomization and dispensation data, please do not modify the configuration
visit_cycle_duplicated_version = Duplicated visit cycle version number
visit_cycle_formula_error = The formula cannot be recognized, please re-enter it.
visit_cycle_formula_visit_error = The weight at the random visit comparison condition has been enabled. Please configure the random visit to allow dispense.
visit_cycle_visit_formula_error = The weight at the random visit is enabled to calculate the weight for this time. Please configure the random visit to allow dispense.
visit_cycle_formula_customer_error = The formula cannot be recognized, please re-enter it.
work.task.error = Task has been completed and cannot be repeated
work.task.deleted = The task no longer exists. Please refresh and try again
work.task.scan.error = This task does not require scanning code confirmation operation
work.task.packageScan.error = This task does not require package scanning operation
work.task.package_number.error = Scanning failed, the scanned IP is not a system-generated IP in the current project/environment.
work.task.exist = A single operation has not been completed and cannot be repeated
work.task.medicine.error = Scan list has been updated. Please refresh and try again
medicine_other_repeat = Add duplicate, please add again
medicine_drug_configure_check = Deletion failed,IP was used or produced inventory.
medicine_drug_configure_other_check2 = Modification failed,"
medicine_drug_configure_other_check = " has inventory number
medicine_drug_configure_other_check1 = , and can not re-calculate the inventory automatically and accurately, please reconfirm the data before operation.
medicine_other_repeat_formula = Formula add duplicate, please add again
medicine_open_setting_repeat1 =  Configured
medicine_open_setting_openTrue =  Open Configuration
medicine_open_setting_openFlase =  Non-Open Configuration
medicine_open_setting_repeat2 =  , it need to be consistent , please re-configuration.
medicine_open_setting_repeat3 = Dispensation method is inconsistent with existing configuration, please confirm configuration again.
medicine_open_setting_approval = Saving failed, it is not allow the addition of only a single blinded IP control.
site_not_delete = Site cannot set as invalid after subject registration, randomization/IP dispensation.
order_status_error = Shipment status abnormal, please return to the list and try again.
subject_status_error = The subject status is abnormal, please refresh the page and try again.
approval_task_error = The task status is abnormal, please refresh the page and try again.
urgentUnblinding_approval_task_error = The task status is abnormal, please refresh the page and try again.
wms.cancel_order_fail = Bioquick shipment cancellation failed
unblinding_code_error = Unbliding code error
unblinding_password_error = Wrong password
common_configuration_error = configuration error
subject_urgentUnblindingApproval_applicationed = The subject unblinding (urgent) application has been submitted, please wait for the approval of the approver.
subject_urgentUnblindingApproval_pv_applicationed = The subject unblinding (pv) application has been submitted, please wait for the approval of the approver.
supply_plan_duplicated_name = Duplicated name, please enter again.
barcode_error = Saving failed, please close the packaging number function first
barcode_rule = IP barcode generation rules must be consistent. Please confirm.
barcode_package_rule = Packaging barcode generation rules must be consistent. Please confirm.
form_field_used = The field has been used and cannot be deleted
planned_case_error = Current number of randomizad subjects has exceeded the enrollment capping of project, please contact the system configuration personnel in time to adjust and then operate randomization
planned_case_random_error = Randomization failed and the project has reached the enrollment threshold upper limit.
planned_case_register_error = Registration failed and the project has reached the registration upper limit.
planned_case_screen_error = Screening failed and the project has reached the screening success upper limit.
subject_replace_auto_error = Replacement failed. The replacement random ID conflicts with the currently active randomization list. Please modify the rules before proceeding.
random_list_upload_error = The stratification factors are inconsistent, please synchronize before proceeding.
subject_random_factor_error = Randomization failed, please update the latest stratification results before proceeding.
simulate_random_factor_error = The system detects that the factors of different randomization list are not consistent, and it can't  randomization simulation at the same time. Please modify and operate after consistency.
simulate_random_factor_ratio_error = Randomization Simulation stratification configuration differs from actual stratification configuration. Please edit before running.
simulate_random_factor_ratio_list_error = System has detected an inconsistency between the random configuration and the randomization list. Please modify to ensure consistency before proceeding.
simulate_random_factor_ratio_total_error = Total stratification case numbers do not match the randomization count. Please adjust.
simulate_random_factor_ratio_lack_error = Stratification case numbers are missing. Please complete them.
subject_random_sequence_number_start_max_error = The start value of the sequence number exceeds the digit limit. Please check the random number configuration.
edc_register_error = Registration failed, the draft state does not allow registration, please contact the IRT project administrator, modify the cohort status to "Enrolling" before operation.
factor_calc_mapping_converge = Mapping relations converge, please confirm again.
randomization_config_factor_not_calc_form = The form field corresponding to the custom formula variable ID is missing. Please check the configuration.
randomization_config_factor_not_calc = Variable ID configuration calculation conflict, please confirm again.
randomization_config_factor_not_calc_type = Variable ID configuration control conflict, please confirm again.
report.template.name_exist = Template Name Already Exists
report.template.name_invalid = Template Name Invalid
project_at_random_error = 再随机项目只能添加两个阶段
medicine_package_err = Modification failed, package IP name is not match with setting, please reconfirm.

[enum]
cohort.status.complete = Complete
cohort.status.draft = Draft
cohort.status.enrollment = Enrollment
cohort.status.stop = Stop

[general]
common.operator = Operation
common.required = Please enter
common.error.default = An error occurred processing the request.
common.error.request = Bad request
common.error.not-logged-in = Not logged in
common.error.unauthorized = Unauthorized
common.error.not-found = Unknown request
common.checkbox = checkbox
common.date = date
common.dateTime = Time Selection Box
common.delete.fail = Deletion failed.
common.delete.success = Delete succeed
common.duplicated.factors = Duplicated factors
common.duplicated.names = Duplicated names
common.no = No
common.input = input
common.inputNumber = inputNumber
common.load.fail = Loaded failed
common.load.success = Loaded successfully
common.nil = "null"
common.operation.edc.dsp.fail = IP has dispensed at current visit. Information is returned a second time.
common.operation.edc.fail = The subject is randomized
common.operation.fail = Operation failed
common.operation.success = Operation Succeeded
common.radio = radio
common.remark = Remark
common.save.fail = Save failed
common.save.success = Save succeed
common.select = select
common.switch = switch
common.textArea = Multiline text box
common.yes = Yes
common.update.fail = Update failed
common.update.success = Update succeed
common.wrong.parameters = Parameter error
common.country = country

[history]
history.dispensing.updateBatch = IP【Update】,Expiration:{{.expirationDate}},batch No.:{{.batchNumber}},status:{{.status}},Update Count:{{.count}}
history.dispensing.cancel = 【Dispensing Withdrawed】{{.label}}:{{.subject}}     Withdrawed reason:{{.reason}}
history.dispensing.dispensing = 【Dispensation】{{.label}}:{{.subject}}    IP number:{{.medicine}}
history.dispensing.dispensing-other = 【Dispensation】 {{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}}
history.dispensing.dispensing-with-other = 【Dispensation】 {{.label}}:{{.subject}}    IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}}
history.dispensing.dispensing-with-other-reason = 【Dispensation】 {{.label}}:{{.subject}}     IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
history.dispensing.dispensingVisit = 【Unscheduled Dispensation】{{.label}}:{{.subject}}    IP number:{{.medicine}},Unscheduled Dispensation reason:{{.reason}}
history.dispensing.dispensingVisit-other = 【Unscheduled Dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}},Unscheduled dispensation reason:{{.reason}}
history.dispensing.dispensing-new = 【Dispensation】{{.label}}:{{.subject}} {{.form}}  {{.formula}} IP number:{{.medicine}},  remark:{{.remark}}
history.dispensing.dispensing-other-new = 【Dispensation】 {{.label}}:{{.subject}}  {{.form}} {{.formula}} unnumbered IP name /quantity date:{{.medicine}},  remark:{{.remark}}
history.dispensing.dispensing-with-other-new = 【Dispensation】 {{.label}}:{{.subject}}  {{.form}} {{.formula}} IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}}, remark:{{.remark}}
history.dispensing.dispensing-with-other-reason-new = 【Unscheduled Dispensation】 {{.label}}:{{.subject}}  {{.form}} {{.formula}}  IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
history.dispensing.dispensingVisit-new = 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.form}} {{.formula}} IP number:{{.medicine}},  Unscheduled dispensation reason:{{.reason}}
history.dispensing.dispensingVisit-other-new = 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.form}} {{.formula}} unnumbered IP name /quantity date:{{.medicine}}, Unscheduled dispensation reason:{{.reason}}
history.dispensing.dtp-dispensing = 【Application】{{.label}}:{{.subject}}    IP number:{{.medicine}} remark:{{.remark}}
history.dispensing.dtp-dispensing-other = 【Dispensation】 {{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}} remark:{{.remark}}
history.dispensing.dtp-dispensing-with-other = 【Dispensation】 {{.label}}:{{.subject}}    IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}} remark:{{.remark}}
history.dispensing.dtp-dispensing-with-other-reason = 【Unscheduled Dispensation】 {{.label}}:{{.subject}}     IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
history.dispensing.dtp-dispensingVisit = 【Unscheduled Dispensation】{{.label}}:{{.subject}}    IP number:{{.medicine}},Unscheduled Dispensation reason:{{.reason}}
history.dispensing.dtp-dispensingVisit-other = 【Unscheduled Dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}},Unscheduled dispensation reason:{{.reason}}
history.dispensing.dtp-reissue = 【Re-dispensation Application】 {{.label}}:{{.subject}}    IP number:{{.medicine}} reason:{{.remark}}
history.dispensing.dtp-reissue-other = 【Re-dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}} reason:{{.remark}}
history.dispensing.dtp-reissue-with-other = 【Re-dispensation】{{.label}}:{{.subject}}    IP:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}} reason:{{.remark}}
history.dispensing.replace-logistics = 【Replacement】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
history.dispensing.dispensing-logistics = 【Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}}, {{.form}} IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
history.dispensing.dispensing-logistics-noRandomNumber = 【Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}}, {{.form}}IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
history.dispensing.dispensingVisit-logistics = 【Unscheduled Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},  {{.form}} IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
history.dispensing.dispensingVisit-logistics-noRandomNumber = 【Unscheduled Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}}, {{.form}} IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
history.dispensing.reissue-with-logistics = 【Re-dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Re-dispensation Reason:{{.remark}}.
history.dispensing.reissue-with-logistics-noRandomNumber = 【Re-dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}},IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Re-dispensation Reason:{{.remark}}.
history.dispensing.invalid = 【Do Not Attend the Visit】{{.label}}:{{.subject}}, remark:{{.remark}}.
history.dispensing.register = 【Registration of Actually Used IP】  {{.label}}:{{.subject}},IPs dispensed by the system:{{.medicine}}, Actual dispensed IPs:{{.real_medicine}}
history.dispensing.reissue = 【Re-dispensation】 {{.label}}:{{.subject}}    IP number:{{.medicine}}, Re-dispensation Reason:{{.remark}}
history.dispensing.reissue-other = 【Re-dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}}, Re-dispensation Reason:{{.remark}}
history.dispensing.reissue-with-other = 【Re-dispensation】{{.label}}:{{.subject}}    IP:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Re-dispensation Reason:{{.remark}}
history.dispensing.replace = 【Replacement IP 】{{.label}}:{{.subject}}    Reason for Replacement:{{.reason}} ,replace IP number:{{.medicine}}, be replace IP number:{{.beReplaceMedicine}}
history.dispensing.retrieval = 【Retrieval IP】{{.label}}:{{.subject}}    Retrieved IP:{{.medicine}}
history.dispensing.retrieval-order = 【Retrieval IP】{{.label}}:{{.subject}}    Retrieved IP:{{.medicine}} reason:order close or cancel
history.dispensing.send-type-0 = Site(Site Inventory)
history.dispensing.send-type-1 = Site(Direct-to-Patient Shipment)
history.dispensing.send-type-2 = Deport(Direct-to-Patient Shipment)
history.dispensing.scanConfrim = 【Scan Confirm】{{.label}}:{{.subject}},IP number:{{.medicine}},shortCode:{{.shortCode}}
history.dispensing.dispensing-new-formula = 【Dispensation】{{.label}}:{{.subject}} {{.formula}} {{.form}}   IP number:{{.medicine}}, remark:{{.remark}}
history.dispensing.dispensing-other-new-formula = 【Dispensation】 {{.label}}:{{.subject}} {{.formula}}{{.form}}   unnumbered IP name /quantity date:{{.medicine}},  remark:{{.remark}}
history.dispensing.dispensing-with-other-new-formula = 【Dispensation】 {{.label}}:{{.subject}}  {{.formula}}{{.form}}  IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}}, remark:{{.remark}}
history.dispensing.dispensing-with-other-reason-new-formula = 【Unscheduled Dispensation】 {{.label}}:{{.subject}}   {{.formula}}{{.form}}  IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
history.dispensing.dispensingVisit-new-formula = 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.formula}}{{.form}}  IP number:{{.medicine}}, Unscheduled dispensation reason:{{.reason}}
history.dispensing.dispensingVisit-other-new-formula = 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.formula}}{{.form}}  unnumbered IP name /quantity date:{{.medicine}}, Unscheduled dispensation reason:{{.reason}}
history.dispensing.dispensing-logistics-formula = 【Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}}, {{.formula}}{{.form}} IP number:{{.medicine}},  Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
history.dispensing.dispensing-logistics-noRandomNumber-formula = 【Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}},{{.formula}}{{.form}} IP number:{{.medicine}},  Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
history.dispensing.dispensingVisit-logistics-formula = 【Unscheduled Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},  {{.formula}}{{.form}}  IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
history.dispensing.dispensingVisit-logistics-noRandomNumber-formula = 【Unscheduled Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}}, {{.formula}}{{.form}} IP number:{{.medicine}},  Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
history.dispensing.resume = 【Recover Dispensation】{{.label}}:{{.subject}}
history.dispensing.dispensingCustomer-dispensing = 【Dispensation】 {{.label}}:{{.subject}}, {{.data}}
history.dispensing.dispensingCustomer-dispensingVisit = 【Unscheduled Dispensation】 {{.label}}:{{.subject}}, {{.data}}
history.dispensing.dispensingCustomer-dispensingVisitCustomer = 【{{.dispensingType}}】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-reissue = 【Re-dispensation】 {{.label}}:{{.subject}}, {{.data}}
history.dispensing.dispensingCustomer-replace = 【Replacement IP】 {{.label}}:{{.subject}}, {{.data}}
history.dispensing.dispensingCustomer-retrieval = 【Retrieval IP】{{.label}}:{{.subject}}, {{.data}}
history.dispensing.dispensingCustomer-register = 【Registration of Actually Dispensed IP】 {{.label}}:{{.subject}}, {{.data}}
history.dispensing.dispensingCustomer-not-attend = 【Do Not Attend the Visit】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.dispensingCustomer-resume = 【Recover Dispensation】 {{.label}}：{{.subject}}， {{.data}}
history.dispensing.single.comma = ,
history.dispensing.single.period = .
history.dispensing.single.colon = :
history.dispensing.single.dispensingVisit = Unscheduled Dispensation
history.dispensing.single.dispensingApply =  Apply
history.dispensing.single.randomNumber = Randomization ID:{{.randomNumber}}
history.dispensing.single.visit = Visit Cycle:{{.visit}}
history.dispensing.single.form = {{.form}}
history.dispensing.single.date = Date Of Birth:{{.date}}
history.dispensing.single.weight = Weight:{{.weight}}
history.dispensing.single.height = Height:{{.height}}
history.dispensing.single.dose = {{.dose}}
history.dispensing.single.level = Dispensation Level:{{.level}}
history.dispensing.single.medicine = IP number:{{.medicine}}
history.dispensing.single.otherMedicine = unnumbered IP name /quantity date:{{.medicine}}
history.dispensing.single.order = Shipment No.:{{.order}}
history.dispensing.single.sendType = Dispensation Method
history.dispensing.single.vendor = Logistics:{{.vendor}}/{{.number}}
history.dispensing.single.outSize = Is it overdue:Yes
history.dispensing.single.remark = Remark:{{.remark}}
history.dispensing.single.reason = Reason:{{.reason}}
history.dispensing.single.reasonDispensingVisit = Unscheduled Dispensation reason:{{.reasonDispensingVisit}}
history.dispensing.single.reasonDispensingVisitCustomer = {{.dispensingType}} reason：{{.reasonDispensingVisit}}
history.dispensing.single.reasonReissue = Re-dispensation Reason:{{.reasonDispensingVisit}}
history.dispensing.single.reasonReplace = Reason for Replacement:{{.reasonDispensingVisit}}
history.dispensing.single.replaceNumber = replace IP number:{{.replaceNumber}}
history.dispensing.single.beReplaceMedicine = be replace IP number:{{.beReplaceMedicine}}
history.dispensing.single.retrievalMedicine = IP:{{.retrievalMedicine }}
history.dispensing.single.systemMedicine = IP delivery system:{{.systemMedicine}}
history.dispensing.single.realMedicine = Actual dispensed IPs:{{.realMedicine}}
history.dispensing.single.noKey = {{ .data }}
history.dispensing.single.notAttendRemark = "Remark：Open follow-up visit stage,current stage:{{.random}},the follow-up stage:{{.atRandom}}"
history.dispensing.single.resumeRemark = "Remark：Close follow-up visit stage,current stage:{{.currentStage}},the follow-up stage:{{.nextStage}}"
history.medicine.update-batch-expireDate = 【Edit】 Related shipment:{{.orderNumber}},Status:{{.status}},IP Number:{{.ipNumber}},Expiration Date:{{.expirationDate}},Batch NO.:{{.batchNumber}}.
history.medicine.other-update-batch-expireDate = 【Edit】 Related shipment:{{.orderNumber}},Status:{{.status}},IP :{{.ip}},Quantity :{{.count}},Expiration Date:{{.expirationDate}},Batch NO.:{{.batchNumber}}.
history.medicine.updateBatch = IP【Update】,Expiration:{{.expirationDate}},batch No.:{{.batchNumber}},status:{{.status}}.
history.medicine.updateOtherBatch = IP【Update】,Expiration:{{.expirationDate}},batch No.:{{.batchNumber}},status:{{.status}},Update Count:{{.count}}.
history.medicine.expired = 【Expired IP】 expired reason:Automatically expired. Current expired IP number:{{.packNumber}}.
history.medicine.freeze = Quarantine IP, quarantine reason:{{.reason}}, current quarantine IP number:{{.packNumber}}.
history.medicine.freeze-new = IP 【Quarantined】,quarantine number:{{.freezeNumber}},IP Number:{{.packNumber}},reason:{{.reason}}.
history.medicine.freeze-package-new = IP 【Quarantined】,Quarantine number:{{.freezeNumber}},IP Number:{{.packNumber}},Package Number:{{.packageNumber}},reason:{{.reason}}.
history.medicine.otherFreeze = IP 【Quarantined】, quarantine No.:{{.freezeNumber}}, reason:{{.freezeReason}}, name of current quarantined IP:{{.name}}, batch No.:{{.batch}}, expiry date:{{.expireDate}}, quarantine:{{.freezeCount}}.
history.medicine.otherMedicineLost = IP【Lost / Wasted】, reason:{{.freezeReason}}, name of current quarantined IP:{{.name}}, batch No.:{{.batch}}, Expiration:{{.expireDate}}, Lost / invalid:{{.freezeCount}}.
history.medicine.otherMedicineLost-new = IP【Lost/Wasted IP】,IP:{{.name}}, batch No.:{{.batch}}, expiry date:{{.expireDate}}, quarantine:{{.freezeCount}},reason:{{.freezeReason}}.
history.medicine.lost = Lost / Wasted reason:{{.reason}}, current lost / wasted IP number:{{.packNumber}}.
history.medicine.lost-new = IP【Lost/Wasted】, IP:{{.packNumber}},reason:{{.reason}}.
history.medicine.lost-package = Lost / Wasted reason:{{.reason}}, current lost / wasted IP number:{{.packNumber}},Package Number:{{.packageNumber}}.
history.medicine.release = IP【Release】, current IP number:{{.packNumber}},reason:{{.reason}}.
history.medicine.release-package = IP【Lifting Quarantine】, currently lifted IP number:{{.packNumber}},package number:{{.packageNumber}},reason:{{.reason}}.
history.medicine.quarantine-no = IP【Lifting Quarantine Approval】,IP Number:{{.packNumber}},Lifting Confirmation:Reject,Deny Reason:{{.reason}}.
history.medicine.approval = IP【Release Request】,reason:{{.reason}},IP Number:{{.packNumber}}.
history.medicine.locked = IP【Locked】.
history.medicine.otherLocked = IP【Locked】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.otherUse = IP【Dispensed】,current IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.otherCanUse = IP【Available】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.otherLost = IP【Lost/Wasted】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.otherToBeConfirm = IP【To be confirmed】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.otherExpired = IP【Expired】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.toBeConfirm = IP【To be confirmed】.
history.medicine.toBeConfirmNew = Shipment Number:{{.order}},Status:To be confirmed.
history.medicine.toFrozenNew = "Shipment Number:{{.orderNumber}},Status:Frozen."
history.medicine.toBeConfirmNewUpdate = Shipment Number:{{.orderNumber}},Status:To be confirmed.
history.medicine.confirmed = IP【Confirmed】.
history.medicine.confirmedNew = Shipment Number:{{.orderNumber}},Status:Confirmed.
history.medicine.otherConfirmed = IP【Confirmed】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.releaseLost = 【Lifting lost/wasted IP】 lost / wasted IP is:{{.reason}}, current IP number:{{.packNumber}}.
history.medicine.releaseLost-package = 【Lifted  lost/wasted IP】reason for lost/ wasted :{{.reason}}, current IP number:{{.packNumber}},Package Number:{{.packageNumber}}.
history.medicine.otherRelease = 【Lifting IP】The reason for Lifting Quarantine is:{{.reason}},name of current quarantine IP:{{.name}}, batch No.:{{.batch}}, Expiration date:{{.expireDate}}, quarantine:{{.count}}.
history.medicine.otherReleaseLost = 【Lost/Wasted IP】 lost / wasted IP is:{{.reason}},name of current lost / wasted IP:{{.name}}, batch No.:{{.batch}}, expiry date:{{.expireDate}}, quarantine:{{.count}}.
history.medicine.other-quarantine-no = IP【Lifting Approval】,Lifting Confirmation:Reject, Deny Reason:{{.reason}},IP name:{{.name}}, Batch Number.:{{.batch}}, expiry date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.other-approval = IP【Release Request】 reason:{{.reason}},IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.replace = 【IP Replacement】Wasted reason: {{.reason}}, current wasted IP number: {{.packNumber}}.
history.medicine.use = 【Set as available IP】 set as available reason:{{.reason}}, current available IP number:{{.packNumber}}.
history.medicine.use-package = Set as available IP,set as available reason:{{.reason}}, current available IP number:{{.packNumber}},Package Number:{{.packageNumber}}.
history.medicine.replace-dtp = IP replaced, before replacement: {{.beReplace}}, after replacement: {{.replace}}.
history.medicine.replace-with-dtp = 【IP Replacement】,IP【Lost/Wasted】,Reason:IP has been replaced.
history.medicine.cancel = IP【Withdrew】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:IP Withdrew.
history.medicine.register = IP【Dispensed】 Reason:Registration of Actually Used IP, IP delivery system:{{.medicine}}, Actually dispensed IP Number:{{.real_medicine}}.
history.medicine.apply = IP has been applied.
history.medicine.shipped = Shipment Number:{{.orderNumber}},Status:In delivery.
history.medicine.otherShipped = IP 【Delivered】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.receive = IP 【received】.
history.medicine.receiveNew = Shipment Number:{{.orderNumber}},Status:Received.
history.medicine.otherReceive = IP 【received】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
history.medicine.used = IP dispensed.
history.medicine.canUse = IP【Available】.
history.medicine.uploadCanUse = 【IP Upload】Status:To be Warehoused.
history.medicine.scanCanUse = 【Scan for Warehousing】Status:To be approved.
history.medicine.examinePassThrough = 【Approved】Status:To be Warehoused
history.medicine.examineRefuse = 【Reject】Status:Approval failed
history.medicine.updateName = 【Update】Status:To be approved
history.medicine.updateNameScanCan = 【Update】Status：To be scanned
history.medicine.release-usable = 【Release】Status:Available.
history.medicine.register-use = IP【Available】,reason:IP has been registered to be "available".
history.medicine.register-frozen = IP【Frozen】,reason:IP has been registered to be "frozen".
history.medicine.register-lost = IP【Lost/Wasted】,reason:IP has been registered as "Lost/Wasted".
history.medicine.in-order = IP to be confirmed, Subject No.:{{.subject}},IP:{{.medicine}}.
history.medicine.confrim = IP is confirmed.
history.medicine.dispensing-used = IP dispensed,Subject No.:{{.subject}},IP number:{{.medicine}}.
history.medicine.use-dtp = Shipment Number:{{.orderNumber}},Status:available.
history.medicine.use-order-cancel = IP Status Recover,reason:Shipment Cancelled.
history.medicine.use-order-close = IP Status Recover,reason:Close Shipment.
history.medicine.use-order-termination = The status of the IP is rescovered,reason:Terminate Shipment.
history.medicine.use-available-order-cancel = IP【Available】,reason:Shipment Cancelled,Status Recover.
history.medicine.use-available-order-close = IP【Available】,reason:Shipment is closed,Status Recover.
history.medicine.use-available-order-termination = IP【Available】,reason:Terminate Shipment,Status Recover.
history.medicine.use-frozen-order-cancel = IP【Frozen】,reason:Shipment Cancelled,Status Recover.
history.medicine.use-frozen-order-close = IP【Frozen】,reason:Shipment is closed,Status Recover.
history.medicine.use-frozen-order-termination = IP【Frozen】,reason:Terminate Shipment,Status Recover.
history.medicine.use-available-order-confrim = Shipment Number:{{.orderNumber}} ,Status:available,Reason:The IP was not confirmed during order confirmation,Status Recover.
history.medicine.use-available-order-frozen = Shipment Number:{{.orderNumber}} ,Status:frozen,Reason:The IP was not confirmed during order confirmation,Status Recover.
history.medicine.use-lose-order-cancel = IP【Lost/Wasted】,reason:Shipment Cancelled,Status Recover.
history.medicine.use-lose-order-close = IP【Lost/Wasted】,reason:Shipment is closed,Status Recover.
history.medicine.use-lose-order-termination = IP【Lost/Wasted】,reason:Terminate Shipment,Status Recover.
history.medicine.use-expired-order-cancel = IP【Expired】,reason:Cancel Shipment,Status Recover.
history.medicine.use-expired-order-close = IP【Expired】,reason:Shipment is closed,Status Recover.
history.medicine.use-expired-order-termination = IP【Expired】,reason:Terminate Shipment,Status Recover.
history.medicine.sku-freeze = IP 【Frozen】.
history.medicine.sku-freeze-reason = 【Set as freeze IP】 set as freeze reason:{{.reason}}, current freeze IP number:{{.packNumber}}.
history.medicine.sku-freeze-subject = IP【Frozen】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.sku-used-subject = IP【Dispensed】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.sku-use-subject = IP【Available】, Assigned Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.sku-in-order-subject = IP 【To be confirmed】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.sku-lost-subject = IP 【Lost/Wasted】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.rest-receive = IP【Received】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.rest-return = IP【Returned】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.rest-destroy = IP【Destroyed】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:{{.operation}}.
history.medicine.rest-return-retrieve = IP【Returned】,Operation:{{.operation}}.
history.medicine.rest-destroy-retrieve = IP【Destroyed】,Operation:{{.operation}}.
history.medicine.operation.dispensing = Dispense
history.medicine.operation.retrieval = Retrieve
history.medicine.operation.replace = IP Replace
history.medicine.operation.unscheduled = Unscheduled dispense
history.medicine.operation.reissue = Re-dispense
history.medicine.operation.register = Registration of Actually Used IP
history.medicine.sku-lose = Shipment Number:{{.orderNumber}},Status:【Lost】.
history.medicine.cancel-dtp = IP has been terminated, termination reason:{{.reason}}.
history.medicine.drugFreeze.drugFreeze = IP 【Quarantined】,quarantine number:{{.freezeNumber}}, IP Number:{{.medicines}}, reason:{{.freezeReason}}.
history.medicine.drugFreeze.drugFreeze-package = IP 【quarantined】,quarantine number:{{.freezeNumber}}, IP Number/Package Number:{{.medicines}}, reason:{{.freezeReason}}.
history.medicine.drugFreeze.otherDrugFreeze = IP 【Quarantined】,quarantine number:{{.freezeNumber}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},reason:{{.freezeReason}}.
history.medicine.drugFreeze.allDrugFreeze = IP 【Quarantined】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number:{{.medicines}}.
history.medicine.drugFreeze.allDrugFreeze-package = IP 【Quarantined】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
history.medicine.drugFreeze.lost = IP 【lost/Wasted】,quarantine number:{{.freezeNumber}}, IP Number:{{.medicines}} , reason:{{.freezeReason}}.
history.medicine.drugFreeze.lost-package = IP 【lost/Wasted】,quarantine number:{{.freezeNumber}}, IP Number/Package Number:{{.medicines}} , reason:{{.freezeReason}}.
history.medicine.drugFreeze.approval = IP【Lifting Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},IP Number:{{.medicines}}.
history.medicine.drugFreeze.approval-package = IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},IP Number/Package Number:{{.medicines}}.
history.medicine.drugFreeze.release = IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}}, IP Number:{{.medicines}} , reason:{{.freezeReason}}.
history.medicine.drugFreeze.release-package = IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}}, IP Number/Package Number:{{.medicines}} , reason:{{.freezeReason}}.
history.medicine.drugFreeze.quarantine-no = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
history.medicine.drugFreeze.quarantine-no-package = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number/Package Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
history.medicine.drugFreeze.quarantine-yes = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Pass.
history.medicine.drugFreeze.quarantine-yes-package = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number/Package Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Pass.
history.medicine.drugFreeze.otherLost = IP 【lost/Wasted】,quarantine number:{{.freezeNumber}},name of current lost/Wasted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.freezeReason}}.
history.medicine.drugFreeze.other-approval = IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}}.
history.medicine.drugFreeze.otherRelease = IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.freezeReason}}.
history.medicine.drugFreeze.other-quarantine-no = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
history.medicine.drugFreeze.other-quarantine-yes = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.untieReason}},Lifting Confirmation:Pass.
history.medicine.drugFreeze.allLost = IP 【lost/Wasted】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current lost/Wasted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}.
history.medicine.drugFreeze.allLost-package = IP 【lost/Wasted】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current lost/Wasted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
history.medicine.drugFreeze.all-approval = IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number:{{.medicines}}.
history.medicine.drugFreeze.all-approval-package = IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
history.medicine.drugFreeze.allRelease = IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}.
history.medicine.drugFreeze.allRelease-package = IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
history.medicine.drugFreeze.all-quarantine-no = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
history.medicine.drugFreeze.all-quarantine-no-package = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
history.medicine.drugFreeze.all-quarantine-yes = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Pass.
history.medicine.drugFreeze.all-quarantine-yes-package = IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Pass.
history.medicine.change.newToConfirm = Related shipment:{{.orderNumber}},Status:To be confirmed,Original IP:{{.oldMedicine}},Post-replacement IP:Post-replacement IP:{{.newMedicine}},Replacement Reason:{{.reason}}。
history.medicine.change.newConfirmed = Related shipment:{{.orderNumber}},Status:Confirmed,Original IP:{{.oldMedicine}},Replacement IP name/batch number/expiration date/count:{{.newMedicine}},Replacement Reason:{{.reason}}。
history.medicine.change.old = IP 【Quarantined】, quarantine number:{{.freezeNumber}},IP Number:{{.oldMedicine}},Related shipment:{{.orderNumber}},Replacement Reason:{{.reason}}。
history.medicine.change.otherNewToConfirm = Related shipment:{{.orderNumber}},To be confirmed,Original IP name/batch number/expiration date/count:{{.oldMedicine}},Replacement IP name/batch number/expiration date/count:{{.newMedicine}},Replacement Reason:{{.reason}}。
history.medicine.change.otherNewConfirmed = Related shipment:{{.orderNumber}},Status:Confirmed,Original IP name/batch number/expiration date/count:{{.oldMedicine}},Replacement IP name/batch number/expiration date/count:{{.newMedicine}},Replacement Reason:{{.reason}}。
history.medicine.change.otherOld = IP 【Quarantined】,quarantine number:{{.freezeNumber}},IP name/batch No/Expiration/count:{{.oldMedicine}},Related shipment:{{.orderNumber}},Replacement Reason:{{.reason}}。
history.order.cancel-new = Shipment No.:{{.orderNumber}},shipment【Canceled】, reason for cancellation:{{.reason}}.
history.order.close-new = Shipment No.:{{.orderNumber}},order【Closed】, reason for closed:{{.reason}}.
history.order.confrim-new = Shipment No.:{{.orderNumber}},order【Confirmed】.
history.order.confrim-expectedArrivalTime-new = Shipment No.:{{.orderNumber}},order【Confirmed】,Expected Arrival Time:{{.expectedArrivalTime}}.
history.order.create-new = Shipment No.:{{.orderNumber}},order【To be confirmed】.
history.order.create-expectedArrivalTime-new = Shipment No.:{{.orderNumber}},order【To be confirmed】,Expected Arrival Time:{{.expectedArrivalTime}}.
history.order.lost-new = Shipment No.:{{.orderNumber}},shipment【Lost】, reason for loss:{{.reason}}.
history.order.receive-new = Shipment No.:{{.orderNumber}},order【Received】.
history.order.receive-actualTime-new = Shipment No.:{{.orderNumber}},order【Received】,Actual Receipt Time:{{.actualReceiptTime}}.
history.order.send-new = Shipment No.:{{.orderNumber}},shipment【In delivery】.
history.order.send-expectedArrivalTime-new = Shipment No.:{{.orderNumber}},shipment【In delivery】,Expected Arrival Time:{{.expectedArrivalTime}}.
history.order.logistics—other = 【Logistics information update】Logistics Vendor: {{.logistics}}, Other logistics: {{.other}}, logistics Number: {{.number}}.
history.order.logistics = 【Logistics information update】Logistics Vendor: {{.logistics}}, Tracking Number: {{.number}}.
history.order.logistics—other-actualTime = 【Logistics information update】Shipment No.:{{.orderNumber}},Actual Receipt Time:{{.actualReceiptTime}}.
history.order.logistics-actualTime = 【Logistics information update】Shipment No.:{{.orderNumber}},Actual Receipt Time:{{.actualReceiptTime}}.
history.order.logistics—other-actualTime-all = 【Logistics information update】Logistics Vendor:{{.logistics}}, Other logistics:{{.other}},logistics Number:{{.number}},Actual Receipt Time:{{.actualReceiptTime}}.
history.order.logistics-actualTime-all = 【Logistics information update】Logistics Vendor:{{.logistics}},logistics Number:{{.number}},Actual Receipt Time:{{.actualReceiptTime}}.
history.order.send-with-logistics = Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Logistics Number:{{.number}}.
history.order.send-with-logistics-expectedTime = Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Logistics Number:{{.number}},,Expected Arrival Time:{{.expectedArrivalTime}}.
history.order.send-with-other-logistics = Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Other Vendor:{{.other}},Logistics Number:{{.number}}.
history.order.send-with-other-logistics-expectedTime = Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Other Vendor:{{.other}},Logistics Number:{{.number}},,Expected Arrival Time:{{.expectedArrivalTime}}.
history.order.cancel-dtp = Shipment No.:{{.orderNumber}}, order 【Terminated】, termination reason:{{.reason}}.
history.order.cancel = Cancel shipment, shipment has been cancelled, reason for cancellation:{{.reason}}.
history.order.close = Close shipment, shipment closed, reason for closing:{{.reason}}.
history.order.confirm-task = Send shipment confirmation task, shipment requested.
history.order.confirm-task-finish = The shipment confirmation task has been completed and the shipment is in delivery.
history.order.confrim = Confirm shipment, shipment requested.
history.order.create = Create shipment, shipment to be confirmed.
history.order.close-with-dtp = Shipment No.:{{.orderNumber}}, order 【closed】,Reason:All IP are replaced.
history.order.close-with-register = Shipment No.:{{.orderNumber}}, order 【closed】,Reason:IP has been registered to be "available/frozen".
history.order.close-with-register-lost = Shipment No.:{{.orderNumber}}, order 【closed】,Reason:IP has been registered to be "lost/Wasted".
history.order.lost = Shipment lost, Shipment lost, reason for loss:{{.reason}}.
history.order.receive = Shipment received.
history.order.receive-task-confrim = Send IP receive task, shipment requested.
history.order.receive-task-finish = The task of receiving IP has been completed, and the shipment has been received.
history.order.receive-task-send = Send IPreceiving task, Order Shipping.
history.order.send = Deliver the shipment. The shipment is in delivery.
history.order.apply = Create shipment, shipment requested. remark:{{.remark}}.
history.order.approval = Shipment application【approved】,order【To be confirmed】,Shipment No.:{{.orderNumber}}.
history.order.change = 【Replacement】,Shipment No.:{{.orderNumber}},Original IP details:{{.oldMedicines}},Replacement IP details:{{.newMedicines}}, Reason for Replacement:{{.reason}}.
history.order.batch = 【Edit】,Shipment No.:{{.orderNumber}}，IP-Expiration Date-Batch Number:{{.ipExpireDateBatch}}.
history.order.expireDate = 【Edit】， {{.data}}
history.order.expireDateBatch.orderNumber = Shipment No.：{{.orderNumber}}
history.order.expireDateBatch.ipExpireDateBatch = IP-Expiration Date-Batch Number：{{.ipExpireDateBatch}}
history.project.cohort.add = 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},alert threshold:{{.reminderThresholds}}%
history.project.cohort.delete = 【Cohort/Re-randomization Deleted】 name:{{.cohortName}}
history.project.cohort.edcAdd = 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},factor:{{.factor}},alert threshold:{{.reminderThresholds}}%
history.project.cohort.edcEdit = 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},factor:{{.factor}},alert threshold:{{.reminderThresholds}}%
history.project.cohort.edcRAdd = 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},factor:{{.factor}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
history.project.cohort.edcREdit = 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capacity:{{.capacity}},status:{{.state}},factor:{{.factor}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
history.project.cohort.edit = 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},alert threshold:{{.reminderThresholds}}%
history.project.cohort.rAdd = 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
history.project.cohort.rEdit = 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capacity:{{.capacity}},status:{{.state}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
history.project.drugConfigur.add = Add IP configuration,group:{{.group}},IP configuration:{{.drugConfigure}}
history.project.drugConfigur.delete = Delete IP configuration,group:{{.group}},IP configuration:{{.drugConfigure}}
history.project.drugConfigur.edit = Edit IP configuration,group:{{.group}},IP configuration:{{.drugConfigure}}
history.project.env.add = 【Adding Environment】Create {{.env}}environment
history.project.info.add = 【Adding project】 project number:{{.projectNum}},project name:{{.projectName}},sponsor:{{.biddingUnit}},project description:{{.projectDesc}}
history.project.info.edit = Edit project, project number:{{.projectNum}},project name:{{.projectName}},sponsor:{{.biddingUnit}},project description:{{.projectDesc}}
history.project.medicine.batch = 【Batch Management】 The inventory batch number is updated:{{.batch}},expiry date:{{.expireDate}},status:{{.status}},update batch number:{{.batchUpdate}},update expiry date:{{.expireDateUpdate}}
history.project.medicine.upload = 【Uploading IP】 IP name:{{.drugName}},quantity:{{.count}}
history.randomization.attribute = properties:Randomization or not:{{.random}}, Display Randomization ID:{{.isRandomNumber}}, Dispensing or not:{{.dispensing}}, Blind or open label:{{.blind}}, Use prefix:{{.prefix}}, Subject No. prifix:{{.prefixExpression}}, Prefix text:{{.subjectReplaceText}}, Exact number of digits (1:less than or equal to / 2:equal to):{{.accuracy}}, Exact digit:{{.digit}}, When running the delivery algorithm, the quarantined items are calculated as part of the re usable inventory of the research institution:{{.isFreeze}}, EDC docking IP Configuration Label:{{.edcDrugConfigLabel}}, Is the segment random:{{.segment}}{{.segmentLength}}
history.randomization.block.distributionFactor = 【Random segmentation】 【{{.name}}】 Block:{{.block}} Distribution factor:{{.valueSite}}
history.randomization.block.distributionSite = 【Random segmentation】 【{{.name}}】 Block:{{.block}}  Distribution site:{{.valueSite}}
history.randomization.block.generate = 【Block randomization】【 Randomization ID generation】 Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}},Block configuration block length (number of blocks):{{.blockNumber}},Factor:{{.factors}},ID length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}},Generated quantity:{{.numberText}}
history.randomization.block.upload = 【Block Randomization】【Randomization ID Upload】 Name:{{.name}},Factor:{{.factors}},Acceptable block size:{{.blockSize}},Number of uploads:{{.numberText}}
history.randomization.config.block.distributionFactor = 【Random segmentation】 【{{.name}}】 Block:{{.block}} Distribution factor:{{.valueSite}}
history.randomization.config.block.distributionSite = 【Random segmentation】 【{{.name}}】 Block:{{.block}}  Distribution site:{{.valueSite}}
history.randomization.config.block.generate = 【Block randomization】【 Randomization ID generation】 Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}},Block configuration block length (number of blocks):{{.blockNumber}},Factor:{{.factors}},ID length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}},Generated quantity:{{.numberText}}
history.randomization.config.block.upload = 【Block Randomization】【Randomization ID Upload】 Name:{{.name}},Factor:{{.factors}},Acceptable block size:{{.blockSize}},Number of uploads:{{.numberText}}
history.randomization.config.factor.add = 【Factor Add】 Field Code:{{.number}}, Name:{{.label}} , Control type:{{ .type}}, Options:{{.options}}
history.randomization.config.factor.addEDC = 【Factor Add】 Field Code:{{.number}}, Name:{{.label}} ,Variable name:{{ .name}}, Control type:{{ .type}}, Options:{{.options}}
history.randomization.config.factor.clean = 【Empty other layers】 【{{ .name}}】 Block[{{ .block}}] Empty other layers
history.randomization.config.factor.countryEnable = 【Set country as stratification factor】 Enable
history.randomization.config.factor.delete = 【Factor Deleted】 Field Code:{{.oldNumber}}, Name:{{.oldLabel}} , Control type:{{ .oldType}}, Options {{.oldOptions}}
history.randomization.config.factor.deleteEDC = 【Factor Deleted】 Field Code:{{.oldNumber}}, Name:{{.oldLabel}} ,Variable name:{{ .oldName}}, Control type:{{ .oldType}}, Options {{.oldOptions}}
history.randomization.config.factor.disableCountryLayer = 【Set country as stratification factor】 Disable
history.randomization.config.factor.disableLayer = 【Set stratification factor】 Disable
history.randomization.config.factor.disableSiteLayer = 【Set site as stratification factor】 Disable
history.randomization.config.factor.edit = 【Stratification Factor Edit】 Replacement Field Code:{{.oldNumber}}, Name:{{.oldLabel}} , Control type:{{ .oldType}}, Options {{.oldOptions}} with Field Code:{{.number}}, Name:{{.label}} ,Variable name:{{ .name}}, Control type:{{ .type}}, Options:{{.options}}
history.randomization.config.factor.editEDC = 【Stratification Factor Edit】 Replacement Field Code:{{.oldNumber}}, Name:{{.oldLabel}} ,Variable name:{{ .oldName}}, Control type:{{ .oldType}}, Options {{.oldOptions}} with Field Code:{{.number}}, Name:{{.label}} ,Variable name:{{ .name}}, Control type:{{ .type}}, Options:{{.options}}
history.randomization.config.factor.number = 【Setting Stratification Factor number】 【{{ .name}}】  Stratification Factor:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
history.randomization.config.factor.siteEnable = 【Stratification factor Edit】 Set site as stratification factor
history.randomization.config.form.add = 【Adding Form】 Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}}
history.randomization.config.form.addEDC = 【Adding Form】 Field name:{{.name}} Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}}
history.randomization.config.form.addOption = 【Adding Form】 Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}}  Options:{{.options}}
history.randomization.config.form.addOptionEDC = 【Adding Form】 Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}}  Options:{{.options}}
history.randomization.config.form.clean = 【Clean other factor】 【{{ .name}}】 Block {{ .block}} clean other factor
history.randomization.config.form.delete = 【Form Deletion】 Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}}
history.randomization.config.form.deleteEDC = 【Form Deletion】 Field name:{{.name}} Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}}
history.randomization.config.form.deleteOption = 【Form Deletion】Variable name:{{.label}}, Can be modified or not:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}}
history.randomization.config.form.deleteOptionEDC = 【Form Deletion】Field name:{{.name}} Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}}
history.randomization.config.form.edit = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} ) replace (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
history.randomization.config.form.editEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} ) replace  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
history.randomization.config.form.editOption = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} )    Modify as (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
history.randomization.config.form.editOptionEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}})    replace (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
history.randomization.config.form.editOptionend = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} )  Modify as (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
history.randomization.config.form.editOptionendEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Editable or Not:{{ .oldModifiable}}, Control type:{{.oldType}} )    replace (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.oldOptions}})
history.randomization.config.form.editOptionstart = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
history.randomization.config.form.editOptionstartEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
history.randomization.config.form.editOptionstartend = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by ( Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
history.randomization.config.form.editOptionstartendEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
history.randomization.config.form.editend = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} ) Modify as  (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
history.randomization.config.form.editendEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} )  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
history.randomization.config.form.editstart = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) replace  (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
history.randomization.config.form.editstartEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) replace  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
history.randomization.config.form.editstartend = 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) replace  ( Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
history.randomization.config.form.editstartendEDC = 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) Modify as  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
history.randomization.config.group.add = 【Adding Treatment Group】 Group Code:{{.number}},Group Name:{{.name}}
history.randomization.config.group.delete = 【Treatment Group Deletion】  Group Code:{{.number}}, Group Name:{{.name}}
history.randomization.config.group.edit = 【Treatment Group Edit】 Replace   Group Code:{{.oldNumber}}, Group Name:{{.oldName}} with Group Code:{{.number}}, Group Name:{{.name}}
history.randomization.config.list.disableStatus = 【{{.name}}】 State adjustment Disable randomization Status
history.randomization.config.list.enableStatus = 【status adjustment】 【{{.name}}】 Enable random state
history.randomization.config.list.invalid = 【Wasted】Randomization list【{{.name}}】has been voided
history.randomization.config.minimize = Minimizing random Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}}, Stratification factor stratification (weight ratio):{{.factors}},Bias probability:{{.probability}},Total cases:{{.total}},Number length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}}
history.randomization.config.typeBlock = 【Randomization type adjustment】 Enable Block randomization
history.randomization.config.typeMin = 【Randomization Type Adjustment】 Enable Minimized Randomization
history.randomization.factor.clean = 【Empty other layers】 【{{ .name}}】 Block[{{ .block}}] Empty other layers
history.randomization.factor.addNumber = 【Add layers number】 【{{ .name}}】 layers:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
history.randomization.factor.editNumber = 【Edit layers number】 【{{ .name}}】 layers:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
history.randomization.factor.delNumber = 【Delete layers number】 【{{ .name}}】 layers:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
history.randomization.factor.number = 【Setting Stratification Factor number】 【{{ .name}}】  Stratification Factor:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
history.randomization.list.disableStatus = 【Status adjustment】 Disable randomization Status
history.randomization.list.enableStatus = 【Invalidation】Randomization list has been invalidated
history.randomization.list.invalid = 【Invalidation】Randomization list has been invalidated
history.randomization.list.site = 【Edit】 Binding Center:{{.siteName}}
history.randomization.minimize = Minimizing random Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}}, Stratification factor stratification (weight ratio):{{.factors}},Bias probability:{{.probability}},Total cases:{{.total}},Number length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}}
history.subject.add = 【Add】 {{.content}}
history.subject.at-random-add = 【Add】 Stage:{{.stage}},{{.content}}
history.subject.delete = 【Delete】 {{.label}}：{{.name}}
history.subject.beReplaced = 【Be replaced The current】 subject is already a subject:{{.name}} replace
history.subject.replaced-new = 【Subject Replacement】original subjects:{{.name}},original Randomization ID:{{.beReplaceRandomNumber}}, replacement subjects:{{.replaceName}}, replacement subject Randomization ID:{{.replaceRandomNumber}}
history.subject.pvUnblinding = 【PV Unblinding]】 subject:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}}
history.subject.remark-pvUnblinding = 【PV Unblinding]】 subject:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}},Remark:{{.remark}}
history.subject.random = 【Randomization】 subject:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}}
history.subject.randomSub = 【Randomization】 subject:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}},Sub Group:{{.subGroup}}
history.subject.randomNoNumber = 【Randomization】 subject:{{.name}} It's already randomized,Group:{{.group}}
history.subject.randomNoNumberSub = 【Randomization】 subject:{{.name}} It's already randomized,Group:{{.group}},Sub Group:{{.subGroup}}
history.subject.replaced = 【Replacement】The current subject is derived from subject replacement,Replaced subjects:{{.name}} Randomization ID:{{.randomNumber}}
history.subject.signOut = 【Stop】 subject:{{.name}},Reason for Stop:{{.reason}}
history.subject.unblinding = 【Unblinding】 subject:{{.name}} Unblinding,Reasons for uncovering blindness:{{.reasonStr}},Remark:{{.reason}},Whether the Sponsor has been notified or not:{{.isSponsor}},Remark:{{.remark}}
history.subject.unblinding-success = Unblinding (urgent) success
history.subject.at-random-unblinding-success = Stage:{{.stage}},Unblinding (urgent) success
history.subject.unblinding-application = 【Applied for unblinding (urgent)】approval number: {{.approvalNumber}}, status pending approval
history.subject.at-random-unblinding-application = 【Applied for unblinding (urgent)】 Stage:{{.stage}},approval number: {{.approvalNumber}}, status pending approval
history.subject.unblinding-application-pv = 【Applied for unblinding (pv)】approval number: {{.approvalNumber}}, status pending approval
history.subject.at-random-unblinding-application-pv = 【Applied for unblinding (pv)】 Stage:{{.stage}},approval number: {{.approvalNumber}}, status pending approval
history.subject.unblinding-approval-agree = 【Unblinding (urgent) approved】approval number: {{.approvalNumber}}, status passed
history.subject.unblinding-approval-agree-pv = 【Unblinding (pv) approved】approval number: {{.approvalNumber}}, status passed
history.subject.unblinding-approval-reject = 【Unblinding (Urgent) Approved】approval Number: {{.approvalNumber}}, Status Rejected, Reason: {{.reason}}
history.subject.unblinding-approval-reject-pv = 【Unblinding (pv) Approved】approval Number: {{.approvalNumber}}, Status Rejected, Reason: {{.reason}}
history.subject.update = 【Modify】 {{.content}}
history.subject.at-random-update = 【Modify】 Stage:{{.stage}},{{.content}}
history.subject.at-random-random = 【Randomization】 Stage:{{.stage}},{{.content}}
history.subject.transfer = 【Transfer】 {{.label}}:{{.subjectNo}},Original site:{{.oldSite}},New site:{{.newSite}}.
history.subject.at-random-transfer =【Transfer】 Stage:{{.stage}},{{.label}}:{{.subjectNo}},Original site:{{.oldSite}},New site:{{.newSite}}.
history.subject.switch-cohort = 【Switch Cohort】 {{.label}}：{{.subjectNo}}，Original cohort：{{.oldCohort}}，New cohort：{{.newCohort}}.
history.subject.updateSubjectNo = 【Edit】 Subject:{{.oldSubjectNo}},Modify as:{{.shortname}}
history.subject.joinTime = 【Edit】 {{.label}}:{{.subjectNo}},Enrollment Time:{{.joinTime}}.
history.subject.at-random-joinTime = 【Edit】 Stage:{{.stage}},{{.label}}:{{.subjectNo}},Enrollment Time:{{.joinTime}}.
history.subject.start-follow-up-visits = 【Open follow-up visit stage】 Stage:{{.currentStage}},{{.label}}:{{.subjectNo}},current stage:{{.currentStage}},the follow-up stage:{{.nextStage}}
history.subject.close-follow-up-visits = 【Close follow-up visit stage】 Stage:{{.currentStage}},{{.label}}:{{.subjectNo}},current stage:{{.currentStage}},the follow-up stage:{{.nextStage}}
history.subject.label.common-key-value = {{.name}}:{{.value}}
history.subject.label.common-key-value1 = {{.name}}：{{.value}}
history.subject.label.replaced-new = 【Subject Replacement】original {{.label}}:{{.name}},original Randomization ID:{{.beReplaceRandomNumber}}, replacement {{.label}}:{{.replaceName}}, replacement subject Randomization ID:{{.replaceRandomNumber}}
history.subject.label.at-random-replaced-new-a = 【Subject Replacement】 original{{.label}}:{{.name}},replacement{{.label}}:{{.replaceName}},Stage:{{.stage}},original Randomization ID:{{.beReplaceRandomNumber}},replacement subject Randomization ID:{{.replaceRandomNumber}}
history.subject.label.at-random-replaced-new-b = 【Subject Replacement】 original{{.label}}:{{.name}},replacement{{.label}}:{{.replaceName}},Stage:{{.stage}},original Randomization ID:{{.beReplaceRandomNumber}},replacement subject Randomization ID:{{.replaceRandomNumber}}；Stage:{{.stage2}},original Randomization ID:{{.beReplaceRandomNumber2}},replacement subject Randomization ID:{{.replaceRandomNumber2}}
history.subject.label.pvUnblinding = 【PV Unblinding]】 {{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}}
history.subject.label.at-random-pvUnblinding = 【PV Unblinding]】 Stage:{{.stage}},{{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}}
history.subject.label.remark-pvUnblinding = 【PV Unblinding]】 {{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}},Remark:{{.remark}}
history.subject.label.at-random-remark-pvUnblinding = 【PV Unblinding]】 Stage:{{.stage}},{{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}},Remark:{{.remark}}
history.subject.label.random = 【Randomization】 {{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}}
history.subject.label.at-random-random = 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}}
history.subject.label.randomSub = 【Randomization】 {{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}},Sub Group:{{.subGroup}}
history.subject.label.at-random-randomSub = 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}},Sub Group:{{.subGroup}}
history.subject.label.randomNoNumber = 【Randomization】 {{.label}}:{{.name}} It's already random,Group:{{.group}}
history.subject.label.at-random-randomNoNumber = 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Group:{{.group}}
history.subject.label.randomNoNumberSub = 【Randomization】 {{.label}}:{{.name}} It's already random,Group:{{.group}},Sub Group:{{.subGroup}}
history.subject.label.at-random-randomNoNumberSub = 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Group:{{.group}},Sub Group:{{.subGroup}}
history.subject.label.signOut = 【Stop】 {{.label}}:{{.name}},Reason for Stop:{{.reason}}
history.subject.label.at-random-signOut = 【Stop】 Stage:{{.stage}},{{.label}}:{{.name}},Reason for Stop:{{.reason}}
history.subject.label.signOutReal = 【Stop】 {{.label}}:{{.name}} It's already stop,Reason for Stop:{{.reason}},Actual Stopped Date:{{.signOutRealTime}}
history.subject.label.at-random-signOutReal = 【Stop】 Stage:{{.stage}},{{.label}}:{{.name}} It's already stop,Reason for Stop:{{.reason}},Actual Stopped Date:{{.signOutRealTime}}
history.subject.label.unblinding = 【Unblinding】 {{.label}}:{{.name}} Unblinding,Reasons for uncovering blindness:{{.reasonStr}},Remark:{{.reason}},Whether the Sponsor has been notified or not:{{.isSponsor}},Remark:{{.remark}}
history.subject.label.at-random-unblinding = 【Unblinding】 Stage:{{.stage}},{{.label}}:{{.name}} Unblinding,Reasons for uncovering blindness:{{.reasonStr}},Remark:{{.reason}},Whether the Sponsor has been notified or not:{{.isSponsor}},Remark:{{.remark}}
history.subject.label.updateSubjectNo = 【Edit】 {{.label}}:{{.oldSubjectNo}},Modify as:{{.shortname}}
history.subject.label.screen = 【Screening】{{.label}}:{{.name}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date{{.icfTime}}
history.subject.label.at-random-screen = 【Screening】 Stage:{{.stage}},{{.label}}:{{.name}} It's already screening,Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date{{.icfTime}}
history.subject.label.screenScreenFail = 【Screening】{{.label}}:{{.name}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}}
history.subject.label.update = 【Modify】{{.label}}:{{.name}},{{.content}}
history.subject.label.at-random-update = 【Modify】 Stage:{{.stage}},{{.label}}:{{.name}},{{.content}}
history.subject.label.updateCustomize = 【Modify】{{.label}}:{{.name}},{{.content}},{{.updateFields}}
history.subject.label.update2Customize = 【Edit】 {{.updateFields}}
history.subject.label.updateCustomizeConnectingSymbol = ,
history.subject.label.updateCustomizeLastSymbolKey = .
history.subject.label.updateCustomizeJoinTime = Enrollment Time:{{.joinTime}}
history.subject.label.updateCustomizeStage = Stage:{{.stage}}
history.subject.label.updateCustomizeSignOutRealTime = Actual Stopped Date:{{.signOutRealTime}}
history.subject.label.updateCustomizeIsScreen = Screening successful or not:{{.isScreen}}
history.subject.label.updateCustomizeScreenTime = Screening Date:{{.screenTime}}
history.subject.label.updateCustomizeIcfTime = ICF Signed Date:{{.icfTime}}
history.subject.label.updateCustomizeReason = Reason for Stop:{{.reason}}
history.subject.label.updateCustomizeRemark = Complete study-remark：{{.remark}}
history.subject.label.updateSignOutTime = 【Modify】{{.label}}:{{.name}},{{.content}},Actual Stopped Date:{{.signOutRealTime}}
history.subject.label.updateScreen = 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date:{{.icfTime}}
history.subject.label.updateScreenSignOutTime = 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date:{{.icfTime}},Actual Stopped Date:{{.signOutRealTime}}
history.subject.label.updateScreenFail = 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}}
history.subject.label.updateScreenFailSignOutTime = 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},Actual Stopped Date:{{.signOutRealTime}}
history.subject.label.finish = 【Complete Study】{{.label}}:{{.name}},Randomization ID:{{.randomNumber}},It's already complete study,Remark:{{.remark}}
history.subject.label.at-random-finish = 【Complete Study】 Stage:{{.stage}},{{.label}}:{{.name}},Randomization ID:{{.randomNumber}},It's already complete study,Remark:{{.remark}}
history.supply-plan.add = 【new supply plan】 Supply Plan Name:{{.name}},plan description:{{.description}}
history.supply-plan.update = 【edit supply plan】 Supply Plan Name:{{.name}},plan description:{{.description}}
history.supply-plan-medicine.add = 【Adding IP configuration in supply plan】 IP:{{.medicineName}},Quantity of Initial Shipment:{{.initSupply}} , value:{{.warning}}, Maximum buffer:{{.buffer}},  Re-supply:{{.secondSupply}},Do not deliver days:{{.unDistributionDate}},Do not dispensation date:{{.unProvideDate}},   Expiration Reminder:{{.validityReminder}},    Automatic re-supply:{{.autoSupply}},  Automatic re-supply quantity:{{.autoSupplySize}},    Supplyment Mode:{{.supplyMode}}.
history.supply-plan-medicine.update = 【edit IP configuration in supply plan】 IP:{{.medicineName}},Quantity of Initial Shipment:{{.initSupply}} , value:{{.warning}}, Maximum buffer:{{.buffer}},  Re-supply:{{.secondSupply}},Do not deliver days:{{.unDistributionDate}},Do not dispensation date:{{.unProvideDate}},   Expiration Reminder:{{.validityReminder}},   Automatic re-supply:{{.autoSupply}},  Automatic re-supply quantity:{{.autoSupplySize}},   Supplyment Mode:{{.supplyMode}}.
medicine.autoSupplySize1 = Maximum buffer
medicine.autoSupplySize2 = Re-supply
medicine.supplyMode1 = Total IP supplementation
medicine.supplyMode2 = Single-product Supplement
medicine.supplyMode3 = Full-product Supplement Plus One Random IP
medicine.supplyMode4 = Single-product Supplement plus one random IP
subject_urgentUnblindingApproval_reason_other = Other
subject_urgentUnblindingApproval_reason_sae = SAE
subject_urgentUnblindingApproval_reason_pregnancy = Pregnancy
subject_urgentUnblindingApproval_reason_policy = Policy Requirements

[operation_log]
operation_log.label.medicine = IP Name
operation_log.label.site = Site Number
operation_log.label.dept = Depot Name
operation_log.label.supply = Supply Plan Name
operation_log.label.group = Group Code
operation_log.label.factor = Stratification Factor
operation_log.label.list = Randomization List Name
operation_log.label.invalidList = Invalidate Randomization List
operation_log.label.visitCycle = Visit Cycle
operation_log.label.drugConfigure = Treatment Design
operation_log.label.packageConfigure = Package Configuration
operation_log.label.medicinesList = Packlist
operation_log.label.updateBatch = Batch Management
operation_log.label.otherMedicines = Unnumbered IP
operation_log.label.simulate_random_name = Name
operation_log.label.name = Field Name
operation_log.label.attribute = Properties
operation_log.label.uploadMedicine = Upload IP
operation_log.label.barcode_add = Generate Barcode
operation_log.label.uploadPacklist = Upload Packlist
operation_log.label.deleteMedicine = Mass Delete IP
operation_log.label.projectUser = User
operation_log.label.project = Project settings
operation_log.module.barcode = Project Design-Coding Configuration
operation_log.module.supply = Project Design-Supply Plan
operation_log.module.supply_detail = Project Design-Supply Plan-Supply Plan Detail
operation_log.module.random_design = Project Design-Randomization Design-Randomization Design
operation_log.module.project_site = Project Design-Site Management
operation_log.module.project_storehouse = Project Design-Depot Management
operation_log.module.project_storehouse_medicine_alert = Project Design-Depot Management-IP Alert
operation_log.module.visitCycle = Project Design-Treatment Design-Visit Cycle
operation_log.module.visitSetting = Project Design-Treatment Design-Visit Cycle-Setting
operation_log.module.attribute = Project Design-Properties
operation_log.module.drug_configure = Project Design-Treatment Design-Treatment Design
operation_log.module.drug_configure_setting = Project Design-Treatment Design-Treatment Design-Setting
operation_log.module.package_configure = Project Design-Packlist- Configuration
operation_log.module.examine = 项目构建-研究产品列表-审核
operation_log.module.update = 项目构建-研究产品列表-修改
operation_log.module.release = 项目构建-研究产品列表-放行
operation_log.module.barcode_add = Project Design-Treatment Design-Barcode List-Generate Barcode
operation_log.module.work_task_add = Project Design-Treatment Design-Scan for Warehousing
operation_log.module.medicinesList_uploadMedicine = Project Design-Treatment Design-Packlist-Upload
operation_log.module.medicinesList_uploadPacklist = Project Design-Treatment Design-Packlist-Upload Packlist
operation_log.module.medicinesList_delete = Project Design-Treatment Design-Packlist-Mass Delete
operation_log.module.updateBatch = Project Design-Treatment Design-Batch Management
operation_log.module.otherMedicines = Project Design-Treatment Design-Unnumbered IP
operation_log.module.form = Project Design-Randomization Design-Region configuration
operation_log.module.simulate_random = Project Design-Randomization Simulation
operation_log.module.push = Project Design-Statistics-Send
operation_log.module.user = Settings-Users
operation_log.module.project = Project settings
operation_log.module.projectUser_role = Other settings-User Management-Role
operation_log.module.projectUser_site = Other settings-User Management-Site
operation_log.module.projectUser_depot = Other Settings - User Management - Depot
operation_log.module.projectUser = Other settings-User Management
operation_log.module.project_information = Project settings-Basic Information
operation_log.module.project_env = Project settings-Project Environments
operation_log.module.project_function = Project settings-Business Functions
operation_log.module.project_docking = Project settings-External Sync
operation_log.module.project_custom = Project settings-Custom Process
operation_log.module.project_permission = Project settings-Project Permissions
operation_log.module.notifications = Other Settings-Notifications
operation_log.module.configure_export = Other Settings-Download Configuration
operation_log.module.project_basic_information = Project settings-Basic Information
operation_log.module.subjects = Subjects
operation_log.module.project_notice = Project Notification
operation_log.module.project_multi_language = Multilingual
operation_log.module.project_multi_language_translate = Multilingual-
operation_log.module.project_multi_language_batch_upload = Multilingual-
operation_log.add = Add
operation_log.edit = Edit
operation_log.delete = Delete
operation_log.copy = Copy
operation_log.run = Run
operation_log.unbind = Unbind
operation_log.close = Close
operation_log.setting = Setting
operation_log.cancel = Cancel
operation_log.reauthorization = Reauthorization
operation_log.invite_again = Invite Again
operation_log.export = Export
operation_log.project_copy = Project Copy
operation_log.cohort_copy = Copy
operation_log.activate = Activate
operation_log.inactivate = Inactivate
operation_log.barcode.env_name = Environment
operation_log.barcode.random = Coding Rules
operation_log.barcode.manual = Manual Coding Upload
operation_log.barcode.auto = Automatic Coding
operation_log.supply.env_name = Environment
operation_log.supply.name = Supply Plan Name
operation_log.supply.status = Plan Status
operation_log.supply.status_effective = valid
operation_log.supply.status_invalid = invalid
operation_log.supply.site = Plan Applicable Site
operation_log.supply.all_site = All Site
operation_log.supply.desc = Plan Description
operation_log.supply.control = Supply Plan Control
operation_log.supply.alarm = Site Inventory Alert
operation_log.supply.supply = Automatic Supply
operation_log.supply.blindMedicine = Blind IP
operation_log.supply.openMedicine = Open IP
operation_log.supply.forecastStop = Blind IP minimum predicted automatic supply shutdown
operation_log.supply_detail.env_name = Environment
operation_log.supply_detail.name = IP
operation_log.supply_detail.init_supply = Quantity of Initial Shipment
operation_log.supply_detail.warning = Site Inventory Alert Value
operation_log.supply_detail.dispensing_alert = Subject dispensation alert value
operation_log.supply_detail.na = NA
operation_log.supply_detail.buffer = Maximum Buffer
operation_log.supply_detail.second_supply = Re-supply
operation_log.supply_detail.forecast = Minimum prediction
operation_log.supply_detail.un_distribution_date = Do Not Deliver
operation_log.supply_detail.un_provide_date = Do Not Dispense Setting
operation_log.supply_detail.validity_reminder = Expiration Reminder
operation_log.supply_detail.auto_supply_size = automatic re-supply quantity
operation_log.supply_detail.supply_mode_key = Supply Mode
operation_log.supply_detail.forecastPeriod = Forecast Window
operation_log.supply_detail.supply_mode.all_supply = Full-IP Supplement
operation_log.supply_detail.supply_mode.single = Single-product Supplement
operation_log.supply_detail.supply_mode.all_one = Full-IP Supplement Plus One Random IP
operation_log.supply_detail.supply_mode.single_one = Single-product Supplement Plus One Random IP
operation_log.random_design.env_name = Environment
operation_log.region.env_name = Environment
operation_log.random_design.factorLabel = Stratification Factor
operation_log.random_design.sync = Synchronization
operation_log.random_design.inactivate = Synchronization
operation_log.random_design.list = Randomization List Name
operation_log.random_design.type = Randomization Type
operation_log.random_design.block = Block Randomization
operation_log.random_design.min = Minimized Randomization
operation_log.random_design.group_name = Group Name
operation_log.random_design.group_code = Group Code
operation_log.random_design.status = Status
operation_log.random_design.status_effective = valid
operation_log.random_design.status_invalid = Invalid
operation_log.random_design.factor.layer = Set region as stratification factor
operation_log.random_design.factor.number = Field Code
operation_log.random_design.factor.name = System Field
operation_log.random_design.factor.calcType = Calculation Formula
operation_log.random_design.factor.formula = Custom formula
operation_log.random_design.factor.keepDecimal = Keep Decimal Places
operation_log.random_design.factor.roundingMethod = Keep Decimal Places - Rounding Method
operation_log.random_design.factor.roundingMethod_up = Ceiling rounding
operation_log.random_design.factor.roundingMethod_down = Floor rounding
operation_log.random_design.factor.calcType_age = Age
operation_log.random_design.factor.calcType_bmi = BMI
operation_log.random_design.factor.inputLabel = Enter Field Name
operation_log.random_design.factor.inputWeightLabel = Enter Weight Field Name
operation_log.random_design.factor.inputHeightLabel = Enter Height Field Name
operation_log.random_design.factor.label = Field Name
operation_log.random_design.factor.type = Control Type
operation_log.random_design.factor.options = Option
operation_log.random_design.factor.options_label_value = Option Label
operation_log.random_design.factor.folder_oid = Folder OID
operation_log.random_design.factor.form_oid = Form OID
operation_log.random_design.factor.field_oid = Field OID
operation_log.random_design.factor.disable = Set regions as stratification factors is prohbited
operation_log.random_design.factor.country = Set Country as stratification factor
operation_log.random_design.factor.site = Set Site as stratification factor
operation_log.random_design.factor.region = Set Regions as stratification factors
operation_log.random_design.factor.status = Status
operation_log.random_design.factor.precision = Keep Decimal Places
operation_log.random_design.factor.status_effective = valid
operation_log.random_design.factor.status_invalid = Invalid
operation_log.random_design.mapping.random.folder_oid = Randomization ID-Folder OID
operation_log.random_design.mapping.random.form_oid = Randomization ID-Form OID
operation_log.random_design.mapping.random.field_oid = Randomization ID-Field OID
operation_log.random_design.mapping.group.folder_oid = Group-Folder OID
operation_log.random_design.mapping.group.form_oid = Group-Form OID
operation_log.random_design.mapping.group.field_oid = Group-Field OID
operation_log.random_design.mapping.time.folder_oid = Randomization Time-Folder OID
operation_log.random_design.mapping.time.form_oid = Randomization Time-Form OID
operation_log.random_design.mapping.time.field_oid = Randomization Time-Field OID
operation_log.random_list.env_name = Environment
operation_log.random_list.onlyID = Form ID
operation_log.random_list.name = Name
operation_log.random_list.site = Site
operation_log.random_list.initial_number = Initial Number
operation_log.random_list.end_number = Termination Number
operation_log.random_list.block_rule = Block Rules
operation_log.random_list.random_number_rule = Randomization ID Rule
operation_log.random_list.block_rule_order = Order
operation_log.random_list.block_rule_reverse = Disorder
operation_log.random_list.random_number_rule_order = Order
operation_log.random_list.random_number_rule_reverse = Disorder
operation_log.random_list.weight_ratio = Group Configuration(weight ratio)
operation_log.random_list.block_configuration = Block Configuration
operation_log.random_list.factor_ratio = Stratification Factor(weight ratio)
operation_log.random_list.number_length = Number Length
operation_log.random_list.seed = Randomization Seed
operation_log.random_list.prefix = Number Prefix
operation_log.random_list.size = Acceptable Block Size(Use ‘,’ to separate multiple blocks)
operation_log.random_list.status = Status
operation_log.random_list.disable = Disable
operation_log.random_list.enable = Enable
operation_log.random_list.invalid = Invalidate
operation_log.random_list.isRandom = Site cannot enroll subjects without assigned randomization IDs.
operation_log.random_list.isCountryRandom = Site cannot enroll subjects if random IDs are not assigned to  countries
operation_log.random_list.isRegionRandom = Site cannot enroll subjects if the randomization ID has not been assigned to the region.
operation_log.random_list.set_site = Assign Block to Site
operation_log.random_list.set_region = Assign Block to Region
operation_log.random_list.set_country = Assign Block to Country
operation_log.random_list.set_factor = Assign Block to Stratification Factor
operation_log.random_list.clean_factor = Clear Other Stratification factors
operation_log.random_list.set_count = Set Quantity
operation_log.random_list.file_name = Upload
operation_log.random_list.factor = Stratification Factor
operation_log.random_list.estimateNumber = Estimated Number
operation_log.random_list.warnNumber = Subject Alert Number
operation_log.random_list.block = Block
operation_log.random_list.randomNumber = Randomization Number
operation_log.random_list.randomNumberStatus = Status
operation_log.visitCycle.env_name = Environment
operation_log.visitCycle.type = Visit Offset Type
operation_log.visitCycle.baseCohort = baseline基准
operation_log.visitCycle.sort = Sort
operation_log.visitCycle.number = Visit Number
operation_log.visitCycle.name = Visit Name
operation_log.visitCycle.random = Randomize or Not
operation_log.visitCycle.dispensing = Dispense or Not
operation_log.visitCycle.replace = Allowed To Subject Replace
operation_log.visitCycle.doseAdjustment = Dose Adjustment
operation_log.visitCycle.startDays = Start Day
operation_log.visitCycle.endDays = End Day
operation_log.visitCycle.folder_oid = Folder OID
operation_log.visitCycle.form_oid = Form OID
operation_log.visitCycle.dispensing_ip_oid = Dispensation OID
operation_log.visitCycle.dispensing_time_oid = Dispensation time OID
operation_log.visitCycle.interval = Interval Duration
operation_log.visitCycle.period = Window
operation_log.visitCycle.group = Group
operation_log.visitCycle.baseline = Baseline
operation_log.visitCycle.lastdate = Lastdate
operation_log.visitCycle.sop = Visit Process
operation_log.visitCycle.version = Visit Cycle Version Number
operation_log.visitCycle.push = Preview and release
operation_log.visitCycle.label = Label
operation_log.visitCycle.open_setting = Open Configuration
operation_log.visitCycle.formula = Formula
operation_log.visitCycle.DTPMode = DTP mode
operation_log.visitCycle.dtp = DTP or Not
operation_log.visitCycle.send-type-0 = Site(Site Inventory)
operation_log.visitCycle.send-type-1 = Site(Direct-to-Patient Shipment)
operation_log.visitCycle.send-type-2 = Deport(Direct-to-Patient Shipment)
operation_log.visitSetting.env_name = Environment
operation_log.visitSetting.unscheduled_visit = Unscheduled Visit
operation_log.visitSetting.name_zh = Chinese Name
operation_log.visitSetting.name_en = English Name
operation_log.drug_configure.env_name = Environment
operation_log.drug_configure.onlyID = Form ID
operation_log.drug_configure.group = Group
operation_log.drug_configure.preGroup = 主组别
operation_log.drug_configure.subGroup = Sub Group
operation_log.drug_configure.drugValue = IP Name/Dispensation Quantity/IP Specification
operation_log.drug_configure.open = Open Configuration
operation_log.drug_configure.formula = According to the calculation formula
operation_log.drug_configure.spec = Spec
operation_log.drug_configure.calculationType = Formula
operation_log.drug_configure.age = Age
operation_log.drug_configure.weight = Weight
operation_log.drug_configure.bsa = Simple body surface area BSA
operation_log.drug_configure.otherBsa = Other body surface area BSA
operation_log.drug_configure.visitCycles = Visit Name
operation_log.drug_configure.label = (Combined) Dispensation label
operation_log.drug_configure.drugLabel = Dispensation label
operation_log.drug_configure.room = Room Number
operation_log.drug_configure.isDispense = Not Dispense Setting
operation_log.drug_configure.notDispenseConfig = IP Name-Do Not Dispense Setting
operation_log.drug_configure.isOpenPackage = Shipped by Package
operation_log.drug_configure.isOpenApplication = Site Shipment Application
operation_log.drug_configure.supplyRatio = Supply Ratio
operation_log.drug_configure.orderApplictionConfig = IP Name-Ratio
operation_log.drug_configure.packageConfig = Mixed Packages
operation_log.drug_configure.packageConfigNew = Mixed Packages
operation_log.drug_configure.drugNameSpec = IP Name/IP Specification
operation_log.drug_configure.visitDrugNameDispeningNumber = Visit Name/IP Name/Dispensation Quantity
operation_log.drug_configure.weightDispeningNumber = Weight Range/Dispensation Quantity
operation_log.drug_configure.ageDispeningNumber = Age Range/Dispensation Quantity
operation_log.drug_configure.dispensingNumber = Dispensation Quantity
operation_log.drug_configure.specifications = Unit Capacity
operation_log.drug_configure.standard = Unit Calculation Standard
operation_log.drug_configure.comparisonSwitch = Weight Comparison Calculation
operation_log.drug_configure.comparisonType = Comparison Conditions
operation_log.drug_configure.comparisonRatio = The change is
operation_log.drug_configure.currentComparisonType = Is used for this calculatio
operation_log.drug_configure.otherCheck = Unnumbered IP
operation_log.drug_configure.openCheck = Open IP
operation_log.drug_configure.keepDecimal = Keep Decimal Places
operation_log.drug_configure.precision = Number of decimal places
operation_log.drug_configure.automatic_recode = Automatic Assignment
operation_log.drug_configure.automatic_recode_spec = Calculation Unit
operation_log.drug_configure.check = Check
operation_log.drug_configure.uncheck = Unchecked
operation_log.drug_configure.calculationOpen = Open
operation_log.drug_configure.calculationUnOpen = Unopened
operation_log.drug_configure.weightCalculation = The calculated weight at the last visit
operation_log.drug_configure.weightActual = The actual weight at the last visit
operation_log.drug_configure.weightRandom = The weight at the random visit
operation_log.drug_configure.customerCalculation = Custom formula
operation_log.drug_configure_setting.env_name = Environment
operation_log.drug_configure_setting.dtp_ipType = IP/Dispensation Method
operation_log.drug_configure_setting.dtp_ipType_site = Site(Site Inventory)
operation_log.drug_configure_setting.dtp_ipType_siteSubject = Site(Direct-to-Patient Shipment)
operation_log.drug_configure_setting.dtp_ipType_depotSubject = Depot(Direct-to-Patient Shipment)
operation_log.drug_configure_setting.doseAdjustment = Dose Adjustment
operation_log.drug_configure_setting.selectType = Dose Selection
operation_log.drug_configure_setting.doseForm = Dose Form
operation_log.drug_configure_setting.isFirstInitial = Initial dose enabled at the first visit
operation_log.drug_configure_setting.isDoseReduction = Allows the subject to reduce the dose a certain number of times
operation_log.drug_configure_setting.frequency = Frequency
operation_log.drug_configure_setting.doseLevel = Dose Level
operation_log.drug_configure_setting.doseLevelID = ID
operation_log.drug_configure_setting.doseLevelName = Name
operation_log.drug_configure_setting.doseLevelGroup = Group
operation_log.drug_configure_setting.doseLevelDoseDistribution = Dispensation Dose
operation_log.drug_configure_setting.doseLevelInitialDose = Initial Dose
operation_log.drug_configure_setting.visitJudgment = Visit Judgment
operation_log.drug_configure_setting.visitJudgmentID = ID
operation_log.drug_configure_setting.visitJudgmentName = Name/Value
operation_log.drug_configure_setting.visitJudgmentGroup = Group
operation_log.drug_configure_setting.visitJudgmentDoseDistribution = Dispensation Dose
operation_log.drug_configure_setting.visitJudgmentVisitInheritance = Subsequent visits inherit
operation_log.drug_configure_setting.visitJudgmentVisitInheritanceCount = Number of Subsequent visits inherit
operation_log.drug_configure_setting.InheritanceGroup = Group of Subsequent visits inherit
operation_log.drug_configure_setting.InheritanceRule = Rule of Subsequent visits inherit
operation_log.drug_configure_setting.groupRuleMain = Main Visit
operation_log.drug_configure_setting.groupRuleStop = Stop Dispensation
operation_log.drug_examine.nameDateNumberCount = 研究产品名称/有效期/批次号/数量
operation_log.drug_examine.examineConfig = 审核确认
operation_log.drug_examine.examineNotes = 备注
operation_log.drug_update.nameDateNumberCount = 研究产品名称/有效期/批次号/数量
operation_log.addBarcode.env_name = Environment
operation_log.addBarcode.medicineName = IP Name
operation_log.addBarcode.medicineSpec = IP Specification
operation_log.addBarcode.storehouse = Depot
operation_log.addBarcode.expireDate = Expiration
operation_log.addBarcode.batchNumber = Batch Number
operation_log.addBarcode.count = Barcode Quantity
operation_log.addBarcode.prefix = Short Code prefix
operation_log.addBarcode.barcodeRule = 研究产品条形码规则
operation_log.addBarcode.isPackageBarcode = 包装条形码
operation_log.addBarcode.packageRule = 包装条形码规则
operation_log.addBarcode.rule_order = Order
operation_log.addBarcode.rule_reverse = Disorder
operation_log.addBarcode.openPackageBarcode = Open 
operation_log.addBarcode.closePackageBarcode = Close
operation_log.uploadMedicines.env_name = Environment
operation_log.uploadMedicines.onlyID = Form ID
operation_log.uploadMedicines.medicineName = IP Name
operation_log.uploadMedicines.medicineSpec = IP Specification
operation_log.uploadMedicines.storehouse = Depot
operation_log.uploadMedicines.expireDate = Expiration
operation_log.uploadMedicines.batchNumber = Batch Number
operation_log.uploadMedicines.count = Quantity
operation_log.uploadMedicines.singleCount = 单品数量
operation_log.uploadMedicines.packageCount = 包装数量
operation_log.uploadMedicines.deleteMedicines = Mass Delete IP
operation_log.uploadMedicines.fileName = Upload File Name
operation_log.updateBatch.batch = Batch Number
operation_log.updateBatch.expirationDate = Expiration
operation_log.updateBatch.status = the status of IP
operation_log.updateBatch.name = IP Name
operation_log.updateBatch.updateCount = Update Count
operation_log.updateBatch.updateBatch = Update Batch Number
operation_log.updateBatch.updateExpirationDate = Update Expiration
operation_log.updateBatch.toBeWarehoused = To be Warehoused
operation_log.updateBatch.available = Available
operation_log.updateBatch.delivered = Confirmed
operation_log.updateBatch.transit = In Delivery
operation_log.updateBatch.quarantine = Quarantined
operation_log.updateBatch.used = Used
operation_log.updateBatch.lose = Lost/Wasted
operation_log.updateBatch.expired = Expired
operation_log.updateBatch.InOrder = To be Confirmed
operation_log.updateBatch.stockPending = To be warehoused
operation_log.updateBatch.apply = Applied
operation_log.updateBatch.frozen = Freeze
operation_log.updateBatch.approval = To be approved
operation_log.updateBatch.lock = Locked
operation_log.updateBatch.depot = Depots
operation_log.updateBatch.group = Group
operation_log.updateBatch.warn = Subject Alert
operation_log.updateBatch.capacity = Subject Limit
operation_log.updateBatch.batchNo = Batch Number
operation_log.workTask.label = Scan the code and enter the depot task
operation_log.workTask.add = send
operation_log.form.env_name = Environment
operation_log.form.name = Field Name
operation_log.form.editable = Editable or Not
operation_log.form.required = Required
operation_log.form.type = Control Type
operation_log.form.format = format type
operation_log.form.options = Option
operation_log.form.input = Input Box
operation_log.form.inputNumber = Numeric Input Box
operation_log.form.textArea = Multiline Text Box
operation_log.form.select = Drop-down Box
operation_log.form.checkbox = Checkbox
operation_log.form.radio = Radio Box
operation_log.form.switch = Switch
operation_log.form.datePicker = Date Selection Box
operation_log.form.timePicker = TimePickerDialog
operation_log.form.length = Length
operation_log.form.max = Max
operation_log.form.min = Minimum value
operation_log.form.variableFormat = Variable Format
operation_log.form.status = Status
operation_log.form.status_effective = valid
operation_log.form.status_invalid = Invalid
operation_log.form.applicationType = Application Type
operation_log.form.applicationTypeRegister = Subject Registration
operation_log.form.applicationTypeFormula = Custom formula
operation_log.form.applicationTypeDoseAdjustment = Dose Adjustment
operation_log.form.applicationTypeFactorCalc = Stratification Calculation
operation_log.form.variable = Variable ID
operation_log.form.currentTime = Current Time
operation_log.simulateRandom.env_name = Environment
operation_log.simulateRandom.onlyID = Form ID
operation_log.simulateRandom.name = Name
operation_log.simulateRandom.randomList = Randomization List
operation_log.simulateRandom.siteQuantity = Site Quantity
operation_log.simulateRandom.countryQuantity = Country Quantity
operation_log.simulateRandom.regionQuantity = Region Quantity
operation_log.simulateRandom.RunQuantity = Number of Runs
operation_log.simulateRandom.SubjectQuantity = Subject Quantity
operation_log.simulateRandom.FactorRatio = Stratification Case Numbers
operation_log.simulateRandom.run = Randomization Simulation:Run
operation_log.project_storehouse.env_name = Environment
operation_log.project_storehouse.onlyID = Form ID
operation_log.project_storehouse.name = Depots
operation_log.project_storehouse.country = Country and Region
operation_log.project_storehouse.contacts = Contacts
operation_log.project_storehouse.phone = TEL
operation_log.project_storehouse.email = Email
operation_log.project_storehouse.address = Address
operation_log.project_storehouse.connected = Sync or Not
operation_log.project_storehouse.supplier = Logistics Vendor
operation_log.project_storehouse.notIncluded = Shipment does not include quarantined IP
operation_log.project_storehouse.research_product_name_validity_reminder = IP Name(Expiration Reminder)
operation_log.project_storehouse.medicine_name = IP Name
operation_log.project_storehouse.medicine_alert = Alert Value
operation_log.project_storehouse.medicine_info = IP Name/Alert Value/Expiration Date Reminder
operation_log.supplierType.shengsheng = SHENGSHENG LOGISTICS
operation_log.supplierType.catalent = catalent
operation_log.supplierType.baicheng = Bioquick
operation_log.supplierType.eDRUG = eDRUG
operation_log.projectSiteStatus.valid = Valid
operation_log.projectSiteStatus.invalid = Invalid
operation_log.projectSiteActive.open = Open
operation_log.projectSiteActive.close = Close
operation_log.project_site.env_name = Environment
operation_log.project_site.number = Site Number
operation_log.project_site.name = Site Standard Name
operation_log.project_site.shortName = Site abbreviation
operation_log.project_site.country = Country/Region
operation_log.project_site.region = Region
operation_log.project_site.status = Status
operation_log.project_site.active = Automatic Re-supply
operation_log.project_site.supplyPlan = Supply Plan
operation_log.project_site.storehouse = Depot Name
operation_log.project_site.contacts = Contacts
operation_log.project_site.phone = Contact Information
operation_log.project_site.email = Email
operation_log.project_site.address = Address
operation_log.project_site.order_trail = Initial Automatic Shipment: ON, Shipment Number
operation_log.attribute.env_name = Environment
operation_log.attribute.random = Randomize
operation_log.attribute.random_true = Randomize
operation_log.attribute.random_false = non-randomized
operation_log.attribute.isRandomNumber = Display Randomization ID
operation_log.attribute.isRandomNumber_true = yes
operation_log.attribute.isRandomNumber_false = no
operation_log.attribute.isRandomSequenceNumber = Random Sequence Number Display
operation_log.attribute.isRandomSequenceNumber_true = yes
operation_log.attribute.isRandomSequenceNumber_false = no
operation_log.attribute.randomSequenceNumberPrefix = Random Sequence Number Prefix
operation_log.attribute.randomSequenceNumberDigit = Random Sequence Number Digits
operation_log.attribute.randomSequenceNumberStart = Random Sequence Number Start Number
operation_log.attribute.dispensing = Treatment Design
operation_log.attribute.dispensing_true = Dispense
operation_log.attribute.dispensing_false = Do not dispense
operation_log.attribute.dtpRule = DTP Rules
operation_log.attribute.dtpRule_ip = IP
operation_log.attribute.dtpRule_visitFlow = Visit Flow
operation_log.attribute.dtpRule_notApplicable = Not Applicable
operation_log.attribute.randomControl = Randomization Control
operation_log.attribute.randomControlRule = Randomization Control Rule
operation_log.attribute.randomControl1 = All groups, can randomize after with sufficient inventory
operation_log.attribute.randomControl2 = Allocated groups, can randomize after with sufficient in
operation_log.attribute.randomControl3 = Force random to available inventory group
operation_log.attribute.randomControl3_info = How much groups need to be supplied at least
operation_log.attribute.allowRegisterGroup = Actually used IP group
operation_log.attribute.blind = Blind Design
operation_log.attribute.blind_true = Blind
operation_log.attribute.blind_false = Open
operation_log.attribute.minimize_calc = Minimized Randomization Calculation
operation_log.attribute.minimize_calc_factor = Randomization Stratification
operation_log.attribute.minimize_calc_actual = Actual Stratification
operation_log.attribute.notApplicable = Not Applicable
operation_log.attribute.subject_number_rule = Subject number input rule
operation_log.attribute.subject_number_rule1 = Customize
operation_log.attribute.subject_number_rule2 = Auto-incrementing and unique within the project
operation_log.attribute.subject_number_rule3 = Auto-incrementing and unique within the site
operation_log.attribute.screen = Subject Screening Process
operation_log.attribute.screen_true = Open
operation_log.attribute.screen_false = Close
operation_log.attribute.prefix = Subject Prefix
operation_log.attribute.prefix_number = Subject No. Prefix
operation_log.attribute.prefix_true = Have
operation_log.attribute.prefix_false = Not Found
operation_log.attribute.sitePrefix = Use Site Number as Prefix or Not
operation_log.attribute.prefixConnector = Prefix Connector
operation_log.attribute.otherPrefix = Other Prefixes of Subject ID
operation_log.attribute.otherPrefixText = Prefix Text
operation_log.attribute.subjectReplaceText = Replacement text for Subject ID
operation_log.attribute.subjectReplaceTextEn = Replacement text for Subject ID (English)
operation_log.attribute.accuracy = Exact value for Subject ID
operation_log.attribute.accuracy_le = Less than or Equal to
operation_log.attribute.accuracy_eq = Equal to
operation_log.attribute.digit = Subject No. Digit
operation_log.attribute.isFreeze = Quarantined-IP Counting Rule
operation_log.attribute.edcDrugConfigLabel = EDC Docking Treatment Design Label
operation_log.attribute.segmentType = Calculation Rules
operation_log.attribute.serialNumber = Sequence Number
operation_log.attribute.medicineNumber = IP Number
operation_log.attribute.segment = Segmented Dispensation
operation_log.attribute.segmentLength = Segmented Dispensation Length
operation_log.attribute.unblindingReason = Unblinding Reason
operation_log.attribute.unblindingAllowTrue = Allowed to remark
operation_log.attribute.unblindingAllowFalse = Not allowed to remark
operation_log.attribute.allowReplace = Subject Replacement
operation_log.attribute.allowReplaceOpen = Open
operation_log.attribute.allowReplaceUnOpen = Close
operation_log.attribute.replaceRule = Replacing randomization ID
operation_log.attribute.ReplaceRuleNumber = 替换受试者随机号规则
operation_log.attribute.blindingRestrictions = Stop Unblinded Subjects
operation_log.attribute.pvBlindingRestrictions = Include PV unblinded subjects
operation_log.attribute.IPInheritance = IP Inheritance
operation_log.attribute.RemainingVisit = Remaining Visit Cycles
operation_log.push.registered = Register
operation_log.push.update = Modify
operation_log.push.random = Randomize
operation_log.push.dispense = Dispense
operation_log.push.out_visit_dispensing = Unscheduled Dispensation
operation_log.push.replace = Replace IP
operation_log.push.reissue = Re-dispensation
operation_log.push.cancel = Withdraw IP
operation_log.push.retrieval = Retrieve IP
operation_log.push.realDispensing = Actually Dispensed IP
operation_log.push.subjectReplace = Subject Replacement
operation_log.push.unknown = Unknown
operation_log.projectUser.emailLanguage = Email Language
operation_log.projectUser.email = Email
operation_log.projectUser.roles = Role
operation_log.projectUser.addRoles = Assign roles
operation_log.projectUser.cancelRoles = Cancel Roles
operation_log.projectUser.unblindingCode = Generate unblinding codes
operation_log.projectUser.sites = Site
operation_log.projectUser.addSites = Assign Site
operation_log.projectUser.cancelSites = Distribute site
operation_log.projectUser.depots = Depot
operation_log.projectUser.addDepots = Assign Depots
operation_log.projectUser.cancelDepots = Cancel Depots
operation_log.projectUser.App = APP Account
operation_log.projectUser.status = Status
operation_log.projectUser.status_effective = Valid
operation_log.projectUser.status_invalid = Invalid
operation_log.project_notice.envName = Environments
operation_log.project_notice.notice_rule = Notification Rules
operation_log.project_notice.notice_targets = Notification targets
operation_log.project.sponsor = Sponsor
operation_log.project.name = Project Name
operation_log.project.startTime = Project Cycle(Start Date)
operation_log.project.endTime = Project Cycle(end Date)
operation_log.project.plannedCases = Planned Enrollment Number
operation_log.project.phone = Contact Information
operation_log.project.descriptions = Remark
operation_log.project.timeZone = TimeZone
operation_log.project.status = Status
operation_log.project.progress = In Progress
operation_log.project.finish = Completed
operation_log.project.close = Closed
operation_log.project.pause = Paused
operation_log.project.terminate = Terminate
operation_log.project.orderCheck = Run Shipping Algorithm
operation_log.project.timing = Fixed Time(including manual run)
operation_log.project.realTime = Real Time
operation_log.project.notApplicable = Not Applicable
operation_log.project.orderConfirmation = Return Shipments Confirmation
operation_log.project.deIsolationApproval = Lifting Quarantine Approval
operation_log.project.administrators = Administrator
operation_log.project.connectEdc = Sync EDC
operation_log.project.pushMode = Data Push Mode
operation_log.project.real = Real-time request by EDC
operation_log.project.active = Active push from IRT
operation_log.project.pushRules = Push Rules
operation_log.project.subjectNumber = Subject ID
operation_log.project.subjectUID = Subject UID
operation_log.project.pushScenario = Push Scenarios
operation_log.project.registerPush = Register
operation_log.project.updateRandomFrontPush = Subject modify(Before Randomization)
operation_log.project.updateRandomAfterPush = Subject modify(After Randomization)
operation_log.project.randomPush = Randomize
operation_log.project.randomBlockPush = Stratification checks inconsistency, and randomization blocking is performed
operation_log.project.formRandomBlockPush = Form checks inconsistency, and randomization blocking is performed
operation_log.project.cohortRandomBlockPush = Cohort Name checks inconsistency, and randomization blocking is performed
operation_log.project.stageRandomBlockPush = Stage Name checks inconsistency, and randomization blocking is performed
operation_log.project.dispensingPush = Dispense
operation_log.project.screenPush = Screening
operation_log.project.synchronizationMode = Synchronization Mode
operation_log.project.edcUrl = URL
operation_log.project.edcSupplier = EDC Supplier
operation_log.project.edcMappingRules = EDC Mapping Rules
operation_log.project.folderOid = Folder OID
operation_log.project.formOid = Form OID
operation_log.project.fieldOid = Field OID
operation_log.project.ipNumberOid = IP Number OID
operation_log.project.dispenseTimeOid = Dispensation Time OID
operation_log.project.randomizationCode = Randomization Number
operation_log.project.randomizationTime = Randomization Time
operation_log.project.group = group
operation_log.project.factor = factor
operation_log.project.cohor = Cohort/Re-randomization
operation_log.project.stepBy = Data synchronization when subject screening
operation_log.project.timeFull = Data synchronization when subjects randomizing
operation_log.project.connectLearning = Sync eLearning
operation_log.project.needLearning = Must complete course
operation_log.project.needLearningEnv = Environment
operation_log.project.unblindingControl = Unblinding control
operation_log.project.unblindingSms = SMS
operation_log.project.unblindingProcess = Process operation
operation_log.project.unblindingCode = Unblinding code
operation_log.project.pvUnblinding = Unblinding(pv)
operation_log.project.pvUnblindingSms = SMS(pv)
operation_log.project.pvUnblindingProcess = Process operation(pv)
operation_log.project.orderApprovalControl = Site Shipment Approval Control
operation_log.project.envName = Environment Name
operation_log.project.envCapacity = Capping
operation_log.project.alertThresholds = Status/Capping/Alert Threshold
operation_log.project.envReminderThresholds = Alert Threshold
operation_log.project.newEnvName = New Environment
operation_log.project.lockStatus = Status
operation_log.project.unlock = Unlock
operation_log.project.locked = Lock
operation_log.project.roleName = Role Name
operation_log.project.scope = Scope
operation_log.project.roleStatus = Status
operation_log.project.roleDescription = Description
operation_log.project.cancelRolePermissionSelect = Cancel Tick
operation_log.project.addRolePermissionSelect = Tick
operation_log.project.cohort = Cohort Name
operation_log.project.stage = Stage Name
operation_log.project.complete = Completed
operation_log.project.draft = Draft
operation_log.project.enrollment = Enrolling
operation_log.project.stop = Stopped
operation_log.project.enrollmentFull = Enrollment Full
operation_log.project.capacity = Capping
operation_log.project.lastStage = Previous Stage
operation_log.project.cohortStatus = Status
operation_log.project.properties = Project Properties
operation_log.project.type = Project Type
operation_log.project.region.name = Name
operation_log.project_multi_language.projectName = Project
operation_log.project_multi_language.language = Language
operation_log.project_multi_language.status = Enabled Status
operation_log.project_multi_language.sharedSystemLibrary = Shared System Library
operation_log.project_multi_language_translate.projectName = Project
operation_log.project_multi_language_translate.languageLibrary = Language Library
operation_log.project_multi_language_translate.pagePath = Page Path
operation_log.project_multi_language_translate.envName = Environment
operation_log.project_multi_language_translate.cohortName = Cohort
operation_log.project_multi_language_translate.stageName = Stage
operation_log.project_multi_language_translate.type = Type
operation_log.project_multi_language_translate.key = Key
operation_log.project_multi_language_translate.name = Name
operation_log.project_multi_language_translate.label = label
operation_log.project_multi_language_batch_upload.projectName = Project
operation_log.project_multi_language_batch_upload.filename = Bulk Import Filename

[edc]
is_screen = Screening successful or not
screen_time = Screening Date
icf_time = ICF Signed Date
edc_push_subject_number = Subject Number
edc_push_randomization_number = Randomization number
edc_push_group = Group
edc_push_randomization_time = Randomization Time
edc_push_visit_number = Visit Number
edc_push_dispense = Dispense
edc_push_dispense_time = Dispensing Time
edc_push_drug = drug
edc_push_drug_level = Dose Level
edc_push_drug_label = Label
edc_push_cohort = Cohort/Stage
edc_push_edc_return = Return from EDC

[edc_push_log]
edc_push_error_code_200 = Succeeded
edc_push_error_code_201 = Timestamp, signature and base parameters are wrong, please contact IRT engineer to confirm.
edc_push_error_code_202 = Project number is wrong, please contact IRT configuration administrator to confirm.
edc_push_error_code_203 = Environment number is wrong, please contact IRT configuration administrator to confirm.
edc_push_error_code_204 = Site number is error, please contact IRT configuration administrator to confirm.
edc_push_error_code_205 = Subject duplicate, please contact EDC configuration administrator for manual processing.
edc_push_error_code_206 = Subject number prefix rule is inconsistent, please contact EDC and IRT configuration administrator to confirm.
edc_push_error_code_207 = Modification failed, the target subject number already exists, please reconfirm.
edc_push_error_code_208 = Subject creation is in progress, please operate later.
edc_push_error_code_209 = Creation failed, EDC site version not pushed, please contact EDC configuration administrator to confirm.
edc_push_error_code_210 = Creation failed, abbreviations are missing, please reconfirm.
edc_push_error_code_211 = Failed to create, please contact EDC engineer for confirmation.
edc_push_error_code_212 = EDC data saving resolution error, please contact EDC engineer to confirm.
edc_push_error_code_213 = EDC data request parsing error, please contact EDC engineer to confirm.
edc_push_error_code_299 = Other undefined errors, please contact EDC engineer to confirm.

[project_dynamics]
project_dynamics_scene_personnel = Personnel
project_dynamics_scene_order = Order
project_dynamics_scene_unblinding = Unblinding
project_dynamics_scene_forecast = 库存
project_dynamics_scene_visit = 访视
project_dynamics_type_enter_site = Enter the site
project_dynamics_type_bind_storehouse = Assign depot
project_dynamics_type_role_assignment = Role Assignment
project_dynamics_type_overtime = Overtime
project_dynamics_type_emergency_unblinding = Emergency unblinding
project_dynamics_type_emergency_unblinding_pv = Pv unblinding
project_dynamics_type_alert_storehouse = Depot alert
project_dynamics_type_forecast = Inventory Prediction Time
project_dynamics_type_visit = 访视超窗
project_dynamics_content_enter_site = 【{{.siteName}}】<a>{{.email}}</a>Entered the site
project_dynamics_content_bind_storehouse = 【{{.storehouseName}}】<a>{{.email}}</a>Storehouse bound
project_dynamics_content_role_assignment = 【Role Assignment】<a>{{.email}}</a>Assigned{{.roles}}Role
project_dynamics_content_overtime = 【Shipment timeout】<a>{{.orderNumber}}</a>Time out
project_dynamics_content_emergency_unblinding = 【{{.siteName}}】<a>{{.subjectName}}</a>Unblinded
project_dynamics_content_emergency_unblinding_emergency = 【{{.siteName}}】<a>{{.subjectName}}</a>Unblinded(Emergency)
project_dynamics_content_emergency_unblinding_pv = 【{{.siteName}}】<a>{{.subjectName}}</a>Unblinded(PV)
project_dynamics_content_forecast = 【<a>{{.siteName}}</a>】inventory available time forecast reminder.
project_dynamics_content_alert_storehouse = 【<a>{{.storehouseName}}</a>】The IP in the storehouse has reached the alert value
project_dynamics_content_visit = 【{{.siteName}}】【{{.subject}}】{{.visit}} overdue.

[app]
app_scan_package_notification_title = Package scan Notification
app_scan_package_notification_content = Package scan
app_scan_notification_title = Scan to Enter Depot Notification
app_scan_notification_content = Scan to Enter Depot
app_shipment_confirmed_notification_title = Shipment to be Confirmed Notification
app_shipment_confirmed_notification_content = Shipment to be confirmed
app_shipment_received_notification_title = Shipment to be Received Notification
app_shipment_received_notification_content = Shipment to be Received
app_recovery_shipment_confirmed_notification_title = Return Shipment to be Confirmed Notification
app_recovery_shipment_confirmed_notification_content = Return Shipment to be confirmed
app_recovery_shipment_received_notification_title = Return Shipment to be Received Notification
app_recovery_shipment_received_notification_content = Return Shipment to be Received
app_recovery_shipment_delivered_notification_title = Return Shipment to be Delivered Notification
app_recovery_shipment_delivered_notification_content = Return Shipment to be Delivered
app_dispensation_confirmed_notification_title = IP Dispensation Confirmation Notification
app_dispensation_confirmed_notification_content = IP Dispensation Confirmation
app_re_dispensation_confirmed_notification_title = Re-dispensation Confirmation Notification
app_re_dispensation_confirmed_notification_content = Re-dispensation Confirmation
app_shipment_delivered_notification_title = Shipment to be Delivered Notification
app_shipment_delivered_notification_content = Shipment to be Delivered
app_shipment_delivered_application_title = Site Shipment Application Notification
app_shipment_delivered_application_content = Site Shipment Application
app_unblinding_urgent_notification_title = Unblinding (Urgent) Approval Notification
app_unblinding_urgent_notification_content = Unblinding (Urgent) Approval
app_unschedualed_dispensation_confirmation_notification_title = Unschedualed Dispensation Confirmation Notication
app_unschedualed_dispensation_confirmation_notification_content = Unschedualed Dispensation Confirmation
app_unblinding_pv_notification_title = Unblinding(PV) Approval Notification
app_unblinding_pv_notification_content = Unblinding(PV)
app_ip_dispensation_notification_title = IP Dispensation Notification
app_ip_dispensation_notification_content = IP Dispensation
app_site_alert_notification_title = Site Alert Notification
app_site_alert_notification_content = IP has reached alert value
app_depot_alert_notification_title = Depot Alert Notification
app_depot_alert_notification_content = IP has reached alert value
app_shipment_timeout_notification_title = Shipment Timeout Notification
app_shipment_timeout_notification_content_1 = Shipment
app_shipment_timeout_notification_content_2 = is timeout by
app_shipment_timeout_notification_content_day = day
app_shipment_timeout_notification_content_days = days
app_visit_reminder_title = Visit Reminder
app_visit_reminder_content_a = {{.days}} days later you have a visit task, please remember to arrange it in advance!
app_visit_reminder_content_b = You have a visit task tomorrow, please remember to arrange it!
app_visit_reminder_content_c = You have a visit task today, please remember to go for a check-up!
app_visit_reminder_content_d = Did you forget to arrange the visit today? Come in and take a look!

[other]
order_logistics_1 = SF Express
order_logistics_2 = EMS
order_logistics_3 = JD
order_logistics_4 = YTO Express
order_logistics_5 = UDA Express
order_logistics_6 = ZTO
order_logistics_7 = STO Express
order_logistics_8 = J&T Express
order_logistics_9 = Others