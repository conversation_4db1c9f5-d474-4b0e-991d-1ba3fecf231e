package locales

import (
	"clinflash-irt/ini"
	"context"
	"embed"
	"io/fs"

	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
	"gopkg.in/yaml.v3"
)

var (
	//go:embed *
	LocalesFS embed.FS

	bundle *i18n.Bundle
)

// LoadMessageFileFS is like LoadMessageFile but instead of reading from the
// hosts operating system's file system it reads from the fs file system.
func loadMessageFileFS(b *i18n.Bundle, fsys fs.FS, path string) (*i18n.MessageFile, error) {
	buf, err := fs.ReadFile(fsys, path)
	if err != nil {
		panic(err)
	}
	return b.ParseMessageFileBytes(buf, path)
}

func Init() {
	bundle = i18n.NewBundle(language.English)
	bundle.RegisterUnmarshalFunc("yml", yaml.Unmarshal)
	_, err := loadMessageFileFS(bundle, LocalesFS, "en.yml")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "zh.yml")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "ko.yml")
	if err != nil {
		panic(err)
	}
	bundle.RegisterUnmarshalFunc("ini", ini.Unmarshal)
	_, err = loadMessageFileFS(bundle, LocalesFS, "en.ini")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "zh.ini")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "ko.ini")
	if err != nil {
		panic(err)
	}
}

func Lang(c context.Context) string {
	var lang = "en"
	switch ctx := c.(type) {
	case *gin.Context:
		lang = ctx.Request.Header.Get("Accept-Language")
	default:
		if v, ok := ctx.Value("lang").(string); ok {
			lang = v
		}
		// grpc metadata中key对应的value为[]string类型
		if v, ok := ctx.Value("lang").([]string); ok {
			if len(v) > 0 {
				lang = v[0]
			}
		}
	}
	return lang
}

func Tr(c context.Context, key string, data ...interface{}) string {
	return TrWithLang(Lang(c), key, data...)
}

func TrWithLang(lang string, key string, data ...interface{}) string {
	var templateData interface{}
	if len(data) > 0 {
		templateData = data[0]
	}
	localizeConfig := &i18n.LocalizeConfig{MessageID: key, TemplateData: templateData}
	return trWithConfig(lang, localizeConfig, bundle)
}

func TrMessage(c context.Context, key string, content string, data ...interface{}) string {
	return TrWithMessage(Lang(c), &i18n.Message{ID: key, Other: content}, data...)
}

func TrWithMessage(lang string, message *i18n.Message, data ...interface{}) string {
	var templateData interface{}
	if len(data) > 0 {
		templateData = data[0]
	}
	localizeConfig := &i18n.LocalizeConfig{DefaultMessage: message, TemplateData: templateData}
	// 临时bundle
	tempBundle := i18n.NewBundle(language.English)
	tempBundle.AddMessages(language.English, message)
	return trWithConfig(lang, localizeConfig, tempBundle)
}

func trWithConfig(lang string, config *i18n.LocalizeConfig, bundle *i18n.Bundle) string {
	msg := ""
	defer func() string {
		if err := recover(); err != nil {
			return msg
		}
		return msg
	}()
	localizer := i18n.NewLocalizer(bundle, lang)
	msg = localizer.MustLocalize(config)
	return msg
}

type TrData struct {
	Key  string
	Data []interface{}
}

func TrStash(c context.Context, key string, data ...interface{}) TrData {
	return TrData{Key: key, Data: data}
}
