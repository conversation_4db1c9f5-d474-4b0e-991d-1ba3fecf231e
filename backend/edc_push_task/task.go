package edc_push_task

import (
	"clinflash-irt/task"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type TaskType int

const (
	RandomPushType TaskType = iota + 1
	DispensingPushType
)

// 统一任务接口
type Task interface {
	Execute() error
	Type() TaskType
	GetLogData() map[string]interface{}
}

// 基础任务结构
type BaseTask struct {
	LogData map[string]interface{}
}

func (t *BaseTask) GetLogData() map[string]interface{} {
	return t.LogData
}

// 随机推送任务
type RandomPushTask struct {
	BaseTask
	SubjectID        primitive.ObjectID
	SourceType       int
	NowDuration      time.Duration
	ReplaceSubjectId string
	ReplaceSubjectNo string
	OldCohort        string
}

// 全局执行器
var executor task.EdcPushTaskExecutor

// 设置执行器
func SetExecutor(e task.EdcPushTaskExecutor) {
	executor = e
}

func (t *RandomPushTask) Execute() error {
	if executor == nil {
		panic("EDC executor not initialized")
	}
	return executor.SubjectRandomPush(
		t.SubjectID,
		t.SourceType,
		t.NowDuration,
		t.ReplaceSubjectId,
		t.ReplaceSubjectNo,
		t.OldCohort,
	)
}

func (t *RandomPushTask) Type() TaskType {
	return RandomPushType
}

// 发药推送任务
type DispensingPushTask struct {
	BaseTask
	DispensingID primitive.ObjectID
	SourceType   int
	NowDuration  time.Duration
}

func (t *DispensingPushTask) Execute() error {
	if executor == nil {
		panic("EDC executor not initialized")
	}
	return executor.SubjectDispensingPush(
		t.DispensingID,
		t.SourceType,
		t.NowDuration,
	)
}

func (t *DispensingPushTask) Type() TaskType {
	return DispensingPushType
}
