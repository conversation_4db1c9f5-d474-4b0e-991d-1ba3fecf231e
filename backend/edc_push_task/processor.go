package edc_push_task

import (
	"clinflash-irt/tools"
	"github.com/panjf2000/ants/v2"
	"github.com/sirupsen/logrus"
)

var (
	taskQueue = make(chan Task, 10000) // 任务队列
	pool      *ants.Pool               // 协程池
)

// 初始化任务处理器
func InitTaskProcessor() {
	// 创建协程池
	var err error
	pool, err = ants.NewPool(500, ants.WithPanicHandler(panicHandler))
	if err != nil {
		logrus.Errorf("Failed to create goroutine pool: %v", err)
	}

	// 启动任务处理器
	go processTasks()
}

func panicHandler(err interface{}) {
	logrus.Errorf("Goroutine panic: %v", err)
}

func processTasks() {
	for task := range taskQueue {
		// 使用闭包捕获当前任务
		currentTask := task

		// 提交到协程池
		if err := pool.Submit(func() {
			if err := currentTask.Execute(); err != nil {
				// 记录错误日志
				tools.SavePushEdcErrorLogNew(err, currentTask.GetLogData())
			}
		}); err != nil {
			logrus.Errorf("Failed to submit task: %v", err)
			// 直接执行任务（降级）
			if err = currentTask.Execute(); err != nil {
				tools.SavePushEdcErrorLogNew(err, currentTask.GetLogData())
			}
		}
	}
}

// 提交任务到队列
func SubmitTask(task Task) {
	taskQueue <- task
}

// 关闭任务处理器
func CloseAnts() {
	close(taskQueue)
	pool.Release()
}
