package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// ProjectSiteController ..
type ProjectSiteController struct {
	s service.ProjectSiteService
}

// List 中心管理
func (c *ProjectSiteController) List(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.List(ctx, customerID, projectID, envID, roleID, start, limit)
	tools.Response(ctx, err, data)
}

// SiteList 获取项目环境下启用的中心
func (c *ProjectSiteController) SiteList(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	data, err := c.s.SiteList(ctx, customerID, projectID, envID)
	tools.Response(ctx, err, data)

}

// UserSites 中心药房
func (c *ProjectSiteController) UserSites(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	data, err := c.s.UserSites(ctx, customerID, projectID, envID, roleID)
	tools.Response(ctx, err, data)

}

// Add 新增/修改项目中心
func (c *ProjectSiteController) Add(ctx *gin.Context) {
	var data models.ProjectSiteParameter
	var _ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.Add(ctx, data)
	tools.Response(ctx, err, data)

}

// GetSitesAndStorehouses 获取项目中心和仓库
func (c *ProjectSiteController) GetSitesAndStorehouses(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	data, err := c.s.GetSitesAndStorehouses(ctx, customerID, projectID, envID)
	tools.Response(ctx, err, data)

}

// GetUserSitesAndStorehouses 获取项目中心和仓库
func (c *ProjectSiteController) GetUserSitesAndStorehouses(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	data, err := c.s.GetUserSitesAndStorehouses(ctx, customerID, projectID, envID, roleID)
	tools.Response(ctx, err, data)
}

// GetProjectSiteById 根据中心id，获取中心信息
func (c *ProjectSiteController) GetProjectSiteById(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	projectSiteID := ctx.Query("siteId")
	data, err := c.s.GetProjectSiteById(ctx, customerID, projectID, envID, projectSiteID)
	tools.Response(ctx, err, data)
}

// GetUserSites 获取用户关联的中心
func (c *ProjectSiteController) GetUserSites(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	userID := ctx.Query("userId")
	data, err := c.s.GetUserSites(ctx, customerID, envID, userID)
	tools.Response(ctx, err, data)

}

// GetBatchUserSites 批量添加用户获取用户关联的中心
func (c *ProjectSiteController) GetBatchUserSites(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	data, err := c.s.GetBatchUserSites(ctx, customerID, envID)
	tools.Response(ctx, err, data)
}

// GetUserStorehouses 获取用户关联的仓库
func (c *ProjectSiteController) GetUserStorehouses(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	userID := ctx.Query("userId")
	data, err := c.s.GetUserStorehouses(ctx, customerID, envID, userID)
	tools.Response(ctx, err, data)

}

// GetBatchUserStorehouses 批量添加用户获取用户关联的仓库
func (c *ProjectSiteController) GetBatchUserStorehouses(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	data, err := c.s.GetBatchUserStorehouses(ctx, customerID, envID)
	tools.Response(ctx, err, data)
}

// UpdateSupplyPlan 更新中心的供应计划、库房
func (c *ProjectSiteController) UpdateSupplyPlan(ctx *gin.Context) {
	id := ctx.Query("id")
	supplyPlanId := ctx.Query("supplyPlanId")
	envId := ctx.Query("envId")
	cohortId := ctx.Query("cohortId")
	err := c.s.UpdateSupplyPlan(ctx, id, supplyPlanId, envId, cohortId)
	tools.Response(ctx, err)
}

// GetStoreListOption 获取项目仓库
func (c *ProjectSiteController) GetStoreListOption(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	data, err := c.s.GetStoreListOption(ctx, customerID, projectID, envID)
	tools.Response(ctx, err, data)

}

func (c *ProjectSiteController) GetCountry(ctx *gin.Context) {
	data, err := c.s.GetCountry(ctx)
	tools.Response(ctx, err, data)
}

func (c *ProjectSiteController) GetProjectSiteByTz(ctx *gin.Context) {
	tz := ctx.Query("tz")
	timeZone := ctx.Query("timeZone")
	data, err := c.s.GetProjectSiteByTz(ctx, tz, timeZone)
	tools.Response(ctx, err, data)
}

func (c *ProjectSiteController) GetBatchGroupNum(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	siteID := ctx.Query("siteId")
	data, err := c.s.GetBatchGroupNum(ctx, projectID, envID, siteID)
	tools.Response(ctx, err, data)
}
