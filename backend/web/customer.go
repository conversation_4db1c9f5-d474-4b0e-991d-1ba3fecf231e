package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin/binding"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CustomerController struct {
	s service.CustomerService
}

// ListCustomerUsers 获取客户的用户列表
func (c *CustomerController) ListCustomerUsers(ctx *gin.Context) {
	var err error
	sysAdmin, err := strconv.ParseBool(ctx.Query("sysAdmin"))
	keyword := ctx.Query("keyword")
	customerID := ctx.Query("customerId")
	start, err := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, err := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.ListCustomerUsers(ctx, customerID, keyword, start, limit)
	if sysAdmin {
		data, err = c.s.ListSysAdminUsers(ctx, keyword, start, limit)
	}
	tools.Response(ctx, err, data)
}

// ListCustomerUsersExport 导出客户的用户列表
func (c *CustomerController) ListCustomerUsersExport(ctx *gin.Context) {

	sysAdmin, _ := strconv.ParseBool(ctx.Query("sysAdmin"))
	keyword := ctx.Query("keyword")
	customerID := ctx.Query("customerId")
	//start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	//limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))

	err := c.s.UserExport(ctx, sysAdmin, customerID, keyword)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// ListRolesPermissionExport 导出角色权限列表
func (c *CustomerController) ListRolesPermissionExport(ctx *gin.Context) {
	template, _ := strconv.Atoi(ctx.Query("template"))
	keyword := ctx.Query("keyword")

	err := c.s.RoleExport(ctx, template, keyword)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// UpdateUserCustomerAdmin 更新用户的客户admin权限
func (c *CustomerController) UpdateUserCustomerAdmin(ctx *gin.Context) {
	var err error
	customerID := ctx.Query("customerId")
	userID := ctx.Query("id")
	var userCustomer models.UserCustomer
	err = ctx.ShouldBindBodyWith(&userCustomer, binding.JSON)
	err = c.s.UpdateUserCustomerAdmin(ctx, customerID, userID, userCustomer)
	tools.Response(ctx, err)
}

// ListCustomerUserProjects 获取客户下用户参与的项目列表
func (c *CustomerController) ListCustomerUserProjects(ctx *gin.Context) {
	var err error
	customerID := ctx.Query("id")
	start, err := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, err := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.ListCustomerUserProjects(ctx, customerID, start, limit)
	tools.Response(ctx, err, data)
}

// ListCustomerProjects 获取客户下的项目列表
func (c *CustomerController) ListCustomerProjects(ctx *gin.Context) {
	var err error
	customerID := ctx.Query("id")
	start, err := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, err := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.ListCustomerProjects(ctx, customerID, start, limit)
	tools.Response(ctx, err, data)
}

// AddCustomerUser 新增客户的用户
func (c *CustomerController) AddCustomerUser(ctx *gin.Context) {
	var err error
	customerID := ctx.Query("customerId")
	var data models.UserInfo
	err = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err = c.s.AddCustomerUser(ctx, customerID, data)
	tools.ResponseWithMsg(ctx, "user.customer.bind.success", err, data)
}

// AddCustomerBatchUserVerify 批量新增客户的用户验证邮箱
func (c *CustomerController) AddCustomerBatchUserVerify(ctx *gin.Context) {
	var err error
	customerID := ctx.Query("customerId")
	var userInfoList models.UserInfoList
	err = ctx.ShouldBindBodyWith(&userInfoList, binding.JSON)
	data, err := c.s.AddCustomerBatchUserVerify(ctx, customerID, userInfoList)
	tools.Response(ctx, err, data)
}

// AddCustomerBatchUser 批量新增客户的用户
func (c *CustomerController) AddCustomerBatchUser(ctx *gin.Context) {
	var err error
	customerID := ctx.Query("customerId")
	var userInfoList models.UserInfoList
	err = ctx.ShouldBindBodyWith(&userInfoList, binding.JSON)
	data, err := c.s.AddCustomerBatchUser(ctx, customerID, userInfoList)
	tools.Response(ctx, err, data)
}

// SetUsersRoles 批量设置用户角色
// Method:	PATCH
func (c *CustomerController) SetUsersRoles(ctx *gin.Context) {
	var userRole models.UserRole
	_ = ctx.ShouldBindBodyWith(&userRole, binding.JSON)
	err := c.s.SetUsersRoles(ctx, userRole)
	tools.Response(ctx, err)
}

// AddCustomerProject 新增客户的项目
func (c *CustomerController) AddCustomerProject(ctx *gin.Context) {
	var err error
	customerID := ctx.Query("id")
	var info models.ProjectInfo
	err = ctx.ShouldBindBodyWith(&info, binding.JSON)
	err = c.s.AddCustomerProject(ctx, customerID, info)
	tools.Response(ctx, err)
}

// CloseCustomerUser 关闭客户下的用户
func (c *CustomerController) CloseCustomerUser(ctx *gin.Context) {
	err := c.s.CloseCustomerUser(ctx)
	tools.Response(ctx, err)
}

// BatchCloseCustomerUser 批量关闭客户下的用户
func (c *CustomerController) BatchCloseCustomerUser(ctx *gin.Context) {
	err := c.s.BatchCloseCustomerUser(ctx)
	tools.Response(ctx, err)
}

func (c *CustomerController) InviteAgainCustomerUser(ctx *gin.Context) {
	err := c.s.InviteAgainCustomerUser(ctx)
	tools.Response(ctx, err)
}
