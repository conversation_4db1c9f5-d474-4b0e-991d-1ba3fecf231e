package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// RandomizationController struct
type RandomizationController struct {
	s service.RandomizationService
}

// GetForm 查询表单字段列表
// Method:	GET
func (c *RandomizationController) GetForm(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	cohortID := ctx.Query("cohortId")
	data, err := c.s.ListForm(ctx, customerID, envID, cohortID)
	tools.Response(ctx, err, data)
}

// GetRegisterForm 受试者登记时查询表单字段
// Method:	GET
func (c *RandomizationController) GetRegisterForm(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	cohortID := ctx.Query("cohortId")
	siteID := ctx.Query("siteId")
	data, err := c.s.GetRegisterForm(ctx, customerID, projectID, envID, cohortID, siteID)
	tools.Response(ctx, err, data)
}

// GetFormulaForm 受试者查询公式表单字段
// Method:	GET
func (c *RandomizationController) GetFormulaForm(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	cohortID := ctx.Query("cohortId")
	subjectID := ctx.Query("subjectId")
	visitID := ctx.Query("visitId")
	data, err := c.s.GetFormulaForm(ctx, customerID, envID, cohortID, subjectID, visitID)
	tools.Response(ctx, err, data)
}

// DeleteForm 删除表单字段
// Method:	Delete
func (c *RandomizationController) DeleteForm(ctx *gin.Context) {
	id := ctx.Query("id")
	fieldID := ctx.Query("fieldId")
	err := c.s.DeleteForm(ctx, id, fieldID)
	tools.Response(ctx, err)
}

// UpdateForm 更新表单字段
// Method:	PATCH
func (c *RandomizationController) UpdateForm(ctx *gin.Context) {
	id := ctx.Query("id")
	fieldID := ctx.Query("fieldId")
	var field models.Field
	_ = ctx.ShouldBindBodyWith(&field, binding.JSON)
	err := c.s.UpdateForm(ctx, id, fieldID, field)
	tools.Response(ctx, err)
}

// AddForm 新增form
// Method:	POST
func (c *RandomizationController) AddForm(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	var form models.Form
	_ = ctx.ShouldBindBodyWith(&form, binding.JSON)
	err := c.s.AddForm(ctx, customerID, envID, form.CohortID, form.ProjectID, form.Fields[0])
	tools.Response(ctx, err)
}

// GetAttribute ..
func (c *RandomizationController) GetAttribute(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customer")
	envID := ctx.Query("env")
	cohortID := ctx.DefaultQuery("cohort", "")
	data, err := c.s.GetAttribute(ctx, projectID, customerID, envID, cohortID)
	tools.Response(ctx, err, data)
}

// GetRandomizationType ..
func (c *RandomizationController) GetRandomizationType(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customer")
	envID := ctx.Query("env")
	cohortID := ctx.DefaultQuery("cohort", "")
	data, err := c.s.GetRandomizationType(ctx, projectID, customerID, envID, cohortID)
	tools.Response(ctx, err, data)
}

// GetAttributes ..
func (c *RandomizationController) GetAttributes(ctx *gin.Context) {
	envID := ctx.Query("env")
	data, err := c.s.GetAttributes(ctx, envID)
	tools.Response(ctx, err, data)
}

// UpdateAttribute ..
func (c *RandomizationController) UpdateAttribute(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	message, err := c.s.UpdateAttribute(ctx, data)
	tools.Response(ctx, err, message)
}

// UpdateSiteLayered ..
func (c *RandomizationController) UpdateSiteLayered(ctx *gin.Context) {
	err := c.s.UpdateSiteLayered(ctx)
	tools.Response(ctx, err)
}

// GetRandomizationInfo ..
func (c *RandomizationController) GetRandomizationInfo(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customer")
	envID := ctx.Query("env")
	cohortID := ctx.DefaultQuery("cohort", "")
	roleId := ctx.DefaultQuery("roleId", "")
	data, err := c.s.GetRandomizationInfo(ctx, projectID, customerID, envID, cohortID, roleId)
	tools.Response(ctx, err, data)
}

// AddRandomizationInfo ..
func (c *RandomizationController) AddRandomizationInfo(ctx *gin.Context) {
	err := c.s.AddRandomizationInfo(ctx)
	tools.Response(ctx, err)
}

// UpdateRandomizationInfo ..
func (c *RandomizationController) UpdateRandomizationInfo(ctx *gin.Context) {
	err := c.s.UpdateRandomizationInfo(ctx)
	tools.Response(ctx, err)
}

// UploadRandomizationFile ..
func (c *RandomizationController) UploadRandomizationFile(ctx *gin.Context) {
	err := c.s.UploadRandomizationFile(ctx)
	tools.Response(ctx, err)
}

// DeleteRandomGroup ..
func (c *RandomizationController) DeleteRandomGroup(ctx *gin.Context) {
	err := c.s.DeleteRandomGroup(ctx)
	tools.Response(ctx, err)

}

// DeleteRandomFactor ..
func (c *RandomizationController) DeleteRandomFactor(ctx *gin.Context) {
	err := c.s.DeleteRandomFactor(ctx)
	tools.Response(ctx, err)
}

// GenerateRandomNumber 随机号生成
func (c *RandomizationController) GenerateRandomNumber(ctx *gin.Context) {
	var data models.RandomListConfigPt
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.GenerateRandomNumber(ctx, data)
	tools.Response(ctx, err)
}

// RandomNumberInactivate 随机号失活
func (c *RandomizationController) RandomNumberInactivate(ctx *gin.Context) {
	var req models.RandomNumberInactivateReq
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	err := c.s.RandomNumberInactivate(ctx, req)
	tools.Response(ctx, err)
}

// RandomNumberActivateInactivate 随机号激活或失活
func (c *RandomizationController) RandomNumberActivateInactivate(ctx *gin.Context) {
	var req models.RandomNumberActivateInactivateReq
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	err := c.s.RandomNumberActivateInactivate(ctx, req)
	tools.Response(ctx, err)
}

// UpdateRandomList 修改
func (c *RandomizationController) UpdateRandomList(ctx *gin.Context) {
	err := c.s.UpdateRandomList(ctx)
	tools.Response(ctx, err)
}

// GetRandomList ..
func (c *RandomizationController) GetRandomList(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customer")
	envID := ctx.Query("env")
	roleId := ctx.Query("roleId")
	cohortID := ctx.DefaultQuery("cohort", "")
	data, err := c.s.GetRandomList(ctx, projectID, customerID, envID, cohortID, roleId)
	tools.Response(ctx, err, data)
}

// GetRandomListConfigure ..
func (c *RandomizationController) GetRandomListConfigure(ctx *gin.Context) {
	randomListID := ctx.Query("id")
	data, err := c.s.GetRandomListConfigure(ctx, randomListID)
	tools.Response(ctx, err, data)
}

// GetRandomNumberGroup ..
func (c *RandomizationController) GetRandomNumberGroup(ctx *gin.Context) {
	randomListID := ctx.Query("randomListId")

	ret, err := c.s.GetRandomNumberGroup(ctx, randomListID)
	tools.Response(ctx, err, ret)
}

// GetFactorCombination ..
func (c *RandomizationController) GetFactorCombination(ctx *gin.Context) {
	ID := ctx.Query("id")
	data, err := c.s.GetFactorCombination(ctx, ID)
	tools.Response(ctx, err, data)
}

// UpdateRandomListStatus ..
func (c *RandomizationController) UpdateRandomListStatus(ctx *gin.Context) {
	var randomList models.RandomList
	_ = ctx.ShouldBindBodyWith(&randomList, binding.JSON)
	err := c.s.UpdateRandomListStatus(ctx, randomList)
	tools.Response(ctx, err)
}

// UpdateRandomListInvalid ..
func (c *RandomizationController) UpdateRandomListInvalid(ctx *gin.Context) {
	var randomList models.RandomList
	_ = ctx.ShouldBindBodyWith(&randomList, binding.JSON)
	err := c.s.UpdateRandomListInvalid(ctx, randomList)
	tools.Response(ctx, err)
}

// GetBlock 获取可分配的区组
// Method:	GET
func (c *RandomizationController) GetBlock(ctx *gin.Context) {
	data, err := c.s.GetBlock(ctx)
	tools.Response(ctx, err, data)
}

// GetProjectSite 获取当前项目下可用的中心
// Method:	GET
func (c *RandomizationController) GetProjectSite(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	data, err := c.s.GetProjectSite(ctx, customerID, projectID, envID)
	tools.Response(ctx, err, data)
}

// UpdateProjectSite 随机列表中心分配
// Method:	PATCH
func (c *RandomizationController) UpdateProjectSite(ctx *gin.Context) {
	randomListID := ctx.Query("randomListId")
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.UpdateProjectSite(ctx, randomListID, data)
	tools.Response(ctx, err)

}

// GetTemplateFile 随机号上传模板下载
// Method:	GET
func (c *RandomizationController) GetTemplateFile(ctx *gin.Context) {
	err := c.s.GetTemplateFile(ctx)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// UpdateFactorNumber 分层因素人数设置
// Method:	POST
func (c *RandomizationController) UpdateFactorNumber(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	ID := ctx.Query("id")
	err := c.s.UpdateFactorNumber(ctx, data, ID)
	tools.Response(ctx, err)
}

// AddFactorNumber 添加组合分层因素人数设置
// Method:	POST
func (c *RandomizationController) AddFactorNumber(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	ID := ctx.Query("id")
	err := c.s.AddFactorNumber(ctx, data, ID)
	tools.Response(ctx, err)
}

// DelFactorNumber 删除组合分层因素人数设置
// Method:	POST
func (c *RandomizationController) DelFactorNumber(ctx *gin.Context) {
	id := ctx.Query("id")
	combinationID := ctx.Query("combinationId")
	err := c.s.DelFactorNumber(ctx, id, combinationID)
	tools.Response(ctx, err)
}

// GetLastGroup 获取当前项目下环境cohort下的上一阶段组别
// Method:	GET
func (c *RandomizationController) GetLastGroup(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	lastID := ctx.Query("lastId")
	data, err := c.s.GetLastGroup(ctx, projectID, envID, lastID)
	tools.Response(ctx, err, data)
}

// ExportRandomList 获取当前项目下环境cohort下的上一阶段组别
// Method:	GET
func (c *RandomizationController) ExportRandomList(ctx *gin.Context) {
	ID := ctx.Query("id")
	err := c.s.ExportRandomList(ctx, ID)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// CalculateCombination 获取当前项目下环境cohort下的上一阶段组别
func (c *RandomizationController) CalculateCombination(ctx *gin.Context) {
	ID := ctx.Query("id")
	data, err := c.s.CalculateCombination(ctx, ID)
	tools.Response(ctx, err, data)

}

// CleanFactor 其他分层清空
func (c *RandomizationController) CleanFactor(ctx *gin.Context) {
	ID := ctx.Query("id")
	block := ctx.Query("block")
	blockInt, _ := strconv.Atoi(block)
	err := c.s.CleanFactor(ctx, ID, blockInt)
	tools.Response(ctx, err)
}

// DownloadReport 修改随机列表配置
// Method:	GET
func (c *RandomizationController) DownloadReport(ctx *gin.Context) {
	customerID := ctx.Query("customer_id")
	projectID := ctx.Query("project_id")
	envID := ctx.Query("env_id")
	cohortID := ctx.Query("cohort_id")
	roleId := ctx.Query("roleId")
	err := c.s.DownloadReport(ctx, customerID, projectID, envID, cohortID, roleId)
	if err != nil {
		tools.Response(ctx, err)
	}
}

func (c *RandomizationController) GetRandomStatisticsPie(ctx *gin.Context) {
	data, err := c.s.GetRandomStatisticsPie(ctx)
	tools.Response(ctx, err, data)
}

func (c *RandomizationController) GetRandomStatisticsBar(ctx *gin.Context) {
	data, err := c.s.GetRandomStatisticsBar(ctx)
	tools.Response(ctx, err, data)
}

func (c *RandomizationController) GetRandomListMany(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	var cohorts []string
	if data["cohort_id"] != nil {
		cohorts = data["cohort_id"].([]string)
	}
	res, err := c.s.GetRandomListMany(ctx, data["env_id"].(string), cohorts)
	tools.Response(ctx, err, res)
}

// GetAttributeList ..
func (c *RandomizationController) GetAttributeList(ctx *gin.Context) {
	envID := ctx.Query("envId")
	data, err := c.s.GetAttributeList(ctx, envID)
	tools.Response(ctx, err, data)
}

func (c *RandomizationController) GetRegion(ctx *gin.Context) {
	data, err := c.s.GetRegion(ctx)
	tools.Response(ctx, err, data)
}

func (c *RandomizationController) AddRegion(ctx *gin.Context) {
	err := c.s.AddRegion(ctx)
	tools.Response(ctx, err)
}

func (c *RandomizationController) DeleteRegion(ctx *gin.Context) {
	err := c.s.DeleteRegion(ctx)
	tools.Response(ctx, err)
}

func (c *RandomizationController) SyncRandomList(ctx *gin.Context) {
	err := c.s.SyncRandomList(ctx)
	tools.Response(ctx, err)
}

// GetAttributeConnectAli ..
func (c *RandomizationController) GetAttributeConnectAli(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envId := ctx.Query("envId")
	connectAli := ctx.Query("connectAli")
	aliProjectNo := ctx.Query("aliProjectNo")
	data, err := c.s.GetAttributeConnectAli(ctx, projectID, envId, connectAli, aliProjectNo)
	tools.Response(ctx, err, data)
}

// GetAttributeConnect ..
func (c *RandomizationController) GetAttributeConnect(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envId := ctx.Query("envId")
	data, err := c.s.GetAttributeConnect(ctx, projectID, envId)
	tools.Response(ctx, err, data)
}

// GetFormType 根据应用类型查询表单字段列表
// Method:	GET
func (c *RandomizationController) GetFormType(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	cohortID := ctx.Query("cohortId")
	applicationType, _ := strconv.Atoi(ctx.DefaultQuery("applicationType", "3"))
	data, err := c.s.GetFormType(ctx, customerID, envID, cohortID, applicationType)
	tools.Response(ctx, err, data)
}

// GetRandomCohort 获取开启随机的cohort
// Method:	GET
func (c *RandomizationController) GetRandomCohort(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	data, err := c.s.GetRandomCohort(ctx, projectID, envID)
	tools.Response(ctx, err, data)
}

// GetGroupDistributionStatistics 获取区组分配统计
func (c *RandomizationController) GetGroupDistributionStatistics(ctx *gin.Context) {
	randomListID := ctx.Query("randomListId")

	ret, err := c.s.GetGroupDistributionStatistics(ctx, randomListID)
	tools.Response(ctx, err, ret)
}
