package web

import (
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
)

// AppTaskNoticeController struct
type AppTaskNoticeController struct {
	s service.AppTaskNoticeService
}

// 刚进去的概览查询
func (c *AppTaskNoticeController) Overview(ctx *gin.Context) {
	data, err := c.s.Overview(ctx)
	tools.Response(ctx, err, data)
}

// 查询通知
func (c *AppTaskNoticeController) List(ctx *gin.Context) {
	data, err := c.s.List(ctx)
	tools.Response(ctx, err, data)
}

// 修改状态（已读）
func (c *AppTaskNoticeController) Update(ctx *gin.Context) {
	err := c.s.Update(ctx)
	tools.Response(ctx, err)
}
