package web

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/pb"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/mongo"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// RemoteSubjectController struct
type RemoteSubjectController struct {
	s service.SubjectService
}

// Register ..
// 受试者登记（EDC对接）
// Method:	POST
func (c *RemoteSubjectController) Register(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)
	subject, project, err := parameterConversion(ctx, parameter) // 转换基本参数
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}

	factorSign, err := getLayeredBl(subject) // factorSign为false时表示需要登记分层因素
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}
	if project.SynchronizationMode == 2 { // 全量同步不能单独调用登记接口
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.register.synchronization.error"))
		return
	}

	sbj, _, err := subjectNoVerification(ctx, subject) // 查询受试者是否存在
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}
	cohortId := subject["cohortId"]
	envId := subject["envId"]
	if project.Type == 2 || project.Type == 3 {
		for _, env := range project.Environments {
			if env.ID.Hex() == envId {
				for _, cohort := range env.Cohorts {
					if cohort.ID.Hex() == cohortId && cohort.Status == 1 {
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc_register_error"))
						return
					}
				}
			}
		}
	} else {
		for _, env := range project.Environments {
			if env.ID.Hex() == envId && *env.Status == 1 {
				tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc_register_error"))
				return
			}
		}
	}

	if sbj.SubjectNo == "" {
		subject, err = conversionForm(ctx, subject, parameter, factorSign) // 转换表单参数
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
		// 调用登记接口
		_, _, err = service.SubjectRegister(ctx, subject, nil)
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		} else {
			tools.EdcResponse(ctx, nil)
			return
		}
	} else {
		if sbj.Status == 1 || sbj.Status == 2 {
			if factorSign {
				// code 4 受试者已登记
				tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.existence", 4))
				return
			}
			subject, err = conversionForm(ctx, subject, parameter, factorSign) // 转换表单参数
			if err != nil {
				tools.EdcResponse(ctx, err)
				return
			}
			subject["id"] = sbj.ID.Hex()
			err := service.SaveFactors(ctx, subject, 1, nil, 1)
			if err != nil {
				tools.EdcResponse(ctx, err)
				return
			} else {
				tools.EdcResponse(ctx, nil)
				return
			}
			// code 4 受试者已登记
			//tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.existence", 4))
			//return
		} else {
			// code 5 受试者已经随机
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.operation.edc.fail", 5))
			return
		}
	}
}

// Random ..
// 受试者随机（EDC对接）
// Method:	POST
func (c *RemoteSubjectController) Random(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	subject, project, err := parameterConversion(ctx, parameter) // 转换基本参数
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}

	edcUserName := "EDC"
	if subject["edcUserName"] != nil {
		edcUserName = subject["edcUserName"].(string)
	}

	factorSign, err := getLayeredBl(subject) // factorSign为false时表示需要登记分层因素
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}
	sbj, projectSiteID, err := subjectNoVerification(ctx, subject) // 查询受试者是否存在
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}
	cohortId := subject["cohortId"]
	envId := subject["envId"]
	if project.Type == 2 || project.Type == 3 {
		for _, env := range project.Environments {
			if env.ID.Hex() == envId {
				for _, cohort := range env.Cohorts {
					if cohort.ID.Hex() == cohortId && cohort.Status == 1 {
						tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc_register_error"))
						return
					}
				}
			}
		}
	} else {
		for _, env := range project.Environments {
			if env.ID.Hex() == envId && *env.Status == 1 {
				tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc_register_error"))
				return
			}
		}
	}
	if sbj.SubjectNo == "" { // 不存在 转换表单并且登记随机
		if project.SynchronizationMode == 1 { // 分步同步随机前必须单独走登记接口
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.register.error"))
			return
		}
		subject, err = conversionForm(ctx, subject, parameter, factorSign) // 转换表单参数
		if err != nil {
			tools.EdcResponse(ctx, err)
			return
		}
		data := models.RemoteSubjectRandom{}
		callback := func(sctx mongo.SessionContext) (interface{}, error) {
			subjectID, _, err := service.SubjectRegister(ctx, subject, sctx) // 调用登记接口
			if err != nil {
				return nil, err
			}
			data, err = c.s.SubjectRandom(ctx, subjectID, 1, edcUserName, "", sctx) // 调用随机接口
			if err != nil {
				return nil, err
			}
			return nil, nil
		}
		err := tools.Transaction(callback)
		data.Project = models.Project{}
		tools.EdcResponse(ctx, err, data)
		return
	} else {
		if sbj.Status == 1 || sbj.Status == 2 { // 已登记或者已筛选状态则进行随机
			// 判断EDC和IRT的中心是否一致
			if parameter["siteNo"] == nil {
				tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.site.error"))
				return
			} else {
				// 中心编号
				siteNo, _ := parameter["siteNo"].(string)
				// 标准中心名称
				//standardSiteName := ""
				//if parameter["standardSiteName"] != nil {
				//	standardSiteName, _ = parameter["standardSiteName"].(string)
				//}
				// 普通中心名称
				//siteName := ""
				//if parameter["siteName"] != nil {
				//	siteName, _ = parameter["siteName"].(string)
				//}

				var projectSite models.ProjectSite
				err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteID}).Decode(&projectSite)
				if err != nil {
					tools.EdcResponse(ctx, err)
					return
				}

				//if standardSiteName != "" && projectSite.Name != "" {
				//	if siteNo != projectSite.Number || standardSiteName != projectSite.Name {
				//		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.site.error"))
				//		return
				//	}
				//} else {
				//
				//}
				if siteNo != projectSite.Number {
					tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.subject.site.error"))
					return
				}
			}

			//if factorSign { // true表示登记的时候没录入分层因素那么随机的时候就要录入
			subject, err = conversionForm(ctx, subject, parameter, false) // 转换表单参数
			if err != nil {
				tools.EdcResponse(ctx, err)
				return
			}
			subject["id"] = sbj.ID.Hex()
			data := models.RemoteSubjectRandom{}
			callback := func(sctx mongo.SessionContext) (interface{}, error) {
				err := service.SaveFactors(ctx, subject, 0, sctx, 1)
				if err != nil {
					return nil, err
				}
				//}
				data, err = c.s.SubjectRandom(ctx, sbj.ID.Hex(), 1, edcUserName, "", sctx) // 调用随机接口
				if err != nil {
					return nil, err
				}
				return nil, nil
			}
			err := tools.Transaction(callback)
			data.Project = models.Project{}
			tools.EdcResponse(ctx, err, data)
			return
		} else {
			// code 5 受试者已经随机
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "common.operation.edc.fail", 5), sbj)
			return
		}
	}
}

// SiteSynchronization ..
// 中心同步（EDC对接）
// Method:	POST
func (c *RemoteSubjectController) SiteSynchronization(ctx *gin.Context) {
	// 传入参数
	var edcSynchronizationSite models.EdcSynchronizationSite
	_ = ctx.ShouldBindBodyWith(&edcSynchronizationSite, binding.JSON)

	if edcSynchronizationSite.Version != "2022/7/19" {
		tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.version"))
		return
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询项目
		var project models.Project
		var projects []models.Project
		pjCursor, pjErr := tools.Database.Collection("project").Find(nil, bson.M{"info.number": edcSynchronizationSite.ProjectNo, "info.connect_edc": 1})
		if pjErr != nil {
			return nil, tools.BuildServerError(ctx, "edc.query.project.error")
		}
		_ = pjCursor.All(nil, &projects)
		if projects == nil || len(projects) != 1 {
			return nil, tools.BuildServerError(ctx, "edc.query.project.number.error")
		}
		project = projects[0]
		if project.ResearchAttribute == 1 {
			return nil, tools.BuildServerError(ctx, "edc.query.project.dtp.error")
		}

		// 筛选环境信息
		var environment models.Environment
		for _, env := range project.Environments {
			if env.Name == edcSynchronizationSite.Env {
				environment = env
			}
		}
		if environment.Name == "" {
			return nil, tools.BuildServerError(ctx, "edc.env.error")
		}

		// 查询项目环境下的中心
		var projectSites []models.ProjectSite
		pstCursor, pstErr := tools.Database.Collection("project_site").Find(nil, bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": environment.ID})
		if pstErr != nil {
			tools.EdcResponse(ctx, tools.BuildServerError(ctx, "edc.relation.site.error"))
			return nil, tools.BuildServerError(ctx, "edc.relation.site.error")
		}
		_ = pstCursor.All(nil, &projectSites)

		for _, synchronizationSite := range edcSynchronizationSite.SynchronizationSite {
			if synchronizationSite.SiteNo != "" && synchronizationSite.SiteName != "" {
				projectSiteBl := false

				// 查询该中心是否符合标准中心库 中心名称
				var sitesConfig []models.SitesConfig
				grpcCtx := tools.DmpGrpcContext()
				cli := pb.NewHospitalV2Client(tools.DmpGrpcClientConn)
				req := pb.ListHospitalsV2Request{HospitalName: synchronizationSite.SiteName, CombineNames: true}
				out, err := cli.ListHospitalsV2(grpcCtx, &req)
				if err != nil {
					return nil, tools.BuildServerError(ctx, "edc.site.error")
				}
				for _, site := range out.Items {
					country := site.HospitalAddresses[0].CountryCn
					city := site.HospitalAddresses[0].CityCn
					if locales.Lang(ctx) == "en" {
						country = site.HospitalAddresses[0].CountryEn
						city = site.HospitalAddresses[0].CityEn
					}
					if site.HospitalNames != nil && len(site.HospitalNames) > 0 {
						for _, hospitalName := range site.HospitalNames {
							siteConfig := models.SitesConfig{
								Id:          site.Id,
								Country:     country,
								CountryCode: site.HospitalAddresses[0].CountryCode,
								City:        city,
								TimeZone:    site.HospitalAddresses[0].Timezone,
								En:          hospitalName.En,
								Cn:          hospitalName.Cn,
							}
							if len(siteConfig.Cn) != 0 && len(siteConfig.En) == 0 {
								siteConfig.En = siteConfig.Cn
							} else if len(siteConfig.Cn) == 0 && len(siteConfig.En) != 0 {
								siteConfig.Cn = siteConfig.En
							}
							sitesConfig = append(sitesConfig, siteConfig)
						}
					}
				}
				if len(sitesConfig) == 0 {
					return nil, tools.BuildCustomError(fmt.Sprintf("%s(%s)", locales.Tr(ctx, "edc.standard.site.error"), synchronizationSite.SiteName))
				}
				if len(sitesConfig[0].CountryCode) == 0 {
					return nil, tools.BuildCustomError(fmt.Sprintf("%s(%s)", locales.Tr(ctx, "edc.standard.lost.site.error"), synchronizationSite.SiteName))
				}
				// 处理私有中心
				var pSite models.ProjectSite
				for _, projectSite := range projectSites {
					if projectSite.Number == synchronizationSite.SiteNo {
						pSite = projectSite
						projectSiteBl = true
						break
					}
				}

				if synchronizationSite.SiteName == synchronizationSite.ShortName {
					synchronizationSite.ShortName = ""
				}
				if projectSiteBl { // 中心编号存在， 更新覆盖中心名称，简称 并且置为启用状态
					if _, err := tools.Database.Collection("project_site").UpdateOne(nil, bson.M{"_id": pSite.ID}, bson.M{"$set": bson.M{"deleted": 2, "short_name": synchronizationSite.ShortName, "name": synchronizationSite.SiteName}}); err != nil {
						return nil, tools.BuildServerError(ctx, "edc.start.site.error")
					}
				} else {
					dmpOID, _ := primitive.ObjectIDFromHex(sitesConfig[0].Id)
					contactInfos := make([]models.ContactInfo, 0)

					var projectSite models.ProjectSite
					projectSite.ID = primitive.NewObjectID()
					projectSite.CustomerID = project.CustomerID
					projectSite.ProjectID = project.ID
					projectSite.EnvironmentID = environment.ID
					projectSite.Number = synchronizationSite.SiteNo
					projectSite.Name = synchronizationSite.SiteName
					projectSite.ShortName = synchronizationSite.ShortName
					projectSite.Country = sitesConfig[0].CountryCode
					projectSite.TimeZone = sitesConfig[0].TimeZone
					projectSite.ContactGroup = contactInfos
					projectSite.DmpID = dmpOID
					projectSite.Active = 1
					projectSite.Deleted = 2
					projectSite.Meta = models.Meta{
						CreatedAt: time.Duration(time.Now().Unix()),
					}
					if _, err := tools.Database.Collection("project_site").InsertOne(nil, projectSite); err != nil {
						return nil, tools.BuildServerError(ctx, "edc.add.relation.site.error")
					}
				}
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	tools.EdcResponse(ctx, err)
	return

}

// UpdateSubjectNo ..
func (c *RemoteSubjectController) UpdateSubjectNo(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)
	subject, err := updateSubjectNoParameterConversion(ctx, parameter) // 转换基本参数
	if err != nil {
		tools.EdcResponse(ctx, err)
		return
	}
	err = c.s.UpdateSubjectNo(ctx, subject)
	tools.EdcResponse(ctx, err)
	return
}

// 随机入组参数转换
func parameterConversion(ctx *gin.Context, parameter map[string]interface{}) (map[string]interface{}, models.Project, error) {
	// 返回参数
	type resultMap map[string]interface{}
	subject := make(resultMap)

	// 获取项目编号和环境(参数)
	if parameter["projectNo"] == nil || parameter["env"] == nil || parameter["siteNo"] == nil || parameter["subjectNo"] == nil || parameter["siteName"] == nil {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.parameter.error")
	}
	subjectNo, _ := parameter["subjectNo"].(string)
	projectNo, _ := parameter["projectNo"].(string)
	envName, _ := parameter["env"].(string)
	siteNo, _ := parameter["siteNo"].(string)
	siteName, _ := parameter["siteName"].(string)
	edcUserName := "EDC"
	if parameter["username"] != nil {
		edcUserName = parameter["username"].(string)
	}
	if projectNo == "" || envName == "" || siteNo == "" || subjectNo == "" || siteName == "" {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.parameter.error")
	}
	parameter["shortname"] = subjectNo // 转换受试者号
	// 查询项目
	var project models.Project
	var projects []models.Project
	pjCursor, pjErr := tools.Database.Collection("project").Find(nil, bson.M{"info.number": projectNo, "info.connect_edc": 1})
	if pjErr != nil {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.query.project.error")
	}
	_ = pjCursor.All(nil, &projects)
	if projects == nil || len(projects) != 1 {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.query.project.number.error")
	}
	project = projects[0]
	if project.PushMode == 2 { // irt全量推送不可以从EDC请求
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.error")
	}
	if project.ResearchAttribute == 1 {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.query.project.dtp.error")
	}
	// 筛选环境信息
	var environment models.Environment
	for _, env := range project.Environments {
		if env.Name == envName {
			environment = env
		}
	}
	if environment.Name == "" {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.env.error")
	}

	var projectSites []models.ProjectSite
	var projectSite models.ProjectSite
	cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": environment.ID, "number": siteNo})
	if err != nil {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.relation.site.error")
	}
	_ = cursor.All(nil, &projectSites)
	if len(projectSites) == 0 {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.relation.site.error")
	}
	if len(projectSites) > 1 {
		return nil, models.Project{}, tools.BuildServerError(ctx, "edc.query.site.number.error")
	}
	projectSite = projectSites[0]

	// 各id参数
	subject["customerId"] = project.CustomerID.Hex()
	subject["projectId"] = project.ID.Hex()
	subject["envId"] = environment.ID.Hex()
	subject["projectSiteId"] = projectSite.ID.Hex()
	subject["shortname"] = subjectNo
	subject["edcUserName"] = edcUserName
	// 判断项目类型不是基本研究项目还要查询cohortId
	if project.Type != 1 {
		if parameter["factor"] == nil {
			return nil, models.Project{}, tools.BuildServerError(ctx, "edc.factor.error")
		}
		factor, _ := parameter["factor"].(string)
		if factor == "" {
			return nil, models.Project{}, tools.BuildServerError(ctx, "edc.factor.error")
		}

		var cohort models.Cohort
		for _, ch := range environment.Cohorts {
			if ch.Factor == factor {
				cohort = ch
			}
		}
		if cohort.Name == "" {
			return nil, models.Project{}, tools.BuildServerError(ctx, "edc.query.factor.error")
		}
		// 传入cohortID
		subject["cohortId"] = cohort.ID.Hex()
		// 如果是在随机项目还要把上阶段的ID传进去
		if project.Type == 3 {
			subject["lastId"] = cohort.LastID.Hex()
		}
	}
	return subject, project, nil
}

// 更新受试者号参数转换
func updateSubjectNoParameterConversion(ctx *gin.Context, parameter map[string]interface{}) (map[string]interface{}, error) {
	// 返回参数
	type resultMap map[string]interface{}
	subject := make(resultMap)

	// 获取项目编号和环境(参数)
	if parameter["projectNo"] == nil || parameter["env"] == nil || parameter["siteNo"] == nil || parameter["subjectNo"] == nil || parameter["oldSubjectNo"] == nil {
		return nil, tools.BuildServerError(ctx, "edc.parameter.error")
	}
	edcUserName := "EDC"
	if parameter["username"] != nil {
		edcUserName = parameter["username"].(string)
	}
	projectNo, _ := parameter["projectNo"].(string)
	envName, _ := parameter["env"].(string)
	siteNo, _ := parameter["siteNo"].(string)
	subjectNo, _ := parameter["subjectNo"].(string)
	oldSubjectNo, _ := parameter["oldSubjectNo"].(string)

	if projectNo == "" || envName == "" || siteNo == "" || subjectNo == "" || oldSubjectNo == "" {
		return nil, tools.BuildServerError(ctx, "edc.parameter.error")
	}

	// 查询项目
	//var project models.Project
	var projects []models.Project
	pjCursor, pjErr := tools.Database.Collection("project").Find(nil, bson.M{"info.number": projectNo, "info.connect_edc": 1})
	if pjErr != nil {
		return nil, tools.BuildServerError(ctx, "edc.query.project.error")
	}
	err := pjCursor.All(nil, &projects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if projects == nil || len(projects) != 1 {
		return nil, tools.BuildServerError(ctx, "edc.query.project.number.error")
	}
	if projects[0].ResearchAttribute == 1 {
		return nil, tools.BuildServerError(ctx, "edc.query.project.dtp.error")
	}
	// 筛选环境信息
	var environment models.Environment
	for _, env := range projects[0].Environments {
		if env.Name == envName {
			environment = env
		}
	}
	if environment.Name == "" {
		return nil, tools.BuildServerError(ctx, "edc.env.error")
	}

	// 查询受试者
	var subjectArray []models.Subject
	subjectFile := bson.M{
		"customer_id": projects[0].CustomerID,
		"project_id":  projects[0].ID,
		"env_id":      environment.ID,
		"info": bson.M{
			"$elemMatch": bson.M{
				"name":  "shortname",
				"value": oldSubjectNo,
			},
		},
		"deleted": bson.M{"$ne": true},
	}
	sbjCursor, sbjErr := tools.Database.Collection("subject").Find(nil, subjectFile)
	if sbjErr != nil {
		return nil, tools.BuildServerError(ctx, "edc.query.project.error")
	}
	err = sbjCursor.All(nil, &subjectArray)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if subjectArray == nil || len(subjectArray) == 0 {
		return nil, tools.BuildServerError(ctx, "edc.no.subject")
	} else if len(subjectArray) > 1 {
		return nil, tools.BuildServerError(ctx, "edc.multiple.subject")
	}

	// 各id参数
	subject["customerId"] = projects[0].CustomerID.Hex()
	subject["projectId"] = projects[0].ID.Hex()
	subject["envId"] = environment.ID.Hex()
	subject["id"] = subjectArray[0].ID.Hex()
	subject["shortname"] = subjectNo
	subject["oldSubjectNo"] = oldSubjectNo
	subject["edcUserName"] = edcUserName
	return subject, nil
}

// 表单映射
func conversionForm(ctx *gin.Context, subject map[string]interface{}, parameter map[string]interface{}, factorSign bool) (map[string]interface{}, error) {
	//查询表单
	customerOID, _ := primitive.ObjectIDFromHex(subject["customerId"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(subject["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(subject["envId"].(string))
	projectSiteOID, _ := primitive.ObjectIDFromHex(subject["projectSiteId"].(string))
	var cohortOID = primitive.NilObjectID

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if subject["cohortId"] != nil {
		cohortOID, _ = primitive.ObjectIDFromHex(subject["cohortId"].(string))
		filter = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 声明接受字段信息
	var fields []models.Field
	fields = append(fields, attribute.AttributeInfo.Field)

	// 查询form表单
	var form models.Form
	tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
	if form.Fields != nil {
		for _, fm := range form.Fields {
			if fm.Status == nil || *fm.Status == 1 {
				if fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4 {
					fields = append(fields, fm)
				}
			}
		}
	}

	// 查询是否有分层因素
	randomFilter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"status":      1,
		"$or": bson.A{
			bson.M{"site_ids": nil},
			bson.M{"site_ids": projectSiteOID},
		}}
	if subject["cohortId"] != nil {
		randomFilter = bson.M{
			"customer_id": customerOID,
			"env_id":      envOID,
			"status":      1,
			"cohort_id":   cohortOID,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": projectSiteOID},
			}}
	}
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	if !factorSign {
		for i := 0; i < len(randomList.Design.Factors); i++ {
			if randomList.Design.Factors[i].Status == nil || *randomList.Design.Factors[i].Status == 1 {
				fields = append(fields, models.Field{
					Name:    randomList.Design.Factors[i].Name,
					Label:   randomList.Design.Factors[i].Label,
					Type:    randomList.Design.Factors[i].Type,
					Options: randomList.Design.Factors[i].Options,
				})
			}
		}
	}

	for _, field := range fields {
		if field.Type == "select" || field.Type == "radio" {
			value := ""
			for _, ov := range field.Options {
				if ov.Label == parameter[field.Name] {
					value = ov.Value
				}
			}
			if value == "" {
				return nil, tools.BuildServerError(ctx, "edc.matching.value.error")
			}
			subject[field.Name] = value
		} else if field.Type == "checkbox" {
			str := parameter[field.Name].(string)
			countSplit := strings.Split(str, "$")
			var valueArray []interface{}
			for _, ov := range field.Options {
				for _, sp := range countSplit {
					if ov.Label == sp {
						valueArray = append(valueArray, ov.Value)
					}
				}
			}
			if len(countSplit) != len(valueArray) {
				return nil, tools.BuildServerError(ctx, "edc.check.matching.value.error")
			}
			subject[field.Name] = valueArray
		} else {
			subject[field.Name] = parameter[field.Name]
		}
	}

	return subject, nil
}

/**
 * 校验受试者号是否存在
 */
func subjectNoVerification(ctx *gin.Context, parameter map[string]interface{}) (models.RemoteSubjectRandom, primitive.ObjectID, error) {
	var remoteSubjectRandom models.RemoteSubjectRandom

	// 参数
	customerOID, _ := primitive.ObjectIDFromHex(parameter["customerId"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(parameter["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(parameter["envId"].(string))

	// 项目
	projectFilter := bson.M{"_id": projectOID}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	if err != nil {
		return models.RemoteSubjectRandom{}, primitive.NilObjectID, errors.WithStack(err)
	}

	// 查询项目配置信息
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if parameter["cohortId"] != nil {
		cohortOID, _ := primitive.ObjectIDFromHex(parameter["cohortId"].(string))
		filter = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return models.RemoteSubjectRandom{}, primitive.NilObjectID, errors.WithStack(err)
	}

	// 受试者号校验 针对环境或者cohort
	checkCondition := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
		"info": bson.M{
			"$elemMatch": bson.M{
				"name":  "shortname",
				"value": parameter["shortname"],
			},
		},
		"deleted": bson.M{"$ne": true},
	}
	if project.ProjectInfo.Type == 3 {
		cohortOID, _ := primitive.ObjectIDFromHex(parameter["cohortId"].(string))
		checkCondition["cohort_id"] = cohortOID
	}

	var subject models.Subject
	tools.Database.Collection("subject").FindOne(nil, checkCondition).Decode(&subject)
	if subject.Info != nil && len(subject.Info) > 0 {
		if project.ProjectInfo.Type == 2 && subject.Status != 1 && subject.Status != 2 {
			edcCohortOID, _ := primitive.ObjectIDFromHex(parameter["cohortId"].(string))
			if edcCohortOID != subject.CohortID {
				return models.RemoteSubjectRandom{}, primitive.NilObjectID, tools.BuildServerError(ctx, "common.operation.edc.fail")
			}
		}
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return models.RemoteSubjectRandom{}, primitive.NilObjectID, err
		}
		if projectSite.Tz == "" {
			strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
			if err != nil {
				return models.RemoteSubjectRandom{}, primitive.NilObjectID, err
			}
			if strTimeZone == "" {
				zone, err := tools.GetTimeZone(subject.ProjectID)
				if err != nil {
					return models.RemoteSubjectRandom{}, primitive.NilObjectID, err
				}
				strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
			}
			//timeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
			timeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			remoteSubjectRandom.RandomTime = time.Unix(subject.RandomTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			remoteSubjectRandom.TimeZone = strTimeZone
		} else {
			timeStr, e := tools.GetLocation(projectSite.Tz, int64(subject.RandomTime))
			if e != nil {
				return models.RemoteSubjectRandom{}, primitive.NilObjectID, e
			}
			offset, e := tools.GetLocationTZ(projectSite.Tz, int64(subject.RandomTime))
			if e != nil {
				return models.RemoteSubjectRandom{}, primitive.NilObjectID, e
			}
			remoteSubjectRandom.RandomTime = timeStr
			remoteSubjectRandom.TimeZone = offset
		}

		remoteSubjectRandom.ID = subject.ID
		remoteSubjectRandom.RandomNo = subject.RandomNumber
		remoteSubjectRandom.SubjectNo = subject.Info[0].Value.(string)
		remoteSubjectRandom.Timestamp = subject.RandomTime
		remoteSubjectRandom.Status = subject.Status
		remoteSubjectRandom.Group = subject.Group
		remoteSubjectRandom.SiteNo = projectSite.Number
		remoteSubjectRandom.StandardSiteName = projectSite.Name
		if tools.BlockProject(project.ID.Hex()) && attribute.AttributeInfo.Blind {
			remoteSubjectRandom.Group = tools.BlindData
		}
	}

	return remoteSubjectRandom, subject.ProjectSiteID, nil
}

// 检测登记是否需要填写分层因素（此方法是LayeredBl的嵌套方法，用于查询LayeredBl方法的必传参数拿不到时 用这个方法查询）
func getLayeredBl(subject map[string]interface{}) (bool, error) {
	//查询表单
	customerOID, _ := primitive.ObjectIDFromHex(subject["customerId"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(subject["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(subject["envId"].(string))
	projectSiteOID, _ := primitive.ObjectIDFromHex(subject["projectSiteId"].(string))
	var cohortOID = primitive.NilObjectID

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if subject["cohortId"] != nil {
		cohortOID, _ = primitive.ObjectIDFromHex(subject["cohortId"].(string))
		filter = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return false, errors.WithStack(err)
	}

	// 查询是否有分层因素
	randomFilter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"status":      1,
		"$or": bson.A{
			bson.M{"site_ids": nil},
			bson.M{"site_ids": projectSiteOID},
		}}
	if subject["cohortId"] != nil {
		randomFilter = bson.M{
			"customer_id": customerOID,
			"env_id":      envOID,
			"status":      1,
			"cohort_id":   cohortOID,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": projectSiteOID},
			}}
	}
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	// 查询访视周期
	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	return service.LayeredBl(attribute, visitCycle, randomList.Design.Factors), nil
}
