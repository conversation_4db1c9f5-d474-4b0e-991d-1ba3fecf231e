package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.mongodb.org/mongo-driver/mongo"
)

// SubjectController struct
type SubjectController struct {
	s service.SubjectService
}

// 获取受试者号
func (c *SubjectController) GetSubjectNumber(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	cohortID := ctx.DefaultQuery("cohortId", "")
	siteID := ctx.Query("siteId")
	data, err := c.s.GetSubjectNumber(ctx, customerID, projectID, envID, cohortID, siteID)
	tools.Response(ctx, err, data)
}

// Add 受试者登记
func (c *SubjectController) Add(ctx *gin.Context) {
	var subject map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&subject, binding.JSON)
	_, subjectNumber, err := service.SubjectRegister(ctx, subject, nil)
	tools.Response(ctx, err, subjectNumber)
}

// screen 受试者筛选
func (c *SubjectController) Screen(ctx *gin.Context) {
	var screenReq models.SubjectScreenReq
	_ = ctx.ShouldBindBodyWith(&screenReq, binding.JSON)
	err := service.SubjectScreen(ctx, screenReq)
	tools.Response(ctx, err)
}

// FactorRandom 受试者随机（填写分层因素）
func (c *SubjectController) FactorRandom(ctx *gin.Context) {
	var subject map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&subject, binding.JSON)
	id := subject["id"].(string)
	data := models.RemoteSubjectRandom{}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		err := service.SaveFactors(ctx, subject, 0, sctx, 0)
		if err != nil {
			return nil, err
		} // 保存分层因素数据
		data, err = c.s.SubjectRandom(ctx, id, 0, "", "", sctx) // 随机操作
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err := tools.Transaction(callback)

	// 推给EDC
	if tools.PushScenarioFilter(data.Project.ConnectEdc, data.Project.PushMode, data.Project.EdcSupplier, data.Project.PushScenario.RandomPush) {
		logData := tools.PrepareLogData(ctx)
		service.AsyncSubjectRandomPush(logData, data.ID, 3, data.Timestamp, "", "", "")
	}
	//if data.Project.ConnectEdc == 1 && data.Project.PushMode == 2 && (data.Project.PushTypeEdc == "" || data.Project.PushTypeEdc == "OnlyRandom" || data.Project.PushTypeEdc == "RandomAndDrug") {
	//	service.SubjectRandomPush(ctx, data.ID, 3, data.Timestamp, "", "")
	//}

	tools.Response(ctx, err)
}

// Update 受试者修改
func (c *SubjectController) Update(ctx *gin.Context) {
	err := c.s.Update(ctx)
	tools.Response(ctx, err)
}

// Delete 删除受试者
func (c *SubjectController) Delete(ctx *gin.Context) {
	id := ctx.Query("id")
	err := c.s.Delete(ctx, id)
	tools.Response(ctx, err)
}

// 受试者转运新中心
func (c *SubjectController) Transfer(ctx *gin.Context) {
	err := c.s.Transfer(ctx)
	tools.Response(ctx, err)
}

// GetList ..
func (c *SubjectController) GetList(ctx *gin.Context) {
	req := models.SubjectListReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	data, err := c.s.GetList(ctx, req)
	tools.Response(ctx, err, data)
}

// GetList ..
func (c *SubjectController) GetListCount(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	cohortID := ctx.DefaultQuery("cohortId", "")
	data, err := c.s.GetListCount(ctx, customerID, projectID, envID, cohortID)
	tools.Response(ctx, err, data)
}

// 查询app上展示受试者列表信息
func (c *SubjectController) GetAppList(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	siteID := ctx.Query("siteId")
	roleID := ctx.Query("roleId")
	cohortID := ctx.DefaultQuery("cohortId", "")
	subjectNo := ctx.DefaultQuery("subjectNo", "")
	data, err := c.s.GetAppList(ctx, customerID, projectID, envID, cohortID, siteID, roleID, subjectNo)
	tools.Response(ctx, err, data)
}

// GetSubject 查询单个受试者
func (c *SubjectController) GetSubject(ctx *gin.Context) {
	id := ctx.Query("id")
	data, err := c.s.GetSubject(ctx, id)
	tools.Response(ctx, err, data)
}

// SubjectRandom 受试者随机
func (c *SubjectController) SubjectRandom(ctx *gin.Context) {
	id := ctx.Query("id")
	roleId := ctx.Query("roleId")
	data, err := c.s.SubjectRandom(ctx, id, 0, "", roleId, nil)
	// 推给EDC
	if tools.PushScenarioFilter(data.Project.ConnectEdc, data.Project.PushMode, data.Project.EdcSupplier, data.Project.PushScenario.RandomPush) {
		logData := tools.PrepareLogData(ctx)
		service.AsyncSubjectRandomPush(logData, data.ID, 3, data.Timestamp, "", "", "")
	}
	//if data.Project.ConnectEdc == 1 && data.Project.PushMode == 2 && (data.Project.PushTypeEdc == "" || data.Project.PushTypeEdc == "OnlyRandom" || data.Project.PushTypeEdc == "RandomAndDrug") {
	//	service.SubjectRandomPush(ctx, data.ID, 3, data.Timestamp, "", "")
	//}
	tools.Response(ctx, err)
}

// SubjectEdcVerification edc对接项目受试者随机前校验
func (c *SubjectController) SubjectEdcVerification(ctx *gin.Context) {
	data, err := c.s.SubjectEdcVerification(ctx)
	tools.Response(ctx, err, data)
}

// UpdateStatus 受试者修改状态
func (c *SubjectController) UpdateStatus(ctx *gin.Context) {
	err := c.s.UpdateStatus(ctx)
	tools.Response(ctx, err)
}

// SubjectReplace 受试者替换（基本研究项目/群组研究项目）
func (c *SubjectController) SubjectReplace(ctx *gin.Context) {
	err := c.s.SubjectReplace(ctx)
	tools.Response(ctx, err)
}

// GetSubjectReplaceSite 受试者替换校验是否同中心
func (c *SubjectController) GetSubjectReplaceSite(ctx *gin.Context) {
	data, err := c.s.GetSubjectReplaceSite(ctx)
	tools.Response(ctx, err, data)
}

// GetSubjectReplaceSite 受试者替换校验是否同分层
func (c *SubjectController) GetSubjectReplaceFactor(ctx *gin.Context) {
	data, err := c.s.GetSubjectReplaceFactor(ctx)
	tools.Response(ctx, err, data)
}

// SubjectReplace 受试者替换（再随机项目）
func (c *SubjectController) SubjectReplaceAtRandom(ctx *gin.Context) {
	err := c.s.SubjectReplaceAtRandom(ctx)
	tools.Response(ctx, err)
}

// DownloadUnblindingData
func (c *SubjectController) DownloadUnblindingData(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.DefaultQuery("roleId", "")
	err := c.s.DownloadUnblindingData(ctx, customerID, projectID, envID, roleID)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// DownloadRandomData
func (c *SubjectController) DownloadRandomData(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.DefaultQuery("roleId", "")
	err := c.s.DownloadRandomData(ctx, customerID, projectID, envID, roleID)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// UrgentUnblindingApplication 紧急揭盲申请
func (c *SubjectController) UrgentUnblindingApplication(ctx *gin.Context) {
	data, err := c.s.UrgentUnblindingApplication(ctx)
	tools.Response(ctx, err, data)
}

func (c *SubjectController) UrgentUnblindingApproval(ctx *gin.Context) {
	data, err := c.s.UrgentUnblindingApproval(ctx)
	tools.Response(ctx, err, data)
}

func (c *SubjectController) ResendSms(ctx *gin.Context) {
	err := c.s.ResendSms(ctx)
	tools.Response(ctx, err)
}

func (c *SubjectController) GetSubjectInfo(ctx *gin.Context) {
	var params map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&params, binding.JSON)
	envID := params["env_id"].(string)
	cohortIDs := params["cohort_id"].([]interface{})
	projectSiteID := params["project_site_id"].([]interface{})
	data, err := c.s.GetSubjectInfo(ctx, envID, cohortIDs, projectSiteID)
	tools.Response(ctx, err, data)
}

func (c *SubjectController) SubjectReplaceRandomNumber(ctx *gin.Context) {
	data, err := c.s.SubjectReplaceRandomNumber(ctx)
	tools.Response(ctx, err, data)
}

// RealityCapacity 实际入组
func (c *SubjectController) RealityCapacity(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	cohortID := ctx.DefaultQuery("cohortId", "")
	data, err := c.s.RealityCapacity(ctx, customerID, projectID, envID, cohortID)
	tools.Response(ctx, err, data)
}

// UpdateSubjectJoinTime 仅发药入组时间更新
func (c *SubjectController) UpdateSubjectJoinTime(ctx *gin.Context) {
	var params map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&params, binding.JSON)
	ID := params["id"].(string)
	joinTime := params["joinTime"].(string)
	roleId := params["roleId"].(string)
	err := c.s.UpdateSubjectJoinTime(ctx, ID, joinTime, roleId)
	tools.Response(ctx, err)
}

// 查询在随机的表单
func (c *SubjectController) AtRandomForm(ctx *gin.Context) {
	subjectId := ctx.Query("subjectId")
	data, err := c.s.AtRandomForm(ctx, subjectId)
	tools.Response(ctx, err, data)
}

// 更新实际分层
func (c *SubjectController) UpdateActualFactor(ctx *gin.Context) {
	err := c.s.UpdateActualFactor(ctx)
	tools.Response(ctx, err)
}

// GetNewSubjectPage 查找在当前页面查询条件下，新增的受试者所处的页码
func (c *SubjectController) GetNewSubjectPage(ctx *gin.Context) {
	req := models.NewSubjectPageReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	data, err := c.s.GetNewSubjectPage(ctx, req)
	tools.Response(ctx, err, data)
}

// 检查是否有需要推送给EDC的历史数据
func (c *SubjectController) CheckPushHistory(ctx *gin.Context) {
	req := models.CheckPushHistoryReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	data, err := c.s.CheckPushHistory(ctx, req)
	tools.Response(ctx, err, data)
}

// 给EDC推送历史数据
func (c *SubjectController) PushHistory(ctx *gin.Context) {
	req := models.CheckPushHistoryReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	err := c.s.PushHistory(ctx, req)
	tools.Response(ctx, err)
}

// 查询无效的受试者
func (c *SubjectController) GetInvalidList(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	cohortID := ctx.Query("cohortId")
	data, err := c.s.GetInvalidList(ctx, projectID, envID, roleID, cohortID)
	tools.Response(ctx, err, data)
}

// SwitchCohort 受试者切换群组
func (c *SubjectController) SwitchCohort(ctx *gin.Context) {
	err := c.s.SwitchCohort(ctx)
	tools.Response(ctx, err)
}
