package web

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strings"
	"time"
)

// AliMsgController struct
type AliMsgController struct {
}

// UnblindedApprovalMsg
// Method:	POST
func (c *AliMsgController) UnblindedApprovalMsg(ctx *gin.Context) {
	err := c.unblindedApproval(ctx)
	if err != nil {
		tools.SaveErrorLog("UnblindedApprovalMsg", err)
	}
	success := struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}{
		Code: 0,
		Msg:  "成功",
	}
	ctx.JSON(200, success)
}

func (c *AliMsgController) unblindedApproval(ctx *gin.Context) error {
	type msgCallback = struct {
		Content     string `json:"content"`
		PhoneNumber string `json:"phone_number"`
		SendTime    string `json:"send_time"`
	}
	msgs := make([]msgCallback, 0)
	err := ctx.ShouldBindBodyWith(&msgs, binding.JSON)
	if err != nil {
		return errors.WithStack(err)
	}
	var now time.Time
	for _, msg := range msgs {
		if msg.Content == "" || msg.SendTime == "" || msg.PhoneNumber == "" {
			continue
		}
		phone := msg.PhoneNumber
		phone86 := msg.PhoneNumber
		if !strings.Contains(phone, "+86") {
			phone86 = fmt.Sprintf("%s%s", "+86", msg.PhoneNumber)
		}
		parse, _ := time.Parse("2006-01-02 15:04:05", msg.SendTime)
		utcTime := parse.UTC().Add(time.Hour * time.Duration(-8))
		//对应utc时间戳
		unix := utcTime.Unix()
		callbackApprovalNumber := fmt.Sprintf("%s%s", utcTime.Format("20060102"), msg.Content[:3])
		agree64, err := convertor.ToInt(msg.Content[3:])
		if err != nil {
			continue
		}
		agree := int(agree64)
		callback := func(sctx mongo.SessionContext) (interface{}, error) {
			update := bson.M{}
			now = time.Now()
			// 1紧急揭盲 / 2pv揭盲
			unblindingSign := 1
			// 查紧急揭盲
			filter := bson.M{
				//"urgent_unblinding_approvals.sms_users.phone":  phone,
				"$or": bson.A{
					bson.M{"urgent_unblinding_approvals.sms_users.phone": phone},
					bson.M{"urgent_unblinding_approvals.sms_users.phone": phone86},
				},
				"urgent_unblinding_approvals.status":           0,
				"urgent_unblinding_approvals.application_time": bson.M{"$lt": unix},
				"urgent_unblinding_approvals.number":           callbackApprovalNumber,
				"deleted":                                      bson.M{"$ne": true},
			}
			var subject models.Subject
			err := tools.Database.Collection("subject").FindOne(sctx, filter).Decode(&subject)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			if subject.ID.IsZero() { // 查不到再查pv揭盲
				unblindingSign = 2
				filter = bson.M{
					//"pv_urgent_unblinding_approvals.sms_users.phone":  phone,
					"$or": bson.A{
						bson.M{"pv_urgent_unblinding_approvals.sms_users.phone": phone},
						bson.M{"pv_urgent_unblinding_approvals.sms_users.phone": phone86},
					},
					"pv_urgent_unblinding_approvals.status":           0,
					"pv_urgent_unblinding_approvals.application_time": bson.M{"$lt": unix},
					"pv_urgent_unblinding_approvals.number":           callbackApprovalNumber,
				}
				err = tools.Database.Collection("subject").FindOne(sctx, filter).Decode(&subject)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}

			}

			if subject.ID.IsZero() { // 查不到再查ip揭盲
				filter = bson.M{
					//"pv_urgent_unblinding_approvals.sms_users.phone":  phone,
					"$or": bson.A{
						bson.M{"unblinding_approvals.sms_users.phone": phone},
						bson.M{"unblinding_approvals.sms_users.phone": phone86},
					},
					"unblinding_approvals.status":           0,
					"unblinding_approvals.application_time": bson.M{"$lt": unix},
					"unblinding_approvals.number":           callbackApprovalNumber,
				}
				var medicine models.Medicine
				err = tools.Database.Collection("medicine").FindOne(sctx, filter).Decode(&medicine)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				var dispensing models.Dispensing
				err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"dispensing_medicines.medicine_id": medicine.ID}).Decode(&dispensing)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}

				err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				approvalP, ok := slice.Find(medicine.UnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
					return item.Number == callbackApprovalNumber
				})
				approval := *approvalP

				meP, ok := slice.Find(approval.SmsUsers, func(index int, item models.SmsUser) bool {
					return item.Phone == phone || item.Phone == phone86
				})
				if !ok {
					return nil, nil
				}
				me := *meP
				user := models.User{
					ID:      me.UserId,
					CloudId: me.CloudId,
					UserInfo: models.UserInfo{
						Name:  me.Name,
						Email: me.Email,
						Phone: me.Phone,
					},
				}
				ctx.Set("user", user)
				lang := approval.Lang
				ctx.Request.Header.Set("Accept-Language", lang)
				reason := locales.Tr(ctx, "subject_urgentUnblindingApproval_reason_other")
				_, err := service.UnbindingApproval(ctx, sctx, subject, medicine.ID, dispensing.ID, approval.Number, agree, reason, primitive.NilObjectID, now)
				return nil, errors.WithStack(err)

			}
			if subject.ID.IsZero() {
				return nil, nil
			}
			if unblindingSign == 1 {
				if subject.Status != 3 {
					return nil, nil
				}
			} else if unblindingSign == 2 {
				if subject.Status != 3 && subject.Status != 6 {
					return nil, nil
				}
			}

			approvalP, ok := slice.Find(subject.UrgentUnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
				return item.Number == callbackApprovalNumber
			})
			if unblindingSign == 2 {
				approvalP, ok = slice.Find(subject.PvUrgentUnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
					return item.Number == callbackApprovalNumber
				})
			}

			if !ok {
				return nil, nil
			}
			approval := *approvalP
			meP, ok := slice.Find(approval.SmsUsers, func(index int, item models.SmsUser) bool {
				return item.Phone == phone || item.Phone == phone86
			})
			if !ok {
				return nil, nil
			}
			me := *meP
			if approval.Status == 1 || approval.Status == 2 {
				return nil, nil
			}
			lang := approval.Lang
			ctx.Request.Header.Set("Accept-Language", lang)
			rejectReason := ""
			var project models.Project
			err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			envp, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.ID == subject.EnvironmentID
			})
			env := *envp
			attributeFilter := bson.M{"env_id": subject.EnvironmentID}
			if !subject.CohortID.IsZero() {
				attributeFilter = bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
			}
			var attribute models.Attribute
			err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var projectSite models.ProjectSite
			err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 添加轨迹
			history := models.History{
				OID:  subject.ID,
				Time: time.Duration(now.Unix()),
				UID:  me.UserId,
				User: me.Name,
			}
			//通过
			if agree == 1 {
				update = bson.M{"$set": bson.M{
					"status":                                          6,
					"urgent_unblinding_approvals.$.status":            1,
					"urgent_unblinding_status":                        1,
					"urgent_unblinding_time":                          time.Duration(now.Unix()),
					"urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
					"urgent_unblinding_approvals.$.approval_by":       me.UserId,
					"urgent_unblinding_approvals.$.approval_by_email": me.Email,
				}}
				history.Data = map[string]interface{}{"approvalNumber": callbackApprovalNumber}
				history.Key = "history.subject.unblinding-approval-agree"

				if unblindingSign == 2 { // pv揭盲
					update = bson.M{"$set": bson.M{
						//"status":                                          6,
						"pv_urgent_unblinding_approvals.$.status":            1,
						"pv_unblinding_status":                               1,
						"pv_unblinding_time":                                 time.Duration(now.Unix()),
						"pv_urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
						"pv_urgent_unblinding_approvals.$.approval_by":       me.UserId,
						"pv_urgent_unblinding_approvals.$.approval_by_email": me.Email,
					}}
					history.Key = "history.subject.unblinding-approval-agree-pv"
				}
			} else if agree == 2 {
				rejectReason = locales.Tr(ctx, "subject_urgentUnblindingApproval_reason_other")
				update = bson.M{"$set": bson.M{
					"urgent_unblinding_approvals.$.status":            2,
					"urgent_unblinding_approvals.$.reject_reason":     rejectReason,
					"urgent_unblinding_time":                          time.Duration(now.Unix()),
					"urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
					"urgent_unblinding_approvals.$.approval_by":       me.UserId,
					"urgent_unblinding_approvals.$.approval_by_email": me.Email,
				}}
				history.Data = map[string]interface{}{"approvalNumber": callbackApprovalNumber, "reason": rejectReason}
				history.Key = "history.subject.unblinding-approval-reject"

				if unblindingSign == 2 { // pv揭盲
					update = bson.M{"$set": bson.M{
						"pv_urgent_unblinding_approvals.$.status":            2,
						"pv_urgent_unblinding_approvals.$.reject_reason":     rejectReason,
						"pv_unblinding_time":                                 time.Duration(now.Unix()),
						"pv_urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
						"pv_urgent_unblinding_approvals.$.approval_by":       me.UserId,
						"pv_urgent_unblinding_approvals.$.approval_by_email": me.Email,
					}}
				}
			}
			updateFilter := bson.M{"_id": subject.ID, "urgent_unblinding_approvals.number": callbackApprovalNumber}
			if unblindingSign == 2 { // pv揭盲
				updateFilter = bson.M{"_id": subject.ID, "pv_urgent_unblinding_approvals.number": callbackApprovalNumber}
			}
			_, err = tools.Database.Collection("subject").UpdateOne(sctx, updateFilter, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			ctx.Set("HISTORY", []models.History{history})

			//如果有任务关闭任务
			if unblindingSign == 1 { // 紧急揭盲
				_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"info.subject_approval.number": approval.Number},
					bson.M{"$set": bson.M{"info.status": 1,
						"info.finish_time":    time.Duration(time.Now().Unix()),
						"info.finish_user_id": me.UserId,
					}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
			} else {
				_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"info.subject_pv_approval.number": approval.Number},
					bson.M{"$set": bson.M{"info.status": 1,
						"info.finish_time":    time.Duration(time.Now().Unix()),
						"info.finish_user_id": me.UserId,
					}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
			// **************************************20241112分支-邮件调整开始*************************************
			// 揭盲审批发送的邮件通知（通知对象：操作人+审批人）
			userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
			userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": subject.EnvironmentID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var userIDs []primitive.ObjectID
			for _, upel := range userProjectEnvironmentList {
				userIDs = append(userIDs, upel.UserID)
			}
			// 查询user
			var userList []models.User
			userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = userCursor.All(nil, &userList)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 查询项目角色权限
			projectRolePermissionList := make([]models.ProjectRolePermission, 0)
			projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": subject.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 查询用户关联的角色
			userSiteList := make([]models.UserSite, 0)
			userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": subject.EnvironmentID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = userSiteCursor.All(nil, &userSiteList)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var noticeConfig models.NoticeConfig
			err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": env.ID, "key": "notice.subject.unblinding"}).Decode(&noticeConfig)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, err
			}

			excludeRecipientMap := make(map[string]bool)
			if noticeConfig.ExcludeRecipientList != nil && len(noticeConfig.ExcludeRecipientList) > 0 {
				excludeRecipientMap = getArrMap(noticeConfig.ExcludeRecipientList)
			}

			emailList := make([]string, 0)
			unblindingApprovalUser, pvUnblindingApprovalUser := queryApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subject.ProjectSiteID)
			if unblindingSign == 1 { // 紧急揭盲
				for _, uau := range unblindingApprovalUser {
					emailList = append(emailList, uau.Email)
				}
			} else {
				for _, puau := range pvUnblindingApprovalUser {
					emailList = append(emailList, puau.Email)
				}
			}

			// 查询揭盲审批发起人
			emailSign := true
			var applicationUser models.User
			ap := *approvalP
			err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": ap.ApplicationBy}).Decode(&applicationUser)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if emailList != nil && len(emailList) > 0 {
				for _, e := range emailList {
					if e == applicationUser.Email {
						emailSign = false
						break
					}
				}
			}
			if emailSign {
				emailList = append(emailList, applicationUser.Email)
			}

			emails := make([]string, 0)
			for _, email := range emailList {
				isExists := false
				if _, is := excludeRecipientMap[email]; is {
					isExists = true
				}
				if !isExists {
					emails = append(emails, email)
				}
			}

			shTime, err := tools.GetTimeZoneTime(now.UTC(), projectSite, project)
			if err != nil {
				return nil, err
			}

			// 收件人
			var mails []models.Mail
			var envs []models.MailEnv

			var mailsA []models.Mail
			if emails != nil && len(emails) > 0 {
				remark := ""
				for _, approval := range subject.UrgentUnblindingApprovals {
					if approval.Number == callbackApprovalNumber {
						remark = approval.Remark
						break
					}
				}
				if unblindingSign == 2 {
					for _, approval := range subject.PvUrgentUnblindingApprovals {
						if approval.Number == callbackApprovalNumber {
							remark = approval.Remark
							break
						}
					}
				}

				mailBodyContet, err := tools.MailBodyContent(ctx, env.ID, "notice.subject.unblinding")
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, email := range emails {
					var toUserMail []string
					toUserMail = append(toUserMail, email)
					mail := models.Mail{
						ID:           primitive.NewObjectID(),
						To:           toUserMail,
						Lang:         ctx.GetHeader("Accept-Language"),
						Status:       0,
						CreatedTime:  time.Duration(now.Unix()),
						ExpectedTime: time.Duration(now.Unix()),
						SendTime:     time.Duration(now.Unix()),
					}
					mail.Subject = "subject.unblinding-approval.title"
					if unblindingSign == 2 {
						mail.Subject = "subject.pv-unblinding-approval.title"
					}
					mail.SubjectData = map[string]interface{}{
						"projectNumber": project.Number,
						"envName":       env.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
					}
					mail.Content = "subject.unblinding-approval.content"
					if unblindingSign == 2 {
						mail.Content = "subject.pv-unblinding-approval.content"
					}
					randomNumber := tools.BlindData
					if attribute.AttributeInfo.IsRandomNumber {
						randomNumber = subject.RandomNumber
					}
					reason := subject.UrgentUnblindingReasonStr
					if unblindingSign == 2 {
						reason = subject.PvUnblindingReasonStr
					}
					contentData := map[string]interface{}{
						"projectNumber":  project.Number,
						"projectName":    project.Name,
						"envName":        env.Name,
						"siteNumber":     projectSite.Number,
						"siteName":       tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":     tools.GetProjectSiteLangName(projectSite, "en"),
						"subjectNumber":  subject.Info[0].Value,
						"randomNumber":   randomNumber,
						"time":           shTime,
						"reason":         reason,
						"approvalResult": tools.GetApprovalResultTran(ctx, agree),
						"rejectReason":   rejectReason,
						"remark":         remark,
						"approvalNumber": callbackApprovalNumber,
						"label":          attribute.AttributeInfo.SubjectReplaceText,
					}
					for key, v := range mailBodyContet {
						contentData[key] = v
					}
					bodyContentKeys, _, err := tools.MailCustomContent(&gin.Context{}, env.ID, "notice.subject.unblinding", contentData, nil)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					mail.BodyContentKey = bodyContentKeys
					mail.ContentData = contentData
					mailsA = append(mailsA, mail)
					mails = append(mails, mail)
				}
			}
			if len(mailsA) > 0 {
				for _, m := range mailsA {
					envs = append(envs, models.MailEnv{
						ID:         primitive.NewObjectID(),
						MailID:     m.ID,
						CustomerID: subject.CustomerID,
						ProjectID:  subject.ProjectID,
						EnvID:      subject.EnvironmentID,
						CohortID:   subject.CohortID,
					})
				}
			}

			// 审批通过并且是紧急揭盲才发送审批通过邮件提醒（通知对象：通知设置-紧急揭盲的角色）
			if agree == 1 && unblindingSign == 1 {
				// 揭盲发送邮件
				userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.unblinding", subject.ProjectSiteID)
				if err != nil {
					return nil, err
				}
				var mailsB []models.Mail
				if len(userMail) > 0 {
					for _, userEmail := range userMail {
						var toUserMail []string
						toUserMail = append(toUserMail, userEmail.Email)
						mail := models.Mail{
							ID:           primitive.NewObjectID(),
							To:           toUserMail,
							Lang:         ctx.GetHeader("Accept-Language"),
							Status:       0,
							CreatedTime:  time.Duration(now.Unix()),
							ExpectedTime: time.Duration(now.Unix()),
							SendTime:     time.Duration(now.Unix()),
						}
						mail.Subject = "subject.ordinary-unblinding-approval.title"
						mail.SubjectData = map[string]interface{}{
							"projectNumber": project.Number,
							"envName":       env.Name,
							"siteNumber":    projectSite.Number,
							"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
							"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
						}
						mail.Content = "subject.ordinary-unblinding-approval.content"
						randomNumber := tools.BlindData
						if attribute.AttributeInfo.IsRandomNumber {
							randomNumber = subject.RandomNumber
						}
						reason := subject.UrgentUnblindingReasonStr
						contentData := map[string]interface{}{
							"projectNumber": project.Number,
							"projectName":   project.Name,
							"envName":       env.Name,
							"siteNumber":    projectSite.Number,
							"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
							"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
							"label":         attribute.AttributeInfo.SubjectReplaceText,
							"subjectNumber": subject.Info[0].Value,
							"randomNumber":  randomNumber,
							"time":          shTime,
							"reason":        reason,
						}
						mail.ContentData = contentData
						mailsB = append(mailsB, mail)
						mails = append(mails, mail)
					}
				}

				if len(mailsB) > 0 {
					for _, m := range mailsB {
						envs = append(envs, models.MailEnv{
							ID:         primitive.NewObjectID(),
							MailID:     m.ID,
							CustomerID: subject.CustomerID,
							ProjectID:  subject.ProjectID,
							EnvID:      subject.EnvironmentID,
							CohortID:   subject.CohortID,
						})
					}
				}
			}

			if mails != nil && len(mails) > 0 {
				ctx.Set("MAIL", mails)
			}
			if envs != nil && len(envs) > 0 {
				ctx.Set("MAIL-ENV", envs)
			}
			// **************************************20241112分支-邮件调整结束*************************************

			// 揭盲发送邮件
			//userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.unblinding", subject.ProjectSiteID)
			//if err != nil {
			//	return nil, err
			//}
			//shTime, err := tools.GetTimeZoneTime(now.UTC(), projectSite, project)
			//if err != nil {
			//	return nil, err
			//}
			//var mails []models.Mail
			//if len(userMail) > 0 {
			//	mailBodyContet, err := tools.MailBodyContent(ctx, env.ID, "notice.subject.unblinding")
			//	if err != nil {
			//		return nil, errors.WithStack(err)
			//	}
			//	for _, userEmail := range userMail {
			//		var toUserMail []string
			//		toUserMail = append(toUserMail, userEmail.Email)
			//		mail := models.Mail{
			//			ID:           primitive.NewObjectID(),
			//			To:           toUserMail,
			//			Lang:         lang,
			//			Status:       0,
			//			CreatedTime:  time.Duration(now.Unix()),
			//			ExpectedTime: time.Duration(now.Unix()),
			//			SendTime:     time.Duration(now.Unix()),
			//		}
			//		mail.Subject = "subject.unblinding-approval.title"
			//		if unblindingSign == 2 {
			//			mail.Subject = "subject.pv-unblinding-approval.title"
			//		}
			//		mail.SubjectData = map[string]interface{}{
			//			"projectNumber": project.Number,
			//			"envName":       env.Name,
			//			"siteNumber":    projectSite.Number,
			//			"siteName":      models.GetProjectSiteName(ctx, projectSite),
			//		}
			//		mail.Content = "subject.unblinding-approval.content"
			//		if unblindingSign == 2 {
			//			mail.Content = "subject.pv-unblinding-approval.content"
			//		}
			//		randomNumber := subject.RandomNumber
			//		if !attribute.AttributeInfo.IsRandomNumber {
			//			randomNumber = tools.BlindData
			//		}
			//		reason := subject.UrgentUnblindingReasonStr
			//		if unblindingSign == 2 {
			//			reason = subject.PvUnblindingReasonStr
			//		}
			//		contentData := map[string]interface{}{
			//			"projectNumber":  project.Number,
			//			"projectName":    project.Name,
			//			"envName":        env.Name,
			//			"siteNumber":     projectSite.Number,
			//			"siteName":       models.GetProjectSiteName(ctx, projectSite),
			//			"subjectNumber":  subject.Info[0].Value,
			//			"randomNumber":   randomNumber,
			//			"time":           shTime,
			//			"reason":         reason,
			//			"approvalResult": tools.GetApprovalResultTran(ctx, agree),
			//			"rejectReason":   rejectReason,
			//			"remark":         approval.Remark,
			//			"approvalNumber": callbackApprovalNumber,
			//			"label":          attribute.AttributeInfo.SubjectReplaceText,
			//		}
			//		for key, v := range mailBodyContet {
			//			contentData[key] = v
			//		}
			//		mail.ContentData = contentData
			//		bodyContentKeys, _, err := tools.MailCustomContent(&gin.Context{}, env.ID, "notice.subject.unblinding", contentData, nil)
			//		if err != nil {
			//			return nil, errors.WithStack(err)
			//		}
			//		mail.BodyContentKey = bodyContentKeys
			//		mails = append(mails, mail)
			//	}
			//}
			//ctx.Set("MAIL", mails)
			//if len(mails) > 0 {
			//	var envs []models.MailEnv
			//	for _, m := range mails {
			//		envs = append(envs, models.MailEnv{
			//			ID:         primitive.NewObjectID(),
			//			MailID:     m.ID,
			//			CustomerID: subject.CustomerID,
			//			ProjectID:  subject.ProjectID,
			//			EnvID:      subject.EnvironmentID,
			//			CohortID:   subject.CohortID,
			//		})
			//	}
			//	ctx.Set("MAIL-ENV", envs)
			//}

			//查询对应的项目任务
			taskType := 2 // 紧急揭盲
			if unblindingSign == 2 {
				taskType = 3 // pv揭盲
			}
			//更新审批任务
			approvalProcessMatch := bson.M{"project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "type": taskType, "status": 0, "unblinding_data.subject_id": subject.ID, "unblinding_data.number": callbackApprovalNumber}
			approvalProcessUpdate := bson.M{
				"$set": bson.M{
					"approval_status":                   agree,
					"approval_time":                     time.Duration(time.Now().Unix()),
					"approval_by":                       me.UserId,
					"approval_by_email":                 me.Email,
					"reason":                            rejectReason,
					"status":                            1,
					"unblinding_data.approval_time":     time.Duration(time.Now().Unix()),
					"unblinding_data.approval_by":       me.UserId,
					"unblinding_data.approval_by_email": me.Email,
					"unblinding_data.reason":            rejectReason,
					"unblinding_data.status":            agree,
				},
			}
			_, err = tools.Database.Collection("approval_process").UpdateOne(sctx, approvalProcessMatch, approvalProcessUpdate)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			return nil, nil

		}
		err = tools.Transaction(callback)
		if err != nil {
			return err
		}
	}
	return nil
}

// 查询有揭盲权限的用户
func queryApprovalUser(
	userProjectEnvironmentList []models.UserProjectEnvironment,
	userList []models.User,
	projectRolePermissionList []models.ProjectRolePermission,
	userSiteList []models.UserSite,
	siteOID primitive.ObjectID,
) ([]models.ApprovalUser, []models.ApprovalUser) {

	var unblindingApprovalUser []models.ApprovalUser
	var pvUnblindingApprovalUser []models.ApprovalUser

	for _, upel := range userProjectEnvironmentList {
		if !upel.Unbind { // 排除无效用户
			for _, rid := range upel.Roles {
				for _, prpl := range projectRolePermissionList {
					var user models.User
					if rid == prpl.ID {
						if prpl.Scope == "study" { // 全权限
							// 筛选用户
							for _, u := range userList {
								if u.ID == upel.UserID {
									user = u
									break
								}
							}

							// 紧急揭盲
							if queryApprovalUserOperationPermissions(prpl.Permissions, 1) {
								// 数据去重
								bl := true
								for _, uau := range unblindingApprovalUser {
									if uau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									unblindingApprovalUser = append(unblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
								}
							}

							// pv揭盲
							if queryApprovalUserOperationPermissions(prpl.Permissions, 2) {
								// 数据去重
								bl := true
								for _, puau := range pvUnblindingApprovalUser {
									if puau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									pvUnblindingApprovalUser = append(pvUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
								}
							}
						} else if prpl.Scope == "site" {
							// 判断数据权限
							_, ok := slice.Find(userSiteList, func(index int, item models.UserSite) bool {
								return item.SiteID == siteOID && item.UserID == upel.UserID
							})
							if ok {
								// 筛选用户
								for _, u := range userList {
									if u.ID == upel.UserID {
										user = u
										break
									}
								}

								// 紧急揭盲
								if queryApprovalUserOperationPermissions(prpl.Permissions, 1) {
									// 数据去重
									bl := true
									for _, uau := range unblindingApprovalUser {
										if uau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										unblindingApprovalUser = append(unblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
									}
								}

								// pv揭盲
								if queryApprovalUserOperationPermissions(prpl.Permissions, 2) {
									// 数据去重
									bl := true
									for _, puau := range pvUnblindingApprovalUser {
										if puau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										pvUnblindingApprovalUser = append(pvUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
									}
								}
							}
						}
						break
					}
				}
			}
		}

	}
	return unblindingApprovalUser, pvUnblindingApprovalUser
}

func queryApprovalUserOperationPermissions(permissions []string, sign int) bool {
	bl := true
	if permissions == nil || len(permissions) == 0 {
		bl = false
	} else {
		if sign == 1 {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding-approval" || item == "operation.subject-dtp.unblinding-approval"
			})
			if len(p) == 0 {
				bl = false
			}
		} else {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding-pv-approval"
			})
			if len(p) == 0 {
				bl = false
			}
		}
	}
	return bl
}

func getArrMap(arr []string) map[string]bool {
	// 使用一个 map 来提高查找效率
	exists := make(map[string]bool)
	// 将 array2 的元素存入 map
	for _, value := range arr {
		exists[value] = true
	}
	return exists
}
