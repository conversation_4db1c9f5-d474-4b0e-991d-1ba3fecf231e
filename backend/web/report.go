package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type ReportController struct {
	s service.ReportService
}

func (c *ReportController) GetReports(ctx *gin.Context) {
	projectId := ctx.Query("projectId")
	envId := ctx.Query("envId")
	data, err := c.s.GetReports(ctx, projectId, envId)
	tools.Response(ctx, err, data)
}

func (c *ReportController) GetTemplate(ctx *gin.Context) {
	templateType, _ := strconv.Atoi(ctx.Query("type"))
	projectId := ctx.Query("projectId")
	envId := ctx.Query("envId")
	data, err := c.s.GetTemplate(ctx, templateType, projectId, envId)
	tools.Response(ctx, err, data)
}

func (c *ReportController) AddTemplate(ctx *gin.Context) {
	var template models.CustomTemplate
	err := ctx.ShouldBindBodyWith(&template, binding.JSON)
	if err == nil {
		err = c.s.AddTemplate(ctx, template)
	}
	tools.Response(ctx, err)
}

func (c *ReportController) UpdateTemplate(ctx *gin.Context) {
	id := ctx.Query("id")
	var template models.CustomTemplate
	err := ctx.ShouldBindBodyWith(&template, binding.JSON)
	if err == nil {
		err = c.s.UpdateTemplate(ctx, id, template)
	}
	tools.Response(ctx, err)
}

func (c *ReportController) DeleteTemplate(ctx *gin.Context) {
	id := ctx.Query("id")
	err := c.s.DeleteTemplate(ctx, id)
	tools.Response(ctx, err)
}

func (c *ReportController) CopyTemplate(ctx *gin.Context) {
	id := ctx.Query("id")
	err := c.s.CopyTemplate(ctx, id)
	tools.Response(ctx, err)
}

func (c *ReportController) Export(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	reportType, _ := parameter["type"].(float64)
	projectId, _ := parameter["projectId"].(string)
	envId, _ := parameter["envId"].(string)
	templateId, _ := parameter["templateId"].(string)
	cohortID, _ := parameter["cohortId"].(string)
	roleId, _ := parameter["roleId"].(string)
	startTime, _ := parameter["startTime"].(float64)
	endTime, _ := parameter["endTime"].(float64)
	exportTypes, _ := parameter["exportTypes"].(string)
	cipher, _ := parameter["cipher"].(string)
	simulateRandomId, _ := parameter["simulateRandomId"].(string)
	err := c.s.Export(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId)
	if err != nil {
		tools.Response(ctx, err)
	}
}

func (c *ReportController) GetHistory(ctx *gin.Context) {
	reportType, _ := strconv.Atoi(ctx.Query("reportType"))
	projectId := ctx.Query("projectId")
	envId := ctx.Query("envId")
	data, err := c.s.GetHistory(ctx, projectId, envId, reportType)
	tools.Response(ctx, err, data)
}

func (c *ReportController) DownloadHistory(ctx *gin.Context) {
	id := ctx.Query("id")
	err := c.s.DownloadHistory(ctx, id)
	if err != nil {
		tools.Response(ctx, err)
	}
}

func (c *ReportController) ExportAuthority(ctx *gin.Context) {
	roleId := ctx.Request.URL.Query().Get("roleId")

	resp, err := c.s.ExportAuthority(ctx, roleId)
	tools.Response(ctx, err, resp)
}

func (c *ReportController) ExportRandomizationSimulation(ctx *gin.Context) {

	customerId := ctx.Request.URL.Query().Get("customerId")
	projectId := ctx.Request.URL.Query().Get("projectId")
	envId := ctx.Request.URL.Query().Get("envId")
	cohortId := ctx.Request.URL.Query().Get("cohortId")

	resp, err := c.s.ExportRandomizationSimulation(ctx, customerId, projectId, envId, cohortId)
	tools.Response(ctx, err, resp)
}

// ExportConfigurePdf 配置报告pdf数据
func (c *ReportController) ExportConfigurePdf(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	reportType, _ := parameter["type"].(float64)
	projectId, _ := parameter["projectId"].(string)
	envId, _ := parameter["envId"].(string)
	templateId, _ := parameter["templateId"].(string)
	cohortID, _ := parameter["cohortId"].(string)
	roleId, _ := parameter["roleId"].(string)
	startTime, _ := parameter["startTime"].(float64)
	endTime, _ := parameter["endTime"].(float64)
	exportTypes, _ := parameter["exportTypes"].(string)
	cipher, _ := parameter["cipher"].(string)
	simulateRandomId, _ := parameter["simulateRandomId"].(string)
	//resp, err := c.s.ExportConfigurePdf(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId)
	//tools.Response(ctx, err, resp)
	// 后端生产pdf并返回
	err := c.s.ExportConfigurePdfNew(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId)
	tools.Response(ctx, err)
}

func (c *ReportController) ExportConfigureReport(ctx *gin.Context) {

	var reportType float64
	if len(ctx.PostForm("type")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("type"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		reportType = f
	}
	projectId := ctx.PostForm("projectId")
	envId := ctx.PostForm("envId")
	templateId := ctx.PostForm("templateId")
	cohortID := ctx.PostForm("cohortId")
	roleId := ctx.PostForm("roleId")
	var startTime float64
	if len(ctx.PostForm("startTime")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("startTime"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		startTime = f
	}
	var endTime float64
	if len(ctx.PostForm("endTime")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("endTime"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		startTime = f
	}
	exportTypes := ctx.PostForm("exportTypes")
	cipher := ctx.PostForm("cipher")
	simulateRandomId := ctx.PostForm("simulateRandomId")

	ctx.PostForm("type")

	// 获取文件头
	fileHeader, e := ctx.FormFile("file")
	if e != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get file"})
		return
	}

	// 打开文件
	file, e := fileHeader.Open()
	if e != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to open file"})
		return
	}
	defer file.Close()

	// 读取文件数据
	fileData, e := io.ReadAll(file)
	if e != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
		return
	}

	pdfFile := fileData

	err := c.s.ExportConfigureReport(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId, pdfFile)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// ExportSubjectPdf 受试者pdf数据 todo !!!由于nginx 设置300s 超时，经观察，当受试者超过约500 条时，将无法返回，如遇到问题，可分批导出
func (c *ReportController) ExportSubjectPdf(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	reportType, _ := parameter["type"].(float64)
	projectId, _ := parameter["projectId"].(string)
	envId, _ := parameter["envId"].(string)
	templateId, _ := parameter["templateId"].(string)
	cohortID, _ := parameter["cohortId"].(string)
	roleId, _ := parameter["roleId"].(string)
	startTime, _ := parameter["startTime"].(float64)
	endTime, _ := parameter["endTime"].(float64)
	exportTypes, _ := parameter["exportTypes"].(string)
	cipher, _ := parameter["cipher"].(string)
	simulateRandomId, _ := parameter["simulateRandomId"].(string)
	subjects := parameter["subject"].([]interface{})
	// 返回数据，前端生成pdf
	//resp, err := c.s.ExportSubjectPdf(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId, subjects)
	//tools.Response(ctx, err, resp)
	// 后端生产pdf并返回
	exportFormat, _ := parameter["exportFormat"].(float64)
	err := c.s.ExportSubjectPdfNew(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, int(exportFormat), cipher, simulateRandomId, subjects)
	tools.Response(ctx, err)
}

func (c *ReportController) ExportSubjectReport(ctx *gin.Context) {

	var reportType float64
	if len(ctx.PostForm("type")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("type"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		reportType = f
	}
	projectId := ctx.PostForm("projectId")
	envId := ctx.PostForm("envId")
	templateId := ctx.PostForm("templateId")
	cohortID := ctx.PostForm("cohortId")
	roleId := ctx.PostForm("roleId")
	var startTime float64
	if len(ctx.PostForm("startTime")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("startTime"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		startTime = f
	}
	var endTime float64
	if len(ctx.PostForm("endTime")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("endTime"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		endTime = f
	}
	exportTypes := ctx.PostForm("exportTypes")
	cipher := ctx.PostForm("cipher")
	simulateRandomId := ctx.PostForm("simulateRandomId")

	var exportFormat int
	if len(ctx.PostForm("exportFormat")) != 0 {
		// 将字符串转换为 int
		f, err := strconv.Atoi(ctx.PostForm("exportFormat"))
		if err != nil {
			fmt.Println("Error converting string to int:", err)
			return
		}
		exportFormat = f
	}

	subject := ctx.PostForm("subject")

	err := c.s.ExportSubjectReport(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId, exportFormat, subject)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// ExportSimulateRandomPdf 模拟随机pdf数据
func (c *ReportController) ExportSimulateRandomPdf(ctx *gin.Context) {
	// 传入参数
	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	reportType, _ := parameter["type"].(float64)
	projectId, _ := parameter["projectId"].(string)
	envId, _ := parameter["envId"].(string)
	templateId, _ := parameter["templateId"].(string)
	cohortID, _ := parameter["cohortId"].(string)
	roleId, _ := parameter["roleId"].(string)
	startTime, _ := parameter["startTime"].(float64)
	endTime, _ := parameter["endTime"].(float64)
	exportTypes, _ := parameter["exportTypes"].(string)
	cipher, _ := parameter["cipher"].(string)
	simulateRandomId, _ := parameter["simulateRandomId"].(string)
	//resp, err := c.s.ExportSimulateRandomPdf(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId)
	//tools.Response(ctx, err, resp)
	// 后端生产pdf并返回
	err := c.s.ExportSimulateRandomPdfNew(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId)
	tools.Response(ctx, err)
}

func (c *ReportController) ExportSimulateRandomReport(ctx *gin.Context) {

	var reportType float64
	if len(ctx.PostForm("type")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("type"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		reportType = f
	}
	projectId := ctx.PostForm("projectId")
	envId := ctx.PostForm("envId")
	templateId := ctx.PostForm("templateId")
	cohortID := ctx.PostForm("cohortId")
	roleId := ctx.PostForm("roleId")
	var startTime float64
	if len(ctx.PostForm("startTime")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("startTime"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		startTime = f
	}
	var endTime float64
	if len(ctx.PostForm("endTime")) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(ctx.PostForm("endTime"), 64)
		if err != nil {
			fmt.Println("Error converting string to float64:", err)
			return
		}
		startTime = f
	}
	exportTypes := ctx.PostForm("exportTypes")
	cipher := ctx.PostForm("cipher")
	simulateRandomId := ctx.PostForm("simulateRandomId")

	ctx.PostForm("type")

	// 获取文件头
	fileHeader, e := ctx.FormFile("file")
	if e != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get file"})
		return
	}

	// 打开文件
	file, e := fileHeader.Open()
	if e != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to open file"})
		return
	}
	defer file.Close()

	// 读取文件数据
	fileData, e := io.ReadAll(file)
	if e != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read file data"})
		return
	}

	pdfFile := fileData

	err := c.s.ExportSimulateRandomReport(ctx, int(reportType), projectId, envId, templateId, cohortID, roleId, int(startTime), int(endTime), exportTypes, cipher, simulateRandomId, pdfFile)
	if err != nil {
		tools.Response(ctx, err)
	}
}
