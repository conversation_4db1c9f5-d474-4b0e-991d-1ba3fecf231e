package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// MedicineController struct
type MedicineController struct {
	s service.MedicineService
}

// GetMedicineList 查询研究产品列表
// Method:	GET
func (c *MedicineController) GetMedicineList(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	attributeId := ctx.Query("attributeId")
	roleId := ctx.Query("roleId")
	projectId := ctx.Query("projectId")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.GetMedicineList(ctx, customerID, projectId, envID, start, limit, attributeId, roleId)
	tools.Response(ctx, err, data)
}

// GetMedicineFreezeList 查询研究产品隔离列表
// Method:	GET
func (c *MedicineController) GetMedicineFreezeList(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	cohortID := ctx.Query("cohortId")
	roleID := ctx.Query("roleId")
	freezeInstituteId := ctx.Query("freezeInstituteId")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.GetMedicineFreezeList(ctx, customerID, projectID, envID, cohortID, roleID, freezeInstituteId, start, limit)
	tools.Response(ctx, err, data)
}

// GetMedicineFreezeDetailsList 查询隔离订单研究产品详情
// Method:	GET
func (c *MedicineController) GetMedicineFreezeDetailsList(ctx *gin.Context) {
	id := ctx.Query("id")
	attributeId := ctx.Query("attributeId")
	roleId := ctx.Query("roleId")
	ipNumber := ctx.Query("ipNumber")
	cohortId := ctx.Query("cohortId")
	upperNumber := ctx.Query("upperNumber")
	lowerNumber := ctx.Query("lowerNumber")
	status, _ := strconv.Atoi(ctx.DefaultQuery("status", "0"))
	// start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	// limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	sign, _ := strconv.Atoi(ctx.DefaultQuery("sign", "1"))
	data, err := c.s.GetMedicineFreezeDetailsList(ctx, cohortId, id, ipNumber, upperNumber, lowerNumber, attributeId, roleId, status, sign)
	tools.Response(ctx, err, data)
}

// UploadMedicines 上传研究产品
// Method:	POST
func (c *MedicineController) UploadMedicines(ctx *gin.Context) {
	err := c.s.UploadMedicines(ctx)
	tools.Response(ctx, err)
}

// UploadMedicinesPacklist 上传研究产品包装清单
// Method:	POST
func (c *MedicineController) UploadMedicinesPacklist(ctx *gin.Context) {
	err := c.s.UploadMedicinesPacklist(ctx)
	tools.Response(ctx, err)
}

// DownloadData 研究产品列表数据下载
// Method:	GET
func (c *MedicineController) DownloadData(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	attributeId := ctx.Query("attributeId")
	roleId := ctx.Query("roleId")
	err := c.s.DownloadData(ctx, customerID, projectID, envID, attributeId, roleId)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// GetTemplateFile 研究产品上传模板下载
// Method:	GET
func (c *MedicineController) GetTemplateFile(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	err := c.s.GetTemplateFile(ctx, customerID, envID)
	if err != nil {
		tools.Response(ctx, err)
	}

}

// GetPacklistTemplateFile 研究产品包装清单模板下载
// Method:	GET
func (c *MedicineController) GetPacklistTemplateFile(ctx *gin.Context) {
	err := c.s.GetPacklistTemplateFile(ctx)
	tools.Response(ctx, err)
}

// GetBatchList 查询批次列表
// Method:	GET
func (c *MedicineController) GetBatchList(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	roleId := ctx.Query("roleId")
	queryValue := ctx.Query("queryValue")
	queryTypes := ctx.Query("queryTypes")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.GetBatchList(ctx, customerID, envID, roleId, queryValue, queryTypes, start, limit)
	tools.Response(ctx, err, data)
}

func (c *MedicineController) GetBatchGroupStatus(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	roleId := ctx.Query("roleId")
	data, err := c.s.GetBatchGroupStatus(ctx, customerID, envID, roleId)
	tools.Response(ctx, err, data)
}

func (c *MedicineController) GetBatchGroupStatusMult(ctx *gin.Context) {
	var batchManagement models.BatchManagementRequst
	_ = ctx.ShouldBindBodyWith(&batchManagement, binding.JSON)
	data, err := c.s.GetBatchGroupStatusMult(ctx, batchManagement)
	tools.Response(ctx, err, data)
}

// UpdateBatch 批次管理更新
// Method:	PATCH
func (c *MedicineController) UpdateBatch(ctx *gin.Context) {
	var batchInfo models.BatchInfo
	_ = ctx.ShouldBindBodyWith(&batchInfo, binding.JSON)
	err := c.s.UpdateBatch(ctx, batchInfo)
	tools.Response(ctx, err)
}

// FreezeMedicines 隔离研究产品
// Method:	PATCH
func (c *MedicineController) FreezeMedicines(ctx *gin.Context) {
	var updateMedicines models.UpdateSiteMedicines
	_ = ctx.ShouldBindBodyWith(&updateMedicines, binding.JSON)
	err := c.s.FreezeMedicines(ctx, updateMedicines)
	tools.Response(ctx, err)
}

// FreezeOtherMedicines 未编码隔离研究产品
// Method:	PATCH
func (c *MedicineController) FreezeOtherMedicines(ctx *gin.Context) {
	var otherMedicineFreeze models.OtherMedicineFreeze
	_ = ctx.ShouldBindBodyWith(&otherMedicineFreeze, binding.JSON)
	err := c.s.FreezeOtherMedicines(ctx, otherMedicineFreeze)
	tools.Response(ctx, err)
}

// UpdateOtherMedicines 更新未编码研究产品
func (c *MedicineController) UpdateOtherMedicine(ctx *gin.Context) {
	var otherMedicineFreeze models.OtherMedicineFreeze
	_ = ctx.ShouldBindBodyWith(&otherMedicineFreeze, binding.JSON)
	err := c.s.UpdateOtherMedicines(ctx, otherMedicineFreeze)
	tools.Response(ctx, err)
}

// GetOtherMedicineCount 查询未编号研究产品数量
// Method:	Get
func (c *MedicineController) GetOtherMedicineCount(ctx *gin.Context) {
	data, err := c.s.GetOtherMedicineCount(ctx)
	tools.Response(ctx, err, data)
}

// UpdateMedicines 作废/丢失研究产品、设为可用
// Method:	PATCH
func (c *MedicineController) UpdateMedicines(ctx *gin.Context) {
	var updateMedicines models.UpdateMedicines
	_ = ctx.ShouldBindBodyWith(&updateMedicines, binding.JSON)
	err := c.s.UpdateMedicines(ctx, updateMedicines)
	tools.Response(ctx, err)
}

// ReleaseMedicines 研究产品解隔离
// Method:	PATCH
func (c *MedicineController) ReleaseMedicines(ctx *gin.Context) {
	var updateMedicines models.UpdateMedicines
	_ = ctx.ShouldBindBodyWith(&updateMedicines, binding.JSON)
	err := c.s.ReleaseMedicines(ctx, updateMedicines)
	tools.Response(ctx, err)
}

// GetSummaryStorehouse 研究产品概述
// Method:	POST
func (c *MedicineController) GetSummaryStorehouse(ctx *gin.Context) {
	data, err := c.s.GetSummaryStorehouse(ctx)
	tools.Response(ctx, err, data)
}

// GetMedicineStorehouseSku 研究产品单品
// Method:	GET
func (c *MedicineController) GetMedicineStorehouseSku(ctx *gin.Context) {
	req := models.MedicineSkuReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	data, err := c.s.GetMedicineStorehouseSku(ctx, req)
	tools.Response(ctx, err, data)
}

// GetMedicineSiteSku 中心药房
// Method:	GET
func (c *MedicineController) GetMedicineSiteSku(ctx *gin.Context) {
	req := models.MedicineSkuReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	// customerID := ctx.Query("customerId")
	// projectID := ctx.Query("projectId")
	// envID := ctx.Query("envId")
	// roleID := ctx.Query("roleId")
	// start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	// limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.GetMedicineSiteSku(ctx, req)
	tools.Response(ctx, err, data)
}

// GetMedicineDtpSku DTP研究产品
// Method:	GET
func (c *MedicineController) GetMedicineDtpSku(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	role, err := tools.GetRole(roleID)
	if err != nil {
		tools.Response(ctx, err)
		return
	}
	if role.Scope == "depot" {
		tools.Response(ctx, nil, map[string]interface{}{"total": 0, "items": make([]interface{}, 0)})
		return
	}
	data, err := c.s.GetMedicineDtpSku(ctx, customerID, projectID, envID, roleID, start, limit)
	tools.Response(ctx, err, data)
}

// GetOtherMedicineSiteSku 中心药房(未编号研究产品)
// Method:	GET
func (c *MedicineController) GetOtherMedicineSiteSku(ctx *gin.Context) {
	req := models.MedicineSkuReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	// customerID := ctx.Query("customerId")
	// projectID := ctx.Query("projectId")
	// envID := ctx.Query("envId")
	// roleID := ctx.Query("roleId")
	// start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	// limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.GetOtherMedicineSiteSku(ctx, req)
	tools.Response(ctx, err, data)
}

// GetOtherMedicineStorehouseSku 库房(未编号研究产品)
// Method:	GET
func (c *MedicineController) GetOtherMedicineStorehouseSku(ctx *gin.Context) {
	req := models.MedicineSkuReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	// customerID := ctx.Query("customerId")
	// projectID := ctx.Query("projectId")
	// envID := ctx.Query("envId")
	// roleID := ctx.Query("roleId")
	// start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	// limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	data, err := c.s.GetOtherMedicineStorehouseSku(ctx, req)
	tools.Response(ctx, err, data)
}

// GetAllMedicineStorehouseSTAT 项目概览——库房单品统计(编号和未编号研究产品)
// Method:	GET
func (c *MedicineController) GetAllMedicineStorehouseSTAT(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	data, err := c.s.GetAllMedicineStorehouseSTAT(ctx, customerID, projectID, envID, roleID)
	tools.Response(ctx, err, data)
}

// GetAllMedicineSiteSTAT 项目概览——中心单品统计(编号和未编号研究产品)
// Method:	GET
func (c *MedicineController) GetAllMedicineSiteSTAT(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	data, err := c.s.GetAllMedicineSiteSTAT(ctx, customerID, projectID, envID, roleID)
	tools.Response(ctx, err, data)
}

// GetSummarySite 中心药房统计
// Method:	POST
func (c *MedicineController) GetSummarySite(ctx *gin.Context) {
	data, err := c.s.GetSummarySite(ctx)
	tools.Response(ctx, err, data)

}

// GetSiteMedicineList 中心管理, 获取所有中心的药的(包括运送中)
// Method:	GET
func (c *MedicineController) GetSiteMedicineList(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	data, err := c.s.GetSiteMedicineList(ctx, customerID, projectID, envID)
	tools.Response(ctx, err, data)

}

// DownloadStorehouseSku
// Method:	GET
func (c *MedicineController) DownloadStorehouseSku(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	err := c.s.DownloadStorehouseSku(ctx, customerID, projectID, envID, roleID)
	if err != nil {
		tools.Response(ctx, err)
	}

}

// DownloadSiteSku
// Method:	GET
func (c *MedicineController) DownloadSiteSku(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	err := c.s.DownloadSiteSku(ctx, customerID, projectID, envID, roleID)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// DownloadDtpSku
// Method:	GET
func (c *MedicineController) DownloadDtpSku(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")
	err := c.s.DownloadDtpSku(ctx, customerID, projectID, envID, roleID)
	if err != nil {
		tools.Response(ctx, err)
	}

}

// DeleteData
// Method:	DeleteData
// Path:	/customers/{id: string}/envs/{envId}
func (c *MedicineController) DeleteData(ctx *gin.Context) {
	var deleteData models.DeleteData
	_ = ctx.ShouldBindBodyWith(&deleteData, binding.JSON)
	err := c.s.DeleteData(ctx, deleteData)
	tools.Response(ctx, err)
}

// GetOtherMedicineSiteSkuWithDTP
// Method:	GET
func (c *MedicineController) GetOtherMedicineSiteSkuWithDTP(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	subject := ctx.DefaultQuery("subject", "")
	roleID := ctx.Query("roleId")
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	role, err := tools.GetRole(roleID)
	if err != nil {
		tools.Response(ctx, err)
		return
	}
	if role.Scope == "depot" {
		tools.Response(ctx, nil, map[string]interface{}{"total": 0, "items": make([]interface{}, 0)})
		return
	}
	data, err := c.s.GetOtherMedicineSiteSkuWithDTP(ctx, customerID, projectID, envID, subject, roleID, start, limit)
	tools.Response(ctx, err, data)
}

// GetMedicineForOrderGroup 研究产品回收的研究产品分组信息
// Method:	GET
func (c *MedicineController) GetMedicineForOrderGroup(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	envID := ctx.Query("envId")
	roleId := ctx.Query("roleId")
	cohortID := ctx.Query("cohortId")
	data, err := c.s.GetMedicineForOrderGroup(ctx, customerID, envID, cohortID, roleId)
	tools.Response(ctx, err, data)
}

// GetMedicineBatchList 查询批次列表(研究产品订单创建页面数据)
// Method:	GET
func (c *MedicineController) GetMedicineBatchList(ctx *gin.Context) {
	var orderInfo models.QueryOrderInfo
	_ = ctx.ShouldBindBodyWith(&orderInfo, binding.JSON)
	data, err := c.s.GetMedicineBatchList(ctx, orderInfo)
	tools.Response(ctx, err, data)
}

// GetMedicineForOrder 研究产品回收的研究产品信息
// Method:	GET
func (c *MedicineController) GetMedicineForOrder(ctx *gin.Context) {
	var recoveryOrderGroupData models.RecoveryOrderGroupData
	_ = ctx.ShouldBindBodyWith(&recoveryOrderGroupData, binding.JSON)
	data, err := c.s.GetMedicineForOrder(ctx, recoveryOrderGroupData)
	tools.Response(ctx, err, data)
}

// GetMedicineForOrderGroupDetail 研究产品回收的研究产品详细信息
// Method:	GET
func (c *MedicineController) GetMedicineForOrderGroupAll(ctx *gin.Context) {
	var recoveryOrderGroupData models.RecoveryOrderGroupData
	_ = ctx.ShouldBindBodyWith(&recoveryOrderGroupData, binding.JSON)
	data, err := c.s.GetMedicineForOrderAll(ctx, recoveryOrderGroupData)
	tools.Response(ctx, err, data)
}

// GetMedicineFreezeByIds 包装开启时,根据ids，查询需要一起隔离的研究产品，
// Method:	GET
func (c *MedicineController) GetMedicineFreezeByIds(ctx *gin.Context) {
	var updateMedicines models.UpdateMedicines
	_ = ctx.ShouldBindBodyWith(&updateMedicines, binding.JSON)
	data, err := c.s.GetMedicineFreezeByIds(ctx, updateMedicines)
	tools.Response(ctx, err, data)
}

// SiteForecast 库存预测
// Method:	GET
func (c *MedicineController) SiteForecast(ctx *gin.Context) {
	envID := ctx.Query("envId")
	roleID := ctx.Query("roleId")

	data, err := c.s.SiteForecast(ctx, envID, roleID)
	tools.Response(ctx, err, data)
}

// 根据研究产品名称、有效期和批次号获取数据
func (c *MedicineController) GetGroupNameDataNumber(ctx *gin.Context) {
	status := ctx.Query("status")
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")

	data, err := c.s.GetGroupNameDataNumber(ctx, status, customerID, projectID, envID)
	tools.Response(ctx, err, data)
}

// 审核/修改/放行流程
func (c *MedicineController) ToExamineFlowPath(ctx *gin.Context) {
	var toExamineFlowPathParameter models.ToExamineFlowPathParameter
	_ = ctx.ShouldBindBodyWith(&toExamineFlowPathParameter, binding.JSON)
	err := c.s.ToExamineFlowPath(ctx, toExamineFlowPathParameter)
	tools.Response(ctx, err)
}

// 查询自动编码已编号药物的药物名称
func (c *MedicineController) GetMedicineName(ctx *gin.Context) {
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	data, err := c.s.GetMedicineName(ctx, projectID, envID)
	tools.Response(ctx, err, data)
}

// GetBatchSelectList 查询批次管理选中研究产品(不包含未编号)
// Method:	GET
func (c *MedicineController) GetBatchSelectList(ctx *gin.Context) {
	var selectMedicines models.SelectMedicines
	_ = ctx.ShouldBindBodyWith(&selectMedicines, binding.JSON)
	data, err := c.s.GetBatchSelectList(ctx, selectMedicines)
	tools.Response(ctx, err, data)
}
