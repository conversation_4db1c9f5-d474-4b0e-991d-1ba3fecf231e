package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MedicineOrder 研究产品订单
type MedicineOrder struct {
	ID                  primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID          primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID           primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID       primitive.ObjectID `json:"envId" bson:"env_id"`
	OrderNumber         string             `json:"orderNumber" bson:"order_number"`
	SendID              primitive.ObjectID `json:"sendId" bson:"send_id"`
	ReceiveID           primitive.ObjectID `json:"receiveId" bson:"receive_id"`
	ExpectedArrivalTime string             `json:"expectedArrivalTime" bson:"expected_arrival_time"` //期望接收时间
	ActualReceiptTime   string             `json:"actualReceiptTime" bson:"actual_receipt_time"`     //实际接收时间
	MedicinesPackage    []MedicinePackage  `json:"medicinesPackage" bson:"medicines_package"`        //研究产品名称运输方式
	// CancellerBy      primitive.ObjectID   `json:"cancellerBy" bson:"canceller_by"`
	// CancellerAt      time.Duration        `json:"cancellerAt" bson:"canceller_at"`
	// ConfirmBy        primitive.ObjectID   `json:"confirmBy" bson:"confirm_by"`
	ConfirmAt time.Duration `json:"confirmAt" bson:"confirm_at"`
	// SendBy           primitive.ObjectID   `json:"sendBy" bson:"send_by"`
	// SendAt           time.Duration        `json:"sendAt" bson:"send_at"`
	// ReceiveBy        primitive.ObjectID   `json:"receiveBy" bson:"receive_by"`
	// ReceiveAt        time.Duration        `json:"receiveAt" bson:"receive_at"`
	// CloseBy          primitive.ObjectID   `json:"colseBy" bson:"close_by"`
	// CloseAt          time.Duration        `json:"colseAt" bson:"close_at"`
	// EndBy            primitive.ObjectID   `json:"endBy" bson:"end_by"`
	// EndAt            time.Duration        `json:"endAt" bson:"end_at"`
	// LostBy           primitive.ObjectID   `json:"lostBy" bson:"lost_by"`
	// LostAt           time.Duration        `json:"lostAt" bson:"lost_at"`
	Status                   int                  `json:"status" bson:"status"` //1:已确认、2:已运送、3:已接收、4:已丢失、5:已取消 6:待确认 7:已申请  8:已终止 9:已关闭
	Mode                     int                  `json:"mode" bson:"mode"`     // 1:发药属性 2：再供应量 3：最大缓冲量 4：首次订单
	Medicines                []primitive.ObjectID `json:"medicines" bson:"medicines"`
	MedicinesHistory         []MedicinesHistory   `json:"medicinesHistory" bson:"medicines_history"`
	OtherMedicines           []OtherMedicineCount `json:"otherMedicines" bson:"other_medicines"`
	OtherMedicinesNew        []primitive.ObjectID `json:"otherMedicinesNew" bson:"other_medicines_new"`                //未编号重构之后字段
	OtherMedicinesHistoryNew []primitive.ObjectID `json:"otherMedicinesHistoryNew" bson:"other_medicines_history_new"` //未编号重构之后字段
	OtherHistoryNew          []MedicinesHistory   `json:"otherHistory" bson:"other_history"`                           //订单记录未编号俩是状态
	Type                     int                  `json:"type" bson:"type"`                                            //1:仓库->中心、2:中心->中心、3:仓库->仓库、4:中心->仓库    5 仓库 ->受试者    6 中心-> 受试者
	SubjectID                primitive.ObjectID   `json:"subjectId" bson:"subject_id"`
	DispensingID             primitive.ObjectID   `json:"dispensingId" bson:"dispensing_id"`
	TaskID                   primitive.ObjectID   `json:"taskId" bson:"task_id"`
	App                      int                  `json:"app" bson:"app"` //创建方式：0:web  1：app
	LastReminderDate         time.Duration        `bson:"last_reminder_date"`
	ShipmentInfo             `json:"shipmentInfo" bson:"shipment_info"`
	Contacts                 string `json:"contacts"`                    // 联系人
	Phone                    string `json:"phone"`                       // 联系方式
	Email                    string `json:"email"`                       // 邮箱
	Address                  string `json:"address"`                     // 地址
	SortIndex                int    `json:"sortIndex" bson:"sort_index"` //排序 1待确认、5已运送、10已确认、15已申请、20已接收、25已丢失、30已取消、35已终止、40已关闭。筛选中已超时，为不同订单状态中超时订单的筛选。
	Meta                     `json:"meta"`
	LogisticsInfo            `json:"logisticsInfo" bson:"logistics_info"` //物流信息
	Frozen                   bool                                         `json:"frozen" bson:"frozen"` //物流信息
}

type MedicinePackage struct {
	Name          string `json:"name"`                                //药物名称
	PackageMethod bool   `json:"packageMethod" bson:"package_method"` //包装运输:true,单品运输：false
	PackageNumber int    `json:"packageNumber" bson:"package_number"` //整包包装数量
}

type StatisticsMedicinePackage struct {
	Number string `json:"number" bson:"number"` //包装号
	Count  int    `json:"count" bson:"count"`   //整包包装数量
}

// ShipmentInfo ..
type ShipmentInfo struct {
	Logistics int    `json:"logistics" bson:"logistics"` // 物流供应商  1、顺丰 2、EMS 3、京东 4、圆通 5、韵达 6、中通 7、申通 8、极兔 9、其他
	Other     string `json:"other" bson:"other"`         // 其他物流
	Number    string `json:"number" bson:"number"`       // 物流单号
}

type LogisticsInfo struct {
	Logistics string `json:"logistics" bson:"logistics"` // 物流供应商编号
	Other     string `json:"other" bson:"other"`         // 其他物流
	Number    string `json:"number" bson:"number"`       // 物流单号
}

// MedicinesHistory ..
type MedicinesHistory struct {
	ID     primitive.ObjectID `json:"id" bson:"id"`
	Status int                `json:"status"`
}

// MedicineCount ..
type MedicineCount struct {
	Name           string `json:"name"`
	Count          int    `json:"count"`
	ExpirationDate string `json:"expirationDate"`
	BatchNumber    string `json:"batchNumber"`
	PackageMethod  bool   `json:"package_method"`
	Salt           string `json:"salt"`
	SaltName       string `json:"saltName"`
}

type OtherMedicineMap struct {
	Name       string `json:"name" bson:"name"`
	ExpireDate string `json:"expireDate" bson:"expire_date"`
	Batch      string `json:"batch" bson:"batch"`
}

// OtherMedicineCount ..
type OtherMedicineCount struct {
	ID           primitive.ObjectID `json:"id" bson:"id"`
	Name         string             `json:"name" bson:"name"`
	Count        int                `json:"count" bson:"count"`
	UseCount     int                `json:"useCount" bson:"use_count"`
	ReceiveCount int                `json:"receiveCount" bson:"receive_count"`
	ExpireDate   string             `json:"expireDate" bson:"expire_date"`
	Batch        string             `json:"batch" bson:"batch"`
}

// OtherMedicineCountWithSalt ..
type OtherMedicineCountWithSalt struct {
	ID             primitive.ObjectID `json:"id" bson:"id"`
	Name           string             `json:"name" bson:"name"`
	AvailableCount int                `json:"availableCount" bson:"availableCount"`
	Count          int                `json:"count" bson:"count"`
	UseCount       int                `json:"useCount" bson:"use_count"`
	ReceiveCount   int                `json:"receiveCount" bson:"receive_count"`
	ExpireDate     string             `json:"expirationDate" bson:"expiration_date"`
	PackageMethod  bool               `json:"packageMethod" bson:"package_method"`
	Batch          string             `json:"batchNumber" bson:"batch_number"`
	Salt           string             `json:"salt" bson:"salt"`
	SaltName       string             `json:"saltName" bson:"saltName"`
	Status         int                `json:"status" bson:"status"`
}

// ReceiveOtherMedicineCount ..
type ReceiveOtherMedicineCount struct {
	ID            primitive.ObjectID `json:"id" bson:"id"`
	Name          string             `json:"name" bson:"name"`
	UseCount      int                `json:"use_count" bson:"use_count" `
	PackageMethod bool               `json:"package_method" bson:"package_method" `
	ReceiveCount  int                `json:"receive_count" bson:"receive_count"`
	ExpireDate    string             `json:"expire_date" bson:"expiration_date"`
	Batch         string             `json:"batch" bson:"batch_number"`
	Salt          string             `json:"salt"`
	SaltName      string             `json:"saltName"`
	Status        int                `json:"status"`
}

// OrderInfo ..
type OrderInfo struct {
	CustomerID          primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID           primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvID               primitive.ObjectID   `json:"envId" bson:"env_id"`
	CohortID            primitive.ObjectID   `json:"cohortId"`
	SendID              primitive.ObjectID   `json:"sendId"`
	ReceiveID           primitive.ObjectID   `json:"receiveId"`
	Mode                int                  `json:"mode"`
	DrugNames           []string             `json:"drugNames"`
	DrugNamesWithSalts  []DrugNamesWithSalt  `json:"drugNamesWithSalts"`
	MedicinesCount      []MedicineCount      `json:"medicinesCount"`
	OtherMedicinesCount []OtherMedicineCount `json:"otherMedicinesCount"`
	MedicineIds         []primitive.ObjectID `json:"medicineIds"`
	SupplyID            primitive.ObjectID   `json:"supplyId"`
}

// AddOrderInfo ..
type AddOrderInfo struct {
	CustomerID          primitive.ObjectID           `json:"customerId" bson:"customer_id"`
	ProjectID           primitive.ObjectID           `json:"projectId" bson:"project_id"`
	EnvID               primitive.ObjectID           `json:"envId" bson:"env_id"`
	CohortID            primitive.ObjectID           `json:"cohortId"`
	SendID              primitive.ObjectID           `json:"sendId"`
	ReceiveID           primitive.ObjectID           `json:"receiveId"`
	Mode                int                          `json:"mode"` //1:发药属性 2：再供应量 3：最大缓冲量 4：首次订单
	ExpectedArrivalTime string                       `json:"expectedArrivalTime" bson:"expected_arrival_time"`
	DrugNames           []string                     `json:"drugNames"`
	DrugNamesWithSalts  []DrugNamesWithSalt          `json:"drugNamesWithSalts"`
	MedicinesCount      []MedicineCount              `json:"medicinesCount"`
	OtherMedicinesCount []OtherMedicineCountWithSalt `json:"otherMedicinesCount"`
	MedicineIds         []primitive.ObjectID         `json:"medicineIds"`
	SupplyID            primitive.ObjectID           `json:"supplyId"`
	SupplyCount         int                          `json:"supplyCount"`
	RoleID              string                       `json:"roleId"`
	Contacts            string                       `json:"contacts"`
	BlindCount          int                          `json:"blindCount" bson:"blind_count"`        //供应单品总数（仅盲态研究产品）
	OpenDrugCount       []OrderDrugNames             `json:"openDrugCount" bson:"open_drug_count"` //开放药物
	App                 int                          `json:"app"`                                  //创建方式：0:web  1：app
	IntervalStart       string                       `json:"intervalStart" bson:"interval_start"`
	IntervalEnd         string                       `json:"intervalEnd" bson:"interval_end"`
}

type QueryOrderInfo struct {
	CustomerID         primitive.ObjectID  `json:"customerId"`
	ProjectID          primitive.ObjectID  `json:"projectId"`
	EnvID              primitive.ObjectID  `json:"envId"`
	CohortID           primitive.ObjectID  `json:"cohortId"`
	SendID             primitive.ObjectID  `json:"sendId"`
	ReceiveID          primitive.ObjectID  `json:"receiveId"`
	Mode               int                 `json:"mode"`
	DrugNames          []string            `json:"drugNames"`
	DrugNamesWithSalts []DrugNamesWithSalt `json:"drugNamesWithSalts"`
	SupplyPlanID       primitive.ObjectID  `json:"supplyPlanId"`
	RoleID             string              `json:"roleId"`
	OrderType          int                 `json:"orderType"`
	IntervalStart      string              `json:"intervalStart"`
	IntervalEnd        string              `json:"intervalEnd"`
}

// DrugNamesWithSalt ..
type DrugNamesWithSalt struct {
	Salt     string `json:"salt"`
	SaltName string `json:"saltName"`
}

type ChangeOrderMedicines struct {
	ID                   primitive.ObjectID    `json:"id"`
	UpdateMedicines      []ChangeMedicine      `json:"updateMedicines"`
	UpdateOtherMedicines []ChangeOtherMedicine `json:"updateOtherMedicines"`
	RoleID               string                `json:"roleId"`
	Reason               string                `json:"reason"`
}

type ChangeHistoryRecord struct {
	ID                primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID        primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID         primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID     primitive.ObjectID `json:"envId" bson:"env_id"`
	OrderID           primitive.ObjectID `json:"orderId" bson:"order_id"`
	OldMedicineID     primitive.ObjectID `json:"oldMedicineId" bson:"old_medicine_id"`
	OldName           string             `json:"oldName" bson:"old_name"`
	OldExpirationDate string             `json:"oldExpirationDate" bson:"old_expiration_date"`
	OldBatchNumber    string             `json:"oldBatchNumber" bson:"old_batch_number"`
	OldSpec           string             `json:"oldSpec" bson:"old_spec"`
	OldCount          int                `json:"oldCount" bson:"old_count"`
	NewMedicineID     primitive.ObjectID `json:"newMedicineId" bson:"new_medicine_id"`
	NewName           string             `json:"newName" bson:"new_name"`
	NewExpirationDate string             `json:"newExpirationDate" bson:"new_expiration_date"`
	NewBatchNumber    string             `json:"newBatchNumber" bson:"new_batch_number"`
	NewSpec           string             `json:"newSpec" bson:"new_spec"`
	NewCount          int                `json:"newCount" bson:"new_count"`
	Meta              `json:"meta"`
}

type ChangeMedicine struct {
	ID             primitive.ObjectID `json:"_id"`
	Name           string             `json:"name" `
	Number         string             `json:"number" `
	StorehouseID   primitive.ObjectID `json:"storehouse_id"`
	SiteID         primitive.ObjectID `json:"site_id" `
	ExpirationDate string             `json:"expiration_date" `
	BatchNumber    string             `json:"batch_number" `
	Spec           string             `json:"spce"`
	PackageNumber  string             `json:"package_number" ` //包装号
}

// ChangeOtherMedicine ..
type ChangeOtherMedicine struct {
	ID            primitive.ObjectID `json:"id" bson:"id"`
	Name          string             `json:"name" bson:"name"`
	UseCount      int                `json:"useCount" bson:"use_count"`
	ChangeCount   int                `json:"change_count" bson:"change_count"`
	ExpireDate    string             `json:"expire_date" bson:"expire_date"`
	Batch         string             `json:"batch" bson:"batch"`
	PackageMethod bool               `json:"package_method"`
}

// UpdateOrderInfo ..
type UpdateOrderInfo struct {
	ID                  primitive.ObjectID   `json:"id"`
	Status              int                  `json:"status"`
	Reason              string               `json:"reason"`
	App                 bool                 `json:"app"`
	WorkTaskId          primitive.ObjectID   `json:"workTaskId"`
	Medicines           []primitive.ObjectID `json:"medicines"`
	ExpectedArrivalTime string               `json:"expectedArrivalTime"`
	ShipmentInfo        ShipmentInfo         `json:"shipmentInfo"`
	Carrier             string               `json:"carrier"`       //承运人
	LOGOrderNo          string               `json:"LOGOrderNo"`    //物流单号
	LogisticsInfo       LogisticsInfo        `json:"logisticsInfo"` //物流信息
	RoleID              string               `json:"roleId"`
	ActualReceiptTime   string               `json:"actualReceiptTime" bson:"actual_receipt_time"` //实际接收时间
}

// RecevieOrder ..
type RecevieOrder struct {
	ID                  primitive.ObjectID          `json:"id"`
	Medicines           []primitive.ObjectID        `json:"medicines" bson:"medicines"`
	OtherMedicines      []ReceiveOtherMedicineCount `json:"otherMedicines" bson:"other_medicines"`
	ExpectedArrivalTime string                      `json:"expectedArrivalTime"`
	Reason              string                      `json:"reason" bson:"reason"`
	Remark              string                      `json:"remark" bson:"remark"`
	App                 bool                        `json:"app" bson:"app"`
	Method              int                         `json:"method" bson:"method"` //方式：0人工 1扫码
	WorkTaskId          primitive.ObjectID          `json:"workTaskId"`
	CohortId            primitive.ObjectID          `json:"cohortId"`
	RoleID              string                      `json:"roleId"`
	ActualReceiptTime   string                      `json:"actualReceiptTime" bson:"actual_receipt_time"` //实际接收时间
}

// RecevieOrder ..
type RecevieOrderView struct {
	ID                  primitive.ObjectID              `json:"id"`
	Medicines           []primitive.ObjectID            `json:"medicines" bson:"medicines"`
	OtherMedicines      []ReceiveOtherMedicineCountView `json:"otherMedicines" bson:"other_medicines"`
	ExpectedArrivalTime string                          `json:"expectedArrivalTime"`
	Reason              string                          `json:"reason" bson:"reason"`
	App                 bool                            `json:"app" bson:"app"`
	Method              int                             `json:"method" bson:"method"` //方式：0人工 1扫码
	WorkTaskId          primitive.ObjectID              `json:"workTaskId"`
	CohortId            primitive.ObjectID              `json:"cohortId"`
	RoleID              string                          `json:"roleId"`
	ActualReceiptTime   string                          `json:"actualReceiptTime" bson:"actual_receipt_time"` //实际接收时间
}

// ReceiveOtherMedicineCount ..
type ReceiveOtherMedicineCountView struct {
	ID            primitive.ObjectID `json:"id" bson:"id"`
	Name          string             `json:"name" bson:"name"`
	UseCount      int                `json:"use_count" bson:"use_count" `
	PackageMethod bool               `json:"package_method" bson:"package_method" `
	PackageCount  int                `json:"package_count" bson:"package_count"`
	ReceiveCount  int                `json:"receive_count" bson:"receive_count"`
	ExpireDate    string             `json:"expire_date" bson:"expiration_date"`
	Batch         string             `json:"batch" bson:"batch_number"`
	Salt          string             `json:"salt"`
	SaltName      string             `json:"saltName"`
}

// UpdateMedicines 更新研究产品(批量隔离/批量解隔离)
type RecoveryOrderGroupData struct {
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID  primitive.ObjectID `json:"envId" bson:"env_id"`
	RoleID         primitive.ObjectID `json:"roleId" bson:"role_id"`
	SendID         primitive.ObjectID `json:"sendId" bson:"send_id"`
	CohortID       primitive.ObjectID `json:"cohortId"`
	Name           []string           `json:"name"`
	ExpirationDate string             `json:"expirationDate"`
	BatchNumber    string             `json:"batchNumber"`
	Status         int                `json:"status"`
	Number         string             `json:"number"`
	Salts          []Salt             `json:"salts"`
	Start          int                `json:"start"`
	Limit          int                `json:"limit"`
}

// SingleOrder ..
type SingleOrder struct {
	CustomerID     primitive.ObjectID   `json:"customerId"`
	ProjectID      primitive.ObjectID   `json:"projectId"`
	EnvironmentID  primitive.ObjectID   `json:"envId"`
	IsOther        bool                 `json:"isOther"`
	IsPackage      bool                 `json:"isPackage"`
	IdList         []primitive.ObjectID `json:"idList"`
	OrderID        primitive.ObjectID   `json:"orderID"`
	OtherList      []SingleOther        `json:"otherList"`
	ExpirationDate string               `json:"expirationDate"`
	BatchNumber    string               `json:"batchNumber"`
}

// SingleOther ..
type SingleOther struct {
	Batch         string `json:"batch"`
	ExpireDate    string `json:"expire_date"`
	Name          string `json:"name"`
	PackageMethod bool   `json:"package_method"`
	UseCount      int    `json:"use_count"`
}

// BatchOrder ..
type BatchOrder struct {
	CustomerID          primitive.ObjectID   `json:"customerId"`
	ProjectID           primitive.ObjectID   `json:"projectId"`
	EnvironmentID       primitive.ObjectID   `json:"envId"`
	IsPackage           bool                 `json:"isPackage"`
	IdList              []primitive.ObjectID `json:"idList"`
	ExpirationDate      string               `json:"expirationDate"`
	BatchNumber         string               `json:"batchNumber"`
	OrderID             primitive.ObjectID   `json:"orderID"`
	OtherList           []SingleOther        `json:"otherList"`
	OtherExpirationDate string               `json:"otherExpirationDate"`
	OtherBatchNumber    string               `json:"otherBatchNumber"`
}

// TableIp ..
type TableIp struct {
	Ip         string `json:"ip"`
	ExpireDate string `json:"expire_date"`
	Batch      string `json:"batch"`
}
