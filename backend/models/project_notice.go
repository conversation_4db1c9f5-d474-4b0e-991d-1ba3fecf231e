package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// ProjectNotice ...
type ProjectNotice struct {
	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvID      primitive.ObjectID `json:"envId" bson:"env_id"`
	Open       bool               `json:"open" bson:"open"`
	NoticeRule []NoticeRule       `json:"noticeRule" bson:"notice_rule"`
	RoleUser   []RoleUsers        `json:"userIds" bson:"user_ids"`
}

// NoticeRule ...
type NoticeRule struct {
	ID       primitive.ObjectID `json:"id" bson:"_id"`
	Date     int                `json:"date" bson:"date"`
	Time     string             `json:"time" bson:"time"`
	Template string             `json:"template" bson:"template"`
}

// RoleUsers ..
type RoleUsers struct {
	RoleID primitive.ObjectID   `json:"roleId" bson:"role_id"`
	Users  []primitive.ObjectID `json:"users" bson:"users"`
}

type VisitNotice struct {
	ID            primitive.ObjectID   `bson:"_id"`
	CustomerID    primitive.ObjectID   `bson:"customer_id"`
	ProjectID     primitive.ObjectID   `bson:"project_id"`
	EnvID         primitive.ObjectID   `bson:"env_id"`
	CohortID      primitive.ObjectID   `bson:"cohort_id"`
	ProjectSiteID primitive.ObjectID   `bson:"project_site_id"`
	SubjectID     primitive.ObjectID   `bson:"subject_id"`
	DispensingID  primitive.ObjectID   `bson:"dispensing_id"`
	Status        int                  `bson:"status"`          // 0 未推送 1推送成功 2、推送失败 3、取消推送
	Content       VisitNoticeContent   `bson:"content"`         // 推送内容
	TimeZoneFloat float64              `bson:"time_zone_float"` // 受试者时区
	TimeZone      int                  `bson:"time_zone"`       // 受试者时区
	Tz            string               `bson:"tz"`              // 受试者时区
	BeSendTime    string               `bson:"record_time"`     // 推送时间 按UTC+0 算 2006-01-02 15:04:05
	SendTime      time.Duration        `bson:"send_time"`       // 实际推送时间
	PushType      int                  `bson:"push_type"`       // 1 app通知 2、短信通知
	NoticeUser    []primitive.ObjectID `bson:"notice_user"`     // 通知对象
	UserID        primitive.ObjectID   `bson:"user_id"`         // 推送人
	PhoneUser     []string             `bson:"phone_user_id"`   //
	error         string               `bson:"error"`           //
	AllRoleIDs    []primitive.ObjectID `bson:"all_roleIDs"`     // 角色选中全部用户的
}

type VisitNoticeContent struct {
	Template      string `json:"template" bson:"template"`
	Day           string `json:"day" bson:"day"`
	ProjectNumber string `json:"projectNumber" bson:"project_number"`
	SiteNumber    string `json:"siteNumber" bson:"site_number"`
	SubjectNumber string `json:"subjectNumber" bson:"subject_number"`
	VisitNumber   string `json:"visitNumber" bson:"visit_number"`
}

type UserNotice struct {
	UserID primitive.ObjectID `bson:"user_id"`
}

type PushVisitReq struct {
	DispensingID string      `bson:"dispensingId"`
	RoleUser     []RoleUsers `json:"userIds"`
	PhoneUser    []string    `json:"phoneUser"`
	Type         int         `json:"type"`
	Template     string      `json:"template"`
	PeriodMin    string      `json:"periodMin"`
}

type VisitNoticeHistory struct {
	ID            primitive.ObjectID `json:"id"`
	Status        int                `json:"status"`          // 0 未推送 1推送成功 2、推送失败 3、取消推送
	Content       VisitNoticeContent `json:"content"`         // 推送内容
	TimeZoneFloat float64            `bson:"time_zone_float"` // 受试者时区
	TimeZone      int                `json:"timeZone"`        // 受试者时区
	PushTime      time.Duration      `json:"pushTime"`        // 推送时间
	PushPeople    string             `json:"pushPeople"`      // 推送人
	PushType      int                `json:"pushType"`        // 推送方式 1 app通知 2、短信通知
	Object        string             `json:"object"`          // 通知对象
}
