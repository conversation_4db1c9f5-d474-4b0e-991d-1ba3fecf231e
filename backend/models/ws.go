package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type WsResult struct {
	CustomerId primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvId      primitive.ObjectID `json:"envId" bson:"env_id"`
	RoleId     primitive.ObjectID `json:"roleId" bson:"role_id"`
	UserId     primitive.ObjectID `json:"userId" bson:"user_id"`
	Text       string             `json:"text" bson:"text"` // app语言
}
