package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UploadMedicine 上传研究产品
type UploadMedicine struct {
	CustomerID    primitive.ObjectID `json:"customerId"`
	ProjectID     primitive.ObjectID `json:"projectId"`
	EnvironmentID primitive.ObjectID `json:"envId"`
	CohortID      primitive.ObjectID `json:"cohortId"`
	UploadInfo    UploadInfo         `json:"info"`
}

// UploadInfo ..
type UploadInfo struct {
	DrugName       string             `json:"drugName"`
	Storehouse     primitive.ObjectID `json:"storehouse"`
	ExpirationDate string             `json:"expirationDate"`
	Batch          string             `json:"batch"`
}

// Medicine 研究产品号
type Medicine struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID  primitive.ObjectID `json:"envId" bson:"env_id"`
	Name           string             `json:"name" bson:"name"`
	Number         string             `json:"number" bson:"number"`
	StorehouseID   primitive.ObjectID `json:"storehouseId" bson:"storehouse_id"`
	SiteID         primitive.ObjectID `json:"siteId" bson:"site_id"`
	ExpirationDate string             `json:"expirationDate" bson:"expiration_date"`
	BatchNumber    string             `json:"batchNumber" bson:"batch_number"`
	Spec           string             `json:"spce" bson:"spec"`
	OrderID        primitive.ObjectID `json:"orderId" bson:"order_id"`
	//0:待入仓 1:可用、2:已确认、3:已运送、4:已隔离、5:已使用、6:作废/已丢失、7:已过期、8:已领药、9:已退货、10:已销毁、11:待确认 12:待入库（对接物流系统） 13:已申请 14:已冻结 20：锁定状态 21: 待扫码(自动编码) 22: 待审核(自动编码) 23: 审核失败(自动编码)
	Status int `json:"status" bson:"status"`
	// 解隔离审批标记（0正常 1解隔离待审批）
	IsolationApprovalSign int                        `json:"isolationApprovalSign" bson:"isolation_approval_sign"`
	TtxSkuID              string                     `json:"ttxSkuId" bson:"ttx_sku_id"`
	PackageNumber         string                     `json:"packageNumber" bson:"package_number"`              //包装号
	PackageSerialNumber   string                     `json:"packageSerialNumber" bson:"package_serial_number"` //包装号序列号
	Packlist              []string                   `json:"packlist" bson:"packlist"`
	SubjectID             primitive.ObjectID         `json:"subjectId" bson:"subject_id"`       // 中心交付模式下:研究产品取回 绑定受试者 只发给对应受试者。   DTP 模式、发药后绑定受试者
	EntryTime             time.Duration              `json:"entryTime" bson:"entry_time"`       //入仓时间
	ScanStatus            int                        `json:"scanStatus" bson:"scan_status"`     //扫码状态 0未扫 1已扫
	SerialNumber          string                     `json:"serialNumber" bson:"serial_number"` //序列号，如果为空默认取number 研究产品编号
	ShortCode             string                     `json:"shortCode" bson:"short_code"`       //app对接新增短码字段
	TransferData          TransferData               `json:"transferData" bson:"transfer_data"`
	UnblindingApprovals   []UrgentUnblindingApproval `json:"UnblindingApprovals" bson:"unblinding_approvals"` //pv揭盲 审批流程/短信
	LabelStatus           int                `json:"labelStatus" bson:"label_status"` //标签状态  0未创建 1已创建

}

// Medicine 研究产品号B
type MedicineBrief struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	Name           string             `json:"name" bson:"name"`
	Number         string             `json:"number" bson:"number"`
	ExpirationDate string             `json:"expirationDate" bson:"expiration_date"`
	BatchNumber    string             `json:"batchNumber" bson:"batch_number"`
	Spec           string             `json:"spce" bson:"spec"`
	PackageNumber  string             `json:"packageNumber" bson:"package_number"` //包装号
}

// Package 自动生成的包装号
type Package struct {
	ID                  primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID          primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID           primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID       primitive.ObjectID `json:"envId" bson:"env_id"`
	PackageNumber       string             `json:"packageNumber" bson:"package_number"`              //包装号
	PackageSerialNumber string             `json:"packageSerialNumber" bson:"package_serial_number"` //包装号序列号
}

// Medicine 研究产品号
type MedicinePackageDrug struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID  primitive.ObjectID `json:"envId" bson:"env_id"`
	Name           string             `json:"name" bson:"name"`
	Number         string             `json:"number" bson:"number"`
	StorehouseID   primitive.ObjectID `json:"storehouseId" bson:"storehouse_id"`
	SiteID         primitive.ObjectID `json:"siteId" bson:"site_id"`
	ExpirationDate string             `json:"expirationDate" bson:"expiration_date"`
	BatchNumber    string             `json:"batchNumber" bson:"batch_number"`
	Spec           string             `json:"spce" bson:"spec"`
	OrderID        primitive.ObjectID `json:"orderId" bson:"order_id"`
	//0:待入仓 1:可用、2:已确认、3:已运送、4:已隔离、5:已使用、6:作废/已丢失、7:已过期、8:已领药、9:已退货、10:已销毁、11:待确认 12:待入库（对接物流系统） 13:已申请 14:已冻结 20：锁定状态
	Status int `json:"status" bson:"status"`
	// 解隔离审批标记（0正常 1解隔离待审批）
	IsolationApprovalSign int                `json:"isolationApprovalSign" bson:"isolation_approval_sign"`
	TtxSkuID              string             `json:"ttxSkuId" bson:"ttx_sku_id"`
	PackageNumber         string             `json:"packageNumber" bson:"package_number"`
	Packlist              []string           `json:"packlist" bson:"packlist"`
	SubjectID             primitive.ObjectID `json:"subjectId" bson:"subject_id"`       // 中心交付模式下:研究产品取回 绑定受试者 只发给对应受试者。   DTP 模式、发药后绑定受试者
	EntryTime             time.Duration      `json:"entryTime" bson:"entry_time"`       //入仓时间
	ScanStatus            int                `json:"scanStatus" bson:"scan_status"`     //扫码状态 0未扫 1已扫
	SerialNumber          string             `json:"serialNumber" bson:"serial_number"` //序列号，如果为空默认取number 研究产品编号
	PackageDrug           bool               `json:"packageDrug"`
}

// BatchInfo 批次信息
type BatchInfo struct {
	CustomerID           primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID            primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvironmentID        primitive.ObjectID   `json:"envId" bson:"env_id"`
	CohortID             primitive.ObjectID   `json:"cohortId"`
	Status               []int                `json:"status"`
	UpdateBatchs         []BatchManagement    `json:"updateBatchs"`
	UpdateCount          int                  `json:"updateCount"`
	UpdateBatch          string               `json:"updateBatch"`
	UpdateExpirationDate string               `json:"updateExpirationDate"`
	MultUpdate           bool                 `json:"multUpdate"`
	IdList               []primitive.ObjectID `json:"idList"`
}

// UpdateMedicines 中心批量更新研究产品(批量隔离)
type UpdateSiteMedicines struct {
	CustomerID       primitive.ObjectID     `json:"customerId" bson:"customer_id"`
	ProjectID        primitive.ObjectID     `json:"projectId" bson:"project_id"`
	EnvironmentID    primitive.ObjectID     `json:"envId" bson:"env_id"`
	CohortID         primitive.ObjectID     `json:"cohortId"`
	InstituteIDS     []primitive.ObjectID   `json:"instituteIds"`
	InstituteType    int                    `json:"instituteType"`
	Reason           string                 `json:"reason"`
	Remark           string                 `json:"remark"` //揭盲原因备注
	Status           int                    `json:"status"`
	ApprovalStatus   int                    `json:"approvalStatus"`
	MedicineIds      []primitive.ObjectID   `json:"medicineIds"`
	OtherMedicines   []ReleaseOtherMedicine `json:"otherMedicines" bson:"other_medicines"`
	MedicineFreezeID primitive.ObjectID     `json:"freezeId"`
}

// UpdateMedicines 更新研究产品(批量隔离/批量解隔离)
type UpdateMedicines struct {
	CustomerID       primitive.ObjectID     `json:"customerId" bson:"customer_id"`
	ProjectID        primitive.ObjectID     `json:"projectId" bson:"project_id"`
	EnvironmentID    primitive.ObjectID     `json:"envId" bson:"env_id"`
	CohortID         primitive.ObjectID     `json:"cohortId"`
	InstituteID      primitive.ObjectID     `json:"instituteId"`
	InstituteType    int                    `json:"instituteType"`
	Reason           string                 `json:"reason"`
	Remark           string                 `json:"remark"` //揭盲原因备注
	Status           int                    `json:"status"`
	ApprovalStatus   int                    `json:"approvalStatus"`
	MedicineIds      []primitive.ObjectID   `json:"medicineIds"`
	OtherMedicines   []ReleaseOtherMedicine `json:"otherMedicines" bson:"other_medicines"`
	MedicineFreezeID primitive.ObjectID     `json:"freezeId"`
}

// MedicineFreeze 研究产品隔离
type MedicineFreeze struct {
	ID                primitive.ObjectID    `json:"id" bson:"_id"`
	CustomerID        primitive.ObjectID    `json:"customerId" bson:"customer_id"`
	ProjectID         primitive.ObjectID    `json:"projectId" bson:"project_id"`
	EnvironmentID     primitive.ObjectID    `json:"envId" bson:"env_id"`
	InstituteID       primitive.ObjectID    `json:"instituteId" bson:"institute_id"`
	InstituteType     int                   `json:"instituteType" bson:"institute_type"` // 1:中心 2：仓库
	OrderType         int                   `json:"orderType" bson:"order_type"`         //1：是订单
	Number            string                `json:"number" bson:"number"`
	Reason            string                `json:"reason" bson:"reason"`                         // 隔离原因
	UntieReason       string                `json:"untieReason" bson:"untie_reason"`              // 解隔离申请原因
	MedicinesPackage  []MedicinePackage     `json:"medicinesPackage" bson:"medicines_package"`    //研究产品名称运输方式
	Medicines         []primitive.ObjectID  `json:"medicines" bson:"medicines"`                   //编码研究产品ID
	FrozenMedicineIds []primitive.ObjectID  `json:"frozenMedicineIds" bson:"frozen_medicine_ids"` //记录隔离时冻结药物的ID，因为解隔离后还要返回冻结状态
	History           []primitive.ObjectID  `json:"history" bson:"history"`
	OtherMedicines    []FreezeOtherMedicine `json:"otherMedicines" bson:"other_medicines"`        //未编码研究产品ID
	OtherHistory      []FreezeOtherMedicine `json:"otherHistory" bson:"other_history"`            //未编码研究产品ID
	OtherMedicinesNew []primitive.ObjectID  `json:"otherMedicinesNew" bson:"other_medicines_new"` //未编码研究产品ID
	OtherHistoryNew   []primitive.ObjectID  `json:"otherHistoryNew" bson:"other_history_new"`     //未编码研究产品ID
	CloseDate         time.Duration         `json:"closeDate" bson:"close_date"`
	Meta              Meta                  `json:"meta"`
}

// 删除研究产品列表
type DeleteData struct {
	CustomerID    primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID   `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID   `json:"cohortId" bson:"cohort_id"`
	DeleteIds     []primitive.ObjectID `json:"deleteIds"`
}

// MedicineStatus 研究产品状态
type MedicineStatus struct {
	Key  int    `json:"key"`
	Name string `json:"name"`
}

// FreezeOtherMedicine 未编码研究产品
type FreezeOtherMedicine struct {
	Name string `json:"name"`
	//Count            int    `json:"count"`
	MedicineOtherInfo
	ApprovedQuantity int    `json:"approvedQuantity" bson:"approved_quantity"`
	ExpireDate       string `json:"expireDate" bson:"expire_date"`
	Batch            string `json:"batch"`
}

type OtherMedicineFreeze struct {
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID `json:"cohortId"`
	//InstituteID    primitive.ObjectID     `json:"instituteId" bson:"institute_id"`
	InstituteType  int                    `json:"instituteType" bson:"institute_type"` //1中心  2 仓库
	Status         int                    `json:"status" bson:"status"`
	Reason         string                 `json:"reason" bson:"reason"`
	Remark         string                 `json:"remark" bson:"remark"`
	OtherMedicines []FreezeOtherMedicines `json:"otherMedicines" bson:"other_medicines"` //未编码研究产品ID
}

type FreezeOtherMedicines struct {
	ID            primitive.ObjectID `json:"id"`
	Name          string             `json:"name"`
	Salt          string             `json:"salt"`
	SaltName      string             `json:"saltName"`
	ExpireDate    string             `json:"expirationDate"`
	Batch         string             `json:"batchNumber"`
	IsOpenPackage bool               `json:"isOpenPackage"`
	Count         int                `json:"count"`
	FreezeCount   int                `json:"freezeCount"`
	LostCount     int                `json:"lostCount"`
	ExpiredCount  int                `json:"expiredCount"`
	StorehouseID  primitive.ObjectID `json:"storehouse" `
	SiteID        primitive.ObjectID `json:"site"`
}

// ReleaseOtherMedicine 解隔离未编码研究产品
type ReleaseOtherMedicine struct {
	Name string `json:"name"`
	MedicineOtherInfo
	//Count            int    `json:"count"`
	ApprovedQuantity int    `json:"approved_quantity"` // 待审批药物
	PackageMethod    bool   `json:"package_method"`
	UseCount         int    `json:"useCount"`
	ExpireDate       string `json:"expirationDate"`
	Batch            string `json:"batchNumber"`
	Salt             string `json:"salt"`
	SaltName         string `json:"saltName"`
}

// 手动上传研究产品Request
type UploadMedicineRequest struct {
	CustomerID     string         `form:"customerId" json:"customerId" bson:"customer_id"`
	ProjectID      string         `form:"projectId" json:"projectId" bson:"project_id"`
	EnvironmentID  string         `form:"envId" json:"envId" bson:"env_id"`
	DrugNames      []DrugNameInfo `form:"drugNames" json:"drugNames" bson:"drug_names"`
	StorehouseID   string         `form:"storehouseId" json:"storehouseId" bson:"storehouse_id"`
	ExpirationDate string         `form:"expirationDate" json:"expirationDate" bson:"expiration_date"`
	BatchNumber    string         `form:"batch" json:"batch" bson:"batch"`
}

type DrugNameInfo struct {
	DrugName string `form:"drugName" json:"drugName" bson:"drug_name"`
	Spec     string `form:"spec" json:"spec" bson:"spec"`
}

type MedicinesGroupID struct {
	Name           []string `json:"name" bson:"name"`
	ExpirationDate string   `json:"expirationDate" bson:"expiration_date"`
	BatchNumber    string   `json:"batchNumber" bson:"batch_number"`
	Salts          []Salt   `json:"salts" bson:"salts"`
}

type Salt struct {
	Name     string `json:"name" bson:"name"`
	Salt     string `json:"salt"`
	SaltName string `json:"saltName"`
}

type MedicinesGroup struct {
	MedicinesGroupID `json:",inline" bson:"_id"`
	Count            int         `json:"count"`
	PackageMethod    string      `json:"packageMethod"`
	DetailData       interface{} `json:"detailData"`
}

type BatchManagement struct {
	ExpirationDate string             `json:"expirationDate" bson:"expiration_date"`
	BatchNumber    string             `json:"batchNumber" bson:"batch_number"`
	Name           string             `json:"name" bson:"name"`
	Position       string             `json:"position" bson:"position"`
	PositionId     primitive.ObjectID `json:"positionId" bson:"position_id"`
	Count          int32              `json:"count" bson:"count"`
	Type           int                `json:"type" bson:"type"`                    //1:库房 -》2   2：中心-》1  3：订单
	OrderType      int                `json:"orderType" bson:"order_type"`         // 订单状态
	Status         int32              `json:"status" bson:"status"`                //type：3的时候，订单状态
	IsOther        bool               `json:"isOther" bson:"is_other"`             //是否未编号药物
	IsPackage      bool               `json:"isPackage" bson:"is_package"`         // 是否是包装药物
	PackageNumber  int                `json:"packageNumber" bson:"package_number"` // 包装数量
}

type BatchManagementRequst struct {
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	UpdateBatchs  []BatchManagement  `json:"updateBatchs" bson:"update_batchs"`
}

type ToExamineFlowPathParameter struct {
	CustomerID        primitive.ObjectID  `json:"customerId"`
	ProjectID         primitive.ObjectID  `json:"projectId"`
	EnvironmentID     primitive.ObjectID  `json:"envId"`
	CohortID          primitive.ObjectID  `json:"cohortId"`
	Sign              int                 `json:"sign"`        // 标记 1：审核 2：修改 3：放行
	RadioValue        int                 `json:"radioValue"`  // 审核状态 1：通过 2：拒绝
	RemarkValue       string              `json:"remarkValue"` // 拒绝的备注
	MedicineParameter []MedicineParameter `json:"medicineParameter"`
}

type MedicineParameter struct {
	Name           string               `json:"name"`           // 药品名称
	NewName        string               `json:"newName"`        // 新的药品名称
	BatchNumber    string               `json:"batchNumber"`    // 批次
	Count          int                  `json:"count"`          // 数量
	ExpirationDate string               `json:"expirationDate"` // 日期
	Status         int                  `json:"status"`         // 状态
	MesicineIds    []primitive.ObjectID `json:"mesicineIds"`    // 药品ID
	PackageNumber  string               `json:"packageNumber"`  // 包装号
}

var MedicineFields = bson.M{
	"_id":                   1,
	"name":                  1,
	"number":                1,
	"expiration_date":       1,
	"batch_number":          1,
	"spec":                  1,
	"package_number":        1,
	"scan_status":           1,
	"status":                1,
	"short_code":            1,
	"serial_number":         bson.M{"$ifNull": bson.A{"$serial_number", "$number"}},
	"package_serial_number": 1,
}

// 取药顺序
var MedicineProjectM = bson.M{
	"_id":                     1,
	"customer_id":             1,
	"project_id":              1,
	"env_id":                  1,
	"name":                    1,
	"number":                  1,
	"storehouse_id":           1,
	"site_id":                 1,
	"expiration_date":         1,
	"batch_number":            1,
	"spec":                    1,
	"order_id":                1,
	"isolation_approval_sign": 1,
	"ttx_sku_id":              1,
	"packlist":                1,
	"package_number":          1,
	"subject_id":              1,
	"entry_time":              1,
	"scan_status":             1,
	"status":                  1,
	"short_code":              1,
	"serial_number":           bson.M{"$ifNull": bson.A{"$serial_number", "$number"}},
	"package_serial_number":   1,
}
var MedicineProject = bson.D{{Key: "$project", Value: MedicineProjectM}}

var MedicineQueryProjectM = bson.M{
	"_id":                     1,
	"customer_id":             1,
	"project_id":              1,
	"env_id":                  1,
	"name":                    1,
	"number":                  1,
	"storehouse_id":           1,
	"site_id":                 1,
	"expiration_date":         bson.M{"$cond": bson.M{"if": bson.M{"$ne": bson.A{"$expiration_date", ""}}, "then": "$expiration_date", "else": "9999-12-31"}},
	"batch_number":            1,
	"spec":                    1,
	"order_id":                1,
	"isolation_approval_sign": 1,
	"ttx_sku_id":              1,
	"packlist":                1,
	"package_number":          1,
	"subject_id":              1,
	"entry_time":              1,
	"scan_status":             1,
	"status":                  1,
	"short_code":              1,
	"serial_number":           bson.M{"$ifNull": bson.A{"$serial_number", "$number"}},
	"package_serial_number":   1,
}
var MedicineQueryProject = bson.D{{Key: "$project", Value: MedicineQueryProjectM}}

var MedicineSort = bson.D{{Key: "$sort", Value: bson.D{{"expiration_date", 1}, {"serial_number", 1}}}}

type NameCount struct {
	Name  string
	Count int
}

// CopyMedicine 复制研究产品号
type CopyMedicine struct {
	Name           string             `json:"name" bson:"name"`
	StorehouseID   primitive.ObjectID `json:"storehouseId" bson:"storehouse_id"`
	ExpirationDate string             `json:"expirationDate" bson:"expiration_date"`
	BatchNumber    string             `json:"batchNumber" bson:"batch_number"`
	Spec           string             `json:"spce" bson:"spec"`
	Count          int                `json:"count" bson:"count"`
}

// SelectMedicines 批次管理选中研究产品(不包含未编号)
type SelectMedicines struct {
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID  primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID       primitive.ObjectID `json:"cohortId"`
	Status         []int              `json:"status"`
	FindBatchs     []BatchManagement  `json:"findBatchs"`
	Batch          string             `json:"batch"`
	ExpirationDate string             `json:"expirationDate"`
	UpperNumber    string             `json:"upperNumber"`
	LowerNumber    string             `json:"lowerNumber"`
	UpperSequence  string             `json:"upperSequence"`
	LowerSequence  string             `json:"lowerSequence"`
}

type AlarmMedicine struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	Number         string             `json:"number" bson:"number"`
	ExpirationDate string             `json:"expirationDate" bson:"expiration_date"`
	SerialNumber   string             `json:"serialNumber" bson:"serial_number"` //序列号，如果为空默认取number 研究产品编号

}

type MedicineSkuReq struct {
	CustomerID    primitive.ObjectID   `json:"customerId"`
	ProjectID     primitive.ObjectID   `json:"projectId"`
	EnvID         primitive.ObjectID   `json:"envId"`
	CohortID      primitive.ObjectID   `json:"cohortId"`
	SiteIDs       []primitive.ObjectID `json:"siteIds"`
	StorehouseIDs []primitive.ObjectID `json:"storehouseIds"`
	RoleID        primitive.ObjectID   `json:"roleId"`
	Start         int                  `json:"start"`
	Limit         int                  `json:"limit"`
	Status        string               `json:"status"`
	Field         string               `json:"field"`
	FieldValue    string               `json:"fieldValue"`
}

type SortByMedicineNumber []AlarmMedicine

func (a SortByMedicineNumber) Len() int      { return len(a) }
func (a SortByMedicineNumber) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortByMedicineNumber) Less(i, j int) bool {
	//先按照中心降序，然后按照国家降序，最后按照区域降序
	if a[i].ExpirationDate == a[j].ExpirationDate {
		if a[i].SerialNumber == a[j].SerialNumber {
			return a[i].Number > a[j].Number
		}
		return a[i].SerialNumber > a[j].SerialNumber
	}
	return a[i].ExpirationDate > a[j].ExpirationDate
}
