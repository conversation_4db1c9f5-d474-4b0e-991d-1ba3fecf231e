package multilingual

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"context"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"reflect"
)

func LanguageId(c context.Context) (primitive.ObjectID, error) {
	switch ctx := c.(type) {
	case *gin.Context:
		languageId, err := primitive.ObjectIDFromHex(ctx.GetHeader("languageId"))
		if err != nil {
			return primitive.NilObjectID, err
		}
		return languageId, nil
	default:
		return primitive.NilObjectID, errors.New("not languageId")
	}
}

func Tr(c context.Context, key string, data ...interface{}) string {
	// 加载数据库翻译
	translations := loadTranslationsFromDB(c, []string{key})
	if content, ok := translations[key]; ok {
		return locales.TrMessage(c, key, content, data)
	}
	return locales.Tr(c, key, data)
}

func TrBatch(c context.Context, data []interface{}) []interface{} {
	// 加载数据库翻译
	translations := loadTranslationsFromDB(c, getTrKeys(data))
	for i, item := range data {
		if isTrData(item) {
			trData := item.(locales.TrData)
			if content, ok := translations[trData.Key]; ok {
				data[i] = locales.TrMessage(c, trData.Key, content, trData.Data)
			} else {
				data[i] = locales.Tr(c, trData.Key, trData.Data)
			}
		}
	}
	return data
}

func TrBatchMap(c context.Context, data []interface{}) map[string]string {
	result := make(map[string]string)
	// 加载数据库翻译
	translations := loadTranslationsFromDB(c, getTrKeys(data))
	for _, item := range data {
		if isTrData(item) {
			trData := item.(locales.TrData)
			if content, ok := translations[trData.Key]; ok {
				result[trData.Key] = locales.TrMessage(c, trData.Key, content, trData.Data)
			} else {
				result[trData.Key] = locales.Tr(c, trData.Key, trData.Data)
			}
		}
	}
	return result
}

func TrDoubleBatch(c context.Context, data [][]interface{}) [][]interface{} {
	// 加载数据库翻译
	fields := make([]interface{}, 0)
	slice.ForEach(data, func(index int, item []interface{}) {
		fields = append(fields, item...)
	})
	translations := loadTranslationsFromDB(c, getTrKeys(fields))
	for i, fieldItems := range data {
		for i2, item := range fieldItems {
			if isTrData(item) {
				trData := item.(locales.TrData)
				if content, ok := translations[trData.Key]; ok {
					data[i][i2] = locales.TrMessage(c, trData.Key, content, trData.Data)
				} else {
					data[i][i2] = locales.Tr(c, trData.Key, trData.Data)
				}
			}
		}
	}
	return data
}

func isTrData(item interface{}) bool {
	return reflect.TypeOf(item).Name() == reflect.TypeOf(locales.TrData{}).Name()
}

func getTrKeys(data []interface{}) []string {
	keys := slice.Map(data, func(index int, item interface{}) string {
		if isTrData(item) {
			return item.(locales.TrData).Key
		}
		return ""
	})
	return slice.Filter(keys, func(index int, item string) bool {
		return item != ""
	})
}

// 加载翻译
func loadTranslationsFromDB(ctx context.Context, keys []string) map[string]string {
	translations := make(map[string]string)
	languageId, err := LanguageId(ctx)
	// languageId, err := primitive.ObjectIDFromHex("67ed4defc73817c202175fc6")
	if err != nil {
		return translations
	}
	// 处理数据
	translationMap := database.MultiLanguageTranslateFindByKeys(ctx, languageId, keys)
	for key, name := range translationMap {
		translations[key] = name
	}
	return translations
}
