package service

import (
	"clinflash-irt/database"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"time"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ProjectSiteService struct{}

func (s *ProjectSiteService) List(ctx *gin.Context, customerID string, projectID string, envID string, roleID string, start int, limit int) (map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)

	isBlindRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	data := make([]map[string]interface{}, 0)
	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}

	var projectRolePermission models.ProjectRolePermission
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID, "customer_id": customerOID, "project_id": projectOID}).Decode(&projectRolePermission)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	if projectRolePermission.Scope == "site" {
		siteIdList := make([]primitive.ObjectID, 0)
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		condition := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "user_id": user.ID}
		userSiteList := make([]models.UserSite, 0)
		userSiteCursor, err := tools.Database.Collection("user_site").Find(ctx, condition)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = userSiteCursor.All(ctx, &userSiteList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if userSiteList != nil && len(userSiteList) > 0 {
			for _, userSite := range userSiteList {
				siteIdList = append(siteIdList, userSite.SiteID)
			}
		}
		match["_id"] = bson.M{"$in": siteIdList}
	}

	total, err := tools.Database.Collection("project_site").CountDocuments(context.TODO(), match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	planLookup := bson.M{
		"from":         "supply_plan",
		"localField":   "supply_plan_id",
		"foreignField": "_id",
		"as":           "plan",
	}
	storehouseLookup := bson.M{
		"from":         "project_storehouse",
		"localField":   "storehouse_id",
		"foreignField": "_id",
		"as":           "storehouse",
	}
	storehouseNameLookup := bson.M{
		"from":         "storehouse",
		"localField":   "storehouse.storehouse_id",
		"foreignField": "_id",
		"as":           "storehouseName",
	}

	medicineOrderLookup := bson.M{
		"from":         "medicine_order",
		"localField":   "_id",
		"foreignField": "receive_id",
		"as":           "medicine_order",
	}

	project := bson.M{
		"_id":                    0,
		"id":                     "$_id",
		"active":                 1,
		"deleted":                1,
		"supplyPlanId":           "$supply_plan_id",
		"storehouseId":           "$storehouse_id",
		"number":                 1,
		"name":                   models.ProjectSiteNameNoShortNameBson(ctx),
		"cn":                     "$name",
		"en":                     "$name_en",
		"contacts":               1,
		"contactGroup":           "$contact_group",
		"regionId":               "$region_id",
		"phone":                  1,
		"email":                  1,
		"address":                1,
		"country":                1,
		"shortName":              "$short_name",
		"time_zone":              "$time_zone",
		"tz":                     "$tz",
		"dmp_id":                 "$dmp_id",
		"plan":                   "$plan.info.name",
		"storehouseName":         "$storehouseName.name",
		"sendOrder":              bson.M{"$size": "$medicine_order"},
		"aliSiteId":              "$ali_site_id",
		"supplyRatio":            "$is_supply_ratio",
		"orderApplicationConfig": "$order_application_config",
		"batchGroupAlarmOpen":    "$batch_group_alarm_open",
		"batchGroupAlarm":        "$batch_group_alarm",
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
		{{Key: "$lookup", Value: planLookup}},
		{{Key: "$lookup", Value: storehouseLookup}},
		{{Key: "$lookup", Value: storehouseNameLookup}},
		{{Key: "$lookup", Value: medicineOrderLookup}},
		{{Key: "$project", Value: project}},
	}
	cursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := cursor.All(nil, &data); err != nil {
		return nil, errors.WithStack(err)
	}

	if data != nil && len(data) > 0 {
		for _, da := range data {
			t, ok := da["tz"].(string)
			if ok {
				if len(t) > 0 {
					tz, err := tools.GetUTCOffsetString(t)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					da["location"] = tz
				}
			}
			if isBlindRole {
				da["orderApplicationConfig"] = nil
			}
		}
	}

	result := map[string]interface{}{
		"list":  data,
		"total": total,
	}
	return result, nil
}

func (s *ProjectSiteService) SiteList(ctx *gin.Context, customerID string, projectID string, envID string) ([]map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	var returnData []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"customer_id": customerOID,
			"project_id":  projectOID,
			"env_id":      envOID,
			"deleted":     2,
		}}},
		{{Key: "$project", Value: bson.M{
			"id":     "$_id",
			"_id":    0,
			"number": 1,
			"name":   models.ProjectSiteNameBson(ctx),
		}}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
	}
	siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := siteCursor.All(nil, &returnData); err != nil {
		return nil, errors.WithStack(err)
	}
	return returnData, nil
}

func (s *ProjectSiteService) UserSites(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) ([]map[string]interface{}, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var data []map[string]interface{}
	var match = bson.M{
		"env_id":  envOID,
		"deleted": 2,
	}
	if customerID != "" {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		match["customer_id"] = customerOID
	}
	if projectID != "" {
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		match["project_id"] = projectOID
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":        "$_id",
				"_id":       0,
				"number":    "$number",
				"nameEn":    "$name_en",
				"name":      models.ProjectSiteNameBson(ctx),
				"time_zone": 1,
			},
		}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
	}
	role, err := tools.GetRole(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if "depot" == role.Scope {
		return data, nil
	}
	if "site" == role.Scope {
		user, _ := tools.Me(ctx)
		match["user_site.user_id"] = user.ID
		pipeline = mongo.Pipeline{
			{{Key: "$lookup", Value: bson.M{
				"from":         "user_site",
				"localField":   "_id",
				"foreignField": "site_id",
				"as":           "user_site",
			}}},
			{{Key: "$unwind", Value: "$user_site"}},
			{{Key: "$match", Value: match}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":        "$_id",
					"_id":       0,
					"number":    "$number",
					"nameEn":    "$name_en",
					"name":      models.ProjectSiteNameBson(ctx),
					"time_zone": 1,
				},
			}},
			{{Key: "$sort", Value: bson.D{{"number", 1}}}},
		}
	}
	collection := tools.Database.Collection("project_site")
	cursor, err := collection.Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	return data, nil
}

func (s *ProjectSiteService) GetSiteFromMedicineOrder(customerOID primitive.ObjectID, projectOID primitive.ObjectID, envOID primitive.ObjectID) ([]primitive.ObjectID, error) {
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "type": bson.M{"$in": bson.A{1, 3}}}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("medicine_order").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":        0,
				"receive_id": 1,
			},
		}},
	})
	if err != nil {
		return nil, errors.WithStack(err)

	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var siteList = make([]primitive.ObjectID, 0)
	for _, item := range data {
		if !tools.In(item["receive_id"].(primitive.ObjectID), siteList) {
			siteList = append(siteList, item["receive_id"].(primitive.ObjectID))
		}
	}
	return siteList, nil
}

func (s *ProjectSiteService) Add(ctx *gin.Context, projectSiteParameter models.ProjectSiteParameter) error {
	// 赋值参数
	var projectSite = models.ProjectSite{
		ID:                     projectSiteParameter.ID,
		CustomerID:             projectSiteParameter.CustomerID,
		ProjectID:              projectSiteParameter.ProjectID,
		EnvironmentID:          projectSiteParameter.EnvironmentID,
		SupplyPlanID:           projectSiteParameter.SupplyPlanID,
		StoreHouseID:           projectSiteParameter.StoreHouseID,
		Number:                 projectSiteParameter.Number,
		Name:                   projectSiteParameter.Zh,
		NameEn:                 projectSiteParameter.En,
		ShortName:              projectSiteParameter.ShortName,
		Contacts:               projectSiteParameter.Contacts,
		ContactGroup:           projectSiteParameter.ContactGroup,
		Phone:                  projectSiteParameter.Phone,
		Email:                  projectSiteParameter.Email,
		Address:                projectSiteParameter.Address,
		Country:                projectSiteParameter.Country,
		Active:                 projectSiteParameter.Active,
		Deleted:                projectSiteParameter.Deleted,
		TimeZone:               projectSiteParameter.TimeZone,
		Tz:                     projectSiteParameter.Tz,
		Meta:                   projectSiteParameter.Meta,
		DmpID:                  projectSiteParameter.DmpID,
		RegionID:               projectSiteParameter.RegionID,
		SupplyRatio:            projectSiteParameter.SupplyRatio,
		OrderApplicationConfig: projectSiteParameter.OrderApplicationConfig,
		BatchGroupAlarmOpen:    projectSiteParameter.BatchGroupAlarmOpen,
		BatchGroupAlarm:        projectSiteParameter.BatchGroupAlarm,
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		if len(projectSiteParameter.Tz) > 0 {
			offsetString, err := tools.GetUTCOffsetString(projectSiteParameter.Tz)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			projectSite.TimeZone = offsetString
			projectSite.Tz = projectSiteParameter.Tz
		}

		match := bson.M{
			"customer_id": projectSite.CustomerID,
			"project_id":  projectSite.ProjectID,
			"env_id":      projectSite.EnvironmentID,
			"number":      bson.M{"$regex": "^" + projectSite.Number + "$", "$options": "i"},
		}
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var data models.ProjectSite
		collection := tools.Database.Collection("project_site")

		// 查询中心时区
		//var sitesConfig models.SitesConfig
		//_ = tools.Database.Collection("sites_config").FindOne(sctx, bson.M{"$or": bson.A{bson.M{"cn": projectSite.Name}, bson.M{"en": projectSite.Name}}}).Decode(&sitesConfig)
		//projectSite.TimeZone = sitesConfig.TimeZone

		if projectSite.ID == primitive.NilObjectID {
			// 校验编号是否重复
			collection.FindOne(nil, match).Decode(&data)
			if data.ID != primitive.NilObjectID {
				return nil, tools.BuildServerError(ctx, "sites.duplicated.number")
			}
			projectSite.ID = primitive.NewObjectID()
			projectSite.Meta = models.Meta{
				CreatedBy: user.ID,
				CreatedAt: time.Duration(time.Now().Unix()),
			}
			if _, err := collection.InsertOne(sctx, projectSite); err != nil {
				return nil, errors.WithStack(err)
			}
			OID := projectSiteParameter.EnvironmentID
			err = insertProjectSiteLog(ctx, sctx, OID, 1, models.ProjectSite{}, projectSite, "")
			if err != nil {
				return nil, err
			}
		} else {
			var oldData models.ProjectSite
			queryMatch := bson.M{
				"_id": projectSite.ID,
			}
			collection.FindOne(nil, queryMatch).Decode(&oldData)
			// 校验编号是否重复
			match["_id"] = bson.M{"$ne": projectSite.ID}
			collection.FindOne(nil, match).Decode(&data)
			if data.ID != primitive.NilObjectID {
				return nil, tools.BuildServerError(ctx, "sites.duplicated.number")
			}
			if projectSite.Deleted == 1 {
				count, err := tools.Database.Collection("subject").CountDocuments(sctx, bson.M{"project_site_id": projectSite.ID, "deleted": bson.M{"$ne": true}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if count > 0 {
					return nil, tools.BuildServerError(ctx, "site_not_delete")
				}
			}
			projectSite.DmpID = projectSiteParameter.DmpID
			projectSite.Tz = projectSiteParameter.Tz
			projectSite.TimeZone = projectSiteParameter.TimeZone
			setUpdate := bson.M{
				"supply_plan_id":         projectSite.SupplyPlanID,
				"storehouse_id":          projectSite.StoreHouseID,
				"number":                 projectSite.Number,
				"name":                   projectSite.Name,
				"name_en":                projectSite.NameEn,
				"short_name":             projectSite.ShortName,
				"contacts":               projectSite.Contacts,
				"contact_group":          projectSite.ContactGroup,
				"phone":                  projectSite.Phone,
				"email":                  projectSite.Email,
				"address":                projectSite.Address,
				"country":                projectSite.Country,
				"active":                 projectSite.Active,
				"deleted":                projectSite.Deleted,
				"time_zone":              projectSite.TimeZone,
				"tz":                     projectSite.Tz,
				"dmp_id":                 projectSite.DmpID,
				"region_id":              projectSite.RegionID,
				"batch_group_alarm_open": projectSite.BatchGroupAlarmOpen,
				"batch_group_alarm":      projectSite.BatchGroupAlarm,
				"meta": models.Meta{
					CreatedBy: data.CreatedBy,
					CreatedAt: data.CreatedAt,
					UpdatedAt: time.Duration(time.Now().Unix()),
					UpdatedBy: user.ID,
				},
			}

			isBlindRole, err := tools.IsBlindedRole(projectSiteParameter.RoleID)
			if err != nil {
				return nil, err
			}
			if !isBlindRole {
				setUpdate["is_supply_ratio"] = projectSite.SupplyRatio
				setUpdate["order_application_config"] = projectSite.OrderApplicationConfig
			}
			if isBlindRole {
				setUpdate["batch_group_alarm"] = oldData.BatchGroupAlarm
			}

			update := bson.M{"$set": setUpdate}
			if _, err := collection.UpdateOne(sctx, bson.M{"_id": projectSite.ID}, update); err != nil {
				return nil, errors.WithStack(err)
			}
			OID := projectSiteParameter.EnvironmentID
			err = insertProjectSiteLog(ctx, sctx, OID, 2, oldData, projectSite, "")
			if err != nil {
				return nil, err
			}

			if oldData.TimeZone != projectSite.TimeZone {
				err = task.UpdateNotice(sctx, 3, projectSite.EnvironmentID, primitive.NilObjectID, projectSite.ID, primitive.NilObjectID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			// 添加tz和time_zone字段表
			if len(projectSite.Tz) > 0 {
				e := UpdateTimeZone(ctx, projectSite.Tz)
				if e != nil {
					return nil, errors.WithStack(e)
				}
			}

		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 添加tz和time_zone字段表
func UpdateTimeZone(ctx *gin.Context, tz string) error {
	float, err := tools.GetLocationFloat(tz)
	if err != nil {
		return errors.WithStack(err)
	}
	timeZoneStr := fmt.Sprintf("%.10g", float)
	if count, _ := tools.Database.Collection("time_zone").CountDocuments(ctx, bson.M{"tz": tz}); count > 0 {
		if _, err := tools.Database.Collection("time_zone").UpdateMany(ctx, bson.M{"tz": tz}, bson.M{"$set": bson.M{"time_zone": timeZoneStr}}); err != nil {
			return errors.WithStack(err)
		}
	} else {
		timeZone := models.TimeZone{
			ID:       primitive.NewObjectID(),
			TimeZone: timeZoneStr,
			Tz:       tz,
		}
		_, err = tools.Database.Collection("time_zone").InsertOne(ctx, timeZone)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func insertProjectSiteLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old models.ProjectSite, projectSite models.ProjectSite, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.project_site",
				TranKey: "operation_log.project_site.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.number",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.Number,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: projectSite.Number,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.name",
			Old: models.OperationLogField{
				Type:    30,
				Value:   old.Name,
				ENValue: old.NameEn,
			},
			New: models.OperationLogField{
				Type:    30,
				Value:   projectSite.Name,
				ENValue: projectSite.NameEn,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.shortName",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.ShortName,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: projectSite.ShortName,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.country",
			Old: models.OperationLogField{
				Type:  3,
				Value: old.Country,
			},
			New: models.OperationLogField{
				Type:  3,
				Value: projectSite.Country,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Deleted,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: projectSite.Deleted,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_configure",
			TranKey: "operation_log.drug_configure.supplyRatio",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.SupplyRatio,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: projectSite.SupplyRatio,
			},
		})

		var oldOrderApplicationCongfig string
		var newOrderApplicationCongfig string
		for _, config := range old.OrderApplicationConfig {
			for _, v := range config.DrugNames {
				oldOrderApplicationCongfig = oldOrderApplicationCongfig + fmt.Sprintf("%s-%d,", v.DrugName, v.Number)
			}
			oldOrderApplicationCongfig = oldOrderApplicationCongfig[:len(oldOrderApplicationCongfig)-1] + ";"
		}
		for _, mixedPackage := range projectSite.OrderApplicationConfig {
			for _, v := range mixedPackage.DrugNames {
				newOrderApplicationCongfig = newOrderApplicationCongfig + fmt.Sprintf("%s-%d,", v.DrugName, v.Number)
			}
			newOrderApplicationCongfig = newOrderApplicationCongfig[:len(newOrderApplicationCongfig)-1] + ";"
		}

		if oldOrderApplicationCongfig != newOrderApplicationCongfig {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.orderApplictionConfig",
				Old: models.OperationLogField{
					Type:  7,
					Value: oldOrderApplicationCongfig,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: newOrderApplicationCongfig,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.active",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Active,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: projectSite.Active,
			},
		})

		//查询供应计划名称
		var oldSupplyPlan models.SupplyPlan
		tools.Database.Collection("supply_plan").FindOne(sctx, bson.M{"_id": old.SupplyPlanID}).Decode(&oldSupplyPlan)
		var newSupplyPlan models.SupplyPlan
		tools.Database.Collection("supply_plan").FindOne(sctx, bson.M{"_id": projectSite.SupplyPlanID}).Decode(&newSupplyPlan)

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.supplyPlan",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlan.SupplyPlanInfo.Name,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlan.SupplyPlanInfo.Name,
			},
		})

		//查询区域
		var oldRegion models.Region
		tools.Database.Collection("region").FindOne(sctx, bson.M{"_id": old.RegionID}).Decode(&oldRegion)
		var newRegion models.Region
		tools.Database.Collection("region").FindOne(sctx, bson.M{"_id": projectSite.RegionID}).Decode(&newRegion)

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.region",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldRegion.Name,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newRegion.Name,
			},
		})

		//查询仓库名称
		var oldProjectStorehouse models.ProjectStorehouse
		var oldStorehouse models.Storehouse
		if len(old.StoreHouseID) > 0 {
			tools.Database.Collection("project_storehouse").FindOne(sctx, bson.M{"_id": old.StoreHouseID[0]}).Decode(&oldProjectStorehouse)
			tools.Database.Collection("storehouse").FindOne(sctx, bson.M{"_id": oldProjectStorehouse.StorehouseID}).Decode(&oldStorehouse)
		}

		var newProjectStorehouse models.ProjectStorehouse
		var newStorehouse models.Storehouse
		if len(projectSite.StoreHouseID) > 0 {
			tools.Database.Collection("project_storehouse").FindOne(sctx, bson.M{"_id": projectSite.StoreHouseID[0]}).Decode(&newProjectStorehouse)
			tools.Database.Collection("storehouse").FindOne(sctx, bson.M{"_id": newProjectStorehouse.StorehouseID}).Decode(&newStorehouse)
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.storehouse",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldStorehouse.Name,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newStorehouse.Name,
			},
		})

		tmpOperationLogFieldGroups := batchGroupSiteAndDepot(old.BatchGroupAlarm, projectSite.BatchGroupAlarm)
		OperationLogFieldGroups = append(OperationLogFieldGroups, tmpOperationLogFieldGroups...)

		oldGroupStr := ""
		for _, old := range old.ContactGroup {
			oldGroupStr = oldGroupStr + fmt.Sprintf("%s,%s,%s,%s;", old.Contacts, old.Phone, old.Email, old.Address)
		}

		newGroupStr := ""
		for _, new := range projectSite.ContactGroup {
			newGroupStr = newGroupStr + fmt.Sprintf("%s,%s,%s,%s;", new.Contacts, new.Phone, new.Email, new.Address)
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.contacts",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldGroupStr,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newGroupStr,
			},
		})

	}
	marks := []models.Mark{}
	if types != 1 {
		marks = append(marks,
			models.Mark{
				Label: "operation_log.label.site",
				Value: projectSite.Number,
				Blind: false,
			})
	}

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_site", OID, types, OperationLogFieldGroups, marks, projectSite.ID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *ProjectSiteService) GetSitesAndStorehouses(ctx *gin.Context, customerID string, projectID string, envID string) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	status := ctx.Query("status")

	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}

	if status != "" {
		match["deleted"] = 2
	}

	var storeHouseData []map[string]interface{}
	storeHousePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "storehouse",
		}}},
		{{
			Key: "$project",
			Value: bson.M{
				"value":   "$_id",
				"_id":     0,
				"deleted": 1,
				"label": bson.M{
					"$arrayElemAt": bson.A{"$storehouse.name", 0},
				},
			},
		}},
	}
	cursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, storeHousePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &storeHouseData)
	if err != nil {
		return nil, errors.WithStack(err)

	}

	var siteData []map[string]interface{}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$sort", Value: bson.D{{Key: "number", Value: 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"value":   "$_id",
				"_id":     0,
				"deleted": 1,
				"label":   bson.M{"$concat": bson.A{"$number", "-", models.ProjectSiteNameBson(ctx)}},
			},
		}},
	}
	siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = siteCursor.All(nil, &siteData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return map[string]interface{}{"site": siteData, "storehouse": storeHouseData}, nil
}

func (s *ProjectSiteService) GetProjectSiteById(ctx *gin.Context, customerID string, projectID string, envID string, projectSiteID string) (models.ProjectSite, error) {
	projectSiteOID, _ := primitive.ObjectIDFromHex(projectSiteID)
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteOID}).Decode(&projectSite)
	if err != nil {
		return projectSite, errors.WithStack(err)
	}
	// //只返回供应比例开放的药物
	// isBlindDrugMap, _ := tools.IsBlindDrugMap(projectSite.EnvironmentID)
	// if projectSite.SupplyRatio && len(projectSite.OrderApplicationConfig) > 0 {
	// 	var newApplicationConfig []models.ApplicationConfig
	// 	for _, config := range projectSite.OrderApplicationConfig {
	// 		var newDrugNames []models.OrderDrugNames
	// 		for _, drugName := range config.DrugNames {
	// 			blind := isBlindDrugMap[drugName.DrugName]
	// 			if !blind {
	// 				newDrugNames = append(newDrugNames, drugName)
	// 			}
	// 		}
	// 		if len(newDrugNames) > 0 {
	// 			applicationConfig := models.ApplicationConfig{
	// 				DrugNames: newDrugNames,
	// 			}
	// 			newApplicationConfig = append(newApplicationConfig, applicationConfig)
	// 		}
	// 	}
	// 	projectSite.OrderApplicationConfig = newApplicationConfig
	// }
	return projectSite, nil
}

func (s *ProjectSiteService) GetBatchUserSites(ctx *gin.Context, customerID string, envID string) ([]map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var siteData []map[string]interface{}
	match := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"deleted":     2,
	}
	sort := bson.D{{"number", 1}}
	project := bson.M{
		"value": "$_id",
		"_id":   0,

		"label": bson.M{"$concat": bson.A{"$number", "-", models.ProjectSiteNameBson(ctx)}},
	}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$sort", Value: sort}},
		{{Key: "$project", Value: project}},
	}
	siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = siteCursor.All(nil, &siteData)
	if err != nil {
		return nil, errors.WithStack(err)

	}

	for _, site := range siteData {
		site["checked"] = false
	}

	return siteData, nil
}

func (s *ProjectSiteService) GetUserSitesAndStorehouses(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)

	//判断当前登录的角色的分类
	var role models.RolePermission
	err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&role)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	siteQuery := false
	depotQuery := false
	if role.Scope == "study" {
		//如果当前角色的分类是study，所有的仓库和中心都可以查看
		siteQuery = true
		depotQuery = true
	} else if role.Scope == "depot" {
		//如果当前角色的分类是depot,查看该用户分配的仓库
		depotQuery = true
	} else if role.Scope == "site" {
		//如果当前角色的分类是site，查看该用户分配的中心
		siteQuery = true
	}

	//当前操作
	user, _ := tools.Me(ctx)
	userOID := user.ID

	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
		"user_id":     userOID,
	}

	storeHouseData := make([]map[string]interface{}, 0)

	if depotQuery {
		storeHousePipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_storehouse",
				"localField":   "depot_id",
				"foreignField": "_id",
				"as":           "projectStorehouse",
			}}},
			{{
				Key: "$project",
				Value: bson.M{
					"_id":               0,
					"id":                "$_id",
					"customer_id":       1,
					"project_id":        1,
					"env_id":            1,
					"depot_id":          1,
					"projectStorehouse": bson.M{"$first": "$projectStorehouse"},
				},
			}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "storehouse",
				"localField":   "projectStorehouse.storehouse_id",
				"foreignField": "_id",
				"as":           "storehouse",
			}}},
			{{
				Key: "$project",
				Value: bson.M{
					"id":                1,
					"customer_id":       1,
					"project_id":        1,
					"env_id":            1,
					"depot_id":          1,
					"storehouse":        bson.M{"$first": "$storehouse"},
					"projectStorehouse": 1,
				},
			}},
			{{
				Key: "$project",
				Value: bson.M{
					"value":   "$depot_id",
					"label":   "$storehouse.name",
					"deleted": "$projectStorehouse.deleted",
				},
			}},
		}
		cursor, err := tools.Database.Collection("user_depot").Aggregate(nil, storeHousePipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &storeHouseData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	siteData := make([]map[string]interface{}, 0)
	if siteQuery {
		sitePipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_site",
				"localField":   "site_id",
				"foreignField": "_id",
				"as":           "project_site",
			}}},
			{{
				Key: "$project",
				Value: bson.M{
					"_id":          0,
					"id":           "$_id",
					"customer_id":  1,
					"project_id":   1,
					"env_id":       1,
					"site_id":      1,
					"project_site": bson.M{"$first": "$project_site"},
				},
			}},
			{{
				Key: "$project",
				Value: bson.M{
					"value": "$site_id",

					//"label":   models.ProjectSiteNameLookUpBson(ctx),
					"label":   bson.M{"$concat": bson.A{"$project_site.number", "-", models.ProjectSiteNameLookUpBson(ctx)}},
					"deleted": "$project_site.deleted",
				},
			}},
		}
		siteCursor, err := tools.Database.Collection("user_site").Aggregate(nil, sitePipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = siteCursor.All(nil, &siteData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	return map[string]interface{}{"site": siteData, "storehouse": storeHouseData}, nil
}

func (s *ProjectSiteService) GetUserSites(ctx *gin.Context, customerID string, envID string, userID string) ([]map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	userOID, _ := primitive.ObjectIDFromHex(userID)

	var siteData []map[string]interface{}
	match := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"deleted":     2,
	}
	sort := bson.D{{"number", 1}}
	project := bson.M{
		"value": "$_id",
		"_id":   0,

		"label": bson.M{"$concat": bson.A{"$number", "-", models.ProjectSiteNameBson(ctx)}},
	}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$sort", Value: sort}},
		{{Key: "$project", Value: project}},
	}
	siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = siteCursor.All(nil, &siteData)
	if err != nil {
		return nil, errors.WithStack(err)

	}

	siteMatch := bson.M{"customer_id": customerOID, "env_id": envOID, "user_id": userOID}
	var selectSites []models.UserSite
	cursor, err := tools.Database.Collection("user_site").Find(nil, siteMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := cursor.All(nil, &selectSites); err != nil {
		return nil, errors.WithStack(err)
	}

	for _, site := range siteData {
		var checked = false
		for _, selectedSite := range selectSites {
			if selectedSite.SiteID == site["value"] {
				checked = true
				break
			}
		}
		site["checked"] = checked
	}

	return siteData, nil
}

func (s *ProjectSiteService) GetUserStorehouses(ctx *gin.Context, customerID string, envID string, userID string) ([]map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	userOID, _ := primitive.ObjectIDFromHex(userID)

	match := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"deleted":     2,
	}

	var storeHouseData []map[string]interface{}
	storeHousePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "storehouse",
		}}},
		{{
			Key: "$project",
			Value: bson.M{
				"value": "$_id",
				"_id":   0,
				"label": bson.M{
					"$arrayElemAt": bson.A{"$storehouse.name", 0},
				},
			},
		}},
	}
	cursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, storeHousePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &storeHouseData)
	if err != nil {
		return nil, errors.WithStack(err)

	}

	depotMatch := bson.M{"customer_id": customerOID, "env_id": envOID, "user_id": userOID}
	var selectDepots []models.UserDepot
	depotCursor, depotErr := tools.Database.Collection("user_depot").Find(nil, depotMatch)
	if depotErr != nil {
		return nil, errors.WithStack(err)
	}
	if err := depotCursor.All(nil, &selectDepots); err != nil {
		return nil, errors.WithStack(err)
	}

	for _, site := range storeHouseData {
		var checked = false
		for _, selectedSite := range selectDepots {
			if selectedSite.DepotID == site["value"] {
				checked = true
				break
			}
		}
		site["checked"] = checked
	}

	return storeHouseData, nil
}

func (s *ProjectSiteService) GetBatchUserStorehouses(ctx *gin.Context, customerID string, envID string) ([]map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	match := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"deleted":     2,
	}

	var storeHouseData []map[string]interface{}
	storeHousePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "storehouse",
		}}},
		{{
			Key: "$project",
			Value: bson.M{
				"value": "$_id",
				"_id":   0,
				"label": bson.M{
					"$arrayElemAt": bson.A{"$storehouse.name", 0},
				},
			},
		}},
	}
	cursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, storeHousePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &storeHouseData)
	if err != nil {
		return nil, errors.WithStack(err)

	}

	for _, site := range storeHouseData {
		site["checked"] = false
	}

	return storeHouseData, nil
}

func (s *ProjectSiteService) UpdateSupplyPlan(ctx *gin.Context, id string, supplyId string, envId string, cohortId string) error {
	siteOID, _ := primitive.ObjectIDFromHex(id)
	supplyPlanOID, _ := primitive.ObjectIDFromHex(supplyId)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortId)
	match := bson.M{
		"_id": siteOID,
	}
	update := bson.M{"$set": bson.M{
		"supply_plan_id": supplyPlanOID,
	}}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var oldData models.ProjectSite
		queryMatch := bson.M{
			"_id": siteOID,
		}
		err := tools.Database.Collection("project_site").FindOne(nil, queryMatch).Decode(&oldData)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if _, err := tools.Database.Collection("project_site").UpdateOne(nil, match, update); err != nil {
			return nil, errors.WithStack(err)
		}
		var OID = primitive.NilObjectID
		if cohortOID == primitive.NilObjectID {
			OID = envOID
		} else {
			OID = cohortOID
		}
		var OperationLogFieldGroups []models.OperationLogFieldGroup
		//查询供应计划名称
		var oldSupplyPlan models.SupplyPlan
		err = tools.Database.Collection("supply_plan").FindOne(sctx, bson.M{"_id": oldData.SupplyPlanID}).Decode(&oldSupplyPlan)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var newSupplyPlan models.SupplyPlan
		err = tools.Database.Collection("supply_plan").FindOne(sctx, bson.M{"_id": supplyPlanOID}).Decode(&newSupplyPlan)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_site",
			TranKey: "operation_log.project_site.supplyPlan",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlan.SupplyPlanInfo.Name,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlan.SupplyPlanInfo.Name,
			},
		})
		marks := []models.Mark{}
		marks = append(marks,
			models.Mark{
				Label: "operation_log.label.site",
				Value: oldData.Number,
				Blind: false,
			})

		err = tools.SaveOperation(ctx, sctx, "operation_log.module.project_site", OID, 2, OperationLogFieldGroups, marks, siteOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil

}

func (s *ProjectSiteService) GetStoreListOption(ctx *gin.Context, customerID string, projectID string, envID string) ([]map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	var data []map[string]interface{}

	collection := tools.Database.Collection("project_storehouse")
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"customer_id": customerOID,
			"project_id":  projectOID,
			"env_id":      envOID,
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "storehouse",
		}}},
		{{Key: "$unwind", Value: "$storehouse"}},
		{{
			Key: "$project",
			Value: bson.M{
				"id":           "$_id",
				"_id":          0,
				"storehouseId": "$storehouse_id",
				"name":         "$storehouse.name",
				"deleted":      1,
			},
		}},
	}
	cursor, err := collection.Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return data, nil
}

func (s *ProjectSiteService) GetCountry(ctx *gin.Context) (interface{}, error) {
	envId := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envId)
	sites := make([]models.ProjectSite, 0)
	cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &sites)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	countrySites := slice.Filter(sites, func(index int, item models.ProjectSite) bool {
		return item.Country != nil && len(item.Country) > 0
	})
	countries := slice.Map(countrySites, func(index int, item models.ProjectSite) string {
		return item.Country[0]
	})
	countries = slice.Unique(countries)
	type resp struct {
		Cn   string `json:"cn"`
		En   string `json:"en"`
		Ko   string `json:"ko"`
		Code string `json:"code"`
	}
	cs := make([]resp, len(countries))
	cursor, err = tools.Database.Collection("country").Find(nil, bson.M{"code": bson.M{"$in": countries}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &cs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return cs, nil
}

func (s *ProjectSiteService) GetProjectSiteByTz(ctx *gin.Context, tz string, timeZone string) (string, error) {
	location := ""
	if tz != "" {
		loc, err := tools.GetUTCOffsetString(tz)
		if err != nil {
			return "", errors.WithStack(err)
		}
		location = loc
	} else {
		if timeZone != "" {
			location = timeZone
		}
	}

	return location, nil
}

func (s *ProjectSiteService) GetBatchGroupNum(ctx *gin.Context, projectID, envID, siteID string) (interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 组别label
	var randomDesigns []models.RandomDesign
	cursor, err := tools.Database.Collection("random_design").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomDesigns)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	type LabelValue struct {
		Value        string             `json:"value" bson:"value"`
		Label        string             `json:"label" bson:"label"`
		StorehouseID primitive.ObjectID `bson:"storehouse_id" json:"storehouseId,omitempty"`
		SiteID       primitive.ObjectID `bson:"site_id" json:"siteId,omitempty"`
	}

	attributes, err := database.GetAttributes(nil, envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	groupLabel := []LabelValue{}
	if project.Type != 1 {
		envP, ok := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == envOID
		})
		if ok {
			env := *envP
			for _, cohort := range env.Cohorts {

				_, open := slice.Find(attributes, func(index int, item models.Attribute) bool {
					return item.AttributeInfo.IPInheritance && cohort.ID == item.CohortID
				})
				if !open {
					continue
				}

				randomDesignP, ok := slice.Find(randomDesigns, func(index int, item models.RandomDesign) bool {
					return item.CohortID == cohort.ID
				})
				if ok {

					randomDesign := *randomDesignP
					for _, group := range randomDesign.Info.Groups {
						groupLabel = append(groupLabel, LabelValue{
							Value: cohort.Name + "-" + group.Name,
							Label: cohort.Name + "-" + group.Name,
						})
					}
				}
			}
		}
	} else {
		for _, design := range randomDesigns {
			for _, group := range design.Info.Groups {
				groupLabel = append(groupLabel, LabelValue{
					Value: group.Name,
					Label: group.Name,
				})
			}
		}
	}

	// 批次结合
	var medicineBatchLabel []LabelValue
	// 编号批次
	match := bson.M{"env_id": envOID}
	if siteID != "" {
		siteOID, _ := primitive.ObjectIDFromHex(siteID)

		match["site_id"] = siteOID
	}
	cursor, err = tools.Database.Collection("medicine").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$project", bson.M{
			"storehouse_id": bson.M{"$ifNull": bson.A{"$storehouse_id", primitive.NilObjectID}},
			"site_id":       bson.M{"$ifNull": bson.A{"$site_id", primitive.NilObjectID}},
			"batch_number":  1,
		}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"batch_number": "$batch_number", "storehouse_id": "$storehouse_id", "site_id": "$site_id"}}}},
		{{"$sort", bson.M{"batch_number": 1}}},
		{{"$project", bson.M{
			"label":         "$_id.batch_number",
			"value":         "$_id.batch_number",
			"storehouse_id": "$_id.storehouse_id",
			"site_id":       "$_id.site_id",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineBatchLabel)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 未编号批次
	var medicineOtherBatchLabel []LabelValue

	cursor, err = tools.Database.Collection("medicine_others").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$project", bson.M{
			"storehouse_id": bson.M{"$ifNull": bson.A{"$storehouse_id", primitive.NilObjectID}},
			"site_id":       bson.M{"$ifNull": bson.A{"$site_id", primitive.NilObjectID}},
			"batch_number":  1,
		}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"batch_number": "$batch_number", "storehouse_id": "$storehouse_id", "site_id": "$site_id"}}}},
		{{"$sort", bson.M{"_id.batch_number": 1}}},
		{{"$project", bson.M{
			"label":         "$_id.batch_number",
			"value":         "$_id.batch_number",
			"storehouse_id": "$_id.storehouse_id",
			"site_id":       "$_id.site_id",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineOtherBatchLabel)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, value := range medicineOtherBatchLabel {
		_, ok := slice.Find(medicineBatchLabel, func(index int, item LabelValue) bool {
			return item.SiteID == value.SiteID && value.StorehouseID == item.StorehouseID && item.Value == value.Value && value.Label == item.Label
		})
		if !ok {
			medicineBatchLabel = append(medicineBatchLabel, value)
		}
	}
	medicineBatchLabel = slice.Filter(medicineBatchLabel, func(index int, item LabelValue) bool {
		return item.Label != ""
	})
	return map[string]interface{}{
		"group": groupLabel,
		"batch": medicineBatchLabel,
	}, nil
}
