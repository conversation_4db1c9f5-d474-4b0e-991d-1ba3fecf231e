package service

import (
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type WorkTaskService struct {
	drugConfigureService DrugConfigureService
}

func (w *WorkTaskService) WorkTaskDetail(ctx *gin.Context) (map[string]interface{}, error) {
	workTaskID := ctx.Query("workTaskId")
	roleID := ctx.Query("roleId")
	workTaskOID, _ := primitive.ObjectIDFromHex(workTaskID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)
	if roleOID.IsZero() {
		return nil, tools.BuildServerError(ctx, "user_no_permission")
	}
	filter := bson.M{"_id": workTaskOID}
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOne(nil, filter).Decode(&workTask)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	switch workTask.Info.WorkType {
	case 1:
		return w.scanDetail(filter, roleOID)
	case 2:
		return w.orderDetail(ctx, filter, workTask.EnvironmentID, workTask.CohortID, roleOID)
	case 3:
		return w.orderDetail(ctx, filter, workTask.EnvironmentID, workTask.CohortID, roleOID)
	case 4:
		return w.dispensingDetail(ctx, filter, workTask.EnvironmentID, roleOID)
	case 5:
		return w.dispensingDetail(ctx, filter, workTask.EnvironmentID, roleOID)
	case 6:
		return w.orderDetail(ctx, filter, workTask.EnvironmentID, workTask.CohortID, roleOID)
	case 7:
		return w.approvalProcessDetail(ctx, filter, workTask.EnvironmentID, roleOID)
	case 8:
		return w.unblindingApprovalDetail(ctx, workTask)
	case 9:
		return w.dispensingDetail(ctx, filter, workTask.EnvironmentID, roleOID)
	case 10:
		return w.unblindingApprovalDetail(ctx, workTask)
	case 11:
		return w.sendMedicationDetails(ctx, workTask, roleOID)
	case 12:
		return w.packageScanDetail(filter, roleOID, workTask.EnvironmentID)
	case 13:
		return w.orderDetail(ctx, filter, workTask.EnvironmentID, workTask.CohortID, roleOID)
	case 14:
		return w.orderDetail(ctx, filter, workTask.EnvironmentID, workTask.CohortID, roleOID)
	case 15:
		return w.orderDetail(ctx, filter, workTask.EnvironmentID, workTask.CohortID, roleOID)
	}
	return nil, nil
}

func (w *WorkTaskService) scanDetail(filter bson.M, roleOID primitive.ObjectID) (map[string]interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project.envs", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine",
			"let": bson.M{"ids": "$info.medicine_ids"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$ids"}}}},
				bson.M{"$project": bson.M{"_id": 0, "id": "$_id", "name": 1, "number": 1, "batchNumber": "$batch_number", "status": "$status", "expirationDate": "$expiration_date"}},
			},
			"as": "medicines"}}},
		{{Key: "$project", Value: bson.M{
			"_id":           0,
			"id":            "$_id",
			"projectStatus": "$project.status",
			"projectNumber": "$project.info.number",
			"projectName":   "$project.info.name",
			"env":           "$project.envs",
			"info": bson.M{
				"workType":    "$info.work_type",
				"status":      1,
				"createdTime": "$info.created_time",
				"deadline":    1,
				"finishTime":  "$info.finish_time",
				"medicines":   "$medicines",
			},
		}}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("work_task").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := data[0]
	if !roleOID.IsZero() {
		isBlindRole, err := tools.IsBlindedRole(roleOID.Hex())
		if err != nil {
			return nil, err
		}
		if isBlindRole {
			for i := 0; i < len(result["info"].(map[string]interface{})["medicines"].(primitive.A)); i++ {
				result["info"].(map[string]interface{})["medicines"].(primitive.A)[i].(map[string]interface{})["name"] = tools.BlindData
			}
		}
	}
	return result, nil
}

func (w *WorkTaskService) packageScanDetail(filter bson.M, roleOID primitive.ObjectID, envOID primitive.ObjectID) (map[string]interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project.envs", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		{{Key: "$project", Value: bson.M{
			"_id":           0,
			"id":            "$_id",
			"projectStatus": "$project.status",
			"projectNumber": "$project.info.number",
			"projectName":   "$project.info.name",
			"env":           "$project.envs",
			"info": bson.M{
				"workType":         "$info.work_type",
				"status":           1,
				"createdTime":      "$info.created_time",
				"deadline":         1,
				"finishTime":       "$info.finish_time",
				"packageMedicines": "$info.package_medicines",
			},
		}}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("work_task").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := data[0]
	if !roleOID.IsZero() {
		_, _, _, _, mixedPackage, err := tools.IsOpenPackage(envOID)
		if err != nil {
			return nil, err
		}
		info := result["info"].(map[string]interface{})
		if info["packageMedicines"] != nil {
			packageMedicines := info["packageMedicines"].(primitive.A)
			var medicineIds []primitive.ObjectID
			for _, v := range packageMedicines {
				packMInfo := v.(map[string]interface{})
				medicines := packMInfo["medicines"].(primitive.A)
				for _, medicineId := range medicines {
					medicineIds = append(medicineIds, medicineId.(primitive.ObjectID))
				}
			}

			medicinesMap := make(map[primitive.ObjectID]models.MedicineBrief)
			medicinesInfo := make([]models.MedicineBrief, len(medicineIds))
			cursor, _ := tools.Database.Collection("medicine").Find(nil, bson.M{"_id": bson.M{"$in": medicineIds}})
			_ = cursor.All(nil, &medicinesInfo)
			for _, info := range medicinesInfo {
				medicinesMap[info.ID] = info
			}

			for index, v := range packageMedicines {
				packMInfo := v.(map[string]interface{})
				medicines := packMInfo["medicines"].(primitive.A)
				var medicinesDetail []models.MedicineBrief
				for _, medicineId := range medicines {
					medicinesDetail = append(medicinesDetail, medicinesMap[medicineId.(primitive.ObjectID)])
				}
				packMInfo["medicines"] = medicinesDetail
				packageMedicines[index] = packMInfo
			}
			info["packageMedicines"] = packageMedicines
		}
		info["mixedPackage"] = mixedPackage
		result["info"] = info
	}
	return result, nil
}

func (w *WorkTaskService) orderDetail(ctx *gin.Context, filter bson.M, envID primitive.ObjectID, cohortID primitive.ObjectID, roleOID primitive.ObjectID) (map[string]interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project.envs", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine_order", "localField": "info.medicine_order_id", "foreignField": "_id", "as": "order"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$order", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine", "localField": "order.medicines", "foreignField": "_id", "as": "medicines"}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "project_site",
			"let": bson.M{
				"receive_id": "$order.receive_id",
				"send_id":    "$order.send_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$receive_id"}}},
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$send_id"}}},
						},
					},
				},
			},
			"as": "project_site",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project_site", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "project_storehouse",
			"let": bson.M{
				"receive_id": "$order.receive_id",
				"send_id":    "$order.send_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$receive_id"}}},
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$send_id"}}},
						},
					},
				},
				bson.M{
					"$lookup": bson.M{
						"from":         "storehouse",
						"localField":   "storehouse_id",
						"foreignField": "_id",
						"as":           "storehouse",
					},
				},
				bson.M{"$unwind": "$storehouse"},
			},
			"as": "project_storehouse",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project_storehouse", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"_id":           0,
			"id":            "$_id",
			"projectStatus": "$project.status",
			"projectNumber": "$project.info.number",
			"projectName":   "$project.info.name",
			"env":           "$project.envs",
			"sendName": bson.M{"$cond": bson.M{"if": bson.M{"$eq": bson.A{"$order.send_id", "$project_site._id"}},
				"then": bson.M{"$concat": bson.A{"$project_site.number", "-", models.ProjectSiteNameLookUpBson(ctx)}},
				"else": "$project_storehouse.storehouse.name"}},
			"receiveName": bson.M{"$cond": bson.M{"if": bson.M{"$eq": bson.A{"$order.receive_id", "$project_site._id"}},
				"then": bson.M{"$concat": bson.A{"$project_site.number", "-", models.ProjectSiteNameLookUpBson(ctx)}},
				"else": "$project_storehouse.storehouse.name"}},
			"orderId":             "$order._id",
			"orderNumber":         "$order.order_number",
			"orderStatus":         "$order.status",
			"orderType":           "$order.type",
			"subjectId":           "$order.subject_id",
			"mode":                "$order.mode",
			"medicineIds":         "$order.medicines",
			"medicines":           "$medicines",
			"otherMedicinesNew":   "$order.other_medicines_new",
			"medicinesPackage":    "$order.medicines_package",
			"logisticsInfo":       "$order.logistics_info",
			"expectedArrivalTime": "$order.expected_arrival_time",
			"info": bson.M{
				"workType":    "$info.work_type",
				"status":      1,
				"deadline":    1,
				"createdTime": "$info.created_time",
				"finishTime":  "$info.finish_time",
			},
		}}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("work_task").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := data[0]
	//物流
	if result["logisticsInfo"] != nil {
		logisticsInfo := result["logisticsInfo"].(map[string]interface{})
		code := logisticsInfo["logistics"].(string)
		var logisticsCompanyCode models.LogisticsCompanyCode
		if code != "" {
			if code != "qita" {
				// 设置查询条件
				filter := bson.M{"code": code}
				err := tools.Database.Collection("logistics_company_code").FindOne(ctx, filter).Decode(&logisticsCompanyCode)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				logisticsInfo["logisticsName"] = logisticsCompanyCode.Name
			} else {
				logisticsInfo["logisticsName"] = logisticsInfo["other"].(string)
			}
		}
	}
	//目的地
	if result["orderType"].(int32) == 5 || result["orderType"].(int32) == 6 {
		//查询受试者
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": result["subjectId"]}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 受试者号
		for _, sbj := range subject.Info {
			if sbj.Name == "shortname" {
				result["receiveName"] = sbj.Value.(string)
				break
			}
		}
	} else {
		if len(data) > 1 {
			if data[1]["sendName"] != nil {
				result["sendName"] = data[1]["sendName"]
			}
		}
	}

	//该订单的运送方式
	packageNames := make([]string, 0)
	packageDrugNames := make(map[string]models.MedicinePackage)
	packageIsOpen := false
	if result["medicinesPackage"] != nil {
		medicinesPackage := result["medicinesPackage"].(primitive.A)
		for _, medicinePackage := range medicinesPackage {
			medicine := medicinePackage.(map[string]interface{})
			if medicine["package_method"].(bool) {
				packageIsOpen = true
				packageNames = append(packageNames, medicine["name"].(string))
			}
			packageDrugNames[medicine["name"].(string)] = models.MedicinePackage{
				Name:          medicine["name"].(string),
				PackageMethod: medicine["package_method"].(bool),
				PackageNumber: int(medicine["package_number"].(int32)),
			}
		}
	}

	//处理未编号数据
	if result["otherMedicinesNew"] != nil && len(result["otherMedicinesNew"].(primitive.A)) > 0 {
		otherMedicines := result["otherMedicinesNew"].(primitive.A)
		if len(otherMedicines) != 0 && otherMedicines[0] != nil {
			var groupOtherMedicineData []map[string]interface{}
			//单品数据
			groupPipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": otherMedicines}, "name": bson.M{"$nin": packageNames}}}},
				{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "status": "$order_old_status"}, "availableCount": bson.M{"$sum": 1}}}},
				{{Key: "$project", Value: bson.M{"_id": 0,
					"name":        "$_id.name",
					"batch":       "$_id.batchNumber",
					"expire_date": "$_id.expirationDate",
					"use_count":   "$availableCount",
					"status":      "$_id.status",
				}}},
			}
			cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
			err = cursor.All(nil, &groupOtherMedicineData)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, other := range groupOtherMedicineData {
				name := other["name"].(string)
				if packageDrugNames[name].PackageMethod {
					other["package_method"] = true
				} else {
					other["package_method"] = false
				}
			}
			//包装数据
			if len(packageNames) > 0 {
				var packageOtherMedicineData []map[string]interface{}
				//判断药物列表是否有数据
				groupPipeline := mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": otherMedicines}, "name": bson.M{"$in": packageNames}}}},
					{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "batchNumber": "$batch_number", "expirationDate": "$expiration_date", "packageNumber": "$package_number", "status": "$order_old_status"}}}},
					{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$_id.name", "batchNumber": "$_id.batchNumber", "expirationDate": "$_id.expirationDate", "status": "$_id.status"}, "availableCount": bson.M{"$sum": 1}}}},
					{{Key: "$project", Value: bson.M{"_id": 0,
						"name":        "$_id.name",
						"batch":       "$_id.batchNumber",
						"expire_date": "$_id.expirationDate",
						"use_count":   "$availableCount",
						"status":      "$_id.status",
					}}},
				}
				cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, groupPipeline)
				err = cursor.All(nil, &packageOtherMedicineData)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, other := range packageOtherMedicineData {
					name := other["name"].(string)
					if packageDrugNames[name].PackageMethod {
						other["package_method"] = true
						other["packaged_count"] = packageDrugNames[name].PackageNumber
					} else {
						other["package_method"] = false
					}
				}
				groupOtherMedicineData = append(groupOtherMedicineData, packageOtherMedicineData...)
			}

			result["otherMedicines"] = groupOtherMedicineData
		}
	}

	if !roleOID.IsZero() {
		isBlindedDrug, _ := tools.IsBlindDrugMap(envID)
		//如果是盲法项目，判断他是否有权限查看，如果没有权限查看，打码显示
		isBlindedRole, err := tools.IsBlindedRole(roleOID.Hex())
		if err != nil {
			return nil, err
		}

		//包装运送
		//packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envID)
		if result["medicines"] != nil {
			orderMedicines := result["medicines"].(primitive.A)
			//该订单的运送方式
			if result["medicinesPackage"] != nil && packageIsOpen {
				var newMedicines []map[string]interface{}
				hasData := make(map[string]string)
				//查询字段不为空的时候，包装方式打开并在设置中配置，需要去把这个包装中的所有数据查询出来
				for _, med := range orderMedicines {
					medicine := med.(map[string]interface{})
					name := medicine["name"].(string)
					packageSetting, ok := packageDrugNames[name]
					hasPackageNumber, hasOk := hasData[name]
					packageNumber := ""
					if medicine["package_number"] != nil {
						packageNumber = medicine["package_number"].(string)
					}
					//判断这个订单是按照包装还是单品
					if ok && packageSetting.PackageMethod {
						if !hasOk || (hasOk && hasPackageNumber != packageNumber) {
							hasData[name] = packageNumber
							medicinesData := []map[string]interface{}{}
							medicineFilter := bson.M{"env_id": envID, "name": name, "package_number": packageNumber, "_id": bson.M{"$in": result["medicineIds"]}}
							pipepine := mongo.Pipeline{
								{{Key: "$match", Value: medicineFilter}},
							}
							cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							err = cursor.All(nil, &medicinesData)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							newMedicines = append(newMedicines, medicinesData...)
						}
					} else {
						newMedicines = append(newMedicines, medicine)
					}
				}

				//orderMedicines = newMedicines

				sort.SliceStable(newMedicines, func(i int, j int) bool {
					datai := newMedicines[i]
					dataj := newMedicines[j]
					if datai["name"].(string) < dataj["name"].(string) {
						return true
					}

					if datai["name"].(string) > dataj["name"].(string) {
						return false
					}

					if datai["package_number"] != nil && dataj["package_number"] != nil {
						if datai["package_number"].(string) < dataj["package_number"].(string) {
							return true
						}

						if datai["package_number"].(string) > dataj["package_number"].(string) {
							return false
						}
					} else {
						return false
					}
					return datai["number"].(string) < dataj["number"].(string)
				})

				for _, med := range newMedicines {
					name := med["name"].(string)
					if isBlindedDrug[name] && isBlindedRole {
						med["name"] = tools.BlindData
					}
					if packageDrugNames[name].PackageMethod {
						med["packageDrug"] = true
					} else {
						med["packageDrug"] = false
					}
					if med["batch_number"] == nil || med["batch_number"].(string) == "" {
						med["batch_number"] = "-"
					}
					if med["expiration_date"] == nil || med["expiration_date"].(string) == "" {
						med["expiration_date"] = "-"
					}
				}
				result["medicines"] = newMedicines
			} else {
				for _, med := range orderMedicines {
					name := med.(map[string]interface{})["name"].(string)
					if isBlindedDrug[name] && isBlindedRole {
						med.(map[string]interface{})["name"] = tools.BlindData
					}
					if med.(map[string]interface{})["batch_number"] == nil || med.(map[string]interface{})["batch_number"].(string) == "" {
						med.(map[string]interface{})["batch_number"] = "-"
					}
					if med.(map[string]interface{})["expiration_date"] == nil || med.(map[string]interface{})["expiration_date"].(string) == "" {
						med.(map[string]interface{})["expiration_date"] = "-"
					}
				}
			}
		} else {
			result["medicines"] = nil
		}

		if result["otherMedicines"] != nil && len(result["otherMedicines"].([]map[string]interface{})) > 0 {
			otherMedicines := result["otherMedicines"].([]map[string]interface{})
			for _, other := range otherMedicines {
				name := other["name"].(string)
				if isBlindedDrug[name] && isBlindedRole {
					encrypted, salt := tools.Encrypt(name)
					other["name"] = tools.BlindData
					other["salt"] = salt
					other["saltName"] = encrypted
				}
				if other["batch"] == nil || other["batch"].(string) == "" {
					other["batch"] = "-"
				}
				if other["expire_date"] == nil || other["expire_date"].(string) == "" {
					other["expire_date"] = "-"
				}
			}
		} else {
			result["otherMedicines"] = nil
		}

		result["medicinesPackage"] = nil

	}
	barcode_rule_filter := bson.M{"env_id": envID}
	if cohortID != primitive.NilObjectID {
		barcode_rule_filter["cohort_id"] = cohortID
	}
	var barcodeRule models.BarcodeRule
	err = tools.Database.Collection("barcode_rule").FindOne(nil, barcode_rule_filter).Decode(&barcodeRule)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	result["code_rule"] = barcodeRule.CodeRule
	return result, nil
}

func (w *WorkTaskService) approvalProcessDetail(ctx *gin.Context, filter bson.M, envID primitive.ObjectID, roleOID primitive.ObjectID) (map[string]interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project.envs", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		{{Key: "$lookup", Value: bson.M{"from": "approval_process", "localField": "info.approval_process_id", "foreignField": "_id", "as": "approvalProcess"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$approvalProcess", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "project_site",
			"let": bson.M{
				"receive_id": "$approvalProcess.data.receive_id",
				"send_id":    "$approvalProcess.data.send_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$receive_id"}}},
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$send_id"}}},
						},
					},
				},
			},
			"as": "project_site",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project_site", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "project_storehouse",
			"let": bson.M{
				"receive_id": "$approvalProcess.data.receive_id",
				"send_id":    "$approvalProcess.data.send_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$receive_id"}}},
							bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$send_id"}}},
						},
					},
				},
				bson.M{
					"$lookup": bson.M{
						"from":         "storehouse",
						"localField":   "storehouse_id",
						"foreignField": "_id",
						"as":           "storehouse",
					},
				},
				bson.M{"$unwind": "$storehouse"},
			},
			"as": "project_storehouse",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project_storehouse", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{"from": "supply_plan", "localField": "approvalProcess.data.supply_id", "foreignField": "_id", "as": "supplyPlan"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$supplyPlan", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"_id":           0,
			"id":            "$_id",
			"projectStatus": "$project.status",
			"projectNumber": "$project.info.number",
			"projectName":   "$project.info.name",
			"env":           "$project.envs",
			"contacts":      "$approvalProcess.data.contacts",
			"phone":         "$approvalProcess.data.phone",
			"email":         "$approvalProcess.data.email",
			"address":       "$approvalProcess.data.address",
			"sendName": bson.M{"$cond": bson.M{"if": bson.M{"$eq": bson.A{"$approvalProcess.data.send_id", "$project_site._id"}},
				"then": bson.M{"$concat": bson.A{"$project_site.number", "-", models.ProjectSiteNameLookUpBson(ctx)}},
				"else": "$project_storehouse.storehouse.name"}},
			"receiveName": bson.M{"$cond": bson.M{"if": bson.M{"$eq": bson.A{"$approvalProcess.data.receive_id", "$project_site._id"}},
				"then": bson.M{"$concat": bson.A{"$project_site.number", "-", models.ProjectSiteNameLookUpBson(ctx)}},
				"else": "$project_storehouse.storehouse.name"}},
			"mode":            "$approvalProcess.data.mode",
			"drugNames":       "$approvalProcess.data.drugNames",
			"supplyName":      "$supplyPlan.info.name",
			"detailData":      "$approvalProcess.data.detail_data",
			"otherDetailData": "$approvalProcess.data.other_detail_data",
			"number":          "$approvalProcess.number",
			//"name":            "$approvalProcess.name",
			"approvalStatus":      "$approvalProcess.approval_status",
			"expectedArrivalTime": "$approvalProcess.data.expected_arrival_time",
			"blindCount":          "$approvalProcess.data.blind_count",
			"reason":              "$approvalProcess.reason",
			"openDrugCount":       "$approvalProcess.data.open_drug_count",
			"info": bson.M{
				"workType":    "$info.work_type",
				"status":      1,
				"deadline":    1,
				"createdTime": "$info.created_time",
				"finishTime":  "$info.finish_time",
				"medicines":   "$medicines",
			},
		}}},
	}

	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("work_task").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := data[0]
	if !roleOID.IsZero() {
		isBlindRole, err := tools.IsBlindedRole(roleOID.Hex())
		if err != nil {
			return nil, err
		}
		if isBlindRole {
			//研究产品名称
			drugNames := result["drugNames"].(primitive.A)
			for i := 0; i < len(drugNames); i++ {
				isBlindedDrug, _ := tools.IsBlindedDrug(envID, drugNames[i].(string))
				if isBlindedDrug {
					drugNames[i] = tools.BlindData
				}
			}
			//编号研究产品
			detailDatas := result["detailData"].(primitive.A)
			for i := 0; i < len(detailDatas); i++ {
				detailData := detailDatas[i].(map[string]interface{})
				isBlindedDrug, _ := tools.IsBlindedDrug(envID, detailData["name"].(string))
				if isBlindedDrug {
					detailData["name"] = tools.BlindData
					detailData["count"] = tools.BlindData
					detailData["use_count"] = tools.BlindData
					detailDatas[i] = detailData
				}
			}

			//未编号研究产品
			otherDetailDatas := result["otherDetailData"].(primitive.A)
			for i := 0; i < len(otherDetailDatas); i++ {
				otherDetailData := otherDetailDatas[i].(map[string]interface{})
				isBlindedDrug, _ := tools.IsBlindedDrug(envID, otherDetailData["name"].(string))
				if isBlindedDrug {
					otherDetailData["name"] = tools.BlindData
					otherDetailData["count"] = tools.BlindData
					otherDetailData["use_count"] = tools.BlindData
					otherDetailDatas[i] = otherDetailData
				}
			}
		}
	}

	return result, nil
}

func (w *WorkTaskService) unblindingApprovalDetail(ctx *gin.Context, workTask models.WorkTask) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	// 查询项目和环境
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result["projectStatus"] = project.Status
	result["projectName"] = project.Name
	result["projectNumber"] = project.Number
	environment := models.Environment{}
	for _, env := range project.Environments {
		if env.ID == workTask.EnvironmentID {
			environment = env
			result["env"] = env.Name
			break
		}
	}
	//查询受试者
	var subject models.Subject
	subjectID := workTask.Info.SubjectApproval.SubjectID
	if workTask.Info.WorkType == 10 { // pv揭盲
		subjectID = workTask.Info.SubjectPvApproval.SubjectID
	}
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 受试者号
	for _, sbj := range subject.Info {
		if sbj.Name == "shortname" {
			result["subjectNo"] = sbj.Value
			break
		}
	}
	// 查询项目属性ID
	attributeFilter := bson.M{
		"customer_id": subject.CustomerID,
		"project_id":  subject.ProjectID,
		"env_id":      subject.EnvironmentID,
	}

	cohortName := ""
	if !subject.CohortID.IsZero() {
		for _, cohort := range environment.Cohorts {
			if cohort.ID == subject.CohortID {
				cohortName = cohort.Name
				break
			}
		}
		attributeFilter["cohort_id"] = subject.CohortID
	}
	result["cohortName"] = cohortName

	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	// 随机号
	result["randomNumber"] = subject.RandomNumber
	if !attribute.AttributeInfo.IsRandomNumber {
		result["randomNumber"] = tools.BlindData
	}
	result["label"] = subjectReplaceText
	// 查询中心
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result["siteName"] = projectSite.Number + "-" + projectSite.Name
	// 审批编号
	result["number"] = workTask.Info.SubjectApproval.Number
	if workTask.Info.WorkType == 10 { // pv揭盲
		result["number"] = workTask.Info.SubjectPvApproval.Number
	}
	// 申请原因
	if workTask.Info.WorkType == 8 { // 紧急揭盲
		for _, uua := range subject.UrgentUnblindingApprovals {
			if uua.Number == workTask.Info.SubjectApproval.Number {
				result["reason"] = uua.ReasonStr + " " + uua.Remark
				result["approvalStatus"] = uua.Status
				result["rejectReason"] = uua.RejectReason
				break
			}
		}
	}
	if workTask.Info.WorkType == 10 { // pv揭盲
		for _, puua := range subject.PvUrgentUnblindingApprovals {
			if puua.Number == workTask.Info.SubjectPvApproval.Number {
				result["reason"] = puua.ReasonStr + " " + puua.Remark
				result["approvalStatus"] = puua.Status
				result["rejectReason"] = puua.RejectReason
				break
			}
		}
	}
	// 项目属性ID
	result["attributeId"] = attribute.ID
	// 受试者ID
	result["subjectId"] = subject.ID
	// 任务状态
	result["status"] = workTask.Info.Status
	// 预期完成时间
	result["deadline"] = workTask.Info.Deadline
	// 开始时间
	result["createdTime"] = workTask.Info.CreatedTime
	// 任务类型
	result["workType"] = workTask.Info.WorkType
	return result, nil
}

func (w *WorkTaskService) sendMedicationDetails(ctx *gin.Context, workTask models.WorkTask, roleOID primitive.ObjectID) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	// 查询项目和环境
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envName := ""
	for _, env := range project.Environments {
		if env.ID == workTask.EnvironmentID {
			envName = env.Name
			break
		}
	}

	// 查询发药信息
	var dispensing models.Dispensing
	err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": workTask.Info.DispensingID}).Decode(&dispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//查询受试者
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 受试者号
	for _, sbj := range subject.Info {
		if sbj.Name == "shortname" {
			result["subjectNo"] = sbj.Value
			break
		}
	}
	// 查询项目属性ID
	attributeFilter := bson.M{
		"customer_id": subject.CustomerID,
		"project_id":  subject.ProjectID,
		"env_id":      subject.EnvironmentID,
	}
	if !subject.CohortID.IsZero() {
		attributeFilter["cohort_id"] = subject.CohortID
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	// 随机号
	result["randomNumber"] = subject.RandomNumber
	if !attribute.AttributeInfo.IsRandomNumber {
		result["randomNumber"] = tools.BlindData
	}

	// 查询中心
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	isBlindRole := false
	if !roleOID.IsZero() {
		isBlindRole, err = tools.IsBlindedRole(roleOID.Hex())
		if err != nil {
			return nil, err
		}
	}

	// 已编号药物
	var dispensingMedicines []models.DispensingMedicine
	for _, dm := range dispensing.DispensingMedicines {
		isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, dm.Name)
		if isBlindRole && isBlindedDrug {
			dm.Name = tools.BlindData
		}
		dispensingMedicines = append(dispensingMedicines, dm)
	}

	// 未编号药物
	var otherDispensingMedicines []models.OtherDispensingMedicine
	for _, odm := range dispensing.OtherDispensingMedicines {
		isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, odm.Name)
		if isBlindRole && isBlindedDrug {
			odm.Name = tools.BlindData
		}
		otherDispensingMedicines = append(otherDispensingMedicines, odm)
	}

	// 实际使用的已编号药物
	var realDispensingMedicines []models.DispensingMedicine
	for _, rdm := range dispensing.RealDispensingMedicines {
		isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, rdm.Name)
		if isBlindRole && isBlindedDrug {
			rdm.Name = tools.BlindData
		}
		realDispensingMedicines = append(realDispensingMedicines, rdm)
	}

	// 实际使用的未编号药物
	var realOtherDispensingMedicines []models.OtherDispensingMedicineInfo
	for _, rodm := range dispensing.RealOtherDispensingMedicines {
		isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, rodm.Name)
		if isBlindRole && isBlindedDrug {
			rodm.Name = tools.BlindData
		}
		realOtherDispensingMedicines = append(realOtherDispensingMedicines, rodm)
	}

	//result["label"] = subjectReplaceText		// 受试者label
	result["subjectId"] = subject.ID                                      // 受试者ID
	result["projectNumber"] = project.Number                              // 项目编号
	result["projectName"] = project.Name                                  // 项目名称
	result["env"] = envName                                               // 环境
	result["projectStatus"] = project.Status                              // 项目状态
	result["siteName"] = projectSite.Number + "-" + projectSite.Name      // 中心名称
	result["visitName"] = dispensing.VisitInfo.Name                       // 访视名称
	result["status"] = workTask.Info.Status                               // 任务状态
	result["createdTime"] = workTask.Info.CreatedTime                     // 开始时间
	result["deadline"] = workTask.Info.Deadline                           // 预期完成时间
	result["finishTime"] = workTask.Info.FinishTime                       // 完成时间
	result["dispensingMedicines"] = dispensingMedicines                   // 编号研究产品
	result["otherDispensingMedicines"] = otherDispensingMedicines         // 未编号研究产品
	result["realDispensingMedicines"] = realDispensingMedicines           // 实际发放的编号研究产品
	result["realOtherDispensingMedicines"] = realOtherDispensingMedicines // 实际发放的编号研究产品
	result["workType"] = workTask.Info.WorkType                           // 任务类型
	result["remark"] = dispensing.Remark                                  // 备注
	result["sendMode"] = "-"                                              // 发放方式
	return result, nil
}

func (w *WorkTaskService) dispensingDetail(ctx *gin.Context, filter bson.M, envID primitive.ObjectID, roleOID primitive.ObjectID) (map[string]interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "info.dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$dispensing", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject",
			"let": bson.M{
				"subject_id": "$dispensing.subject_id",
			},
			"pipeline": bson.A{
				bson.M{"$unwind": "$info"},
				bson.M{"$match": bson.M{
					"$expr": bson.M{
						"$and": bson.A{
							bson.M{"$eq": bson.A{"$_id", "$$subject_id"}},
							bson.M{"$ne": bson.A{"$deleted", true}},
						},
					},
					"info.name": "shortname",
				},
				},
			},
			"as": "subject",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$subject", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{"from": "medicine",
			"let": bson.M{"ids": bson.M{
				"$cond": bson.A{
					bson.M{"$eq": bson.A{"$info.status", 0}},
					"$dispensing.dispensing_medicines.medicine_id",
					"$info.medicine_ids"}},
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$ids"}}}},
				bson.M{"$project": bson.M{"_id": 0, "id": "$_id", "name": 1, "number": 1, "batchNumber": "$batch_number", "status": "$status", "expirationDate": "$expiration_date", "spec": "$spec", "shortCode": "$short_code"}},
			},
			"as": "medicines"}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "project_site",
			"let": bson.M{
				"site_id": "$subject.project_site_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$site_id"}}},
				},
			},
			"as": "project_site",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project_site", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "history",
			"let":  bson.M{"id": "$dispensing._id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$oid", "$$id"}}}},
				bson.M{"$sort": bson.D{{"time", -1}}},
			},
			"as": "history",
		}}},
		{{Key: "$project", Value: bson.M{
			"_id":           0,
			"id":            "$_id",
			"projectStatus": "$project.status",
			"projectNumber": "$project.info.number",
			"projectName":   "$project.info.name",
			"subjectName":   "$subject.info.value",
			"visitNumber":   "$dispensing.visit_info.number",
			"siteName":      bson.M{"$concat": bson.A{"$project_site.number", "-", models.ProjectSiteNameLookUpBson(ctx)}},
			"history":       1,
			"info": bson.M{
				"workType":    "$info.work_type",
				"status":      1,
				"deadline":    1,
				"createdTime": "$info.created_time",
				"finishTime":  "$info.finish_time",
				"medicines":   "$medicines",
			},
		}}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("work_task").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := data[0]
	keys := []string{"dispensing.dispensing", "dispensing.dtp-dispensing",
		"dispensing.reissue", "dispensing.dtp-reissue"}
	reason := ""
	for _, history := range result["history"].(primitive.A) {
		for _, key := range keys {
			if strings.Contains(history.(map[string]interface{})["key"].(string), key) {
				info := result["info"].(map[string]interface{})
				workType := info["workType"].(int32)
				if workType == 9 && history.(map[string]interface{})["data"].(map[string]interface{})["reason"] != nil {
					reason = history.(map[string]interface{})["data"].(map[string]interface{})["reason"].(string)
				} else if workType == 5 && history.(map[string]interface{})["data"].(map[string]interface{})["remark"] != nil {
					reason = history.(map[string]interface{})["data"].(map[string]interface{})["remark"].(string)
				}
			}
		}
	}
	result["reason"] = reason
	result["history"] = nil

	IsBlindDrugMap, err := tools.IsBlindDrugMap(envID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if !roleOID.IsZero() {
		isBlindRole, err := tools.IsBlindedRole(roleOID.Hex())
		if err != nil {
			return nil, err
		}
		if isBlindRole {
			medicines := result["info"].(map[string]interface{})["medicines"].(primitive.A)
			for i := 0; i < len(medicines); i++ {
				name := medicines[i].(map[string]interface{})["name"].(string)
				isBlind, Ok := IsBlindDrugMap[name]
				if Ok {
					if isBlind {
						result["info"].(map[string]interface{})["medicines"].(primitive.A)[i].(map[string]interface{})["name"] = tools.BlindData
					}
				}
			}
		}
	}
	return result, nil
}

func (w *WorkTaskService) QueryList(ctx *gin.Context) ([]models.WorkTask, error) {
	customerId := ctx.Query("customerId")
	projectId := ctx.Query("projectId")
	envId := ctx.Query("envId")
	cohortId := ctx.Query("cohortId")
	workTypeStr := ctx.Query("workType")
	workType, err := strconv.Atoi(workTypeStr)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	customerOID, _ := primitive.ObjectIDFromHex(customerId)
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortId)

	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var workTaskList []models.WorkTask
	match := bson.M{"info.status": 0,
		"info.work_type": workType,
		"deleted":        false,
		"user_ids":       me.ID,
		"customer_id":    customerOID,
		"project_id":     projectOID,
		"env_id":         envOID}
	if !cohortOID.IsZero() {
		match["cohort_id"] = cohortOID
	}
	workTaskCursor, err := tools.Database.Collection("work_task").Find(nil, match)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = workTaskCursor.All(nil, &workTaskList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return workTaskList, nil
}

func (w *WorkTaskService) List(ctx *gin.Context) ([]models.WorkTaskView, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	ia := "1"
	isAll, _ := ctx.GetQuery("isAll")
	if isAll != "" {
		newIsAll, _ := strconv.ParseBool(isAll)
		if newIsAll { // true 查询已办任务
			ia = "1"
		} else { // false 查询待办任务
			ia = "2"
		}
	} else {
		ia = "3" // 查全部任务
	}

	filter := bson.M{"user_id": me.ID, "app": true}
	envs := make([]models.UserProjectEnvironment, 0)
	cursor, err := tools.Database.Collection("user_project_environment").Find(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &envs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envIds := slice.Map(envs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.EnvID
	})
	envIds = slice.Unique(envIds)
	// 拿到项目ID
	projectIds := slice.Map(envs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.ProjectID
	})
	projectIds = slice.Unique(projectIds)

	roleIds := make([]primitive.ObjectID, 0)
	for _, env := range envs {
		roleIds = append(roleIds, env.Roles...)
	}
	roleIds = slice.Unique(roleIds)
	workTaskFilter := bson.M{
		"user_ids": me.ID,
		//过滤掉没权限的环境
		"env_id":  bson.M{"$in": envIds},
		"deleted": bson.M{"$ne": true},
	}
	// 查全部任务排序
	sortRule := bson.D{
		{"info.created_time", -1},
	}
	if ia == "1" { // 查询已办任务
		workTaskFilter["info.status"] = 1
		sortRule = bson.D{
			{"info.finish_time", -1},
		}
	} else if ia == "2" { // 查询待办任务
		workTaskFilter["info.status"] = 0
		sortRule = bson.D{
			{"info.deadline", -1},
		}
	}

	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "0"))

	s := int64(start)
	l := int64(limit)
	opts := &options.FindOptions{
		//Sort:  bson.D{{"info.created_time", -1}},
		Sort:  sortRule,
		Skip:  &s,
		Limit: &l,
	}

	var workTasks []models.WorkTask
	workTaskCursor, err := tools.Database.Collection("work_task").Find(ctx, workTaskFilter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = workTaskCursor.All(nil, &workTasks)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目角色权限
	prpFilter := bson.M{"_id": bson.M{"$in": roleIds}}
	projectRolePermissions := make([]models.ProjectRolePermission, 0)
	prpCursor, err := tools.Database.Collection("project_role_permission").Find(ctx, prpFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = prpCursor.All(nil, &projectRolePermissions)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询关联中心
	userSiteFilter := bson.M{"user_id": me.ID, "env_id": bson.M{"$in": envIds}}
	userSites := make([]models.UserSite, 0)
	userSiteCursor, err := tools.Database.Collection("user_site").Find(ctx, userSiteFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userSiteCursor.All(nil, &userSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询关联仓库
	userDepotFilter := bson.M{"user_id": me.ID, "env_id": bson.M{"$in": envIds}}
	userDepots := make([]models.UserDepot, 0)
	userDepotCursor, err := tools.Database.Collection("user_depot").Find(nil, userDepotFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userDepotCursor.All(nil, &userDepots)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目
	pFilter := bson.M{"_id": bson.M{"$in": projectIds}}
	projects := make([]models.Project, 0)
	pCursor, err := tools.Database.Collection("project").Find(nil, pFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = pCursor.All(nil, &projects)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var filterTask []models.WorkTaskView
	for _, workTask := range workTasks {
		view := models.WorkTask2View(workTask)
		//扫码入仓 projectName
		//运送订单确认 projectName
		//研究产品接收 projectName receiveName orderNumber
		//发药 subjectName sitName visitNumber
		//补发药 subjectName sitName visitNumber
		switch workTask.Info.WorkType {
		case 1: // 扫码入仓
			permissions := []string{"operation.build.medicine.barcode.scan"}
			haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
			if haveAppOperaPermission {
				w.projectInfo(&view, projects)
				filterTask = append(filterTask, view)
			}
		case 2: // 发送订单确认
			w.projectInfo(&view, projects)
			siteIds, deportIds, err := w.orderInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
			if haveAppDataPermission {
				permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 3: // 研究产品接收
			w.projectInfo(&view, projects)
			siteIds, deportIds, err := w.orderInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
			if haveAppDataPermission {
				permissions := []string{"operation.supply.shipment.receive", "operation.supply.shipment.lose", "operation.supply.shipment.terminated"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 4: // 发药
			w.projectInfo(&view, projects)
			siteId, err := w.dispensingInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteId, []primitive.ObjectID{})
			if haveAppDataPermission {
				permissions := []string{"operation.subject.medicine.dispensing"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 5: // 补发药
			w.projectInfo(&view, projects)
			siteId, err := w.dispensingInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteId, []primitive.ObjectID{})
			if haveAppDataPermission {
				permissions := []string{"operation.subject.medicine.reissue"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 6: // 订单待运送
			w.projectInfo(&view, projects)
			siteIds, deportIds, err := w.orderInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
			if haveAppDataPermission {
				permissions := []string{"operation.supply.shipment.send", "operation.supply.shipment.lose", "operation.supply.shipment.receive", "operation.supply.shipment.close"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 7: // 创建订单审批
			w.projectInfo(&view, projects)
			siteIds, err := w.siteInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
			if haveAppDataPermission {
				permissions := []string{"operation.supply.shipment.approval"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 8: // 紧急揭盲审批
			w.projectInfo(&view, projects)
			siteId, err := w.unblindingApproval(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteId, []primitive.ObjectID{})
			if haveAppDataPermission {
				permissions := []string{"operation.subject.unblinding-approval"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 9: // 计划外发药
			w.projectInfo(&view, projects)
			siteId, err := w.dispensingInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteId, []primitive.ObjectID{})
			if haveAppDataPermission {
				permissions := []string{"operation.subject.medicine.out-visit-dispensing"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 10: // pv揭盲审批
			w.projectInfo(&view, projects)
			siteId, err := w.unblindingApproval(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteId, []primitive.ObjectID{})
			if haveAppDataPermission {
				permissions := []string{"operation.subject.unblinding-pv-approval"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 11: // 发放通知
			w.projectInfo(&view, projects)
			siteId, err := w.dispensingAppInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteId, []primitive.ObjectID{})
			if haveAppDataPermission {
				permissions := []string{"operation.subject.medicine.dispensing"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 12: // 包装扫码
			permissions := []string{"operation.build.medicine.barcode.scanPackage"}
			haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
			if haveAppOperaPermission {
				w.projectInfo(&view, projects)
				filterTask = append(filterTask, view)
			}
		case 13: // 发送回收订单待确认任务
			w.projectInfo(&view, projects)
			siteIds, deportIds, err := w.orderInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
			if haveAppDataPermission {
				permissions := []string{"operation.supply.recovery.cancel", "operation.supply.recovery.determine"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 14: // 发送回收订单待运送
			w.projectInfo(&view, projects)
			siteIds, deportIds, err := w.orderInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
			if haveAppDataPermission {
				permissions := []string{"operation.supply.recovery.confirm", "operation.supply.recovery.lose", "operation.supply.recovery.receive", "operation.supply.recovery.close"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		case 15: // 回收订单接收任务
			w.projectInfo(&view, projects)
			siteIds, deportIds, err := w.orderInfo(ctx, &view)
			if err != nil {
				return nil, err
			}
			haveAppDataPermission := tools.HaveAppDataPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
			if haveAppDataPermission {
				permissions := []string{"operation.supply.recovery.receive", "operation.supply.recovery.lose", "operation.supply.recovery.end"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					filterTask = append(filterTask, view)
				} else {
					//没操作权限未完成的隐藏，已完成的保留但不能查看详情
					if workTask.Info.Status == 1 {
						view.NoPermission = true
						filterTask = append(filterTask, view)
					}
				}
			}
		}
	}
	return filterTask, nil
}

func (w *WorkTaskService) projectInfo(data *models.WorkTaskView, projects []models.Project) {
	projectID := data.ProjectID
	envID := data.EnvironmentID
	var project models.Project
	for _, p := range projects {
		if p.ID == projectID {
			project = p
		}
	}
	data.ProjectName = project.Name
	data.ProjectNumber = project.Number
	data.ProjectStatus = project.Status
	// 筛选环境
	for _, env := range project.Environments {
		if env.ID == envID {
			data.EnvName = env.Name
			break
		}
	}
}

func (w *WorkTaskService) siteInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	approvalProcessID := data.Info.ApprovalProcessID
	orderTaskFilter := bson.M{"_id": approvalProcessID}
	var orderAddTask models.OrderAddTask
	err := tools.Database.Collection("approval_process").FindOne(ctx, orderTaskFilter).Decode(&orderAddTask)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	projectSiteFilter := bson.M{"_id": orderAddTask.Data.ReceiveID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(ctx, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
	return siteIds, nil
}

func (w *WorkTaskService) unblindingApproval(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	subjectApproval := data.Info.SubjectApproval
	if data.Info.WorkType == 10 {
		subjectApproval = data.Info.SubjectPvApproval
	}
	siteIds := make([]primitive.ObjectID, 0)
	// 查询受试者
	subjectIdFilter := bson.M{"_id": subjectApproval.SubjectID}
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, subjectIdFilter).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}

	// 查询中心
	projectSiteFilter := bson.M{"_id": subject.ProjectSiteID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
	return siteIds, nil
}

func (w *WorkTaskService) orderInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, []primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	depotIds := make([]primitive.ObjectID, 0)
	var order models.MedicineOrder
	orderId := data.Info.MedicineOrderID
	status := data.Info.Status
	err := tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": orderId}).Decode(&order)
	if err != nil {
		return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
	}
	if status == 1 || (order.Status != 4 && order.Status != 5) {
		data.OrderName = order.OrderNumber
		if order.Type != 5 && order.Type != 6 {
			// 接收机构
			receiveID := order.ReceiveID
			var projectSite models.ProjectSite
			err := tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": receiveID}).Decode(&projectSite)
			if err != nil && err != mongo.ErrNoDocuments {
				return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
			}
			if !projectSite.ID.IsZero() {
				//判断数据权限
				siteIds = append(siteIds, projectSite.ID)
				data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
			} else {
				var projectStorehouse models.ProjectStorehouse
				err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": receiveID}).Decode(&projectStorehouse)
				if err != nil {
					return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
				}
				//判断数据权限
				depotIds = append(depotIds, projectStorehouse.ID)
				var storehouse models.Storehouse
				err = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
				if err != nil {
					return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
				}
				data.SiteName = storehouse.Name
			}
		} else {
			//查询受试者
			var subject models.Subject
			err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": order.SubjectID}).Decode(&subject)
			if err != nil {
				return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
			}
			// 受试者号
			for _, sbj := range subject.Info {
				if sbj.Name == "shortname" {
					data.SiteName = sbj.Value.(string)
					break
				}
			}
		}

		// 运送机构
		sendID := order.SendID
		var sendProjectSite models.ProjectSite
		err := tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": sendID}).Decode(&sendProjectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
		}
		if !sendProjectSite.ID.IsZero() {
			//判断数据权限
			siteIds = append(siteIds, sendProjectSite.ID)
			data.SendName = sendProjectSite.Number + "-" + models.GetProjectSiteName(ctx, sendProjectSite)
		} else {
			var sendProjectStorehouse models.ProjectStorehouse
			err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": sendID}).Decode(&sendProjectStorehouse)
			if err != nil {
				return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
			}
			//判断数据权限
			depotIds = append(depotIds, sendProjectStorehouse.ID)
			var sendStorehouse models.Storehouse
			err = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": sendProjectStorehouse.StorehouseID}).Decode(&sendStorehouse)
			if err != nil {
				return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
			}
			data.SendName = sendStorehouse.Name
		}
	}
	return siteIds, depotIds, nil
}

func (w *WorkTaskService) dispensingInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	dispensingId := data.Info.DispensingID
	status := data.Info.Status
	var dispensing models.Dispensing
	err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingId}).Decode(&dispensing)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	if status == 1 || dispensing.DispensingMedicines != nil {
		data.VisitNumber = dispensing.VisitInfo.Number
		var subject models.Subject
		err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
		if err != nil {
			return []primitive.ObjectID{}, errors.WithStack(err)
		}
		var subjectName string
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				subjectName = info.Value.(string)
				break
			}
		}
		data.SubjectName = subjectName
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds = append(siteIds, projectSite.ID)
		data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
	}
	return siteIds, nil
}

func (w *WorkTaskService) dispensingAppInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	dispensingId := data.Info.DispensingID
	var dispensing models.Dispensing
	err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingId}).Decode(&dispensing)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	if dispensing.VisitInfo.VisitCycleInfoID != primitive.NilObjectID {
		data.VisitCycleId = dispensing.VisitInfo.VisitCycleInfoID.Hex()
	}

	data.VisitNumber = dispensing.VisitInfo.Number
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	var subjectName string
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectName = info.Value.(string)
			break
		}
	}

	//待发药--插入任务表中
	match := bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID}
	if dispensing.CohortID != primitive.NilObjectID {
		match = bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}
	}
	var visit models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visit)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	filter := bson.M{"subject_id": dispensing.SubjectID}
	var resDispensing []models.ResDispensing
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_order",
			"let": bson.M{
				"dispensing_id": "$_id",
				//"order_number":  "$order",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
			},

			"as": "medicine_order",
		}}},
		{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &resDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	visitCycleMap := map[string]models.VisitCycleInfo{}
	for _, info := range visit.Infos {
		visitCycleMap[info.Number] = info
	}
	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return nil, err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	lastTime := time.Duration(0)
	afterRandom := false
	interval := float64(0)

	var period models.Period

	// TODO 7064
	subjectMap := make(map[string]models.Subject)
	if !visit.BaseCohort.IsZero() && visit.CohortID != visit.BaseCohort {
		subjectMap, err = task.GetBaseCohortSubjectMap(nil, visit.BaseCohort, primitive.NilObjectID, "")
	}
	firstTime := time.Duration(0)
	randomTime := subject.RandomTime
	if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
		randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
	}
	for _, res := range resDispensing {
		if !res.VisitSign {
			if res.Status == 2 {
				interval = 0
				lastTime = res.DispensingTime
			}
			if res.VisitInfo.Random {
				afterRandom = true
			}
			// TODO 7064

			if firstTime == 0 && res.DispensingTime != 0 && visitCycleMap[res.VisitInfo.Number].Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(res.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[res.VisitInfo.Number].Interval)).Unix())
				firstTime = res.DispensingTime
			}
			if res.ID == dispensing.ID {
				period = handlePeriod(afterRandom, visit.VisitType, visitCycleMap[dispensing.VisitInfo.Number], randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
			}
		}
	}
	// 不展示随机号
	if !attribute.AttributeInfo.IsRandomNumber {
		data.RandomNumber = tools.BlindData
	} else {
		data.RandomNumber = subject.RandomNumber
	}
	data.SubjectName = subjectName
	data.SubjectId = dispensing.SubjectID.Hex()
	data.PlanLeftTime = ""
	if len(period.MinPeriod) != 0 {
		data.PlanLeftTime = timestampConversion(period.MinPeriod)
	}
	data.PlanRightTime = ""
	if len(period.MaxPeriod) != 0 {
		data.PlanRightTime = timestampConversion(period.MaxPeriod)
		if data.Info.WorkType == 11 {
			data.Info.Deadline = time.Duration(period.MaximumTime)
		}
	}

	if subject.RandomTime != 0 {
		//timeZone := time.Duration(intTimeZone)
		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		formattedDate := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006.01.02")
		if data.PlanLeftTime == "" {
			data.PlanLeftTime = formattedDate
		}
		if data.PlanRightTime == "" {
			data.PlanRightTime = formattedDate
			if data.Info.WorkType == 11 {
				data.Info.Deadline = subject.RandomTime
			}
		}
	}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)

	return siteIds, nil
}

func timestampConversion(dateStr string) string {
	timestamp := strings.Replace(dateStr, "-", ".", -1) // 第三个参数表示全局替换（-1）或仅替换首次出现的位置（0）
	return timestamp
}

func (w *WorkTaskService) Update(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var req models.WorkTaskUpdate
		_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
		t := time.Unix(int64(req.Deadline), 0)
		deadline := t.Add(23 * time.Hour).Unix() // app传入整数日期+23小时，否则任务逾期通知的定时任务扫描不到

		workTaskUpdate := bson.M{"$set": bson.M{
			"info.deadline": deadline,
		}}
		_, err := tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": req.WorkTaskID, "info.status": 0}, workTaskUpdate)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (w *WorkTaskService) Add(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var req map[string]interface{}
		_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
		customerId := req["customerId"].(string)
		projectId := req["projectId"].(string)
		envId := req["envId"].(string)
		cohort := req["cohortId"]
		var cohortID = primitive.NilObjectID
		if cohort != nil {
			cohortID, _ = primitive.ObjectIDFromHex(req["cohortId"].(string))
		}
		customerID, _ := primitive.ObjectIDFromHex(customerId)
		projectID, _ := primitive.ObjectIDFromHex(projectId)
		envID, _ := primitive.ObjectIDFromHex(envId)
		//roleOID, _ := primitive.ObjectIDFromHex(req["roleId"].(string))
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		workType := int(req["workType"].(float64))

		medicineOrderId := primitive.NilObjectID
		if req["medicineOrderId"] != nil {
			m, _ := primitive.ObjectIDFromHex(req["medicineOrderId"].(string))
			medicineOrderId = m
		}

		dispensingId := primitive.NilObjectID
		if req["dispensingId"] != nil {
			m, _ := primitive.ObjectIDFromHex(req["dispensingId"].(string))
			dispensingId = m
		}

		// 查询权限扫码入仓权限的用户
		var userIds []primitive.ObjectID
		var permissions []string
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if workType == 1 || workType == 2 || workType == 3 {
			existFilter := bson.M{
				"customer_id": customerID,
				"project_id":  projectID,
				"env_id":      envID,
				//"user_id":        user.ID,
				"info.work_type": workType,
			}
			if !cohortID.IsZero() {
				existFilter["cohort_id"] = cohortID
			}
			if workType == 2 || workType == 3 {
				existFilter["info.medicine_order_id"] = medicineOrderId
			}

			if workType == 1 { // 查询扫码入仓权限的用户
				permissions = append(permissions, "operation.build.medicine.barcode.scan")
			}
			userIds, err = tools.GetPermissionUserIds(sctx, permissions, projectID, envID)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			var existWorkTask models.WorkTask
			err := tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&existWorkTask)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			if !existWorkTask.ID.IsZero() {
				if existWorkTask.Info.Status == 0 {
					return nil, tools.BuildServerError(ctx, "work.task.exist")
				} else if workType == 2 || workType == 3 {
					return nil, tools.BuildServerError(ctx, "order_status_error")
				}
			}
		}

		// 如果没有可操作的用户不能创建任务
		if userIds != nil {
			OID := primitive.NewObjectID()
			workTask := models.WorkTask{
				ID:            OID,
				CustomerID:    customerID,
				ProjectID:     projectID,
				EnvironmentID: envID,
				CohortID:      cohortID,
				UserIDs:       userIds,
				//RoleID: roleOID,
				Info: models.WorkTaskInfo{
					WorkType:        workType,
					Status:          0,
					CreatedTime:     time.Duration(time.Now().Unix()),
					Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
					MedicineIDs:     []primitive.ObjectID{},
					MedicineOrderID: medicineOrderId,
					DispensingID:    dispensingId,
				},
			}
			_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if workType == 1 {
				// 项目日志
				var OperationLogFieldGroups []models.OperationLogFieldGroup
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "workTask.add",
					TranKey: "operation_log.workTask.label",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  6,
						Value: "add",
					},
				})
				historyID := envID
				if !cohortID.IsZero() {
					historyID = cohortID
				}
				err := tools.SaveOperation(ctx, sctx, "operation_log.module.work_task_add", historyID, 1, OperationLogFieldGroups, []models.Mark{}, OID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			if workType == 2 || workType == 3 {
				var key string
				if workType == 2 {
					key = "history.order.confirm-task"
				}
				if workType == 3 {
					var order models.MedicineOrder
					err := tools.Database.Collection("medicine_order").FindOne(sctx, bson.M{"_id": workTask.Info.MedicineOrderID}).Decode(&order)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					if order.Status == 1 {
						key = "history.order.receive-task-confrim"
					} else if order.Status == 2 {
						key = "history.order.receive-task-send"
					}
				}
				var operHistories []models.History
				operHistory := models.History{
					Key:  key,
					OID:  workTask.Info.MedicineOrderID,
					Data: nil,
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				}
				operHistories = append(operHistories, operHistory)
				ctx.Set("HISTORY", operHistories)
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (w *WorkTaskService) PackageScanConfirm(ctx *gin.Context, packageScanReq models.PackageScanReq) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var workTask models.WorkTask
		err := tools.Database.Collection("work_task").FindOne(sctx, bson.M{"_id": packageScanReq.WorkTaskID}).Decode(&workTask)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//判断任务是否是删除状态
		if workTask.Deleted {
			return nil, tools.BuildServerError(ctx, "work.task.deleted")
		}
		//判断任务状态
		if workTask.Info.Status == 1 {
			return nil, tools.BuildServerError(ctx, "work.task.error")
		}

		if workTask.Info.WorkType != 12 {
			return nil, tools.BuildServerError(ctx, "work.task.packageScan.error")
		}

		queryPackageDrug := make([]string, 0)
		_, _, packageDrugNames, packageConfigs, _, _ := tools.IsOpenPackage(workTask.EnvironmentID)
		for key, _ := range packageDrugNames {
			queryPackageDrug = append(queryPackageDrug, key)
		}

		// 没有包装配置的药物
		if len(queryPackageDrug) <= 0 {
			return nil, tools.BuildServerError(ctx, "work.task.packageScan.error")
		}

		//判断包装扫码确认的数据是否正确
		//查询生成条形码里面自动生成的包装号
		var data []map[string]interface{}
		match := bson.M{"customer_id": workTask.CustomerID, "project_id": workTask.ProjectID, "env_id": workTask.EnvironmentID, "package_numbers.0": bson.M{"$exists": true}}
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$project", Value: bson.M{
				"_id":            0,
				"packageNumbers": "$package_numbers",
			}}},
		}
		cursor, err := tools.Database.Collection("barcode_group").Aggregate(sctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		packageMap := make(map[string]bool)
		for _, barcodeGroup := range data {
			packageNumbers := barcodeGroup["packageNumbers"].(primitive.A)
			for _, packageNumber := range packageNumbers {
				if !packageMap[packageNumber.(string)] {
					packageMap[packageNumber.(string)] = true
				}
			}
		}

		packageMedicines := packageScanReq.PackageMedicines
		packageMs := make([]models.PackageMedicine, 0)
		for _, packageMedicine := range packageMedicines {
			//判断包装号是否存在
			packageNumber := packageMedicine.PacakageNumber
			if !packageMap[packageNumber] { //不存在，说明不是该项目的包装号条形码
				return nil, tools.BuildServerError(ctx, "work.task.package_number.error")
			}

			//判断该包装内的药物是否符合配置要求
			medicinePackageCount := make(map[string]int)
			medicines := packageMedicine.Medicines
			// 药物ID
			medicineIds := make([]primitive.ObjectID, 0)
			for _, medicine := range medicines {
				count, Ok := medicinePackageCount[medicine.Name]
				if Ok {
					medicinePackageCount[medicine.Name] = count + 1
				} else {
					medicinePackageCount[medicine.Name] = 1
				}

				medicineIds = append(medicineIds, medicine.ID)
			}

			packageMs = append(packageMs, models.PackageMedicine{
				PackageNumber: packageNumber,
				Medicines:     medicineIds,
			})

			medicineNames := packageConfigs[packageMedicine.Medicines[0].Name]

			if len(medicineNames) != len(medicinePackageCount) {
				return nil, tools.BuildServerError(ctx, "work.task.packageScan.error")
			}

			for medicineName, packageCount := range medicinePackageCount {
				count := packageDrugNames[medicineName]
				if packageCount != count {
					return nil, tools.BuildServerError(ctx, "work.task.packageScan.error")
				}
			}

			//更新包装号
			medicineFilter := bson.M{"_id": bson.M{"$in": medicineIds}}
			// 手动编码药物ID
			manualMedicineIds := make([]primitive.ObjectID, 0)
			// 自动编码药物ID
			automaticMedicineIds := make([]primitive.ObjectID, 0)
			// 查询包装药物
			var medicineList []models.Medicine
			cursor, err := tools.Database.Collection("medicine").Find(sctx, medicineFilter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if err := cursor.All(sctx, &medicineList); err != nil {
				return nil, errors.WithStack(err)
			}
			for _, ml := range medicineList {
				if ml.ShortCode == "" { // 手动编码药物
					manualMedicineIds = append(manualMedicineIds, ml.ID)
				} else { // 自动编码药物
					automaticMedicineIds = append(automaticMedicineIds, ml.ID)
				}
			}

			// 手动编码药物修改
			if manualMedicineIds != nil && len(manualMedicineIds) > 0 {
				medicineUpdate := bson.M{
					"$set": bson.M{
						"package_number":        packageNumber,
						"package_serial_number": packageNumber,
						"status":                0, // 1 -> 0 手动编码药物从可用改为待入仓
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": manualMedicineIds}}, medicineUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
			}
			// 自动编码药物修改
			if automaticMedicineIds != nil && len(automaticMedicineIds) > 0 {
				medicineUpdate := bson.M{
					"$set": bson.M{
						"package_number":        packageNumber,
						"package_serial_number": packageNumber,
						"status":                22, // 1 -> 22 自动编码药物 从可用改为待审核
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": automaticMedicineIds}}, medicineUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		//判断自动生成的包装的药物，packageNumber是否没有值，如果有，说明全部已经包装扫码（手动上传的药物，上传的时候就已经有包装号）
		existMedicineFilter := bson.M{
			"customer_id": workTask.CustomerID,
			"project_id":  workTask.ProjectID,
			"env_id":      workTask.EnvironmentID,
			"name":        bson.M{"$in": queryPackageDrug},
			"status":      0,
		}
		var medicinesGroupsStorehouse []map[string]interface{}
		mpipeline := mongo.Pipeline{
			{{Key: "$match", Value: existMedicineFilter}},
			{{Key: "$group", Value: bson.M{"_id": "$storehouse_id"}}},
		}
		mcursor, err := tools.Database.Collection("medicine").Aggregate(sctx, mpipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = mcursor.All(nil, &medicinesGroupsStorehouse)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if len(medicinesGroupsStorehouse) > 0 {
			// 查询权限扫码入仓权限的用户
			permissions := []string{"operation.build.medicine.barcode.scanPackage"}
			siteOrStoreIDs := []primitive.ObjectID{}
			for _, storehouseId := range medicinesGroupsStorehouse {
				siteOrStoreIDs = append(siteOrStoreIDs, storehouseId["_id"].(primitive.ObjectID))
			}
			userIds, err := tools.GetPermissionUserIds(sctx, permissions, workTask.ProjectID, workTask.EnvironmentID, siteOrStoreIDs...)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			workTask := models.WorkTask{
				ID:            primitive.NewObjectID(),
				CustomerID:    workTask.CustomerID,
				ProjectID:     workTask.ProjectID,
				EnvironmentID: workTask.EnvironmentID,
				CohortID:      workTask.CohortID,
				UserIDs:       userIds,
				Info: models.WorkTaskInfo{
					WorkType:        12,
					Status:          0,
					CreatedTime:     time.Duration(time.Now().Unix()),
					Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
					MedicineIDs:     []primitive.ObjectID{},
					MedicineOrderID: primitive.NilObjectID,
					DispensingID:    primitive.NilObjectID,
				},
			}
			_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//更新任务
		//当前操作人
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		updateWorkTask := bson.M{"$set": bson.M{
			"info.package_medicines": packageMs,
			"info.finish_time":       time.Duration(time.Now().Unix()),
			"info.finish_user_id":    user.ID,
			"info.status":            1,
		}}
		_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": packageScanReq.WorkTaskID}, updateWorkTask)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 记录报表轨迹
		drugTrack := []interface{}{}
		for _, pk := range packageMs {
			for _, mid := range pk.Medicines {
				drugTrack = append(drugTrack, models.DrugTrack{
					ID:            primitive.NewObjectID(),
					CustomerID:    workTask.CustomerID,
					ProjectID:     workTask.ProjectID,
					EnvironmentID: workTask.EnvironmentID,
					MedicineID:    mid,
					Status:        6, // 包装扫码
					Remarks:       "",
					OperatorID:    user.ID,
					OperatorTime:  time.Duration(time.Now().Unix()),
				})
			}
		}
		if len(drugTrack) > 0 {
			_, err := tools.Database.Collection("drug_track").InsertMany(sctx, drugTrack)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (w *WorkTaskService) ScanCodeConfirm(ctx *gin.Context, scanMedicineReq models.ScanMedicineReq) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var workTask models.WorkTask
		err := tools.Database.Collection("work_task").FindOne(sctx, bson.M{"_id": scanMedicineReq.WorkTaskID}).Decode(&workTask)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//判断任务是否是删除状态
		if workTask.Deleted {
			return nil, tools.BuildServerError(ctx, "work.task.deleted")
		}
		//判断任务状态
		if workTask.Info.Status == 1 {
			return nil, tools.BuildServerError(ctx, "work.task.error")
		}

		if !(workTask.Info.WorkType == 4 || workTask.Info.WorkType == 5 || workTask.Info.WorkType == 9) {
			return nil, tools.BuildServerError(ctx, "work.task.scan.error")
		}

		//判断扫码确认的药物是否完整
		if len(scanMedicineReq.Medicines) == len(workTask.Info.MedicineIDs) {
			var medicineIds []primitive.ObjectID
			for _, v := range scanMedicineReq.Medicines {
				medicineIds = append(medicineIds, v.ID)
			}

			for _, medicine := range workTask.Info.MedicineIDs {
				in := tools.In(medicine, medicineIds)
				if !in {
					return nil, tools.BuildServerError(ctx, "work.task.medicine.error")
				}
			}

			//更新任务
			//当前操作人
			user, err := tools.Me(ctx)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			updateWorkTask := bson.M{"$set": bson.M{
				"info.finish_time":    time.Duration(time.Now().Unix()),
				"info.finish_user_id": user.ID,
				"info.status":         1,
			}}
			_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": scanMedicineReq.WorkTaskID}, updateWorkTask)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var dispensing models.Dispensing
			update := bson.M{"$set": bson.M{
				"scan_status": 1,
			}}
			err = tools.Database.Collection("dispensing").FindOneAndUpdate(sctx, bson.M{"_id": workTask.Info.DispensingID}, update).Decode(&dispensing)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var medicineStr []string
			var shortCodeStr []string
			for _, v := range dispensing.DispensingMedicines {
				medicineStr = append(medicineStr, v.Number)
				shortCodeStr = append(shortCodeStr, v.ShortCode)
			}

			//查询项目属性
			var attribute models.Attribute
			attributeMatch := bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID}
			if dispensing.CohortID != primitive.NilObjectID {
				attributeMatch = bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}
			}
			err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

			var subject models.Subject
			err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var histories []models.History
			history := models.History{
				Key:  "history.dispensing.scanConfrim",
				OID:  dispensing.ID,
				Data: bson.M{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": medicineStr, "shortCode": shortCodeStr},
				Time: time.Duration(time.Now().Unix()),
				UID:  user.ID,
				User: user.Name,
			}
			histories = append(histories, history)
			ctx.Set("HISTORY", histories)
		} else {
			return nil, tools.BuildServerError(ctx, "work.task.medicine.error")
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (w *WorkTaskService) ScanCodeWarehousing(ctx *gin.Context, scanMedicineReq models.ScanMedicineReq) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var workTask models.WorkTask
		err := tools.Database.Collection("work_task").FindOne(sctx, bson.M{"_id": scanMedicineReq.WorkTaskID}).Decode(&workTask)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//判断任务状态
		if workTask.Info.Status == 1 {
			return nil, tools.BuildServerError(ctx, "work.task.error")
		}

		//当前操作人
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if workTask.Info.WorkType == 1 { //扫码入仓
			medicines := scanMedicineReq.Medicines
			oids := make([]primitive.ObjectID, len(medicines))
			var histories []models.History

			//获取研究产品配置信息
			drugNamesData, _ := w.drugConfigureService.GetDrugNames(ctx, workTask.CustomerID.Hex(), workTask.EnvironmentID.Hex(), workTask.CohortID.Hex(), "")
			drugNames := drugNamesData["drugNames"]
			drugSpecs := drugNamesData["drugSpecs"]

			specsData := make(map[string]string)
			if drugNames != nil {
				drugNamesA := drugNames.([]string)
				for _, drugName := range drugNamesA {
					spec := ""
					if drugSpecs != nil {
						drugSpecsMap := drugSpecs.(map[string][]string)
						if len(drugSpecsMap) > 0 {
							medicineSpecs := drugSpecsMap[drugName]
							if len(medicineSpecs) > 0 {
								spec = medicineSpecs[0]
							}
						}
					}
					specsData[drugName] = spec
				}
			}

			isOpenPackage, packageAllDrugNames, _, _, _, _ := tools.IsOpenPackage(workTask.EnvironmentID)

			for index, medicine := range medicines {
				status := 22 // 待审核
				//判断是否包装扫码的药物，如果是包装药物，扫码入仓之后，保持待入仓状态
				if isOpenPackage {
					_, exist := packageAllDrugNames[medicine.Name]
					if exist {
						status = 21 // 待扫码
					}
				}
				update := bson.M{"$set": bson.M{
					// "entry_time":  time.Duration(time.Now().Unix()),    加了审核流程这个时候不会直接入仓
					"scan_status": 1,
					"status":      status,
					"name":        medicine.Name,
					"spec":        specsData[medicine.Name],
				}}
				_, err = tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": medicine.ID}, update)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				oids[index] = medicine.ID
				//药物history
				history := models.History{
					Key: "history.medicine.scanCanUse",
					OID: medicine.ID,
					//Data: bson.M{"number": v[2]},
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				}
				histories = append(histories, history)
			}
			ctx.Set("HISTORY", histories)

			//更新任务
			updateWorkTask := bson.M{"$set": bson.M{
				"info.medicine_ids":   oids,
				"info.finish_time":    time.Duration(time.Now().Unix()),
				"info.finish_user_id": user.ID,
				"info.status":         1,
			}}
			err = tools.Database.Collection("work_task").FindOneAndUpdate(sctx, bson.M{"_id": scanMedicineReq.WorkTaskID, "info.status": 0}, updateWorkTask, nil).Decode(&workTask)
			if err != nil {
				return nil, tools.BuildServerError(ctx, "work.task.error")
			}

			// 查询是否有未完成的任务，有的话不添加任务，没有再添加
			existFilter := bson.M{
				"_id":            bson.M{"$ne": scanMedicineReq.WorkTaskID},
				"customer_id":    workTask.CustomerID,
				"project_id":     workTask.ProjectID,
				"env_id":         workTask.EnvironmentID,
				"info.work_type": 1, // 扫码入仓任务
				"info.status":    0, // 状态未完成
			}
			if !workTask.CohortID.IsZero() {
				existFilter["cohort_id"] = workTask.CohortID
			}
			var existWorkTask models.WorkTask
			err = tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&existWorkTask)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			//判断是否还有待入仓的药物
			existMedicineFilter := bson.M{
				"customer_id": workTask.CustomerID,
				"project_id":  workTask.ProjectID,
				"env_id":      workTask.EnvironmentID,
				"name":        "",
				"_id":         bson.M{"$nin": oids},
			}
			var medicinesGroupsStorehouse []map[string]interface{}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: existMedicineFilter}},
				{{Key: "$group", Value: bson.M{"_id": "$storehouse_id"}}},
			}
			cursor, err := tools.Database.Collection("medicine").Aggregate(sctx, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &medicinesGroupsStorehouse)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if existWorkTask.ID.IsZero() && len(medicinesGroupsStorehouse) > 0 {
				// 查询权限扫码入仓权限的用户
				permissions := []string{"operation.build.medicine.barcode.scan"}
				siteOrStoreIDs := []primitive.ObjectID{}
				for _, storehouseId := range medicinesGroupsStorehouse {
					siteOrStoreIDs = append(siteOrStoreIDs, storehouseId["_id"].(primitive.ObjectID))
				}
				userIds, err := tools.GetPermissionUserIds(sctx, permissions, workTask.ProjectID, workTask.EnvironmentID, siteOrStoreIDs...)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				workTask := models.WorkTask{
					ID:            primitive.NewObjectID(),
					CustomerID:    workTask.CustomerID,
					ProjectID:     workTask.ProjectID,
					EnvironmentID: workTask.EnvironmentID,
					CohortID:      workTask.CohortID,
					UserIDs:       userIds,
					Info: models.WorkTaskInfo{
						WorkType:        1,
						Status:          0,
						CreatedTime:     time.Duration(time.Now().Unix()),
						Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
						MedicineIDs:     []primitive.ObjectID{},
						MedicineOrderID: primitive.NilObjectID,
						DispensingID:    primitive.NilObjectID,
					},
				}
				_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			// 记录报表轨迹
			drugTrack := []interface{}{}
			for _, medicine := range medicines {
				drugTrack = append(drugTrack, models.DrugTrack{
					ID:            primitive.NewObjectID(),
					CustomerID:    workTask.CustomerID,
					ProjectID:     workTask.ProjectID,
					EnvironmentID: workTask.EnvironmentID,
					MedicineID:    medicine.ID,
					Status:        1, // 扫码入仓
					Remarks:       "",
					OperatorID:    user.ID,
					OperatorTime:  time.Duration(time.Now().Unix()),
				})
			}
			if len(drugTrack) > 0 {
				_, err := tools.Database.Collection("drug_track").InsertMany(sctx, drugTrack)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (w *WorkTaskService) MedicineInfo(ctx *gin.Context) (map[string]interface{}, error) {
	workTaskID, _ := ctx.GetQuery("workTaskId")
	workTaskOID, _ := primitive.ObjectIDFromHex(workTaskID)
	number, _ := ctx.GetQuery("medicineNumber")
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOne(ctx, bson.M{"_id": workTaskOID}).Decode(&workTask)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	filter := bson.M{
		"customer_id": workTask.CustomerID,
		"project_id":  workTask.ProjectID,
		"env_id":      workTask.EnvironmentID,
		"number":      number,
	}
	opts := &options.FindOneOptions{
		Projection: bson.M{
			"id":             "$_id",
			"name":           "$name",
			"number":         "$number",
			"batchNumber":    "$batch_number",
			"status":         "$status",
			"expirationDate": "$expiration_date",
			"packageNumber":  "$package_number",
		},
	}
	var data map[string]interface{}
	err = tools.Database.Collection("medicine").FindOne(nil, filter, opts).Decode(&data)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	if data == nil {
		return nil, tools.BuildServerError(ctx, "medicine_not_exist")

	}
	return data, nil
}

func (w *WorkTaskService) PackageNumberInfo(ctx *gin.Context) (map[string]bool, error) {
	workTaskID, _ := ctx.GetQuery("workTaskId")
	workTaskOID, _ := primitive.ObjectIDFromHex(workTaskID)
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOne(ctx, bson.M{"_id": workTaskOID}).Decode(&workTask)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var medicineData []map[string]interface{}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"customer_id": workTask.CustomerID, "project_id": workTask.ProjectID, "env_id": workTask.EnvironmentID, "package_number": bson.M{"$nin": bson.A{nil, ""}}}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"packageNumber": "$package_number"}}}},
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	hasPackNumber := make(map[string]string)
	for _, medicine := range medicineData {
		id := medicine["_id"].(map[string]interface{})
		packageNumber := id["packageNumber"].(string)
		hasPackNumber[packageNumber] = packageNumber
	}

	//查询生成条形码里面自动生成的包装号
	var data []map[string]interface{}
	match := bson.M{"customer_id": workTask.CustomerID, "project_id": workTask.ProjectID, "env_id": workTask.EnvironmentID, "package_numbers.0": bson.M{"$exists": true}}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$project", Value: bson.M{
			"_id":            0,
			"packageNumbers": "$package_numbers",
		}}},
	}
	cursor, err = tools.Database.Collection("barcode_group").Aggregate(ctx, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	packageMap := make(map[string]bool, 0)
	for _, barcodeGroup := range data {
		packageNumbers := barcodeGroup["packageNumbers"].(primitive.A)
		for _, packageNumber := range packageNumbers {
			packageNumberStr := packageNumber.(string)
			_, ok := hasPackNumber[packageNumberStr]
			if ok {
				packageMap[packageNumberStr] = true
			} else {
				packageMap[packageNumberStr] = false
			}
		}
	}

	return packageMap, nil
}
