package service

import (
	"clinflash-irt/config"
	"clinflash-irt/convert"
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"math/rand"
	"sort"
	"strings"
	"time"

	"github.com/wxnacy/wgo/arrays"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin/binding"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type UserServer struct {
	projectService ProjectService
}

type RolePermissionsRsp []struct {
	ID                       primitive.ObjectID        `json:"_id,omitempty" bson:"_id"`
	RoleId                   primitive.ObjectID        `json:"role_id,omitempty" bson:"role_id"`
	Role                     string                    `json:"role,omitempty" bson:"role"`
	CustomerID               primitive.ObjectID        `json:"customer_id,omitempty" bson:"customer_id"`
	ProjectID                primitive.ObjectID        `json:"project_id,omitempty" bson:"project_id"`
	Name                     string                    `json:"name,omitempty"`
	Scope                    string                    `json:"scope,omitempty"`
	Description              string                    `json:"description,omitempty"`
	Template                 int                       `json:"template,omitempty"` // 1 通用模板 2、DTP
	Status                   int                       `json:"status,omitempty"`   // 2 无效  1、 有效
	Permissions              []string                  `json:"permissions,omitempty" bson:"permissions"`
	Page                     []interface{}             `json:"page,omitempty"`
	CohortDisablePermissions []CohortDisablePermission `json:"cohortDisablePermission,omitempty"`
}

type CohortDisablePermission struct {
	Permission          string `json:"permission"`
	CohortDisableStatus []int  `json:"cohortDisableStatus"`
}

func (s *UserServer) Verify(c *gin.Context, request models.UserLoginReq) (*models.Login, error) {
	loginResp := &models.UserLoginResult{}
	out := models.UserLoginResult{}
	customers := []models.CustomerInfoWithApps{}
	//ctx := tools.GrpcContext(locales.Lang(c))
	//cli := pb.NewUserClient(tools.GrpcClientConn)
	var err error

	if request.Type == models.LoginTypeLogin || request.Type == models.LoginTypeRefresh { // 登录 刷新按 原来逻辑校验
		var _login models.Login
		if err := tools.Database.Collection("login").FindOne(context.TODO(), bson.M{"token": request.Token}).Decode(&_login); err == nil {
			err = s.saveUserLoginHistory(c, models.LoginSuccess, _login.User.ID)
			if err != nil {
				return nil, err
			}
			return &_login, nil
		}
		loginResp, err = tools.UserLoginWithToken(&models.UserLoginRequest{Token: request.Token}, locales.Lang(c))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		out = *loginResp
		// 根据用户id 查customers
		customers, err = tools.ListUserCustomers(out.Id, locales.Lang(c))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		ok := false
		for _, v := range out.Apps {
			if v == config.CLOUD_KEY {
				ok = true
				break
			}
		}
		if !ok {
			return nil, errors.WithStack(tools.BuildServerError(c, "app.unauthorized"))
		}
	} else if request.Type == models.LoginTypeUnlock { // lock校验 先判断token是否存在， 不存在则往下逻辑走插入login数据
		// 校验token是否已经过期了
		hours, _ := time.ParseDuration("8h")
		one, err := tools.Database.Collection("login").UpdateOne(nil, bson.M{"token": c.GetHeader("token")},
			bson.M{
				"$set": bson.M{
					"expire_at": primitive.NewDateTimeFromTime(time.Now().Add(hours)),
				},
			})
		if one.MatchedCount == 0 {
			return nil, tools.BuildServerError(c, "", tools.LoginTimeoutError)
		}
		login := models.Login{}
		err = tools.Database.Collection("login").FindOne(nil, bson.M{"token": c.GetHeader("token")}).Decode(&login)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = tools.VerifyPassword(&models.UserAccount{
			Username: request.Username,
			Password: request.Password,
		}, locales.Lang(c))
		if err != nil {
			errMsg := err.Error()
			err = s.saveUserLoginHistory(c, models.LoginFail, login.User.ID)
			if err != nil {
				return nil, err
			}
			return nil, tools.BuildCustomError(errMsg)
		}
		// token 未过期，密码正确 直接返回
		if one.MatchedCount == 1 {
			err = s.saveUserLoginHistory(c, models.LoginSuccess, login.User.ID)
			if err != nil {
				return nil, err
			}
			return &login, nil
		}
	} else {
		return &models.Login{}, tools.BuildServerError(nil, "common.operation.fail")
	}

	var user models.User

	_ctx := context.Background()
	session, _ := tools.MongoClient.StartSession()
	defer session.EndSession(_ctx)
	if _, err := session.WithTransaction(_ctx, func(sctx mongo.SessionContext) (interface{}, error) {
		oId, err := primitive.ObjectIDFromHex(out.Id)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		uid, _ := primitive.ObjectIDFromHex(out.Id)
		if err := tools.Database.Collection("user").FindOne(_ctx, bson.M{"cloud_id": oId, "deleted": bson.M{"$ne": true}}).Decode(&user); err != nil {
			// 用户不存在时，IRT中新增此用户（Cloud中邀请的管理员才会有此情况）

			userNew := models.User{
				ID: uid,
				UserInfo: models.UserInfo{
					Email:       out.Info.Email,
					Name:        out.Info.Name,
					Phone:       out.Info.Mobile,
					Company:     out.Info.Company,
					Description: out.Info.Description,
					Version:     int(out.Version),
					Unicode:     out.Number,
				},
				Roles:         []primitive.ObjectID{},
				CloseCustomer: []primitive.ObjectID{},
				CloudId:       uid,
				UserSettings: models.UserSettings{
					Timezone:   out.Settings.Timezone,
					Dateformat: out.Settings.Dateformat,
					Tz:         out.Settings.Tz,
				},
			}
			_, err := tools.Database.Collection("user").InsertOne(_ctx, userNew)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			user = userNew
		} else {
			// 用户版本号不一致时，更新IRT中用户的信息
			if user.Version != int(out.Version) {
				update := bson.M{
					"$set": bson.M{
						"info.email":       out.Info.Email,
						"info.name":        out.Info.Name,
						"info.phone":       out.Info.Mobile,
						"info.company":     out.Info.Company,
						"info.description": out.Info.Description,
						"info.version":     out.Version,
						"cloud_id":         uid,
						"unicode":          out.Number,
					},
				}
				opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
				var latestUser models.User
				if err := tools.Database.Collection("user").FindOneAndUpdate(
					_ctx,
					bson.M{"cloud_id": uid, "deleted": bson.M{"$ne": true}},
					update,
					opts,
				).Decode(&latestUser); err != nil {
					return nil, errors.WithStack(err)
				}
				user = latestUser
			}
		}
		return nil, nil
	}); err != nil {
		return nil, err
	}

	// 检查是否为任意客户的管理员
	admin := false
Loop:
	for _, v := range customers {
		for _, tag := range v.Admin {
			if tag == config.CLOUD_KEY {
				admin = true
				break Loop
			}
		}
	}
	// 检查是否为任意项目的管理员
	projectAdmin := false
	if count, _ := tools.Database.Collection("project").CountDocuments(nil, bson.M{"administrators": user.ID}); count > 0 {
		projectAdmin = true
	}
	user.Admin = admin
	user.ProjectAdmin = projectAdmin

	// IRT中记录用户登录缓存
	hours, _ := time.ParseDuration("8h")
	login := models.Login{
		Token:    request.Token,
		User:     user,
		ExpireAt: primitive.NewDateTimeFromTime(time.Now().Add(hours)),
	}
	if _, err := tools.Database.Collection("login").InsertOne(nil, login); err != nil {
		return nil, errors.WithStack(err)
	}
	returnUser := models.Login{
		Token: request.Token,
		User:  user,
	}
	err = s.saveUserLoginHistory(c, models.LoginSuccess, login.User.ID)
	if err != nil {
		return nil, err
	}
	return &returnUser, err
}

func (s *UserServer) saveUserLoginHistory(ctx *gin.Context, success int, userId primitive.ObjectID) error {
	loginHistory := models.LoginHistory{
		ID:      primitive.NewObjectID(),
		UserId:  userId,
		IP:      ctx.ClientIP(),
		Time:    time.Duration(time.Now().Unix()),
		Success: success,
	}
	_, err := tools.Database.Collection(models.LoginHistoryTable).InsertOne(nil, loginHistory)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *UserServer) ListCustomers(ctx *gin.Context, isProjectAdmin bool, isCustomerAdmin bool) ([]*models.SelectCustomer, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	customers := make([]*models.SelectCustomer, 0)

	if isProjectAdmin {
		// 匹配用户作为项目管理员的客户
		{
			var projectAdminCustomers []models.Customer
			match := bson.M{"administrators": user.ID}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: match}},
				{{Key: "$group", Value: bson.M{"_id": "$customer_id"}}},
				{{Key: "$project", Value: bson.M{"_id": 1}}},
			}
			cursor, err := tools.Database.Collection("project").Aggregate(nil, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if err := cursor.All(context.TODO(), &projectAdminCustomers); err != nil {
				return nil, errors.WithStack(err)
			}

			// 从Cloud查询所有客户列表
			allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}
			enBool := ctx.GetHeader("Accept-Language") == "en"
			for _, v := range projectAdminCustomers {
				for _, customer := range allCustomers {
					if v.ID.Hex() == customer.Id {
						customerName := customer.Name
						if enBool && customer.NameEn != "" {
							customerName = customer.NameEn
						}
						customers = append(customers, &models.SelectCustomer{
							ID:    customer.Id,
							Name:  customerName,
							Types: []string{"project"}},
						)
						break
					}
				}
			}
		}
	}

	// 匹配用户 "作为客户管理员" 或 "关联" 的客户
	{
		// 从Cloud查询用户的客户数据
		userCustomers, err := tools.ListUserCustomersSimple(user.Email, isProjectAdmin || isCustomerAdmin, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		types := []string{}
		envs := make([]models.UserProjectEnvironment, 0)
		//关联的时候才查
		if !isProjectAdmin && !isCustomerAdmin {
			cursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"user_id": user.ID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &envs)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		enBool := ctx.GetHeader("Accept-Language") == "en"
		for _, v := range userCustomers {
			if isCustomerAdmin {
				types = []string{"admin"}
				customerName := v.Name
				if enBool && v.NameEn != "" {
					customerName = v.NameEn
				}
				customers = append(customers, &models.SelectCustomer{
					ID:    v.ID,
					Name:  customerName,
					Types: types,
				})
			} else {
				for _, customer := range userCustomers {
					// 仅显示用户在项目环境下角色数量 > 0的客户
					for _, env := range envs {
						if env.CustomerID.Hex() == customer.ID && len(env.Roles) > 0 {
							customerName := customer.Name
							if enBool && customer.NameEn != "" {
								customerName = customer.NameEn
							}
							customers = append(customers, &models.SelectCustomer{
								ID:    customer.ID,
								Name:  customerName,
								Types: types,
							})
							break
						}
					}
				}
			}
		}
	}

	// 去重，合并types
	_customers := []*models.SelectCustomer{}

	for _, v := range customers {
		exists := false
		for _, _v := range _customers {
			if v.ID == _v.ID {
				exists = true
				for _, t := range v.Types {
					typeExists := false
					for _, _t := range _v.Types {
						if _t == t {
							typeExists = true
						}
					}
					if !typeExists {
						_v.Types = append(_v.Types, v.Types...)
					}
				}
			}
		}
		if !exists {
			_customers = append(_customers, v)
		}
	}
	sort.SliceStable(_customers, func(i, j int) bool { return _customers[i].ID < _customers[j].ID })
	return _customers, nil
}

func (s *UserServer) Update(ctx *gin.Context, id string, user models.User) error {

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("user")
		oid, _ := primitive.ObjectIDFromHex(id)
		if count, _ := collection.CountDocuments(sctx, bson.M{"_id": bson.M{"$ne": oid}, "info.email": user.Email, "deleted": bson.M{"$ne": true}}); count > 0 {
			return nil, tools.BuildServerError(ctx, "users.duplicated.emails")
		}
		update := bson.M{"$set": bson.M{
			"info.email": user.Email,
		}}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": oid}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserServer) UpdateUser(ctx *gin.Context, id string, user models.User) error {

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("user")
		oid, _ := primitive.ObjectIDFromHex(id)

		update := bson.M{"$set": bson.M{
			"deleted":     user.Deleted,
			"info.status": user.Status,
		}}
		//if len(closeCustomer) != 0 {
		//	objectIDs := make([]primitive.ObjectID, 0, 1)
		//	objectID, _ := primitive.ObjectIDFromHex(closeCustomer)
		//	objectIDs = append(objectIDs, objectID)
		//	update = bson.M{"$set": bson.M{
		//		"deleted": user.Deleted,
		//		"info.status": user.Status,
		//		"close_customer": objectIDs,
		//	}}
		//}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": oid}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserServer) Delete(ctx *gin.Context, id string) error {
	oid, _ := primitive.ObjectIDFromHex(id)
	res, err := tools.Database.Collection("user").DeleteOne(nil, bson.M{"_id": oid})
	if err != nil || res.DeletedCount != 1 {
		return errors.WithStack(err)
	}
	return nil
}

func (s *UserServer) Get(ctx *gin.Context, id string) (models.User, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	var document models.User
	if err := tools.Database.Collection("user").FindOne(nil, bson.M{"_id": oid}).Decode(&document); err != nil {
		return document, errors.WithStack(err)
	}
	return document, nil
}

func (s *UserServer) GetUserByEmail(ctx *gin.Context, email string) (map[string]interface{}, error) {
	var data []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"info.email": email, "deleted": bson.M{"$ne": true}}}},
		{{Key: "$lookup", Value: bson.M{"from": "role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
		{{Key: "$project", Value: bson.M{"_id": 0, "id": 1, "email": "$info.email", "roles._id": 1, "roles.name": 1, "roles.template": 1}}},
	}
	cursor, err := tools.Database.Collection("user").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := cursor.All(nil, &data); err != nil {
		return nil, errors.WithStack(err)
	}
	if len(data) == 0 {
		return nil, nil
	}
	return data[0], nil
}

func (s *UserServer) GetUserFromCloud(ctx *gin.Context) (*models.UserData, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return users[0], nil

}

func (s *UserServer) GetUserRoles(ctx *gin.Context, projectID string, userID string) (interface{}, error) {
	userOID, _ := primitive.ObjectIDFromHex(userID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": userOID}}},
		{{Key: "$unwind", Value: "$roles"}},
		{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "role_id", "as": "role"}}},
		{{Key: "$unwind", Value: "$role"}},
		{{Key: "$match", Value: bson.M{"role.project_id": projectOID, "role.status": 1}}},
		{{Key: "$project", Value: bson.M{
			"_id":      0,
			"id":       "$role._id",
			"name":     "$role.name",
			"scope":    "$role.scope",
			"template": "$role.template",
			"status":   "$role.status"}}},
		{{Key: "$sort", Value: bson.D{{"name", 1}}}},
	}
	type projectRole struct {
		ID       primitive.ObjectID `json:"id" bson:"id"`
		Name     string             `json:"name" bson:"name"`
		Scope    string             `json:"scope" bson:"scope"`
		Template int                `json:"template" bson:"template"`
		Type     int                `json:"type" bson:"type"`
		Status   int                `json:"status" bson:"status"`
	}
	d := make([]projectRole, 0)
	rs := make([]projectRole, 0)
	cursor, err := tools.Database.Collection("user").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)

	project := models.Project{}
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if slice.Contain(project.Administrators, userOID) {
		projectAdmin := models.ProjectRolePermission{}
		err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{
			"project_id": projectOID,
			"name":       "Project-Admin",
		}).Decode(&projectAdmin)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		d = append(d, projectRole{
			ID:       projectAdmin.ID,
			Name:     projectAdmin.Name,
			Scope:    projectAdmin.Scope,
			Template: projectAdmin.Template,
			Type:     0,
			Status:   projectAdmin.Status,
		})
	}
	for _, r := range d {
		rs = append(rs, projectRole{
			ID:       r.ID,
			Name:     r.Name,
			Scope:    r.Scope,
			Template: r.Template,
			Type:     data.RolePoolMap[r.Name].Type,
			Status:   r.Status,
		})
	}

	if err != nil {
		return nil, errors.WithStack(err)

	}
	return rs, nil
}

func (s *UserServer) SetUserRoles(ctx *gin.Context, user models.User) error {
	filter := bson.M{"_id": user.ID}
	update := bson.M{"$addToSet": bson.M{"roles": bson.M{"$each": user.Roles}}}
	res, err := tools.Database.Collection("user").UpdateOne(nil, filter, update)
	if err != nil {
		return errors.WithStack(err)
	}
	if res.ModifiedCount != 1 {
		return tools.BuildServerError(ctx, "common.operation.fail")
	}
	return nil
}

// app根据当前登陆人获取可以创建订单的项目信息
func (s *UserServer) GetProjectInfos(ctx *gin.Context, permissions []string, operType int, workType int) ([]models.ProjectEnvsRoles, error) {
	//返回值
	var projectEnvsRoles []models.ProjectEnvsRoles

	me, err := tools.Me(ctx)
	if err != nil {
		return projectEnvsRoles, errors.WithStack(err)
	}

	//查询用户分配了哪些项目
	var userProjectEnv []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"user_id": me.ID, "app": true}}},
	}
	if operType == 1 {
		pipeline = append(pipeline,
			bson.D{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
			bson.D{{Key: "$unwind", Value: "$project"}},
			bson.D{{"$match", bson.M{"project.status": 0}}},
		)
	}
	pipeline = append(pipeline, bson.D{{Key: "$group", Value: bson.M{"_id": "$project_id"}}})
	cur, err := tools.Database.Collection("user_project_environment").Aggregate(nil, pipeline)
	if err != nil {
		return projectEnvsRoles, errors.WithStack(err)
	}
	err = cur.All(nil, &userProjectEnv)
	if err != nil {
		return projectEnvsRoles, errors.WithStack(err)
	}

	for _, project := range userProjectEnv {
		projectId := project["_id"].(primitive.ObjectID)
		//查询项目下，哪些角色有创建订单的角色
		var projectRoles []map[string]interface{}
		cur, err := tools.Database.Collection("project_role_permission").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"permissions": bson.M{"$in": permissions}, "project_id": projectId}}},
			{{Key: "$group", Value: bson.M{"_id": "$project_id", "roles": bson.M{"$push": "$_id"}}}},
		})
		if err != nil {
			return projectEnvsRoles, errors.WithStack(err)
		}
		err = cur.All(nil, &projectRoles)
		if err != nil {
			return projectEnvsRoles, errors.WithStack(err)
		}

		//如果有角色分配了创建订单权限，那么就查询项目下分配了创建订单角色的环境
		if len(projectRoles) > 0 {
			roles := projectRoles[0]["roles"]
			var userProjectEnvs []map[string]interface{}
			cur, err := tools.Database.Collection("user_project_environment").Aggregate(nil, mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"user_id": me.ID, "project_id": projectId, "app": true, "roles": bson.M{"$in": roles}}}},
				{{Key: "$lookup", Value: bson.M{
					"from":         "project",
					"localField":   "project_id",
					"foreignField": "_id",
					"as":           "project",
				}}},
				{{Key: "$unwind", Value: "$project"}},
				{{Key: "$unwind", Value: "$project.envs"}},
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
				{{Key: "$project", Value: bson.M{
					"_id":                   0,
					"id":                    "$_id",
					"customer_id":           1,
					"project_id":            1,
					"projectNumber":         "$project.info.number",
					"projectType":           "$project.info.type",
					"connectEdc":            "$project.info.connect_edc",
					"edcSupplier":           "$project.info.edc_supplier",
					"pushMode":              "$project.info.push_mode",
					"registerPush":          "$project.info.push_scenario.register_push",
					"randomPush":            "$project.info.push_scenario.random_push",
					"randomBlockPush":       "$project.info.push_scenario.random_block_push",
					"formRandomBlockPush":   "$project.info.push_scenario.form_random_block_push",
					"cohortRandomBlockPush": "$project.info.push_scenario.cohort_random_block_push",
					"dispensingPush":        "$project.info.push_scenario.dispensing_push",
					"screenPush":            "$project.info.push_scenario.screen_push",
					"envsName":              "$project.envs.name",
					"cohorts":               "$project.envs.cohorts",
					"env_id":                1,
					"user_id":               1,
					"roles":                 1,
					"roles2":                roles,
				}}},
				{{Key: "$project", Value: bson.M{
					"id":                    1,
					"customer_id":           1,
					"project_id":            1,
					"env_id":                1,
					"user_id":               1,
					"project":               1,
					"projectNumber":         1,
					"projectType":           1,
					"connectEdc":            1,
					"edcSupplier":           1,
					"pushMode":              1,
					"registerPush":          1,
					"randomPush":            1,
					"randomBlockPush":       1,
					"formRandomBlockPush":   1,
					"cohortRandomBlockPush": 1,
					"dispensingPush":        1,
					"screenPush":            1,
					"envsName":              1,
					"cohorts":               1,
					"commonRoles":           bson.M{"$setIntersection": bson.A{"$roles", "$roles2"}},
				}}},
			})
			if err != nil {
				return projectEnvsRoles, errors.WithStack(err)
			}
			err = cur.All(nil, &userProjectEnvs)
			if err != nil {
				return projectEnvsRoles, errors.WithStack(err)
			}

			//如果项目下有环境分配了这些角色
			if len(userProjectEnvs) > 0 {
				var projectEnvRoles models.ProjectEnvsRoles
				customerId := userProjectEnvs[0]["customer_id"].(primitive.ObjectID)
				projectEnvRoles.CustomerID = customerId
				projectEnvRoles.ProjectID = projectId
				projectEnvRoles.ProjectName = userProjectEnvs[0]["projectNumber"].(string)
				projectEnvRoles.ProjectType = int(userProjectEnvs[0]["projectType"].(int32))
				projectEnvRoles.ConnectEdc = int(userProjectEnvs[0]["connectEdc"].(int32))
				if projectEnvRoles.ConnectEdc == 1 {
					if userProjectEnvs[0]["edcSupplier"] != nil {
						projectEnvRoles.EdcSupplier = int(userProjectEnvs[0]["edcSupplier"].(int32))
					}
					if userProjectEnvs[0]["pushMode"] != nil {
						projectEnvRoles.PushMode = int(userProjectEnvs[0]["pushMode"].(int32))
					}
					if userProjectEnvs[0]["registerPush"] != nil {
						projectEnvRoles.RegisterPush = userProjectEnvs[0]["registerPush"].(bool)
					}
					if userProjectEnvs[0]["randomPush"] != nil {
						projectEnvRoles.RandomPush = userProjectEnvs[0]["randomPush"].(bool)
					}
					if userProjectEnvs[0]["randomBlockPush"] != nil {
						projectEnvRoles.RandomBlockPush = userProjectEnvs[0]["randomBlockPush"].(bool)
					}
					if userProjectEnvs[0]["formRandomBlockPush"] != nil {
						projectEnvRoles.FormRandomBlockPush = userProjectEnvs[0]["formRandomBlockPush"].(bool)
					}
					if userProjectEnvs[0]["cohortRandomBlockPush"] != nil {
						projectEnvRoles.CohortRandomBlockPush = userProjectEnvs[0]["cohortRandomBlockPush"].(bool)
					}
					if userProjectEnvs[0]["dispensingPush"] != nil {
						projectEnvRoles.DispensingPush = userProjectEnvs[0]["dispensingPush"].(bool)
					}
					if userProjectEnvs[0]["screenPush"] != nil {
						projectEnvRoles.ScreenPush = userProjectEnvs[0]["screenPush"].(bool)
					}
				}
				var envRoles []models.EnvRoles
				for _, v := range userProjectEnvs {
					envId := v["env_id"].(primitive.ObjectID)

					envName := v["envsName"].(string)
					envCommonRoles := v["commonRoles"]

					var permissions []models.ProjectRolePermission
					cursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"_id": bson.M{"$in": envCommonRoles}})
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &permissions)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					var roles []models.RoleBlind
					for _, p := range permissions {
						roles = append(roles, models.RoleBlind{
							Name:  p.Name,
							ID:    p.ID,
							Scope: p.Scope,
							Blind: data.RolePoolMap[p.Name].Type == 2 || data.RolePoolMap[p.Name].Type == 3,
						})
					}

					var cohorts []models.CohortInfo
					if v["cohorts"] != nil {
						for _, v := range v["cohorts"].(primitive.A) {
							cohort := v.(map[string]interface{})
							if projectEnvRoles.ProjectType == 3 && !tools.InRandomIsolation(projectEnvRoles.ProjectName) {
								cohorts = append(cohorts, models.CohortInfo{
									ID:     cohort["id"].(primitive.ObjectID),
									Name:   cohort["name"].(string),
									Status: int(cohort["status"].(int32)),
								})
								//break // 在app 端过滤，选择时仅展示第一阶段
							} else {
								cohortStatus := int(cohort["status"].(int32))
								if cohortStatus != 1 && cohortStatus != 3 && cohortStatus != 4 {
									cohorts = append(cohorts, models.CohortInfo{
										ID:     cohort["id"].(primitive.ObjectID),
										Name:   cohort["name"].(string),
										Status: cohortStatus,
									})
								}
							}
						}
					}

					envRole := models.EnvRoles{
						EnvID:   envId,
						EnvName: envName,
						Cohorts: cohorts,
						Roles:   roles,
					}
					//如果是包装扫码 workType 12
					if workType == 0 || workType == 12 {
						isOpenPackage, _, _, _, mixPackage, err := tools.IsOpenPackage(envId)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						if isOpenPackage {
							envRole.MixedPackage = mixPackage
						}
					}

					//查询环境下是否有对应任务
					if workType == 1 || workType == 12 {
						checkCondition := bson.M{
							"customer_id":    customerId,
							"project_id":     projectId,
							"env_id":         envId,
							"info.work_type": workType,
							"info.status":    0,
							"deleted":        bson.M{"$ne": true},
						}
						if count, _ := tools.Database.Collection("work_task").CountDocuments(nil, checkCondition); count > 0 {
							envRoles = append(envRoles, envRole)
						}
					} else {
						envRoles = append(envRoles, envRole)
					}
				}
				projectEnvRoles.Envs = envRoles
				if workType == 1 || workType == 12 {
					if len(envRoles) > 0 {
						projectEnvsRoles = append(projectEnvsRoles, projectEnvRoles)
					}
				} else {
					projectEnvsRoles = append(projectEnvsRoles, projectEnvRoles)
				}
			}
		}
	}
	return projectEnvsRoles, nil
}

func (s *UserServer) GetProjectEnvironmentRolesByPermission(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, workTaskId string) (*RolePermissionsRsp, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	workTaskOID, _ := primitive.ObjectIDFromHex(workTaskId)
	//cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"project_id": projectOID, "env_id": envOID, "user_id": me.ID, "app": true}
	if customerID != "" {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		filter["customer_id"] = customerOID
	}
	var user models.UserProjectEnvironment
	var project models.Project
	err = tools.Database.Collection("user_project_environment").FindOne(ctx, filter).Decode(&user)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	if err == mongo.ErrNoDocuments {
		return nil, nil
	}

	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": user.ProjectID}).Decode(&project); err != nil {
		return nil, errors.WithStack(err)
	}
	template := 1
	if project.ResearchAttribute == 1 {
		template = 2
	}

	match := bson.M{"user_id": user.UserID, "project_id": projectOID, "env_id": envOID}
	//如果当前角色的分类是site，查看该用户分配的中心
	var userSiteIds []primitive.ObjectID
	var userSites []models.UserSite
	cursor, err := tools.Database.Collection("user_site").Find(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &userSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, userSite := range userSites {
		userSiteIds = append(userSiteIds, userSite.SiteID)
	}

	//如果当前角色的分类是depot,查看该用户分配的仓库，
	var userDepotIds []primitive.ObjectID
	var userDepots []models.UserDepot
	cursor, err = tools.Database.Collection("user_depot").Find(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &userDepots)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, userDepot := range userDepots {
		userDepotIds = append(userDepotIds, userDepot.DepotID)
	}

	var workTask models.WorkTask
	err = tools.Database.Collection("work_task").FindOne(ctx, bson.M{"_id": workTaskOID}).Decode(&workTask)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//1扫码入仓 2发送订单确认 3研究产品接收 4发药 5补发药 6订单待运送 7创建订单审批 8紧急揭盲审批 9访视外发药 10pv揭盲审批 11发放通知 12包装扫码  13回收订单待确认 14回收订单待运送 15回收订单待接收
	var instituteIds []primitive.ObjectID
	if workTask.Info.WorkType == 2 || workTask.Info.WorkType == 3 || workTask.Info.WorkType == 6 || workTask.Info.WorkType == 13 || workTask.Info.WorkType == 14 || workTask.Info.WorkType == 15 {
		var order models.MedicineOrder
		err := tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": workTask.Info.MedicineOrderID}).Decode(&order)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		instituteIds = append(instituteIds, order.SendID)
		if order.Type != 5 && order.Type != 6 {
			instituteIds = append(instituteIds, order.ReceiveID)
		}
	} else if workTask.Info.WorkType == 4 || workTask.Info.WorkType == 5 || workTask.Info.WorkType == 9 || workTask.Info.WorkType == 11 {
		var dispensing models.Dispensing
		err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": workTask.Info.DispensingID}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		instituteIds = append(instituteIds, subject.ProjectSiteID)
	} else if workTask.Info.WorkType == 7 {
		var orderAddTask models.OrderAddTask
		err := tools.Database.Collection("approval_process").FindOne(ctx, bson.M{"_id": workTask.Info.ApprovalProcessID}).Decode(&orderAddTask)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		instituteIds = append(instituteIds, orderAddTask.Data.SendID)
		instituteIds = append(instituteIds, orderAddTask.Data.ReceiveID)
	} else if workTask.Info.WorkType == 8 {
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": workTask.Info.SubjectApproval.SubjectID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		instituteIds = append(instituteIds, subject.ProjectSiteID)
	} else if workTask.Info.WorkType == 10 {
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": workTask.Info.SubjectPvApproval.SubjectID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		instituteIds = append(instituteIds, subject.ProjectSiteID)
	}

	var permissions bson.A
	switch workTask.Info.WorkType {
	case 1:
		permissions = bson.A{"operation.build.medicine.barcode.scan"}
	case 2:
		permissions = bson.A{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
	case 3:
		permissions = bson.A{"operation.supply.shipment.receive", "operation.supply.shipment.lose", "operation.supply.shipment.terminated"}
	case 4:
		permissions = bson.A{"operation.subject.medicine.dispensing"}
	case 5:
		permissions = bson.A{"operation.subject.medicine.reissue"}
	case 6:
		permissions = bson.A{"operation.supply.shipment.send", "operation.supply.shipment.lose", "operation.supply.shipment.receive", "operation.supply.shipment.close"}
	case 7:
		permissions = bson.A{"operation.supply.shipment.approval"}
	case 8:
		permissions = bson.A{"operation.subject.unblinding-approval"}
	case 9:
		permissions = bson.A{"operation.subject.medicine.out-visit-dispensing"}
	case 10:
		permissions = bson.A{"operation.subject.unblinding-pv-approval"}
	case 11:
		permissions = bson.A{"operation.subject.medicine.dispensing", "operation.subject-dtp.medicine.dispensing"}
	case 12:
		permissions = bson.A{"operation.build.medicine.barcode.scanPackage"}
	case 13:
		permissions = bson.A{"operation.supply.recovery.cancel", "operation.supply.recovery.determine"}
	case 14:
		permissions = bson.A{"operation.supply.recovery.confirm", "operation.supply.recovery.lose", "operation.supply.recovery.receive", "operation.supply.recovery.close"}
	case 15:
		permissions = bson.A{"operation.supply.recovery.receive", "operation.supply.recovery.lose", "operation.supply.recovery.end"}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id": bson.M{
				"$in": user.Roles},
			"template":    template,
			"status":      1,
			"permissions": bson.M{"$in": permissions}}}},
		{{Key: "$project", Value: bson.M{
			"role_id":     "$_id",
			"role":        "$name",
			"permissions": 1,
			"template":    1,
			"status":      1,
			"scope":       1,
		}}},
	}

	cursor, err = tools.Database.Collection("project_role_permission").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var rolePermissionsRsp RolePermissionsRsp
	err = cursor.All(nil, &rolePermissionsRsp)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var rsp RolePermissionsRsp
	for _, v := range rolePermissionsRsp {
		if workTask.Info.WorkType != 1 {
			if v.Scope == "study" {
				rsp = append(rsp, v)
			} else if v.Scope == "site" {
				if len(userSiteIds) > 0 {
					siteFlag := false
					for _, instituteId := range instituteIds {
						if contains(userSiteIds, instituteId) {
							siteFlag = true
							break
						}
					}
					if siteFlag {
						rsp = append(rsp, v)
					}
				}
			} else if v.Scope == "depot" {
				if len(userDepotIds) > 0 {
					depotFlag := false
					for _, instituteId := range instituteIds {
						if contains(userDepotIds, instituteId) {
							depotFlag = true
							break
						}
					}
					if depotFlag {
						rsp = append(rsp, v)
					}
				}
			}
		} else {
			rsp = append(rsp, v)
		}
	}

	return &rsp, nil
}

func (s *UserServer) GetProjectEnvironmentRoles(ctx *gin.Context, projectID string, envID string) (*RolePermissionsRsp, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	filter := bson.M{"project_id": projectOID, "env_id": envOID, "user_id": me.ID}
	var user models.UserProjectEnvironment
	var project models.Project
	if err := tools.Database.Collection("user_project_environment").FindOne(nil, filter).Decode(&user); err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	if user.ID == primitive.NilObjectID {
		return nil, errors.WithStack(err)
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": user.ProjectID}).Decode(&project); err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	template := 1
	if project.ResearchAttribute == 1 {
		template = 2
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id": bson.M{
				"$in": user.Roles},
			"template": template,
			"status":   1,
		}}},
		{{Key: "$project", Value: bson.M{
			"role_id":     "$_id",
			"role":        "$name",
			"permissions": 1,
			"template":    1,
			"status":      1,
			"scope":       1,
		}}},
	}
	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var rsp RolePermissionsRsp
	err = cursor.All(nil, &rsp)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var envStatus int
	envp, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envp
	if project.Type == 1 && env.Status != nil {
		envStatus = *env.Status
	}
	cohortDisablePermissions := make([]CohortDisablePermission, 0)
	getMenuChildren(data.MenuPermissions, &cohortDisablePermissions)
	// 获取角色权限对应的页面
	for i, rolePermission := range rsp {
		set := tools.SetFactory()
		var permissionFilter []string
		for _, permission := range rolePermission.Permissions {
			page, _ := data.GetOperation(permission)
			//这里只能过滤掉项目状态和环境状态的权限，cohort状态的权限需要通过GetDisablePermission接口来获取在前端过滤
			if data.GetFrozenOperationDisable(permission, project.Status, envStatus, env.LockConfig) {
				continue
			}
			permissionFilter = append(permissionFilter, permission)
			set.Add(page.Page)
		}
		rsp[i].Permissions = permissionFilter
		rsp[i].Page = set.IterKey()
		rsp[i].CohortDisablePermissions = cohortDisablePermissions
	}

	return &rsp, nil
}

func getMenuChildren(menus []data.MenuPermission, cohortDisablePermissions *[]CohortDisablePermission) {
	for _, menu := range menus {
		if menu.Children != nil && len(menu.Children) > 0 {
			getMenuChildren(menu.Children, cohortDisablePermissions)
		}
		if menu.FrozenOperations != nil && len(menu.FrozenOperations) > 0 {
			for _, operation := range menu.FrozenOperations {
				if operation.DisableCohortStatus != nil && len(operation.DisableCohortStatus) > 0 {
					*cohortDisablePermissions = append(*cohortDisablePermissions, CohortDisablePermission{
						Permission:          operation.Text,
						CohortDisableStatus: operation.DisableCohortStatus,
					})
				}
			}

		}
	}
}
func (s *UserServer) UpdateUserSites(ctx *gin.Context, customerID string, projectID string, envID string, data models.UserSites) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "user_id": data.UserID}
		//对比新旧数组，拿出新增的中心写入项目动态
		oldSites := make([]primitive.ObjectID, 0)
		userEmail := ""
		{
			oldUserSites := make([]models.UserSite, 0)
			cursor, err := tools.Database.Collection("user_site").Find(sctx, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &oldUserSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			user := models.User{}
			err = tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": data.UserID}).Decode(&user)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if len(users) > 0 {
				userData := users[0]
				userEmail = userData.Info.Email
			} else {
				return nil, tools.BuildServerError(ctx, "common.operation.fail")
			}

			me, err := tools.Me(ctx)
			if err != nil {
				return nil, err
			}
			oldSites = slice.Map(oldUserSites, func(index int, item models.UserSite) primitive.ObjectID {
				return item.SiteID
			})

			newSiteIds := make([]primitive.ObjectID, 0)
			newSiteIds = slice.Difference(data.Sites, oldSites)
			if len(newSiteIds) > 0 {
				newProjectSites := make([]models.ProjectSite, 0)
				cursor, err = tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": newSiteIds}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(sctx, &newProjectSites)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				//组装项目动态数据
				now := time.Duration(time.Now().Unix())
				projectDynamics := slice.Map(newSiteIds, func(index int, item primitive.ObjectID) interface{} {
					siteName := ""
					siteP, ok := slice.Find(newProjectSites, func(index int, i models.ProjectSite) bool {
						return i.ID == item
					})
					site := *siteP
					if ok {
						siteName = models.GetProjectSiteName(ctx, site)
					}
					return models.ProjectDynamics{
						ID:          primitive.NewObjectID(),
						Operator:    me.ID,
						OID:         envOID,
						Time:        now,
						SceneTran:   "project_dynamics_scene_personnel",
						TypeTran:    "project_dynamics_type_enter_site",
						ContentTran: "project_dynamics_content_enter_site",
						ContentData: map[string]interface{}{
							"siteId":   item,
							"userId":   data.UserID,
							"siteName": siteName,
							"email":    userEmail,
						},
					}
				})
				_, err = tools.Database.Collection("project_dynamics").InsertMany(sctx, projectDynamics)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		//先删除原有的
		if _, err := tools.Database.Collection("user_site").DeleteMany(sctx, match); err != nil {
			return nil, errors.WithStack(err)
		}

		//增加现有的
		var sitesData = make([]interface{}, len(data.Sites))
		for index, siteID := range data.Sites {
			sitesData[index] = models.UserSite{
				ID:         primitive.NewObjectID(),
				CustomerID: customerOID,
				ProjectID:  projectOID,
				EnvID:      envOID,
				UserID:     data.UserID,
				SiteID:     siteID,
			}
		}

		if len(sitesData) == 0 {
			return nil, nil
		}
		_, err := tools.Database.Collection("user_site").InsertMany(sctx, sitesData)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//项目日志
		var userProjectEnvironment models.UserProjectEnvironment
		tools.Database.Collection("user_project_environment").FindOne(sctx, bson.M{"user_id": data.UserID, "env_id": envOID}).Decode(&userProjectEnvironment)
		insertSitesProjectUserLog(ctx, sctx, envOID, 5, oldSites, data.Sites, userEmail, userProjectEnvironment.ID)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserServer) UpdateUserStorehouses(ctx *gin.Context, customerID string, projectID string, envID string, data models.UserDepots) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		collection := tools.Database.Collection("user_depot")
		match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "user_id": data.UserID}
		//对比新旧数组，拿出新增的中心写入项目动态
		oldStorehouses := make([]primitive.ObjectID, 0)
		userEmail := ""
		{
			oldUserStorehouses := make([]models.UserDepot, 0)
			cursor, err := tools.Database.Collection("user_depot").Find(sctx, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &oldUserStorehouses)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			user := models.User{}
			err = tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": data.UserID}).Decode(&user)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if len(users) > 0 {
				userData := users[0]
				userEmail = userData.Info.Email
			} else {
				return nil, tools.BuildServerError(ctx, "common.operation.fail")
			}

			me, err := tools.Me(ctx)
			if err != nil {
				return nil, err
			}
			oldStorehouses = slice.Map(oldUserStorehouses, func(index int, item models.UserDepot) primitive.ObjectID {
				return item.DepotID
			})
			newStorehouseIds := slice.Difference(data.Depots, oldStorehouses)
			if len(newStorehouseIds) > 0 {
				newProjectStorehouses := make([]models.ProjectStorehouse, 0)
				cursor, err = tools.Database.Collection("project_storehouse").Find(sctx, bson.M{"_id": bson.M{"$in": newStorehouseIds}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(sctx, &newProjectStorehouses)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				storehouses := make([]models.Storehouse, 0)
				storehouseIds := slice.Map(newProjectStorehouses, func(index int, item models.ProjectStorehouse) primitive.ObjectID {
					return item.StorehouseID
				})
				cursor, err = tools.Database.Collection("storehouse").Find(sctx, bson.M{"_id": bson.M{"$in": storehouseIds}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(sctx, &storehouses)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				//组装项目动态数据
				now := time.Duration(time.Now().Unix())
				projectDynamics := slice.Map(newStorehouseIds, func(index int, item primitive.ObjectID) interface{} {
					storehouseName := ""
					projectStorehouseP, ok := slice.Find(newProjectStorehouses, func(index int, i models.ProjectStorehouse) bool {
						return i.ID == item
					})
					if ok {
						projectStorehouse := *projectStorehouseP
						storehouseP, sok := slice.Find(storehouses, func(index int, s models.Storehouse) bool {
							return s.ID == projectStorehouse.StorehouseID
						})
						storehouse := *storehouseP
						if sok {
							storehouseName = storehouse.Name
						}

					}
					return models.ProjectDynamics{
						ID:          primitive.NewObjectID(),
						Operator:    me.ID,
						OID:         envOID,
						Time:        now,
						SceneTran:   "project_dynamics_scene_personnel",
						TypeTran:    "project_dynamics_type_bind_storehouse",
						ContentTran: "project_dynamics_content_bind_storehouse",
						ContentData: map[string]interface{}{
							"storehouseId":   item,
							"userId":         data.UserID,
							"storehouseName": storehouseName,
							"email":          userEmail,
						},
					}
				})
				_, err = tools.Database.Collection("project_dynamics").InsertMany(sctx, projectDynamics)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

		}

		//先删除原有的
		if _, err := collection.DeleteMany(sctx, match); err != nil {
			return nil, errors.WithStack(err)
		}

		//增加现有的
		var depotsData = make([]interface{}, len(data.Depots))
		for index, depotID := range data.Depots {
			depotsData[index] = models.UserDepot{
				ID:         primitive.NewObjectID(),
				CustomerID: customerOID,
				ProjectID:  projectOID,
				EnvID:      envOID,
				UserID:     data.UserID,
				DepotID:    depotID,
			}
		}

		if len(depotsData) == 0 {
			return nil, nil
		}
		_, err := collection.InsertMany(sctx, depotsData)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//项目日志
		var userProjectEnvironment models.UserProjectEnvironment
		tools.Database.Collection("user_project_environment").FindOne(sctx, bson.M{"user_id": data.UserID, "env_id": envOID}).Decode(&userProjectEnvironment)
		insertDepotsProjectUserLog(ctx, sctx, envOID, 5, oldStorehouses, data.Depots, userEmail, userProjectEnvironment.ID)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserServer) GetLearnCourse(ctx *gin.Context, projectID string, envID string) (map[string]interface{}, error) {
	if config.LearnON == "0" {
		d := map[string]interface{}{
			"courses": 0,
		}
		return d, nil
	}
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//获取关联所有项目 以及公共课程的

	// 获取当前用户的角色列表
	var role []models.RolePermission
	var roleName []string
	var roleNameProject []string
	var customerId string
	if len(user.Roles) == 0 {
		return map[string]interface{}{
			"courses": 0,
		}, nil
	}
	// // 系统级别角色
	cursor, _ := tools.Database.Collection("role_permission").Find(nil, bson.M{"_id": bson.M{"$in": user.Roles}})
	err = cursor.All(nil, &role)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, item := range role {
		roleName = append(roleName, item.Name)
	}
	// 查询项目
	var project models.Project
	// 项目 级别角色 ID
	var projects []map[string]interface{}
	if projectID != "" {
		var roles []map[string]interface{}
		projectOID, _ := primitive.ObjectIDFromHex(projectID)

		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		customerId = project.CustomerID.Hex()

		envOID, _ := primitive.ObjectIDFromHex(envID)
		envCursor, _ := tools.Database.Collection("user_project_environment").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"project_id": projectOID, "env_id": envOID, "user_id": user.ID}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_role_permission",
				"localField":   "roles",
				"foreignField": "_id",
				"as":           "role",
			}}},
			{{Key: "$unwind", Value: "$role"}},

			{{Key: "$project", Value: bson.M{
				"name": "$role.name",
			}}},
		})
		err = envCursor.All(nil, &roles)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, item := range roles {
			roleNameProject = append(roleNameProject, item["name"].(string))
		}

		projects = append(projects, map[string]interface{}{
			"id":    projectOID,
			"roles": roleNameProject,
		})
	}

	// 客户管理员
	customerAdmin := false
	var customerProject []map[string]interface{}
	// 项目管理员
	projectAdmin := false
	var adminProject []map[string]interface{}
	var customerIds []string
	var customerOIDs []primitive.ObjectID

	// 1、请求cloud 获取客户信息
	out, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	userData := out[0]
	user, err = convert.CloudUserToUser(&user, userData)
	if err != nil {
		return nil, err
	}

	// 查询当前用户关联的所有客户ID
	for _, customer := range userData.Customers {
		for _, aps := range customer.Apps {
			if aps == config.CLOUD_KEY {
				if filterCustomerStrId(customerIds, customer.Id) {
					customerIds = append(customerIds, customer.Id)
				}
			}
		}
	}

	// 查询customerIds
	if customerId != "" {
		// 2、根据Customers.admin == irt 是否存在判断是否为客户管理员
		for _, customer := range userData.Customers {
			if customer.Id == customerId {
				for _, item := range customer.Admin {
					if item == config.CLOUD_KEY {
						customerOID, _ := primitive.ObjectIDFromHex(customer.Id)
						if filterCustomerObjectId(customerOIDs, customerOID) {
							customerOIDs = append(customerOIDs, customerOID)
						}
					}
				}
			}
		}

		// 3、 为客户管理员 需查询 customer_admin_projects
		if len(customerOIDs) > 0 {
			customerAdmin = true
			cursor, _ = tools.Database.Collection("project").Aggregate(nil, mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"customer_id": bson.M{"$in": customerOIDs}, "info.connect_learning": 1}}},
				{{Key: "$project", Value: bson.M{
					"_id":    0,
					"id":     "$_id",
					"number": "$info.number",
					"name":   "$info.name",
				}}},
			})
			err := cursor.All(nil, &customerProject)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// 4、 为项目管理员 需查询 project
		// 4.1先判断当前用户在当前项目下是不是管理员角色
		var p models.Project
		pOID, _ := primitive.ObjectIDFromHex(projectID)
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": pOID, "administrators": user.ID, "info.connect_learning": 1}).Decode(&p)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if p.ID != primitive.NilObjectID {
			projectAdmin = true
			// 4.2 查询当前用户在当前客户下管理了多少项目（项目管理员）
			cursor, _ = tools.Database.Collection("project").Aggregate(nil, mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"administrators": user.ID, "info.connect_learning": 1, "customer_id": p.CustomerID}}},
				{{Key: "$project", Value: bson.M{
					"_id":    0,
					"id":     "$_id",
					"number": "$info.number",
					"name":   "$info.name",
				}}},
			})
			err = cursor.All(nil, &adminProject)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
	}

	if len(user.Roles) == 0 && !customerAdmin {
		return map[string]interface{}{
			"courses": 0,
		}, nil
	}
	request := map[string]interface{}{
		"email":                   user.Email,
		"name":                    user.Name,
		"roles":                   roleName,
		"projects":                projects,
		"customer_admin":          customerAdmin,   // 客户管理员标签
		"customer_admin_projects": customerProject, // 客户管理员对应的课程
		"project_admin":           projectAdmin,    // 项目管理员标签
		"project_admin_projects":  adminProject,    // 项目管理员对应的课程
		"customer":                customerId,      // 当前客户ID
		"customers":               customerIds,     // 当前用户关联的所有客户ID
	}
	url := config.Learn
	secret := "62274d3c82756e4be6d951db2caaba71ba9076a36914f2c9a806e47e3294ad18"
	// 首次登陆   获取全部项目课程 + 公共课程token
	token := ""
	if projectID == "" {
		// 获取关联所有项目 以及公共课程的
		projects = append(projects, map[string]interface{}{
			"id":    "null project",
			"roles": roleName,
		})
		envCursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"user_id": user.ID}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_role_permission",
				"localField":   "roles",
				"foreignField": "_id",
				"as":           "role",
			}}},
			{{Key: "$unwind", Value: "$role"}},
			{{Key: "$project", Value: bson.M{
				"id":    "$project_id",
				"roles": "$role.name",
			}}},
			{{Key: "$group", Value: bson.M{
				"_id":   "$id",
				"roles": bson.M{"$addToSet": "$roles"},
			}}},
			{{Key: "$project", Value: bson.M{
				"_id":   0,
				"id":    bson.M{"$toString": "$_id"},
				"roles": "$roles",
			}}},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var allProjectRoles []map[string]interface{}
		err = envCursor.All(nil, &allProjectRoles)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(roleName) != 0 {
			allProjectRoles = append(allProjectRoles, map[string]interface{}{
				"id":    "null project",
				"roles": roleName,
			})
		}

		request := map[string]interface{}{
			"email":                   user.Email,
			"name":                    user.Name,
			"roles":                   roleName,
			"projects":                allProjectRoles,
			"customer_admin":          customerAdmin,   // 客户管理员标签
			"customer_admin_projects": customerProject, // 客户管理员对应的课程
			"project_admin":           projectAdmin,    // 项目管理员标签
			"project_admin_projects":  adminProject,    // 项目管理员对应的课程
			"customer":                customerId,      // 当前客户ID
			"customers":               customerIds,     // 当前用户关联的所有客户ID
		}
		allProjectByte, err := tools.PostJson(ctx, url+"/api/users/test", request, map[string]string{"key": "irt", "secret": secret})
		if err != nil {
			panic(err)
		}

		allProjectByteData, err := tools.ResultData(allProjectByte)
		token = allProjectByteData.(map[string]interface{})["token"].(string)

	}
	bytes, err := tools.PostJson(ctx, url+"/api/users/test", request, map[string]string{"key": "irt", "secret": secret})
	if err != nil {
		panic(err)
	}
	resultData, err := tools.ResultData(bytes)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	resultData.(map[string]interface{})["url"] = config.Learn
	if token != "" {
		resultData.(map[string]interface{})["token"] = token
	}
	return resultData.(map[string]interface{}), nil
}

// SwitchAppAccount 用户app权限开关
func (s *UserServer) SwitchAppAccount(ctx *gin.Context, envId string, userID string, checked bool) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		userId, _ := primitive.ObjectIDFromHex(userID)
		envOID, _ := primitive.ObjectIDFromHex(envId)
		var user models.User
		err := tools.Database.Collection("user").FindOne(nil, bson.M{"_id": userId}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)

		}

		out, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		userData := out[0]
		if userData.Info.Mobile == "" && checked == true {
			return nil, tools.BuildServerError(ctx, "users.missing.phone")
		}
		update := bson.M{"$set": bson.M{"app": checked}}
		_, err = tools.Database.Collection("user_project_environment").UpdateOne(sctx, bson.M{"user_id": userId, "env_id": envOID}, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//插入日志
		var userProjectEnv models.UserProjectEnvironment
		err = tools.Database.Collection("user_project_environment").FindOne(ctx, bson.M{"env_id": envOID, "user_id": user.ID}).Decode(&userProjectEnv)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		insertAppProjectUserLog(ctx, sctx, envOID, 2, !checked, checked, user.Email, userProjectEnv.ID)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil

}

func (s *UserServer) GetUserInfo(ctx *gin.Context) (map[string]interface{}, error) {
	var d = make(map[string]interface{}, 3)
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var json map[string]interface{}
		_ = ctx.ShouldBindBodyWith(&json, binding.JSON)
		cloudID := json["cloudId"].(string)
		email := json["email"].(string)
		registrationId := json["registrationId"].(string)
		cloudOID, _ := primitive.ObjectIDFromHex(cloudID)
		var user models.User
		err := tools.Database.Collection("user").FindOne(sctx, bson.M{"cloud_id": cloudOID}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		_, err = tools.Database.Collection("login").DeleteMany(sctx, bson.M{"user._id": user.ID, "app": 1})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 给用户表更新 registrationId（用于极光推送消息通知）
		userFilter := bson.M{"_id": user.ID}
		userUpdate := bson.M{"$set": bson.M{"registration_id": registrationId}}
		_, err = tools.Database.Collection("user").UpdateOne(sctx, userFilter, userUpdate)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// IRT中记录用户登录缓存
		hours, _ := time.ParseDuration("6h")
		login := models.Login{
			Token:    tools.Secret(email),
			User:     user,
			ExpireAt: primitive.NewDateTimeFromTime(time.Now().Add(hours)),
			App:      1,
		}
		if _, err := tools.Database.Collection("login").InsertOne(sctx, login); err != nil {
			return nil, errors.WithStack(err)
		}
		d["token"] = login.Token
		d["name"] = login.UserInfo.Name
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	return d, nil
}

// GetIdentifyCode 获取验证码
func (s *UserServer) GetIdentifyCode(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		phone, _ := ctx.GetQuery("phone")
		userData, err := tools.UserFind(&models.UserFindRequest{
			Mobile: phone,
		}, locales.Lang(ctx))
		if err != nil {
			return nil, tools.BuildServerError(ctx, "users.phone.not.exist")
		}
		var user models.User
		user, err = convert.CloudUserToUser(&user, userData)
		if err != nil {
			return nil, err
		}
		err = tools.Database.Collection("user").FindOne(sctx, bson.M{"cloud_id": user.CloudId}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		envs := make([]models.UserProjectEnvironment, 0)
		cursor, err := tools.Database.Collection("user_project_environment").Find(sctx, bson.M{"user_id": user.ID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &envs)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, exist := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
			return item.App
		})
		if user.ID.IsZero() || !exist {
			return nil, tools.BuildServerError(ctx, "users.phone.not.exist")
		}
		rand.Seed(time.Now().UnixNano())
		code := rand.Intn(1000000)
		codeStr := fmt.Sprintf("%0*d", 6, code)
		// IRT中记录用户登录缓存
		hours, _ := time.ParseDuration("5m")
		login := models.IdentifyCode{
			ID:       primitive.NewObjectID(),
			Phone:    phone,
			Code:     codeStr,
			ExpireAt: primitive.NewDateTimeFromTime(time.Now().Add(hours)),
		}
		if _, err := tools.Database.Collection("identify_code").InsertOne(nil, login); err != nil {
			panic(err)
		}
		err = tools.SendVerificationCode(codeStr, phone)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserServer) AppLogin(ctx *gin.Context) (map[string]interface{}, error) {
	var d = make(map[string]interface{}, 3)
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var json map[string]interface{}
		_ = ctx.ShouldBindBodyWith(&json, binding.JSON)
		phone := json["phone"].(string)
		code := json["code"].(string)
		registrationId := json["registrationId"].(string)
		userData, err := tools.UserFind(&models.UserFindRequest{
			Mobile: phone,
		}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		cloudOID, _ := primitive.ObjectIDFromHex(userData.Id)
		var user models.User
		err = tools.Database.Collection("user").FindOne(sctx, bson.M{"cloud_id": cloudOID}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//取cloud的属性
		user, err = convert.CloudUserToUser(&user, userData)
		if err != nil {
			return nil, err
		}
		var identifyCodes []models.IdentifyCode
		cursor, err := tools.Database.Collection("identify_code").Find(sctx, bson.M{"phone": phone})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &identifyCodes)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(identifyCodes) == 0 {
			return nil, tools.BuildServerError(ctx, "users.identify.code.incorrect")

		}
		codes := make([]string, len(identifyCodes))
		for i, identifyCode := range identifyCodes {
			codes[i] = identifyCode.Code
		}
		if !tools.InStr(code, codes) {
			return nil, tools.BuildServerError(ctx, "users.identify.code.incorrect")
		}
		_, err = tools.Database.Collection("login").DeleteMany(sctx, bson.M{"user.info.phone": phone, "app": 1})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 给用户表更新 registrationId（用于极光推送消息通知）
		userFilter := bson.M{"_id": user.ID}
		userUpdate := bson.M{"$set": bson.M{"registration_id": registrationId}}
		_, err = tools.Database.Collection("user").UpdateOne(sctx, userFilter, userUpdate)

		// IRT中记录用户登录缓存
		hours, _ := time.ParseDuration("6h")
		login := models.Login{
			Token:    tools.Secret(user.UserInfo.Email),
			User:     user,
			ExpireAt: primitive.NewDateTimeFromTime(time.Now().Add(hours)),
			App:      1,
		}
		if _, err := tools.Database.Collection("login").InsertOne(sctx, login); err != nil {
			return nil, errors.WithStack(err)
		}
		d["token"] = login.Token
		d["name"] = login.UserInfo.Name
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	return d, nil
}

func (s *UserServer) AppForgotPassword(ctx *gin.Context) (map[string]interface{}, error) {
	var d = make(map[string]interface{}, 3)
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var json map[string]interface{}
		_ = ctx.ShouldBindBodyWith(&json, binding.JSON)
		email := json["email"].(string)
		err := tools.UserForgotPassword(&models.UserForgetPasswordRequest{
			Email: email,
		}, locales.Lang(ctx))
		if err != nil {
			return nil, tools.BuildCustomError(err.Error())
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	return d, nil
}

func (s *UserServer) AppLoginWithPassword(ctx *gin.Context) (map[string]interface{}, error) {
	var d = make(map[string]interface{}, 3)
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var json map[string]interface{}
		_ = ctx.ShouldBindBodyWith(&json, binding.JSON)
		username := json["username"].(string)
		password := json["password"].(string)
		registrationId := json["registrationId"].(string)
		userData, err := tools.UserLoginWithAccount(&models.UserLoginRequest{
			Username: username,
			Password: password,
		}, locales.Lang(ctx))
		if err != nil {
			return nil, tools.BuildCustomError(err.Error())
		}
		cloudOID, _ := primitive.ObjectIDFromHex(userData.Data.Id)
		var user models.User
		err = tools.Database.Collection("user").FindOne(sctx, bson.M{"cloud_id": cloudOID}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		_, err = tools.Database.Collection("login").DeleteMany(sctx, bson.M{"_id": user.ID, "app": 1})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 给用户表更新 registrationId（用于极光推送消息通知）
		userFilter := bson.M{"_id": user.ID}
		userUpdate := bson.M{"$set": bson.M{"registration_id": registrationId}}
		_, err = tools.Database.Collection("user").UpdateOne(sctx, userFilter, userUpdate)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// IRT中记录用户登录缓存
		hours, _ := time.ParseDuration("6h")
		login := models.Login{
			Token:    tools.Secret(userData.Data.Info.Email),
			User:     user,
			ExpireAt: primitive.NewDateTimeFromTime(time.Now().Add(hours)),
			App:      1,
		}
		if _, err := tools.Database.Collection("login").InsertOne(sctx, login); err != nil {
			return nil, errors.WithStack(err)
		}
		d["token"] = login.Token
		d["name"] = login.UserInfo.Name
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	return d, nil
}

func (s *UserServer) AppLogout(ctx *gin.Context) (map[string]interface{}, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 给用户表更新 registrationId（退出登陆后不在推送消息通知）
		userFilter := bson.M{"_id": user.ID}
		userUpdate := bson.M{"$set": bson.M{"registration_id": ""}}
		_, err = tools.Database.Collection("user").UpdateOne(sctx, userFilter, userUpdate)
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return nil, nil
}

// 修改app语言
func (s *UserServer) AppLanguage(ctx *gin.Context) (map[string]interface{}, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var json map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&json, binding.JSON)
	language := json["language"].(string)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		userFilter := bson.M{"_id": user.ID}
		userUpdate := bson.M{"$set": bson.M{"app_language": language}}
		_, err = tools.Database.Collection("user").UpdateOne(sctx, userFilter, userUpdate)
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return nil, nil
}

func (s *UserServer) Edit(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		ID := ctx.Query("id")
		id, _ := primitive.ObjectIDFromHex(ID)
		name := ctx.Query("name")
		phone := ctx.Query("phone")
		if phone != "" {
			var user models.User
			_ = tools.Database.Collection("user").FindOne(sctx, bson.M{"info.phone": phone}).Decode(&user)
			if !user.ID.IsZero() && user.ID != id {
				return nil, tools.BuildServerError(ctx, "user.phone.exist")
			}
		}
		update := bson.M{"$set": bson.M{
			"info.name":  name,
			"info.phone": phone,
		}}
		_, err := tools.Database.Collection("user").UpdateOne(sctx, bson.M{"_id": id}, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserServer) GetMainRolePermission(ctx *gin.Context) (map[string]interface{}, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var roles []interface{}
	if user.ProjectAdmin {
		roles = append(roles, "Project-Admin")
	}
	if user.Admin {
		roles = append(roles, "Customer-Admin")
	}
	if user.Group == 1 {
		roles = append(roles, "Sys-Admin")
	}
	pageSet := tools.SetFactory()
	pageSet.Add("/report")
	pageSet.Add("/")
	//多语言
	resp, err := s.projectService.GetViewMultiLanguage(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp != nil && len(resp) > 0 {
		pageSet.Add("/multiLanguage")
	}
	res := map[string]interface{}{}
	if len(roles) == 0 {
		// 三种角色都没有
		res["pages"] = pageSet.IterKey()
		return res, nil
	}
	cursor, err := tools.Database.Collection("role_permission").Find(nil, bson.M{"name": bson.M{"$in": roles}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	datas := []models.RolePermission{}
	cursor.All(nil, &datas)

	permissionsSet := tools.SetFactory()
	for _, item := range datas {
		for _, permission := range item.Permissions {
			permissionsSet.Add(permission)
			page, _ := data.GetOperation(permission)
			pageSet.Add(page.Page)
		}
	}
	res["permissions"] = permissionsSet.IterKey()
	if permissionsSet.Len() == 0 {
		res["permissions"] = []string{}
	}
	res["pages"] = pageSet.IterKey()
	if pageSet.Len() == 0 {
		res["pages"] = []string{}
	}
	res["projectPermissions"] = []string{}
	if user.ProjectAdmin {
		for _, rolePermission := range datas {
			if rolePermission.Name == "Project-Admin" {
				res["projectPermissions"] = rolePermission.Permissions
				break
			}
		}
	}
	res["sysPermissions"] = []string{}
	if user.Group == 1 {
		for _, rolePermission := range datas {
			if rolePermission.Name == "Sys-Admin" {
				res["sysPermissions"] = rolePermission.Permissions
				break
			}
		}
	}
	res["customerPermissions"] = []string{}
	if user.Admin {
		for _, rolePermission := range datas {
			if rolePermission.Name == "Customer-Admin" {
				res["customerPermissions"] = rolePermission.Permissions
				break
			}
		}
	}
	return res, nil
}

// UserProjectSwitch 查询用户所有的项目环境
func (s *UserServer) UserProjectSwitch(ctx *gin.Context, keyword string) ([]map[string]interface{}, error) {
	u, _ := ctx.Get("user")
	user := u.(models.User)

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"user_id": user.ID, "roles.0": bson.M{"$exists": 1}, "unbind": bson.M{"$ne": true}}}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: "$project"}},
		{{Key: "$unwind", Value: "$project.envs"}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		{{Key: "$group", Value: bson.M{"_id": "$project_id", "project": bson.M{"$push": "$project"}}}},
		{{Key: "$project", Value: bson.M{
			"_id":            0,
			"administrators": bson.M{"$first": "$project.administrators"},
			"customer_id":    bson.M{"$first": "$project.customer_id"},
			"id":             "$_id",
			"status":         bson.M{"$first": "$project.status"},
			"info":           bson.M{"$first": "$project.info"},
			"envs":           "$project.envs"}}},
		{{Key: "$sort", Value: bson.D{{"info.number", 1}}}},
		//{{Key: "$skip", Value: 0}},
		//{{Key: "$limit", Value: 6}},
	}
	if keyword != "" {
		pipeline = append(pipeline, bson.D{{Key: "$match", Value: bson.D{{"$or",
			bson.A{
				bson.M{"info.number": bson.M{"$regex": keyword}},
				bson.M{"info.name": bson.M{"$regex": keyword}},
			},
		}}}})
	}
	pipeline = append(pipeline, bson.D{{Key: "$skip", Value: 0}})
	pipeline = append(pipeline, bson.D{{Key: "$limit", Value: 20}})

	cursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []map[string]interface{}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return data, nil
}

func (s *UserServer) UpdateUserStatus(ctx *gin.Context, id string) error {

	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		oid, _ := primitive.ObjectIDFromHex(id)

		var user models.User
		err := tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": oid}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 创建一个 string 切片
		var irtCloudIds []string
		// 添加一个元素到 string 切片中
		irtCloudIds = append(irtCloudIds, user.CloudId.Hex())

		//2.查询
		//ctx *gin.Context
		userFetchResp, e := tools.UserFetch(&models.UserFetchRequest{Ids: irtCloudIds}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(e)
		}

		for _, cloudUser := range userFetchResp {
			if user.UserInfo.Status != int8(cloudUser.Status) {

				//禁用的用户
				if user.UserInfo.Status != int8(2) && cloudUser.Status == int32(2) {
					//设置为已关闭状态
					if cloudUser.Customers != nil {
						customers := cloudUser.Customers
						for _, customer := range customers {
							if customer.Apps != nil {
								if stringInSlice(config.CLOUD_KEY, customer.Apps) {
									closed(ctx, user.ID.Hex(), customer.Id, 2)
									break
								}
							}
						}
					}
				}

				//TODO:启用操作-由禁用变成启用
				if user.UserInfo.Status == int8(2) && cloudUser.Status != int32(2) {
					if cloudUser.Customers != nil {
						customers := cloudUser.Customers
						for _, customer := range customers {
							if customer.Apps != nil {
								if stringInSlice(config.CLOUD_KEY, customer.Apps) {
									id, _ := primitive.ObjectIDFromHex(customer.Id)
									_, err = tools.Database.Collection("user").UpdateOne(nil,
										bson.M{"_id": user.ID},
										bson.M{"$pull": bson.M{"close_customer": id}, "$set": bson.M{"info.status": int8(cloudUser.Status)}})
									if err != nil {
										return nil, errors.WithStack(err)
									}
									break
								}
							}
						}
					}
				}

			}

			//已经删除的用户
			if user.Deleted == false && cloudUser.Deleted == true {
				//设置为已关闭状态
				if cloudUser.Customers != nil {
					customers := cloudUser.Customers
					for _, customer := range customers {
						if customer.Apps != nil {
							if stringInSlice(config.CLOUD_KEY, customer.Apps) {
								closed(ctx, user.ID.Hex(), customer.Id, 1)
								break
							}
						}
					}
				}

			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 筛选客户ID(string字符串)
func filterCustomerStrId(customerIds []string, customerId string) bool {
	if customerIds != nil && len(customerIds) > 0 {
		for _, cid := range customerIds {
			if cid == customerId {
				return false
			}
		}
	}
	return true
}

// 筛选客户ID(ObjectId)
func filterCustomerObjectId(customerIds []primitive.ObjectID, customerId primitive.ObjectID) bool {
	if customerIds != nil && len(customerIds) > 0 {
		for _, cid := range customerIds {
			if cid == customerId {
				return false
			}
		}
	}
	return true
}

// 查询固定权限的用户
//func FilterUsers(sctx mongo.SessionContext, permission []string, projectID primitive.ObjectID, envID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
//
//	// 查询项目权限角色
//	projectRoles := make([]models.ProjectRolePermission, 0)
//	cursor, err := tools.Database.Collection("project_role_permission").Find(sctx, bson.M{"project_id": projectID, "permissions": bson.M{"$in": permission}})
//	if err != nil {
//		return false, errors.WithStack(err)
//	}
//	err = cursor.All(sctx, &projectRoles)
//	if err != nil {
//		return false, errors.WithStack(err)
//	}
//	// 查询有该角色的用户
//	var roleIds []primitive.ObjectID
//	for _, pr := range projectRoles {
//		roleIds = append(roleIds, pr.ID)
//	}
//	if roleIds != nil && len(roleIds) > 0 {
//		count, err := tools.Database.Collection("user_project_environment").CountDocuments(sctx, bson.M{"project_id": projectID, "env_id": envID, "user_id": userID, "roles": bson.M{"$in": roleIds}, "app": true})
//		if err != nil {
//			return false, errors.WithStack(err)
//		}
//		if count > 0 {
//			return true, nil
//		}
//	}
//	return false, nil
//}

func (s *UserServer) UpdateUserRolesSitesDepots(ctx *gin.Context, customerID string, projectID string, envID string, data models.UserRolesSitesDepots) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		projectOID, _ := primitive.ObjectIDFromHex(projectID)
		envOID, _ := primitive.ObjectIDFromHex(envID)

		if data.RolesSitesDepotList != nil && len(data.RolesSitesDepotList) > 0 {
			for _, rolesSitesDepot := range data.RolesSitesDepotList {

				//角色
				if rolesSitesDepot.RoleList != nil && len(rolesSitesDepot.RoleList) > 0 {
					var projectEnvironmentUser models.ProjectEnvironmentUser
					projectEnvironmentUser.CustomerID = customerOID
					projectEnvironmentUser.ProjectID = projectOID
					projectEnvironmentUser.EnvID = envOID
					projectEnvironmentUser.Roles = rolesSitesDepot.RoleList
					err := s.projectService.SetProjectEnvironmentUserRoles(ctx, projectID, envID, rolesSitesDepot.UserID.Hex(), projectEnvironmentUser)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

				//中心
				if rolesSitesDepot.SiteList != nil && len(rolesSitesDepot.SiteList) > 0 {
					var userSites models.UserSites
					userSites.CustomerID = customerOID
					userSites.ProjectID = projectOID
					userSites.EnvID = envOID
					userSites.UserID = rolesSitesDepot.UserID
					userSites.Sites = rolesSitesDepot.SiteList
					err := s.UpdateUserSites(ctx, customerID, projectID, envID, userSites)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

				//仓库
				if rolesSitesDepot.DepotList != nil && len(rolesSitesDepot.DepotList) > 0 {
					var userDepots models.UserDepots
					userDepots.CustomerID = customerOID
					userDepots.ProjectID = projectOID
					userDepots.EnvID = envOID
					userDepots.UserID = rolesSitesDepot.UserID
					userDepots.Depots = rolesSitesDepot.DepotList
					err := s.UpdateUserStorehouses(ctx, customerID, projectID, envID, userDepots)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserServer) GetUserUnbindRoles(ctx *gin.Context, customerID string, projectID string, envID string, userRolesSitesDepots models.UserRolesSitesDepots) (string, error) {

	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	blind := false
	//查询
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"customer_id": customerOID, "_id": projectOID}).Decode(&project)
	if err != nil {
		return "", errors.WithStack(err)
	}

	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if project.Type != 1 {
		cohortIds := make([]primitive.ObjectID, 0)
		// 筛选环境
		for _, env := range project.Environments {
			if env.ID == envOID {
				for _, cohort := range env.Cohorts {
					cohortIds = append(cohortIds, cohort.ID)
				}
				match = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": bson.M{"$in": cohortIds}}
				break
			}
		}
	}

	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(ctx, match)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if len(attributes) > 0 {
		for _, attribute := range attributes {
			if attribute.AttributeInfo.Blind {
				blind = true
			}
		}
	}

	unbindRoleNameList := make([]string, 0)
	if userRolesSitesDepots.RolesSitesDepotList != nil && len(userRolesSitesDepots.RolesSitesDepotList) > 0 {
		for _, rolesSitesDepot := range userRolesSitesDepots.RolesSitesDepotList {

			//角色
			if rolesSitesDepot.RoleList != nil && len(rolesSitesDepot.RoleList) > 0 {

				projectRolePermissionList := make([]models.ProjectRolePermission, 0)
				cu, err := tools.Database.Collection("project_role_permission").Find(ctx, bson.M{"customer_id": customerOID, "project_id": projectOID, "_id": bson.M{"$in": rolesSitesDepot.RoleList}})
				if err != nil {
					return "", errors.WithStack(err)
				}
				err = cu.All(ctx, &projectRolePermissionList)
				if err != nil {
					return "", errors.WithStack(err)
				}

				if projectRolePermissionList != nil && len(projectRolePermissionList) > 0 {
					for _, projectRolePermission := range projectRolePermissionList {
						t := data.RolePoolMap[projectRolePermission.Name].Type
						if blind && t == 1 {
							index := arrays.ContainsString(unbindRoleNameList, projectRolePermission.Name)
							if index == -1 {
								//-1说明不存在
								unbindRoleNameList = append(unbindRoleNameList, projectRolePermission.Name)
							}
						}
					}
				}

			}

		}
	}

	unbindRoleNames := ""
	if unbindRoleNameList != nil && len(unbindRoleNameList) > 0 {
		unbindRoleNames = strings.Join(unbindRoleNameList, "、")
	}

	return unbindRoleNames, nil
}

func (s *UserServer) GetProjectEnvironmentRolesPermissions(ctx *gin.Context, projectID string) ([]string, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	filter := bson.M{"project_id": projectOID, "user_id": me.ID}
	userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
	userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(ctx, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userProjectEnvironmentCursor.All(ctx, &userProjectEnvironmentList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var project models.Project
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project); err != nil {
		return nil, errors.WithStack(err)
	}
	template := 1
	if project.ResearchAttribute == 1 {
		template = 2
	}

	roleList := make([]primitive.ObjectID, 0)
	if userProjectEnvironmentList != nil && len(userProjectEnvironmentList) > 0 {
		for _, userProjectEnvironment := range userProjectEnvironmentList {
			if userProjectEnvironment.Roles != nil && len(userProjectEnvironment.Roles) > 0 {
				for _, roleOID := range userProjectEnvironment.Roles {
					index := arrays.Contains(roleList, roleOID)
					if index == -1 {
						roleList = append(roleList, roleOID)
					}
				}
			}
		}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id": bson.M{
				"$in": roleList},
			"template": template,
			"status":   1,
		}}},
		{{Key: "$project", Value: bson.M{
			"role_id":     "$_id",
			"role":        "$name",
			"permissions": 1,
			"template":    1,
			"status":      1,
			"scope":       1,
		}}},
	}
	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var rsp RolePermissionsRsp
	err = cursor.All(nil, &rsp)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	stringArray := make([]string, 0)
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.view")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.add")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.edit")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.delete")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.trail")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.details.view")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.details.edit")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.details.preview")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.details.downloadTemplate")
	stringArray = append(stringArray, "operation.projects.project.multiLanguage.details.batchExport")

	permissionList := make([]string, 0)
	if rsp != nil && len(rsp) > 0 {
		for _, rolePermission := range rsp {
			for _, permission := range rolePermission.Permissions {
				stringIndex := arrays.ContainsString(stringArray, permission)
				if stringIndex != -1 {
					index := arrays.Contains(permissionList, permission)
					if index == -1 {
						permissionList = append(permissionList, permission)
					}
				}
			}

		}
	}

	//调用cloud 获取用户列表
	resp, err := tools.UserFetchWithCustomer(&models.UserFetchWithCustomerRequest{
		CustomerId: project.CustomerID.Hex(),
		Keyword:    me.Email,
		Start:      int64(1),
		Limit:      int64(20),
	}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cloudUsers := resp.Users
	//先获取userIds查本地user表 主要为了给出roles字段   role字段过滤无效部分
	cloudIds := make([]string, len(cloudUsers))
	for i, user := range cloudUsers {
		cloudIds[i] = user.Id
	}

	for _, cloudUser := range cloudUsers {
		//循环user下的客户id 判断是否为admin
		admin := false
	Loop:
		for _, customer := range cloudUser.Customers {
			if customer.Id == project.CustomerID.Hex() {
				for _, a := range customer.Admin {
					if a == config.CLOUD_KEY {
						admin = true
						break Loop
					}
				}
			}
		}

		if admin {
			var rolePermission models.RolePermission
			if err := tools.Database.Collection("role_permission").FindOne(nil, bson.M{"name": "Customer-Admin"}).Decode(&rolePermission); err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			if rolePermission.Permissions != nil && len(rolePermission.Permissions) > 0 {
				for _, permission := range rolePermission.Permissions {
					stringIndex := arrays.ContainsString(stringArray, permission)
					if stringIndex != -1 {
						index := arrays.Contains(permissionList, permission)
						if index == -1 {
							permissionList = append(permissionList, permission)
						}
					}
				}
			}
		}

	}

	return permissionList, nil
}
