package service

import (
	"clinflash-irt/data"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"clinflash-irt/ws"
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type ProjectRoleService struct {
	menuPermissionService MenuPermissionService
}

func (s *ProjectRoleService) List(ctx *gin.Context, projectId string, search string, all string) ([]models.ProjectRolePermission, error) {
	projectOId, err := primitive.ObjectIDFromHex(projectId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	d := make([]models.ProjectRolePermission, 0)
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"project_id": projectOId,
			"$or": bson.A{
				bson.M{"name": search},
				bson.M{"name": bson.M{"$regex": search}},
			},
		}}},
	}
	if all == "" {
		pipeline = append(pipeline, bson.D{
			{Key: "$match", Value: bson.M{
				"status": 1,
			}},
		})
	}
	pipeline = append(pipeline, bson.D{
		{Key: "$sort", Value: bson.M{
			"name": 1,
		}},
	})
	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return d, nil
}

func (s *ProjectRoleService) ListPool(ctx *gin.Context, template int) (interface{}, error) {
	roles := make([]models.RolePermission, 0)
	cursor, err := tools.Database.Collection("role_permission").Find(nil, bson.M{"template": template}, &options.FindOptions{
		Sort: bson.D{{"name", 1}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &roles)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	roleWithTypes := slice.Map(roles, func(index int, item models.RolePermission) models.RolePermissionWithType {
		typeRoleP, ok := slice.Find(data.RolePools, func(index int, pool models.RolePool) bool {
			return item.Name == pool.Name
		})
		if ok {
			typeRole := *typeRoleP
			return models.RolePermissionWithType{
				ID:          item.ID,
				Type:        typeRole.Type,
				Name:        item.Name,
				Scope:       item.Scope,
				Description: item.Description,
				Template:    item.Template,
				Status:      item.Status,
				Permissions: item.Permissions,
			}
		}
		return models.RolePermissionWithType{}
	})
	roleWithTypes = slice.Filter(roleWithTypes, func(index int, item models.RolePermissionWithType) bool {
		return !item.ID.IsZero() && !slice.Contain([]int{4, 5}, item.Type)
	})
	return roleWithTypes, nil

}

func (s *ProjectRoleService) Add(ctx *gin.Context, projectRole models.ProjectRolePermission) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if count, _ := tools.Database.Collection("project_role_permission").CountDocuments(sctx, bson.M{"project_id": projectRole.ProjectID, "name": projectRole.Name}); count > 0 {
			return nil, tools.BuildServerError(ctx, "roles.duplicated.names")
		}
		projectRole.ID = primitive.NewObjectID()
		if _, err := tools.Database.Collection("project_role_permission").InsertOne(sctx, projectRole); err != nil {
			return nil, errors.WithStack(err)
		}

		//插入操作日志
		var oldProjecRole models.ProjectRolePermission
		insertAddProjectRoleLog(ctx, sctx, projectRole.ProjectID, 1, oldProjecRole, projectRole, projectRole.ProjectID)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectRoleService) Update(ctx *gin.Context, id string, projectRole models.ProjectRolePermission) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("project_role_permission")
		oid, _ := primitive.ObjectIDFromHex(id)
		if count, _ := collection.CountDocuments(sctx, bson.M{"_id": bson.M{"$ne": oid}, "project_id": projectRole.ProjectID, "name": projectRole.Name, "template": projectRole.Template}); count > 0 {
			return nil, tools.BuildServerError(ctx, "roles.duplicated.names")
		}

		var oldProjecRole models.ProjectRolePermission
		_ = collection.FindOne(sctx, bson.M{"_id": oid}).Decode(&oldProjecRole)

		update := bson.M{"$set": bson.M{
			"description": projectRole.Description,
			"scope":       projectRole.Scope,
			"status":      projectRole.Status,
			"permissions": projectRole.Permissions,
		}}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": oid}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		if projectRole.Status == 2 {
			tools.Database.Collection("user_project_environment").UpdateMany(sctx,
				bson.M{"project_id": projectRole.ProjectID, "roles": oid},
				bson.M{"$pull": bson.M{"roles": oid}})
		}
		//插入操作日志
		insertAddProjectRoleLog(ctx, sctx, projectRole.ProjectID, 2, oldProjecRole, projectRole, projectRole.ProjectID)
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	//websocket通知
	oid, _ := primitive.ObjectIDFromHex(id)
	userProjectEnvironments := make([]models.UserProjectEnvironment, 0)
	cursor, err := tools.Database.Collection("user_project_environment").Find(ctx, bson.M{
		"project_id": projectRole.ProjectID,
		"roles":      bson.M{"$in": [1]primitive.ObjectID{oid}},
	})
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	err = cursor.All(ctx, &userProjectEnvironments)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	if userProjectEnvironments != nil && len(userProjectEnvironments) > 0 {
		idList := make([]primitive.ObjectID, 0)
		for _, upe := range userProjectEnvironments {
			index := arrays.Contains(idList, upe.UserID)
			if index == -1 {
				idList = append(idList, upe.UserID)
			}
		}

		loginList := make([]models.Login, 0)
		loginCursor, err := tools.Database.Collection("login").Find(ctx, bson.M{
			"user._id": bson.M{"$in": idList},
		})
		if err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)
		}
		err = loginCursor.All(ctx, &loginList)
		if err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)
		}

		userIdList := make([]primitive.ObjectID, 0)
		if loginList != nil && len(loginList) > 0 {
			for _, login := range loginList {
				index := arrays.Contains(userIdList, login.User.ID)
				if index == -1 {
					userIdList = append(userIdList, login.User.ID)
				}
			}
		}

		if userIdList != nil && len(userIdList) > 0 {
			wsResult := models.WsResult{
				ProjectID: projectRole.ProjectID,
				RoleId:    oid,
				Text:      "refreshPermissions",
			}
			for _, userId := range userIdList {
				go func() {
					wsResult.UserId = userId
					jsonData, _ := json.Marshal(wsResult)
					jsonString := string(jsonData)
					ws.Notify(userId.Hex(), jsonString)
				}()
			}
		}
	}

	return nil
}

func (s *ProjectRoleService) Delete(ctx *gin.Context, id string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oid, _ := primitive.ObjectIDFromHex(id)
		//判断角色是否已经被使用
		projectRole := models.ProjectRolePermission{}
		err := tools.Database.Collection("project_role_permission").FindOne(sctx, bson.M{"_id": oid}).Decode(&projectRole)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		match := bson.M{"project_id": projectRole.ProjectID, "roles": oid}
		if count, _ := tools.Database.Collection("user_project_environment").CountDocuments(sctx, match); count > 0 {
			return nil, tools.BuildServerError(ctx, "roles.delete.message")
		}
		res, err := tools.Database.Collection("project_role_permission").DeleteOne(sctx, bson.M{"_id": oid})
		if err != nil || res.DeletedCount != 1 {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *ProjectRoleService) Get(ctx *gin.Context, id string) (models.ProjectRolePermission, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	var document models.ProjectRolePermission
	if err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": oid}).Decode(&document); err != nil {
		return models.ProjectRolePermission{}, errors.WithStack(err)
	}
	return document, nil
}

func (s *ProjectRoleService) HasBindUser(ctx *gin.Context, id string) (interface{}, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	count, err := tools.Database.Collection("user_project_environment").CountDocuments(nil, bson.M{"roles": oid})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return count, nil
}

func (s *ProjectRoleService) Permission(ctx *gin.Context, projectId string) ([]models.ProjectRolePermission, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	cursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": projectOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []models.ProjectRolePermission
	if err := cursor.All(nil, &data); err != nil {
		return nil, errors.WithStack(err)
	}
	return data, nil
}
