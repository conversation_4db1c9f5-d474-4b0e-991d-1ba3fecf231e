package service

import (
	"clinflash-irt/apphtml"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"image"
	"image/draw"
	"image/png"
	"math/rand"
	"net/url"
	"os"
	"strconv"
	"time"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/code128"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/math/fixed"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type BarcodeService struct{}

func (s *BarcodeService) GetList(ctx *gin.Context) (map[string]interface{}, error) {
	var err error
	customerId := ctx.Query("customerId")
	customerID, _ := primitive.ObjectIDFromHex(customerId)
	projectId := ctx.Query("projectId")
	projectID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envID, _ := primitive.ObjectIDFromHex(envId)
	cohortID, _ := primitive.ObjectIDFromHex(ctx.Query("cohortId"))
	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	//attributeId := ctx.Query("attributeId")
	//roleId := ctx.Query("roleId")
	filter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	if !cohortID.IsZero() {
		filter = bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "cohort_id": cohortID}
	}
	lookup := bson.M{"from": "medicine", "localField": "medicine_ids", "foreignField": "_id", "as": "medicines"}
	projectStorehouseLookup := bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}
	storehouseLookup := bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}

	project := bson.M{
		"_id": 0,
		"id":  "$_id",
		//"drugName":       "$drug_name",
		"spec":           "$spec",
		"effectiveTime":  "$effective_time",
		"batchNumber":    "$batch_number",
		"count":          bson.M{"$size": "$medicines"},
		"storehouseName": bson.M{"$ifNull": bson.A{"$storehouse.name", ""}},
		"availableCount": bson.M{"$size": bson.M{"$filter": bson.M{
			"input": "$medicines",
			"as":    "medicine",
			"cond": bson.M{
				"$eq": bson.A{"$$medicine.scan_status", 0},
			},
		}}},
		"packageCount": bson.M{
			"$cond": bson.M{
				"if":   bson.M{"$eq": bson.A{bson.M{"$type": "$package_numbers"}, "missing"}},
				"then": 0,
				"else": bson.M{"$size": "$package_numbers"}}},
		"creatTime":     "$creat_time",
		"correlationId": "$correlation_id",
	}
	var datas []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: lookup}},
		{{Key: "$lookup", Value: projectStorehouseLookup}},
		{{Key: "$lookup", Value: storehouseLookup}},
		{{Key: "$project", Value: project}},
		{{Key: "$sort", Value: bson.D{{"creatTime", -1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}
	total, err := tools.Database.Collection("barcode_group").CountDocuments(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor, err := tools.Database.Collection("barcode_group").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &datas)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// if roleId != "" {
	// 	isBlindedRole, err := tools.IsBlindedRole(roleId)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	if isBlindedRole {
	// 		IsBlindDrugMap, err := tools.IsBlindDrugMap(envID)
	// 		if err != nil {
	// 			return nil, errors.WithStack(err)
	// 		}
	// 		for i := 0; i < len(datas); i++ {
	// 			if datas[i]["drugName"] != nil {
	// 				drugName := datas[i]["drugName"].(string)
	// 				isBlind, Ok := IsBlindDrugMap[drugName]
	// 				if Ok {
	// 					if isBlind {
	// 						datas[i]["drugName"] = tools.BlindData
	// 					}
	// 				} else {
	// 					datas[i]["drugName"] = tools.BlindData
	// 				}
	// 			}
	// 		}
	// 	}
	// }

	// 查询cohort是手动编码还是自动编码
	barcodeRuleFilter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	if !cohortID.IsZero() { // 群组或者在随机项目
		barcodeRuleFilter = bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "cohort_id": cohortID}
	}
	var barcodeRule models.BarcodeRule
	err = tools.Database.Collection("barcode_rule").FindOne(nil, barcodeRuleFilter).Decode(&barcodeRule)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//是否按包装运输
	packageIsOpen, _, _, _, _, _ := tools.IsOpenPackage(envID)

	return map[string]interface{}{"total": total, "datas": datas, "codeRule": barcodeRule.CodeRule, "packageIsOpen": packageIsOpen}, nil
}

func (s *BarcodeService) GetBarcodeTaskList(ctx *gin.Context) (map[string]interface{}, error) {
	var err error
	customerId := ctx.Query("customerId")
	customerID, _ := primitive.ObjectIDFromHex(customerId)
	projectId := ctx.Query("projectId")
	projectID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envID, _ := primitive.ObjectIDFromHex(envId)
	filter := bson.M{
		"customer_id": customerID,
		"project_id":  projectID,
		"env_id":      envID,
		"correlation_id": bson.M{
			"$exists": true,            // 确保字段存在
			"$nin":    bson.A{nil, ""}, // 排除 null 和空字符串
		},
	}
	project := bson.M{
		"_id":           0,
		"id":            "$_id",
		"correlationId": "$correlation_id",
	}
	var datas []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine",
			"let": bson.M{
				"medicineIds": "$medicine_ids",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"status": bson.M{"$in": bson.A{0, 1}}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$medicineIds"}}}},
			},
			"as": "medicines",
		}}},
		{{Key: "$match", Value: bson.M{"medicines": bson.M{"$ne": bson.A{}}}}},
		{{Key: "$project", Value: project}},
		{{Key: "$sort", Value: bson.D{{Key: "creatTime", Value: -1}}}},
	}
	cursor, err := tools.Database.Collection("barcode_group").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &datas)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return map[string]interface{}{"datas": datas}, nil
}

func (s *BarcodeService) GetBarcodeTask(ctx *gin.Context) (map[string]interface{}, error) {
	datas := make([]map[string]interface{}, 0)
	roleID := ctx.Query("roleId")
	id := ctx.Query("id")
	envID := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envID)
	//是否是盲态角色
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, err
	}

	//查询盲态药物
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if id != "" && len(id) > 0 {
		OID, _ := primitive.ObjectIDFromHex(id)
		filter := bson.M{"_id": OID}
		lookup := bson.M{"from": "medicine", "localField": "medicine_ids", "foreignField": "_id", "as": "medicines"}
		project := bson.M{
			"availableMedicines": bson.M{"$filter": bson.M{
				"input": "$medicines",
				"as":    "medicine",
				"cond": bson.M{"$and": bson.A{
					bson.M{"$in": bson.A{"$$medicine.status", bson.A{0, 1}}},
					bson.M{"$ne": bson.A{"$$medicine.storehouse_id", bson.A{nil, primitive.NilObjectID}}},
				}},
			}},
		}
		var data []map[string]interface{}
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: lookup}},
			{{Key: "$project", Value: project}},
		}
		cursor, err := tools.Database.Collection("barcode_group").Aggregate(ctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(ctx, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if data[0] != nil {
			availableMedicines := data[0]["availableMedicines"].(primitive.A)
			datas = make([]map[string]interface{}, len(availableMedicines))
			for index, medicines := range availableMedicines {
				datas[index] = medicines.(map[string]interface{})
			}
		}
	} else {
		project := bson.M{
			"_id":             1,
			"name":            1,
			"status":          1,
			"batch_number":    1,
			"expiration_date": 1,
			"number":          1,
		}

		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{
				"env_id":        envOID,
				"status":        1,
				"storehouse_id": bson.M{"$ne": bson.A{nil, primitive.NilObjectID}},
				"$or": bson.A{
					bson.M{"short_code": nil},
					bson.M{"short_code": ""},
				},
			}}},
			{{Key: "$project", Value: project}},
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(ctx, &datas)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if datas != nil && len(datas) > 0 {
		for _, med := range datas {
			name := med["name"].(string)
			isBlindedDrug, ok := isBlindDrugMap[name]
			if !ok {
				isBlinded, _ := tools.IsBlindedDrug(envOID, name)
				isBlindedDrug = isBlinded
				isBlindDrugMap[name] = isBlinded
			}
			if isBlindedDrug && isBlindedRole {
				med["name"] = tools.BlindData
			}
		}
	}

	return map[string]interface{}{"datas": datas}, nil
}

func (s *BarcodeService) GetBarcodeCodeGroupRule(ctx *gin.Context) (models.BarcodeGroup, error) {
	var barcodeGroupData models.BarcodeGroup
	customerId := ctx.Query("customerId")
	customerID, _ := primitive.ObjectIDFromHex(customerId)
	projectId := ctx.Query("projectId")
	projectID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envID, _ := primitive.ObjectIDFromHex(envId)
	cohortID, _ := primitive.ObjectIDFromHex(ctx.Query("cohortId"))
	if cohortID.IsZero() {
		cohortID = primitive.NilObjectID
	}
	filter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	if !cohortID.IsZero() { // 群组或者在随机项目
		filter = bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "cohort_id": cohortID}
	}
	var barcodeGroupList []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$sort", Value: bson.D{{Key: "creat_time", Value: -1}, {Key: "is_package_barcode", Value: -1}}}},
		{{Key: "$project", Value: bson.M{
			"_id":              0,
			"barcodeRule":      "$barcode_rule",
			"isPackageBarcode": "$is_package_barcode",
			"packageRule":      "$package_rule",
		}}},
	}
	cursor, err := tools.Database.Collection("barcode_group").Aggregate(nil, pipeline)
	if err != nil {
		return barcodeGroupData, errors.WithStack(err)
	}
	err = cursor.All(nil, &barcodeGroupList)
	if err != nil {
		return barcodeGroupData, errors.WithStack(err)
	}
	isOpenPackage, _, _, _, _, _ := tools.IsOpenPackage(envID)
	//默认值
	barcodeGroupData.BarcodeRule = 1
	barcodeGroupData.PackageRule = 0
	if len(barcodeGroupList) > 0 {
		barcodeGroup := barcodeGroupList[0]
		barcodeGroupRule := 1
		isPackageBarcode := true
		packageRule := 0
		if barcodeGroup["barcodeRule"] != nil {
			barcodeGroupRule = int(barcodeGroup["barcodeRule"].(int32))
			for _, barcodeGroup := range barcodeGroupList {
				if barcodeGroup["isPackageBarcode"] != nil && barcodeGroup["isPackageBarcode"].(bool) {
					packageRule = int(barcodeGroup["packageRule"].(int32))
				}
			}
		}
		barcodeGroupData.BarcodeRule = barcodeGroupRule
		barcodeGroupData.IsPackageBarcode = isPackageBarcode
		barcodeGroupData.PackageRule = packageRule
	}
	barcodeGroupData.IsPackageBarcode = isOpenPackage
	return barcodeGroupData, nil
}

func (s *BarcodeService) Add(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerId := ctx.Query("customerId")
		customerID, _ := primitive.ObjectIDFromHex(customerId)
		projectId := ctx.Query("projectId")
		projectID, _ := primitive.ObjectIDFromHex(projectId)
		envId := ctx.Query("envId")
		envID, _ := primitive.ObjectIDFromHex(envId)
		cohortID, _ := primitive.ObjectIDFromHex(ctx.Query("cohortId"))
		var data map[string]interface{}
		_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
		//drugName := data["drugName"].(string)
		//spec := data["spec"].(string)
		effectiveTime := data["effectiveTime"].(string)
		batchNumber := data["batchNumber"].(string)
		count := int(data["count"].(float64))
		prefix := data["prefix"].(string)
		isPackageBarcode := data["isPackageBarcode"].(bool)
		barcodeRule := int(data["barcodeRule"].(float64))
		packageRule := int(data["packageRule"].(float64))
		storehouseID, _ := primitive.ObjectIDFromHex(data["storehouseId"].(string))
		status := 21

		//判断配置是否一致
		var barcodeGroupList []map[string]interface{}
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "cohort_id": cohortID}}},
			{{Key: "$sort", Value: bson.D{{Key: "creat_time", Value: -1}, {Key: "is_package_barcode", Value: -1}}}},
			{{Key: "$project", Value: bson.M{
				"_id":              0,
				"barcodeRule":      "$barcode_rule",
				"isPackageBarcode": "$is_package_barcode",
				"packageRule":      "$package_rule",
			}}},
		}
		cursor, err := tools.Database.Collection("barcode_group").Aggregate(nil, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &barcodeGroupList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if len(barcodeGroupList) > 0 {
			barcodeGroup := barcodeGroupList[0]
			barcodeGroupRule := 1
			if barcodeGroup["barcodeRule"] != nil {
				barcodeGroupRule = int(barcodeGroup["barcodeRule"].(int32))
				if barcodeRule != barcodeGroupRule { //判断研究产品条形码生成顺序规则是否一致
					return nil, tools.BuildServerError(ctx, "barcode_rule")
				} else {
					//判断是否需要生成包装号
					if isPackageBarcode { //需要生成的时候，需要判断，之前生成包装号的时候，顺序规则是否一致
						for _, barcodeGroup := range barcodeGroupList {
							if barcodeGroup["isPackageBarcode"] != nil && barcodeGroup["isPackageBarcode"].(bool) {
								if int(barcodeGroup["packageRule"].(int32)) != packageRule {
									return nil, tools.BuildServerError(ctx, "barcode_package_rule")
								}
								break
							}
						}
					}
				}
			}

		}

		err = generationBarcode(ctx, sctx, customerID, projectID, envID, cohortID, batchNumber, count, prefix, storehouseID, effectiveTime, status, "", isPackageBarcode, barcodeRule, packageRule)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func getCorrelationID(ctx *gin.Context, envOID primitive.ObjectID) (string, error) {
	var CorrelationNumber string
	var data []models.BarcodeGroup
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"env_id": envOID}}},
		{{Key: "$sort", Value: bson.D{{"correlation_id", -1}}}},
		{{Key: "$limit", Value: 1}},
	}
	cursor, err := tools.Database.Collection("barcode_group").Aggregate(ctx, pipepine)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	if data != nil {
		maxCorrelationID := data[0].CorrelationID
		if maxCorrelationID != "" {
			maxNumber, _ := strconv.Atoi(maxCorrelationID[2:])
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%03s", number)
			CorrelationNumber = "BC" + formatNumber
		} else {
			CorrelationNumber = "BC001"
		}
	} else {
		CorrelationNumber = "BC001"
	}

	return CorrelationNumber, nil
}

func generationBarcode(ctx *gin.Context, sctx mongo.SessionContext, customerID primitive.ObjectID, projectID primitive.ObjectID, envID primitive.ObjectID, cohortID primitive.ObjectID, batchNumber string, count int, prefix string, storehouseID primitive.ObjectID, effectiveTime string, status int, copyEnv string,
	isPackageBarcode bool, barcodeRule int, packageRule int) error {
	eTime, _ := time.Parse("2006-01-02", effectiveTime)
	zone, err := tools.GetTimeZone(projectID)
	if err != nil {
		return errors.WithStack(err)
	}
	eTime = eTime.Add(time.Hour * time.Duration(zone))

	//判断上传的药物仓库是否对接物流仓库，如果对接，上传的药物状态为"待入库"
	var projectStorehouse models.ProjectStorehouse
	err = tools.Database.Collection("project_storehouse").FindOne(sctx, bson.M{"_id": storehouseID, "deleted": 2}).Decode(&projectStorehouse)
	if err != nil {
		return errors.WithStack(err)
	}
	// filter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	// if !cohortID.IsZero() {
	// 	filter = bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "cohort_id": cohortID}
	// }
	// var barcodeRule models.BarcodeRule
	// err = tools.Database.Collection("barcode_rule").FindOne(sctx, filter).Decode(&barcodeRule)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }

	// 查询研究产品配置的药物配置
	var drugPackageConfigure models.DrugPackageConfigure
	err = tools.Database.Collection("drug_package_configure").FindOne(sctx, bson.M{"env_id": envID}).Decode(&drugPackageConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	//查询项目环境编码
	var envBarcodeNumber models.EnvBarcodeNumber
	err = tools.Database.Collection("env_barcode_number").FindOne(sctx, bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}).Decode(&envBarcodeNumber)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	beforeBarcodeNumber := ""
	if envBarcodeNumber.ID != primitive.NilObjectID { //已存在
		beforeBarcodeNumber = envBarcodeNumber.Number
	} else {
		var newEnvBarcodeNumber models.EnvBarcodeNumber
		newEnvBarcodeNumber.ID = primitive.NewObjectID()
		newEnvBarcodeNumber.CustomerID = customerID
		newEnvBarcodeNumber.ProjectID = projectID
		newEnvBarcodeNumber.EnvironmentID = envID
		//查询已有项目环境
		var envBarcodeNumbers []models.EnvBarcodeNumber
		cursor, err := tools.Database.Collection("env_barcode_number").Find(ctx, bson.M{"customer_id": customerID, "project_id": projectID})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &envBarcodeNumbers)
		if err != nil {
			return errors.WithStack(err)
		}
		if len(envBarcodeNumbers) > 0 {
			projectBarcode := envBarcodeNumbers[0].ProjectNumber
			envBarcode := strconv.Itoa(len(envBarcodeNumbers) + 1)
			beforeBarcodeNumber = projectBarcode + envBarcode
			newEnvBarcodeNumber.ProjectNumber = projectBarcode
			newEnvBarcodeNumber.EnvNumber = envBarcode
			newEnvBarcodeNumber.Number = beforeBarcodeNumber
		} else {
			//验证生成的项目随机号是否存在
			var projectBarcodeNumbers []map[string]interface{}
			cur, err := tools.Database.Collection("env_barcode_number").Aggregate(ctx, mongo.Pipeline{
				{{Key: "$group", Value: bson.M{"_id": "$project_number"}}},
			})
			if err != nil {
				return errors.WithStack(err)
			}
			err = cur.All(nil, &projectBarcodeNumbers)
			if err != nil {
				return errors.WithStack(err)
			}
			haveProBarNumbers := make(map[string]string, len(projectBarcodeNumbers))
			for _, v := range projectBarcodeNumbers {
				projectNumber := v["_id"].(string)
				haveProBarNumbers[projectNumber] = projectNumber
			}

			proNumber := rand.Int63n(1e4 - 1)
			projectBarcode := fmt.Sprintf("%04d", proNumber)
			_, ok := haveProBarNumbers[projectBarcode]
			for ok {
				proNumber = rand.Int63n(1e4 - 1)
				projectBarcode = fmt.Sprintf("%04d", proNumber)
				_, ok = haveProBarNumbers[projectBarcode]
			}

			envBarcode := "1"
			beforeBarcodeNumber = projectBarcode + envBarcode
			newEnvBarcodeNumber.ProjectNumber = projectBarcode
			newEnvBarcodeNumber.EnvNumber = envBarcode
			newEnvBarcodeNumber.Number = beforeBarcodeNumber
		}
		_, err = tools.Database.Collection("env_barcode_number").InsertOne(sctx, newEnvBarcodeNumber)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	medicines := make([]interface{}, count)
	medicineIds := make([]primitive.ObjectID, count)
	// //使用雪花算法生成研究产品号 node节点为当前真实ip的最后一段
	// ip, err := tools.ExternalIP()
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	// n, err := strconv.ParseInt(strings.Split(ip.String(), ".")[3], 10, 64)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	// node, err := snowflake.NewNode(n)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }

	// 使用当前时间作为随机数种子
	rand.Seed(time.Now().UnixNano())
	//查询项目环境下的研究产品编号和短码
	numbers := make(map[string]bool)
	shortNumbers := make(map[string]bool)

	haveMedicines := []map[string]interface{}{}
	match := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$project", Value: bson.M{
			"_id":           0,
			"id":            "$_id",
			"name":          "$name",
			"number":        "$number",
			"packageNumber": "$package_number",
			"shortCode":     "$short_code",
		}}},
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipepine)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &haveMedicines)
	if err != nil {
		return errors.WithStack(err)
	}

	medicinesMap := make(map[string]bool)
	shortCodeMap := make(map[string]bool)
	packageMap := make(map[string]bool)
	maxNumber := 0
	maxPackageNumber := 0
	for _, medicine := range haveMedicines {
		if medicine["number"] != nil && medicine["number"] != "" {
			medicinesMap[medicine["number"].(string)] = true
			if barcodeRule == 0 && len(medicine["number"].(string)) == 12 {
				number, _ := strconv.Atoi(medicine["number"].(string)[len(beforeBarcodeNumber)+1:])
				if number > maxNumber {
					maxNumber = number
				}
			}

		}
		if medicine["shortCode"] != nil && medicine["shortCode"] != "" {
			shortCodeMap[medicine["shortCode"].(string)] = true
		}
		if drugPackageConfigure.IsOpen && len(drugPackageConfigure.MixedPackage) > 0 && medicine["packageNumber"] != nil && medicine["packageNumber"] != "" {
			if !packageMap[medicine["packageNumber"].(string)] {
				packageMap[medicine["packageNumber"].(string)] = true
				packageNumber, _ := strconv.Atoi(medicine["packageNumber"].(string))
				if barcodeRule == 0 && packageNumber > maxPackageNumber {
					maxPackageNumber = packageNumber
				}
			}
		}
	}

	if barcodeRule == 1 { //乱序生成研究产品条形码
		for len(numbers) < count {
			// 生成一个13位的随机数
			randomNumber := rand.Int63n(1e7 - 1)
			number := beforeBarcodeNumber + fmt.Sprintf("%07d", randomNumber)
			shortCode := ""

			if _, exists := numbers[number]; !exists {
				//验证是否存在1、同一个项目环境下，是否有重复的(包含手动上传的) 2、条形码码是否全局唯一
				exist := medicinesMap[number]
				//条形码插入成功 && 手动上传的药物中不存在一样的研究产品编号
				if !exist {
					numbers[number] = true
					//验证短码项目环境下是否唯一,先保证生成的这一批中没有重复的，然后验证项目环境中是否有重复的
					shortNumber := rand.Int63n(1e6 - 1)
					shortCode = fmt.Sprintf("%06d", shortNumber)
					_, shortExists := shortNumbers[shortCode]
					shortExist := shortCodeMap[shortCode]

					for shortExists || shortExist {
						shortNumber = rand.Int63n(1e6 - 1)
						_, shortExists = shortNumbers[shortCode]
						for shortExists {
							shortNumber = rand.Int63n(1e6 - 1)
							shortExists = shortNumbers[shortCode]
						}
						shortCode = fmt.Sprintf("%06d", shortNumber)
						shortExist = shortCodeMap[shortCode]
					}
					shortNumbers[shortCode] = true
					m := models.Medicine{
						ID:            primitive.NewObjectID(),
						CustomerID:    customerID,
						ProjectID:     projectID,
						EnvironmentID: envID,
						//Name:           drugName,
						Number:         number, // 药物编号
						SerialNumber:   number, // 序列号
						StorehouseID:   storehouseID,
						ExpirationDate: effectiveTime,
						BatchNumber:    batchNumber,
						//Spec:           spec,
						Status:     status,
						ScanStatus: 0,
						ShortCode:  prefix + shortCode,
					}

					medicines[len(numbers)-1] = m
					medicineIds[len(numbers)-1] = m.ID
				}
			}
		}
	} else {
		//顺序生成的时候，取最大值，
		for len(numbers) < count {
			maxNumber++
			number := beforeBarcodeNumber + fmt.Sprintf("%07d", maxNumber)
			shortCode := ""

			if _, exists := numbers[number]; !exists {
				//验证是否存在1、同一个项目环境下，是否有重复的(包含手动上传的) 2、条形码码是否全局唯一
				exist := medicinesMap[number]
				//条形码插入成功 && 手动上传的药物中不存在一样的研究产品编号
				if !exist {
					numbers[number] = true
					//验证短码项目环境下是否唯一,先保证生成的这一批中没有重复的，然后验证项目环境中是否有重复的
					//shortNumber := rand.Int63n(1e6 - 1)
					shortCode = fmt.Sprintf("%06d", maxNumber)
					_, shortExists := shortNumbers[shortCode] //此次生成的shortCode
					shortExist := shortCodeMap[shortCode]     //系统里面已存在的shortCode

					for shortExists || shortExist {
						//shortNumber = rand.Int63n(1e6 - 1)
						maxNumber++
						_, shortExists = shortNumbers[shortCode]
						for shortExists {
							//shortNumber = rand.Int63n(1e6 - 1)
							maxNumber++
							shortExists = shortNumbers[shortCode]
						}
						shortCode = fmt.Sprintf("%06d", maxNumber)
						shortExist = shortCodeMap[shortCode]
					}
					shortNumbers[shortCode] = true
					m := models.Medicine{
						ID:            primitive.NewObjectID(),
						CustomerID:    customerID,
						ProjectID:     projectID,
						EnvironmentID: envID,
						//Name:           drugName,
						Number:         number, // 药物编号
						SerialNumber:   number, // 序列号
						StorehouseID:   storehouseID,
						ExpirationDate: effectiveTime,
						BatchNumber:    batchNumber,
						//Spec:           spec,
						Status:     status,
						ScanStatus: 0,
						ShortCode:  prefix + shortCode,
					}

					medicines[len(numbers)-1] = m
					medicineIds[len(numbers)-1] = m.ID
				}
			}
		}
	}

	//生成包装号，判断包装运输开关是否打开
	packages := make([]string, 0)
	if isPackageBarcode {
		if drugPackageConfigure.IsOpen && len(drugPackageConfigure.MixedPackage) > 0 {
			//查询生成条形码里面自动生成的包装号
			var data []map[string]interface{}
			match := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "package_numbers.0": bson.M{"$exists": true}}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: match}},
				{{Key: "$project", Value: bson.M{
					"_id":            0,
					"packageNumbers": "$package_numbers",
				}}},
			}
			cursor, err := tools.Database.Collection("barcode_group").Aggregate(sctx, pipeline)
			if err != nil {
				return errors.WithStack(err)
			}
			err = cursor.All(nil, &data)
			if err != nil {
				return errors.WithStack(err)
			}
			for _, barcodeGroup := range data {
				packageNumbers := barcodeGroup["packageNumbers"].(primitive.A)
				for _, packageNumber := range packageNumbers {
					if !packageMap[packageNumber.(string)] {
						packageMap[packageNumber.(string)] = true
					}
				}
			}
			//查询每盒最小包装数
			mixCount := 0
			for _, mixedPackage := range drugPackageConfigure.MixedPackage {
				count := 0
				for _, packageConfig := range mixedPackage.PackageConfig {
					count = count + packageConfig.Number
				}
				if mixCount == 0 {
					mixCount = count
				} else {
					if count < mixCount {
						mixCount = count
					}
				}
			}

			//生成包装号的个数
			packageCount := count / mixCount
			if count%mixCount > 0 {
				packageCount++
			}

			packageNumbers := make(map[string]bool)
			packages = make([]string, packageCount)
			if packageRule == 1 { //包装码乱序生成
				for len(packageNumbers) < packageCount {
					packageNumber := rand.Int63n(1e6 - 1)
					packageNum := fmt.Sprintf("%06d", packageNumber)
					if _, exists := packageNumbers[packageNum]; !exists {
						//判断项目下是否存在
						exist := packageMap[packageNum]
						if !exist {
							packageNumbers[packageNum] = true
							packages[len(packageNumbers)-1] = packageNum
						}
					}
				}
			} else {
				//查询packageNumbe最大的值
				for len(packageNumbers) < packageCount {
					maxPackageNumber++
					packageNum := fmt.Sprintf("%06d", maxPackageNumber)
					if _, exists := packageNumbers[packageNum]; !exists {
						//判断项目下是否存在
						exist := packageMap[packageNum]
						if !exist {
							packageNumbers[packageNum] = true
							packages[len(packageNumbers)-1] = packageNum
						}
					}
				}
			}
		}
	}
	correlationID, err := getCorrelationID(ctx, envID)
	if err != nil {
		return errors.WithStack(err)
	}

	barcodeGroup := models.BarcodeGroup{
		ID:            primitive.NewObjectID(),
		CustomerID:    customerID,
		ProjectID:     projectID,
		EnvironmentID: envID,
		CohortID:      cohortID,
		//DrugName:      drugName,
		//Spec:          spec,
		EffectiveTime:    time.Duration(eTime.Unix()),
		BatchNumber:      batchNumber,
		StorehouseID:     storehouseID,
		Prefix:           prefix,
		BarcodeRule:      barcodeRule,
		IsPackageBarcode: isPackageBarcode,
		PackageRule:      packageRule,
		CreatTime:        time.Duration(time.Now().Unix()),
		MedicineIDs:      medicineIds,
		PackageNumbers:   packages,
		CorrelationID:    correlationID,
	}

	// 自动添加扫码入仓任务
	// 查询是否有未完成的扫码入仓任务，有的话不添加任务，没有再添加
	existFilter := bson.M{
		"customer_id":    customerID,
		"project_id":     projectID,
		"env_id":         envID,
		"info.work_type": 1, // 扫码入仓任务
		"info.status":    0, // 状态未完成
	}
	if !cohortID.IsZero() {
		existFilter["cohort_id"] = cohortID
	}
	var existWorkTask models.WorkTask
	err = tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&existWorkTask)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	existUserIds := make(map[primitive.ObjectID]primitive.ObjectID)
	for _, v := range existWorkTask.UserIDs {
		existUserIds[v] = v
	}
	// 查询权限扫码入仓权限的用户
	permissions := []string{"operation.build.medicine.barcode.scan"}
	siteOrStoreIDs := []primitive.ObjectID{storehouseID}
	userIds, err := tools.GetPermissionUserIds(sctx, permissions, projectID, envID, siteOrStoreIDs...)
	if err != nil {
		return errors.WithStack(err)
	}
	if existWorkTask.ID.IsZero() {
		//判断已存在的任务里面的用户是否都包含userIds
		if userIds != nil {
			workTask := models.WorkTask{
				ID:            primitive.NewObjectID(),
				CustomerID:    customerID,
				ProjectID:     projectID,
				EnvironmentID: envID,
				CohortID:      cohortID,
				UserIDs:       userIds,
				Info: models.WorkTaskInfo{
					WorkType:    1,
					Status:      0,
					CreatedTime: time.Duration(time.Now().Unix()),
					Deadline:    time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
					MedicineIDs: []primitive.ObjectID{},
				},
			}
			_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	} else {
		addUserIds := existWorkTask.UserIDs
		//判断已存在任务的user是否都在存在
		for _, userId := range userIds {
			// 检查元素是否在map
			if _, ok := existUserIds[userId]; ok {
			} else {
				addUserIds = append(addUserIds, userId)
			}
		}
		if len(addUserIds) > 0 {
			workTaskUpdate := bson.M{
				"$set": bson.M{
					"user_ids": addUserIds,
				},
			}
			_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": existWorkTask.ID}, workTaskUpdate)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}
	//包装扫码任务
	if drugPackageConfigure.IsOpen && len(drugPackageConfigure.MixedPackage) > 0 {
		existFilter := bson.M{
			"customer_id":    customerID,
			"project_id":     projectID,
			"env_id":         envID,
			"info.work_type": 12, // 包转扫码任务
			"info.status":    0,  // 状态未完成
		}
		if !cohortID.IsZero() {
			existFilter["cohort_id"] = cohortID
		}
		var packageWorkTask models.WorkTask
		err = tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&packageWorkTask)
		if err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)
		}
		existUserIds := make(map[primitive.ObjectID]primitive.ObjectID)
		for _, v := range packageWorkTask.UserIDs {
			existUserIds[v] = v
		}
		// 查询权限包装扫码权限的用户
		permissions := []string{"operation.build.medicine.barcode.scanPackage"}
		siteOrStoreIDs := []primitive.ObjectID{storehouseID}
		userIds, err := tools.GetPermissionUserIds(sctx, permissions, projectID, envID, siteOrStoreIDs...)
		if err != nil {
			return errors.WithStack(err)
		}
		if packageWorkTask.ID.IsZero() {
			if userIds != nil {
				workTask := models.WorkTask{
					ID:            primitive.NewObjectID(),
					CustomerID:    customerID,
					ProjectID:     projectID,
					EnvironmentID: envID,
					CohortID:      cohortID,
					UserIDs:       userIds,
					Info: models.WorkTaskInfo{
						WorkType:    12,
						Status:      0,
						CreatedTime: time.Duration(time.Now().Unix()),
						Deadline:    time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
						MedicineIDs: []primitive.ObjectID{},
					},
				}
				_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				if err != nil {
					return errors.WithStack(err)
				}
			}
		} else {
			addUserIds := packageWorkTask.UserIDs
			//判断已存在任务的userk是否都在存在
			for _, userId := range userIds {
				// 检查元素是否在map
				if _, ok := existUserIds[userId]; ok {
				} else {
					addUserIds = append(addUserIds, userId)
				}
			}
			if len(addUserIds) > 0 {
				workTaskUpdate := bson.M{
					"$set": bson.M{
						"user_ids": addUserIds,
					},
				}
				_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": packageWorkTask.ID}, workTaskUpdate)
				if err != nil {
					return errors.WithStack(err)
				}
			}
		}
	}

	if _, err = tools.Database.Collection("barcode_group").InsertOne(sctx, barcodeGroup); err != nil {
		return errors.WithStack(err)
	}
	if _, err = tools.Database.Collection("medicine").InsertMany(sctx, medicines); err != nil {
		return errors.WithStack(err)
	}

	if copyEnv != "" {
		InsertBarcodeLog(ctx, sctx, 10, barcodeGroup, effectiveTime, copyEnv)
	} else {
		InsertBarcodeLog(ctx, sctx, 1, barcodeGroup, effectiveTime, "")
	}
	return nil
}

func (s *BarcodeService) SaveConfig(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var err error
		id := ctx.Query("barcodeRuleId")
		barcodeRuleId, _ := primitive.ObjectIDFromHex(id)
		var json map[string]interface{}
		_ = ctx.ShouldBindBodyWith(&json, binding.JSON)
		codeRule := int(json["codeRule"].(float64))
		update := bson.M{
			"$set": bson.M{
				"code_rule":        codeRule,
				"code_config_init": true,
			},
		}
		var barcodeRule models.BarcodeRule
		tools.Database.Collection("barcode_rule").FindOne(sctx, bson.M{"_id": barcodeRuleId}).Decode(&barcodeRule)

		//2.9.0版本，自动编码，可以生成包装号，所以这个判断限制去掉
		//判断编码方式，如果是自动编码，判断包装配置是否打开,如果包装配置是打开状态，给出提示，先关闭包装配置
		// if codeRule == 1 {
		// 	isOpenPackage, _, _, _ := tools.IsOpenPackage(barcodeRule.EnvironmentID, "")
		// 	if isOpenPackage {
		// 		return nil, tools.BuildServerError(ctx, "barcode_error")
		// 	}
		// }

		if _, err = tools.Database.Collection("barcode_rule").UpdateOne(sctx, bson.M{"_id": barcodeRuleId}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		//// 保存项目日志
		//types := 2
		//oldField := models.OperationLogField{
		//	Type:  6,
		//	Value: barcodeRule.CodeRule,
		//}
		//if barcodeRule.CodeRule == codeRule {
		//	types = 5
		//	oldField.Type = 1
		//	oldField.Value = ""
		//}
		//var OperationLogFieldGroups []models.OperationLogFieldGroup
		//tranKey := "operation_log.barcode.random"
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "barcode.edit",
		//	TranKey: tranKey,
		//	Old:     oldField,
		//	New: models.OperationLogField{
		//		Type:  6,
		//		Value: codeRule,
		//	},
		//})
		//OID := barcodeRule.EnvironmentID
		//if !barcodeRule.CohortID.IsZero() {
		//	OID = barcodeRule.CohortID
		//}
		//err = tools.SaveOperation(ctx, sctx, "operation_log.module.barcode", OID, types, OperationLogFieldGroups, []models.Mark{}, barcodeRuleId)
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *BarcodeService) GetConfig(ctx *gin.Context) (models.BarcodeRule, error) {
	customerId := ctx.Query("customerId")
	customerID, _ := primitive.ObjectIDFromHex(customerId)
	projectId := ctx.Query("projectId")
	projectID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envID, _ := primitive.ObjectIDFromHex(envId)
	cohortID, _ := primitive.ObjectIDFromHex(ctx.Query("cohortId"))
	if cohortID.IsZero() {
		cohortID = primitive.NilObjectID
	}
	filter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "cohort_id": cohortID}
	var barcodeRule models.BarcodeRule
	if err := tools.Database.Collection("barcode_rule").FindOne(nil, filter).Decode(&barcodeRule); err != nil {
		return models.BarcodeRule{
			CodeRule:       0,
			CodeConfigInit: true,
		}, nil
	}

	return barcodeRule, nil
}

func (s *BarcodeService) GetCodeRule(ctx *gin.Context) (map[string]interface{}, error) {
	customerId := ctx.Query("customerId")
	customerID, _ := primitive.ObjectIDFromHex(customerId)
	projectId := ctx.Query("projectId")
	projectID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envID, _ := primitive.ObjectIDFromHex(envId)
	filter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	var barcodeRules []models.BarcodeRule
	cursor, err := tools.Database.Collection("barcode_rule").Find(nil, filter)
	if err != nil {
		return map[string]interface{}{}, errors.WithStack(err)
	}
	err = cursor.All(nil, &barcodeRules)
	if err != nil {
		return map[string]interface{}{}, errors.WithStack(err)
	}

	codeRule0 := false
	codeRule1 := false
	cohortIds := make([]primitive.ObjectID, 0)
	for _, barcode := range barcodeRules {
		if barcode.CodeRule == 1 { //自动编码
			codeRule1 = true
			if barcode.CohortID != primitive.NilObjectID {
				cohortIds = append(cohortIds, barcode.CohortID)
			}
		}
		if barcode.CodeRule == 0 { //手动上传
			codeRule0 = true
		}
	}
	//如果没有编码配置的数据，默认手动上传的方式
	if len(barcodeRules) <= 0 {
		codeRule0 = true
	}
	return map[string]interface{}{"codeRule0": codeRule0, "codeRule1": codeRule1, "cohortIds": cohortIds}, nil
}

func (s *BarcodeService) Download(ctx *gin.Context) error {
	id := ctx.Query("barcodeId")
	barcodeGroupID, _ := primitive.ObjectIDFromHex(id)
	filter := bson.M{"_id": barcodeGroupID}
	lookup := bson.M{"from": "medicine", "localField": "medicine_ids", "foreignField": "_id", "as": "medicines"}
	var data []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: lookup}},
		{{Key: "$project", Value: bson.M{
			"_id":            0,
			"medicineNumber": "$medicines.number",
			"shortCode":      "$medicines.short_code",
			"project":        "$project_id",
			"env":            "$env_id",
			"packageNumbers": "$package_numbers",
		}}},
	}
	cursor, err := tools.Database.Collection("barcode_group").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return errors.WithStack(err)
	}

	numbers := data[0]["medicineNumber"].(primitive.A)
	shortCodes := data[0]["shortCode"].(primitive.A)

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	textLeftStyleID, _ := f.NewStyle(`{
		"alignment":{
			"horizontal": "center",
			"vertical": "center"
		}
	}`)

	index := f.NewSheet("Sheet1")
	f.SetActiveSheet(index)
	f.SetCellValue("Sheet1", "A1", locales.Tr(ctx, "medicine_number"))
	f.SetCellValue("Sheet1", "B1", locales.Tr(ctx, "medicine_barcode_code"))
	f.SetCellValue("Sheet1", "C1", locales.Tr(ctx, "medicine_code"))
	f.SetCellValue("Sheet1", "D1", locales.Tr(ctx, "medicine_barcode_code_short"))

	f.SetCellStyle("Sheet1", "A1", "D1", textLeftStyleID)
	f.SetColWidth("Sheet1", "A", "D", 36)

	axisIndex := 2
	for i := 0; i < len(numbers); i++ {
		f.SetRowHeight("Sheet1", axisIndex, 90)
		// 条形码数字
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(axisIndex), numbers[i])
		// 条形码
		GenerateBarcode(numbers[i].(string), 220)
		if err = f.AddPicture("Sheet1", "B"+strconv.Itoa(axisIndex), "barcode.png", `{
	   	"x_scale": 0.717,
	   	"y_scale": 1.01,
		"x_offset": 10,
		"y_offset": 10
		}`); err != nil {
			return errors.WithStack(err)
		}
		// 短码
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(axisIndex), shortCodes[i])

		// 条形码&短码组合
		GenerateBarcodeAndShortCode(numbers[i].(string), shortCodes[i].(string), 220)
		if err = f.AddPicture("Sheet1", "D"+strconv.Itoa(axisIndex), "barcode&short.png", `{
			"x_scale": 0.717,
			"y_scale": 1.01,
			"x_offset": 10,
			"y_offset": 10
			}`); err != nil {
			return errors.WithStack(err)
		}
		f.SetCellStyle("Sheet1", fmt.Sprintf("A%d", axisIndex), fmt.Sprintf("D%d", axisIndex), textLeftStyleID)

		axisIndex = axisIndex + 1
	}

	if data[0]["packageNumbers"] != nil {
		packageNumbers := data[0]["packageNumbers"].(primitive.A)
		if len(packageNumbers) > 0 {
			index = f.NewSheet("Sheet2")
			f.SetActiveSheet(index)
			f.SetCellValue("Sheet2", "A1", locales.Tr(ctx, "medicine_package"))
			f.SetCellValue("Sheet2", "B1", locales.Tr(ctx, "medicine_package_barcode"))
			f.SetCellValue("Sheet2", "C1", locales.Tr(ctx, "medicine_package_barcode_short"))

			f.SetCellStyle("Sheet2", "A1", "C1", textLeftStyleID)
			f.SetColWidth("Sheet2", "A", "C", 36)
			axisIndex = 2
			for i := 0; i < len(packageNumbers); i++ {
				f.SetRowHeight("Sheet2", axisIndex, 90)
				// 条形码数字
				f.SetCellValue("Sheet2", "A"+strconv.Itoa(axisIndex), packageNumbers[i])
				// 条形码
				GenerateBarcode(packageNumbers[i].(string), 220)
				if err = f.AddPicture("Sheet2", "B"+strconv.Itoa(axisIndex), "barcode.png", `{
				"x_scale": 0.717,
				"y_scale": 1.01,
				"x_offset": 10,
				"y_offset": 10
				}`); err != nil {
					return errors.WithStack(err)
				}
				// 条形码&短码组合
				GenerateBarcodeAndShortCode(packageNumbers[i].(string), packageNumbers[i].(string), 220)
				if err = f.AddPicture("Sheet2", "C"+strconv.Itoa(axisIndex), "barcode&short.png", `{
				"x_scale": 0.717,
				"y_scale": 1.01,
				"x_offset": 10,
				"y_offset": 10
				}`); err != nil {
					return errors.WithStack(err)
				}
				f.SetCellStyle("Sheet2", fmt.Sprintf("A%d", axisIndex), fmt.Sprintf("C%d", axisIndex), textLeftStyleID)

				axisIndex = axisIndex + 1
			}
		}
	}

	projectID, _ := data[0]["project"].(primitive.ObjectID)
	envID, _ := data[0]["env"].(primitive.ObjectID)
	// 查询项目和环境
	var projectInfo []map[string]interface{}
	pipeline = mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":    0,
					"number": "$info.number",
					"name":   "$info.name",
					"env":    "$envs.name",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}
	project := projectInfo[0]
	if err != nil {
		return errors.WithStack(err)
	}

	timeZone, err := tools.GetTimeZone(projectID)
	if err != nil {
		return errors.WithStack(err)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	now := time.Now().UTC().Add(duration).Format("20060102150405")
	fileName := fmt.Sprintf("%s[%s]IPCodeList_%s.xlsx", project["number"], project["env"], now)

	buffer, err := f.WriteToBuffer()
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", buffer.Bytes())

	if err != nil {
		return err
	}
	return nil
}

func (s *BarcodeService) SubmitFeedback(ctx *gin.Context) error {
	me, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	content := data["content"].(string)
	if content != "" {
		feedback := models.Feedback{
			ID:          primitive.NewObjectID(),
			Content:     content,
			UserId:      me.ID,
			CreatedTime: time.Duration(time.Now().Unix()),
		}
		_, err := tools.Database.Collection("feedback").InsertOne(ctx, feedback)
		if err != nil {
			return errors.WithStack(err)
		}
	} else {
		return tools.BuildServerError(ctx, "common.required")
	}
	return nil
}

func (s *BarcodeService) GetBarcodeCodeRule(ctx *gin.Context) (int, error) {
	customerId := ctx.Query("customerId")
	customerID, _ := primitive.ObjectIDFromHex(customerId)
	projectId := ctx.Query("projectId")
	projectID, _ := primitive.ObjectIDFromHex(projectId)
	envId := ctx.Query("envId")
	envID, _ := primitive.ObjectIDFromHex(envId)
	projectType := ctx.Query("projectType")

	filter := bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID}
	if projectType != "1" { // 群组或者在随机项目
		filter = bson.M{"customer_id": customerID, "project_id": projectID, "env_id": envID, "cohort_id": bson.M{"$ne": primitive.NilObjectID}}
	}

	var barcodeRules []models.BarcodeRule
	cursor, err := tools.Database.Collection("barcode_rule").Find(nil, filter)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	err = cursor.All(nil, &barcodeRules)
	if err != nil {
		return 0, errors.WithStack(err)
	}

	if barcodeRules != nil && len(barcodeRules) > 0 {
		if projectType == "1" {
			return barcodeRules[0].CodeRule, nil
		} else {
			handMovement := 0 // 统计手动编码个数
			automatic := 0    // 统计自动编码个数

			for _, barcode := range barcodeRules {
				if barcode.CodeRule == 0 { // 手动
					handMovement++
				} else { // 自动
					automatic++
				}
			}
			if handMovement == len(barcodeRules) { // 全部手动
				return 0, nil
			} else if automatic == len(barcodeRules) { // 全部自动
				return 1, nil
			} else { // 手动自动都有
				return 2, nil
			}
		}
	} else { // 未获取编码配置
		return 0, nil
	}
}

func (s *BarcodeService) GetContent(ctx *gin.Context) (map[string]interface{}, error) {
	data := make(map[string]interface{}, 4)
	data["userAgreementZhCn"] = apphtml.UserAgreementZhCn
	data["privacyPolicyZhCn"] = apphtml.PrivacyPolicyZhCn
	data["userAgreementEnUs"] = apphtml.UserAgreementEnUs
	data["privacyPolicyEnUs"] = apphtml.PrivacyPolicyEnUs
	return data, nil
}

func (s *BarcodeService) GetVersion(ctx *gin.Context) (models.AppVersion, error) {
	appVersion := models.AppVersion{}
	err := tools.Database.Collection("app_version").FindOne(nil, bson.M{}).Decode(&appVersion)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.AppVersion{}, err
	}
	return appVersion, nil
}

// 下载app的apk包
func (s *BarcodeService) AppDownload(ctx *gin.Context) error {
	appVersion := models.AppVersion{}
	opts := &options.FindOneOptions{
		Sort: bson.D{{"_id", -1}},
	}
	err := tools.Database.Collection("app_version").FindOne(nil, bson.M{}, opts).Decode(&appVersion)
	if err != nil {
		return errors.WithStack(err)
	}
	gridFS := tools.NewGridFS()
	content, err := gridFS.Download(appVersion.FileID)
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(appVersion.FileName+".apk")))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)
	return nil
}

// 生成条形码
func GenerateBarcode(medicineNumber string, width int) {
	// 创建一个code128编码的 BarcodeIntCS
	cs, _ := code128.Encode(medicineNumber)
	// 创建一个要输出数据的文件
	file, _ := os.Create("barcode.png")
	defer file.Close()

	// 设置图片像素大小
	qrCode, _ := barcode.Scale(cs, width, 70)
	// 将code128的条形码编码为png图片
	png.Encode(file, qrCode)
}

// 生成条形码（条形码+数字显示）
func GenerateBarcodeAndShortCode(medicineNumber string, shortCode string, width int) {
	// 创建一个code128编码的 BarcodeIntCS
	cs, _ := code128.Encode(medicineNumber)
	// 设置图片像素大小
	qrCode, _ := barcode.Scale(cs, width, 70)
	white := image.NewRGBA(image.Rect(0, 0, width, 0))
	draw.Draw(white, white.Bounds(), image.White, image.Pt(0, 0), draw.Src)
	img := image.NewRGBA(image.Rect(0, 0, width, 20))
	draw.Draw(img, img.Bounds(), image.White, image.Pt(0, 0), draw.Src)
	x, y := 90, 20
	addLabel(img, x, y, shortCode)
	bg := image.NewRGBA(image.Rect(0, 0, width, 90))
	draw.Draw(bg, bg.Bounds().Add(image.Pt(0, 0)), white, white.Bounds().Min, draw.Src)
	draw.Draw(bg, bg.Bounds().Add(image.Pt(0, 10)), qrCode, qrCode.Bounds().Min, draw.Src)
	draw.Draw(bg, bg.Bounds().Add(image.Pt(0, 70)), img, img.Bounds().Min, draw.Src)
	bar, _ := os.Create("barcode&short.png")
	png.Encode(bar, bg)
}

func addLabel(img *image.RGBA, x, y int, label string) {
	point := fixed.Point26_6{fixed.Int26_6(x * 64), fixed.Int26_6(y * 64)}

	d := &font.Drawer{
		Dst:  img,
		Src:  image.Black,
		Face: basicfont.Face7x13,
		Dot:  point,
	}
	d.DrawString(label)
}
