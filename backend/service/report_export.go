package service

import (
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/algorithm"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 用户角色分配记录

func ExportUserRoleAssignHistoryReport(ctx *gin.Context, projectID string, envID string, roleId string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}
	envs := make([]models.UserProjectEnvironment, 0)
	find, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = find.All(nil, &envs)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	userIds := slice.Map(envs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.UserID
	})
	userIds = slice.Unique(userIds)

	var parameter map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameter, binding.JSON)

	type OperationLog struct {
		ID         primitive.ObjectID              `json:"id" bson:"_id"`
		Operator   primitive.ObjectID              `json:"operator" bson:"operator"`
		OperatorID primitive.ObjectID              `bson:"operatorId" json:"operator_id"` //具体操作数据的ID
		OID        primitive.ObjectID              `bson:"oid" json:"oid"`
		Time       time.Duration                   `json:"time" bson:"time"`
		Type       int                             `json:"type" bson:"type"` //1新增 2编辑 3删除  5编辑	6复制 Module   string                   `json:"module" bson:"module"`
		Module     string                          `json:"module" bson:"module"`
		Fields     []models.OperationLogFieldGroup `json:"fields" bson:"fields"`
		Mark       []models.Mark                   `json:"mark" bson:"mark"`
		User       models.User                     `json:"user" bson:"user"`
	}
	var operationLogs []OperationLog
	pipeline := mongo.Pipeline{
		{{"$match", bson.M{"oid": bson.M{"$in": primitive.A{envOID, project.CustomerID}}, "module": bson.M{"$in": []string{"operation_log.module.projectUser_role", "operation_log.module.user"}}}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "operator",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{"$unwind", "$user"}},
		{{"$sort", bson.D{{"time", -1}}}},
	}

	cursor, err := tools.Database.Collection("operation_log").Aggregate(nil, pipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &operationLogs)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	RetOperationLogs := []models.ReturnOperationLog{}
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	cloudIds := slice.Map(operationLogs, func(index int, item OperationLog) string {
		return item.User.CloudId.Hex()
	})
	cloudUsers, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	m := make(map[string]*models.UserData)
	//取下载用户的时区
	user, err := tools.Me(ctx)
	if err != nil {
		return "", nil, err
	}
	timeZone := float64(8)
	for _, u := range cloudUsers {
		m[u.Id] = u
		if user.CloudId.Hex() == u.Id {
			if len(u.Settings.Tz) > 0 {
				float, _ := tools.GetLocationFloat(u.Settings.Tz)
				timeZone = float
			} else if len(u.Settings.Timezone) > 0 {
				float, _ := strconv.ParseFloat(u.Settings.Timezone, 64) // 第二个参数指定了希望得到的浮点数精度，64 表示需要 float64
				timeZone = float
			}
		}
	}

	utc := tools.FormatOffsetToZoneStringUtc(timeZone)
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	for _, log := range operationLogs {
		if log.Type == 12 || log.Type == 13 {
			continue
		}
		if log.Module == "operation_log.module.user" && !slice.Contain(userIds, log.OperatorID) {
			continue
		}
		var context []string
		labelName := ""
		oldValue := ""
		newValue := ""

		for _, field := range log.Fields {
			var tmp string
			labelName = locales.Tr(ctx, field.TranKey)
			if field.Old.Type != 3 {
				oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, isBlindDrugMap, log.OID, field.TranKey)
			} else {
				oldValue = converCountry(ctx, field.Old.Value)
			}
			if field.New.Type != 3 {
				newValue = models.TypeHandle(ctx, field.New.Type, field.New.Value, isBlindDrugMap, log.OID, field.TranKey)
			} else {
				newValue = converCountry(ctx, field.New.Value)
			}
			if log.Type != 1 && oldValue == newValue && field.Old.Type != 7 {
				// 值未改变的 不需要展示
				continue
			}
			if log.Type == 1 {
				tmp = labelName + ":" + newValue
			} else if log.Type == 2 {
				tmp = labelName + ":" + oldValue + " -> " + newValue
			} else if log.Type == 3 {
				tmp = labelName + ":" + oldValue
			} else if log.Type == 4 {
				tmp = labelName
			} else if log.Type == 5 {
				tmp = labelName + ":" + newValue
			} else if log.Type == 6 {
				tmp = labelName + ":" + newValue
			} else if log.Type == 14 {
				tmp = labelName + ":" + newValue
			} else if log.Type == 15 {
				tmp = labelName + ":" + newValue
			}
			context = append(context, tmp)
		}

		var markContext string
		for _, mark := range log.Mark {
			if mark.Label != "" {
				value := mark.Value
				markContext = value
			}
		}

		timeStr := ""
		if log.Time != 0 {
			userData := m[log.User.CloudId.Hex()]
			if userData == nil {
				continue
			}

			createDate := time.Unix(log.Time.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			timeStr = createDate + "(" + utc + ")"

		}

		RetOperationLogs = append(RetOperationLogs, models.ReturnOperationLog{
			ID:              log.ID,
			UserEmail:       log.User.Email,
			UserName:        log.User.Name,
			Unicode:         log.User.Unicode,
			OperationType:   locales.Tr(ctx, models.OperationLogMap["operation_type"].(bson.M)[convertor.ToString(log.Type)].(string)),
			Time:            timeStr,
			Context:         context,
			OperatorContext: markContext,
		})

	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]UserRoleAssignHistory_%s.xlsx", project.Number, envCode, now.Format("20060102150405"))

	title := []any{locales.Tr(ctx, "report.attributes.project.number")}
	title = append(title, locales.Tr(ctx, "report.attributes.project.name"))
	title = append(title, locales.Tr(ctx, "user.name"))
	title = append(title, locales.Tr(ctx, "user.email"))
	title = append(title, locales.Tr(ctx, "common.operator"))
	title = append(title, locales.Tr(ctx, "operator.content"))
	title = append(title, locales.Tr(ctx, "operator.people"))
	title = append(title, locales.Tr(ctx, "operator.time"))

	content := make([][]interface{}, len(RetOperationLogs))
	//timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	for i := 0; i < len(RetOperationLogs); i++ {
		item := RetOperationLogs[i]
		OperatorContexts := strings.Split(item.OperatorContext, ",")
		userName := ""
		userEmail := ""
		if len(OperatorContexts) > 0 {
			userName = OperatorContexts[0]
		}
		if len(OperatorContexts) > 1 {
			userEmail = OperatorContexts[1]
		}
		row := []any{project.ProjectInfo.Number}
		row = append(row, project.ProjectInfo.Name)
		row = append(row, userName)
		row = append(row, userEmail)
		row = append(row, item.OperationType)
		row = append(row, item.Context)
		row = append(row, fmt.Sprintf("%s(%d)", item.UserName, item.Unicode))
		//hours := time.Duration(timeZone)
		//minutes := time.Duration((timeZone - float64(hours)) * 60)
		//duration := hours*time.Hour + minutes*time.Minute
		operateTime := item.Time
		row = append(row, operateTime)
		content[i] = row
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	t := make([]interface{}, len(title))
	for i, item := range title {
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.SetRow("A1", t)
	for i := 1; i <= len(content); i++ {
		r := make([]interface{}, len(content[i-1]))
		for j := 0; j < len(content[i-1]); j++ {
			r[j] = excelize.Cell{Value: content[i-1][j]}
		}
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		_ = streamWriter.SetRow(cell, r)
	}
	_ = streamWriter.Flush()
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil
}

func ExportUserRoleStatusReport(ctx *gin.Context, projectID string, envID string, now time.Time) (string, []byte, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var project models.Project
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project); err != nil {
		return "", nil, errors.WithStack(err)
	}

	match := bson.M{"project_id": projectOID, "env_id": envOID}
	var d []map[string]interface{}
	lookupPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$and": bson.A{
				bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}},
				bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", envOID}}},
			},
		}}},
	}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$and": bson.A{
				bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$siteId"}}},
				bson.M{"$expr": bson.M{"$eq": bson.A{"$deleted", 2}}},
			},
		}}},
		{{"$project", bson.M{
			"_id":    1,
			"number": 1,
			"name":   models.ProjectSiteNameBson(ctx),
		}}},
	}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "user_id", "foreignField": "_id", "as": "user"}}},
		{{Key: "$unwind", Value: "$user"}},
		{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
		{{Key: "$lookup", Value: bson.M{"from": "user_site", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": lookupPipeline, "as": "userSites"}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "let": bson.M{"siteId": "$userSites.site_id"}, "pipeline": sitePipeline, "as": "sites"}}},
		{{Key: "$lookup", Value: bson.M{"from": "user_depot", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": lookupPipeline, "as": "userDepots"}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "userDepots.depot_id", "foreignField": "_id", "as": "projectDepots"}}},
		{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "projectDepots.storehouse_id", "foreignField": "_id", "as": "depots"}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":         0,
				"id":          "$user._id",
				"email":       "$user.info.email",
				"cloudId":     "$user.cloud_id",
				"name":        "$user.info.name",
				"status":      "$user.info.status",
				"roles._id":   1,
				"roles.name":  1,
				"roles.scope": 1,
				"sites":       1,
				"depots":      1,
				"createAt":    "$meta.created_at",
				"unbind":      bson.M{"$ifNull": bson.A{"$unbind", false}},
			},
		}},
	}
	cursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, pipepine)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	index := f.NewSheet("Sheet1")
	f.SetActiveSheet(index)
	f.SetCellValue("Sheet1", "A1", locales.Tr(ctx, "report.attributes.project.number"))
	f.SetCellValue("Sheet1", "B1", locales.Tr(ctx, "report.attributes.project.name"))
	f.SetCellValue("Sheet1", "C1", locales.Tr(ctx, "export.user.name"))
	f.SetCellValue("Sheet1", "D1", locales.Tr(ctx, "export.user.email"))
	f.SetCellValue("Sheet1", "E1", locales.Tr(ctx, "export.user.role"))
	f.SetCellValue("Sheet1", "F1", locales.Tr(ctx, "user.site"))
	f.SetCellValue("Sheet1", "G1", locales.Tr(ctx, "medicine_download_storehouse"))
	f.SetCellValue("Sheet1", "H1", locales.Tr(ctx, "export.user.create_time"))
	f.SetCellValue("Sheet1", "I1", locales.Tr(ctx, "user.status"))

	cloudIds := slice.Map(d, func(index int, item map[string]interface{}) string {
		return item["cloudId"].(primitive.ObjectID).Hex()
	})

	cloudUsers, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	m := make(map[string]*models.UserData)
	//取下载用户的时区
	user, err := tools.Me(ctx)
	if err != nil {
		return "", nil, err
	}
	timeZone := float64(8)
	for _, u := range cloudUsers {
		m[u.Id] = u
		if user.CloudId.Hex() == u.Id {
			if len(u.Settings.Tz) > 0 {
				float, _ := tools.GetLocationFloat(u.Settings.Tz)
				timeZone = float
			} else if len(u.Settings.Timezone) > 0 {
				float, _ := strconv.ParseFloat(u.Settings.Timezone, 64) // 第二个参数指定了希望得到的浮点数精度，64 表示需要 float64
				timeZone = float
			}
		}
	}

	utc := tools.FormatOffsetToZoneStringUtc(timeZone)
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	axisIndex := 2
	for i := 0; i < len(d); i++ {
		cloudId := d[i]["cloudId"].(primitive.ObjectID)
		userData := m[cloudId.Hex()]
		if userData == nil {
			continue
		}
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(axisIndex), project.ProjectInfo.Number)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(axisIndex), project.ProjectInfo.Name)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(axisIndex), userData.Info.Name)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(axisIndex), userData.Info.Email)
		if d[i]["createAt"] != nil {
			createAt := d[i]["createAt"].(int64)
			createDate := time.Unix(createAt, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			createDate = createDate + "(" + utc + ")"
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(axisIndex), createDate)
		}

		var statusStr string
		if d[i]["unbind"] == true {
			statusStr = locales.Tr(ctx, "export.user.status_invalid")
		} else if userData.Status == 0 {
			statusStr = locales.Tr(ctx, "user.status.open")
		} else {
			statusStr = locales.Tr(ctx, "export.user.status_effective")
		}

		f.SetCellValue("Sheet1", "I"+strconv.Itoa(axisIndex), statusStr)
		var roles primitive.A
		if d[i]["roles"] != nil {
			roles = d[i]["roles"].(primitive.A)
		}
		var sites primitive.A
		if d[i]["sites"] != nil {
			sites = d[i]["sites"].(primitive.A)
		}
		var depots primitive.A
		if d[i]["depots"] != nil {
			depots = d[i]["depots"].(primitive.A)
		}
		max := 1
		if len(roles) > max {
			max = len(roles)
		}
		if len(sites) > max {
			max = len(sites)
		}
		if len(depots) > max {
			max = len(depots)
		}

		f.MergeCell("Sheet1", "A"+strconv.Itoa(axisIndex), "A"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "B"+strconv.Itoa(axisIndex), "B"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "C"+strconv.Itoa(axisIndex), "C"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "D"+strconv.Itoa(axisIndex), "D"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "H"+strconv.Itoa(axisIndex), "H"+strconv.Itoa(axisIndex+max-1))
		f.MergeCell("Sheet1", "I"+strconv.Itoa(axisIndex), "I"+strconv.Itoa(axisIndex+max-1))
		f.SetColWidth("Sheet1", "B", "J", 25)
		for m := 0; m < len(roles); m++ {
			role := roles[m].(map[string]interface{})
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(axisIndex+m), role["name"])
		}

		for n := 0; n < len(sites); n++ {
			site := sites[n].(map[string]interface{})
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(axisIndex+n), fmt.Sprintf("%s-%s", site["number"], site["name"]))
		}

		for o := 0; o < len(depots); o++ {
			depot := depots[o].(map[string]interface{})
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(axisIndex+o), depot["name"])
		}

		axisIndex = axisIndex + max

	}

	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	envCode := getEnvName(project, envOID)

	return fmt.Sprintf("%s[%s]UserRoleStatusReport_%s.xlsx", project.Number, envCode, now.Format("20060102150405")), buffer.Bytes(), nil
}

func ExportProjectPermissionConfigurationExport(ctx *gin.Context, projectID string, envID string, now time.Time, projectRolePermissionIds []string) (string, []byte, error) {
	poid, err := primitive.ObjectIDFromHex(projectID)
	if err != nil {
		return "", nil, err
	}
	envOID, err := primitive.ObjectIDFromHex(envID)
	if err != nil {
		return "", nil, err
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": poid}).Decode(&project)
	if err != nil {
		return "", nil, err
	}

	var projectRolePermissionOIds []primitive.ObjectID
	for _, rolePermissionId := range projectRolePermissionIds {
		projectRolePermissionOID, _ := primitive.ObjectIDFromHex(rolePermissionId)
		projectRolePermissionOIds = append(projectRolePermissionOIds, projectRolePermissionOID)
	}

	agg := bson.A{
		bson.D{{Key: "$match", Value: bson.D{{Key: "project_id", Value: poid}, {Key: "_id", Value: bson.M{"$in": projectRolePermissionOIds}}}}},
		bson.D{
			{Key: "$project",
				Value: bson.D{
					{Key: "_id", Value: 0},
					{Key: "name", Value: 1},
					{Key: "permissions", Value: 1},
				},
			},
		},
	}
	var permissions []models.ProjectRolePermission
	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(ctx, agg)
	if err != nil {
		return "", nil, err
	}
	if err = cursor.All(ctx, &permissions); err != nil {
		return "", nil, err
	}

	positionMap, reverseIndex := reverseIndex(project.ProjectInfo.ResearchAttribute)
	// change reverseIndex to map[string]string
	permission2Menu := make(map[string]string)
	for permission, menu := range reverseIndex {
		permission2Menu[permission] = strings.Join(menu, " > ")
	}
	role := make(map[string]map[string][]string) // role -> menu -> permissions
	for _, permission := range permissions {
		menu := make(map[string][]string)
		for _, p := range permission.Permissions {
			if _, ok := permission2Menu[p]; ok {
				if _, ok := menu[permission2Menu[p]]; !ok {
					menu[permission2Menu[p]] = make([]string, 0)
				}
				menu[permission2Menu[p]] = append(menu[permission2Menu[p]], p)
			}
		}
		role[permission.Name] = menu
	}

	r := make([]tools.Sheet[string], len(role))

	roleIndex := 0
	for roleName, menus := range role {
		// change menus from map[string][]string to [][]string
		tmpM := make([][]string, len(positionMap))
		for _, permissions := range menus {
			if menuCascade, ok := reverseIndex[permissions[0]]; ok {
				lowestMenu := menuCascade[len(menuCascade)-1]
				index := positionMap[lowestMenu]
				tmpM[index] = permissions
			}
		}
		// compact the sparse array m to a dense array menu
		m := make([][]string, len(menus))
		mIndex := 0
		for _, item := range tmpM {
			if len(item) != 0 {
				m[mIndex] = item
				mIndex++
			}
		}
		rows := make([][]string, 0)

		// 表头
		header := make([]string, 0)
		if locales.Lang(ctx) == "zh" {
			header = append(header, "项目编号")
			header = append(header, "项目名称")
			header = append(header, "角色")
			header = append(header, "菜单")
			header = append(header, "操作")
		} else {
			header = append(header, "Project Number")
			header = append(header, "Project Name")
			header = append(header, "Role")
			header = append(header, "Menu")
			header = append(header, "Operation")
		}
		rows = append(rows, header)

		// 第一行
		firstRow := make([]string, 0)
		firstRow = append(firstRow, project.ProjectInfo.Number)
		firstRow = append(firstRow, project.ProjectInfo.Name)
		// 角色名
		firstRow = append(firstRow, roleName)
		// 层叠菜单
		menuCascade := make([]string, 0)
		if len(m) == 0 || m[0] == nil || len(m[0]) == 0 || m[0][0] == "" {
			rows = append(rows, firstRow)
			r[roleIndex] = tools.Sheet[string]{Name: roleName, Rows: rows}
			roleIndex++
			continue
		}
		for _, code := range reverseIndex[m[0][0]] {
			menuCascade = append(menuCascade, locales.Tr(ctx, code))
		}
		firstRow = append(firstRow, strings.Join(menuCascade, "-"))
		// 权限
		p := make([]string, 0)
		for _, permission := range m[0] {
			p = append(p, locales.Tr(ctx, permission))
		}
		firstRow = append(firstRow, strings.Join(p, "、"))

		rows = append(rows, firstRow)

		// 其他行
		for _, permissions := range m[1:] {
			row := make([]string, 0)
			// 空串占位
			row = append(row, "")
			row = append(row, "")
			row = append(row, "")
			// 层叠菜单
			menuCascade := make([]string, 0)
			for _, code := range reverseIndex[permissions[0]] {
				menuCascade = append(menuCascade, locales.Tr(ctx, code))
			}
			row = append(row, strings.Join(menuCascade, "-"))
			// 权限
			p := make([]string, 0)
			for _, permission := range permissions {
				if locales.Tr(ctx, permission) != "" {
					p = append(p, locales.Tr(ctx, permission))
				}
			}
			row = append(row, strings.Join(p, "、"))
			rows = append(rows, row)
		}
		r[roleIndex] = tools.Sheet[string]{Name: roleName, Rows: rows}
		roleIndex++
	}

	// 按角色名排序
	algorithm.MergeSort(r, &comparator{})
	bytes, err := tools.ExcelBytes(r)
	if err != nil {
		return "", nil, err
	}
	envCode := getEnvName(project, envOID)
	filename := fmt.Sprintf("%s[%s]ProjectPermissionConfigurationReport_%s.xlsx", project.Number, envCode, now.Format("20060102150405"))

	return filename, bytes, nil
}

func ConfigureReportHtmlData(ctx *gin.Context, projectID string, envID string, cohortID string, roleId string, now time.Time) (models.ConfigureDetail, error) {
	var configureDetail models.ConfigureDetail

	// 获取配置数据
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	//isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	//if err != nil {
	//	return configureDetail, err
	//}

	match := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match["cohort_id"] = cohortOID
	}

	//项目详情
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return configureDetail, err
	}
	if cohortID != "" {
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				for _, cohort := range environment.Cohorts {
					if cohort.ID.Hex() == cohortID {
						configureDetail.Title = cohort.Name
					}
				}
			}
		}
	} else {
		configureDetail.Title = locales.Tr(ctx, "configureReport.projectDetails")
	}

	//属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return configureDetail, err
	}
	var attributeConfigureInfo models.AttributeConfigureInfo
	attributeConfigureInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure")
	//系统配置
	var systemConfigureInfo models.SystemConfigureInfo
	systemConfigureInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration")
	systemConfigureInfo.RandomizeLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize")
	systemConfigureInfo.DisplayRandomizationIDLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID")
	systemConfigureInfo.DisplayRandomSequenceNumberLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber")
	systemConfigureInfo.RandomSequenceNumberPrefixLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberPrefix")
	systemConfigureInfo.RandomSequenceNumberDigitLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberDigit")
	systemConfigureInfo.RandomSequenceNumberStartLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberStart")
	systemConfigureInfo.TreatmentDesignLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign")
	systemConfigureInfo.DTPRuleLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule")
	systemConfigureInfo.RandomizationSupplyCheckLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck")
	systemConfigureInfo.BlindDesignLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign")
	systemConfigureInfo.SubjectScreeningProcessLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess")
	if attribute.AttributeInfo.Random {
		systemConfigureInfo.Randomize = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.random")
		if attribute.AttributeInfo.IsRandomNumber {
			systemConfigureInfo.DisplayRandomizationID = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.show")
		} else {
			systemConfigureInfo.DisplayRandomizationID = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.notShow")
		}
		if attribute.AttributeInfo.IsRandomSequenceNumber {
			systemConfigureInfo.DisplayRandomSequenceNumberShowFlag = true
			systemConfigureInfo.DisplayRandomSequenceNumber = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.show")
			systemConfigureInfo.RandomSequenceNumberPrefix = attribute.AttributeInfo.RandomSequenceNumberPrefix
			systemConfigureInfo.RandomSequenceNumberDigit = strconv.Itoa(attribute.AttributeInfo.RandomSequenceNumberDigit)
			systemConfigureInfo.RandomSequenceNumberStart = strconv.Itoa(attribute.AttributeInfo.RandomSequenceNumberStart)
		} else {
			systemConfigureInfo.DisplayRandomSequenceNumber = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.notShow")
		}

	} else {
		systemConfigureInfo.Randomize = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.non-randomized")
		systemConfigureInfo.DisplayRandomizationID = "-"
		systemConfigureInfo.DisplayRandomSequenceNumber = "-"
	}
	if attribute.AttributeInfo.Dispensing {
		systemConfigureInfo.TreatmentDesign = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.yes")
		if attribute.AttributeInfo.RandomControl {
			if attribute.AttributeInfo.RandomControlRule == 1 {
				systemConfigureInfo.RandomizationSupplyCheck = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule1")
			} else if attribute.AttributeInfo.RandomControlRule == 2 {
				systemConfigureInfo.RandomizationSupplyCheck = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule2")
			} else if attribute.AttributeInfo.RandomControlRule == 3 {
				systemConfigureInfo.RandomizationSupplyCheck =
					locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3") +
						locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.least") +
						strconv.Itoa(attribute.AttributeInfo.RandomControlGroup) +
						locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.groups")
			}
		} else {
			systemConfigureInfo.RandomizationSupplyCheck = "-"
		}
	} else {
		systemConfigureInfo.TreatmentDesign = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.no")
		systemConfigureInfo.RandomizationSupplyCheck = "-"
	}

	if attribute.AttributeInfo.DtpRule == 1 {
		systemConfigureInfo.DTPRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.ip")
	} else if attribute.AttributeInfo.DtpRule == 2 {
		systemConfigureInfo.DTPRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.visitFlow")
	} else if attribute.AttributeInfo.DtpRule == 3 {
		systemConfigureInfo.DTPRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.notApplicable")
	} else {
		systemConfigureInfo.DTPRule = "-"
	}

	if attribute.AttributeInfo.Blind {
		systemConfigureInfo.BlindDesign = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.blind")
	} else {
		systemConfigureInfo.BlindDesign = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.open")
	}
	if attribute.AttributeInfo.IsScreen {
		systemConfigureInfo.SubjectScreeningProcess = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.open")
	} else {
		systemConfigureInfo.SubjectScreeningProcess = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.close")
	}
	attributeConfigureInfo.SystemConfigure = systemConfigureInfo
	//受试者号规则
	var subjectIDRulesInfo models.SubjectIDRulesInfo
	subjectIDRulesInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules")
	subjectIDRulesInfo.SubjectNumberInputRuleLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule")
	subjectIDRulesInfo.SubjectPrefixLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix")
	subjectIDRulesInfo.SubjectIDPrefixLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDPrefix")
	subjectIDRulesInfo.ReplacementTextForSubjectIDLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextForSubjectID")
	subjectIDRulesInfo.ReplacementTextEnForSubjectIDLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextEnForSubjectID")
	subjectIDRulesInfo.SubjectIDDigitLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDDigit")
	subjectIDRulesInfo.SubjectReplacementLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement")
	subjectIDRulesInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.takeCare")
	if attribute.AttributeInfo.SubjectNumberRule == 0 || attribute.AttributeInfo.SubjectNumberRule == 1 {
		subjectIDRulesInfo.SubjectNumberInputRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule1")
	} else if attribute.AttributeInfo.SubjectNumberRule == 2 {
		subjectIDRulesInfo.SubjectNumberInputRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule2")
	} else if attribute.AttributeInfo.SubjectNumberRule == 3 {
		subjectIDRulesInfo.SubjectNumberInputRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule3")
	}
	if attribute.AttributeInfo.Prefix {
		subjectIDRulesInfo.SubjectPrefix = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.have")
	} else {
		subjectIDRulesInfo.SubjectPrefix = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.nothing")
	}
	if len(attribute.AttributeInfo.PrefixExpression) > 0 {
		subjectIDRulesInfo.SubjectIDPrefix = attribute.AttributeInfo.PrefixExpression
	}
	if len(attribute.AttributeInfo.SubjectReplaceText) > 0 {
		subjectIDRulesInfo.ReplacementTextForSubjectID = attribute.AttributeInfo.SubjectReplaceText
	}
	if len(attribute.AttributeInfo.SubjectReplaceTextEn) > 0 {
		subjectIDRulesInfo.ReplacementTextEnForSubjectID = attribute.AttributeInfo.SubjectReplaceTextEn
	}
	subjectIDRulesInfo.SubjectIDDigit = strconv.Itoa(attribute.AttributeInfo.Digit)
	if attribute.AttributeInfo.AllowReplace {
		subjectIDRulesInfo.SubjectReplacement = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.open")
	} else {
		subjectIDRulesInfo.SubjectReplacement = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.close")
	}
	attributeConfigureInfo.SubjectIDRules = subjectIDRulesInfo
	//其他规则
	var otherRulesInfo models.OtherRulesInfo
	otherRulesInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules")
	otherRulesInfo.StopUnblindedSubjectsLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects")
	otherRulesInfo.QuarantinedIPCountingRuleLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule")
	otherRulesInfo.TransportAccordingPackagingLabel = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging")
	otherRulesInfo.Deactivate = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.deactivate")
	otherRulesInfo.Quarantine = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantine")
	otherRulesInfo.Packing = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.packing")
	if attribute.AttributeInfo.UnBlindingRestrictions {
		otherRulesInfo.StopUnblindedSubjects = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.true")
	} else {
		otherRulesInfo.StopUnblindedSubjects = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.false")
	}
	if attribute.AttributeInfo.IsFreeze {
		otherRulesInfo.QuarantinedIPCountingRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.true")
	} else {
		otherRulesInfo.QuarantinedIPCountingRule = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.false")
	}
	if count, _ := tools.Database.Collection("drug_package_configure").CountDocuments(nil, bson.M{"project_id": projectOID, "env_id": envOID}); count > 0 {
		var drugPackageConfigure models.DrugPackageConfigure
		err = tools.Database.Collection("drug_package_configure").FindOne(nil, bson.M{"project_id": projectOID, "env_id": envOID}).Decode(&drugPackageConfigure)
		if err != nil {
			return configureDetail, err
		}
		if drugPackageConfigure.IsOpen {
			otherRulesInfo.TransportAccordingPackaging = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.true")
		} else {
			otherRulesInfo.TransportAccordingPackaging = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.false")
		}
	} else {
		otherRulesInfo.TransportAccordingPackaging = locales.Tr(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.false")
	}
	attributeConfigureInfo.OtherRules = otherRulesInfo
	configureDetail.AttributeConfigure = attributeConfigureInfo

	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, err
	}

	//随机配置
	var randomConfigureInfo models.RandomConfigureInfo
	randomConfigureInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure")
	var randomInfo models.RandomInfo
	randomInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign")
	randomInfo.RandomTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType")
	randomInfo.GroupLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.group")
	randomInfo.RegionFactorLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor")
	randomInfo.FactorOptionLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factorOption")

	//randomInfo.FactorLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor") + strconv.Itoa(len(factorInfoList)+1)
	randomInfo.FieldNumberLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldNumber")
	randomInfo.FieldNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldName")
	randomInfo.VariableLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.variable")
	randomInfo.ControlTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.controlType")
	randomInfo.OptionLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.option")
	randomInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status")

	//随机类型
	if randomDesign.Info.Type == 1 {
		randomInfo.RandomType = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType.region")
	} else if randomDesign.Info.Type == 2 {
		randomInfo.RandomType = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType.min")
	}
	//组别名称
	//randomGroupList := make([]string, 0)
	//if randomDesign.Info.Groups != nil && len(randomDesign.Info.Groups) > 0 {
	//	for _, group := range randomDesign.Info.Groups {
	//		if group.SubGroup != nil && len(group.SubGroup) > 0 {
	//			for _, sub := range group.SubGroup {
	//				randomGroupList = append(randomGroupList, group.Name+"("+sub.Name+")")
	//			}
	//		} else {
	//			randomGroupList = append(randomGroupList, group.Name)
	//		}
	//	}
	//} else {
	//	randomGroupList = append(randomGroupList, "-")
	//}
	groups := models.RandomDesignToGroupSub(randomDesign, isBlindedRole, attribute)
	randomInfo.Group = groups
	//地区分层
	if attribute.AttributeInfo.CountryLayered {
		randomInfo.RegionFactor = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.country")
	} else if attribute.AttributeInfo.InstituteLayered {
		randomInfo.RegionFactor = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.site")
	} else if attribute.AttributeInfo.RegionLayered {
		regionList := make([]models.Region, 0)
		cursor, err := tools.Database.Collection("region").Find(nil, bson.M{"project_id": projectOID, "env_id": envOID, "deleted": false})
		if err != nil {
			return configureDetail, err
		}
		err = cursor.All(nil, &regionList)
		if err != nil {
			return configureDetail, err
		}
		if regionList != nil && len(regionList) > 0 {
			regionNameList := make([]string, 0)
			for _, region := range regionList {
				regionNameList = append(regionNameList, region.Name)
			}
			randomInfo.RegionFactor = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.region") +
				"(" + strings.Join(regionNameList, ",") + ")"
		} else {
			randomInfo.RegionFactor = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.region")
		}
	} else {
		randomInfo.RegionFactor = ""
	}

	//分层因素
	factorInfoList := make([]models.FactorInfo, 0)
	var stratificationCalculationInfo models.StratificationCalculationInfo
	stratificationCalculationInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation")
	stratificationCalculationInfo.FieldNumberLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.fieldNumber")
	stratificationCalculationInfo.FormulaTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType")
	stratificationCalculationInfo.CustomFormulaLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.customFormula")
	stratificationCalculationInfo.RetainDecimalsLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.retainDecimals")
	stratificationCalculationInfo.LayeredNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredName")
	stratificationCalculationInfo.LayeredOptionLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredOption")
	stratificationCalculationInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status")
	fieldList := make([]models.StratificationCalculationField, 0)
	////分层计算—年龄
	//var ageInfo models.AgeInfo
	//ageInfo.ParagraphNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age")
	//ageInfo.FieldNumberLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.fieldNumber")
	//ageInfo.FormulaLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formula")
	//ageInfo.FieldNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.fieldName")
	//ageInfo.RetainDecimalsLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.retainDecimals")
	//ageInfo.ControlTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.controlType")
	//ageInfo.FormatTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formatType")
	//ageInfo.LayeredNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.layeredName")
	//ageInfo.LayeredOptionLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.layeredOption")
	//ageInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status")
	//ageInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.takeCare")
	//ageFieldList := make([]models.AgeField, 0)
	////分层计算—BMI
	//var bmiInfo models.BmiInfo
	//bmiInfo.ParagraphNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi")
	//bmiInfo.FieldNumberLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.fieldNumber")
	//bmiInfo.FormulaLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.formula")
	//bmiInfo.WeightNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.weightName")
	//bmiInfo.HeightNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.heightName")
	//bmiInfo.ControlTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.controlType")
	//bmiInfo.LayeredNameLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.layeredName")
	//bmiInfo.LayeredOptionLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.layeredOption")
	//bmiInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status")
	//bmiInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.takeCare")
	//bimFieldList := make([]models.BimField, 0)
	if randomDesign.Info.Factors != nil && len(randomDesign.Info.Factors) > 0 {
		for _, factor := range randomDesign.Info.Factors {
			if factor.IsCalc {
				var field models.StratificationCalculationField
				field.FieldNumber = factor.Number
				if *factor.CalcType == 0 {
					field.FormulaType = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.age")
				} else if *factor.CalcType == 1 {
					field.FormulaType = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.bmi")
				}
				field.CustomFormula = factor.CustomFormulas
				if factor.Precision != nil {
					field.RetainDecimals = strconv.Itoa(*factor.Precision)
				}
				field.LayeredName = factor.Label
				optionList := make([]string, 0)
				if factor.Options != nil && len(factor.Options) > 0 {
					for _, option := range factor.Options {
						optionList = append(optionList, *option.Formula+"-"+option.Label)
					}
				}
				field.LayeredOption = optionList

				if factor.Status != nil {
					if *factor.Status == 1 {
						field.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.valid")
					} else if *factor.Status == 2 {
						field.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.invalid")
					}
				} else {
					field.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.valid")
				}
				fieldList = append(fieldList, field)

				//if *factor.CalcType == 0 {
				//	//年龄
				//	var ageField models.AgeField
				//	ageField.FieldNumber = factor.Number
				//	ageField.Formula = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.formula.age")
				//	//ageField.FieldName = *factor.InputLabel
				//	if factor.Precision != nil {
				//		ageField.RetainDecimals = strconv.Itoa(*factor.Precision)
				//	}
				//	ageField.ControlType = TranslateTypeString(ctx, factor.Type)
				//	if factor.DateFormat != nil {
				//		ageField.FormatType = *factor.DateFormat
				//	}
				//	ageField.LayeredName = factor.Label
				//	optionList := make([]string, 0)
				//	if factor.Options != nil && len(factor.Options) > 0 {
				//		for _, option := range factor.Options {
				//			optionList = append(optionList, *option.Formula+"-"+option.Label)
				//		}
				//	}
				//	ageField.LayeredOption = optionList
				//	if factor.Status != nil {
				//		if *factor.Status == 1 {
				//			ageField.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status.valid")
				//		} else if *factor.Status == 2 {
				//			ageField.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status.invalid")
				//		}
				//	} else {
				//		ageField.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.age.status.valid")
				//	}
				//	ageFieldList = append(ageFieldList, ageField)
				//} else if *factor.CalcType == 1 {
				//	//BMI
				//	var bimField models.BimField
				//	bimField.FieldNumber = factor.Number
				//	bimField.Formula = "BMI"
				//	//bimField.WeightName = *factor.InputWeightLabel
				//	//bimField.HeightName = *factor.InputHeightLabel
				//	bimField.ControlType = TranslateTypeString(ctx, factor.Type)
				//	bimField.LayeredName = factor.Label
				//	optionList := make([]string, 0)
				//	if factor.Options != nil && len(factor.Options) > 0 {
				//		for _, option := range factor.Options {
				//			optionList = append(optionList, *option.Formula+"-"+option.Label)
				//		}
				//	}
				//	bimField.LayeredOption = optionList
				//	if factor.Status != nil {
				//		if *factor.Status == 1 {
				//			bimField.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status.valid")
				//		} else if *factor.Status == 2 {
				//			bimField.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status.invalid")
				//		}
				//	} else {
				//		bimField.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.bmi.status.valid")
				//	}
				//	bimFieldList = append(bimFieldList, bimField)
				//}

			} else {
				//分层因素
				var factorInfo models.FactorInfo
				factorInfo.FieldNumber = factor.Number
				factorInfo.FieldName = factor.Label
				factorInfo.Variable = factor.Name
				factorInfo.ControlType = TranslateTypeString(ctx, factor.Type)
				optionList := make([]string, 0)
				if factor.Options != nil && len(factor.Options) > 0 {
					for _, option := range factor.Options {
						optionList = append(optionList, option.Label)
					}
				}
				factorInfo.Option = optionList
				if factor.Status != nil {
					if *factor.Status == 1 {
						factorInfo.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.valid")
					} else if *factor.Status == 2 {
						factorInfo.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid")
					}
				} else {
					factorInfo.Status = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.valid")
				}
				factorInfoList = append(factorInfoList, factorInfo)
			}
		}
	}

	if attribute.AttributeInfo.Random {
		if len(randomInfo.RandomType) > 0 || (randomInfo.Group != nil && len(randomInfo.Group) > 0) || len(randomInfo.RegionFactor) > 0 || (factorInfoList != nil && len(factorInfoList) > 0) ||
			(fieldList != nil && len(fieldList) > 0) {
			if len(randomInfo.RandomType) > 0 || (randomInfo.Group != nil && len(randomInfo.Group) > 0) || len(randomInfo.RegionFactor) > 0 || (factorInfoList != nil && len(factorInfoList) > 0) {
				randomConfigureInfo.Level = ".2"
				randomInfo.Level = ".1"
				//if (bimFieldList != nil && len(bimFieldList) > 0) || (ageFieldList != nil && len(ageFieldList) > 0) {
				if fieldList != nil && len(fieldList) > 0 {
					stratificationCalculationInfo.Level = ".2"
					//if bimFieldList != nil && len(bimFieldList) > 0 {
					//	bmiInfo.Level = "1."
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "2."
					//	}
					//} else {
					//	bmiInfo.Level = ""
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "1."
					//	}
					//}
				} else {
					stratificationCalculationInfo.Level = ""
					//bmiInfo.Level = ""
					//ageInfo.Level = ""
				}
			} else {
				randomInfo.Level = ""
				//if (bimFieldList != nil && len(bimFieldList) > 0) || (ageFieldList != nil && len(ageFieldList) > 0) {
				if fieldList != nil && len(fieldList) > 0 {
					randomConfigureInfo.Level = ".2"
					stratificationCalculationInfo.Level = ".1"
					//if bimFieldList != nil && len(bimFieldList) > 0 {
					//	bmiInfo.Level = "1."
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "2."
					//	}
					//} else {
					//	bmiInfo.Level = ""
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "1."
					//	}
					//}
				} else {
					stratificationCalculationInfo.Level = ""
					//bmiInfo.Level = ""
					//ageInfo.Level = ""
				}
			}
		} else {
			randomConfigureInfo.Level = ""
		}
	} else {
		randomConfigureInfo.Level = ""
	}

	randomInfo.FactorList = factorInfoList
	if factorInfoList != nil && len(factorInfoList) > 0 {
		//randomInfo.Length = strconv.Itoa(len(factorInfoList) + 1)
		var length = 1
		for _, factorInfo := range factorInfoList {
			if len(factorInfo.Option) > 0 {
				length += len(factorInfo.Option)
			} else {
				length += 1
			}
		}
		randomInfo.Length = strconv.Itoa(length)
	} else {
		randomInfo.Length = "2"
	}
	randomConfigureInfo.RandomDesign = randomInfo

	stratificationCalculationInfo.FieldList = fieldList
	//bmiInfo.FieldList = bimFieldList
	//stratificationCalculationInfo.Bmi = bmiInfo
	//ageInfo.FieldList = ageFieldList
	//stratificationCalculationInfo.Age = ageInfo
	randomConfigureInfo.StratificationCalculation = stratificationCalculationInfo

	configureDetail.RandomConfigure = randomConfigureInfo

	//表单配置
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, match).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, err
	}
	var formConfigureInfo models.FormConfigureInfo
	formConfigureInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.formConfigure")
	//受试者登记
	var subjectRegistrationInfo models.SubjectRegistrationInfo
	subjectRegistrationInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration")
	subjectRegistrationInfo.FieldNameLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.fieldName")
	subjectRegistrationInfo.IsEditableLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.isEditable")
	subjectRegistrationInfo.RequiredLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.required")
	subjectRegistrationInfo.VariableIdLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.variableId")
	subjectRegistrationInfo.ControlTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.controlType")
	subjectRegistrationInfo.OptionLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.option")
	subjectRegistrationInfo.FormatTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.formatType")
	subjectRegistrationInfo.VariableFormatLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.variableFormat")
	subjectRegistrationInfo.VariableRangeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.variableRange")
	subjectRegistrationInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.status")
	registrationFieldList := make([]models.RegistrationField, 0)

	//公式计算
	var customFormulaInfo models.CustomFormulaInfo
	customFormulaInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula")
	customFormulaInfo.FieldNameLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.fieldName")
	customFormulaInfo.RequiredLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.required")
	customFormulaInfo.VariableIdLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.variableId")
	customFormulaInfo.ControlTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.controlType")
	customFormulaInfo.FormatTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.formatType")
	customFormulaInfo.VariableFormatLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.variableFormat")
	customFormulaInfo.VariableRangeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.variableRange")
	customFormulaInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.status")
	formulaFieldList := make([]models.FormulaField, 0)

	//剂量调整
	var doseAdjustmentInfo models.DoseAdjustmentInfo
	doseAdjustmentInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment")
	doseAdjustmentInfo.FieldNameLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.fieldName")
	doseAdjustmentInfo.RequiredLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.required")
	doseAdjustmentInfo.VariableIdLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.variableId")
	doseAdjustmentInfo.ControlTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.controlType")
	doseAdjustmentInfo.OptionLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.option")
	doseAdjustmentInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.status")
	doseFieldList := make([]models.DoseField, 0)

	//分层计算
	var layeredCalculationInfo models.LayeredCalculationInfo
	layeredCalculationInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation")
	layeredCalculationInfo.FieldNameLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.fieldName")
	layeredCalculationInfo.IsEditableLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.isEditable")
	layeredCalculationInfo.RequiredLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.required")
	layeredCalculationInfo.VariableIdLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.variableId")
	layeredCalculationInfo.ControlTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.controlType")
	layeredCalculationInfo.OptionLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.option")
	layeredCalculationInfo.FormatTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.formatType")
	layeredCalculationInfo.VariableFormatLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.variableFormat")
	layeredCalculationInfo.VariableRangeLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.variableRange")
	layeredCalculationInfo.StatusLabel = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.status")
	layeredFieldList := make([]models.LayeredField, 0)

	if form.Fields != nil && len(form.Fields) > 0 {
		for _, field := range form.Fields {
			if field.ApplicationType == nil || (field.ApplicationType != nil && *field.ApplicationType == 1) {
				//受试者登记
				var registrationField models.RegistrationField
				registrationField.FieldName = field.Label
				if field.Modifiable {
					registrationField.IsEditable = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.true")
				} else {
					registrationField.IsEditable = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.false")
				}
				if field.Required {
					registrationField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.required.true")
				} else {
					registrationField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.required.false")
				}
				registrationField.VariableId = field.Variable
				registrationField.ControlType = TranslateTypeString(ctx, field.Type)
				optionList := make([]string, 0)
				if field.Options != nil && len(field.Options) > 0 {
					for _, option := range field.Options {
						optionList = append(optionList, option.Label)
					}
				}
				registrationField.Option = optionList
				if len(field.FormatType) > 0 {
					registrationField.FormatType = TranslateTypeString(ctx, field.FormatType)
				} else if len(field.DateFormat) > 0 {
					registrationField.FormatType = TranslateTypeString(ctx, field.DateFormat)
				} else if len(field.TimeFormat) > 0 {
					registrationField.FormatType = TranslateTypeString(ctx, field.TimeFormat)
				}
				if field.Length != nil {
					if field.FormatType == "decimalLength" {
						//小数
						// 将 float64 转换为字符串，动态确定小数点后的位数
						stringLength := strconv.FormatFloat(*field.Length, 'f', -1, 64)
						// 如果有小数点
						if strings.Contains(stringLength, ".") {
							parts := strings.Split(stringLength, ".")
							// 去除小数部分末尾的0
							parts[1] = strings.TrimRight(parts[1], "0")
							// 如果小数部分末尾是空，则去除小数点
							if parts[1] == "" {
								stringLength = parts[0]
							} else {
								stringLength = strings.Join(parts, ".")
							}
						}
						registrationField.VariableFormat = stringLength
					} else if field.FormatType == "numberLength" {
						//整数
						registrationField.VariableFormat = strconv.Itoa(int(*field.Length))
					} else if field.FormatType == "characterLength" {
						// 文本
						registrationField.VariableFormat = strconv.Itoa(int(*field.Length))
					}
				}
				if field.Range != nil {
					min := ""
					if field.Range.Min != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMin := strconv.FormatFloat(*field.Range.Min, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(min, ".") {
								parts := strings.Split(min, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									min = parts[0]
								} else {
									min = strings.Join(parts, ".")
								}
							}
							min = stringMin
						} else if field.FormatType == "numberLength" {
							//整数
							min = strconv.Itoa(int(*field.Range.Min))
						}
					}
					max := ""
					if field.Range.Max != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMax := strconv.FormatFloat(*field.Range.Max, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(max, ".") {
								parts := strings.Split(max, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									max = parts[0]
								} else {
									max = strings.Join(parts, ".")
								}
							}
							max = stringMax
						} else if field.FormatType == "numberLength" {
							//整数
							max = strconv.Itoa(int(*field.Range.Max))
						}
					}
					registrationField.VariableRange = min + "—" + max
				}

				if field.DateRange != nil {
					maxDate := field.DateRange.Max
					if maxDate == "currentTime" {
						maxDate = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.currentTime")
					}
					registrationField.VariableRange = field.DateRange.Min + "—" + maxDate
				}

				if field.Status != nil {
					if *field.Status == 1 {
						registrationField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.status.valid")
					} else if *field.Status == 2 {
						registrationField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.status.invalid")
					}
				} else {
					registrationField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.status.valid")
				}
				registrationFieldList = append(registrationFieldList, registrationField)

			} else if *field.ApplicationType == 2 {
				//公式计算
				var formulaField models.FormulaField
				formulaField.FieldName = field.Label
				if field.Required {
					formulaField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.required.true")
				} else {
					formulaField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.required.false")
				}
				formulaField.VariableId = field.Variable
				formulaField.ControlType = TranslateTypeString(ctx, field.Type)
				if len(field.FormatType) > 0 {
					formulaField.FormatType = TranslateTypeString(ctx, field.FormatType)
				} else if len(field.DateFormat) > 0 {
					formulaField.FormatType = TranslateTypeString(ctx, field.DateFormat)
				} else if len(field.TimeFormat) > 0 {
					formulaField.FormatType = TranslateTypeString(ctx, field.TimeFormat)
				}
				if field.Length != nil {
					if field.FormatType == "decimalLength" {
						//小数
						// 将 float64 转换为字符串，动态确定小数点后的位数
						stringLength := strconv.FormatFloat(*field.Length, 'f', -1, 64)
						// 如果有小数点
						if strings.Contains(stringLength, ".") {
							parts := strings.Split(stringLength, ".")
							// 去除小数部分末尾的0
							parts[1] = strings.TrimRight(parts[1], "0")
							// 如果小数部分末尾是空，则去除小数点
							if parts[1] == "" {
								stringLength = parts[0]
							} else {
								stringLength = strings.Join(parts, ".")
							}
						}
						formulaField.VariableFormat = stringLength
					} else if field.FormatType == "numberLength" {
						//整数
						formulaField.VariableFormat = strconv.Itoa(int(*field.Length))
					}
				}
				if field.Range != nil {
					min := ""
					if field.Range.Min != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMin := strconv.FormatFloat(*field.Range.Min, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(stringMin, ".") {
								parts := strings.Split(stringMin, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									stringMin = parts[0]
								} else {
									stringMin = strings.Join(parts, ".")
								}
							}
							min = stringMin
						} else if field.FormatType == "numberLength" {
							//整数
							min = strconv.Itoa(int(*field.Range.Min))
						}
					}
					max := ""
					if field.Range.Max != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMax := strconv.FormatFloat(*field.Range.Max, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(max, ".") {
								parts := strings.Split(max, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									max = parts[0]
								} else {
									max = strings.Join(parts, ".")
								}
							}
							max = stringMax
						} else if field.FormatType == "numberLength" {
							//整数
							max = strconv.Itoa(int(*field.Range.Max))
						}
					}
					formulaField.VariableRange = min + "—" + max
				}
				if field.Status != nil {
					if *field.Status == 1 {
						formulaField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.status.valid")
					} else if *field.Status == 2 {
						formulaField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.status.invalid")
					}
				} else {
					formulaField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.customFormula.status.valid")
				}
				formulaFieldList = append(formulaFieldList, formulaField)

			} else if *field.ApplicationType == 3 {
				//剂量调整
				var doseField models.DoseField
				doseField.FieldName = field.Label
				if field.Required {
					doseField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.required.true")
				} else {
					doseField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.required.false")
				}
				doseField.VariableId = field.Variable
				doseField.ControlType = TranslateTypeString(ctx, field.Type)
				optionList := make([]string, 0)
				if field.Options != nil && len(field.Options) > 0 {
					for _, option := range field.Options {
						optionLabel := option.Label
						if option.Label == "form.control.type.options.one" {
							optionLabel = locales.Tr(ctx, "form.control.type.options.one")
						} else if option.Label == "form.control.type.options.two" {
							optionLabel = locales.Tr(ctx, "form.control.type.options.two")
						} else if option.Label == "form.control.type.options.three" {
							optionLabel = locales.Tr(ctx, "form.control.type.options.three")
						} else {
						}
						optionList = append(optionList, optionLabel)
					}
				}
				doseField.Option = optionList
				if field.Status != nil {
					if *field.Status == 1 {
						doseField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.status.valid")
					} else if *field.Status == 2 {
						doseField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.status.invalid")
					}
				} else {
					doseField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.status.valid")
				}
				doseFieldList = append(doseFieldList, doseField)
			} else if *field.ApplicationType == 4 {
				//分层计算
				var layeredField models.LayeredField
				layeredField.FieldName = field.Label
				if field.Modifiable {
					layeredField.IsEditable = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.true")
				} else {
					layeredField.IsEditable = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.false")
				}
				if field.Required {
					layeredField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.required.true")
				} else {
					layeredField.Required = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.required.false")
				}
				layeredField.VariableId = field.Variable
				layeredField.ControlType = TranslateTypeString(ctx, field.Type)
				optionList := make([]string, 0)
				if field.Options != nil && len(field.Options) > 0 {
					for _, option := range field.Options {
						optionList = append(optionList, option.Label)
					}
				}
				layeredField.Option = optionList
				if len(field.FormatType) > 0 {
					layeredField.FormatType = TranslateTypeString(ctx, field.FormatType)
				} else if len(field.DateFormat) > 0 {
					layeredField.FormatType = TranslateTypeString(ctx, field.DateFormat)
				} else if len(field.TimeFormat) > 0 {
					layeredField.FormatType = TranslateTypeString(ctx, field.TimeFormat)
				}
				if field.Length != nil {
					if field.FormatType == "decimalLength" {
						//小数
						// 将 float64 转换为字符串，动态确定小数点后的位数
						stringLength := strconv.FormatFloat(*field.Length, 'f', -1, 64)
						// 如果有小数点
						if strings.Contains(stringLength, ".") {
							parts := strings.Split(stringLength, ".")
							// 去除小数部分末尾的0
							parts[1] = strings.TrimRight(parts[1], "0")
							// 如果小数部分末尾是空，则去除小数点
							if parts[1] == "" {
								stringLength = parts[0]
							} else {
								stringLength = strings.Join(parts, ".")
							}
						}
						layeredField.VariableFormat = stringLength
					} else if field.FormatType == "numberLength" {
						//整数
						layeredField.VariableFormat = strconv.Itoa(int(*field.Length))
					}
				}
				if field.Range != nil {
					min := ""
					if field.Range.Min != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMin := strconv.FormatFloat(*field.Range.Min, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(min, ".") {
								parts := strings.Split(min, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									min = parts[0]
								} else {
									min = strings.Join(parts, ".")
								}
							}
							min = stringMin
						} else if field.FormatType == "numberLength" {
							//整数
							min = strconv.Itoa(int(*field.Range.Min))
						}
					}
					max := ""
					if field.Range.Max != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMax := strconv.FormatFloat(*field.Range.Max, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(max, ".") {
								parts := strings.Split(max, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									max = parts[0]
								} else {
									max = strings.Join(parts, ".")
								}
							}
							max = stringMax
						} else if field.FormatType == "numberLength" {
							//整数
							max = strconv.Itoa(int(*field.Range.Max))
						}
					}
					layeredField.VariableRange = min + "—" + max
				}

				if field.DateRange != nil {
					maxDate := field.DateRange.Max
					if maxDate == "currentTime" {
						maxDate = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.currentTime")
					}
					layeredField.VariableRange = field.DateRange.Min + "—" + maxDate
				}

				if field.Status != nil {
					if *field.Status == 1 {
						layeredField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.status.valid")
					} else if *field.Status == 2 {
						layeredField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.status.invalid")
					}
				} else {
					layeredField.Status = locales.Tr(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.status.valid")
				}
				layeredFieldList = append(layeredFieldList, layeredField)
			}
		}
	}

	if len(randomConfigureInfo.Level) > 0 {
		//随机
		if (registrationFieldList != nil && len(registrationFieldList) > 0) || (formulaFieldList != nil && len(formulaFieldList) > 0) || (doseFieldList != nil && len(doseFieldList) > 0) || (layeredFieldList != nil && len(layeredFieldList) > 0) {
			formConfigureInfo.Level = ".3"
			if registrationFieldList != nil && len(registrationFieldList) > 0 {
				//受试者登记
				subjectRegistrationInfo.Level = "1."
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "2."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "3."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "4."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "3."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "3."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "2."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				}
			} else {
				subjectRegistrationInfo.Level = ""
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "1."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "3."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "2."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "1."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "2."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "1."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				}
			}

		} else {
			formConfigureInfo.Level = ""
		}

	} else {
		//不随机
		if (registrationFieldList != nil && len(registrationFieldList) > 0) || (formulaFieldList != nil && len(formulaFieldList) > 0) || (doseFieldList != nil && len(doseFieldList) > 0) {
			formConfigureInfo.Level = ".2"
			if registrationFieldList != nil && len(registrationFieldList) > 0 {
				//受试者登记
				subjectRegistrationInfo.Level = "1."
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "2."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "3."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				}
			} else {
				subjectRegistrationInfo.Level = ""
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "1."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "1."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				}
			}

		} else {
			formConfigureInfo.Level = ""
		}
	}

	subjectRegistrationInfo.FieldList = registrationFieldList
	formConfigureInfo.SubjectRegistration = subjectRegistrationInfo

	customFormulaInfo.FieldList = formulaFieldList
	formConfigureInfo.CustomFormula = customFormulaInfo

	doseAdjustmentInfo.FieldList = doseFieldList
	formConfigureInfo.DoseAdjustment = doseAdjustmentInfo

	layeredCalculationInfo.FieldList = layeredFieldList
	formConfigureInfo.LayeredCalculation = layeredCalculationInfo

	configureDetail.FormConfigure = formConfigureInfo

	//研究产品管理
	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, err
	}
	var ipManagementInfo models.IpManagementInfo
	ipManagementInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement")

	//访视管理
	var visitManagementInfo models.VisitManagementInfo
	visitManagementInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement")
	visitManagementInfo.CycleVersionLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.cycleVersion")
	visitManagementInfo.VisitOffsetTypeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.visitOffsetType")
	visitManagementInfo.CycleVersion = visitCycle.Version
	if visitCycle.ConfigInfo.VisitType == 0 {
		visitManagementInfo.VisitOffsetType = "baseline"
	} else if visitCycle.ConfigInfo.VisitType == 1 {
		visitManagementInfo.VisitOffsetType = "lastdate"
	}
	visitManagementInfo.VisitNumberLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.visitNumber")
	visitManagementInfo.VisitNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.visitName")
	visitManagementInfo.GroupLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.group")
	visitManagementInfo.IntervalDurationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.intervalDuration")
	visitManagementInfo.WindowLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.window")
	visitManagementInfo.IsDispenseLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isDispense")
	visitManagementInfo.IsRandomizeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isRandomize")
	visitManagementInfo.IsDTPLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isDTP")
	visitManagementInfo.IsSubjectReplaceLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isSubjectReplace")
	visitManagementInfo.IsDoseAdjustmentLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isDoseAdjustment")
	visitDetailsList := make([]models.VisitDetails, 0)
	if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
		for _, info := range visitCycle.Infos {
			var visitDetails models.VisitDetails
			visitDetails.VisitNumber = info.Number
			visitDetails.VisitName = info.Name
			groupList := make([]string, 0)
			if info.Group != nil && len(info.Group) > 0 {
				for _, group := range info.Group {
					if isBlindedRole && attribute.AttributeInfo.Blind {
						groupList = append(groupList, models.BlindData)
					} else {
						groupList = append(groupList, group.(string))
					}
				}
			}
			visitDetails.Group = strings.Join(groupList, ",")
			if info.Interval != nil {
				visitDetails.IntervalDuration = strconv.Itoa(*info.Interval)
			} else {
				visitDetails.IntervalDuration = "-"
			}
			min := ""
			if info.PeriodMin != nil {
				min = strconv.Itoa(int(*info.PeriodMin))
			}
			max := ""
			if info.PeriodMax != nil {
				max = strconv.Itoa(int(*info.PeriodMax))
			}
			visitDetails.Window = min + "-" + max
			if info.Dispensing {
				visitDetails.IsDispense = "√"
			} else {
				visitDetails.IsDispense = "×"
			}
			if info.Random {
				visitDetails.IsRandomize = "√"
			} else {
				visitDetails.IsRandomize = "×"
			}
			if info.DTP {
				visitDetails.IsDTP = "√"
			} else {
				visitDetails.IsDTP = "×"
			}
			if info.Replace {
				visitDetails.IsSubjectReplace = "√"
			} else {
				visitDetails.IsSubjectReplace = "×"
			}
			if info.DoseAdjustment {
				visitDetails.IsDoseAdjustment = "√"
			} else {
				visitDetails.IsDoseAdjustment = "×"
			}
			visitDetailsList = append(visitDetailsList, visitDetails)
		}
	}

	//研究产品配置
	var drugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, match).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, errors.WithStack(err)
	}

	// 研究产品配置-设置
	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, match).Decode(&drugConfigureSetting)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return configureDetail, errors.WithStack(err)
	}

	var treatmentDesignInfo models.TreatmentDesignInfo
	treatmentDesignInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign")

	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return configureDetail, errors.WithStack(err)
	}

	isOtherDrugMap, err := tools.IsOtherDrugMap(envOID)
	if err != nil {
		return configureDetail, errors.WithStack(err)
	}

	//DTP研究产品
	var dtpIpInfo models.DTPIpInfo
	dtpIpInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp")
	dtpIpInfo.IpLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.ip")
	dtpIpInfo.DTPModeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.dtpMode")
	dtpIpInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.takeCare")
	dtpIpFieldList := make([]models.DTPIpField, 0)

	//按标签/开放配置
	var labelOpenInfo models.LabelOpenInfo
	labelOpenInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen")
	labelOpenInfo.GroupLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.group")
	labelOpenInfo.VisitNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.visitName")
	labelOpenInfo.IpNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.IpName")
	labelOpenInfo.DispensationQuantityLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.dispensationQuantity")
	labelOpenInfo.CustomFormulaLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.customFormula")
	labelOpenInfo.CombinedDispensationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.combinedDispensation")
	labelOpenInfo.IpSpecificationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.ipSpecification")
	labelOpenInfo.SpecificationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.specification")
	labelOpenInfo.AutomaticAssignmentLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.automaticAssignment")
	labelOpenInfo.CalculationUnitLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.calculationUnit")
	labelOpenInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.takeCare")
	labelOpenFieldList := make([]models.LabelOpenField, 0)

	//按公式计算
	var formulaConfigInfo models.FormulaConfigInfo
	formulaConfigInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig")

	//年龄
	var formulaAgeInfo models.FormulaAgeInfo
	formulaAgeInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge")
	formulaAgeInfo.GroupLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.group")
	formulaAgeInfo.VisitNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.visitName")
	formulaAgeInfo.IpNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.IpName")
	formulaAgeInfo.IpSpecificationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ipSpecification")
	formulaAgeInfo.AgeRangeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ageRange")
	formulaAgeInfo.DispensationQuantityLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.dispensationQuantity")
	formulaAgeInfo.IsOpenIpLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.isOpenIp")
	formulaAgeInfo.KeepDecimalPlacesLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.keepDecimalPlaces")
	formulaAgeInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.takeCare")
	formulaAgeFieldList := make([]models.FormulaAgeField, 0)

	//体重
	var formulaWeightInfo models.FormulaWeightInfo
	formulaWeightInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight")
	formulaWeightInfo.GroupLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.group")
	formulaWeightInfo.VisitNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.visitName")
	formulaWeightInfo.IpNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.IpName")
	formulaWeightInfo.IpSpecificationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.ipSpecification")
	formulaWeightInfo.WeightRangeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightRange")
	formulaWeightInfo.DispensationQuantityLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.dispensationQuantity")
	formulaWeightInfo.IsOpenIpLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.isOpenIp")
	formulaWeightInfo.KeepDecimalPlacesLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.keepDecimalPlaces")
	formulaWeightInfo.WeightComparisonCalculationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightComparisonCalculation")
	formulaWeightInfo.ComparedWithLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.comparedWith")
	formulaWeightInfo.ChangeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.change")
	formulaWeightInfo.CalculationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.calculation")
	formulaWeightInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.takeCare")
	formulaWeightFieldList := make([]models.FormulaWeightField, 0)

	//简易体表面积BSA
	var formulaSimpleBSAInfo models.FormulaSimpleBSAInfo
	formulaSimpleBSAInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA")
	formulaSimpleBSAInfo.GroupLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.group")
	formulaSimpleBSAInfo.VisitNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.visitName")
	formulaSimpleBSAInfo.IpNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.IpName")
	formulaSimpleBSAInfo.IpSpecificationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.ipSpecification")
	formulaSimpleBSAInfo.UnitCapacityLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCapacity")
	formulaSimpleBSAInfo.UnitCalculationStandardLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCalculationStandard")
	formulaSimpleBSAInfo.IsOpenIpLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.isOpenIp")
	formulaSimpleBSAInfo.WeightComparisonCalculationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.weightComparisonCalculation")
	formulaSimpleBSAInfo.ComparedWithLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.comparedWith")
	formulaSimpleBSAInfo.ChangeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.change")
	formulaSimpleBSAInfo.CalculationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.calculation")
	formulaSimpleBSAInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.takeCare")
	formulaSimpleBSAFieldList := make([]models.FormulaSimpleBSAField, 0)

	//其他体表面积(按自定义公式)
	var formulaOtherBSAInfo models.FormulaOtherBSAInfo
	formulaOtherBSAInfo.Title = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA")
	formulaOtherBSAInfo.GroupLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.group")
	formulaOtherBSAInfo.VisitNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.visitName")
	formulaOtherBSAInfo.IpNameLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.IpName")
	formulaOtherBSAInfo.IpSpecificationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.ipSpecification")
	formulaOtherBSAInfo.UnitCapacityLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCapacity")
	formulaOtherBSAInfo.UnitCalculationStandardLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCalculationStandard")
	formulaOtherBSAInfo.IsOpenIpLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.isOpenIp")
	formulaOtherBSAInfo.WeightComparisonCalculationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.weightComparisonCalculation")
	formulaOtherBSAInfo.ComparedWithLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.comparedWith")
	formulaOtherBSAInfo.ChangeLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.change")
	formulaOtherBSAInfo.CalculationLabel = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.calculation")
	formulaOtherBSAFieldList := make([]models.FormulaOtherBSAField, 0)

	// DTP研究产品
	if attribute.AttributeInfo.DtpRule == 1 && drugConfigureSetting.DtpIpList != nil && len(drugConfigureSetting.DtpIpList) > 0 {
		dtpTypeMap := map[int]string{
			1: locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.site"),
			2: locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.siteSubject"),
			3: locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.depotSubject"),
		}

		for _, dtpIp := range drugConfigureSetting.DtpIpList {
			var dtpIpField models.DTPIpField
			dtpIpField.IpName = dtpIp.IP

			if isBlindedRole && isBlindDrugMap[dtpIp.IP] {
				dtpIpField.IpName = tools.BlindData
			} else {
				name := dtpIp.IP
				if isOtherDrugMap[dtpIp.IP] {
					name = "* " + dtpIp.IP
				}
				dtpIpField.IpName = name
			}

			var dtpTypeList []string
			for _, dtpType := range dtpIp.DtpTypeList {
				dtpTypeList = append(dtpTypeList, dtpTypeMap[dtpType])
			}
			dtpIpField.DTPTypeList = dtpTypeList

			dtpIpFieldList = append(dtpIpFieldList, dtpIpField)
		}
	}

	if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
		for _, configure := range drugConfigure.Configures {

			group := ""
			if len(configure.Group) > 0 {
				if isBlindedRole && attribute.AttributeInfo.Blind {
					group = models.BlindData
				} else {
					group = configure.Group
				}
			}
			visitNameList := make([]string, 0)
			if configure.VisitCycles != nil && len(configure.VisitCycles) > 0 {
				for _, cycle := range configure.VisitCycles {
					if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
						for _, info := range visitCycle.Infos {
							if cycle == info.ID {
								visitNameList = append(visitNameList, info.Name)
							}
						}
					}
					if &visitCycle.SetInfo != nil && visitCycle.SetInfo.IsOpen {
						if cycle == visitCycle.SetInfo.Id {
							lang := ctx.Request.Header.Get("Accept-Language")
							if lang == "zh" {
								visitNameList = append(visitNameList, visitCycle.SetInfo.NameZh)
							} else if lang == "en" {
								visitNameList = append(visitNameList, visitCycle.SetInfo.NameEn)
							}
						}
					}
				}
			}

			if configure.OpenSetting == 1 || configure.OpenSetting == 2 {
				//按标签/开放配置
				if configure.Values != nil && len(configure.Values) > 0 {
					for _, value := range configure.Values {
						var labelOpenField models.LabelOpenField
						labelOpenField.Group = group
						labelOpenField.VisitName = strings.Join(visitNameList, ",")
						if isBlindedRole && isBlindDrugMap[value.DrugName] {
							labelOpenField.IpName = tools.BlindData
							labelOpenField.DispensationQuantity = tools.BlindData
							labelOpenField.IpSpecification = tools.BlindData

						} else {
							name := value.DrugName
							if value.IsOther {
								name = "* " + value.DrugName
							}
							labelOpenField.IpName = name
							if len(value.CustomDispensingNumber) > 0 {
								labelOpenField.DispensationQuantity = value.CustomDispensingNumber
							} else {
								labelOpenField.DispensationQuantity = strconv.Itoa(value.DispensingNumber)
							}
							labelOpenField.IpSpecification = value.DrugSpec

						}
						if configure.IsFormula {
							labelOpenField.CustomFormula = configure.CustomerCalculation
							labelOpenField.Specification = configure.CustomerCalculationSpec
						}
						labelList := []string{}

						if value.Label != "" {
							labelList = append(labelList, value.Label)
						}
						if configure.Label != "" {
							labelList = append(labelList, configure.Label)
						}

						labelOpenField.CombinedDispensation = strings.Join(labelList, ",")

						if value.AutomaticRecode {
							labelOpenField.AutomaticAssignment = "√"
							labelOpenField.CalculationUnit = strconv.Itoa(int(*value.AutomaticRecodeSpec))
						} else {
							labelOpenField.AutomaticAssignment = "×"
						}
						labelOpenFieldList = append(labelOpenFieldList, labelOpenField)
					}
				}

			} else if configure.OpenSetting == 3 {
				//按公式计算
				if configure.CalculationType == 1 {
					//年龄
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaAgeField models.FormulaAgeField

							formulaAgeField.Group = group
							formulaAgeField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaAgeField.IpName = tools.BlindData
								formulaAgeField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = "* " + value.DrugName
								}
								formulaAgeField.IpName = name
								formulaAgeField.IpSpecification = value.DrugSpec

							}
							formulaExpressionList := make([]string, 0)
							formulaValueList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
									formulaValueList = append(formulaValueList, strconv.Itoa(formula.Value))
								}
							}
							formulaAgeField.AgeRange = formulaExpressionList
							formulaAgeField.DispensationQuantity = formulaValueList
							if value.IsOpen {
								formulaAgeField.IsOpenIp = "√"
							} else {
								formulaAgeField.IsOpenIp = "×"
							}
							if value.CalculationInfo.KeepDecimal {
								if value.CalculationInfo.Precision != nil {
									formulaAgeField.KeepDecimalPlaces = strconv.Itoa(int(*value.CalculationInfo.Precision))
								}
							}

							formulaAgeFieldList = append(formulaAgeFieldList, formulaAgeField)
						}
					}

				} else if configure.CalculationType == 2 {
					//体重
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaWeightField models.FormulaWeightField
							formulaWeightField.Group = group
							formulaWeightField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaWeightField.IpName = tools.BlindData
								formulaWeightField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = "* " + value.DrugName
								}
								formulaWeightField.IpName = name
								formulaWeightField.IpSpecification = value.DrugSpec
							}
							formulaExpressionList := make([]string, 0)
							formulaValueList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
									formulaValueList = append(formulaValueList, strconv.Itoa(formula.Value))
								}
							}
							formulaWeightField.WeightRange = formulaExpressionList
							formulaWeightField.DispensationQuantity = formulaValueList
							if value.IsOpen {
								formulaWeightField.IsOpenIp = "√"
							} else {
								formulaWeightField.IsOpenIp = "×"
							}
							if value.CalculationInfo.KeepDecimal {
								if value.CalculationInfo.Precision != nil {
									formulaWeightField.KeepDecimalPlaces = strconv.Itoa(int(*value.CalculationInfo.Precision))
								}
							}

							if value.CalculationInfo.ComparisonSwitch {
								if value.CalculationInfo.ComparisonType != nil {
									if *value.CalculationInfo.ComparisonType == 1 {
										formulaWeightField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation")
									} else if *value.CalculationInfo.ComparisonType == 2 {
										formulaWeightField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual")
									} else if *value.CalculationInfo.ComparisonType == 3 {
										formulaWeightField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random")
									}
								}
								change := ""
								if value.CalculationInfo.ComparisonSymbols == 0 {
									change += ">"
								} else if value.CalculationInfo.ComparisonSymbols == 1 {
									change += ">="
								} else if value.CalculationInfo.ComparisonSymbols == 2 {
									change += "<="
								} else if value.CalculationInfo.ComparisonSymbols == 3 {
									change += "<"
								}
								if value.CalculationInfo.ComparisonRatio != nil {
									change = change + strconv.Itoa(int(*value.CalculationInfo.ComparisonRatio)) + "%"
								}
								formulaWeightField.Change = change
								if value.CalculationInfo.CurrentComparisonType != nil {
									if *value.CalculationInfo.CurrentComparisonType == 1 {
										formulaWeightField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation")
									} else if *value.CalculationInfo.CurrentComparisonType == 2 {
										formulaWeightField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual")
									} else if *value.CalculationInfo.CurrentComparisonType == 3 {
										formulaWeightField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random")
									}
								}
							}

							formulaWeightFieldList = append(formulaWeightFieldList, formulaWeightField)
						}
					}

				} else if configure.CalculationType == 3 {
					//简易体表面积BSA
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaSimpleBSAField models.FormulaSimpleBSAField
							formulaSimpleBSAField.Group = group
							formulaSimpleBSAField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaSimpleBSAField.IpName = tools.BlindData
								formulaSimpleBSAField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = "* " + value.DrugName
								}
								formulaSimpleBSAField.IpName = name
								formulaSimpleBSAField.IpSpecification = value.DrugSpec

							}
							formulaExpressionList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
								}
							}
							unit := ""
							if value.CalculationInfo.Specifications.Value != nil {
								unit += strconv.FormatFloat(*value.CalculationInfo.Specifications.Value, 'f', -1, 64)
							}
							if value.CalculationInfo.Specifications.Unit != nil {
								unit += *value.CalculationInfo.Specifications.Unit
							}
							formulaSimpleBSAField.UnitCapacity = unit
							if value.CalculationInfo.UnitCalculationStandard != nil {
								formulaSimpleBSAField.UnitCalculationStandard = strconv.Itoa(int(*value.CalculationInfo.UnitCalculationStandard))
							}
							if value.IsOpen {
								formulaSimpleBSAField.IsOpenIp = "√"
							} else {
								formulaSimpleBSAField.IsOpenIp = "×"
							}

							if value.CalculationInfo.ComparisonSwitch {
								if value.CalculationInfo.ComparisonType != nil {
									if *value.CalculationInfo.ComparisonType == 1 {
										formulaSimpleBSAField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation")
									} else if *value.CalculationInfo.ComparisonType == 2 {
										formulaSimpleBSAField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual")
									} else if *value.CalculationInfo.ComparisonType == 3 {
										formulaSimpleBSAField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random")
									}
								}
								change := ""
								if value.CalculationInfo.ComparisonSymbols == 0 {
									change += ">"
								} else if value.CalculationInfo.ComparisonSymbols == 1 {
									change += ">="
								} else if value.CalculationInfo.ComparisonSymbols == 2 {
									change += "<="
								} else if value.CalculationInfo.ComparisonSymbols == 3 {
									change += "<"
								}
								if value.CalculationInfo.ComparisonRatio != nil {
									change = change + strconv.Itoa(int(*value.CalculationInfo.ComparisonRatio)) + "%"
								}
								formulaSimpleBSAField.Change = change
								if value.CalculationInfo.CurrentComparisonType != nil {
									if *value.CalculationInfo.CurrentComparisonType == 1 {
										formulaSimpleBSAField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation")
									} else if *value.CalculationInfo.CurrentComparisonType == 2 {
										formulaSimpleBSAField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual")
									} else if *value.CalculationInfo.CurrentComparisonType == 3 {
										formulaSimpleBSAField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random")
									}
								}
							}

							formulaSimpleBSAFieldList = append(formulaSimpleBSAFieldList, formulaSimpleBSAField)
						}
					}

				} else if configure.CalculationType == 4 {
					//其他体表面积(按自定义公式)
					formulaOtherBSAInfo.TakeCare = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare1") +
						configure.CustomerCalculation + ";" +
						locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare2")
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaOtherBSAField models.FormulaOtherBSAField
							formulaOtherBSAField.Group = group
							formulaOtherBSAField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaOtherBSAField.IpName = tools.BlindData
								formulaOtherBSAField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = "* " + value.DrugName
								}
								formulaOtherBSAField.IpName = name
								formulaOtherBSAField.IpSpecification = value.DrugSpec

							}
							formulaExpressionList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
								}
							}
							unit := ""
							if value.CalculationInfo.Specifications.Value != nil {
								// 将 float64 转换为 string，保留所有小数位，不补零
								unit += strconv.FormatFloat(*value.CalculationInfo.Specifications.Value, 'f', -1, 64)
							}
							if value.CalculationInfo.Specifications.Unit != nil {
								unit += *value.CalculationInfo.Specifications.Unit
							}
							formulaOtherBSAField.UnitCapacity = unit
							if value.CalculationInfo.UnitCalculationStandard != nil {
								formulaOtherBSAField.UnitCalculationStandard = strconv.Itoa(int(*value.CalculationInfo.UnitCalculationStandard))
							}
							if value.IsOpen {
								formulaOtherBSAField.IsOpenIp = "√"
							} else {
								formulaOtherBSAField.IsOpenIp = "×"
							}

							if value.CalculationInfo.ComparisonSwitch {
								if value.CalculationInfo.ComparisonType != nil {
									if *value.CalculationInfo.ComparisonType == 1 {
										formulaOtherBSAField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation")
									} else if *value.CalculationInfo.ComparisonType == 2 {
										formulaOtherBSAField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual")
									} else if *value.CalculationInfo.ComparisonType == 3 {
										formulaOtherBSAField.ComparedWith = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random")
									}
								}
								change := ""
								if value.CalculationInfo.ComparisonSymbols == 0 {
									change += ">"
								} else if value.CalculationInfo.ComparisonSymbols == 1 {
									change += ">="
								} else if value.CalculationInfo.ComparisonSymbols == 2 {
									change += "<="
								} else if value.CalculationInfo.ComparisonSymbols == 3 {
									change += "<"
								}
								if value.CalculationInfo.ComparisonRatio != nil {
									change = change + strconv.Itoa(int(*value.CalculationInfo.ComparisonRatio)) + "%"
								}
								formulaOtherBSAField.Change = change
								if value.CalculationInfo.CurrentComparisonType != nil {
									if *value.CalculationInfo.CurrentComparisonType == 1 {
										formulaOtherBSAField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation")
									} else if *value.CalculationInfo.CurrentComparisonType == 2 {
										formulaOtherBSAField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual")
									} else if *value.CalculationInfo.CurrentComparisonType == 3 {
										formulaOtherBSAField.Calculation = locales.Tr(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random")
									}
								}
							}

							formulaOtherBSAFieldList = append(formulaOtherBSAFieldList, formulaOtherBSAField)
						}
					}

				}

			}
		}
	}

	dtpIpInfo.DTPIpFieldList = dtpIpFieldList

	labelOpenInfo.LabelOpenFieldList = labelOpenFieldList

	formulaAgeInfo.FormulaAgeFieldList = formulaAgeFieldList
	formulaConfigInfo.FormulaAge = formulaAgeInfo

	formulaWeightInfo.FormulaWeightFieldList = formulaWeightFieldList
	formulaConfigInfo.FormulaWeight = formulaWeightInfo

	formulaSimpleBSAInfo.FormulaSimpleBSAFieldList = formulaSimpleBSAFieldList
	formulaConfigInfo.FormulaSimpleBSA = formulaSimpleBSAInfo

	formulaOtherBSAInfo.FormulaOtherBSAFieldList = formulaOtherBSAFieldList
	formulaConfigInfo.FormulaOtherBSA = formulaOtherBSAInfo

	treatmentDesignInfo.FormulaConfig = formulaConfigInfo

	if len(randomConfigureInfo.Level) > 0 {
		//随机
		if len(formConfigureInfo.Level) > 0 {
			//表单
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".4"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (dtpIpFieldList != nil && len(dtpIpFieldList) > 0) ||
							(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if dtpIpFieldList != nil && len(dtpIpFieldList) > 0 {
								dtpIpInfo.Level = "1."
								if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
									labelOpenInfo.Level = "2."
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "3."
									}
								} else {
									labelOpenInfo.Level = ""
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "2."
									}
								}
							} else {
								dtpIpInfo.Level = ""
								if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
									labelOpenInfo.Level = "1."
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "2."
									}
								} else {
									labelOpenInfo.Level = ""
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "1."
									}
								}
							}

						} else {
							treatmentDesignInfo.Level = ""
						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}

			} else {
				//不发药
				ipManagementInfo.Level = ""
			}

		} else {
			//无表单
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".3"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
								labelOpenInfo.Level = "1."
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "2."
								}
							} else {
								labelOpenInfo.Level = ""
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "1."
								}
							}
						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}
			} else {
				//不发药
				ipManagementInfo.Level = ""
			}
		}

	} else {
		//不随机
		if len(formConfigureInfo.Level) > 0 {
			//表单
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".3"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
								labelOpenInfo.Level = "1."
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "2."
								}
							} else {
								labelOpenInfo.Level = ""
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "1."
								}
							}
						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}

			} else {
				//不发药
				ipManagementInfo.Level = ""
			}

		} else {
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".2"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
								labelOpenInfo.Level = "1."
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "2."
								}
							} else {
								labelOpenInfo.Level = ""
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "1."
								}
							}

						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}
			} else {
				//不发药
				ipManagementInfo.Level = ""
			}
		}

	}

	treatmentDesignInfo.DTPIp = dtpIpInfo
	treatmentDesignInfo.FormulaConfig = formulaConfigInfo
	visitManagementInfo.VisitList = visitDetailsList
	ipManagementInfo.VisitManagement = visitManagementInfo
	treatmentDesignInfo.LabelOpen = labelOpenInfo
	ipManagementInfo.TreatmentDesign = treatmentDesignInfo
	configureDetail.IpManagement = ipManagementInfo

	return configureDetail, nil

}
func TranslateTypeString(ctx *gin.Context, str string) string {
	var result = ""
	if len(str) > 0 {
		if str == "input" {
			//输入框
			result = locales.Tr(ctx, "form.control.type.input")
		} else if str == "inputNumber" {
			//数字输入框
			result = locales.Tr(ctx, "form.control.type.inputNumber")
		} else if str == "textArea" {
			//多行文本框
			result = locales.Tr(ctx, "form.control.type.textArea")
		} else if str == "checkbox" {
			//复选框
			result = locales.Tr(ctx, "form.control.type.checkbox")
		} else if str == "radio" {
			//单选框
			result = locales.Tr(ctx, "form.control.type.radio")
		} else if str == "switch" {
			//开关
			result = locales.Tr(ctx, "form.control.type.switch")
		} else if str == "datePicker" {
			//日期选择框
			result = locales.Tr(ctx, "form.control.type.date")
		} else if str == "timePicker" {
			//时间选择框
			result = locales.Tr(ctx, "form.control.type.dateTime")
		} else if str == "select" {
			//下拉框
			result = locales.Tr(ctx, "form.control.type.select")
		} else if str == "characterLength" {
			//字符长度
			result = locales.Tr(ctx, "form.control.type.format.characterLength")
		} else if str == "numberLength" {
			//数字长度
			result = locales.Tr(ctx, "form.control.type.format.inputNumber.numberLength")
		} else if str == "decimalLength" {
			//小数（整数+小数点+小数）
			result = locales.Tr(ctx, "form.control.type.format.inputNumber.decimalLength")
		} else if str == "checkbox" {
			//多选框
			result = locales.Tr(ctx, "form.control.type.format.checkbox")
		} else if slice.Contain([]string{"YYYY", "YYYY-MM", "MM-YYYY", "MMM-YYYY", "YYYY-MM-DD", "DD-MM-YYYY",
			"MM-DD-YYYY", "DD-MMM-YYYY", "MMM-DD-YYYY"}, str) {
			//日期选择框
			result = str
		} else if slice.Contain([]string{"HH:mm:ss", "HH:mm", "YYYY-MM-DD HH:mm", "YYYY-MM-DD HH:mm:ss",
			"DD-MM-YYYY HH:mm", "DD-MM-YYYY HH:mm:ss", "MM-DD-YYYY HH:mm", "MM-DD-YYYY HH:mm:ss", "DD-MMM-YYYY HH:mm",
			"DD-MMM-YYYY HH:mm:ss", "MMM-DD-YYYY HH:mm", "MMM-DD-YYYY HH:mm:ss"}, str) {
			//时间选择框
			result = str
		}
	}
	return result
}

func GetLogisticsCode(ctx *gin.Context, code string) (models.LogisticsCompanyCode, error) {
	//查询项目下的人员
	var logisticsCompanyCode models.LogisticsCompanyCode
	if len(code) == 0 {
		return logisticsCompanyCode, nil
	}
	// 设置查询条件
	filter := bson.M{"code": code}
	err := tools.Database.Collection("logistics_company_code").FindOne(ctx, filter).Decode(&logisticsCompanyCode)
	if err != nil {
		//logisticsCompanyCode.ID = primitive.NewObjectID()
		//logisticsCompanyCode.Code = "qita"
		//logisticsCompanyCode.Name = "其他"
		//logisticsCompanyCode.Type = "其他物流公司"
		return logisticsCompanyCode, errors.WithStack(err)
	}
	return logisticsCompanyCode, nil
}

func GetLogisCode(ctx *gin.Context, code string) (models.LogisticsCompanyCode, error) {
	//查询项目下的人员
	var logisticsCompanyCode models.LogisticsCompanyCode
	if len(code) == 0 {
		return logisticsCompanyCode, nil
	}
	// 设置查询条件
	filter := bson.M{"code": code}
	err := tools.Database.Collection("logistics_company_code").FindOne(ctx, filter).Decode(&logisticsCompanyCode)
	if err != nil {
		logisticsCompanyCode.ID = primitive.NewObjectID()
		logisticsCompanyCode.Code = "qita"
		if locales.Lang(ctx) == "en" {
			logisticsCompanyCode.Name = "other"
		} else {
			logisticsCompanyCode.Name = "其他"
		}
		logisticsCompanyCode.Type = "其他物流公司"
		return logisticsCompanyCode, errors.WithStack(err)
	}
	return logisticsCompanyCode, nil
}
func getEnvName(project models.Project, envOID primitive.ObjectID) string {
	var envCode string
	for _, env := range project.Environments {
		if env.ID == envOID {
			envCode = env.Name
			break
		}
	}
	return envCode
}

func ExportUserLoginHistory(ctx *gin.Context, projectId string, envId string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envId)
	userProjectEnvs := make([]models.UserProjectEnvironment, 0)
	cursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &userProjectEnvs)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	userIds := make([]primitive.ObjectID, 0)
	userIds = slice.Map(userProjectEnvs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.UserID
	})
	userIds = slice.Unique(userIds)
	users := make([]models.User, 0)
	cursor, err = tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
	if err != nil {
		return "", nil, err
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return "", nil, err
	}
	cloudOIds := make([]primitive.ObjectID, 0)
	cloudOIds = slice.Map(users, func(index int, item models.User) primitive.ObjectID {
		return item.CloudId
	})
	cloudIds := make([]string, 0)
	cloudIds = slice.Map(cloudOIds, func(index int, item primitive.ObjectID) string {
		return item.Hex()
	})
	cloudUsers, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
	if err != nil {
		return "", nil, err
	}
	userMap := make(map[primitive.ObjectID]*models.UserData)
	//取下载用户的时区
	u, err := tools.Me(ctx)
	if err != nil {
		return "", nil, err
	}
	location := "Asia/Shanghai"
	for _, user := range users {
		cloudUserP, ok := slice.Find(cloudUsers, func(index int, item *models.UserData) bool {
			return item.Id == user.CloudId.Hex()
		})
		if ok {
			cloudUser := *cloudUserP
			userMap[user.ID] = cloudUser
			if u.CloudId.Hex() == cloudUser.Id {
				if len(cloudUser.Settings.Tz) > 0 {
					location = cloudUser.Settings.Tz
				}
			}
		}

	}
	loginHistories := make([]models.LoginHistory, 0)
	cursor, err = tools.Database.Collection(models.LoginHistoryTable).Find(nil, bson.M{"user_id": bson.M{"$in": userIds}})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &loginHistories)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = slice.SortByField(loginHistories, "Time", "desc")
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return "", nil, err
	}
	title := []interface{}{
		locales.Tr(ctx, "report.attributes.project.number"),
		locales.Tr(ctx, "report.attributes.project.name"),
		locales.Tr(ctx, "export.user.name"),
		locales.Tr(ctx, "export.user.email"),
		"IP",
		locales.Tr(ctx, "report.user.login.time"),
		locales.Tr(ctx, "report.user.login.success"),
	}
	content := make([][]interface{}, 0)
	//使用cloud查询个人时区
	for _, history := range loginHistories {
		success := "Yes"
		if history.Success == 0 {
			success = "No"
		}

		timeStr, err := tools.GetLocationUtc(location, history.Time.Nanoseconds())
		if err != nil {
			panic(err)
		}

		name := ""
		email := ""
		if userMap[history.UserId] != nil {
			name = userMap[history.UserId].Info.Name
			email = userMap[history.UserId].Info.Email
		}
		contentItem := []interface{}{
			project.Number,
			project.Name,
			name,
			email,
			history.IP,
			timeStr,
			success,
		}
		content = append(content, contentItem)
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	fileName := fmt.Sprintf("%s[%s]UserRoleHistory_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))
	return fileName, buffer.Bytes(), nil
}

// 项目构建 项目设置
func ExportReportTypeProjectConstructionExport(ctx *gin.Context, projectId string, envId string, roleId string, now time.Time, startTime int, endTime int, exportType string) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envId)
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	// timeZone, err := tools.GetTimeZone(projectOID)

	user, err := tools.Me(ctx)
	if err != nil {
		return "", nil, err
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	var Oids []primitive.ObjectID
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return "", nil, err
	}
	if exportType == "1" {
		if project.ProjectInfo.Type == 1 {
			Oids = append(Oids, envOID)
		} else {
			environments := project.Environments
			for _, environment := range environments {
				if environment.ID == envOID {
					cohorts := environment.Cohorts
					for _, cohort := range cohorts {
						Oids = append(Oids, cohort.ID)
					}
				}
			}
			Oids = append(Oids, envOID)
		}
	} else {
		Oids = append(Oids, projectOID)
	}

	type OperationLog struct {
		ID       primitive.ObjectID              `json:"id" bson:"_id"`
		Operator primitive.ObjectID              `json:"operator" bson:"operator"`
		OID      primitive.ObjectID              `bson:"oid" json:"oid"`
		Time     time.Duration                   `json:"time" bson:"time"`
		Type     int                             `json:"type" bson:"type"` //1新增 2编辑 3删除  5编辑	6复制 Module   string                   `json:"module" bson:"module"`
		Module   string                          `json:"module" bson:"module"`
		Fields   []models.OperationLogFieldGroup `json:"fields" bson:"fields"`
		Mark     []models.Mark                   `json:"mark" bson:"mark"`
		User     models.User                     `json:"user" bson:"user"`
	}
	var operationLogList []OperationLog
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.D{{Key: "oid", Value: bson.M{"$in": Oids}}}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "operator",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{"$unwind", "$user"}},
		{{"$sort", bson.D{{"time", -1}}}},
	}
	if startTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$gt": startTime}}}})
	}
	if endTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$lt": endTime}}}})
	}
	modules := []string{
		"operation_log.module.barcode",
		"operation_log.module.barcode_add",
		"operation_log.module.supply",
		"operation_log.module.supply_detail",
		"operation_log.module.random_design",
		"operation_log.module.project_site",
		"operation_log.module.project_storehouse",
		"operation_log.module.project_storehouse_medicine_alert",
		"operation_log.module.visitCycle",
		"operation_log.module.visitSetting",
		"operation_log.module.attribute",
		"operation_log.module.drug_configure",
		"operation_log.module.barcoed_add",
		"operation_log.module.medicinesList_uploadMedicine",
		"operation_log.module.medicinesList_uploadPacklist",
		"operation_log.module.medicinesList_delete",
		"operation_log.module.updateBatch",
		"operation_log.module.otherMedicines",
		"operation_log.module.form",
		"operation_log.module.simulate_random",
		"operation_log.module.push",
		"operation_log.module.region",
		"operation_log.module.notifications",
		"operation_log.module.configure_export",
		"operation_log.module.project_basic_information",
		"operation_log.module.subjects",
		"operation_log.module.drug_configure_setting",
	}

	if exportType == "2" {
		modules = []string{
			"operation_log.module.project_information",
			"operation_log.module.project_env",
			"operation_log.module.project_function",
			"operation_log.module.project_docking",
			"operation_log.module.project_custom",
			"operation_log.module.project_permission",
			"operation_log.module.project_notice",
		}

	}
	pipeline = append(pipeline, bson.D{{"$match", bson.M{"module": bson.M{"$in": modules}}}})

	cursor, err := tools.Database.Collection("operation_log").Aggregate(nil, pipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &operationLogList)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	ctx.Set("isBlind", false)
	ctx.Set("isRoomBlind", true)

	isBlind, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	isRoomBlind, isRoomErr := tools.IsBlindedRoomRole(roleId)
	if isRoomErr != nil {
		return "", nil, errors.WithStack(err)
	}
	ctx.Set("isRoomBlind", isRoomBlind)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	ctx.Set("isBlindedRole", isBlindedRole)

	//查询子组别
	_, sub := slice.Find(operationLogList, func(index int, item OperationLog) bool {
		_, b := slice.Find(item.Fields, func(index int, it models.OperationLogFieldGroup) bool {
			return slice.Contain([]string{
				"operation_log.drug_configure.subGroup",
				"operation_log.visitCycle.group",
				"operation_log.random_design.group_name",
			}, it.TranKey)
		})
		return b
	})
	if sub {
		filter := bson.M{"env_id": envOID}
		// if !cohortOId.IsZero() {
		// 	filter["cohort_id"] = cohortOId
		// }
		var randomDesigns []models.RandomDesign
		cursor, err := tools.Database.Collection("random_design").Find(nil, filter)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &randomDesigns)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		subBlinds := make(map[primitive.ObjectID][]models.GroupBlind)
		for _, randomDesign := range randomDesigns {
			blinds := make([]models.GroupBlind, 0)
			for _, group := range randomDesign.Info.Groups {
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					for _, subGroup := range group.SubGroup {
						blinds = append(blinds, models.GroupBlind{
							Group:    group.Name + " " + subGroup.Name,
							ParName:  group.Name,
							SubName:  subGroup.Name,
							SubBlind: subGroup.Blind,
						})
					}
				}
			}
			if project.Type != 1 {
				subBlinds[randomDesign.CohortID] = blinds
			} else {
				subBlinds[randomDesign.EnvironmentID] = blinds
			}
		}

		ctx.Set("subGroupBlind", subBlinds)
	}

	//db.xxxx.find({"ct":{"$gte":ISODate("2017-04-20T01:16:33.303Z"),"$lte":ISODate("2018-12-05T01:16:33.303Z")}})  //某个时间段

	err = slice.SortByField(operationLogList, "Time", "desc")
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	title := []interface{}{
		locales.Tr(ctx, "report.attributes.project.number"),
		locales.Tr(ctx, "report.attributes.project.name"),
		locales.Tr(ctx, "menu.settings.users"),
		locales.Tr(ctx, "report.user.login.time"),
		locales.Tr(ctx, "report.audit.trail.operation.name"),
		locales.Tr(ctx, "report.audit.trail.operation.way"),
		locales.Tr(ctx, "report.user.role.assign.content"),
	}
	content := make([][]interface{}, 0)
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	for _, operationLog := range operationLogList {
		context := make([]string, 0)
		roleName := ""
		secondaryMenuName := ""
		if exportType == "1" {
			context = append(context, locales.Tr(ctx, operationLog.Module))
			for _, mark := range operationLog.Mark {
				if mark.Label != "" {
					value := mark.Value
					//if mark.Blind {
					//	value = tools.BlindData
					//}
					if operationLog.Module != "operation_log.module.subjects" {
						context = append(context, locales.Tr(ctx, mark.Label)+":"+value)
					}

				}
			}
			b := false

			if operationLog.Fields != nil && len(operationLog.Fields) > 0 {
				_, b = slice.Find(operationLog.Fields, func(index int, item models.OperationLogFieldGroup) bool {
					return item.TranKey == "operation_log.project_storehouse.onlyID" || item.TranKey == "operation_log.drug_configure.onlyID" ||
						item.TranKey == "operation_log.uploadMedicines.onlyID" ||
						//item.TranKey == "operation_log.random_list.onlyID" ||
						item.TranKey == "operation_log.simulateRandom.onlyID"
				})
			}
			if !b {
				for _, mark := range operationLog.Mark {
					if mark.Label != "" {
						value := mark.Value
						if mark.Label == "operation_log.label.otherMedicines" ||
							mark.Label == "operation_log.label.uploadMedicine" ||
							mark.Label == "operation_log.label.medicine" ||
							mark.Label == "operation_log.project_storehouse.medicine_name" {
							if isBlindDrugMap[value] && isBlindedRole {
								value = tools.BlindData
							}
						} else {
							if mark.Blind && isBlind {
								value = tools.BlindData
							}
						}
						if operationLog.Module != "operation_log.module.subjects" {
							context = append(context, locales.Tr(ctx, mark.Label)+" : "+value)
						}
					}
				}
			}

		}

		lang := locales.Lang(ctx)
		for _, field := range operationLog.Fields {
			var tmp string
			labelName := ""
			oldValue := ""
			newValue := ""
			secondaryMenuName = locales.Tr(ctx, field.Key)
			labelName = locales.Tr(ctx, field.TranKey)
			if operationLog.Module == "operation_log.module.project_permission" && field.TranKey == "operation_log.project.roleName" {
				roleName = field.New.Value.(string)
			}
			if field.TranKey == "operation_log.project.addRolePermissionSelect" || field.TranKey == "operation_log.project.cancelRolePermissionSelect" {
				oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
				if field.New.Value != nil && field.New.Type == 5 {
					newValueArray := []string{}
					v := field.New.Value.(primitive.A)
					for _, item := range v {
						newValueArray = append(newValueArray, item.(string))
					}
					positionMap, reverseIndex := reverseIndex(project.ProjectInfo.ResearchAttribute)
					permission2Menu := make(map[string]string)
					for permission, menu := range reverseIndex {
						permission2Menu[permission] = strings.Join(menu, " > ")
					}
					menu := make(map[string][]string)
					for _, p := range newValueArray {
						if _, ok := permission2Menu[p]; ok {
							if _, ok := menu[permission2Menu[p]]; !ok {
								menu[permission2Menu[p]] = make([]string, 0)
							}
							menu[permission2Menu[p]] = append(menu[permission2Menu[p]], p)
						}
					}
					// change menus from map[string][]string to [][]string
					tmpM := make([][]string, len(positionMap))
					for _, permissions := range menu {
						if menuCascade, ok := reverseIndex[permissions[0]]; ok {
							lowestMenu := menuCascade[len(menuCascade)-1]
							index := positionMap[lowestMenu]
							tmpM[index] = permissions
						}
					}
					// compact the sparse array m to a dense array menu
					m := make([][]string, len(menu))
					mIndex := 0
					for _, item := range tmpM {
						if len(item) != 0 {
							m[mIndex] = item
							mIndex++
						}
					}
					rows := make([]string, 0)

					// 其他行
					for _, permissions := range m {
						// 层叠菜单
						menuCascade := make([]string, 0)
						for _, code := range reverseIndex[permissions[0]] {
							menuCascade = append(menuCascade, locales.Tr(ctx, code))
						}
						// 权限
						p := make([]string, 0)
						for _, permission := range permissions {
							p = append(p, locales.Tr(ctx, permission))
						}
						rows = append(rows, fmt.Sprintf("%s:%s;", strings.Join(menuCascade, "-"), strings.Join(p, "、")))
					}
					newValue = strings.Join(rows, "")
				}
			} else {
				if field.Old.Type != 3 {
					if field.Old.Type == 16 {
						oldValue = converRoles(ctx, field.Old.Value)
					} else if field.Old.Type == 17 || field.Old.Type == 18 ||
						field.Old.Type == 19 || field.Old.Type == 20 || field.Old.Type == 22 || field.Old.Type == 23 || field.Old.Type == 24 || field.Old.Type == 25 {
						oldValue = converContentConfiguration(ctx, field.Old.Type, field.Old.Value)
					} else if field.Old.Type == 21 {
						value := field.Old.Value
						oldValue = strconv.Itoa(int(value.(int32)))
					} else if field.Old.Type == 30 {
						if lang == "zh" {
							oldValue = models.TypeHandle(ctx, 7, field.Old.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
						} else {
							oldValue = models.TypeHandle(ctx, 7, field.Old.ENValue, isBlindDrugMap, operationLog.OID, field.TranKey)
						}
					} else {
						if field.TranKey == "operation_log.form.format" {
							if field.Old.Value == "form.control.type.format.numberLength" {
								oldValue = locales.Tr(ctx, "form.control.type.format.numberLength")
							} else if field.Old.Value == "form.control.type.format.decimalLength" {
								oldValue = locales.Tr(ctx, "form.control.type.format.decimalLength")
							} else {
								oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
							}
						} else {
							if field.TranKey == "operation_log.form.format" {
								if field.Old.Value == "form.control.type.format.numberLength" {
									oldValue = locales.Tr(ctx, "form.control.type.format.numberLength")
								} else if field.Old.Value == "form.control.type.format.decimalLength" {
									oldValue = locales.Tr(ctx, "form.control.type.format.decimalLength")
								} else {
									oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
								}
							} else if field.TranKey == "operation_log.form.options" {
								oldValue = converOptionValue(ctx, field.Old.Value)
							} else if field.TranKey == "operation_log.drug_configure_setting.visitJudgment" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentName" {
								oldValue = converDefaultValue(ctx, field.Old.Value)
							} else if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
								oldValue = models.TypeHandle(ctx, 7, field.Old.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
							} else {
								oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
							}
						}
					}
				} else {
					oldValue = converCountry(ctx, field.Old.Value)
				}
				if field.New.Type != 3 {
					if field.New.Type == 16 {
						newValue = converRoles(ctx, field.New.Value)
					} else if field.New.Type == 17 || field.New.Type == 18 ||
						field.New.Type == 19 || field.New.Type == 20 || field.New.Type == 22 || field.New.Type == 23 || field.New.Type == 24 || field.New.Type == 25 {
						newValue = converContentConfiguration(ctx, field.New.Type, field.New.Value)
					} else if field.New.Type == 21 {
						value := field.New.Value
						newValue = strconv.Itoa(int(value.(int32)))
					} else if field.New.Type == 30 {
						if lang == "zh" {
							newValue = models.TypeHandle(ctx, 7, field.New.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
						} else {
							newValue = models.TypeHandle(ctx, 7, field.New.ENValue, isBlindDrugMap, operationLog.OID, field.TranKey)
						}
					} else {
						if field.TranKey == "operation_log.form.format" {
							if field.New.Value == "form.control.type.format.numberLength" {
								newValue = locales.Tr(ctx, "form.control.type.format.numberLength")
							} else if field.New.Value == "form.control.type.format.decimalLength" {
								newValue = locales.Tr(ctx, "form.control.type.format.decimalLength")
							} else {
								newValue = models.TypeHandle(ctx, field.New.Type, field.New.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
							}
						} else if field.TranKey == "operation_log.form.options" {
							newValue = converOptionValue(ctx, field.New.Value)
						} else if field.TranKey == "operation_log.drug_configure_setting.visitJudgment" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentName" {
							newValue = converDefaultValue(ctx, field.New.Value)
						} else if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
							newValue = models.TypeHandle(ctx, 7, field.New.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
						} else {
							newValue = models.TypeHandle(ctx, field.New.Type, field.New.Value, isBlindDrugMap, operationLog.OID, field.TranKey)
						}
					}
				} else {
					newValue = converCountry(ctx, field.New.Value)
				}
			}

			if operationLog.Type != 1 && oldValue == newValue && field.Old.Type != 7 {
				// 值未改变的 不需要展示
				continue
			}
			if operationLog.Type == 1 {
				tmp = labelName + ":" + newValue
				if operationLog.Module == "operation_log.module.project_notice" {
					if oldValue != "" {
						tmp = labelName + ":" + oldValue
					}
					if newValue != "" {
						tmp = labelName + ":" + newValue
					}
				}
				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.DecryptAes(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", encryptedData)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}

				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}

				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}

			} else if operationLog.Type == 2 {
				if operationLog.Module == "operation_log.module.notifications" {
					moduleName := locales.Tr(ctx, operationLog.Module)
					context[0] = moduleName + "-" + secondaryMenuName
				}
				tmp = labelName + ":" + oldValue + " -> " + newValue
				if operationLog.Module == "operation_log.module.project_env" && (field.TranKey == "operation_log.project.envName" || field.TranKey == "operation_log.project.stage" || field.TranKey == "operation_log.project.cohort") {
					tmp = labelName + ":" + newValue
				}
				if field.TranKey == "operation_log.project.edcSupplier" {
					if oldValue == "1" {
						oldValue = "Clinflash  EDC"
					} else if oldValue == "2" {
						oldValue = "Medidata Rave  EDC"
					} else {
						oldValue = ""
					}
					if newValue == "1" {
						newValue = "Clinflash  EDC"
					} else if newValue == "2" {
						newValue = "Medidata Rave  EDC"
					} else {
						newValue = ""
					}
					tmp = labelName + ":" + oldValue + " -> " + newValue
				}
				if operationLog.Module == "operation_log.module.project_basic_information" {
					if oldValue == "1" {
						tr := locales.Tr(ctx, "projectSettings.basicInformation.projectProperties.projectType.basicStudy")
						if newValue == "2" {
							tmp = labelName + ":" + tr + " -> " + locales.Tr(ctx, "projectSettings.basicInformation.projectProperties.projectType.cohortStudy")
						} else if newValue == "3" {
							tmp = labelName + ":" + tr + " -> " + locales.Tr(ctx, "projectSettings.basicInformation.projectProperties.projectType.reRandomizationStudy")
						}
					}
				}
				if field.TranKey == "operation_log.project.visitRandomization" {
					randomization, _ := RaveVisitRandomization(ctx, field.Old.Value, field.New.Value)
					if len(randomization) != 0 {
						tmp = randomization
					} else {
						tmp = ""
					}
				}

				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.DecryptAes(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", encryptedData)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}

				if operationLog.Module == "operation_log.module.project_notice" {
					if oldValue != "" {
						tmp = labelName + ":" + oldValue
					}
					if newValue != "" {
						tmp = labelName + ":" + newValue
					}
				}

				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}

				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}

			} else if operationLog.Type == 3 {
				tmp = labelName + ":" + oldValue
				if operationLog.Module == "operation_log.module.project_notice" {
					if oldValue != "" {
						tmp = labelName + ":" + oldValue
					}
					if newValue != "" {
						tmp = labelName + ":" + newValue
					}
				}
				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.DecryptAes(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", encryptedData)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}
				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}
				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}
			} else if operationLog.Type == 4 {
				tmp = labelName
			} else if operationLog.Type == 5 {
				tmp = roleName + labelName + ":" + newValue
			} else if operationLog.Type == 6 {
				tmp = labelName + ":" + newValue
			} else if operationLog.Type == 10 {
				tmp = labelName + " : " + newValue
				if operationLog.Module == "operation_log.module.notifications" {
					moduleName := locales.Tr(ctx, operationLog.Module)
					context[0] = moduleName + "-" + secondaryMenuName
				}
				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.DecryptAes(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", encryptedData)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}
				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}
				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}
			} else if operationLog.Type == 11 {
				tmp = labelName + " : " + newValue
				if operationLog.Module == "operation_log.module.notifications" {
					moduleName := locales.Tr(ctx, operationLog.Module)
					context[0] = moduleName + "-" + secondaryMenuName
				}
				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}
				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}
			} else if operationLog.Type == 12 {
				tmp = labelName + " : " + newValue
			}

			//else if operationLog.Type == 11 {
			//	tmp = labelName + ":" + oldValue + " -> " + newValue
			//}

			if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
				field.TranKey == "operation_log.uploadMedicines.onlyID" ||
				//field.TranKey == "operation_log.random_list.onlyID" ||
				field.TranKey == "operation_log.simulateRandom.onlyID" {

				context = append(context, tmp)

				for _, mark := range operationLog.Mark {
					if mark.Label != "" {
						value := mark.Value
						if mark.Label == "operation_log.label.otherMedicines" ||
							mark.Label == "operation_log.label.uploadMedicine" ||
							mark.Label == "operation_log.label.medicine" ||
							mark.Label == "operation_log.project_storehouse.medicine_name" {
							if isBlindDrugMap[value] && isBlindedRole {
								value = tools.BlindData
							}
						} else {
							if mark.Blind && isBlind {
								value = tools.BlindData
							}
						}
						if operationLog.Module != "operation_log.module.subjects" {
							context = append(context, locales.Tr(ctx, mark.Label)+" : "+value)
						}
					}
				}
			} else {
				context = append(context, tmp)
			}

		}
		if operationLog.Module == "operation_log.module.project_permission" {
			fmt.Sprintf("%s", locales.Tr(ctx, operationLog.Module))
		}
		if len(context) == 1 {
			if context[0] == locales.Tr(ctx, operationLog.Module) {
				if operationLog.Module != "operation_log.module.configure_export" {
					continue
				}
			}
		} else if context == nil || len(context) == 0 {
			continue
		}

		location := "Asia/Shanghai"
		if len(users) > 0 && users[0].Settings.Tz != "" {
			location = users[0].Settings.Tz
		}
		timeStr, err := tools.GetLocationUtc(location, int64(operationLog.Time))
		if err != nil {
			panic(err)
		}

		if len(context) == 2 && operationLog.Type == 10 {
			continue
		}

		contentItem := []interface{}{
			project.Number,
			project.Name,
			fmt.Sprintf("%s(%d)", operationLog.User.Name, operationLog.User.Unicode),
			//time.Unix(operationLog.Time.Nanoseconds(), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05"),
			//time.Unix(operationLog.Time.Nanoseconds(), 0).UTC().Format("2006-01-02 15:04:05"),
			timeStr,
			locales.Tr(ctx, operationLog.Module),
			locales.Tr(ctx, models.OperationLogMap["operation_type"].(bson.M)[convertor.ToString(operationLog.Type)].(string)),
			context,
		}
		content = append(content, contentItem)
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	fileName := "ProjectDesign.xlsx"
	if exportType == "2" {
		fileName = "ProjectSettings.xlsx"
	}
	return fileName, buffer.Bytes(), nil
}

// 处理rave的EDC映射
// 处理rave的EDC映射
func RaveVisitRandomization(ctx *gin.Context, oldValue interface{}, newValue interface{}) (string, error) {
	// 假设 data 是 interface{} 类型的变量
	//old
	oldVisitRandomization, err := convertVisitRandomization(ctx, oldValue)
	if err != nil {
		return "", errors.WithStack(err)
	}
	//new
	newVisitRandomization, err := convertVisitRandomization(ctx, newValue)
	if err != nil {
		return "", errors.WithStack(err)
	}

	tmpList := make([]string, 0)

	//EDC映射

	//访视映射
	oldVisitMap := make(map[string]models.Visit)
	if oldVisitRandomization.Visits != nil && len(oldVisitRandomization.Visits) > 0 {
		for _, obj := range oldVisitRandomization.Visits {
			id := obj.Name
			if len(obj.Name) == 0 {
				id = "id"
			}
			oldVisitMap[id] = obj
		}
	}
	newVisitMap := make(map[string]models.Visit)
	if newVisitRandomization.Visits != nil && len(newVisitRandomization.Visits) > 0 {
		for _, obj := range newVisitRandomization.Visits {
			id := obj.Name
			if len(obj.Name) == 0 {
				id = "id"
			}
			newVisitMap[id] = obj
		}
	}

	// 找出新增的元素
	for id, newObj := range newVisitMap {
		if _, ok := oldVisitMap[id]; !ok {
			fmt.Printf("新增对象: %v\n", newObj)
			if newObj.VisitConfigs != nil && len(newObj.VisitConfigs) > 0 {
				if len(newObj.Name) > 0 {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+"/"+newObj.Name)
				} else {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+" ")
				}
				for _, newVisitConfig := range newObj.VisitConfigs {
					//IRT访视编号/EDC OID
					//文件夹OID
					if len(newVisitConfig.FolderOid) > 0 {
						tmpList = append(tmpList, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+" -> "+newVisitConfig.FolderOid)
					}
					//表单OID
					if len(newVisitConfig.FormOid) > 0 {
						tmpList = append(tmpList, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+" -> "+newVisitConfig.FormOid)
					}
					//研究产品编号OID
					if len(newVisitConfig.IpNumberOid) > 0 {
						tmpList = append(tmpList, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.ipNumberOid")+":"+" -> "+newVisitConfig.IpNumberOid)
					}
					//发放时间OID
					if len(newVisitConfig.DispenseTimeOid) > 0 {
						tmpList = append(tmpList, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.dispenseTimeOid")+":"+" -> "+newVisitConfig.DispenseTimeOid)
					}
				}
			}
		}
	}

	// 找出删除的元素
	for id, oldObj := range oldVisitMap {
		if _, ok := newVisitMap[id]; !ok {
			fmt.Printf("删除对象: %v\n", oldObj)
			if oldObj.VisitConfigs != nil && len(oldObj.VisitConfigs) > 0 {
				if len(oldObj.Name) > 0 {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+"/"+oldObj.Name)
				} else {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+" ")
				}
				for _, oldVisitConfig := range oldObj.VisitConfigs {
					//IRT访视编号/EDC OID
					//文件夹OID
					if len(oldVisitConfig.FolderOid) > 0 {
						tmpList = append(tmpList, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+oldVisitConfig.FolderOid+" -> ")
					}
					//表单OID
					if len(oldVisitConfig.FormOid) > 0 {
						tmpList = append(tmpList, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+oldVisitConfig.FormOid+" -> ")
					}
					//研究产品编号OID
					if len(oldVisitConfig.IpNumberOid) > 0 {
						tmpList = append(tmpList, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.ipNumberOid")+":"+oldVisitConfig.IpNumberOid+" -> ")
					}
					//发放时间OID
					if len(oldVisitConfig.DispenseTimeOid) > 0 {
						tmpList = append(tmpList, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.dispenseTimeOid")+":"+oldVisitConfig.DispenseTimeOid+" -> ")
					}
				}
			}
		}
	}

	// 找出修改的元素
	for id, newObj := range newVisitMap {
		if oldObj, ok := oldVisitMap[id]; ok {
			// 创建两个map，用于快速查找
			oldVisitConfigMap := make(map[string]models.VisitConfig)
			if oldObj.VisitConfigs != nil && len(oldObj.VisitConfigs) > 0 {
				for _, obj := range oldObj.VisitConfigs {
					oldVisitConfigMap[obj.VisitCode] = obj
				}
			}
			newVisitConfigMap := make(map[string]models.VisitConfig)
			if newObj.VisitConfigs != nil && len(newObj.VisitConfigs) > 0 {
				for _, obj := range newObj.VisitConfigs {
					newVisitConfigMap[obj.VisitCode] = obj
				}
			}
			list := make([]string, 0)
			// 找出新增的元素
			for key, newVisitConfig := range newVisitConfigMap {
				if _, ky := oldVisitConfigMap[key]; !ky {
					fmt.Printf("新增对象: %v\n", newVisitConfig)
					//IRT访视编号/EDC OID
					//文件夹OID
					if len(newVisitConfig.FolderOid) > 0 {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+" -> "+newVisitConfig.FolderOid)
					}
					//表单OID
					if len(newVisitConfig.FormOid) > 0 {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+" -> "+newVisitConfig.FormOid)
					}
					//研究产品编号OID
					if len(newVisitConfig.IpNumberOid) > 0 {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.ipNumberOid")+":"+" -> "+newVisitConfig.IpNumberOid)
					}
					//发放时间OID
					if len(newVisitConfig.DispenseTimeOid) > 0 {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.dispenseTimeOid")+":"+" -> "+newVisitConfig.DispenseTimeOid)
					}
				}
			}

			//找出删除的元素
			for key, oldVisitConfig := range oldVisitConfigMap {
				if _, ky := newVisitConfigMap[key]; !ky {
					fmt.Printf("删除对象: %v\n", oldVisitConfig)
					//IRT访视编号/EDC OID
					//文件夹OID
					if len(oldVisitConfig.FolderOid) > 0 {
						list = append(list, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+oldVisitConfig.FolderOid+" -> ")
					}
					//表单OID
					if len(oldVisitConfig.FormOid) > 0 {
						list = append(list, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+oldVisitConfig.FormOid+" -> ")
					}
					//研究产品编号OID
					if len(oldVisitConfig.IpNumberOid) > 0 {
						list = append(list, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.ipNumberOid")+":"+oldVisitConfig.IpNumberOid+" -> ")
					}
					//发放时间OID
					if len(oldVisitConfig.DispenseTimeOid) > 0 {
						list = append(list, oldVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.dispenseTimeOid")+":"+oldVisitConfig.DispenseTimeOid+" -> ")
					}
				}
			}

			//找出修改的元素
			for key, newVisitConfig := range newVisitConfigMap {
				if _, ky := oldVisitConfigMap[key]; ky {
					//IRT访视编号/EDC OID
					//文件夹OID
					oldFolderOid := ""
					newFolderOid := ""
					if len(oldVisitConfigMap[key].FolderOid) > 0 {
						oldFolderOid = oldVisitConfigMap[key].FolderOid
					}
					if len(newVisitConfig.FolderOid) > 0 {
						newFolderOid = newVisitConfig.FolderOid
					}
					if oldFolderOid != newFolderOid {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+oldFolderOid+" -> "+newFolderOid)
					}

					//表单OID
					oldFormOid := ""
					newFormOid := ""
					if len(oldVisitConfigMap[key].FormOid) > 0 {
						oldFormOid = oldVisitConfigMap[key].FormOid
					}
					if len(newVisitConfig.FormOid) > 0 {
						newFormOid = newVisitConfig.FormOid
					}
					if oldFormOid != newFormOid {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+oldFormOid+" -> "+newFormOid)
					}
					//研究产品编号OID
					oldIpNumberOid := ""
					newIpNumberOid := ""
					if len(oldVisitConfigMap[key].IpNumberOid) > 0 {
						oldIpNumberOid = oldVisitConfigMap[key].IpNumberOid
					}
					if len(newVisitConfig.IpNumberOid) > 0 {
						newIpNumberOid = newVisitConfig.IpNumberOid
					}
					if oldIpNumberOid != newIpNumberOid {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.ipNumberOid")+":"+oldIpNumberOid+" -> "+newIpNumberOid)
					}
					//发放时间OID
					oldDispenseTimeOid := ""
					newDispenseTimeOid := ""
					if len(oldVisitConfigMap[key].DispenseTimeOid) > 0 {
						oldDispenseTimeOid = oldVisitConfigMap[key].DispenseTimeOid
					}
					if len(newVisitConfig.DispenseTimeOid) > 0 {
						newDispenseTimeOid = newVisitConfig.DispenseTimeOid
					}
					if oldDispenseTimeOid != newDispenseTimeOid {
						list = append(list, newVisitConfig.VisitCode+"/"+locales.Tr(ctx, "operation_log.project.dispenseTimeOid")+":"+oldDispenseTimeOid+" -> "+newDispenseTimeOid)
					}
				}
			}

			if list != nil && len(list) > 0 {
				if len(oldObj.Name) > 0 {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+"/"+oldObj.Name)
				} else {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+" ")
				}
				for _, s := range list {
					tmpList = append(tmpList, s)
				}
			}

		}
	}

	//随机映射
	oldRandomizationMap := make(map[string]models.Randomization)
	if oldVisitRandomization.Randomizations != nil && len(oldVisitRandomization.Randomizations) > 0 {
		for _, obj := range oldVisitRandomization.Randomizations {
			id := obj.Name
			if len(obj.Name) == 0 {
				id = "id"
			}
			oldRandomizationMap[id] = obj
		}
	}
	newRandomizationMap := make(map[string]models.Randomization)
	if newVisitRandomization.Randomizations != nil && len(newVisitRandomization.Randomizations) > 0 {
		for _, obj := range newVisitRandomization.Randomizations {
			id := obj.Name
			if len(obj.Name) == 0 {
				id = "id"
			}
			newRandomizationMap[id] = obj
		}
	}

	// 找出新增的元素
	for id, newObj := range newRandomizationMap {
		if _, ok := oldRandomizationMap[id]; !ok {
			fmt.Printf("新增对象: %v\n", newObj)
			if newObj.RandomizationConfigs != nil && len(newObj.RandomizationConfigs) > 0 {
				if len(newObj.Name) > 0 {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+"/"+newObj.Name)
				} else {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+" ")
				}
				for _, newRandomizationConfig := range newObj.RandomizationConfigs {
					//IRT随机字段/EDC OID
					//文件夹OID
					if len(newRandomizationConfig.FolderOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+" -> "+newRandomizationConfig.FolderOid)
					}
					//表单OID
					if len(newRandomizationConfig.FormOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+" -> "+newRandomizationConfig.FormOid)
					}
					//字段OID
					if len(newRandomizationConfig.FieldOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.fieldOid")+":"+" -> "+newRandomizationConfig.FieldOid)
					}
				}
			}
		}
	}

	// 找出删除的元素
	for id, oldObj := range oldRandomizationMap {
		if _, ok := newRandomizationMap[id]; !ok {
			fmt.Printf("删除对象: %v\n", oldObj)
			if oldObj.RandomizationConfigs != nil && len(oldObj.RandomizationConfigs) > 0 {
				if len(oldObj.Name) > 0 {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+"/"+oldObj.Name)
				} else {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+" ")
				}
				for _, oldRandomizationConfig := range oldObj.RandomizationConfigs {
					//IRT访视编号/EDC OID
					//文件夹OID
					if len(oldRandomizationConfig.FolderOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+oldRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+oldRandomizationConfig.FolderOid+" -> ")
					}
					//表单OID
					if len(oldRandomizationConfig.FormOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+oldRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+oldRandomizationConfig.FormOid+" -> ")
					}
					//发放时间OID
					if len(oldRandomizationConfig.FieldOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+oldRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.fieldOid")+":"+oldRandomizationConfig.FieldOid+" -> ")
					}
				}
			}
		}
	}

	// 找出修改的元素
	for id, newObj := range newRandomizationMap {
		if oldObj, ok := oldRandomizationMap[id]; ok {
			// 创建两个map，用于快速查找
			oldRandomizationConfigMap := make(map[string]models.RandomizationConfig)
			if oldObj.RandomizationConfigs != nil && len(oldObj.RandomizationConfigs) > 0 {
				for _, obj := range oldObj.RandomizationConfigs {
					oldRandomizationConfigMap[obj.RandomizationField] = obj
				}
			}
			newRandomizationConfigMap := make(map[string]models.RandomizationConfig)
			if newObj.RandomizationConfigs != nil && len(newObj.RandomizationConfigs) > 0 {
				for _, obj := range newObj.RandomizationConfigs {
					newRandomizationConfigMap[obj.RandomizationField] = obj
				}
			}
			list := make([]string, 0)
			// 找出新增的元素
			for key, newRandomizationConfig := range newRandomizationConfigMap {
				if _, ky := oldRandomizationConfigMap[key]; !ky {
					fmt.Printf("新增对象: %v\n", newRandomizationConfig)
					//IRT随机字段/EDC OID
					//文件夹OID
					if len(newRandomizationConfig.FolderOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+" -> "+newRandomizationConfig.FolderOid)
					}
					//表单OID
					if len(newRandomizationConfig.FormOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+" -> "+newRandomizationConfig.FormOid)
					}
					//字段OID
					if len(newRandomizationConfig.FieldOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.fieldOid")+":"+" -> "+newRandomizationConfig.FieldOid)
					}
				}
			}

			//找出删除的元素
			for key, oldRandomizationConfig := range oldRandomizationConfigMap {
				if _, ky := newRandomizationConfigMap[key]; !ky {
					fmt.Printf("删除对象: %v\n", oldRandomizationConfig)
					//IRT访视编号/EDC OID
					//文件夹OID
					if len(oldRandomizationConfig.FolderOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+oldRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+oldRandomizationConfig.FolderOid+" -> ")
					}
					//表单OID
					if len(oldRandomizationConfig.FormOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+oldRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+oldRandomizationConfig.FormOid+" -> ")
					}
					//字段OID
					if len(oldRandomizationConfig.FieldOid) > 0 {
						tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project."+oldRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.fieldOid")+":"+oldRandomizationConfig.FieldOid+" -> ")
					}
				}
			}

			//找出修改的元素
			for key, newRandomizationConfig := range newRandomizationConfigMap {
				if _, ky := oldRandomizationConfigMap[key]; ky {
					//IRT访视编号/EDC OID
					//文件夹OID
					oldFolderOid := ""
					newFolderOid := ""
					if len(oldRandomizationConfigMap[key].FolderOid) > 0 {
						oldFolderOid = oldRandomizationConfigMap[key].FolderOid
					}
					if len(newRandomizationConfig.FolderOid) > 0 {
						newFolderOid = newRandomizationConfig.FolderOid
					}
					if oldFolderOid != newFolderOid {
						list = append(list, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.folderOid")+":"+oldFolderOid+" -> "+newFolderOid)
					}

					//表单OID
					oldFormOid := ""
					newFormOid := ""
					if len(oldRandomizationConfigMap[key].FormOid) > 0 {
						oldFormOid = oldRandomizationConfigMap[key].FormOid
					}
					if len(newRandomizationConfig.FormOid) > 0 {
						newFormOid = newRandomizationConfig.FormOid
					}
					if oldFormOid != newFormOid {
						list = append(list, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.formOid")+":"+oldFormOid+" -> "+newFormOid)
					}
					//字段OID
					oldFieldOidOid := ""
					newFieldOidOid := ""
					if len(oldRandomizationConfigMap[key].FieldOid) > 0 {
						oldFieldOidOid = oldRandomizationConfigMap[key].FieldOid
					}
					if len(newRandomizationConfig.FieldOid) > 0 {
						newFieldOidOid = newRandomizationConfig.FieldOid
					}
					if oldFieldOidOid != newFieldOidOid {
						list = append(list, locales.Tr(ctx, "operation_log.project."+newRandomizationConfig.RandomizationField)+"/"+locales.Tr(ctx, "operation_log.project.fieldOid")+":"+oldFieldOidOid+" -> "+newFieldOidOid)
					}
				}
			}

			if list != nil && len(list) > 0 {
				if len(oldObj.Name) > 0 {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+"/"+oldObj.Name)
				} else {
					tmpList = append(tmpList, locales.Tr(ctx, "operation_log.project.edcMappingRules")+" ")
				}

				for _, s := range list {
					tmpList = append(tmpList, s)
				}
			}

		}
	}

	result := ""
	if len(tmpList) != 0 {
		result = strings.Join(tmpList, " ")
	}
	if result != "" && (result == "EDC映射 " || result == "EDC Mapping Rules ") {
		result = ""
	}
	return result, nil
}

func convertVisitRandomization(ctx *gin.Context, value interface{}) (models.VisitRandomization, error) {
	var result models.VisitRandomization

	// 假设 data 是 interface{} 类型的变量
	jsonVisitRandomization, err := json.Marshal(value)
	if err != nil {
		return result, err
	}

	var data []map[string]interface{}
	err = json.Unmarshal([]byte(jsonVisitRandomization), &data)
	if err != nil {
		return result, err
	}

	for _, item := range data {
		key := item["Key"].(string)
		if item["Value"] != nil {
			value := item["Value"].([]interface{})
			switch key {
			case "visits":
				visitData := value[0].([]interface{})
				visitName := visitData[0].(map[string]interface{})["Value"].(string)
				if visitData[1].(map[string]interface{})["Value"] != nil {
					visitConfigs := visitData[1].(map[string]interface{})["Value"].([]interface{})
					visitConfigList := make([]models.VisitConfig, 0)
					for _, config := range visitConfigs {
						configData := config.([]interface{})
						visitCode := configData[0].(map[string]interface{})["Value"].(string)
						folderOID := configData[1].(map[string]interface{})["Value"].(string)
						formOID := configData[2].(map[string]interface{})["Value"].(string)
						ipNumberOID := configData[3].(map[string]interface{})["Value"].(string)
						dispenseTimeOID := configData[4].(map[string]interface{})["Value"].(string)
						visitConfig := models.VisitConfig{
							VisitCode:       visitCode,
							FolderOid:       folderOID,
							FormOid:         formOID,
							IpNumberOid:     ipNumberOID,
							DispenseTimeOid: dispenseTimeOID,
						}
						visitConfigList = append(visitConfigList, visitConfig)
					}
					visit := models.Visit{
						Name:         visitName,
						VisitConfigs: visitConfigList,
					}

					result.Visits = append(result.Visits, visit)
				}
			case "randomizations":
				randomizationData := value[0].([]interface{})
				randomizationName := ""
				if randomizationData[0].(map[string]interface{})["Value"] != nil {
					randomizationName = randomizationData[0].(map[string]interface{})["Value"].(string)
				}
				randomizationConfigs := make([]interface{}, 0)
				if randomizationData[1].(map[string]interface{})["Value"] != nil {
					randomizationConfigs = randomizationData[1].(map[string]interface{})["Value"].([]interface{})
				}
				randomizationConfigList := make([]models.RandomizationConfig, 0)

				for _, config := range randomizationConfigs {
					configData := config.([]interface{})
					randomizationField := configData[0].(map[string]interface{})["Value"].(string)
					folderOID := configData[1].(map[string]interface{})["Value"].(string)
					formOID := configData[2].(map[string]interface{})["Value"].(string)
					fieldOID := configData[3].(map[string]interface{})["Value"].(string)

					randomizationConfig := models.RandomizationConfig{
						RandomizationField: randomizationField,
						FolderOid:          folderOID,
						FormOid:            formOID,
						FieldOid:           fieldOID,
					}

					randomizationConfigList = append(randomizationConfigList, randomizationConfig)
				}

				randomization := models.Randomization{
					Name:                 randomizationName,
					RandomizationConfigs: randomizationConfigList,
				}

				result.Randomizations = append(result.Randomizations, randomization)
			}
		}
	}

	return result, err
}

// 隔离管理 研究产品订单 研究产品回收 受试者 受试者发药 研究产品
func ExportReportTypeExampleTypeExport(ctx *gin.Context, projectId string, envId string, now time.Time, startTime int, endTime int, exportType string, subjectList []primitive.ObjectID) (string, []byte, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	key := "history.medicine.drugFreeze"
	modules := "menu.projects.project.supply.release-record"
	if exportType == "4" {
		key = "history.order"
		modules = "menu.report.auditTrailExport.order"
	} else if exportType == "5" {
		key = "history.order"
		modules = "menu.report.auditTrailExport.drug_recovery"
	} else if exportType == "6" {
		key = "history.subject"
		modules = "menu.projects.project.subject"
	} else if exportType == "7" {
		key = "history.dispensing"
		modules = "report.attributes.dispensing"
	} else if exportType == "8" {
		key = "history.medicine"
		modules = "menu.report.auditTrailExport.ip"
	}

	var attribute models.Attribute
	match := bson.M{
		"env_id":     envOID,
		"project_id": projectOID,
	}
	_ = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)

	collection := tools.Database.Collection("history")
	filter := bson.M{"key": bson.M{"$regex": key}}
	_, err := collection.CountDocuments(nil, filter)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	var historyData []models.History
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "medicine_freeze",
			"localField":   "oid",
			"foreignField": "_id",
			"as":           "freeze",
		}}},
		{{Key: "$match", Value: bson.D{{Key: "freeze.project_id", Value: projectOID}}}},
		{{Key: "$match", Value: bson.D{{Key: "freeze.env_id", Value: envOID}}}},
		{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
	}
	if exportType == "4" {
		pipeline = mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "medicine_order",
				"localField":   "oid",
				"foreignField": "_id",
				"as":           "order",
			}}},
			{{Key: "$match", Value: bson.D{{Key: "order.type", Value: bson.M{"$in": [4]int{1, 2, 5, 6}}}}}}, // 匹配目的地关联的中心  以及关联中心的受试者
			{{Key: "$match", Value: bson.D{{Key: "order.project_id", Value: projectOID}}}},
			{{Key: "$match", Value: bson.D{{Key: "order.env_id", Value: envOID}}}},

			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
		}
	} else if exportType == "5" {
		pipeline = mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "medicine_order",
				"localField":   "oid",
				"foreignField": "_id",
				"as":           "order",
			}}},
			{{Key: "$match", Value: bson.D{{Key: "order.type", Value: bson.M{"$in": [2]int{3, 4}}}}}},
			{{Key: "$match", Value: bson.D{{Key: "order.project_id", Value: projectOID}}}},
			{{Key: "$match", Value: bson.D{{Key: "order.env_id", Value: envOID}}}},
			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
		}
	} else if exportType == "6" {
		if subjectList != nil && len(subjectList) > 0 {
			// 再随机项目需要查询其他阶段的轨迹
			var project models.Project
			err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
			if err != nil {
				return "", nil, errors.WithStack(err)
			}

			if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
				for _, subjectOid := range subjectList {
					var subject models.Subject
					err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOid}).Decode(&subject)
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					// 筛选受试者号
					var subjectNo interface{}
					for _, info := range subject.Info {
						if info.Name == "shortname" {
							subjectNo = info.Value
						}
					}

					// 筛选环境
					env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
						return item.ID == subject.EnvironmentID
					})
					// 循环cohort
					var subjectIds []primitive.ObjectID
					if subject.Deleted == true {
						subjectIds = []primitive.ObjectID{subjectOid} // 再随机项目受试者只可能再第一个阶段被删除
					} else {
						for _, cohort := range env.Cohorts {
							var cohortSbj models.Subject
							subjectFilter := bson.M{
								"customer_id": subject.CustomerID,
								"project_id":  subject.ProjectID,
								"env_id":      subject.EnvironmentID,
								"cohort_id":   cohort.ID,
								"info": bson.M{
									"$elemMatch": bson.M{
										"name":  "shortname",
										"value": subjectNo,
									},
								},
								"deleted": bson.M{"$ne": true},
							}
							err = tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&cohortSbj)
							if err != nil && err != mongo.ErrNoDocuments {
								return "", nil, errors.WithStack(err)
							}
							if cohortSbj.Info != nil && len(cohortSbj.Info) > 0 {
								subjectIds = append(subjectIds, cohortSbj.ID)
							}
						}
					}

					// 合并
					subjectList = slice.Concat(subjectList, subjectIds)

				}
				// 去重
				subjectList = slice.Unique(subjectList)

			}

			filter = bson.M{"key": bson.M{"$regex": key}, "oid": bson.M{"$in": subjectList}}
		}
		pipeline = mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: bson.M{
				"from": "subject", // 关联的集合名称
				"let": bson.M{
					"oid": "$oid", // 将当前文档的 oid 字段赋值给变量 oid
				},
				"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
					bson.M{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": bson.A{
									bson.M{"$eq": bson.A{"$_id", "$$oid"}}, // 关联条件：_id 等于 oid
									//bson.M{"$ne": bson.A{"$deleted", true}}, // 过滤条件：deleted 不为 true // 此处被删除的受试者也要查询
								},
							},
						},
					},
				},
				"as": "subject", // 关联结果的字段名
			}}},
			{{Key: "$match", Value: bson.D{{Key: "subject.project_id", Value: projectOID}}}},
			{{Key: "$match", Value: bson.D{{Key: "subject.env_id", Value: envOID}}}},
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "subject.project_site_id", "foreignField": "_id", "as": "site"}}},
			{{Key: "$match", Value: bson.D{{Key: "site.deleted", Value: 2}}}},
			{{Key: "$unwind", Value: "$subject"}},
			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}, {Key: "_id", Value: -1}}}},
		}
	} else if exportType == "7" {
		if subjectList != nil && len(subjectList) > 0 {
			pipeline = mongo.Pipeline{
				{{Key: "$match", Value: filter}},
				{{Key: "$lookup", Value: bson.M{
					"from":         "dispensing",
					"localField":   "oid",
					"foreignField": "_id",
					"as":           "dispensing",
				}}},
				{{Key: "$match", Value: bson.D{{Key: "dispensing.project_id", Value: projectOID}}}},
				{{Key: "$match", Value: bson.D{{Key: "dispensing.env_id", Value: envOID}}}},
				{{Key: "$match", Value: bson.D{{Key: "dispensing.subject_id", Value: bson.M{"$in": subjectList}}}}},
				{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
			}
		} else {
			pipeline = mongo.Pipeline{
				{{Key: "$match", Value: filter}},
				{{Key: "$lookup", Value: bson.M{
					"from":         "dispensing",
					"localField":   "oid",
					"foreignField": "_id",
					"as":           "dispensing",
				}}},
				{{Key: "$match", Value: bson.D{{Key: "dispensing.project_id", Value: projectOID}}}},
				{{Key: "$match", Value: bson.D{{Key: "dispensing.env_id", Value: envOID}}}},
				{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
			}
		}

	} else if exportType == "8" {
		pipeline = mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "medicine",
				"localField":   "oid",
				"foreignField": "_id",
				"as":           "medicine",
			}}},
			{{Key: "$match", Value: bson.D{{Key: "medicine.project_id", Value: projectOID}}}},
			{{Key: "$match", Value: bson.D{{Key: "medicine.env_id", Value: envOID}}}},
			//{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "medicine.site_id", "foreignField": "_id", "as": "project_site"}}},
			//{{Key: "$unwind", Value: "$project_site"}},
			//{{Key: "$sort", Value: bson.D{{"project_site.number", 1}, {"number", 1}}}},
			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
		}
	}

	if startTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$gt": startTime}}}})
	}
	if endTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$lt": endTime}}}})
	}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	if exportType != "8" {
		cursor, err := collection.Aggregate(nil, pipeline, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &historyData)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
	} else {
		cursor, err := collection.Aggregate(nil, pipeline, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &historyData)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}

		var unnumbereds []models.History
		condition := mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "medicine_other_key",
				"localField":   "oid",
				"foreignField": "_id",
				"as":           "medicine",
			}}},
			{{Key: "$match", Value: bson.D{{Key: "medicine.project_id", Value: projectOID}}}},
			{{Key: "$match", Value: bson.D{{Key: "medicine.env_id", Value: envOID}}}},
			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
		}

		if startTime != 0 {
			condition = append(condition, bson.D{{"$match", bson.M{"time": bson.M{"$gt": startTime}}}})
		}
		if endTime != 0 {
			condition = append(condition, bson.D{{"$match", bson.M{"time": bson.M{"$lt": endTime}}}})
		}

		cus, e := tools.Database.Collection("history").Aggregate(nil, condition)
		if e != nil {
			return "", nil, errors.WithStack(e)
		}
		e = cus.All(nil, &unnumbereds)
		if e != nil {
			return "", nil, errors.WithStack(e)
		}
		for _, unnumbered := range unnumbereds {
			historyData = append(historyData, unnumbered)
		}

		//sort.Slice(data, func(i, j int) bool {
		//	return data[i].Time < data[j].Time
		//})
	}

	//var attribute models.Attribute
	//err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"_id": attributeOId}).Decode(&attribute)
	//if err != nil && err != mongo.ErrNoDocuments {
	//	return "", nil, errors.WithStack(err)
	//}
	//isBlindedRole := false
	//if attribute.AttributeInfo.Blind {
	//	isBlindedRole, err = tools.IsBlindedRole(roleId)
	//	if err != nil {
	//		return "", nil, err
	//	}
	//}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return "", nil, err
	}

	title := []interface{}{
		locales.Tr(ctx, "report.attributes.project.number"),
		locales.Tr(ctx, "report.attributes.project.name"),
		locales.Tr(ctx, "menu.settings.users"),
		locales.Tr(ctx, "report.user.login.time"),
		locales.Tr(ctx, "report.audit.trail.operation.name"),
		//locales.Tr(ctx, "report.audit.trail.operation.way"),
		locales.Tr(ctx, "report.user.role.assign.content"),
	}

	timeZone, err := tools.GetTimeZone(projectOID)
	content := make([][]interface{}, 0)
	for _, history := range historyData {
		// 页面请求 有表格数据的 使用已经截取后的字段
		if history.TableInfo != nil {
			history.CustomTemps = *history.ShowCustomTemps
		}
		if exportType == "6" {
			var subject models.Subject
			err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": history.OID}).Decode(&subject)
			if subject.CohortID != primitive.NilObjectID {
				match = bson.M{
					"env_id":     envOID,
					"project_id": projectOID,
					"cohort_id":  subject.CohortID,
				}
			}
			_ = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
		} else if exportType == "7" {
			var dispensing models.Dispensing
			err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": history.OID}).Decode(&dispensing)
			if dispensing.CohortID != primitive.NilObjectID {
				match = bson.M{
					"env_id":     envOID,
					"project_id": projectOID,
					"cohort_id":  dispensing.CohortID,
				}
			}
			_ = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
		}

		// 供应计划 映射
		if key == "history.supply-plan-medicine" {
			switch history.Data["autoSupplySize"] {
			case 1.0:
				history.Data["autoSupplySize"] = locales.Tr(ctx, "medicine.autoSupplySize1")
			case 2.0:
				history.Data["autoSupplySize"] = locales.Tr(ctx, "medicine.autoSupplySize2")
			}
			switch history.Data["supplyMode"] {
			case 1.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode1")
			case 2.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode2")
			case 3.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode3")
			case 4.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode4")
			}
		}
		if key == "history.randomization.config" {
			if history.Data["valueSite"] != nil && history.Data["valueSite"] == "" {
				history.Data["valueSite"] = locales.Tr(ctx, "user.depot")
			}
		}
		if key == "history.randomization" {
			if history.Data["valueSite"] != nil && history.Data["valueSite"] == "" {
				history.Data["valueSite"] = locales.Tr(ctx, "user.depot")
			}
		}
		if strings.Contains(key, "history.subject") {
			if !attribute.AttributeInfo.IsRandomNumber {
				history.Data["randomNumber"] = tools.BlindData
			}
		}
		if history.Key == "history.subject.unblinding" {
			if history.Data["reasonType"] != nil {
				history.Data["reasonType"] = tools.GetUnblindingReasonTran(ctx, int(history.Data["reasonType"].(int32)))
			} else {
				history.Data["reasonType"] = tools.GetUnblindingReasonTran(ctx, 0)

			}
		}

		//if history.Key == "history.randomization.attribute" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["segmentLength"] = tools.BlindData
		//	//}
		//}
		//if history.Key == "history.project.medicine.upload" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["drugName"] = tools.BlindData
		//	//}
		//}
		//if history.Key == "history.randomization.config.block.generate" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["groupRatio"] = tools.BlindData
		//	history.Data["numberText"] = tools.BlindData
		//	//}
		//}
		//if history.Key == "history.randomization.config.group.add" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["name"] = tools.BlindData
		//	//}
		//}
		//if history.Key == "history.randomization.config.group.delete" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["name"] = tools.BlindData
		//	//}
		//}
		//if history.Key == "history.randomization.config.group.edit" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["name"] = tools.BlindData
		//	history.Data["oldName"] = tools.BlindData
		//	//}
		//}
		//if history.Key == "history.supply-plan-medicine.add" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["medicineName"] = tools.BlindData
		//	//}
		//}
		//if history.Key == "history.supply-plan-medicine.update" {
		//	//if attribute.AttributeInfo.Blind && isBlindedRole {
		//	history.Data["medicineName"] = tools.BlindData
		//	//}
		//}
		if history.Key == "history.subject.replaced-new" {
			if !attribute.AttributeInfo.IsRandomNumber {
				history.Data["beReplaceRandomNumber"] = tools.BlindData
				history.Data["replaceRandomNumber"] = tools.BlindData
			}
		}

		if history.Key == "history.medicine.sku-freeze-subject" || history.Key == "history.medicine.sku-used-subject" || history.Key == "history.medicine.sku-use-subject" || history.Key == "history.medicine.sku-in-order-subject" || history.Key == "history.medicine.sku-lost-subject" {
			history.Data["operation"] = getOperationType(ctx, history.Data["operation"].(int32))
		}

		if history.Key == "history.dispensing.reissue-with-logistics" ||
			history.Key == "history.dispensing.dispensing-logistics" ||
			history.Key == "history.dispensing.dispensingVisit-logistics" ||
			history.Key == "history.dispensing.dispensingVisit-logistics-formula" ||
			history.Key == "history.dispensing.dispensing-logistics-formula" {
			if !attribute.AttributeInfo.IsRandomNumber || !attribute.AttributeInfo.Random {
				switch history.Key {
				case "history.dispensing.reissue-with-logistics":
					history.Key = "history.dispensing.reissue-with-logistics-noRandomNumber"
				case "history.dispensing.dispensing-logistics":
					history.Key = "history.dispensing.dispensing-logistics-noRandomNumber"
				case "history.dispensing.dispensingVisit-logistics":
					history.Key = "history.dispensing.dispensingVisit-logistics-noRandomNumber"
				case "history.dispensing.dispensing-logistics-formula":
					history.Key = "history.dispensing.dispensing-logistics-noRandomNumber-formula"
				case "history.dispensing.dispensingVisit-logistics-formula":
					history.Key = "history.dispensing.dispensingVisit-logistics-noRandomNumber-formula"
				}
			}
			//if !attribute.AttributeInfo.IsRandomNumber {
			//history.Data["randomNumber"] = tools.BlindData
			//}
			var medicines []string
			for _, item := range history.Data["medicine"].(primitive.A) {

				if item.(map[string]interface{})["blind"].(bool) {
					//if isBlindedRole {
					//	medicines = append(medicines, tools.BlindData+"("+item.(map[string]interface{})["number"].(string)+")")
					//} else {
					medicines = append(medicines, item.(map[string]interface{})["name"].(string)+"("+item.(map[string]interface{})["number"].(string)+")")
					//}
				} else {
					value := item.(map[string]interface{})["name"].(string) + "/" + convertor.ToString(item.(map[string]interface{})["number"].(int32))
					if item.(map[string]interface{})["batch"] != nil {
						value = value + "/" + item.(map[string]interface{})["batch"].(string) + "/" + item.(map[string]interface{})["expireDate"].(string)
						medicines = append(medicines, value)
					} else {
						medicines = append(medicines, value)
					}
				}
			}
			history.Data["medicine"] = medicines
			if history.Data["vendor"] != nil {
				logisName := "其他"
				if locales.Lang(ctx) == "en" {
					logisName = "other"
				}
				if reflect.TypeOf(history.Data["vendor"]) == reflect.TypeOf("0") {
					logis, _ := GetLogisticsCode(ctx, history.Data["vendor"].(string))
					if len(logis.Name) > 0 {
						logisName = logis.Name
					}
					history.Data["vendor"] = logisName
				}
			}
			//history.Data["vendor"] = locales.Tr(ctx, "order_logistics_"+convertor.ToString(history.Data["vendor"]))
			history.Data["sendType"] = locales.Tr(ctx, "history.dispensing.send-type-"+convertor.ToString(history.Data["sendType"]))
		}

		if history.Key == "history.order.logistics" || history.Key == "history.order.send-with-logistics" {
			if history.Data["logistics"] != nil {
				if reflect.TypeOf(history.Data["logistics"]) == reflect.TypeOf("0") {
					logis, _ := GetLogisticsCode(ctx, convertor.ToString(history.Data["logistics"]))
					logisName := "其他"
					if locales.Lang(ctx) == "en" {
						logisName = "other"
					}
					if len(logis.Name) > 0 {
						logisName = logis.Name
					}
					history.Data["logistics"] = logisName
				}
			}
			//history.Data["logistics"] = locales.Tr(ctx, "order_logistics_"+convertor.ToString(history.Data["logistics"]))
		}

		if history.Key == "history.medicine.rest-receive" || history.Key == "history.medicine.rest-return" || history.Key == "history.medicine.rest-destroy" ||
			history.Key == "history.medicine.rest-return-retrieve" || history.Key == "history.medicine.rest-destroy-retrieve" {
			if history.Data != nil {
				if history.Data["operation"] != nil {
					history.Data["operation"] = locales.Tr(ctx, history.Data["operation"].(string))
				}
			}
		}

		if history.Key == "history.medicine.updateBatch" || history.Key == "history.medicine.updateOtherBatch" || history.Key == "history.dispensing.updateBatch" {
			p := make([]string, 0)
			if history.Data["status"] != nil {
				statuss := history.Data["status"].(primitive.A)
				for _, status := range statuss {
					val := data.GetMedicineStatus(ctx, int(status.(int32)))
					p = append(p, val)
				}
			}
			history.Data["status"] = strings.Join(p, "、")
		}

		if (history.Key == "history.subject.label.screen" ||
			history.Key == "history.subject.label.screenScreenFail" ||
			history.Key == "history.subject.label.updateScreen" ||
			history.Key == "history.subject.label.updateScreenSignOutTime" ||
			history.Key == "history.subject.label.updateScreenFailSignOutTime" ||
			history.Key == "history.subject.label.updateScreenFail") && history.Data["isScreen"] != nil {
			if history.Data["isScreen"].(bool) {
				history.Data["isScreen"] = locales.Tr(ctx, "common.yes")
			} else {
				history.Data["isScreen"] = locales.Tr(ctx, "common.no")
			}
		}

		if strings.Contains(history.Key, "history.dispensing.") || strings.Contains(history.Key, "history.subject.label.") {
			if history.Data["label"] != nil {
				if len(history.Data["label"].(string)) != 0 {
					history.Data["label"] = locales.Tr(ctx, "subject.number")
					if locales.Lang(ctx) == "en" {
						if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
							history.Data["label"] = attribute.AttributeInfo.SubjectReplaceTextEn
						} else {
							if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
								history.Data["label"] = attribute.AttributeInfo.SubjectReplaceText
							}
						}
					} else if locales.Lang(ctx) == "zh" {
						if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
							history.Data["label"] = attribute.AttributeInfo.SubjectReplaceText
						} else {
							if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
								history.Data["label"] = attribute.AttributeInfo.SubjectReplaceTextEn
							}
						}
					}
				}
			}
		}

		//TODO
		//发药轨迹 单字段拼接轨迹
		if history.Key == "history.dispensing.dispensingCustomer-dispensing" ||
			history.Key == "history.dispensing.dispensingCustomer-dispensingVisit" ||
			history.Key == "history.dispensing.dispensingCustomer-reissue" {
			if history.Data["label"] != nil {
				replacement := GetSubjectReplaceText(ctx, attribute)
				history.Data["label"] = replacement
			}
			if !attribute.AttributeInfo.IsRandomNumber || !attribute.AttributeInfo.Random {
				history.CustomTemps[0].CustomTempOptions = slice.Filter(history.CustomTemps[0].CustomTempOptions, func(index int, item models.CustomTempOption) bool {
					return item.Key != "history.dispensing.single.randomNumber"
				})
			}
			//slice.ForEach(history.CustomTemps[0].CustomTempOptions, func(index int, item models.CustomTempOption) {
			//	if item.Key == "history.dispensing.single.medicine" {
			//		item.Data["medicine"].([]interface{}).
			//	}
			//})
		}
		//自定义拼接的轨迹
		colon := locales.Tr(ctx, "history.dispensing.single.colon")
		if history.CustomTemps != nil && len(history.CustomTemps) > 0 {
			for _, temp := range history.CustomTemps {
				err = slice.SortByField(temp.CustomTempOptions, "Index")
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				optionContents := make([]string, 0)
				for _, option := range temp.CustomTempOptions {
					if option.Key == "history.subject.label.updateCustomizeIsScreen" {
						if option.Data["isScreen"].(bool) {
							option.Data["isScreen"] = locales.Tr(ctx, "common.yes")
						} else {
							option.Data["isScreen"] = locales.Tr(ctx, "common.no")
						}
					}
					optionContent := ""
					switch option.TransType {
					case models.KeyUnData:
						optionContent = locales.Tr(ctx, option.Key, option.Data)
						if option.Key == "history.subject.label.common-key-value1" {
							replacement := GetSubjectReplaceText(ctx, attribute)
							option.Data["name"] = replacement
							optionContent = locales.Tr(ctx, option.Key, option.Data)
						}
					case models.UnKeyData:
						optionContent = option.Key + colon + locales.Tr(ctx, option.TransData)
					case models.UnKeyUnData:
						optionContent = option.Key + colon + option.TransData
					case models.KeyData:
						optionContent = locales.Tr(ctx, option.Key) + colon + locales.Tr(ctx, option.TransData)
					}
					optionContents = append(optionContents, optionContent)
				}
				customContent := strings.Join(optionContents, locales.Tr(ctx, temp.ConnectingSymbolKey))
				customContent = customContent + locales.Tr(ctx, temp.LastSymbolKey)
				if history.Data == nil {
					history.Data = make(map[string]interface{}) // 替换为实际键值类型
				}
				history.Data[temp.ParKey] = customContent
			}
		}

		//受试者登记
		if history.Key == "history.subject.add" || history.Key == "history.subject.at-random-add" {
			if history.Data["content"] != nil {
				if len(history.Data["content"].(string)) != 0 {
					replacement := GetSubjectReplaceText(ctx, attribute)
					index := strings.Index(history.Data["content"].(string), ":")
					if index != -1 {
						history.Data["content"] = strings.Replace(history.Data["content"].(string), history.Data["content"].(string)[:index], replacement, 1)
					}
				}
			}
		}

		if history.Key == "history.medicine.update-batch-expireDate" || history.Key == "history.medicine.other-update-batch-expireDate" {
			history.Data["status"] = getShipmentStatus(ctx, history.Data["status"].(int32))
		}

		//受试者修改/筛选/停用/完成研究/删除
		if history.Key == "history.subject.label.update" ||
			history.Key == "history.subject.label.updateSubjectNo" || history.Key == "history.subject.label.updateSignOutTime" ||
			history.Key == "history.subject.label.updateScreen" || history.Key == "history.subject.label.updateScreenSignOutTime" ||
			history.Key == "history.subject.label.updateScreenFail" || history.Key == "history.subject.label.updateScreenFailSignOutTime" ||
			history.Key == "history.subject.label.screen" || history.Key == "history.subject.label.screenScreenFail" ||
			history.Key == "history.subject.label.signOut" || history.Key == "history.subject.label.signOutReal" ||
			history.Key == "history.subject.label.finish" || history.Key == "history.subject.start-follow-up-visits" ||
			history.Key == "history.subject.label.at-random-screen" || history.Key == "history.subject.label.at-random-random" ||
			history.Key == "history.subject.at-random-transfer" || history.Key == "history.subject.at-random-joinTime" ||
			history.Key == "history.subject.at-random-add" || history.Key == "history.subject.at-random-unblinding-application" ||
			history.Key == "history.subject.at-random-unblinding-application-pv" || history.Key == "history.subject.at-random-unblinding-success" ||
			history.Key == "history.subject.at-random-update" || history.Key == "history.subject.label.at-random-finish" ||
			history.Key == "history.subject.label.at-random-pvUnblinding" || history.Key == "history.subject.label.at-random-randomNoNumber" ||
			history.Key == "history.subject.label.at-random-randomNoNumberSub" || history.Key == "history.subject.label.at-random-randomSub" ||
			history.Key == "history.subject.label.at-random-remark-pvUnblinding" || history.Key == "history.subject.label.at-random-replaced-new-a" ||
			history.Key == "history.subject.label.at-random-signOut" || history.Key == "history.subject.label.updateCustomize" ||
			history.Key == "history.subject.label.at-random-replaced-new-b" || history.Key == "history.dispensing.dispensingCustomer-register" ||
			history.Key == "history.dispensing.dispensingCustomer-replace" || history.Key == "history.dispensing.invalid" ||
			history.Key == "history.dispensing.dispensingCustomer-retrieval" || history.Key == "history.dispensing.resume" ||
			history.Key == "history.subject.label.randomNoNumber" || history.Key == "history.subject.label.replaced-new" ||
			history.Key == "history.subject.label.remark-pvUnblinding" || history.Key == "history.subject.label.pvUnblinding" ||
			history.Key == "history.dispensing.retrieval-order" || history.Key == "history.dispensing.retrieval" ||
			history.Key == "history.dispensing.scanConfrim" || history.Key == "history.dispensing.dispensing-new-formula" ||
			history.Key == "history.dispensing.dispensing-with-other-new-formula" || history.Key == "history.dispensing.dispensing-with-other-reason-new-formula" ||
			history.Key == "history.dispensing.dispensingVisit-new-formula" || history.Key == "history.dispensing.dispensingVisit-other-new-formula" ||
			history.Key == "history.dispensing.dispensing-logistics-formula" || history.Key == "history.dispensing.dispensing-logistics-noRandomNumber-formula" ||
			history.Key == "history.dispensing.dispensingVisit-logistics-formula" || history.Key == "history.dispensing.dispensingVisit-logistics-noRandomNumber-formula" ||
			history.Key == "history.dispensing.replace" || history.Key == "history.dispensing.reissue-with-logistics-noRandomNumber" ||
			history.Key == "history.dispensing.reissue-with-logistics" || history.Key == "history.dispensing.dispensingVisit-logistics-noRandomNumber" ||
			history.Key == "history.dispensing.dispensingVisit-logistics" || history.Key == "history.dispensing.dispensing-logistics-noRandomNumber" ||
			history.Key == "history.dispensing.dispensing-logistics" || history.Key == "history.dispensing.replace-logistics" ||
			history.Key == "history.dispensing.reissue-with-other" || history.Key == "history.dispensing.reissue-other" ||
			history.Key == "history.dispensing.reissue" || history.Key == "history.dispensing.register" ||
			history.Key == "history.dispensing.dtp-reissue-with-other" || history.Key == "history.dispensing.dtp-reissue-other" ||
			history.Key == "history.dispensing.dtp-reissue" || history.Key == "history.dispensing.dtp-dispensingVisit-other" ||
			history.Key == "history.dispensing.dtp-dispensingVisit" || history.Key == "history.dispensing.dtp-dispensing-with-other-reason" ||
			history.Key == "history.dispensing.dtp-dispensing-with-other" || history.Key == "history.dispensing.dtp-dispensing-other" ||
			history.Key == "history.dispensing.dtp-dispensing" || history.Key == "history.dispensing.dispensingVisit-other-new" ||
			history.Key == "history.dispensing.dispensingVisit-new" || history.Key == "history.dispensing.dispensing-with-other-reason-new" ||
			history.Key == "history.dispensing.dispensing-with-other-new" || history.Key == "history.dispensing.dispensing-other-new" ||
			history.Key == "history.dispensing.dispensing-new" || history.Key == "history.dispensing.dispensingVisit-other" ||
			history.Key == "history.dispensing.dispensingVisit" || history.Key == "history.dispensing.dispensing-with-other-reason" ||
			history.Key == "history.dispensing.dispensing-with-other" || history.Key == "history.dispensing.dispensing-other" ||
			history.Key == "history.dispensing.dispensing" || history.Key == "history.dispensing.cancel" || history.Key == "history.subject.switch-cohort" ||
			history.Key == "history.subject.delete" || history.Key == "history.subject.close-follow-up-visits" ||
			history.Key == "history.dispensing.dispensingCustomer-not-attend" || history.Key == "history.dispensing.dispensingCustomer-resume" ||
			history.Key == "history.subject.transfer" {
			if history.Data["label"] != nil {
				replacement := GetSubjectReplaceText(ctx, attribute)
				history.Data["label"] = replacement
			}
		}

		context := locales.Tr(ctx, history.Key, history.Data)
		//historyDate[i] = models.HistoryView{
		//	OID:     history.OID,
		//	Content: content,
		//	Time:    history.Time,
		//	User:    history.User,
		//}
		var username string
		if history.UID.IsZero() {
			username = history.User
			if len(username) > 0 {
				if username == "码上放心" && locales.Lang(ctx) == "en" {
					username = "CodeTrust Platform"
				}
			}
		} else {
			var user models.User
			err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": history.UID}).Decode(&user)
			if err != nil {
				return "", nil, errors.WithStack(err)
			}
			username = fmt.Sprintf("%s(%d)", history.User, user.Unicode)
		}
		operationName := locales.Tr(ctx, modules)

		if exportType == "6" {
			var subject models.Subject
			err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": history.OID}).Decode(&subject)
			siteTimeZone, _ := tools.GetSiteTimeZone(subject.ProjectSiteID)
			if len(siteTimeZone) != 0 {
				offset, _ := tools.ParseTimezoneOffset(siteTimeZone)
				timeZone = offset
			}

		} else if exportType == "7" {
			var dispensing models.Dispensing
			err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": history.OID}).Decode(&dispensing)
			var subject models.Subject
			err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
			siteTimeZone, _ := tools.GetSiteTimeZone(subject.ProjectSiteID)
			if len(siteTimeZone) != 0 {
				offset, _ := tools.ParseTimezoneOffset(siteTimeZone)
				timeZone = offset
			}
		} else if exportType == "8" {
			var medicine models.Medicine
			err = tools.Database.Collection("medicine").FindOne(nil, bson.M{"_id": history.OID}).Decode(&medicine)
			if err == nil {
				siteTimeZone, _ := tools.GetSiteTimeZone(medicine.SiteID)
				if len(siteTimeZone) != 0 {
					offset, _ := tools.ParseTimezoneOffset(siteTimeZone)
					timeZone = offset
				}

				operationName = locales.Tr(ctx, modules) + "(" + medicine.Number + ")"
			} else {
				var medicineOtherKey models.MedicineOtherKey
				err = tools.Database.Collection("medicine_other_key").FindOne(nil, bson.M{"_id": history.OID}).Decode(&medicineOtherKey)
				operationName = locales.Tr(ctx, modules) + "(" + medicineOtherKey.Name + "/" + medicineOtherKey.Batch + "/" + medicineOtherKey.ExpireDate + ")"
			}
		}
		var date string
		hours := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		strTimeZone := tools.FormatOffsetToZoneStringUtc(timeZone)

		date = time.Unix(int64(history.Time.Nanoseconds()), 0).UTC().Add(duration).Format("2006-01-02 15:04:05") + "(" + strTimeZone + ")"

		contentItem := []interface{}{
			project.Number,
			project.Name,
			username,
			date,
			//time.Unix(history.Time.Nanoseconds(), 0).UTC().Format("2006-01-02 15:04:05") + "(UTC+0)",
			operationName,
			//locales.Tr(ctx, models.OperationLogMap["operation_type"].(bson.M)[convertor.ToString(operationLog.Type)].(string)),
			context,
		}
		content = append(content, contentItem)
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	fileName := "QuarantineManagement.xlsx"
	if exportType == "4" {
		fileName = "ShipmentOrders.xlsx"
	} else if exportType == "5" {
		fileName = "ReturnOrders.xlsx"
	} else if exportType == "6" {
		fileName = "Subjects.xlsx"
	} else if exportType == "7" {
		fileName = "Dispensing.xlsx"
	} else if exportType == "8" {
		fileName = "IP.xlsx"
	}
	return fileName, buffer.Bytes(), nil
}

// 项目通知配置报表
func ExportProjectNotificationsConfigurationReport(ctx *gin.Context, projectId string, envId string, roleId string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envId)
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	customerOID := project.CustomerID
	var keys = []string{
		"notice.basic.settings",
		"notice.subject.add",
		"notice.subject.random",
		"notice.subject.signOut",
		"notice.subject.replace",
		"notice.subject.screen",
		"notice.subject.update",
		"notice.subject.dispensing",
		"notice.subject.alarm",
		"notice.subject.unblinding",
		"notice.medicine.isolation",
		"notice.medicine.order",
		"notice.medicine.reminder",
		"notice.medicine.alarm",
		"notice.storehouse.alarm",
		"notice.order.timeout",
		"notice.subject.alert.threshold",
	}

	noticeConfigList := make([]models.NoticeConfig, 0)
	for _, key := range keys {
		var noticeConfig models.NoticeConfig
		noticeConfig.Key = key
		noticeConfigList = append(noticeConfigList, noticeConfig)
	}

	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "key": bson.M{"$in": keys}}
	noticeConfigs := make([]models.NoticeConfig, 0)
	cursor, err := tools.Database.Collection("notice_config").Find(nil, match)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &noticeConfigs)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	//return noticeConfigs, nil

	keyList := make([]string, 0)
	for _, noticeConfig := range noticeConfigs {
		keyList = append(keyList, noticeConfig.Key)
	}

	newNoticeConfigs := make([]models.NoticeConfig, 0)
	for _, config := range noticeConfigList {
		var newNoticeConfig models.NoticeConfig
		newNoticeConfig.Key = config.Key
		for _, noticeConfig := range noticeConfigs {
			if noticeConfig.Key == config.Key {
				newNoticeConfig.ID = noticeConfig.ID
				newNoticeConfig.CustomerID = noticeConfig.CustomerID
				newNoticeConfig.ProjectID = noticeConfig.ProjectID
				newNoticeConfig.EnvironmentID = noticeConfig.EnvironmentID
				newNoticeConfig.Key = noticeConfig.Key
				newNoticeConfig.Repeatable = noticeConfig.Repeatable
				newNoticeConfig.Cycle = noticeConfig.Cycle
				newNoticeConfig.Roles = noticeConfig.Roles
				newNoticeConfig.TimeoutDays = noticeConfig.TimeoutDays
				newNoticeConfig.ForecastTime = noticeConfig.ForecastTime
				newNoticeConfig.SendDays = noticeConfig.SendDays
				newNoticeConfig.FieldsConfig = noticeConfig.FieldsConfig
				newNoticeConfig.State = noticeConfig.State
				newNoticeConfig.Automatic = noticeConfig.Automatic
				newNoticeConfig.Manual = noticeConfig.Manual
				newNoticeConfig.ExcludeRecipientList = noticeConfig.ExcludeRecipientList
				continue
			}
		}
		newNoticeConfigs = append(newNoticeConfigs, newNoticeConfig)
	}

	//configs := isNoticeConfigs(ctx, keyList, noticeConfigs)

	//// 指定的顺序
	//order := []int{10, 1, 6, 5, 3, 9, 0, 8, 4, 7, 2}
	//// 排序后的新数组
	//newNoticeConfigs := make([]models.NoticeConfig, len(configs))
	//// 根据指定的顺序重新排列数组
	//for i := 0; i < len(order); i++ {
	//	newNoticeConfigs[i] = configs[order[i]]
	//}

	title := []interface{}{
		locales.Tr(ctx, "export.notifications.configuration.report.type"),
		locales.Tr(ctx, "export.notifications.configuration.report.role"),
		locales.Tr(ctx, "export.notifications.configuration.report.content.configuration"),
		locales.Tr(ctx, "export.notifications.configuration.report.scene"),
		locales.Tr(ctx, "export.notifications.configuration.report.excludeRecipientList"),
	}
	content := make([][]interface{}, 0)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	for _, newNoticeConfig := range newNoticeConfigs {
		//邮件类型
		newNoticeType := ""
		//角色勾选
		newNoticeRole := noticeRoles(ctx, newNoticeConfig.Roles)
		//内容配置勾选
		newNoticeContentConfiguration := ""
		if newNoticeConfig.FieldsConfig != nil && len(newNoticeConfig.FieldsConfig) > 0 {
			newNoticeContentConfiguration = noticeContentConfiguration(ctx, newNoticeConfig.Key, newNoticeConfig.FieldsConfig)
		}
		//场景勾选
		newNoticeScene := ""
		if newNoticeConfig.State != nil && len(newNoticeConfig.State) > 0 {
			newNoticeScene = noticeScene(ctx, newNoticeConfig.Key, newNoticeConfig.State)
		}
		//排除收件人
		newNoticeExcludeRecipientList := ""
		if newNoticeConfig.ExcludeRecipientList != nil && len(newNoticeConfig.ExcludeRecipientList) > 0 {
			newNoticeExcludeRecipientList = strings.Join(newNoticeConfig.ExcludeRecipientList, ",")
		}
		if newNoticeConfig.Key == "notice.basic.settings" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.basic.settings")
			detailList := make([]string, 0)
			if &newNoticeConfig.Automatic != nil && newNoticeConfig.Automatic != 0 {
				if newNoticeConfig.Automatic == 1 {
					detail := locales.Tr(ctx, "notifications.notice.basic.settings.email.automatedTasks") + ":" +
						locales.Tr(ctx, "notifications.notice.basic.settings.emailLanguage_zh")
					detailList = append(detailList, detail)
				} else if newNoticeConfig.Automatic == 2 {
					detail := locales.Tr(ctx, "notifications.notice.basic.settings.email.automatedTasks") + ":" +
						locales.Tr(ctx, "notifications.notice.basic.settings.emailLanguage_en")
					detailList = append(detailList, detail)
				} else if newNoticeConfig.Automatic == 3 {
					detail := locales.Tr(ctx, "notifications.notice.basic.settings.email.automatedTasks") + ":" +
						locales.Tr(ctx, "notifications.notice.basic.settings.emailLanguage_zh_en")
					detailList = append(detailList, detail)
				}
			}
			if &newNoticeConfig.Manual != nil && newNoticeConfig.Manual != 0 {
				if newNoticeConfig.Manual == 1 {
					detail := locales.Tr(ctx, "notifications.notice.basic.settings.email.manualTasks") + ":" +
						locales.Tr(ctx, "notifications.notice.basic.settings.emailLanguage_zh")
					detailList = append(detailList, detail)
				} else if newNoticeConfig.Manual == 2 {
					detail := locales.Tr(ctx, "notifications.notice.basic.settings.email.manualTasks") + ":" +
						locales.Tr(ctx, "notifications.notice.basic.settings.emailLanguage_en")
					detailList = append(detailList, detail)
				} else if newNoticeConfig.Manual == 3 {
					detail := locales.Tr(ctx, "notifications.notice.basic.settings.email.manualTasks") + ":" +
						locales.Tr(ctx, "notifications.notice.basic.settings.emailLanguage_zh_en")
					detailList = append(detailList, detail)
				}
			}
			newNoticeContentConfiguration = strings.Join(detailList, ",")
		} else if newNoticeConfig.Key == "notice.subject.add" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.add")
		} else if newNoticeConfig.Key == "notice.subject.random" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.random")
		} else if newNoticeConfig.Key == "notice.subject.signOut" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.signOut")
		} else if newNoticeConfig.Key == "notice.subject.replace" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.replace")
		} else if newNoticeConfig.Key == "notice.subject.screen" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.screen")
		} else if newNoticeConfig.Key == "notice.subject.update" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.update")
		} else if newNoticeConfig.Key == "notice.subject.dispensing" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.dispensing")
		} else if newNoticeConfig.Key == "notice.subject.alarm" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.alarm")
		} else if newNoticeConfig.Key == "notice.subject.unblinding" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.unblinding")
		} else if newNoticeConfig.Key == "notice.medicine.isolation" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.medicine.isolation")
		} else if newNoticeConfig.Key == "notice.medicine.order" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.medicine.order")
		} else if newNoticeConfig.Key == "notice.medicine.reminder" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.medicine.reminder")
		} else if newNoticeConfig.Key == "notice.medicine.alarm" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.medicine.alarm")
			newNoticeScene = noticeMedicineAlarmScene(ctx, newNoticeConfig.Key, newNoticeConfig.State, newNoticeConfig.ForecastTime)
		} else if newNoticeConfig.Key == "notice.storehouse.alarm" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.storehouse.alarm")
		} else if newNoticeConfig.Key == "notice.subject.alert.threshold" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.subject.alert.threshold")
		} else if newNoticeConfig.Key == "notice.order.timeout" {
			newNoticeType = locales.Tr(ctx, "notifications.notice.order.timeout")
			newNoticeScene = locales.Tr(ctx, "notifications.notice.order.timeout.lateShipmentAlertSetting") + " : " + strconv.Itoa(newNoticeConfig.TimeoutDays) +
				" , " + locales.Tr(ctx, "notifications.notice.order.timeout.lateShipmentSendAlertSetting") + " : " + strconv.Itoa(newNoticeConfig.SendDays)
		}

		contentItem := []interface{}{
			newNoticeType,
			newNoticeRole,
			newNoticeContentConfiguration,
			newNoticeScene,
			newNoticeExcludeRecipientList,
		}
		content = append(content, contentItem)
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]ProjectNotificationsConfigurationReport_%s.xlsx", project.Number, envCode, now.Format("20060102150405"))
	return fileName, buffer.Bytes(), nil
}

// 角色
func noticeRoles(ctx *gin.Context, v []primitive.ObjectID) string {
	var roles []string
	if len(v) > 0 {
		var projectRolePermission []models.ProjectRolePermission
		var s []primitive.ObjectID
		for i := 0; i < len(v); i++ {
			s = append(s, v[i])
		}
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.D{{Key: "_id", Value: bson.M{"$in": s}}}}},
		}
		cus, _ := tools.Database.Collection("project_role_permission").Aggregate(nil, pipeline)
		_ = cus.All(nil, &projectRolePermission)

		for j := 0; j < len(projectRolePermission); j++ {
			roles = append(roles, projectRolePermission[j].Name)
		}
	}
	return strings.Join(roles, ",")
}

// 通知设置-内容配置
func noticeContentConfiguration(ctx *gin.Context, key string, v []string) string {
	result := make([]string, 0)
	if len(v) > 0 {
		for i := 0; i < len(v); i++ {
			if v[i] == "group" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.group"))
			} else if v[i] == "random_number" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.randomizationNumber"))
			} else if v[i] == "projectNumber" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.projectNumber"))
			} else if v[i] == "projectName" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.projectName"))
			} else if v[i] == "siteNumber" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.siteNumber"))
			} else if v[i] == "siteName" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.siteName"))
			}
		}
	}
	return strings.Join(result, ",")
}

func noticeMedicineAlarmScene(ctx *gin.Context, key string, v []string, forecastTime *int) string {
	var result []string
	for i := 0; i < len(v); i++ {
		if v[i] == "order.no_automatic_title" {
			result = append(result, locales.Tr(ctx, "notifications.notice.medicine.alarm.scene.no_automatic_title"))
		} else if v[i] == "order.forecast_title" {
			result = append(result, locales.Tr(ctx, "notifications.notice.medicine.alarm.scene.forecast_title")+strconv.Itoa(*forecastTime))
		}
	}
	return strings.Join(result, ",")
}

// 通知设置-场景(不包含订单超时场景、中心警戒)
func noticeScene(ctx *gin.Context, key string, v []string) string {
	var result []string

	switch key {
	case "notice.subject.add":
		return ""
	case "notice.subject.random":
		return ""
	case "notice.subject.signOut":
		return ""
	case "notice.subject.replace":
		return ""
	case "notice.subject.screen":
		for i := 0; i < len(v); i++ {
			if v[i] == "subject.screen.success" {
				result = append(result, locales.Tr(ctx, "notifications.subject.screen.success"))
			} else if v[i] == "subject.screen.fail" {
				result = append(result, locales.Tr(ctx, "notifications.subject.screen.fail"))
			}
		}
		return strings.Join(result, ",")
	case "notice.subject.update":
		for i := 0; i < len(v); i++ {
			if v[i] == "subject.update.form_factor" {
				result = append(result, locales.Tr(ctx, "notifications.subject.update.form.factor"))
			} else if v[i] == "subject.update.screen" {
				result = append(result, locales.Tr(ctx, "notifications.subject.update.screen"))
			} else if v[i] == "subject.update.stop" {
				result = append(result, locales.Tr(ctx, "notifications.subject.update.stop"))
			} else if v[i] == "subject.update.finish" {
				result = append(result, locales.Tr(ctx, "notifications.subject.update.finish"))
			} else if v[i] == "subject.update.shortname" {
				result = append(result, locales.Tr(ctx, "notifications.subject.update.shortname"))
			}
		}
		return strings.Join(result, ",")
	case "notice.subject.dispensing":
		for i := 0; i < len(v); i++ {
			if v[i] == "dispensing.plan-title" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.Dispense"))
			} else if v[i] == "dispensing.unscheduled-plan-title" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.unscheduledDispense"))
			} else if v[i] == "dispensing.reissue-title" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.reDispense"))
			} else if v[i] == "dispensing.replace-title" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.replaceIP"))
			} else if v[i] == "dispensing.retrieval-title" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.retrieveIP"))
			} else if v[i] == "dispensing.register-title" {
				result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.actuallyUsedIP"))
			}
		}
		return strings.Join(result, ",")
	case "notice.subject.alarm":
		return ""
	case "notice.subject.unblinding":
		return ""
	case "notice.medicine.isolation":
		for i := 0; i < len(v); i++ {
			if v[i] == "medicine.freeze.title" {
				result = append(result, locales.Tr(ctx, "notifications.isolation.scene.quarantine"))
			} else if v[i] == "medicine.freeze.release" {
				result = append(result, locales.Tr(ctx, "notifications.isolation.scene.release"))
			}
		}
		return strings.Join(result, ",")
	case "notice.medicine.order":
		for i := 0; i < len(v); i++ {
			if v[i] == "order.approval.add-title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.apply"))
			} else if v[i] == "order.approval.failed-title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.applicationApprovalFailed"))
			} else if v[i] == "order.medicine_order_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.createManualOrder"))
			} else if v[i] == "order.cancel_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.cancel"))
			} else if v[i] == "order.no_automatic_success_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.confirm"))
			} else if v[i] == "order.close_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.close"))
			} else if v[i] == "order.send_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.deliver"))
			} else if v[i] == "order.receive_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.receive"))
			} else if v[i] == "order.end_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.termination"))
			} else if v[i] == "order.lost_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.lost"))
			} else if v[i] == "order.automatic_success_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.createAutomaticOrder"))
			} else if v[i] == "order.automatic_error_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.creationFailedAutomaticOrder"))
			} else if v[i] == "order.automatic_alarm_title" {
				result = append(result, locales.Tr(ctx, "notifications.order.scene.automaticOrderAlarm"))
			}
		}
		return strings.Join(result, ",")
	case "notice.medicine.reminder":
		return ""
	case "notice.medicine.alarm":
		return ""
	case "notice.storehouse.alarm":
		return ""
	case "notice.subject.alert.threshold":
		return ""
	case "notice.order.timeout":
		return strings.Join(result, ",")
	default:
		return ""
	}
}

func getPeriod(ctx *gin.Context, EnvID primitive.ObjectID, attributes []models.Attribute, timeZone float64) (map[primitive.ObjectID]models.Period, error) {

	mapPeriod := map[primitive.ObjectID]models.Period{}

	// 查询访视配置
	var visitCycles []models.VisitCycle
	cursor, err := tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": EnvID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询所有中心
	// 查询所有受试者
	var projectSites []models.ProjectSite
	cursor, err = tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": EnvID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询所有受试者
	cursor, err = tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"env_id": EnvID, "deleted": bson.M{"$ne": true}}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing",
			"let":  bson.M{"subject_id": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
				bson.M{"$sort": bson.D{{"serial_number", 1}}},
			},
			"as": "dispensing",
		}}},
		{{"$project", bson.M{
			"_id":                          1,
			"cohort_id":                    1,
			"project_site_id":              1,
			"info":                         1,
			"random_time":                  1,
			"dispensing.dispensing_time":   1,
			"dispensing.status":            1,
			"dispensing.visit_sign":        1,
			"dispensing.visit_info.number": 1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjectDispensing []models.SubjectDispensing
	fmt.Println(time.Now().UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05.999"))

	err = cursor.All(nil, &subjectDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	fmt.Println(time.Now().UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05.999"))

	// TODO 7064
	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
		}
	}

	for _, subject := range subjectDispensing {

		intTimeZone := timeZone

		projectSiteP, ok := slice.Find(projectSites, func(index int, item models.ProjectSite) bool {
			return item.ID == subject.ProjectSiteID
		})
		if !ok {
			continue
		}
		projectSite := *projectSiteP
		if projectSite.Tz != "" {
			//intTimeZone, _ = strconv.Atoi(strings.Replace(projectSite.TimeZone, "UTC", "", 1))
			offsetString, e := tools.GetLocationFloat(projectSite.Tz)
			if e != nil {
				return nil, errors.WithStack(e)
			}
			intTimeZone = offsetString
		}

		afterRandom := false
		interval := float64(0)
		lastTime := time.Duration(0)
		visitCycleP, ok := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
			return item.CohortID == subject.CohortID
		})

		if !ok {
			continue
		}

		attributeP, ok := slice.Find(attributes, func(index int, item models.Attribute) bool {
			return item.CohortID == subject.CohortID
		})
		if !ok {
			continue
		}

		visitCycle := *visitCycleP
		attribute := *attributeP
		randomTime := subject.RandomTime
		firstTime := time.Duration(0)
		for _, dispensing := range subject.Dispensing {
			if !dispensing.VisitSign {
				visitCycleInfo := models.VisitCycleInfo{}
				visitCycleInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
					return item.Number == dispensing.VisitInfo.Number
				})
				if ok {
					visitCycleInfo = *visitCycleInfoP
				}
				// 计算窗口期最小窗口时间
				// TODO 7064
				if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
					subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
				}
				if firstTime == 0 && dispensing.DispensingTime != 0 && visitCycleInfo.Interval != nil && randomTime == 0 {
					randomTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfo.Interval)).Unix())
					firstTime = dispensing.DispensingTime
				}
				period := handlePeriod(afterRandom, visitCycle.VisitType, visitCycleInfo, randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
				if dispensing.Status == 2 {
					interval = 0
					lastTime = dispensing.DispensingTime
				}
				if dispensing.VisitInfo.Random {
					afterRandom = true
				}
				mapPeriod[dispensing.ID] = period
			}
		}

	}
	return mapPeriod, nil
}

func typeRandomKeyGetRandomTime(item models.ExportSubjectNumber, zone float64) (string, error) {
	randomTime := ""
	if item.RandomTime != 0 {
		if item.Tz != "" {
			timeStr, err := tools.GetLocationUtc(item.Tz, item.RandomTime)
			if err != nil {
				return "", errors.WithStack(err)
			}
			randomTime = timeStr

		} else {
			hours := time.Duration(zone)
			minutes := time.Duration((zone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			randomTime = time.Unix(item.RandomTime, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			//strTimeZone := fmt.Sprintf("UTC%+d", zone)
			strTimeZone := tools.FormatOffsetToZoneString(zone)
			randomTime = randomTime + "(" + strTimeZone + ")"
		}
	}
	return randomTime, nil
}

func typeRandomKeyGetGroupGroup(haveSubGroup, isBlindedRole bool, attribute models.Attribute, item models.ExportSubjectNumber, randomLists []models.RandomList) (string, string, string) {
	group := tools.BlindData
	subGroup := ""
	groupCode := tools.BlindData
	if !isBlindedRole || !attribute.AttributeInfo.Blind {
		if item.SubGroupName != "" {
			group = item.ParGroupName
		} else {
			group = item.Group
		}
		if group == "" {
			groupCode = ""
		}
	}
	currentRandomListID := item.RegisterRandomListID
	if !item.RandomListID.IsZero() {
		currentRandomListID = item.RandomListID
	}
	randomListP, _ := slice.Find(randomLists, func(index int, list models.RandomList) bool {
		return currentRandomListID == list.ID
	})
	if randomListP != nil {
		randomList := *randomListP
		//TODO 子组别
		groupCodeP, _ := slice.Find(randomList.Design.Groups, func(index int, group models.RandomListGroup) bool {
			return item.Group == group.Name
		})
		if !isBlindedRole || !attribute.AttributeInfo.Blind {
			if groupCodeP != nil {
				groupCodeStruct := *groupCodeP
				groupCode = groupCodeStruct.Code
			} else {
				groupCode = ""
			}
		}
		if haveSubGroup && groupCodeP != nil {
			listGroup := *groupCodeP
			if listGroup.Blind && isBlindedRole {
				subGroup = tools.BlindData
			} else {
				subGroup = listGroup.SubName
			}
		}
	} else {
		// 旧数据
		for _, randomList := range randomLists {
			//TODO 子组别
			groupCodeP, _ := slice.Find(randomList.Design.Groups, func(index int, group models.RandomListGroup) bool {
				return item.Group == group.Name
			})
			if haveSubGroup && groupCodeP != nil {
				listGroup := *groupCodeP
				if listGroup.Blind && isBlindedRole {
					subGroup = tools.BlindData
				} else {
					subGroup = listGroup.SubName
				}
			}
			if !isBlindedRole || !attribute.AttributeInfo.Blind {
				if groupCodeP != nil {
					groupCodeStruct := *groupCodeP
					groupCode = groupCodeStruct.Code
				} else {
					groupCode = ""
				}
				break
			}
		}
	}
	return group, subGroup, groupCode
}

func typeRandomKeyGetFactorFields(item models.ExportSubjectNumber, randomLists []models.RandomList) (factorFields []models.Field, err error) {
	currentRandomListID := item.RegisterRandomListID
	if !item.RandomListID.IsZero() {
		currentRandomListID = item.RandomListID
	}
	//如果登记randomListID 和随机randomListID都为空，取当前有效的随机表id
	if currentRandomListID.IsZero() {
		randomFilter := bson.M{
			"project_id": item.ProjectID,
			"env_id":     item.EnvironmentID,
			"status":     1,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": item.ProjectSiteID},
			}}
		if !item.CohortID.IsZero() {
			randomFilter["cohort_id"] = item.CohortID
		}
		opts := &options.FindOneOptions{Sort: bson.D{{"meta.created_at", 1}}}
		var randomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(nil, randomFilter, opts).Decode(&randomList)
		if err != nil && err != mongo.ErrNoDocuments {
			return factorFields, errors.WithStack(err)
		}
		currentRandomListID = randomList.ID
	}

	if !currentRandomListID.IsZero() {
		randomList, b := slice.Find(randomLists, func(index int, list models.RandomList) bool {
			return currentRandomListID == list.ID
		})
		if b && randomList.Design.Factors != nil {
			for i := 0; i < len(randomList.Design.Factors); i++ {
				dateFormat := ""
				if randomList.Design.Factors[i].DateFormat != nil {
					dateFormat = *randomList.Design.Factors[i].DateFormat
				}
				factorFields = append(factorFields, models.Field{
					CohortId: randomList.CohortID,
					IsCalc:   randomList.Design.Factors[i].IsCalc,
					CalcType: randomList.Design.Factors[i].CalcType,
					Name:     randomList.Design.Factors[i].Name,
					//InputLabel:       randomList.Design.Factors[i].InputLabel,
					//InputWeightLabel: randomList.Design.Factors[i].InputWeightLabel,
					//InputHeightLabel: randomList.Design.Factors[i].InputHeightLabel,
					Round:          randomList.Design.Factors[i].Round,
					CustomFormulas: randomList.Design.Factors[i].CustomFormulas,
					Label:          randomList.Design.Factors[i].Label,
					Type:           randomList.Design.Factors[i].Type,
					Options:        randomList.Design.Factors[i].Options,
					Modifiable:     true, // 标记可修改
					Stratification: true, // 标记是分层因素
					Status:         randomList.Design.Factors[i].Status,
					DateFormat:     dateFormat,
				})
			}
		}
	}
	return factorFields, nil
}

func ExportConfigureReportPdf(ctx *gin.Context, projectID string, envID string, cohortIds []string, roleId string, now time.Time) (models.ConfigureReport, error) {

	var pdfData models.ConfigureReport

	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return pdfData, err
	}

	user, err := tools.Me(ctx)
	if err != nil {
		return pdfData, err
	}
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return pdfData, err
	}
	hour := time.Duration(timeZone)
	minute := time.Duration((timeZone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	date := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")

	envName := ""
	cohortNameList := make([]string, 0)
	if project.Environments != nil && len(project.Environments) > 0 {
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				envName = environment.Name
				if environment.Cohorts != nil && len(environment.Cohorts) > 0 && cohortIds != nil && len(cohortIds) > 0 {
					for _, cohortId := range cohortIds {
						for _, cohort := range environment.Cohorts {
							if cohort.ID.Hex() == cohortId {
								cohortNameList = append(cohortNameList, models.GetCohortReRandomName(cohort))
							}
						}
					}
				}
			}
		}
	}

	userName := ""
	if user.Unicode == 0 {
		userName = user.Name
	} else {
		userName = fmt.Sprintf("%s(%d)", user.Name, user.Unicode)
	}

	pdfData = models.ConfigureReport{
		Sponsor:                          project.Sponsor,
		ProjectNumber:                    project.Number,
		ConfigureReport:                  locales.Tr(ctx, "export.random_config.export"),
		Env:                              envName,
		GenerationTime:                   locales.Tr(ctx, "export.random_config.createDate"),
		CreateDate:                       date,
		Generator:                        locales.Tr(ctx, "export.random_config.createBy"),
		CreateBy:                         userName,
		Directory:                        locales.Tr(ctx, "export.random_config.directory"),
		Summary:                          locales.Tr(ctx, "export.random_config.summary"),
		SummaryDetails:                   locales.Tr(ctx, "export.random_config.report"),
		BasicInformation:                 locales.Tr(ctx, "configureReport.basicInformation"),
		ProjectTimeZoneLabel:             locales.Tr(ctx, "configureReport.basicInformation.projectTimeZone"),
		ProjectTypeLabel:                 locales.Tr(ctx, "configureReport.basicInformation.projectType"),
		ProjectOrderCheckLabel:           locales.Tr(ctx, "configureReport.basicInformation.projectOrderCheck"),
		ProjectOrderConfirmationLabel:    locales.Tr(ctx, "configureReport.basicInformation.projectOrderConfirmation"),
		ProjectDeIsolationApprovalLabel:  locales.Tr(ctx, "configureReport.basicInformation.projectDeIsolationApproval"),
		ProjectUnblindingControlLabel:    locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl"),
		ProjectOrderApprovalControlLabel: locales.Tr(ctx, "configureReport.basicInformation.projectOrderApprovalControl"),
		ProjectNoticeLabel:               locales.Tr(ctx, "configureReport.basicInformation.projectNotice"),
		ProjectConnectEdcLabel:           locales.Tr(ctx, "configureReport.basicInformation.projectConnectEdc"),
		ProjectPushModeLabel:             locales.Tr(ctx, "configureReport.basicInformation.projectPushMode"),
		ProjectSynchronizationModeLabel:  locales.Tr(ctx, "configureReport.basicInformation.projectSynchronizationMode"),
		GeneralSituation:                 locales.Tr(ctx, "configureReport.generalSituation"),
		AllOfThem:                        locales.Tr(ctx, "configureReport.allOfThem"),
		Name:                             locales.Tr(ctx, "configureReport.name"),
		CohortNameList:                   cohortNameList,
	}
	//项目时区
	t, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
	pdfData.ProjectTimeZone = t
	//if project.ProjectInfo.TimeZone == 0 {
	//	pdfData.ProjectTimeZone = "UTC"
	//} else if project.ProjectInfo.TimeZone > 0 {
	//	pdfData.ProjectTimeZone = "UTC+" + strconv.Itoa(project.ProjectInfo.TimeZone)
	//} else if project.ProjectInfo.TimeZone < 0 {
	//	pdfData.ProjectTimeZone = "UTC" + strconv.Itoa(project.ProjectInfo.TimeZone)
	//}
	//项目类型
	if project.ProjectInfo.Type == 1 {
		pdfData.ProjectType = locales.Tr(ctx, "configureReport.basicInformation.projectType.basicStudy")
	} else if project.ProjectInfo.Type == 2 {
		pdfData.ProjectType = locales.Tr(ctx, "configureReport.basicInformation.projectType.cohortStudy")
	} else if project.ProjectInfo.Type == 3 {
		pdfData.ProjectType = locales.Tr(ctx, "configureReport.basicInformation.projectType.reRandomizationStudy")
	}

	//中心研究产品库存核查
	if project.ProjectInfo.OrderCheck == 1 {
		pdfData.ProjectOrderCheck = locales.Tr(ctx, "configureReport.basicInformation.projectOrderCheck.timing")
	} else if project.ProjectInfo.OrderCheck == 2 {
		pdfData.ProjectOrderCheck = locales.Tr(ctx, "configureReport.basicInformation.projectOrderCheck.realTime")
	} else if project.ProjectInfo.OrderCheck == 3 {
		pdfData.ProjectOrderCheck = locales.Tr(ctx, "configureReport.basicInformation.projectOrderCheck.notApplicable")
	}

	//中心回收订单确认
	if project.ProjectInfo.OrderConfirmation == 1 {
		pdfData.ProjectOrderConfirmation = locales.Tr(ctx, "configureReport.basicInformation.projectOrderConfirmation.open")
	} else if project.ProjectInfo.OrderConfirmation == 0 {
		pdfData.ProjectOrderConfirmation = locales.Tr(ctx, "configureReport.basicInformation.projectOrderConfirmation.close")
	}

	//解隔离审批
	if project.ProjectInfo.DeIsolationApproval == 1 {
		pdfData.ProjectDeIsolationApproval = locales.Tr(ctx, "configureReport.basicInformation.projectDeIsolationApproval.open")
	} else if project.ProjectInfo.DeIsolationApproval == 0 {
		pdfData.ProjectDeIsolationApproval = locales.Tr(ctx, "configureReport.basicInformation.projectDeIsolationApproval.close")
	}

	unblindingList := make([]string, 0)
	//揭盲控制
	if project.ProjectInfo.UnblindingControl == 1 {
		//紧急揭盲
		if project.ProjectInfo.UnblindingType == 1 {
			//确认方式 短信
			if project.ProjectInfo.UnblindingSms == 1 {
				str := locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding") + "-" + locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding.sms")
				unblindingList = append(unblindingList, str)
			}
			//确认方式 流程操作
			if project.ProjectInfo.UnblindingProcess == 1 {
				str := locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding") + "-" + locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding.process")
				unblindingList = append(unblindingList, str)
			}
			//揭盲码
			if project.ProjectInfo.UnblindingCode == 1 {
				str := locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding") + "-" + locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding.code")
				unblindingList = append(unblindingList, str)
			}
		}
		//PV揭盲
		if project.ProjectInfo.PvUnblindingType == 1 {
			//确认方式 短信
			if project.ProjectInfo.PvUnblindingSms == 1 {
				str := locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.pvUnblinding") + "-" + locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.pvUnblinding.sms")
				unblindingList = append(unblindingList, str)
			}
			//确认方式 流程操作
			if project.ProjectInfo.PvUnblindingProcess == 1 {
				str := locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.pvUnblinding") + "-" + locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.pvUnblinding.process")
				unblindingList = append(unblindingList, str)
			}
		}
		if unblindingList != nil && len(unblindingList) > 0 {
			unblindingStr := strings.Join(unblindingList, ", ")
			pdfData.ProjectUnblindingControl = unblindingStr
		} else {
			pdfData.ProjectUnblindingControl = locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.false")
		}
	} else if project.ProjectInfo.UnblindingControl == 0 {
		pdfData.ProjectUnblindingControl = locales.Tr(ctx, "configureReport.basicInformation.projectUnblindingControl.false")
	}

	//研究中心订单申请
	if project.ProjectInfo.OrderApprovalSms == 0 && project.ProjectInfo.OrderApprovalProcess == 0 {
		pdfData.ProjectOrderApprovalControl = locales.Tr(ctx, "configureReport.basicInformation.projectOrderApprovalControl.false")
	} else {
		if project.ProjectInfo.OrderApprovalSms == 1 {
			pdfData.ProjectOrderApprovalControl = locales.Tr(ctx, "configureReport.basicInformation.projectOrderApprovalControl.sms")
		}
		if project.ProjectInfo.OrderApprovalProcess == 1 {
			pdfData.ProjectOrderApprovalControl = locales.Tr(ctx, "configureReport.basicInformation.projectOrderApprovalControl.process")
		}
	}

	//项目通知
	if count, _ := tools.Database.Collection("project_notice").CountDocuments(nil, bson.M{"project_id": projectOID, "env_id": envOID}); count > 0 {
		var projectNotice models.ProjectNotice
		err = tools.Database.Collection("project_notice").FindOne(nil, bson.M{"project_id": projectOID, "env_id": envOID}).Decode(&projectNotice)
		if err != nil {
			return pdfData, err
		}
		if projectNotice.Open {
			pdfData.ProjectNotice = locales.Tr(ctx, "configureReport.basicInformation.projectNotice.open")
		} else {
			pdfData.ProjectNotice = locales.Tr(ctx, "configureReport.basicInformation.projectNotice.close")
		}

	} else {
		pdfData.ProjectNotice = locales.Tr(ctx, "configureReport.basicInformation.projectNotice.close")
	}

	//对接EDC ---- 如对接，显示EDC供应商值，否则
	if project.ProjectInfo.ConnectEdc == 1 {
		if project.ProjectInfo.EdcSupplier == 1 {
			pdfData.ProjectConnectEdc = "Clinflash  EDC"
		} else if project.ProjectInfo.EdcSupplier == 2 {
			pdfData.ProjectConnectEdc = "Medidata Rave  EDC"
		}
		//数据推送方式
		if project.ProjectInfo.PushMode == 1 {
			pdfData.ProjectPushMode = locales.Tr(ctx, "configureReport.basicInformation.projectPushMode.real")
			//同步方式
			if project.ProjectInfo.SynchronizationMode == 1 {
				pdfData.ProjectSynchronizationMode = locales.Tr(ctx, "configureReport.basicInformation.projectSynchronizationMode.screen")
			} else if project.ProjectInfo.SynchronizationMode == 2 {
				pdfData.ProjectSynchronizationMode = locales.Tr(ctx, "configureReport.basicInformation.projectSynchronizationMode.random")
			}
		} else if project.ProjectInfo.PushMode == 2 {
			pdfData.ProjectPushMode = locales.Tr(ctx, "configureReport.basicInformation.projectPushMode.active")
			pdfData.ProjectSynchronizationMode = "-"
		}
	} else if project.ProjectInfo.ConnectEdc == 0 || project.ProjectInfo.ConnectEdc == 2 {
		pdfData.ProjectConnectEdc = "-"
		pdfData.ProjectPushMode = "-"
		pdfData.ProjectSynchronizationMode = "-"
	}

	//是否群组、再随机项目
	if project.ProjectInfo.Type == 1 {
		pdfData.IsGroupStage = false
	} else {
		pdfData.IsGroupStage = true
		if project.ProjectInfo.Type == 2 {
			pdfData.CohortOrStage = locales.Tr(ctx, "configureReport.cohort")
		} else if project.ProjectInfo.Type == 3 {
			pdfData.CohortOrStage = locales.Tr(ctx, "configureReport.stage")
		}
	}

	// 创建一个空的map[string]interface{}切片
	cohortDatas := make([]models.ConfigureDetail, 0)
	if cohortIds != nil && len(cohortIds) > 0 {
		for index, cohortId := range cohortIds {
			cohortData, err := ConfigureReportHtmlData(ctx, projectID, envID, cohortId, roleId, now)
			if err != nil {
				return pdfData, errors.WithStack(err)
			}
			cohortData.Index = strconv.Itoa(4 + index)
			cohortDatas = append(cohortDatas, cohortData)
		}
	} else {
		cohortData, err := ConfigureReportHtmlData(ctx, projectID, envID, "", roleId, now)
		if err != nil {
			return pdfData, errors.WithStack(err)
		}
		cohortData.Index = "3"
		cohortDatas = append(cohortDatas, cohortData)
	}
	pdfData.ConfigureDetailList = cohortDatas

	//bytes, err := tools.ExportHtmlPdf(ctx, pdfData)
	//if err != nil {
	//	return "", nil, err
	//}
	//
	//envCode := getEnvName(project, envOID)
	//fileName := fmt.Sprintf("%s[%s]ConfigureReport_%s.pdf", project.Number, envCode, now.Format("20060102150405"))
	return pdfData, nil
}

func ExportForecastReport(ctx *gin.Context, projectID string, envID string, roleID string, startDateTitle, endDateTitle string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	startDate := strings.ReplaceAll(startDateTitle, "/", "-")
	endDate := strings.ReplaceAll(endDateTitle, "/", "-")
	blindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	var attributes []models.Attribute
	cursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP

	var drugConfigures []models.DrugConfigure
	cursor, err = tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)

	}
	err = cursor.All(nil, &drugConfigures)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var visitCycles []models.VisitCycle
	cursor, err = tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	cursor, err = tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID}, &options.FindOptions{
		Projection: bson.M{
			"_id":       1,
			"env_id":    1,
			"number":    1,
			"name":      models.ProjectSiteNameZhBson(),
			"name_en":   models.ProjectSiteNameEnBson(),
			"time_zone": 1,
		},
		Sort: bson.D{{"number", 1}},
	})

	cursor, err = tools.Database.Collection("project_site").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"env_id": envOID}}},
		{{"$lookup", bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "project_storehouse"}}},
		{{"$lookup", bson.M{"from": "storehouse", "localField": "project_storehouse.storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$storehouse", "preserveNullAndEmptyArrays": true}}},
		{{"$project", bson.M{
			"_id":        1,
			"env_id":     1,
			"number":     1,
			"name":       models.ProjectSiteNameZhBson(),
			"name_en":    models.ProjectSiteNameEnBson(),
			"time_zone":  1,
			"store_name": "$storehouse.name",
		}}},
	})

	var projectSites []models.ProjectSiteStore
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	content := make([][]interface{}, 0)
	// 表头
	timeTitle := []interface{}{}
	timeTitle = append(timeTitle, locales.Tr(ctx, "report.forecast.date")+fmt.Sprintf("： %s-%s", startDateTitle, endDateTitle))
	title := []interface{}{}

	for _, field := range data.ForecastingPredictionReportReport.DefaultFields {
		if project.Type == 1 {
			if field.Key == data.ReportAttributesRandomCohort || field.Key == data.ReportAttributesRandomStage {
				continue
			}
		}
		if project.Type == 2 {
			if field.Key == data.ReportAttributesRandomStage {
				continue
			}
		}
		if project.Type == 3 {
			if field.Key == data.ReportAttributesRandomCohort {
				continue
			}
		}
		if field.Key == data.ReportForecastPeriod {
			title = append(title, locales.Tr(ctx, field.Key))
			title = append(title, "")

		} else {
			title = append(title, locales.Tr(ctx, field.Key))

		}
	}
	content = append(content, title)
	timeZone, err := tools.GetTimeZone(projectOID)
	IsBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)

	}

	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")

		}
	}

	for _, site := range projectSites {
		strTimeZone, err := tools.GetSiteTimeZoneInfo(site.ProjectSite)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		if strTimeZone == "" {
			//strTimeZone = fmt.Sprintf("UTC%+d", timeZone)
			strTimeZone = tools.FormatOffsetToZoneStringUtc(timeZone)
		}
		//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
		//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
		intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

		for _, attribute := range attributes {
			cohortName := ""
			if !attribute.CohortID.IsZero() {

				cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return item.ID == attribute.CohortID
				})
				if cohortP == nil {
					continue
				}
				cohort := *cohortP
				cohortName = cohort.Name
			}

			drugConfigureP, b := slice.Find(drugConfigures, func(index int, item models.DrugConfigure) bool {
				return item.CohortID == attribute.CohortID
			})
			if b {
				drugConfigure := *drugConfigureP

				visitCycleP, b2 := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
					return item.CohortID == attribute.CohortID
				})
				if b2 {
					visitCycle := *visitCycleP
					visitCycleInfoMap := make(map[string]models.VisitCycleInfo)
					for _, v := range visitCycle.Infos {
						visitCycleInfoMap[v.Number] = v
					}

					VisitGroupCount := task.MaxCount(drugConfigure)

					subjectDispensing, err := task.GetSubjectDispensing(nil, attribute, site.ID)
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					for _, subject := range subjectDispensing {
						lastTime := time.Duration(0)
						afterRandom := false
						interval := float64(0)

						if subject.Group == "" {
							subject.Group = "N/A"
						}

						if attribute.AttributeInfo.Random && subject.Group != "" && subject.RegisterGroup != "" {
							subject.Group = subject.RegisterGroup
						}
						randomTime := subject.RandomTime
						firstTime := time.Duration(0)
						for _, dispensing := range subject.Dispensing {
							if !dispensing.VisitSign {

								if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
									randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
								}
								if firstTime == 0 && dispensing.DispensingTime != 0 && visitCycleInfoMap[dispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
									randomTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfoMap[dispensing.VisitInfo.Number].Interval)).Unix())
									firstTime = dispensing.DispensingTime
								}
								period := handlePeriod(afterRandom, visitCycle.VisitType, visitCycleInfoMap[dispensing.VisitInfo.Number], randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
								if dispensing.Status == 2 {
									interval = 0
									lastTime = dispensing.DispensingTime
								}

								if period.LineTime == "" && visitCycle.VisitType == 0 && dispensing.VisitInfo.Random && subject.Group != "" && dispensing.VisitInfo.Dispensing && dispensing.Status == 1 { //随机访视  并且随机过
									hours := time.Duration(intTimeZone)
									minutes := time.Duration((intTimeZone - float64(hours)) * 60)

									duration := hours*time.Hour + minutes*time.Minute
									randomTime := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
									period.LineTime = randomTime
									period.MinPeriod = randomTime
									period.MaxPeriod = randomTime
								}

								if period.MinPeriod <= endDate && period.MaxPeriod >= startDate && dispensing.Status == 1 {
									// 计算数量
									NameCount := SubjectMedicineCount(drugConfigure, VisitGroupCount, dispensing, subject)

									// 写入表格
									contentItem := ContentItem(blindedRole, cohortName, NameCount, subject, dispensing, site, period, IsBlindDrugMap)
									content = append(content, contentItem...)
								}

							}
							if dispensing.VisitInfo.Random {
								afterRandom = true
							}
						}

					}
				}
			}

		}
	}

	fileName := fmt.Sprintf("%s[%s]ForecastingPredictionReport_%s.xlsx", project.Number, env.Name, now.Format("20060102150405"))
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	t := make([]interface{}, len(timeTitle))
	for i, item := range timeTitle {
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.MergeCell("F2", "G2")
	_ = streamWriter.SetRow("A1", t)
	for i := 1; i <= len(content); i++ {
		r := make([]interface{}, len(content[i-1]))
		for j := 0; j < len(content[i-1]); j++ {
			r[j] = excelize.Cell{Value: content[i-1][j]}
		}
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		_ = streamWriter.SetRow(cell, r)
	}
	_ = streamWriter.Flush()
	if err != nil {
		return "", nil, err
	}
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}

	return fileName, buffer.Bytes(), nil
}

func ExportVisitForecastReport(ctx *gin.Context, projectID string, envID string, cohortIDs, subjectIDs []interface{}, templateId string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	var subjects []primitive.ObjectID
	for _, subject := range subjectIDs {
		subjectOID, _ := primitive.ObjectIDFromHex(subject.(string))
		subjects = append(subjects, subjectOID)
	}

	var attributes []models.Attribute

	cursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP

	var visitCycles []models.VisitCycle
	cursor, err = tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	cursor, err = tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID}, &options.FindOptions{
		Projection: bson.M{
			"_id":       1,
			"env_id":    1,
			"number":    1,
			"name":      models.ProjectSiteNameZhBson(),
			"name_en":   models.ProjectSiteNameEnBson(),
			"time_zone": 1,
		},
		Sort: bson.D{{"number", 1}},
	})

	cursor, err = tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID})

	var projectSites []models.ProjectSite
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	content := make([][]interface{}, 0)
	// 表头
	title := []interface{}{}
	reportFields := []string{}

	subjectReplaceText := ""
	var attribute models.Attribute
	attributeMatch := bson.M{"customer_id": project.CustomerID, "project_id": projectOID, "env_id": envOID}
	if env.Cohorts != nil && len(env.Cohorts) > 0 {
		attributeMatch["cohort_id"] = env.Cohorts[0].ID
	}
	err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}
	subjectReplaceText = GetSubjectReplaceText(ctx, attribute)

	showRandomNumber := map[primitive.ObjectID]bool{}
	if len(cohortIDs) > 0 {
		attributes = slice.Filter(attributes, func(index int, item models.Attribute) bool {
			_, ok := slice.Find(cohortIDs, func(index int, cohortID interface{}) bool {
				return cohortID.(string) == item.CohortID.Hex()
			})
			return ok

		})
	}

	for _, attribute := range attributes {
		if attribute.AttributeInfo.IsRandomNumber {
			showRandomNumber[attribute.CohortID] = true
		}
	}

	if templateId != "" {
		var template models.CustomTemplate
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		err = tools.Database.Collection("custom_template").FindOne(nil, bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		reportFields = template.Fields
	} else {
		double := false
		for _, field := range data.VisitForecastRecordReportReport.MultiDefaultFields {
			if field.Type == project.Type && !double {
				double = true
				for _, defaultField := range field.DefaultFields {
					reportFields = append(reportFields, defaultField.Key)
				}
			}
		}
	}

	for _, field := range reportFields {
		if field == data.ReportForecastPeriod {
			title = append(title, locales.Tr(ctx, field))
			title = append(title, "")
			continue

		}
		if field == data.ReportAttributesInfoSubjectNumber && subjectReplaceText != "" {
			title = append(title, subjectReplaceText)
			continue
		}
		if field == data.ReportAttributesRandomNumber && len(showRandomNumber) == 0 {
			continue
		}
		title = append(title, locales.Tr(ctx, field))

	}

	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")

		}
	}
	userMap := map[primitive.ObjectID]string{}
	type UserInfo struct {
		ID      primitive.ObjectID `bson:"_id"`
		Email   string             `bson:"email"`
		Name    string             `bson:"name"`
		Unicode int32              `bson:"unicode"`
	}
	var users []UserInfo

	cursor, err = tools.Database.Collection("user_project_environment").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{
			"env_id": envOID,
		}}},
		{{"$lookup", bson.M{
			"from":         "user",
			"localField":   "user_id",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{Key: "$unwind", Value: "$user"}},
		{{"$project", bson.M{
			"_id":     "$user._id",
			"email":   "$user.info.email",
			"name":    "$user.info.name",
			"unicode": "$user.info.unicode",
		}}},
	})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	for _, u := range users {
		userMap[u.ID] = u.Name
	}

	for _, site := range projectSites {
		strTimeZone, err := tools.GetSiteTimeZoneInfo(site)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		if strTimeZone == "" {
			t, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
			strTimeZone = t
		}
		//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
		//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
		intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

		for _, attribute := range attributes {
			cohortName := ""
			if !attribute.CohortID.IsZero() {

				cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return item.ID == attribute.CohortID
				})
				if cohortP == nil {
					continue
				}
				cohort := *cohortP
				cohortName = cohort.Name
			}

			visitCycleP, b2 := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
				return item.CohortID == attribute.CohortID
			})
			if b2 {
				visitCycle := *visitCycleP
				visitCycleInfoMap := make(map[string]models.VisitCycleInfo)
				for _, v := range visitCycle.Infos {
					visitCycleInfoMap[v.Number] = v
				}

				subjectDispensing, err := task.GetSubjectDispensingNoticeRecord(nil, attribute, site.ID, subjects)
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				for _, subject := range subjectDispensing {
					lastTime := time.Duration(0)
					afterRandom := false
					interval := float64(0)

					if subject.Group == "" {
						subject.Group = "N/A"
					}

					if !attribute.AttributeInfo.IsRandomNumber {
						subject.RandomNumber = ""
					}

					if attribute.AttributeInfo.Random && subject.Group != "" && subject.RegisterGroup != "" {
						subject.Group = subject.RegisterGroup
					}
					randomTime := subject.RandomTime
					firstTime := time.Duration(0)
					for _, dispensing := range subject.Dispensing {
						if !dispensing.VisitSign {

							if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
								randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
							}
							if firstTime == 0 && dispensing.DispensingTime != 0 && visitCycleInfoMap[dispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
								randomTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfoMap[dispensing.VisitInfo.Number].Interval)).Unix())
								firstTime = dispensing.DispensingTime
							}
							period := handlePeriod(afterRandom, visitCycle.VisitType, visitCycleInfoMap[dispensing.VisitInfo.Number], randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
							if dispensing.Status == 2 {
								interval = 0
								lastTime = dispensing.DispensingTime
							}

							if period.LineTime == "" && visitCycle.VisitType == 0 && dispensing.VisitInfo.Random && subject.Group != "" && dispensing.VisitInfo.Dispensing { //随机访视  并且随机过
								hours := time.Duration(intTimeZone)
								minutes := time.Duration((intTimeZone - float64(hours)) * 60)
								duration := hours*time.Hour + minutes*time.Minute
								randomTime := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
								period.LineTime = randomTime
								period.MinPeriod = randomTime
								period.MaxPeriod = randomTime
							}

							subjectVisitSummary := models.SubjectVisitSummary{}
							subjectVisitHomeSummary := models.SubjectVisitHomeSummary{}
							dispensingTime := ""
							if dispensing.VisitInfo.Dispensing {
								hours := time.Duration(intTimeZone)
								minutes := time.Duration((intTimeZone - float64(hours)) * 60)
								duration := hours*time.Hour + minutes*time.Minute
								formulaDate := time.Now().UTC().Add(duration).Format("2006-01-02")
								isDispensing := false

								if dispensing.Status != 1 {
									formulaDate = time.Unix(int64(dispensing.DispensingTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
									dispensingTime = time.Unix(int64(dispensing.DispensingTime), 0).UTC().Add(duration).UTC().Format("2006-01-02 15:04:05")
									dispensingTime = dispensingTime + strTimeZone

									isDispensing = true
								}

								_, status := getPeriodStatus(&subjectVisitSummary, &subjectVisitHomeSummary, period, formulaDate, isDispensing)
								statusStr := visitStatusChange(ctx, status)

								// 写入表格
								notices := slice.Filter(dispensing.VisitNotice, func(index int, item models.VisitNotice) bool {
									return item.Status == 1
								})

								if len(notices) == 0 && period.LineTime != "" {
									contentItem := ContentVisitReportItem(reportFields, project, cohortName, subject, dispensing, site, period, dispensingTime, statusStr, "", "", "", "", []string{}, len(showRandomNumber) == 0)
									content = append(content, contentItem...)
								}
								for _, notice := range dispensing.VisitNotice {
									if notice.Status != 1 {
										continue
									}
									sendContent := task.SwitchNoticeContent(ctx.GetHeader("Accept-Language"), notice.Content.Template, notice.Content.Day)
									pushTime := time.Unix(int64(notice.SendTime), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
									pushTime = pushTime + strTimeZone
									pushType := ""
									pushUser := ""
									if notice.UserID.IsZero() {
										pushUser = locales.Tr(ctx, "calendar.button.site.visit.matter.notice.history.people.system")
									} else {
										pushUser = userMap[notice.UserID]

									}
									if notice.PushType == 1 {
										pushType = locales.Tr(ctx, "report.subject.visit.app.notice")
									}
									if notice.PushType == 2 {
										pushType = locales.Tr(ctx, "report.subject.visit.message.notice")
									}

									sendUsers := []string{}
									for _, userID := range notice.NoticeUser {
										sendUsers = append(sendUsers, userMap[userID])
									}
									phones := []string{}
									for _, phone := range notice.PhoneUser {
										phones = append(phones, phone)
									}
									if len(phones) > 0 || len(sendUsers) > 0 {
										sendAll := append(sendUsers, phones...)
										contentItem := ContentVisitReportItem(reportFields, project, cohortName, subject, dispensing, site, period, dispensingTime, statusStr, sendContent, pushTime, pushUser, pushType, sendAll, len(showRandomNumber) == 0)
										content = append(content, contentItem...)

									}

								}
							}

						}
						if dispensing.VisitInfo.Random {
							afterRandom = true
						}
					}

				}
			}

		}
	}

	fileName := fmt.Sprintf("%s[%s]VisitStatisticsReport_%s.xlsx", project.Number, env.Name, now.Format("20060102150405"))
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	t := make([]interface{}, len(title))
	for i, item := range title {
		if item == locales.Tr(ctx, data.ReportForecastPeriod) {
			title1Hcell, _ := excelize.CoordinatesToCellName(i+1, 1)
			title1Vcell, _ := excelize.CoordinatesToCellName(i+2, 1)
			_ = streamWriter.MergeCell(title1Hcell, title1Vcell)
		}
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.SetRow("A1", t)

	//处理订单详情合并
	for i := 1; i <= len(content); i++ {
		r := make([]interface{}, len(content[i-1]))
		for j := 0; j < len(content[i-1]); j++ {
			r[j] = excelize.Cell{Value: content[i-1][j]}
		}
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		_ = streamWriter.SetRow(cell, r)
	}
	_ = streamWriter.Flush()
	if err != nil {
		return "", nil, err
	}
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}

	return fileName, buffer.Bytes(), nil
}

func ContentVisitReportItem(reportFields []string, project models.Project, cohortName string, subject models.SubjectDispensingVisitNotice, dispensing models.DispensingVisitNotice, site models.ProjectSite, period models.Period, dispensingTime, status, visitContent, pushTime, pushUser, pushType string, pushEmail []string, showRandomNumber bool) [][]interface{} {
	items := make([][]interface{}, 0)
	tmp := []interface{}{}

	for _, field := range reportFields {
		switch field {
		case data.ReportAttributesProjectNumber:
			tmp = append(tmp, project.Number)
		case data.ReportAttributesProjectName:
			tmp = append(tmp, project.Name)
		case data.ReportAttributesInfoSiteNumber:
			tmp = append(tmp, site.Number)
		case data.ReportAttributesInfoSiteName:
			tmp = append(tmp, site.Name)
		case data.ReportAttributesRandomCohort:
			tmp = append(tmp, cohortName)
		case data.ReportAttributesRandomStage:
			tmp = append(tmp, cohortName)
		case data.ReportAttributesInfoSubjectNumber:
			tmp = append(tmp, subject.Info[0].Value)
		case data.ReportAttributesRandomNumber:
			if !showRandomNumber {
				tmp = append(tmp, subject.RandomNumber)

			}
		case data.ReportAttributesDispensingCycleName:
			tmp = append(tmp, dispensing.VisitInfo.Number)
		case data.ReportForecastPeriod:
			tmp = append(tmp, period.MinPeriod)
			tmp = append(tmp, period.MaxPeriod)

		case data.ReportAttributesDispensingTime:
			tmp = append(tmp, dispensingTime)

		case data.ReportVisitForecastVisitStatus:
			tmp = append(tmp, status)
		case data.ReportVisitForecastNoticeContent:
			tmp = append(tmp, visitContent)
		case data.ReportVisitForecastNoticeTime:
			tmp = append(tmp, pushTime)
		case data.ReportVisitForecastNoticeUser:
			tmp = append(tmp, pushUser)
		case data.ReportVisitForecastNoticeType:
			tmp = append(tmp, pushType)
		case data.ReportVisitForecastNoticeEmail:
			if len(pushEmail) == 0 {
				tmp = append(tmp, "")
			} else {
				tmp = append(tmp, pushEmail)
			}
		}

	}
	items = append(items, tmp)
	return items
}

func visitStatusChange(ctx *gin.Context, status int) string {
	switch status {
	case OutSizeNotCompleted:
		return locales.Tr(ctx, "report.subject.visit.OutSizeNotCompleted")
	case InProgress:
		return locales.Tr(ctx, "report.subject.visit.InProgress")
	case OutSizeCompleted:
		return locales.Tr(ctx, "report.subject.visit.OutSizeCompleted")
	case Prepare:
		return locales.Tr(ctx, "report.subject.visit.Prepare")
	case CompletedOnSchedule:
		return locales.Tr(ctx, "report.subject.visit.CompletedOnSchedule")
	}
	return ""
}

func SubjectMedicineCount(drugConfigure models.DrugConfigure, visitGroupCount map[string][]task.MedicineCount, dispensing models.Dispensing, subject models.SubjectDispensing) []models.NameCount {
	NameCount := make([]models.NameCount, 0)
	for _, medicine := range visitGroupCount[dispensing.VisitInfo.VisitCycleInfoID.Hex()+subject.Group] {
		if medicine.IsFormula {
			forest, _ := task.ForecastMedicineCount(drugConfigure.Configures, subject, medicine.Medicine, dispensing.VisitInfo.VisitCycleInfoID)
			medicine.Count = forest
		}
		i := -1
		_, ok := slice.Find(NameCount, func(index int, item models.NameCount) bool {
			find := item.Name == medicine.Medicine
			if find {
				i = index
			}
			return find
		})
		if ok {
			if NameCount[i].Count < medicine.Count {
				NameCount[i].Count = medicine.Count
			}
		} else {
			NameCount = append(NameCount, models.NameCount{
				Name:  medicine.Medicine,
				Count: medicine.Count,
			})
		}

	}
	// 存在重复、取最大的值
	return NameCount
}

func ContentItem(blindedRole bool, cohortName string, nameCounts []models.NameCount, subject models.SubjectDispensing, dispensing models.Dispensing, site models.ProjectSiteStore, period models.Period, isBlindMedicine map[string]bool) [][]interface{} {
	items := make([][]interface{}, 0)
	nameCounts = slice.Filter(nameCounts, func(index int, item models.NameCount) bool {
		return item.Count != 0
	})
	for _, nameCount := range nameCounts {
		tmp := []interface{}{}
		tmp = append(tmp, site.StoreName)
		tmp = append(tmp, site.Number)
		tmp = append(tmp, site.Name)
		tmp = append(tmp, subject.Info[0].Value)
		tmp = append(tmp, dispensing.VisitInfo.Name)
		tmp = append(tmp, period.MinPeriod)
		tmp = append(tmp, period.MaxPeriod)
		if blindedRole && isBlindMedicine[nameCount.Name] {
			tmp = append(tmp, tools.BlindData)
		} else {
			tmp = append(tmp, nameCount.Name)
		}
		tmp = append(tmp, nameCount.Count)
		if blindedRole {
			tmp = append(tmp, tools.BlindData)
		} else {
			tmp = append(tmp, subject.Group)
		}
		tmp = append(tmp, cohortName)
		items = append(items, tmp)
	}
	return items
}
