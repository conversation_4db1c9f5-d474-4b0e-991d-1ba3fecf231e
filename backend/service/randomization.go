package service

import (
	"bytes"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/url"
	"os"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/maputil"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/duke-git/lancet/v2/convertor"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// RandomizationService ..
type RandomizationService struct{}

func (s *RandomizationService) ListForm(ctx *gin.Context, customerID string, envID string, cohortID string) (models.Form, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}
	var form models.Form
	if err := tools.Database.Collection("form").FindOne(nil, filter).Decode(&form); err != nil && err != mongo.ErrNoDocuments {
		return form, errors.WithStack(err)
	}
	opts := options.Find().SetProjection(bson.M{
		"info": 1,
	})
	type subjectResp struct {
		ID   primitive.ObjectID `json:"id" bson:"_id"`
		Info []models.Info      `json:"info"`
	}
	subjects := make([]*subjectResp, 0)
	cursor, err := tools.Database.Collection("subject").Find(nil, filter, opts)
	if err != nil {
		return models.Form{}, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return models.Form{}, errors.WithStack(err)
	}
	var drugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.Form{}, errors.WithStack(err)
	}
	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.Form{}, errors.WithStack(err)
	}
	var dispensings []models.Dispensing
	cursor, err = tools.Database.Collection("dispensing").Find(nil, bson.M{
		"env_id":    envOID,
		"cohort_id": cohortOID,
	})
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return models.Form{}, errors.WithStack(err)
	}
	for i := range form.Fields {
		//状态默认值
		if form.Fields[i].Status == nil {
			status := 1
			form.Fields[i].Status = &status
		}
		// 应用类型 默认值
		if form.Fields[i].ApplicationType == nil {
			applicationType := 1
			form.Fields[i].ApplicationType = &applicationType
		}
		if *(form.Fields[i].ApplicationType) == 1 || *(form.Fields[i].ApplicationType) == 4 {
			//判断是否有subject引用
			for _, subject := range subjects {
				for _, info := range subject.Info {
					if info.Name == form.Fields[i].Name {
						form.Fields[i].Used = true
						for j := range form.Fields[i].Options {
							if form.Fields[i].Type == "checkbox" && info.Value != nil {
								for _, v := range info.Value.(primitive.A) {
									if form.Fields[i].Options[j].Value == v {
										form.Fields[i].Options[j].Disable = true
									}
								}
							} else if (form.Fields[i].Type == "select" || form.Fields[i].Type == "radio") && form.Fields[i].Options[j].Value == info.Value {
								form.Fields[i].Options[j].Disable = true
							}
						}
					}
				}
			}
		} else if *(form.Fields[i].ApplicationType) == 2 {
			for _, configure := range drugConfigure.Configures {
				if configure.IsFormula && strings.Contains(configure.CustomerCalculation, form.Fields[i].Variable) {
					form.Fields[i].Used = true
				}
			}
		} else if *(form.Fields[i].ApplicationType) == 3 {
			if drugConfigureSetting.FormRecordList != nil && len(drugConfigureSetting.FormRecordList) > 0 {
				for _, formRecord := range drugConfigureSetting.FormRecordList {
					if formRecord.DoseFormId == form.Fields[i].ID.Hex() {
						form.Fields[i].Used = true
						if formRecord.SelectType == 2 {
							for j := range form.Fields[i].Options {
								form.Fields[i].Options[j].Disable = false
								if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" {
									if formRecord.VisitJudgmentList != nil && len(formRecord.VisitJudgmentList) > 0 {
										for _, visitJudgment := range formRecord.VisitJudgmentList {
											if form.Fields[i].Options[j].Value == visitJudgment.Name {
												form.Fields[i].Options[j].Disable = true
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	dispensingDoseFormUse(dispensings, &form)
	return form, nil
}

func dispensingDoseFormUse(dispensings []models.Dispensing, form *models.Form) {
	for _, dispensing := range dispensings {
		for _, item := range dispensing.DispensingMedicines {
			doseInfoUseItem(form, item.DoseInfo)
		}
		for _, item := range dispensing.CancelMedicinesHistory {
			doseInfoUseItem(form, item.DoseInfo)
		}
		for _, item := range dispensing.OtherDispensingMedicines {
			doseInfoUseItem(form, item.DoseInfo)
		}
		for _, item := range dispensing.OtherMedicinesHistory {
			doseInfoUseItem(form, item.DoseInfo)
		}

	}
	return
}

func doseInfoUseItem(form *models.Form, doseInfo *models.DoseInfo) {
	if doseInfo != nil && doseInfo.Form != nil {
		for i, formItem := range form.Fields {
			if doseInfo.Form.Key == formItem.Variable && formItem.ApplicationType != nil && *formItem.ApplicationType == 3 {
				form.Fields[i].Used = true
				for j, option := range form.Fields[i].Options {
					if !form.Fields[i].Options[j].Disable {
						form.Fields[i].Options[j].Disable = false
						if option.Value == doseInfo.Form.Value {
							form.Fields[i].Options[j].Disable = true
						}
					}

				}
			}

		}
	}
}

func (s *RandomizationService) GetRegisterForm(ctx *gin.Context, customerID string, projectID, envID string, cohortID string, siteID string) (map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	siteOID, _ := primitive.ObjectIDFromHex(siteID)
	filter := bson.M{"env_id": envOID}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if project.ProjectInfo.Type == 2 || project.ProjectInfo.Type == 3 {
		if !cohortOID.IsZero() {
			filter = bson.M{"env_id": envOID, "cohort_id": cohortOID}
		}
	}

	// 查询访视周期
	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	// 查属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 声明接受字段信息
	var fields []models.Field
	fields = append(fields, attribute.AttributeInfo.Field)

	// 查表单
	var form models.Form
	_ = tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
	if form.Fields != nil {
		for _, fm := range form.Fields {
			if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && (fm.Status == nil || *fm.Status == 1) {
				fields = append(fields, fm)
			}
		}
	}

	// 查询是否有分层因素
	randomFilter := filter
	randomFilter["status"] = 1
	if !siteOID.IsZero() {
		randomFilter["$or"] = bson.A{
			bson.M{"site_ids": siteOID},
			bson.M{"site_ids": nil},
		}
	}
	var randomList models.RandomList
	_ = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)

	if nil != randomList.Design.Factors {
		randomList.Design.Factors = slice.Filter(randomList.Design.Factors, func(index int, item models.RandomFactor) bool {
			if item.Status == nil {
				return true
			}
			return *item.Status != 2
		})
		for i := 0; i < len(randomList.Design.Factors); i++ {
			dateFormatP := randomList.Design.Factors[i].DateFormat
			dateFormat := ""
			if dateFormatP != nil {
				dateFormat = *dateFormatP
			}
			fields = append(fields, models.Field{
				IsCalc:   randomList.Design.Factors[i].IsCalc,
				CalcType: randomList.Design.Factors[i].CalcType,
				Name:     randomList.Design.Factors[i].Name,
				Label:    randomList.Design.Factors[i].Label,
				//InputLabel:       randomList.Design.Factors[i].InputLabel,
				//InputWeightLabel: randomList.Design.Factors[i].InputWeightLabel,
				//InputHeightLabel: randomList.Design.Factors[i].InputHeightLabel,
				Precision:      randomList.Design.Factors[i].Precision,
				Round:          randomList.Design.Factors[i].Round,
				CustomFormulas: randomList.Design.Factors[i].CustomFormulas,
				Type:           randomList.Design.Factors[i].Type,
				Status:         randomList.Design.Factors[i].Status,
				Options:        randomList.Design.Factors[i].Options,
				Modifiable:     true, // 标记可修改
				Stratification: true, // 标记是分层因素
				DateFormat:     dateFormat,
			})
		}
	}

	form.Fields = fields
	factorSign := LayeredBl(attribute, visitCycle, randomList.Design.Factors)
	return map[string]interface{}{
		"form":       form,
		"factorSign": factorSign,
		"attribute":  attribute,
	}, nil
}
func (s *RandomizationService) GetFormulaForm(ctx *gin.Context, customerID, envID, cohortID, subjectID, visitID string) ([]models.Field, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	visitOID, _ := primitive.ObjectIDFromHex(visitID)
	filter := bson.M{"customer_id": customerOID, "env_id": envOID}
	if cohortID != "" {
		filter = bson.M{"customer_id": customerOID, "env_id": envOID, "cohort_id": cohortOID}
	}

	// 声明接受字段信息
	fields := make([]models.Field, 0)

	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if subject.Group == "" {
		subject.Group = "N/A"
	}

	var drugDrugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, filter).Decode(&drugDrugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	formulaStr := ""
	for _, configure := range drugDrugConfigure.Configures {
		if configure.Group == subject.Group {
			_, ok := slice.Find(configure.VisitCycles, func(index int, item primitive.ObjectID) bool {
				return item == visitOID
			})
			if ok && configure.IsFormula {
				formulaStr = formulaStr + configure.CustomerCalculation
			}
		}
	}

	// 查表单
	var form models.Form
	_ = tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
	if form.Fields != nil {
		for _, fm := range form.Fields {
			if strings.Contains(formulaStr, "{"+fm.Variable+"}") && fm.ApplicationType != nil && *fm.ApplicationType == 2 && *fm.Status != 2 {
				fields = append(fields, fm)
			}
		}
	}
	return fields, nil
}

func (s *RandomizationService) DeleteForm(ctx *gin.Context, id string, fieldID string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		fieldOID, _ := primitive.ObjectIDFromHex(fieldID)
		OID, _ := primitive.ObjectIDFromHex(id)
		filter := bson.M{"_id": OID}
		var form []map[string]interface{}
		cursor, _ := tools.Database.Collection("form").Aggregate(sctx, mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$unwind", Value: "$fields"}},
			{{Key: "$match", Value: bson.M{"fields.id": fieldOID}}},
			{{Key: "$project", Value: bson.M{
				"env_id":     "$env_id",
				"cohort_id":  "$cohort_id",
				"project_id": "$project_id",
				"name":       "$fields.name",
				"label":      "$fields.label",
				"type":       "$fields.type",
				"modifiable": "$fields.modifiable",
				"options":    "$fields.options",
			}}},
		})
		err := cursor.All(nil, &form)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		fieldName := form[0]["name"].(string)

		envOID := form[0]["env_id"].(primitive.ObjectID)
		subjectFilter := bson.M{"env_id": envOID, "info.name": fieldName, "deleted": bson.M{"$ne": true}}
		historyID := envOID
		if form[0]["cohort_id"] != primitive.NilObjectID {
			cohortID := form[0]["cohort_id"].(primitive.ObjectID)
			historyID = cohortID
			subjectFilter["cohort_id"] = cohortID
		}
		count, err := tools.Database.Collection("subject").CountDocuments(sctx, subjectFilter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//已有受试者使用就不能删除
		if count > 0 {
			return nil, tools.BuildServerError(ctx, "form_field_used")
		}
		update := bson.M{
			"$pull": bson.M{
				"fields": bson.M{
					"id": fieldOID,
				},
			},
		}
		_, err = tools.Database.Collection("form").UpdateOne(sctx, filter, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		err = insertFormLog(ctx, sctx, historyID, 3, models.Field{Label: form[0]["label"].(string)}, models.Field{}, OID, "")
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RandomizationService) UpdateForm(ctx *gin.Context, id string, fieldID string, field models.Field) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		OID, _ := primitive.ObjectIDFromHex(id)
		fieldOID, _ := primitive.ObjectIDFromHex(fieldID)
		filter := bson.M{"_id": OID}
		// 轨迹
		var form []map[string]interface{}
		cursor, _ := tools.Database.Collection("form").Aggregate(sctx, mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$unwind", Value: "$fields"}},
			{{Key: "$match", Value: bson.M{"fields.id": fieldOID}}},
			{{Key: "$project", Value: bson.M{
				"env_id":           "$env_id",
				"project_id":       "$project_id",
				"cohort_id":        "$cohort_id",
				"name":             "$fields.name",
				"label":            "$fields.label",
				"type":             "$fields.type",
				"modifiable":       "$fields.modifiable",
				"options":          "$fields.options",
				"date_format":      "$fields.date_format",
				"required":         "$fields.required",
				"length":           "$fields.length",
				"range":            "$fields.range",
				"variable":         "$fields.variable",
				"status":           "$fields.status",
				"date_range":       "$fields.date_range",
				"application_type": bson.M{"$ifNull": bson.A{"$fields.application_type", 1}},
			}}},
		})
		err := cursor.All(sctx, &form)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//变量ID重复校验
		dfilter := bson.M{"env_id": form[0]["env_id"].(primitive.ObjectID)}
		if form[0]["cohort_id"] != primitive.NilObjectID {
			dfilter["cohort_id"] = form[0]["cohort_id"].(primitive.ObjectID)
		}
		oldForm := models.Form{}
		err = tools.Database.Collection("form").FindOne(sctx, dfilter).Decode(&oldForm)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, b := slice.Find(oldForm.Fields, func(index int, item models.Field) bool {
			return item.ID != field.ID && item.Label == field.Label && *item.Status == *field.Status
		})
		if b {
			return nil, tools.BuildServerError(ctx, "form_name_duplicated")
		}
		ffields := slice.Filter(oldForm.Fields, func(index int, item models.Field) bool {
			return item.ApplicationType != nil && (*item.ApplicationType == 1 || *item.ApplicationType == 2 || *item.ApplicationType == 4)
		})
		_, b = slice.Find(ffields, func(index int, item models.Field) bool {
			return item.ID != field.ID && item.Variable == field.Variable && *item.Status == *field.Status
		})
		if b {
			return nil, tools.BuildServerError(ctx, "variable_duplicated")
		}
		if field.Options != nil && len(field.Options) > 0 {
			optionLabels := slice.Map(field.Options, func(index int, item models.Option) string {
				return item.Label
			})
			optionLabels = slice.Unique(optionLabels)
			if len(optionLabels) != len(field.Options) {
				return nil, tools.BuildServerError(ctx, "option_label_duplicated")
			}
		}
		update := bson.M{
			"$set": bson.M{
				"fields.$[field].name":        field.Name,
				"fields.$[field].label":       field.Label,
				"fields.$[field].status":      field.Status,
				"fields.$[field].modifiable":  field.Modifiable,
				"fields.$[field].required":    field.Required,
				"fields.$[field].options":     field.Options,
				"fields.$[field].variable":    field.Variable,
				"fields.$[field].formatType":  field.FormatType,
				"fields.$[field].length":      field.Length,
				"fields.$[field].range":       field.Range,
				"fields.$[field].date_range":  field.DateRange,
				"fields.$[field].date_format": field.DateFormat,
				"fields.$[field].time_format": field.TimeFormat,
				"fields.$[field].type":        field.Type,
			},
		}

		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{
					bson.M{"field.id": fieldOID},
				},
			},
		}
		var oldOptions []string
		var options []string

		key := "history.randomization.config.form.edit"
		if form[0]["options"] != nil && len(form[0]["options"].(primitive.A)) != 0 {
			key = "history.randomization.config.form.editOption"
			key = fmt.Sprintf(key+"%s", "start")
			for _, option := range form[0]["options"].(primitive.A) {
				oldOptions = append(oldOptions, option.(map[string]interface{})["label"].(string))
			}
		}
		if field.Options != nil && len(field.Options) != 0 {
			key = fmt.Sprintf(key+"%s", "end")
			for _, option := range field.Options {
				options = append(options, option.Label)
			}
		}

		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": form[0]["project_id"]}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)

		}
		if project.ProjectInfo.ConnectEdc == 1 {
			key = fmt.Sprintf(key+"%s", "EDC")
		}

		historyID := form[0]["env_id"].(primitive.ObjectID)
		if form[0]["cohort_id"] != primitive.NilObjectID {
			historyID = form[0]["cohort_id"].(primitive.ObjectID)
		}
		_, err = tools.Database.Collection("form").UpdateOne(sctx, filter, update, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//无效状态则清空研究产品配置-设置表
		if *field.Status == 2 {
			condition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": form[0]["env_id"].(primitive.ObjectID), "dose_form_id": field.ID.Hex(), "is_open": true}
			escapedVariable := regexp.QuoteMeta("{" + field.Variable + "}")
			drugConfigMatch := bson.M{
				"customer_id":                     project.CustomerID,
				"project_id":                      project.ID,
				"env_id":                          form[0]["env_id"].(primitive.ObjectID),
				"configures.customer_calculation": bson.M{"$regex": escapedVariable},
			}
			if form[0]["cohort_id"] != primitive.NilObjectID {
				condition["cohort_id"] = form[0]["cohort_id"].(primitive.ObjectID)
				drugConfigMatch["cohort_id"] = form[0]["cohort_id"].(primitive.ObjectID)
			}
			count, err := tools.Database.Collection("drug_configure_setting").CountDocuments(sctx, condition)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if count > 0 {
				return nil, tools.BuildServerError(ctx, "form_invalid_error")
			}
			count, err = tools.Database.Collection("drug_configure").CountDocuments(sctx, drugConfigMatch)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if count > 0 {
				return nil, tools.BuildServerError(ctx, "form_invalid_error")
			}

		}

		// 项目日志
		Opts := []models.Option{}
		if form[0]["options"] != nil && len(form[0]["options"].(primitive.A)) != 0 {
			for _, option := range form[0]["options"].(primitive.A) {
				Opts = append(Opts, models.Option{
					Label: option.(map[string]interface{})["label"].(string),
					Value: option.(map[string]interface{})["value"].(string),
				})
			}
		}
		Range := &models.Range{}
		if form[0]["range"] != nil {
			if form[0]["range"].(map[string]interface{})["max"] != nil {
				if reflect.TypeOf(form[0]["range"].(map[string]interface{})["max"]).String() == "int32" {
					maxInt := float64(form[0]["range"].(map[string]interface{})["max"].(int32))
					Range.Max = &maxInt
				} else {
					maxInt := form[0]["range"].(map[string]interface{})["max"].(float64)
					Range.Max = &maxInt
				}
			}
			if form[0]["range"].(map[string]interface{})["min"] != nil {
				if reflect.TypeOf(form[0]["range"].(map[string]interface{})["min"]).String() == "int32" {
					minInt := float64(form[0]["range"].(map[string]interface{})["min"].(int32))
					Range.Min = &minInt
				} else {
					minInt := form[0]["range"].(map[string]interface{})["min"].(float64)
					Range.Min = &minInt
				}
			}
		}
		length := 0.0
		if form[0]["length"] != nil {
			if reflect.TypeOf(form[0]["length"]).String() == "int32" {
				length = float64(form[0]["length"].(int32))
			} else {
				length = form[0]["length"].(float64)
			}
		}
		required := false
		if form[0]["required"] != nil {
			required = form[0]["required"].(bool)
		}
		dateFormat := ""
		if form[0]["date_format"] != nil {
			dateFormat = form[0]["date_format"].(string)
		}
		variable := ""
		if form[0]["variable"] != nil {
			variable = form[0]["variable"].(string)
		}

		applicationType := 1
		if form[0]["application_type"] != nil {
			applicationType = int(form[0]["application_type"].(int32))
		}
		var status *int
		if form[0]["status"] != nil {
			statusInt32 := form[0]["status"].(int32)
			statusI := int(statusInt32)
			status = &statusI
		}
		oldField := models.Field{
			Name:       form[0]["name"].(string),
			Label:      form[0]["label"].(string),
			Modifiable: form[0]["modifiable"].(bool),
			Type:       form[0]["type"].(string),
			Options:    Opts,
			Required:   required,
			DateFormat: dateFormat,
			Length:     &length,
			Range:      Range,
			//DateRange:           date,
			Variable:        variable,
			ApplicationType: &applicationType,
			Status:          status,
		}
		err = insertFormLog(ctx, sctx, historyID, 2, oldField, field, OID, "")
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) AddForm(ctx *gin.Context, customerID string, envID string, cohortID primitive.ObjectID, projectID primitive.ObjectID, field models.Field) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(customerID)
		envOID, _ := primitive.ObjectIDFromHex(envID)
		historyID := envOID
		collection := tools.Database.Collection("form")
		OID := primitive.NewObjectID()
		oldField := models.Form{}
		filter := bson.M{"customer_id": customerOID, "env_id": envOID}
		if !cohortID.IsZero() {
			filter = bson.M{"customer_id": customerOID, "env_id": envOID, "cohort_id": cohortID}
			historyID = cohortID
		}
		//变量ID重复校验
		oldForm := models.Form{}
		err := tools.Database.Collection("form").FindOne(sctx, filter).Decode(&oldForm)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		_, b := slice.Find(oldForm.Fields, func(index int, item models.Field) bool {
			return item.ID != field.ID && item.Label == field.Label && *item.Status == *field.Status
		})
		if b {
			return nil, tools.BuildServerError(ctx, "form_name_duplicated")
		}
		ffields := slice.Filter(oldForm.Fields, func(index int, item models.Field) bool {
			return item.ApplicationType != nil && (*item.ApplicationType == 1 || *item.ApplicationType == 2 || *item.ApplicationType == 4)
		})
		_, b = slice.Find(ffields, func(index int, item models.Field) bool {
			return item.ID != field.ID && item.Variable == field.Variable && *item.Status == *field.Status
		})
		if b {
			return nil, tools.BuildServerError(ctx, "variable_duplicated")
		}

		if field.Options != nil && len(field.Options) > 0 {
			optionLabels := slice.Map(field.Options, func(index int, item models.Option) string {
				return item.Label
			})
			optionLabels = slice.Unique(optionLabels)
			if len(optionLabels) != len(field.Options) {
				return nil, tools.BuildServerError(ctx, "option_label_duplicated")
			}
		}

		//判断form是否存在，如果已存在，push field
		field.ID = primitive.NewObjectID()
		if count, _ := collection.CountDocuments(sctx, filter); count > 0 {
			update := bson.M{
				"$push": bson.M{
					"fields": field,
				},
			}

			err := collection.FindOne(sctx, filter).Decode(&oldField)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if len(oldField.Fields) == 0 {
				update = bson.M{
					"$set": bson.M{
						"fields": []models.Field{field},
					},
				}
			}

			OID = oldField.ID
			res, err := collection.UpdateOne(sctx, filter, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if res.ModifiedCount != 1 {
				return nil, tools.BuildServerError(ctx, "common.operation.fail")

			}
		} else {
			form := models.Form{
				ID:            primitive.NewObjectID(),
				CustomerID:    customerOID,
				ProjectID:     projectID,
				EnvironmentID: envOID,
				Fields:        []models.Field{field},
			}
			if !cohortID.IsZero() {
				form.CohortID = cohortID
			}
			if _, err := collection.InsertOne(sctx, form); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// 项目日志
		err = insertFormLog(ctx, sctx, historyID, 1, models.Field{}, field, OID, "")
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RandomizationService) GetAttribute(ctx *gin.Context, projectID string, customerID string, envID string, cohortID string) (models.Attribute, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{
		"env_id": envOID,
	}

	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)

	environment, exist := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	if exist {
		if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
			match["cohort_id"] = environment.Cohorts[0].ID
		}
	}

	data := models.Attribute{}
	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		if cohortOID != primitive.NilObjectID {
			match = bson.M{
				"env_id":    envOID,
				"cohort_id": cohortOID,
			}
		}
	}
	err := tools.Database.Collection("attribute").FindOne(nil, match).Decode(&data)
	if err != nil && err != mongo.ErrNoDocuments {
		return data, errors.WithStack(err)
	}

	return data, nil
}

func (s *RandomizationService) GetRandomizationType(ctx *gin.Context, projectID string, customerID string, envID string, cohortID string) (int, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}
	if cohortOID != primitive.NilObjectID {
		match["cohort_id"] = cohortOID
	}

	var randomDesign models.RandomDesign
	err := tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return 0, errors.WithStack(err)
	}

	return randomDesign.Info.Type, nil
}

func (s *RandomizationService) GetAttributes(ctx *gin.Context, envID string) ([]models.Attribute, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	data := make([]models.Attribute, 0)
	match := bson.M{
		"env_id": envOID,
	}
	cursor, err := tools.Database.Collection("attribute").Find(nil, match)
	if err != nil {
		return nil, err
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return data, nil
}

func insertAttributeLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old models.AttributeInfo, new models.AttributeInfo, oldSegmentLength string, segmentLength string, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.attribute",
				TranKey: "operation_log.attribute.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.random",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Random,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Random,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.isRandomNumber",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.IsRandomNumber,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.IsRandomNumber,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.isRandomSequenceNumber",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.IsRandomSequenceNumber,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.IsRandomSequenceNumber,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.randomSequenceNumberPrefix",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.RandomSequenceNumberPrefix,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.RandomSequenceNumberPrefix,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.randomSequenceNumberDigit",
			Old: models.OperationLogField{
				Type:  1,
				Value: old.RandomSequenceNumberDigit,
			},
			New: models.OperationLogField{
				Type:  1,
				Value: new.RandomSequenceNumberDigit,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.randomSequenceNumberStart",
			Old: models.OperationLogField{
				Type:  1,
				Value: old.RandomSequenceNumberStart,
			},
			New: models.OperationLogField{
				Type:  1,
				Value: new.RandomSequenceNumberStart,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.dispensing",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Dispensing,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Dispensing,
			},
		})
		oldDtpRule := ""
		oldDtpRuleEn := ""
		newDtpRule := ""
		newDtpRuleEn := ""
		if new.Dispensing {
			if old.DtpRule != 0 {
				if old.DtpRule == 1 {
					oldDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_ip")
					oldDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_ip")
				} else if old.DtpRule == 2 {
					oldDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_visitFlow")
					oldDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_visitFlow")
				} else if old.DtpRule == 3 {
					oldDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_notApplicable")
					oldDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_notApplicable")
				}
			}
			if new.DtpRule != 0 {
				if new.DtpRule == 1 {
					newDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_ip")
					newDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_ip")
				} else if new.DtpRule == 2 {
					newDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_visitFlow")
					newDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_visitFlow")
				} else if new.DtpRule == 3 {
					newDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_notApplicable")
					newDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_notApplicable")
				}
			}
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.dtpRule",
			Old: models.OperationLogField{
				Type:    30,
				Value:   oldDtpRule,
				ENValue: oldDtpRuleEn,
			},
			New: models.OperationLogField{
				Type:    30,
				Value:   newDtpRule,
				ENValue: newDtpRuleEn,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.randomControl",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.RandomControl,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.RandomControl,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.randomControlRule",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.RandomControlRule,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.RandomControlRule,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.segmentType",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.SegmentType,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.SegmentType,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.allowRegisterGroup",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.AllowRegisterGroup,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.AllowRegisterGroup,
			},
		})

		if new.RandomControl && new.RandomControlRule == 3 { //强制随机到有供应的分组
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.attribute",
				TranKey: "operation_log.attribute.randomControl3_info",
				Old: models.OperationLogField{
					Type:  1,
					Value: old.RandomControlGroup,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: new.RandomControlGroup,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.blind",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Blind,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Blind,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.subject_number_rule",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.SubjectNumberRule,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.SubjectNumberRule,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.screen",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.IsScreen,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.IsScreen,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.minimize_calc",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.MinimizeCalc,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.MinimizeCalc,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.IPInheritance",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.IPInheritance,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.IPInheritance,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.RemainingVisit",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.RemainingVisit,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.RemainingVisit,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.prefix",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Prefix,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Prefix,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.prefix_number",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.PrefixExpression,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.PrefixExpression,
			},
		})
		oldAllowReplace := "1"
		if old.AllowReplace {
			oldAllowReplace = "2"
		}
		newAllowReplace := "1"
		if new.AllowReplace {
			newAllowReplace = "2"
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.allowReplace",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldAllowReplace,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newAllowReplace,
			},
		})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.sitePrefix",
		//	Old: models.OperationLogField{
		//		Type:  4,
		//		Value: old.SitePrefix,
		//	},
		//	New: models.OperationLogField{
		//		Type:  4,
		//		Value: new.SitePrefix,
		//	},
		//})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.prefixConnector",
		//	Old: models.OperationLogField{
		//		Type:  2,
		//		Value: old.PrefixConnector,
		//	},
		//	New: models.OperationLogField{
		//		Type:  2,
		//		Value: new.PrefixConnector,
		//	},
		//})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.otherPrefix",
		//	Old: models.OperationLogField{
		//		Type:  4,
		//		Value: old.OtherPrefix,
		//	},
		//	New: models.OperationLogField{
		//		Type:  4,
		//		Value: new.OtherPrefix,
		//	},
		//})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.otherPrefixText",
		//	Old: models.OperationLogField{
		//		Type:  2,
		//		Value: old.OtherPrefixText,
		//	},
		//	New: models.OperationLogField{
		//		Type:  2,
		//		Value: new.OtherPrefixText,
		//	},
		//})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.subjectReplaceText",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.SubjectReplaceText,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.SubjectReplaceText,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.subjectReplaceTextEn",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.SubjectReplaceTextEn,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.SubjectReplaceTextEn,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.accuracy",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Accuracy,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Accuracy,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.digit",
			Old: models.OperationLogField{
				Type:  1,
				Value: old.Digit,
			},
			New: models.OperationLogField{
				Type:  1,
				Value: new.Digit,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.isFreeze",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.IsFreeze,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.IsFreeze,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.replaceRule",
			Old: models.OperationLogField{
				Type:  6,
				Value: convertor.ToString(old.ReplaceRule),
			},
			New: models.OperationLogField{
				Type:  6,
				Value: convertor.ToString(new.ReplaceRule),
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.ReplaceRuleNumber",
			Old: models.OperationLogField{
				Type:  1,
				Value: old.ReplaceRuleNumber,
			},
			New: models.OperationLogField{
				Type:  1,
				Value: new.ReplaceRuleNumber,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.blindingRestrictions",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.UnBlindingRestrictions,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.UnBlindingRestrictions,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.pvBlindingRestrictions",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.PvUnBlindingRestrictions,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.PvUnBlindingRestrictions,
			},
		})

		oldField := models.OperationLogField{
			Type:  6,
			Value: old.CodeRule,
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.barcode.random",
			Old:     oldField,
			New: models.OperationLogField{
				Type:  6,
				Value: new.CodeRule,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.edcDrugConfigLabel",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.EdcDrugConfigLabel,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.EdcDrugConfigLabel,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.segment",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Segment,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Segment,
			},
		})
		if len(segmentLength) > 0 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.attribute",
				TranKey: "operation_log.attribute.segmentLength",
				Old: models.OperationLogField{
					Type:  7,
					Value: oldSegmentLength,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: segmentLength,
				},
			})
		}
		oldUnblindingReason := make([]models.UnblindingReasonConfigOperationLog, 0)
		for _, config := range old.UnblindingReasonConfig {
			key := ""
			if config.AllowRemark {
				key = "operation_log.attribute.unblindingAllowTrue"
			} else {
				key = "operation_log.attribute.unblindingAllowFalse"
			}
			oldUnblindingReason = append(oldUnblindingReason, models.UnblindingReasonConfigOperationLog{
				Reason:             config.Reason,
				AllowRemarkTranKey: key,
			})
		}
		newUnblindingReason := make([]models.UnblindingReasonConfigOperationLog, 0)
		for _, config := range new.UnblindingReasonConfig {
			key := ""
			if config.AllowRemark {
				key = "operation_log.attribute.unblindingAllowTrue"
			} else {
				key = "operation_log.attribute.unblindingAllowFalse"
			}
			newUnblindingReason = append(newUnblindingReason, models.UnblindingReasonConfigOperationLog{
				Reason:             config.Reason,
				AllowRemarkTranKey: key,
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.unblindingReason",
			Old: models.OperationLogField{
				Type:  10,
				Value: oldUnblindingReason,
			},
			New: models.OperationLogField{
				Type:  10,
				Value: newUnblindingReason,
			},
		})

		//隔离原因
		oldFreezeReason := make([]models.UnblindingReasonConfigOperationLog, 0)
		for _, config := range old.FreezeReasonConfig {
			key := ""
			if config.AllowRemark {
				key = "operation_log.attribute.unblindingAllowTrue"
			} else {
				key = "operation_log.attribute.unblindingAllowFalse"
			}
			oldFreezeReason = append(oldFreezeReason, models.UnblindingReasonConfigOperationLog{
				Reason:             config.Reason,
				AllowRemarkTranKey: key,
			})
		}
		newFreezeReason := make([]models.UnblindingReasonConfigOperationLog, 0)
		for _, config := range new.FreezeReasonConfig {
			key := ""
			if config.AllowRemark {
				key = "operation_log.attribute.unblindingAllowTrue"
			} else {
				key = "operation_log.attribute.unblindingAllowFalse"
			}
			newFreezeReason = append(newFreezeReason, models.UnblindingReasonConfigOperationLog{
				Reason:             config.Reason,
				AllowRemarkTranKey: key,
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.freezeReason",
			Old: models.OperationLogField{
				Type:  10,
				Value: oldFreezeReason,
			},
			New: models.OperationLogField{
				Type:  10,
				Value: newFreezeReason,
			},
		})

	}
	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.attribute", OID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *RandomizationService) UpdateAttribute(ctx *gin.Context, data map[string]interface{}) (string, error) {
	var message string
	var cohortIds []primitive.ObjectID
	var cohortOID primitive.ObjectID
	var attribute models.AttributeInfo

	projectOID, _ := primitive.ObjectIDFromHex(data["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(data["envId"].(string))
	OID := envOID
	customerOID, _ := primitive.ObjectIDFromHex(data["customerId"].(string))

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return "", errors.WithStack(err)
	}

	var ab models.Attribute
	attributeMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if data["cohortId"] != nil && data["cohortId"] != primitive.NilObjectID {
		cohortOID, _ = primitive.ObjectIDFromHex(data["cohortId"].(string))

		attributeMatch = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	e := tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&ab)
	if e != nil {
		return "", errors.WithStack(e)
	}

	jsonStr, err := json.Marshal(data["attribute"])
	if err != nil {
		return "", errors.WithStack(err)

	}
	upsert := true
	err = json.Unmarshal(jsonStr, &attribute)
	if err != nil {
		return "", errors.WithStack(err)
	}
	// 7549 编码配置迁移到属性配置中，配置控制逻辑不变
	//attribute.CodeConfigInit = true
	//if ab.AttributeInfo.CodeConfigInit == true {
	//	attribute.CodeRule = ab.AttributeInfo.CodeRule
	//}
	count := 1
	env := models.Environment{}
	cohort := models.Cohort{}
	if project.Environments != nil && len(project.Environments) > 0 {
		for _, environment := range project.Environments {
			if envOID == environment.ID {
				env = environment
				if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
					count = len(environment.Cohorts)
					for _, c := range environment.Cohorts {
						if c.ID == cohortOID {
							cohort = c
						}
					}
				}
			}
		}
	}
	if attribute.Dispensing && !attribute.Random {
		if project.Type == 1 {
			types := slice.Map(env.AlertThresholds, func(i int, item models.AlertThreshold) int {
				return item.Type
			})
			if (slice.Contain(types, 1) && slice.Contain(types, 3)) || (slice.Contain(types, 7) && slice.Contain(types, 3)) {
				return "", tools.BuildServerError(ctx, "environment.alertThresholds.attributeError")
			}
		} else {
			types := slice.Map(cohort.AlertThresholds, func(i int, item models.AlertThreshold) int {
				return item.Type
			})
			if (slice.Contain(types, 1) && slice.Contain(types, 3)) || (slice.Contain(types, 7) && slice.Contain(types, 3)) {
				return "", tools.BuildServerError(ctx, "environment.alertThresholds.attributeError")
			}
		}
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var old models.Attribute

		match := bson.M{
			"project_id":  projectOID,
			"env_id":      envOID,
			"customer_id": customerOID,
		}
		if data["cohortId"] != nil && data["cohortId"] != primitive.NilObjectID {
			cohortOID, _ = primitive.ObjectIDFromHex(data["cohortId"].(string))
			match = bson.M{
				"project_id":  projectOID,
				"env_id":      envOID,
				"customer_id": customerOID,
				"cohort_id":   cohortOID,
			}
			OID = cohortOID
		}
		err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&old)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var field = models.Field{
			ID:         primitive.NewObjectID(),
			Name:       "shortname",
			Label:      attribute.SubjectReplaceText,
			LabelEn:    attribute.SubjectReplaceTextEn,
			Type:       "input",
			Modifiable: true,
			Digit:      attribute.Digit,
			Accuracy:   attribute.Accuracy,
		}
		attribute.Field = field
		for i, config := range attribute.UnblindingReasonConfig {
			attribute.UnblindingReasonConfig[i].Reason = strings.TrimSpace(config.Reason)
		}
		update := bson.M{"$set": bson.M{"info": attribute}}
		opts := &options.FindOneAndUpdateOptions{
			Upsert: &upsert,
		}
		var res models.Attribute
		_ = tools.Database.Collection("attribute").FindOneAndUpdate(sctx, match, update, opts).Decode(&res)
		if res.ID == primitive.NilObjectID {
			err := tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&res)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		//校验配置发生改变重新修改受试者的顺序号
		if attribute.IsRandomSequenceNumber {
			if attribute.RandomSequenceNumberPrefix != old.AttributeInfo.RandomSequenceNumberPrefix ||
				attribute.RandomSequenceNumberDigit != old.AttributeInfo.RandomSequenceNumberDigit ||
				attribute.RandomSequenceNumberStart != old.AttributeInfo.RandomSequenceNumberStart ||
				!old.AttributeInfo.IsRandomSequenceNumber {
				b := tools.IsIncrementImpossibleByString(attribute.RandomSequenceNumberDigit, attribute.RandomSequenceNumberPrefix, attribute.RandomSequenceNumberStart)
				// 如果起始数已经大于最大可能的数，则无法自增
				if b {
					return nil, tools.BuildServerError(ctx, "subject_random_sequence_number_start_max_error")
				}
				subjects := make([]models.Subject, 0)
				filter := bson.M{
					"project_id": projectOID,
					"env_id":     envOID,
					"group":      bson.M{"$ne": ""},
				}
				if !res.CohortID.IsZero() {
					filter["cohort_id"] = res.CohortID
				}
				//按随机时间排序
				subjectOpts := options.FindOptions{
					//id和随机时间
					Projection: bson.M{"_id": 1, "random_time": 1},
					Sort:       bson.M{"random_time": 1},
				}
				cursor, err := tools.Database.Collection("subject").Find(sctx, filter, &subjectOpts)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(sctx, &subjects)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				start := attribute.RandomSequenceNumberStart
				for _, subject := range subjects {
					number, b2 := tools.GenerateRandomSequenceNumber(attribute.RandomSequenceNumberPrefix, attribute.RandomSequenceNumberDigit, start)
					if !b2 {
						number = ""
					}
					start++
					up := bson.M{"$set": bson.M{"random_sequence_number": number}}
					_, err := tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": subject.ID}, up)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		}

		var project models.Project
		condition := bson.M{
			"_id":         projectOID,
			"customer_id": customerOID,
		}
		e := tools.Database.Collection("project").FindOne(sctx, condition).Decode(&project)
		if e != nil {
			return nil, errors.WithStack(e)
		}

		if len(project.Environments) > 0 {
			for _, env := range project.Environments {
				if env.ID == envOID {
					if len(env.Cohorts) > 0 {
						for _, cohort := range env.Cohorts {
							cohortIds = append(cohortIds, cohort.ID)
						}
					}
				}
			}
		}

		//号段随机长度
		var segmentLength strings.Builder
		var oldSegmentLength strings.Builder
		if attribute.Blind && data["groupsSegment"] != nil {
			randomDesignUpdate := bson.M{}
			var groupsSegmentData = data["groupsSegment"].([]interface{})
			type groupReq struct {
				ID            primitive.ObjectID `json:"id"`
				Name          string             `json:"name"`
				SegmentLength int                `json:"segmentLength"`
			}
			var randomDesign models.RandomDesign
			err = tools.Database.Collection("random_design").FindOne(sctx, match).Decode(&randomDesign)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			groups := randomDesign.Info.Groups
			for index, group := range groups {
				oldSegmentLength.WriteString(group.Name)
				oldSegmentLength.WriteString(":")
				oldSegmentLength.WriteString(strconv.Itoa(group.SegmentLength))
				if len(groups)-1 > index {
					oldSegmentLength.WriteString("、")
				}
			}
			for index, groupsSegment := range groupsSegmentData {
				greq := groupReq{}
				gjsonStr, err := json.Marshal(groupsSegment)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = json.Unmarshal(gjsonStr, &greq)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				segmentLength.WriteString(greq.Name)
				segmentLength.WriteString(":")
				segmentLength.WriteString(strconv.Itoa(greq.SegmentLength))
				if len(groupsSegmentData)-1 > index {
					segmentLength.WriteString("、")
				}
				for i := 0; i < len(groups); i++ {
					group := &groups[i]
					if group.SubGroup != nil && len(group.SubGroup) > 0 {
						sub, ok := slice.Find(group.SubGroup, func(index int, item models.SubGroup) bool {
							return group.ID == greq.ID && group.Name+" "+item.Name == greq.Name
						})
						if ok {
							sub.SegmentLength = greq.SegmentLength
							break
						}
					} else {
						if group.ID == greq.ID {
							group.SegmentLength = greq.SegmentLength
							break
						}
					}

				}
			}
			randomDesignUpdate = bson.M{
				"$set": bson.M{
					"info.groups": groups,
				},
			}
			_, rderr := tools.Database.Collection("random_design").UpdateOne(sctx, match, randomDesignUpdate)
			if rderr != nil {
				return nil, errors.WithStack(rderr)
			}
		}
		err := insertAttributeLog(ctx, nil, OID, 2, old.AttributeInfo, attribute, oldSegmentLength.String(), segmentLength.String(), "")
		if err != nil {
			return nil, err
		}
		return nil, nil
	}

	err = tools.Transaction(callback)
	if err != nil {
		return "", err
	}

	if len(cohortIds) > 0 {
		////受试者号规则项目维度需一致，请重新确认
		//countMatch := bson.M{
		//	"project_id":               projectOID,
		//	"env_id":                   envOID,
		//	"customer_id":              customerOID,
		//	"cohort_id":                bson.M{"$ne": cohortOID},
		//	"info.subject_number_rule": bson.M{"$ne": attribute.SubjectNumberRule},
		//}
		//count, err := tools.Database.Collection("attribute").CountDocuments(nil, countMatch)
		//if err != nil {
		//	return "", errors.WithStack(err)
		//}

		if ab.AttributeInfo.IsScreen != attribute.IsScreen && (ab.AttributeInfo.SubjectNumberRule == attribute.SubjectNumberRule &&
			(ab.AttributeInfo.SubjectReplaceText == attribute.SubjectReplaceText && ab.AttributeInfo.SubjectReplaceTextEn == attribute.SubjectReplaceTextEn) &&
			ab.AttributeInfo.Prefix == attribute.Prefix && ab.AttributeInfo.PrefixExpression == attribute.PrefixExpression &&
			ab.AttributeInfo.Accuracy == attribute.Accuracy && ab.AttributeInfo.Digit == attribute.Digit &&
			ab.AttributeInfo.AllowReplace == attribute.AllowReplace && ab.AttributeInfo.ReplaceRule == attribute.ReplaceRule &&
			ab.AttributeInfo.ReplaceRuleNumber == attribute.ReplaceRuleNumber) {
			if count != 1 {
				if project.Type == 2 {
					message = "project.attribute.subject_number_rule.info2"
				} else if project.Type == 3 {
					message = "project.attribute.subject_number_rule.info3"
				}
			}
		}

		if ab.AttributeInfo.IsScreen == attribute.IsScreen && (ab.AttributeInfo.SubjectNumberRule != attribute.SubjectNumberRule || (ab.AttributeInfo.SubjectReplaceText != attribute.SubjectReplaceText || ab.AttributeInfo.SubjectReplaceTextEn != attribute.SubjectReplaceTextEn) ||
			ab.AttributeInfo.Prefix != attribute.Prefix || ab.AttributeInfo.PrefixExpression != attribute.PrefixExpression ||
			ab.AttributeInfo.Accuracy != attribute.Accuracy || ab.AttributeInfo.Digit != attribute.Digit ||
			ab.AttributeInfo.AllowReplace != attribute.AllowReplace || ab.AttributeInfo.ReplaceRule != attribute.ReplaceRule ||
			ab.AttributeInfo.ReplaceRuleNumber != attribute.ReplaceRuleNumber) {
			if count != 1 {
				if project.Type == 2 {
					message = "project.attribute.subject_number_rule.info4"
				} else if project.Type == 3 {
					message = "project.attribute.subject_number_rule.info5"
				}
			}
		}

		if ab.AttributeInfo.IsScreen != attribute.IsScreen && (ab.AttributeInfo.SubjectNumberRule != attribute.SubjectNumberRule || (ab.AttributeInfo.SubjectReplaceText != attribute.SubjectReplaceText || ab.AttributeInfo.SubjectReplaceTextEn != attribute.SubjectReplaceTextEn) ||
			ab.AttributeInfo.Prefix != attribute.Prefix || ab.AttributeInfo.PrefixExpression != attribute.PrefixExpression ||
			ab.AttributeInfo.Accuracy != attribute.Accuracy || ab.AttributeInfo.Digit != attribute.Digit ||
			ab.AttributeInfo.AllowReplace != attribute.AllowReplace || ab.AttributeInfo.ReplaceRule != attribute.ReplaceRule ||
			ab.AttributeInfo.ReplaceRuleNumber != attribute.ReplaceRuleNumber) {
			if count != 1 {
				if project.Type == 2 {
					message = "project.attribute.subject_number_rule.info6"
				} else if project.Type == 3 {
					message = "project.attribute.subject_number_rule.info7"
				}
			}
		}

		//判断隔离原因是否修改
		changeFreezeReasonConfig := false
		if len(ab.AttributeInfo.FreezeReasonConfig) != len(attribute.FreezeReasonConfig) {
			changeFreezeReasonConfig = true
		} else {
			freezeReasonMap := make(map[string]bool)
			for _, v := range ab.AttributeInfo.FreezeReasonConfig {
				freezeReasonMap[v.Reason] = v.AllowRemark
			}
			for _, v := range attribute.FreezeReasonConfig {
				if val, ok := freezeReasonMap[v.Reason]; ok {
					if val != v.AllowRemark {
						changeFreezeReasonConfig = true
						break
					}
				} else {
					changeFreezeReasonConfig = true
					break
				}
			}
		}

		if changeFreezeReasonConfig {
			if project.Type == 2 {
				message = "project.attribute.freeze_reason.info1"
			} else if project.Type == 3 {
				message = "project.attribute.freeze_reason.info2"
			}
		}

		if changeFreezeReasonConfig || ab.AttributeInfo.SubjectNumberRule != attribute.SubjectNumberRule || (ab.AttributeInfo.SubjectReplaceText != attribute.SubjectReplaceText || ab.AttributeInfo.SubjectReplaceTextEn != attribute.SubjectReplaceTextEn) ||
			ab.AttributeInfo.Prefix != attribute.Prefix || ab.AttributeInfo.PrefixExpression != attribute.PrefixExpression ||
			ab.AttributeInfo.Accuracy != attribute.Accuracy || ab.AttributeInfo.Digit != attribute.Digit ||
			ab.AttributeInfo.AllowReplace != attribute.AllowReplace || ab.AttributeInfo.ReplaceRule != attribute.ReplaceRule ||
			ab.AttributeInfo.ReplaceRuleNumber != attribute.ReplaceRuleNumber || ab.AttributeInfo.IsScreen != attribute.IsScreen {

			for _, cOID := range cohortIds {

				if cOID != OID {
					var old models.Attribute
					var newAttributeInfo models.AttributeInfo
					match := bson.M{
						"project_id":  projectOID,
						"env_id":      envOID,
						"customer_id": customerOID,
						"cohort_id":   cOID,
					}
					err = tools.Database.Collection("attribute").FindOne(ctx, match).Decode(&old)
					if err != nil {
						return "", errors.WithStack(err)
					}

					newAttributeInfo = old.AttributeInfo

					newAttributeInfo.ConnectAli = attribute.ConnectAli
					newAttributeInfo.AliProjectNO = attribute.AliProjectNO
					newAttributeInfo.SubjectReplaceText = attribute.SubjectReplaceText
					newAttributeInfo.SubjectReplaceTextEn = attribute.SubjectReplaceTextEn
					newAttributeInfo.SubjectNumberRule = attribute.SubjectNumberRule
					newAttributeInfo.Prefix = attribute.Prefix
					newAttributeInfo.PrefixExpression = attribute.PrefixExpression
					newAttributeInfo.Accuracy = attribute.Accuracy
					newAttributeInfo.Digit = attribute.Digit
					newAttributeInfo.AllowReplace = attribute.AllowReplace
					newAttributeInfo.ReplaceRule = attribute.ReplaceRule
					newAttributeInfo.ReplaceRuleNumber = attribute.ReplaceRuleNumber
					newAttributeInfo.IsScreen = attribute.IsScreen
					newAttributeInfo.FreezeReasonConfig = attribute.FreezeReasonConfig
					newAttributeInfo.Field.Label = attribute.SubjectReplaceText
					newAttributeInfo.Field.LabelEn = attribute.SubjectReplaceTextEn

					err = insertAttributeLog(ctx, nil, cOID, 2, old.AttributeInfo, newAttributeInfo, "", "", "")
					if err != nil {
						return "", err
					}
				}
			}

		}

		fiter := bson.M{
			"project_id":  projectOID,
			"env_id":      envOID,
			"customer_id": customerOID,
			"cohort_id":   bson.M{"$in": cohortIds},
		}
		aliUpdate := bson.M{"$set": bson.M{"info.connect_ali": attribute.ConnectAli, "info.ali_project_no": attribute.AliProjectNO,
			"info.subject_replace_text": attribute.SubjectReplaceText, "info.subject_replace_text_en": attribute.SubjectReplaceTextEn,
			"info.subject_number_rule": attribute.SubjectNumberRule, "info.prefix": attribute.Prefix, "info.prefix_expression": attribute.PrefixExpression,
			"info.accuracy": attribute.Accuracy, "info.digit": attribute.Digit, "info.allow_replace": attribute.AllowReplace,
			"info.replace_rule": attribute.ReplaceRule, "info.replace_rule_number": attribute.ReplaceRuleNumber, "info.is_screen": attribute.IsScreen,
			"info.field.label": attribute.SubjectReplaceText, "info.field.label_en": attribute.SubjectReplaceTextEn,
			"info.freeze_reason_config": attribute.FreezeReasonConfig}}
		_, rderr := tools.Database.Collection("attribute").UpdateMany(nil, fiter, aliUpdate)
		if rderr != nil {
			return "", nil
		}

	}

	return message, nil
}

// UpdateSiteLayered
func (s *RandomizationService) UpdateSiteLayered(ctx *gin.Context) error {
	req := struct {
		ID    string `json:"id"`
		Type  int    `json:"type"`
		Value int    `json:"value"`
	}{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		OID, _ := primitive.ObjectIDFromHex(req.ID)
		tp := req.Type // type: 1设置是否中心分层 2中心没有分配随机号不能入组
		filter := bson.M{"_id": OID}
		update := bson.M{}
		var newAttribute models.AttributeInfo

		if tp == 1 {
			value := req.Value
			instituteLayered := false
			countryLayered := false
			regionLayered := false
			newAttribute.InstituteLayered = false
			newAttribute.CountryLayered = false
			newAttribute.RegionLayered = false
			switch value {
			case 1:
				countryLayered = true
				newAttribute.CountryLayered = true
			case 2:
				instituteLayered = true
				newAttribute.InstituteLayered = true
			case 3:
				regionLayered = true
				newAttribute.RegionLayered = true
			}
			update = bson.M{"$set": bson.M{
				"info.institute_layered": instituteLayered,
				"info.country_layered":   countryLayered,
				"info.region_layered":    regionLayered,
			}}
		}
		if tp == 2 {
			isRandom := false
			isCountryRandom := false
			isRegionRandom := false
			switch req.Value {
			case 1:
				isRandom = true
			case 2:
				isCountryRandom = true
			case 3:
				isRegionRandom = true
			}
			newAttribute.IsRandom = isRandom
			newAttribute.IsCountryRandom = isCountryRandom
			newAttribute.IsRegionRandom = isRegionRandom
			update = bson.M{"$set": bson.M{"info.is_random": isRandom, "info.is_country_random": isCountryRandom, "info.is_region_random": isRegionRandom}}
		}

		// 轨迹
		var attribute models.Attribute
		err := tools.Database.Collection("attribute").FindOne(sctx, filter).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		historyID := attribute.EnvironmentID
		if attribute.CohortID != primitive.NilObjectID {
			historyID = attribute.CohortID
		}
		one, err := tools.Database.Collection("attribute").UpdateOne(sctx, filter, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if tp == 1 && one.ModifiedCount != 0 {
			// 插入项目日志
			err = insertLayerLog(ctx, sctx, historyID, 2, attribute.AttributeInfo, newAttribute, historyID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		if tp == 2 && one.ModifiedCount != 0 {
			// 插入项目日志
			err = insertLayerRegionLog(ctx, sctx, historyID, 2, attribute.AttributeInfo, newAttribute, historyID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RandomizationService) GetRandomizationInfo(ctx *gin.Context, projectID string, customerID string, envID string, cohortID string, roleId string) (models.RandomDesignInfo, error) {
	var randomization models.RandomDesign
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}

	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match = bson.M{
			"customer_id": customerOID,
			"project_id":  projectOID,
			"env_id":      envOID,
			"cohort_id":   cohortOID,
		}
	}
	err := tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomization)
	if err != nil && err != mongo.ErrNoDocuments {
		return randomization.Info, errors.WithStack(err)
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$match", Value: bson.M{"status": 2}}},
		{{Key: "$group", Value: bson.M{"_id": "$random_list_id"}}},
	}
	type id struct {
		ID primitive.ObjectID `bson:"_id"`
	}
	ids := make([]id, 0)
	cursor, err := tools.Database.Collection("random_number").Aggregate(nil, pipeline)
	if err != nil {
		return models.RandomDesignInfo{}, errors.WithStack(err)
	}
	err = cursor.All(nil, &ids)
	if err != nil {
		return models.RandomDesignInfo{}, errors.WithStack(err)
	}
	randomListIds := slice.Map(ids, func(index int, item id) primitive.ObjectID {
		return item.ID
	})

	randomLists := make([]models.RandomList, 0)
	cursor, err = tools.Database.Collection("random_list").Find(nil, bson.M{"_id": bson.M{"$in": randomListIds}})
	if err != nil {
		return models.RandomDesignInfo{}, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return models.RandomDesignInfo{}, errors.WithStack(err)
	}
	if roleId != "" {
		isBlindedRole, err := tools.IsBlindedRole(roleId)
		if err != nil {
			return randomization.Info, errors.WithStack(err)
		}
		if isBlindedRole {
			var attribute models.Attribute
			err := tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
			if err != nil && err != mongo.ErrNoDocuments {
				return randomization.Info, errors.WithStack(err)
			}
			for i := 0; i < len(randomization.Info.Groups); i++ {
				if attribute.AttributeInfo.Blind {
					randomization.Info.Groups[i].Name = tools.BlindData
					randomization.Info.Groups[i].Code = tools.BlindData
				}
				if randomization.Info.Groups[i].SubGroup != nil && len(randomization.Info.Groups[i].SubGroup) > 0 {
					for j, subGroup := range randomization.Info.Groups[i].SubGroup {
						if subGroup.Blind {
							randomization.Info.Groups[i].SubGroup[j].Name = tools.BlindData
						}
					}
				}

			}

		}

	}
	subjects := make([]models.Subject, 0)
	cursor, err = tools.Database.Collection("subject").Find(nil, match, &options.FindOptions{
		Projection: bson.M{
			"info": 1,
		},
	})
	if err != nil {
		return models.RandomDesignInfo{}, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return models.RandomDesignInfo{}, errors.WithStack(err)
	}
	factorOptionMap := make(map[string]map[string]bool)
	for _, subject := range subjects {
		for _, info := range subject.Info {
			if strings.Contains(info.Name, "factor") && info.Value != nil && !strings.Contains(info.Name, "Weight") && !strings.Contains(info.Name, "Birthday") && !strings.Contains(info.Name, "Height") {
				m := factorOptionMap[info.Name]
				if m == nil {
					m = make(map[string]bool)
				}
				m[info.Value.(string)] = true
				factorOptionMap[info.Name] = m
			}
		}
	}
	//状态默认值
	for i, group := range randomization.Info.Groups {
		if group.Status == nil {
			status := 1
			randomization.Info.Groups[i].Status = &status
		}
		//能否被删除
		_, used := slice.Find(randomLists, func(index int, item models.RandomList) bool {
			_, b := slice.Find(item.Design.Groups, func(index int, g models.RandomListGroup) bool {
				return g.Code == group.Code && g.Name == group.Name
			})
			return b
		})
		randomization.Info.Groups[i].Used = used
		if !used {
			randomization.Info.Groups[i].Used = group.IsCopyData
		}
	}
	//状态默认值
	for i, f := range randomization.Info.Factors {
		if randomization.Info.Factors[i].Status == nil {
			status := 1
			randomization.Info.Factors[i].Status = &status
		}
		for j, o := range randomization.Info.Factors[i].Options {
			if factorOptionMap[f.Name][o.Value] {
				randomization.Info.Factors[i].Options[j].Disable = true
			} else {
				randomization.Info.Factors[i].Options[j].Disable = randomization.Info.Factors[i].Options[j].IsCopyData
				randomization.Info.Factors[i].IsCopyData = randomization.Info.Factors[i].Options[j].IsCopyData
			}
		}
	}
	return randomization.Info, nil
}

func (s *RandomizationService) AddRandomizationInfo(ctx *gin.Context) error {
	req := models.RandomFactorGroupReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	projectOID := req.ProjectID
	envOID := req.EnvID
	customerOID := req.CustomerID
	upsert := true
	historyID := envOID
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	if !req.CohortID.IsZero() {
		historyID = req.CohortID
		match = bson.M{
			"project_id":  projectOID,
			"env_id":      envOID,
			"customer_id": customerOID,
			"cohort_id":   req.CohortID,
		}
	}
	update := bson.M{}
	opts := &options.UpdateOptions{
		Upsert: &upsert,
	}

	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	if attribute.AttributeInfo.MinimizeCalc == 2 && req.Type != nil && *req.Type == 2 {
		return tools.BuildServerError(ctx, "random_attribute_error")
	}
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, match).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	var randomDesign models.RandomDesign
	var newRandomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		updateType := "type"
		if req.Type != nil {
			t := *req.Type
			update = bson.M{"$set": bson.M{fmt.Sprintf("info.%s", "type"): t}}
			// 项目日志
			newRandomDesign.Info.Type = t
		}
		if req.Group != nil {
			updateType = "group"
			group := *req.Group
			group.ID = primitive.NewObjectID()

			exist := false
			for _, g := range randomDesign.Info.Groups {
				if g.Status == nil || *g.Status == 1 {
					if g.SubGroup != nil && len(g.SubGroup) > 0 {
						for _, sg := range g.SubGroup {
							if g.Code == group.Code && g.Name == group.Name {
								for _, subGroup := range group.SubGroup {
									if sg.Name == subGroup.Name {
										exist = true
										break
									}
								}
							}
						}
					} else {
						if g.Code == group.Code && g.Name == group.Name {
							exist = true
							break
						}
					}
				}
			}
			if exist {
				return nil, tools.BuildServerError(ctx, "option_label_duplicated")
			}

			// 若 group == null 或者 不存在 则 用set 插入数据
			if len(randomDesign.Info.Groups) == 0 {
				var groups []interface{}
				groups = append(groups, group)
				update = bson.M{"$set": bson.M{"info.groups": groups}}
			} else {
				update = bson.M{"$push": bson.M{"info.groups": group}}

			}
			var subGroup []models.SubGroup
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, sub := range group.SubGroup {
					subGroup = append(subGroup, models.SubGroup{
						Name:          sub.Name,
						Blind:         sub.Blind,
						SegmentLength: 0,
					})
				}
			}
			newRandomDesign.Info.Groups = append([]models.RandomGroup{}, models.RandomGroup{
				ID:       group.ID,
				Code:     group.Code,
				Name:     group.Name,
				SubGroup: subGroup,
				Status:   &group.Status,
			})

		}
		if req.Factor != nil {
			updateType = "factor"
			factor := *req.Factor
			if factor.IsCalc {
				formulas := slice.Map(factor.Options, func(index int, item struct {
					Label      string  `json:"label" bson:"label"`
					Value      string  `json:"value" bson:"value"`
					Formula    *string `json:"formula" bson:"formula"`
					IsCopyData bool    `json:"isCopyData" bson:"is_copy_data"`
				}) string {
					return *item.Formula
				})
				err := tools.ValidateFactorFormula(ctx, formulas)
				if err != nil {
					return nil, err
				}
				calcFields := slice.Filter(form.Fields, func(index int, item models.Field) bool {
					return item.ApplicationType != nil && (*item.ApplicationType == 1 || *item.ApplicationType == 4)
				})
				calcVariables := slice.Map(calcFields, func(index int, item models.Field) string {
					return item.Variable
				})
				re := regexp.MustCompile(`\{([\x{4e00}-\x{9fa5}\w]+)\}`)
				customFormulas := factor.CustomFormulas
				matches := re.FindAllStringSubmatch(customFormulas, -1)
				fieldNames := slice.Map(matches, func(index int, item []string) string {
					return item[1]
				})
				for _, fieldName := range fieldNames {
					if factor.CalcType != nil && *factor.CalcType == 0 {
						if fieldName == "CurrentTime" {
							continue
						} else {
							if !slice.Contain(calcVariables, fieldName) {
								return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_form")
							} else {
								find, b := slice.Find(calcFields, func(index int, item models.Field) bool {
									return item.Variable == fieldName
								})
								if b && (find.Type != "inputNumber" && find.Type != "datePicker" && find.Type != "timePicker") {
									return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_type")
								}
							}
						}
					} else if factor.CalcType != nil && *factor.CalcType == 1 {
						if !slice.Contain(calcVariables, fieldName) {
							return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_form")
						} else {
							find, b := slice.Find(calcFields, func(index int, item models.Field) bool {
								return item.Variable == fieldName
							})
							if b && (find.Type != "inputNumber" && find.Type != "datePicker" && find.Type != "timePicker") {
								return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_type")
							}
						}
					}
				}
			}
			if factor.Name != "" {
				// EDC对接项目分层因素插入
				filter := make(map[string]interface{})
				filterFactor := make(map[string]interface{})

				for k, v := range match {
					filter[k] = v
					filterFactor[k] = v

				}
				// 校验name 是否为唯一字段
				filter["info.factors.name"] = factor.Name
				if count, _ := tools.Database.Collection("random_design").CountDocuments(nil, filter); count > 0 {
					return nil, tools.BuildServerError(ctx, "random_filed_error")
				}
				factor.ID = primitive.NewObjectID()

				// 若factors == null 或者 不存在 则 用set 插入数据
				filterFactor["info.factors"] = nil
				var randomDesign models.RandomDesign
				tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
				if len(randomDesign.Info.Factors) == 0 {
					var factors []interface{}
					factors = append(factors, factor)
					update = bson.M{"$set": bson.M{"info.factors": factors}}
				} else {
					update = bson.M{"$push": bson.M{"info.factors": factor}}
				}

				var options []string
				for _, value := range factor.Options {
					options = append(options, value.Label)
				}
				var newOptions []models.Option
				for _, option := range factor.Options {
					newOptions = append(newOptions, models.Option{
						Label: option.Label,
						Value: option.Value,
					})
				}
				newRandomDesign.Info.Factors = append(newRandomDesign.Info.Factors, models.RandomFactor{
					Name:    factor.Name,
					Label:   factor.Label,
					Number:  factor.Number,
					Type:    factor.Type,
					Options: newOptions,
				})
			} else {
				// 分层因素插入
				factor.ID = primitive.NewObjectID()
				pipepine := mongo.Pipeline{
					{{Key: "$match", Value: match}},
					{{Key: "$unwind", Value: "$design.factors"}},
					{{Key: "$group", Value: bson.M{"_id": "$design.factors.name"}}},
					{{Key: "$project", Value: bson.M{"id": "$_id"}}},
					{{Key: "$sort", Value: bson.D{{"id", -1}}}},
				}

				// 根据随机列表，随机配置 自动生成name
				var randomList []map[string]interface{}
				cursor, err := tools.Database.Collection("random_list").Aggregate(nil, pipepine)
				if err != nil {
					return nil, errors.WithStack(err)

				}
				err = cursor.All(nil, &randomList)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				var randomDesign []map[string]interface{}
				pipepine = mongo.Pipeline{
					{{Key: "$match", Value: match}},
					{{Key: "$unwind", Value: "$info.factors"}},
					{{Key: "$project", Value: bson.M{"id": "$info.factors.name"}}},
					{{Key: "$sort", Value: bson.D{{"id", -1}}}},
				}
				designCursor, err := tools.Database.Collection("random_design").Aggregate(nil, pipepine)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = designCursor.All(nil, &randomDesign)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 存在随机列表， 筛选所有随机列表分层字段与随机配置分层对比 取name字段最大编号+1，否则筛选随机配置分层字段 最大编号+1
				if len(randomList) == 0 {
					factor.Name = "factor0"
					if len(randomDesign) != 0 {
						index, _ := strconv.Atoi(randomDesign[0]["id"].(string)[6:])
						name := "factor" + strconv.Itoa(index+1)
						factor.Name = name
					}
				} else {
					randomListIndex, _ := strconv.Atoi(randomList[0]["id"].(string)[6:])
					name := "factor" + strconv.Itoa(randomListIndex+1)
					if len(randomDesign) != 0 {
						randomDesignIndex, _ := strconv.Atoi(randomDesign[0]["id"].(string)[6:])
						if randomListIndex <= randomDesignIndex {
							name = "factor" + strconv.Itoa(randomDesignIndex+1)
						}
					}
					factor.Name = name
				}
				if len(randomDesign) == 0 {
					var factors []interface{}
					factors = append(factors, factor)
					update = bson.M{"$set": bson.M{fmt.Sprintf("info.%s", "factors"): factors}}
				} else {
					update = bson.M{"$push": bson.M{fmt.Sprintf("info.%s", "factors"): factor}}
				}
				var options []string
				for _, value := range factor.Options {
					options = append(options, value.Label)
				}
				var newOptions []models.Option
				for _, option := range factor.Options {
					newOptions = append(newOptions, models.Option{
						Label:   option.Label,
						Value:   option.Value,
						Formula: option.Formula,
					})
				}
				status := 1
				if factor.Status != nil {
					status = *factor.Status
				}
				newRandomDesign.Info.Factors = append(newRandomDesign.Info.Factors, models.RandomFactor{
					IsCalc:         factor.IsCalc,
					CalcType:       factor.CalcType,
					CustomFormulas: factor.CustomFormulas,
					Round:          factor.Round,
					Precision:      factor.Precision,
					Name:           factor.Name,
					Label:          factor.Label,
					Number:         factor.Number,
					Type:           factor.Type,
					DateFormat:     factor.DateFormat,
					Options:        newOptions,
					Status:         &status,
				})
			}
		}

		_, err := tools.Database.Collection("random_design").UpdateOne(sctx, match, update, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		types := 1
		if req.Type != nil && randomDesign.Info.Type != 0 {
			types = 2
		}

		err = insertRandomDesignLog(ctx, sctx, historyID, types, randomDesign.Info, newRandomDesign.Info, randomDesign.ID, updateType, project, "")
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil

}

func (s *RandomizationService) UpdateRandomizationInfo(ctx *gin.Context) error {
	req := models.RandomFactorGroupReq{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	projectOID := req.ProjectID
	envOID := req.EnvID
	customerOID := req.CustomerID
	upsert := true
	historyID := envOID
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	if !req.CohortID.IsZero() {
		historyID = req.CohortID
		match = bson.M{
			"project_id":  projectOID,
			"env_id":      envOID,
			"customer_id": customerOID,
			"cohort_id":   req.CohortID,
		}
	}
	update := bson.M{}
	opts := &options.UpdateOptions{
		Upsert: &upsert,
	}

	var randomDesign models.RandomDesign
	var newRandomDesign models.RandomDesign
	err := tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, match).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		updateType := "type"
		newRandomDesign.Info.Type = randomDesign.Info.Type
		if req.Type != nil {
			t := *req.Type
			update = bson.M{"$set": bson.M{"info.type": t}}
			newRandomDesign.Info.Type = t
		}
		if req.Group != nil {
			group := *req.Group
			// 修改组别
			updateType = "group"

			exist := false
			for _, g := range randomDesign.Info.Groups {
				if g.Status == nil || *g.Status == 1 {
					if g.SubGroup != nil && len(g.SubGroup) > 0 {
						for _, sg := range g.SubGroup {
							if g.Code == group.Code && g.Name == group.Name {
								count := slice.Count(group.SubGroup, func(index int, item models.SubGroupReq) bool {
									return sg.Name == item.Name
								})
								if req.Group.Status == 1 && count > 1 {
									exist = true
									break
								}
							}
						}
					} else {
						if g.Code == group.Code && g.Name == group.Name && req.Group.Status == 1 && g.ID != group.ID {
							exist = true
							break
						}
					}
				}
			}
			if exist {
				return nil, tools.BuildServerError(ctx, "option_label_duplicated")
			}

			update = bson.M{
				"$set": bson.M{
					"info.groups.$[id]": group,
				},
			}
			opts = &options.UpdateOptions{
				ArrayFilters: &options.ArrayFilters{
					Filters: bson.A{
						bson.M{"id.id": group.ID},
					},
				},
			}
			var subGroup []models.SubGroup
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, sub := range group.SubGroup {
					subGroup = append(subGroup, models.SubGroup{
						Name:          sub.Name,
						Blind:         sub.Blind,
						IsCopyData:    sub.IsCopyData,
						SegmentLength: 0,
					})
				}
			}
			newRandomDesign.Info.Groups = append([]models.RandomGroup{}, models.RandomGroup{
				ID:       group.ID,
				Code:     group.Code,
				Name:     group.Name,
				SubGroup: subGroup,
				Status:   &group.Status,
			})
		}
		if req.Factor != nil {
			updateType = "factor"
			factor := *req.Factor
			// 校验name 是否为唯一字段
			filter := make(map[string]interface{})
			for k, v := range match {
				filter[k] = v
			}
			if factor.IsCalc {
				formulas := slice.Map(factor.Options, func(index int, item struct {
					Label      string  `json:"label" bson:"label"`
					Value      string  `json:"value" bson:"value"`
					Formula    *string `json:"formula" bson:"formula"`
					IsCopyData bool    `json:"isCopyData" bson:"is_copy_data"`
				}) string {
					return *item.Formula
				})
				err := tools.ValidateFactorFormula(ctx, formulas)
				if err != nil {
					return nil, err
				}
				calcFields := slice.Filter(form.Fields, func(index int, item models.Field) bool {
					return item.ApplicationType != nil && (*item.ApplicationType == 1 || *item.ApplicationType == 4)
				})
				calcVariables := slice.Map(calcFields, func(index int, item models.Field) string {
					return item.Variable
				})
				re := regexp.MustCompile(`\{([\x{4e00}-\x{9fa5}\w]+)\}`)
				customFormulas := factor.CustomFormulas
				matches := re.FindAllStringSubmatch(customFormulas, -1)
				fieldNames := slice.Map(matches, func(index int, item []string) string {
					return item[1]
				})
				for _, fieldName := range fieldNames {
					if factor.CalcType != nil && *factor.CalcType == 0 {
						if fieldName == "CurrentTime" {
							continue
						} else {
							if !slice.Contain(calcVariables, fieldName) {
								return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_form")
							} else {
								find, b := slice.Find(calcFields, func(index int, item models.Field) bool {
									return item.Variable == fieldName
								})
								if b && (find.Type != "inputNumber" && find.Type != "datePicker" && find.Type != "timePicker") {
									return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_type")
								}
							}
						}
					} else if factor.CalcType != nil && *factor.CalcType == 1 {
						if !slice.Contain(calcVariables, fieldName) {
							return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_form")
						} else {
							find, b := slice.Find(calcFields, func(index int, item models.Field) bool {
								return item.Variable == fieldName
							})
							if b && (find.Type != "inputNumber" && find.Type != "datePicker" && find.Type != "timePicker") {
								return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_type")
							}
						}
					}
				}
			}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: filter}},
				{{Key: "$unwind", Value: "$info.factors"}},
				{{Key: "$match", Value: bson.M{
					"info.factors.name": factor.Name,
					"info.factors.id":   bson.M{"$ne": factor.ID},
				}}},
			}
			var factors []map[string]interface{}
			cursor, err := tools.Database.Collection("random_design").Aggregate(nil, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &factors)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			filter["info.factors.name"] = factor.Name
			if len(factors) > 0 {
				return nil, tools.BuildServerError(ctx, "random_filed_error")
			}
			update = bson.M{
				"$set": bson.M{
					"info.factors.$[id]": factor,
				},
			}
			opts = &options.UpdateOptions{
				ArrayFilters: &options.ArrayFilters{
					Filters: bson.A{
						bson.M{"id.id": factor.ID},
					},
				},
			}

			var factorInfo []map[string]interface{}
			cursor, _ = tools.Database.Collection("random_design").Aggregate(nil, mongo.Pipeline{
				{{Key: "$match", Value: match}},
				{{Key: "$unwind", Value: "$info.factors"}},
				{{Key: "$match", Value: bson.M{"info.factors.id": factor.ID}}},
				{{Key: "$project", Value: bson.M{
					"name":    "$info.factors.name",
					"number":  "$info.factors.number",
					"label":   "$info.factors.label",
					"type":    "$info.factors.type",
					"options": "$info.factors.options",
				}}},
			})
			err = cursor.All(nil, &factorInfo)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var oldOptions []string
			var options []string

			for _, value := range factorInfo[0]["options"].(primitive.A) {
				oldOptions = append(oldOptions, value.(map[string]interface{})["label"].(string))
			}
			for _, value := range factor.Options {
				options = append(options, value.Label)
			}

			// 项目日志 编辑分层
			var newOptions []models.Option
			for _, option := range factor.Options {
				newOptions = append(newOptions, models.Option{
					Label:      option.Label,
					Value:      option.Value,
					Formula:    option.Formula,
					IsCopyData: option.IsCopyData,
				})
			}
			status := 1
			if factor.Status != nil {
				status = *factor.Status
			}
			newRandomDesign.Info.Factors = append(newRandomDesign.Info.Factors, models.RandomFactor{
				ID:             factor.ID,
				IsCalc:         factor.IsCalc,
				CalcType:       factor.CalcType,
				Precision:      factor.Precision,
				CustomFormulas: factor.CustomFormulas,
				Round:          factor.Round,
				Name:           factor.Name,
				Label:          factor.Label,
				Number:         factor.Number,
				Type:           factor.Type,
				DateFormat:     factor.DateFormat,
				Options:        newOptions,
				IsCopyData:     factor.IsCopyData,
				Status:         &status,
			})
		}
		_, err := tools.Database.Collection("random_design").UpdateOne(sctx, match, update, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = insertRandomDesignLog(ctx, sctx, historyID, 2, randomDesign.Info, newRandomDesign.Info, randomDesign.ID, updateType, project, "")
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 随机号生成
func (s *RandomizationService) GenerateRandomNumber(ctx *gin.Context, config models.RandomListConfigPt) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		customerOID, _ := primitive.ObjectIDFromHex(config.CustomerID)
		projectOID, _ := primitive.ObjectIDFromHex(config.ProjectID)
		envOID, _ := primitive.ObjectIDFromHex(config.EnvID)

		var siteListId []primitive.ObjectID
		if config.SiteIds != nil && len(config.SiteIds) > 0 {
			for _, item := range config.SiteIds {
				sId, _ := primitive.ObjectIDFromHex(item)
				siteListId = append(siteListId, sId)
			}
		}
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
		historyOID := envOID
		checkRandomNumberFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": bson.M{"$in": [4]int{1, 2, 4, 5}}} // 查询的随机号状态(1 未使用 2已使用)
		if config.CohortID != "" {
			cohortOID, _ := primitive.ObjectIDFromHex(config.CohortID)
			filter["cohort_id"] = cohortOID
			historyOID = cohortOID

		}
		var randomDesign models.RandomDesign
		if err := tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign); err != nil {
			return nil, errors.WithStack(err)
		}
		var attribute models.Attribute
		if err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute); err != nil {
			return nil, errors.WithStack(err)
		}

		//判断随机列表的名称是否重复
		randomFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": config.Name}
		if randomDesign.CohortID != primitive.NilObjectID {
			randomFilter["cohort_id"] = randomDesign.CohortID
		}
		if count, _ := tools.Database.Collection("random_list").CountDocuments(sctx, randomFilter); count > 0 {
			return nil, tools.BuildServerError(ctx, "random_list_name_duplicated")
		}
		randomDesign.Info.Groups = slice.Filter(randomDesign.Info.Groups, func(index int, item models.RandomGroup) bool {
			return item.Status == nil || *item.Status != 2
		})
		groups := models.DesignGroupToListGroup(config.Groups, randomDesign.Info.Groups)
		randomListInfo := models.RandomListInfo{
			Type:        randomDesign.Info.Type,
			Groups:      groups,
			Factors:     config.Factors,
			Ratio:       config.Ratio,
			Combination: randomDesign.Info.Combination,
			BlockNumber: randomDesign.Info.BlockNumber,
		}
		for i := 0; i < len(randomDesign.Info.Groups); i++ {
			randomDesign.Info.Groups[i].Ratio = config.Groups[i].Ratio
		}
		// 最小化随机设置分层因素权重比
		if randomDesign.Info.Type == 2 {
			nf := make([]models.RandomFactor, 0)
			for _, cf := range config.Factors {
				df, ok := slice.Find(randomDesign.Info.Factors, func(index int, item models.RandomFactor) bool {
					return item.Name == cf.Name
				})
				if ok {
					df.Ratio = cf.Ratio
					nf = append(nf, *df)
				}
			}
			randomListInfo.Factors = nf
			randomListInfo.Ratio = config.Ratio
		} else {
			randomListInfo.Factors = slice.Filter(randomDesign.Info.Factors, func(index int, item models.RandomFactor) bool {
				if item.Status == nil {
					return true
				}
				return *item.Status != 2
			})
		}
		var random = models.RandomList{
			ID:            primitive.NewObjectID(),
			CustomerID:    randomDesign.CustomerID,
			ProjectID:     randomDesign.ProjectID,
			EnvironmentID: randomDesign.EnvironmentID,
			CohortID:      randomDesign.CohortID,
			SiteIds:       siteListId,
			Name:          config.Name,
			LastGroup:     config.LastGroup,
			Design:        randomListInfo,
			Config: models.RandomListConfig{
				InitialValue: config.InitialValue,
				Blocks:       config.Blocks,
				Total:        config.Total,
				NumberLength: config.NumberLength,
				Probability:  config.Probability,
				Seed:         config.Seed,
				Prefix:       config.Prefix,
				EndValue:     config.EndValue,
				BlocksRule:   config.BlocksRule,
				Rule:         config.RandomNumberRule,
			},
			Status: 1,
			Meta: models.Meta{
				CreatedAt: time.Duration(time.Now().Unix()),
				CreatedBy: user.ID,
			},
		}
		//factorsCombinations := layeredArrangement(randomDesign.Info.Factors)
		randomNumbers, randomNumberArray, blockNumber, err := randomNumberGenerate(ctx, random, randomDesign.Info.BlockNumber)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 把分层因素赋值给RandomList
		//random.Design.Combination = factorsCombinations
		opts := &options.FindOptions{
			Projection: bson.M{
				"_id":    0,
				"number": 1,
			},
		}
		cursor, err := tools.Database.Collection("random_number").Find(sctx, checkRandomNumberFilter, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var oldRandomNumber []models.RandomNumber
		err = cursor.All(nil, &oldRandomNumber)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 检测是否有重复的随机号
		err = randomNumberVerification(ctx, randomNumberArray, oldRandomNumber)
		if err != nil {
			return nil, err
		}
		//添加randomList表
		if _, err := tools.Database.Collection("random_list").InsertOne(sctx, random); err != nil {
			return nil, errors.WithStack(err)
		}

		// 批量添加随机号
		if _, err := tools.Database.Collection("random_number").InsertMany(nil, randomNumbers); err != nil {
			return nil, errors.WithStack(err)
		}

		// 区组随机每次生成随机号更新区组号
		if randomDesign.Info.Type == 1 {
			upsert := true
			update := bson.M{}
			opts := &options.UpdateOptions{
				Upsert: &upsert,
			}
			update = bson.M{"$set": bson.M{"info.block_number": blockNumber}}
			res, err := tools.Database.Collection("random_design").UpdateOne(sctx, filter, update, opts)
			if err != nil || (res.ModifiedCount != 1 && res.UpsertedCount != 1) {
				return nil, tools.BuildServerError(ctx, "common.operation.fail")
			}
		}
		// 添加轨迹
		resByre, _ := json.Marshal(randomNumbers)
		var randomNumberList []models.RandomNumber
		err = json.Unmarshal(resByre, &randomNumberList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询中心
		var projectSiteStr strings.Builder
		if siteListId != nil && len(siteListId) > 0 {
			projectSites := make([]models.ProjectSite, 0)
			projectSiteCursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": siteListId}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = projectSiteCursor.All(nil, &projectSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, site := range projectSites {
				projectSiteStr.WriteString(" [")
				siteName := ""
				if site.ShortName != "" {
					siteName = site.ShortName
				} else {
					siteName = site.Name
				}
				projectSiteStr.WriteString(siteName)
				projectSiteStr.WriteString("]")
			}
		} else {
			projectSiteStr.WriteString("All")
		}

		// 项目日志
		err = insertGenerateRandomListLog(ctx, sctx, attribute, randomDesign, historyOID, 1, config, historyOID, projectSiteStr.String(), "")
		if err != nil {
			return nil, err
		}
		// 添加轨迹
		_, err = addRandomizationTrail(sctx, 2, user, random, randomNumberList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 修改randomList
func (s *RandomizationService) UpdateRandomList(ctx *gin.Context) error {
	type SitePt struct {
		Id      string   `json:"id"`
		SiteIds []string `json:"siteIds"`
	}
	var data SitePt
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		OID, _ := primitive.ObjectIDFromHex(data.Id)
		filter := bson.M{"_id": OID}

		var randomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(nil, filter).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var siteListId []primitive.ObjectID
		for _, item := range data.SiteIds {
			sId, _ := primitive.ObjectIDFromHex(item)
			siteListId = append(siteListId, sId)
		}
		update := bson.M{"$set": bson.M{"site_ids": siteListId}}
		if _, err := tools.Database.Collection("random_list").UpdateOne(sctx, filter, update); err != nil {
			return nil, errors.WithStack(err)
		}

		historyOID := randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			historyOID = randomList.CohortID
		}

		// 新的中心
		var newProjectSiteStr strings.Builder
		if siteListId != nil && len(siteListId) > 0 {
			projectSites := make([]models.ProjectSite, 0)
			projectSiteCursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": siteListId}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = projectSiteCursor.All(nil, &projectSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, site := range projectSites {
				newProjectSiteStr.WriteString(" [")
				siteName := ""
				if site.ShortName != "" {
					siteName = site.ShortName
				} else {
					siteName = site.Name
				}
				newProjectSiteStr.WriteString(siteName)
				newProjectSiteStr.WriteString("]")
			}
		} else {
			newProjectSiteStr.WriteString("All")
		}

		// 旧的中心
		var oldProjectSiteStr strings.Builder
		if randomList.SiteIds != nil && len(randomList.SiteIds) > 0 {
			projectSites := make([]models.ProjectSite, 0)
			projectSiteCursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": randomList.SiteIds}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = projectSiteCursor.All(nil, &projectSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, site := range projectSites {
				oldProjectSiteStr.WriteString(" [")
				siteName := ""
				if site.ShortName != "" {
					siteName = site.ShortName
				} else {
					siteName = site.Name
				}
				oldProjectSiteStr.WriteString(siteName)
				oldProjectSiteStr.WriteString("]")
			}
		} else {
			oldProjectSiteStr.WriteString("All")
		}

		// 项目日志
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.list",
			Value: randomList.Name,
			Blind: false,
		})
		operationLogFieldGroups := []models.OperationLogFieldGroup{}
		//operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.random_design",
		//	TranKey: "operation_log.random_list.onlyID",
		//	Old: models.OperationLogField{
		//		Type:  2,
		//		Value: OID,
		//	},
		//	New: models.OperationLogField{
		//		Type:  2,
		//		Value: nil,
		//	},
		//})
		operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.site",
			Old: models.OperationLogField{
				Type:  1,
				Value: oldProjectSiteStr.String(),
			},
			New: models.OperationLogField{
				Type:  1,
				Value: newProjectSiteStr.String(),
			},
		})

		err = tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", historyOID, 2, operationLogFieldGroups, marks, historyOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 随机表轨迹记录
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		history := models.History{
			OID:  randomList.ID,
			Key:  "history.randomization.list.site",
			Data: bson.M{"siteName": newProjectSiteStr.String()},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}
		_, historyErr := tools.Database.Collection("history").InsertOne(sctx, history)
		if historyErr != nil {
			return nil, errors.WithStack(historyErr)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// UploadRandomizationFile ..
func (s *RandomizationService) UploadRandomizationFile(ctx *gin.Context) error {
	allExist := true
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		files, err := ctx.FormFile("files")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// TODO 上传验证
		file, _ := files.Open()
		f, _ := excelize.OpenReader(file)
		rows, _ := f.GetRows(f.GetSheetList()[0])
		if len(rows) == 1 {
			return nil, tools.BuildServerError(ctx, "file_emtpy")
		}
		projectOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("project"))
		envOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("env"))
		customerOID, _ := primitive.ObjectIDFromHex(ctx.PostForm("customer"))
		siteIds := ctx.PostForm("siteIds")

		var siteListId []primitive.ObjectID
		if siteIds != "" {
			siteSlices := strings.Split(siteIds, ",")
			for _, item := range siteSlices {
				sId, _ := primitive.ObjectIDFromHex(item)
				siteListId = append(siteListId, sId)
			}
		}

		var cohortOID primitive.ObjectID
		if ctx.PostForm("cohort") != "null" {
			cohortOID, _ = primitive.ObjectIDFromHex(ctx.PostForm("cohort"))
		}
		blockSizes := strings.Split(ctx.PostForm("groupSize"), ",")
		name := ctx.PostForm("name")
		skipGroupTip := ctx.PostForm("needSkip")
		//判断随机列表的名称是否重复
		randomFilter := bson.M{"customer_id": customerOID, "env_id": envOID, "name": name}
		if cohortOID != primitive.NilObjectID {
			randomFilter = bson.M{"customer_id": customerOID, "env_id": envOID, "cohort_id": cohortOID, "name": name}
		}
		if count, _ := tools.Database.Collection("random_list").CountDocuments(sctx, randomFilter); count > 0 {
			return nil, tools.BuildServerError(ctx, "random_list_name_duplicated")
		}
		user, _ := tools.Me(ctx)
		var randomNumbers []interface{}
		config := models.RandomListConfig{
			BlockSizes: blockSizes,
			Total:      len(rows) - 1,
		}
		var randomDesign models.RandomDesign
		historyOID := envOID
		filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
		if ctx.PostForm("cohort") != "null" {
			cohortOID, _ = primitive.ObjectIDFromHex(ctx.PostForm("cohort"))
			historyOID = cohortOID
			filter["cohort_id"] = cohortOID
		}
		if err := tools.Database.Collection("random_design").FindOne(sctx, filter).Decode(&randomDesign); err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		//校验已激活的随机表的分层因素是否和配置一致
		oldRandomList := make([]models.RandomList, 0)
		filter["status"] = 1
		cursor, err := tools.Database.Collection("random_list").Find(sctx, filter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &oldRandomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		effectFactors := slice.Filter(randomDesign.Info.Factors, func(index int, item models.RandomFactor) bool {
			if item.Status == nil {
				return true
			}
			return *item.Status != 2
		})
		for _, list := range oldRandomList {
			listEffectFactors := slice.Filter(list.Design.Factors, func(index int, item models.RandomFactor) bool {
				if item.Status == nil {
					return true
				}
				return *item.Status != 2
			})
			if len(effectFactors) != len(listEffectFactors) {
				return nil, tools.BuildServerError(ctx, "random_list_upload_error")
			}
			for _, factor := range effectFactors {
				f, ok := slice.Find(listEffectFactors, func(index int, item models.RandomFactor) bool {
					return factor.Name == item.Name
				})
				if !ok || factor.Label != f.Label || len(factor.Options) != len(f.Options) {
					return nil, tools.BuildServerError(ctx, "random_list_upload_error")
				}
				for i, option := range factor.Options {
					if option.Label != f.Options[i].Label || option.Value != f.Options[i].Value {
						return nil, tools.BuildServerError(ctx, "random_list_upload_error")
					}
				}
			}
		}
		randomDesign.Info.Factors = effectFactors
		randomList := models.RandomList{
			ID:            primitive.NewObjectID(),
			CustomerID:    customerOID,
			ProjectID:     projectOID,
			EnvironmentID: envOID,
			CohortID:      cohortOID,
			SiteIds:       siteListId,
			Name:          name,
			LastGroup:     ctx.PostForm("lastGroup"),
			Config:        config,
			Status:        1,
			Meta: models.Meta{
				CreatedAt: time.Duration(time.Now().Unix()),
				CreatedBy: user.ID,
			},
		}

		var cellBlocks []int
		checkBlock := map[int]int{}

		groups := map[string]int{}
		for _, group := range randomDesign.Info.Groups {
			if group.Status == nil || *group.Status == 1 {
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					for _, subGroup := range group.SubGroup {
						groups[group.Name+" "+subGroup.Name] = 0
					}
				} else {
					groups[group.Name] = 0
				}
			}
		}

		// 获取旧的随机号
		checkRandomNumberFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "status": bson.M{"$in": [3]int{1, 2, 4}}} // 查询的随机号状态(1 未使用 2已使用)
		cursor, err = tools.Database.Collection("random_number").Aggregate(sctx, mongo.Pipeline{
			{{"$match", checkRandomNumberFilter}},
			{{"$lookup", bson.M{
				"from":         "random_list",
				"localField":   "random_list_id",
				"foreignField": "_id",
				"as":           "random_list",
			}}},
			{{"$project", bson.M{
				"_id":    1,
				"number": 1,
				"status": bson.M{"$first": "$random_list.status"},
			}}},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var oldRandomNumber []models.CheckNumber
		err = cursor.All(nil, &oldRandomNumber)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		checkNumberMap := map[string]bool{}
		for _, number := range oldRandomNumber {
			if number.Status == 3 {
				// 随机号在作废的列表上
				checkNumberMap[number.Number] = true
			} else {
				// 随机号在启用的列表上
				checkNumberMap[number.Number] = false
			}
		}

		useNumber := map[string]bool{}

		headers := rows[0]
		maxColumns := 10 // 最大检查列数
		validHeaders := make([]string, 0)
		foundEmpty := false // 标记是否已遇到空列

		// 第一列必须存在
		if len(headers) == 0 || strings.TrimSpace(headers[0]) == "" {
			return nil, tools.BuildServerError(ctx, "col_empty_error")
		}

		for i := 0; i < maxColumns; i++ {
			// 处理实际列数不足的情况
			if i >= len(headers) {
				break
			}
			header := strings.TrimSpace(headers[i])
			if header == "" {
				// 遇到第一个空列时记录状态
				foundEmpty = true
			} else {
				// 如果已经遇到过空列，后续又出现非空列
				if foundEmpty {
					return nil, tools.BuildServerError(ctx, "col_empty_error")
				}
				validHeaders = append(validHeaders, header)
			}
		}
		blockHeader := locales.Tr(ctx, "report.attributes.random.block")
		numberHeader := locales.Tr(ctx, "report.attributes.random.number")
		groupHeader := locales.Tr(ctx, "report.attributes.random.group")
		subGroupHeader := ""
		_, b := slice.Find(randomDesign.Info.Groups, func(index int, item models.RandomGroup) bool {
			return item.SubGroup != nil && len(item.SubGroup) > 0
		})
		if b {
			subGroupHeader = locales.Tr(ctx, "report.attributes.random.sub.group")
		}
		requiredHeaders := map[string]bool{
			blockHeader:  false,
			numberHeader: false,
			groupHeader:  false,
		}
		if b {
			requiredHeaders = map[string]bool{
				blockHeader:    false,
				numberHeader:   false,
				groupHeader:    false,
				subGroupHeader: false,
			}
		}
		customHeaders := make([]string, 0)
		for _, h := range validHeaders {
			if _, exists := requiredHeaders[h]; exists {
				requiredHeaders[h] = true
			} else {
				customHeaders = append(customHeaders, h)
			}
		}
		randomList.CustomHeaders = customHeaders
		//todo 校验必填表头
		//-------------------------------------
		for rowIndex, row := range rows[1:] {
			// 动态获取关键列值
			type UploadRow struct {
				Block     string            // 区组值
				Group     string            // 组别值
				SubGroup  string            // 子组别值
				Number    string            // 随机号
				Custom    map[string]string // 自定义列（列头->单元格值）
				RowNumber int               // 行号（用于报错定位）
			}
			uploadRow := UploadRow{
				Custom:    make(map[string]string),
				RowNumber: rowIndex + 2, // Excel行号从1开始
			}
			for i, h := range validHeaders {
				if i < len(row) {
					cellValue := strings.TrimSpace(row[i])
					switch h {
					case blockHeader:
						uploadRow.Block = cellValue
					case numberHeader:
						uploadRow.Number = cellValue
					case groupHeader:
						uploadRow.Group = cellValue
					case subGroupHeader:
						uploadRow.SubGroup = cellValue
					default:
						// 存储自定义列
						uploadRow.Custom[h] = cellValue
					}
				}
			}

			// 数据缺失 格式错误
			if useNumber[uploadRow.Number] {
				return nil, tools.BuildServerError(ctx, "random_number_error")
			}
			if len(row) < 3 || uploadRow.Block == "" || uploadRow.Number == "" || uploadRow.Group == "" {
				return nil, tools.BuildServerError(ctx, "random_number_format_error")
			}
			block, _ := strconv.ParseFloat(uploadRow.Block, 32)

			check, ok := checkNumberMap[uploadRow.Number]
			if ok { // 如果 map表里面存在， 并且 随机号在启用状态的随机列表上
				if check {
					// 上传随机号在作废随机表上并已经使用， 则过滤不插入。  区组大小不做过滤
					checkBlock[int(block)] = checkBlock[int(block)] + 1
					continue
				} else {
					return nil, tools.BuildServerError(ctx, "random_number_error")

				}
			}
			group := uploadRow.Group
			if uploadRow.SubGroup != "" {
				group = uploadRow.Group + " " + uploadRow.SubGroup
			}
			if _, ok := groups[group]; !ok {
				return nil, tools.BuildServerError(ctx, "randomization.upload.group_error")
			}
			groups[group]++

			randomNumbers = append(randomNumbers, models.RandomNumber{
				ID:            primitive.NewObjectID(),
				CustomerID:    customerOID,
				ProjectID:     projectOID,
				EnvironmentID: envOID,
				RandomListID:  randomList.ID,
				CohortID:      cohortOID,
				Block:         int(block),
				Number:        uploadRow.Number,
				Group:         group,
				ParName:       uploadRow.Group,
				SubName:       uploadRow.SubGroup,
				Status:        1,
				Custom:        uploadRow.Custom,
			})
			cellBlocks = append(cellBlocks, int(block))
			checkBlock[int(block)] = checkBlock[int(block)] + 1
			useNumber[uploadRow.Number] = true
		}
		groupMap := slice.GroupWith(randomNumbers, func(item interface{}) string {
			number := item.(models.RandomNumber)
			return number.Group
		})
		notExist := true
		for k, _ := range groupMap {
			for _, group := range randomDesign.Info.Groups {
				if group.Status == nil || *group.Status == 1 {
					if group.SubGroup != nil && len(group.SubGroup) > 0 {
						for _, subGroup := range group.SubGroup {
							if k == fmt.Sprintf("%s %s", group.Name, subGroup.Name) {
								notExist = false
							}
						}
					} else {
						if k == group.Name {
							notExist = false
						}
					}
				}
			}
		}
		if notExist {
			return nil, tools.BuildServerError(ctx, "randomization.upload.group_error")
		}

		keys := maputil.Keys(groupMap)
		for _, group := range randomDesign.Info.Groups {
			if group.Status == nil || *group.Status == 1 {
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					for _, subGroup := range group.SubGroup {
						_, b := slice.Find(keys, func(index int, item string) bool {
							return item == fmt.Sprintf("%s %s", group.Name, subGroup.Name)
						})
						if !b {
							allExist = false
						}

					}
				} else {
					_, b := slice.Find(keys, func(index int, item string) bool {
						return item == group.Name
					})
					if !b {
						allExist = false
					}
				}
			}
		}
		if !allExist && skipGroupTip != "true" {
			return nil, tools.BuildServerValidateError(ctx, []string{"randomization.upload.group_tip_error"}, nil, tools.GroupTipValidationError)
		}
		// 检查区组是否匹配
		mapBlockSizes := map[string]bool{}
		for _, blockSize := range blockSizes {
			mapBlockSizes[blockSize] = true
		}
		for _, value := range checkBlock {
			if _, ok := mapBlockSizes[fmt.Sprintf("%d", value)]; !ok {
				return nil, tools.BuildServerError(ctx, "randomization.upload.blockSize")
			}
		}

		resByre, _ := json.Marshal(randomNumbers)
		var newData []models.RandomNumber
		err = json.Unmarshal(resByre, &newData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//err = randomNumberVerification(ctx, newData, oldRandomNumber)
		//if err != nil {
		//	return nil, err
		//}

		// 检查是否有重复区组
		blocks := tools.RemoveRepeatedElement(cellBlocks)
		var matchBlock bson.A
		for _, block := range blocks {

			matchBlock = append(matchBlock, bson.M{"block": block})
		}
		var limit int64 = 1
		match := bson.M{"project_id": projectOID, "env_id": envOID, "customer_id": customerOID, "cohort_id": cohortOID, "status": bson.M{"$in": [2]int{1, 2}}, "$or": matchBlock}
		//过滤已作废的随机列表在进行检查
		var randomLists []models.RandomList
		cursor, _ = tools.Database.Collection("random_list").Find(sctx, bson.M{"project_id": projectOID, "env_id": envOID, "customer_id": customerOID, "cohort_id": cohortOID, "status": 3})
		cursor.All(nil, &randomLists)
		if len(randomLists) != 0 {
			var randomListItem []primitive.ObjectID
			for _, list := range randomLists {
				randomListItem = append(randomListItem, list.ID)
			}
			match["random_list_id"] = bson.M{"$nin": randomListItem}
		}
		opt := &options.FindOptions{
			Limit: &limit,
		}
		cursors, _ := tools.Database.Collection("random_number").Find(sctx, match, opt)
		var randomNumberBlock []map[string]interface{}
		err = cursors.All(nil, &randomNumberBlock)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(randomNumberBlock) > 0 {
			return nil, tools.BuildServerError(ctx, "random_number_block_error")
		}

		// factorsCombinations := layeredArrangement(randomDesign.Info.Factors)
		// // 把分层因素赋值给RandomList
		// randomList.Design.Combination = factorsCombinations

		// 计算组间比例
		num := 1
		var gcds []int
		for _, group := range randomDesign.Info.Groups {
			if group.Status == nil || *group.Status == 1 {
				g := group.Name
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					for _, subGroup := range group.SubGroup {
						g = group.Name + " " + subGroup.Name
						if groups[g] != 0 {
							gcds = append(gcds, groups[g])
						}
					}
				} else {
					if groups[g] != 0 {
						gcds = append(gcds, groups[g])
					}
				}
			}
		}
		info := models.RandomListInfo{
			Type:    randomDesign.Info.Type,
			Groups:  nil,
			Factors: randomDesign.Info.Factors,
			Ratio:   randomDesign.Info.Ratio,
			//Combination: factorsCombinations,
			BlockNumber: randomDesign.Info.BlockNumber,
		}

		listGroups := make([]models.RandomListGroup, 0)
		for _, group := range randomDesign.Info.Groups {
			if group.Status == nil || *group.Status == 1 {
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					for _, subGroup := range group.SubGroup {
						listGroups = append(listGroups, models.RandomListGroup{
							ID:            group.ID,
							Code:          group.Code,
							Name:          group.Name + " " + subGroup.Name,
							ParName:       group.Name,
							SubName:       subGroup.Name,
							Blind:         subGroup.Blind,
							Ratio:         group.Ratio,
							Description:   group.Description,
							SegmentLength: subGroup.SegmentLength,
						})
					}
				} else {
					listGroups = append(listGroups, models.RandomListGroup{
						ID:            group.ID,
						Code:          group.Code,
						Name:          group.Name,
						ParName:       group.Name,
						SubName:       "",
						Blind:         false,
						Ratio:         group.Ratio,
						Description:   group.Description,
						SegmentLength: group.SegmentLength,
					})
				}
			}
		}
		if len(gcds) == len(listGroups) {
			num = multiGcd(gcds)
		}
		for i, g := range listGroups {
			listGroups[i].Ratio = groups[g.Name] / num
		}
		info.Groups = listGroups
		randomList.Design = info
		if _, err := tools.Database.Collection("random_list").InsertOne(sctx, randomList); err != nil {
			return nil, errors.WithStack(err)

		}
		if _, err := tools.Database.Collection("random_number").InsertMany(nil, randomNumbers); err != nil {
			return nil, errors.WithStack(err)

		}

		// 查询中心
		var projectSiteStr strings.Builder
		if siteListId != nil && len(siteListId) > 0 {
			projectSites := make([]models.ProjectSite, 0)
			projectSiteCursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": siteListId}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = projectSiteCursor.All(nil, &projectSites)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, site := range projectSites {
				projectSiteStr.WriteString(" [")
				siteName := ""
				if site.ShortName != "" {
					siteName = site.ShortName
				} else {
					siteName = site.Name
				}
				projectSiteStr.WriteString(siteName)
				projectSiteStr.WriteString("]")
			}
		} else {
			projectSiteStr.WriteString("All")
		}

		// 项目日志
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.list",
			Value: randomList.Name,
			Blind: false,
		})
		operationLogFieldGroups := []models.OperationLogFieldGroup{}
		operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.name",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: randomList.Name,
			},
		})
		operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.site",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: projectSiteStr.String(),
			},
		})
		operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.size",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: ctx.PostForm("groupSize"),
			},
		})
		operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.file_name",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: files.Filename,
			},
		})
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", historyOID, 1, operationLogFieldGroups, marks, historyOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 添加轨迹
		_, err = addRandomizationTrail(sctx, 1, user, randomList, newData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RandomizationService) DeleteRandomGroup(ctx *gin.Context) error {
	var data map[string]interface{}
	if err := ctx.ShouldBindBodyWith(&data, binding.JSON); err != nil {
		return errors.WithStack(err)
	}
	projectOID, _ := primitive.ObjectIDFromHex(data["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(data["envId"].(string))
	customerOID, _ := primitive.ObjectIDFromHex(data["customerId"].(string))
	historyID := envOID

	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	if data["cohortId"] != nil {
		cohortOID, _ := primitive.ObjectIDFromHex(data["cohortId"].(string))
		match = bson.M{
			"project_id":  projectOID,
			"env_id":      envOID,
			"customer_id": customerOID,
			"cohort_id":   cohortOID,
		}
		historyID = cohortOID
	}
	OID, _ := primitive.ObjectIDFromHex(data["groupId"].(string))
	update := bson.M{
		"$pull": bson.M{
			"info.groups": bson.M{
				"id": OID,
			},
		},
	}

	// 项目日志

	var histories []models.History
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var group []map[string]interface{}
		cursor, _ := tools.Database.Collection("random_design").Aggregate(sctx, mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$info.groups"}},
			{{Key: "$match", Value: bson.M{"info.groups.id": OID}}},
			{{Key: "$project", Value: bson.M{
				"id":   "$info.groups.id",
				"code": "$info.groups.code",
				"name": "$info.groups.name",
			}}},
		})
		err = cursor.All(nil, &group)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		histories = append(histories, models.History{
			Key: "history.randomization.config.group.delete",
			OID: historyID,
			Data: bson.M{
				"number": group[0]["code"],
				"name":   group[0]["name"],
			},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		})

		ctx.Set("HISTORY", histories)
		// 项目日志
		var marks []models.Mark
		marks = append(marks, models.Mark{
			Label: "operation_log.label.group",
			Value: group[0]["code"].(string),
			Blind: true,
		})
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", historyID, 3, []models.OperationLogFieldGroup{}, marks, group[0]["id"].(primitive.ObjectID))
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if _, err := tools.Database.Collection("random_design").UpdateOne(sctx, match, update); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
func (s *RandomizationService) DeleteRandomFactor(ctx *gin.Context) error {
	var data map[string]interface{}
	if err := ctx.ShouldBindBodyWith(&data, binding.JSON); err != nil {
		return errors.WithStack(err)
	}
	projectOID, _ := primitive.ObjectIDFromHex(data["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(data["envId"].(string))
	customerOID, _ := primitive.ObjectIDFromHex(data["customerId"].(string))
	historyID := envOID
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	subjectFilter := bson.M{"env_id": envOID, "info.name": data["factorName"].(string), "deleted": bson.M{"$ne": true}}
	if data["cohortId"] != nil {
		cohortOID, _ := primitive.ObjectIDFromHex(data["cohortId"].(string))
		subjectFilter["cohort_id"] = cohortOID
		match = bson.M{
			"project_id":  projectOID,
			"env_id":      envOID,
			"customer_id": customerOID,
			"cohort_id":   cohortOID,
		}
		historyID = cohortOID
	}
	count, err := tools.Database.Collection("subject").CountDocuments(nil, subjectFilter)
	if err != nil {
		return errors.WithStack(err)
	}
	//已有受试者使用就不能删除
	if count > 0 {
		return tools.BuildServerError(ctx, "form_field_used")
	}

	update := bson.M{
		"$pull": bson.M{
			"info.factors": bson.M{
				"name": data["factorName"],
			},
		},
	}

	var histories []models.History
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	var factors []map[string]interface{}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		cursor, _ := tools.Database.Collection("random_design").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$info.factors"}},
			{{Key: "$match", Value: bson.M{"info.factors.name": data["factorName"]}}},
			{{Key: "$project", Value: bson.M{
				"id":      "$info.factors.id",
				"name":    "$info.factors.name",
				"number":  "$info.factors.number",
				"label":   "$info.factors.label",
				"type":    "$info.factors.type",
				"options": "$info.factors.options",
			}}},
		})
		err = cursor.All(nil, &factors)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var options []string
		for _, value := range factors[0]["options"].(primitive.A) {
			options = append(options, value.(map[string]interface{})["label"].(string))
		}

		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		key := "history.randomization.config.factor.delete"
		if project.ProjectInfo.ConnectEdc == 1 {
			key = "history.randomization.config.factor.deleteEDC"
		}

		histories = append(histories, models.History{
			Key: key,
			OID: historyID,
			Data: bson.M{
				"oldNumber":  factors[0]["number"],
				"oldName":    factors[0]["name"],
				"oldLabel":   factors[0]["label"],
				"oldType":    factors[0]["type"],
				"oldOptions": strings.Join(options, "、"),
			},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		})

		ctx.Set("HISTORY", histories)
		// 项目日志
		var marks []models.Mark
		marks = append(marks, models.Mark{
			Label: "operation_log.label.factor",
			Value: factors[0]["number"].(string),
			Blind: false,
		})
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", envOID, 3, []models.OperationLogFieldGroup{}, marks, factors[0]["id"].(primitive.ObjectID))
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if _, err := tools.Database.Collection("random_design").UpdateOne(sctx, match, update); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) GetRandomList(ctx *gin.Context, projectID string, customerID string, envID string, cohortID string, roleId string) ([]map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{
		"customer_id": customerOID,
		"project_id":  projectOID,
		"env_id":      envOID,
	}

	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match = bson.M{
			"customer_id": customerOID,
			"project_id":  projectOID,
			"env_id":      envOID,
			"cohort_id":   cohortOID,
		}
	}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "meta.created_by", "foreignField": "_id", "as": "meta.createdUser"}}},
	}

	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("random_list").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	isBlindedRole := false
	if attribute.AttributeInfo.Blind {
		isBlindedRole, err = tools.IsBlindedRole(roleId)
		if err != nil {
			return nil, err
		}
	}

	// 查询当前环境下的中心
	projectSites := make([]models.ProjectSite, 0)
	projectSiteCursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = projectSiteCursor.All(nil, &projectSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 此处需要调整为连接查询
	for i := 0; i < len(data); i++ {
		var projectSiteData []models.ProjectSite
		if data[i]["site_ids"] != nil {
			// 转数组
			siteIds := data[i]["site_ids"].(primitive.A)
			for _, sid := range siteIds {
				for _, ps := range projectSites {
					if sid == ps.ID {
						projectSiteData = append(projectSiteData, ps)
					}
				}
			}
		}
		data[i]["projectSiteData"] = projectSiteData
		randomNumberMatch := bson.M{
			"random_list_id": data[i]["_id"],
		}
		randomNumberCursor, _ := tools.Database.Collection("random_number").CountDocuments(nil, randomNumberMatch)
		randomNumberUseMatch := bson.M{
			"random_list_id": data[i]["_id"],
			"status":         2,
		}
		randomNumberUseCursor, _ := tools.Database.Collection("random_number").CountDocuments(nil, randomNumberUseMatch)
		data[i]["use"] = randomNumberUseCursor
		data[i]["count"] = randomNumberCursor
		if isBlindedRole {
			for j := 0; j < len(data[i]["design"].(map[string]interface{})["groups"].(primitive.A)); j++ {
				data[i]["design"].(map[string]interface{})["groups"].(primitive.A)[j].(map[string]interface{})["code"] = tools.BlindData
				data[i]["design"].(map[string]interface{})["groups"].(primitive.A)[j].(map[string]interface{})["name"] = tools.BlindData
			}

		}
	}
	return data, nil
}

func (s *RandomizationService) GetRandomListConfigure(ctx *gin.Context, randomListID string) (models.RandomList, error) {
	var randomList models.RandomList
	randomListOID, _ := primitive.ObjectIDFromHex(randomListID)
	match := bson.M{
		"_id": randomListOID,
	}
	if err := tools.Database.Collection("random_list").FindOne(nil, match).Decode(&randomList); err != nil {
		return randomList, errors.WithStack(err)
	}
	return randomList, nil
}

func (s *RandomizationService) GetRandomNumberGroup(ctx *gin.Context, randomListID string) ([]map[string]interface{}, error) {
	req := struct {
		Block      []int  `json:"block"`
		StatusType int    `json:"statusType"`
		Status     []int  `json:"status"`
		RangeType  int    `json:"rangeType"`
		StartStr   string `json:"startStr"`
		EndStr     string `json:"endStr"`
	}{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	block := req.Block
	randomListOID, _ := primitive.ObjectIDFromHex(randomListID)
	match := bson.M{"random_list_id": randomListOID, "block": bson.M{"$in": block}}
	if req.StatusType == 2 && req.Status != nil && len(req.Status) > 0 {
		matchStatus := bson.A{}
		if slice.Contain(req.Status, 1) {
			matchStatus = append(matchStatus, 1)
		}
		if slice.Contain(req.Status, 2) {
			matchStatus = append(matchStatus, 2)
		}
		if slice.Contain(req.Status, 3) {
			matchStatus = append(matchStatus, 3)
			matchStatus = append(matchStatus, 4)
		}
		if slice.Contain(req.Status, 5) {
			matchStatus = append(matchStatus, 5)
		}
		match["status"] = bson.M{"$in": matchStatus}
	}
	if req.RangeType == 2 {
		if req.StartStr != "" && req.EndStr != "" {
			match["number"] = bson.M{"$gte": req.StartStr, "$lte": req.EndStr}
		} else if req.StartStr != "" {
			match["number"] = bson.M{"$gte": req.StartStr}
		} else if req.EndStr != "" {
			match["number"] = bson.M{"$lte": req.EndStr}
		}
	}
	projectSiteLookup := bson.M{
		"from":         "project_site",
		"localField":   "project_site_id",
		"foreignField": "_id",
		"as":           "project_site",
	}
	subjectLookup := bson.M{
		"from":         "subject",
		"localField":   "subject_id",
		"foreignField": "_id",
		"as":           "subject",
	}
	countryLookup := bson.M{
		"from":         "country",
		"localField":   "country",
		"foreignField": "code",
		"as":           "country",
	}
	regionLookup := bson.M{
		"from":         "region",
		"localField":   "region_id",
		"foreignField": "_id",
		"as":           "region",
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: projectSiteLookup}},

		{{Key: "$unwind", Value: bson.M{"path": "$project_site", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: subjectLookup}},
		{{Key: "$lookup", Value: countryLookup}},
		{{Key: "$lookup", Value: regionLookup}},
		{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"_id":         1,
			"block":       1,
			"number":      1,
			"group":       1,
			"plan_number": 1,
			"factors":     1,
			"status":      1,
			"country":     1,
			"site":        models.ProjectSiteNameLookUpBson(ctx),
			"subject":     "$subject.info",
			"key":         "$number",
			"region":      "$region.name",
		}}},
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
		{{Key: "$group", Value: bson.M{"_id": "$block", "item": bson.M{"$push": bson.M{
			"block":       "$block",
			"number":      "$number",
			"group":       "$group",
			"plan_number": "$plan_number",
			"factors":     "$factors",
			"status":      "$status",
			"site":        "$site",
			"subject":     "$subject",
			"country":     "$country",
			"region":      "$region",
			"key":         "$number",
		},
		}}}},
	}
	cursor, err := tools.Database.Collection("random_number").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []map[string]interface{}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for i, item := range data {
		for j, key := range item["item"].(primitive.A) {
			if key.(map[string]interface{})["factors"] != nil {
				for _, factor := range key.(map[string]interface{})["factors"].(primitive.A) {
					data[i]["item"].(primitive.A)[j].(map[string]interface{})[factor.(map[string]interface{})["name"].(string)] = factor.(map[string]interface{})["text"]
				}
			}
		}
	}
	return data, nil
}

func (s *RandomizationService) GetFactorCombination(ctx *gin.Context, ID string) (map[string]interface{}, error) {
	OID, _ := primitive.ObjectIDFromHex(ID)
	match := bson.M{"_id": OID}
	var randomList models.RandomList
	err := tools.Database.Collection("random_list").FindOne(nil, match).Decode(&randomList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$design.combination"}},
		{{Key: "$lookup", Value: bson.M{
			"from": "random_number",
			"let": bson.M{
				"id":      "$_id",
				"factors": "$design.combination.layered_factors",
			},
			"pipeline": mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$random_list_id", "$$id"}}}}},
				{{Key: "$match", Value: bson.M{"subject_id": bson.M{"$ne": primitive.NilObjectID}, "factors": bson.M{"$ne": nil}}}},
				// 过滤停用的随机号
				{{Key: "$lookup", Value: bson.M{
					"from":         "subject",
					"localField":   "subject_id",
					"foreignField": "_id",
					"as":           "subject",
				}}},
				{{Key: "$match", Value: bson.M{"subject.status": bson.M{"$ne": 5}}}},
			},
			"as": "random_number",
		}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":             0,
				"id":              "$design.combination.id",
				"layered_factors": "$design.combination.layered_factors",
				"estimate_number": "$design.combination.estimate_number",
				"warn_number":     "$design.combination.warn_number",
				"random_number":   "$random_number",
			},
		}},
	}

	datas := make([]struct {
		ID             primitive.ObjectID    `bson:"id" json:"id"`
		LayeredFactors []models.Factors      `bson:"layered_factors" json:"layered_factors"`
		EstimateNumber int                   `bson:"estimate_number" json:"estimate_number"`
		WarnNumber     int                   `bson:"warn_number" json:"warn_number"`
		RandomNumber   []models.RandomNumber `bson:"random_number" json:"randomNumber"`
		Count          int                   `json:"count"`
	}, 0)
	cursor, err := tools.Database.Collection("random_list").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &datas)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	randomListBeReplaceSub, err := randomListBeReplaceSubject(ctx, randomList.EnvironmentID, randomList.ID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	replaceSub, err := replaceSubject(ctx, randomList.EnvironmentID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	subjects := []models.Subject{}
	for _, subject := range randomListBeReplaceSub {
		resultSubject := ReplaceSubjectResult(ctx, subject.ReplaceSubjectID, replaceSub)
		if !resultSubject.ID.IsZero() {
			subjects = append(subjects, resultSubject)
		}
	}

	for i, data := range datas {
		count := slice.Count(data.RandomNumber, func(index int, number models.RandomNumber) bool {
			bools := slice.Map(data.LayeredFactors, func(index int, item models.Factors) bool {
				_, b := slice.Find(number.Factors, func(index int, factor models.Factors) bool {
					return factor.Value == item.Value && factor.Text == item.Text && factor.Name == item.Name && factor.Label == item.Label
				})
				return b
			})
			return slice.Every(bools, func(index int, item bool) bool {
				return item == true
			})
		})

		countSubject := slice.Count(subjects, func(index int, number models.Subject) bool {
			bools := slice.Map(data.LayeredFactors, func(index int, item models.Factors) bool {
				_, b := slice.Find(number.Info, func(index int, factor models.Info) bool {
					return factor.Value == item.Value && factor.Name == item.Name
				})
				return b
			})
			return slice.Every(bools, func(index int, item bool) bool {
				return item == true
			})
		})

		datas[i].Count = count + countSubject

	}
	for i, _ := range datas {
		datas[i].RandomNumber = nil
	}

	factors := randomList.Design.Factors

	result := make(map[string]interface{})
	result["data"] = datas
	result["factors"] = factors

	return result, nil
}

// 获取替换的受试者信息关联信息
func randomListBeReplaceSubject(ctx *gin.Context, envOID, OID primitive.ObjectID) ([]models.Subject, error) {
	cursor, err := tools.Database.Collection("subject").Find(nil, bson.M{
		"env_id": envOID, "group": bson.M{"$ne": ""}, "random_list_id": OID, "status": 5,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjects []models.Subject
	err = cursor.All(nil, &subjects)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return subjects, nil
}

// 获取替换的受试者信息关联信息
func replaceSubject(ctx *gin.Context, envOID primitive.ObjectID) (map[primitive.ObjectID]models.Subject, error) {
	cursor, err := tools.Database.Collection("subject").Find(nil, bson.M{
		"env_id": envOID, "group": bson.M{"$ne": ""}, "random_list_id": primitive.NilObjectID,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	data := make(map[primitive.ObjectID]models.Subject)

	var subjects []models.Subject
	err = cursor.All(nil, &subjects)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, subject := range subjects {
		if !subject.ReplaceSubjectID.IsZero() {
			subjectP, ok := slice.Find(subjects, func(index int, item models.Subject) bool {
				return subject.ReplaceSubjectID == item.ID
			})
			if ok {
				sub := *subjectP
				data[subject.ID] = sub
			} else {
				data[subject.ID] = subject
			}

		} else {
			data[subject.ID] = subject
		}
	}
	return data, nil
}

func ReplaceSubjectResult(ctx *gin.Context, subjectOID primitive.ObjectID, replaceSubject map[primitive.ObjectID]models.Subject) models.Subject {
	// a替换b b替换c c替换d
	key := subjectOID
	subject := models.Subject{}
	for _ = range replaceSubject {
		if replaceSubject[key].ReplaceSubjectID.IsZero() {
			subject = replaceSubject[key]
			break
		}
		key = replaceSubject[key].ReplaceSubjectID
	}
	return subject
}

func (s *RandomizationService) UpdateRandomListStatus(ctx *gin.Context, randomList models.RandomList) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	filter := bson.M{"_id": randomList.ID}
	var status int8
	history := models.History{
		OID:  randomList.ID,
		Time: time.Duration(time.Now().Unix()),
		UID:  user.ID,
		User: user.Name,
	}
	oldStatus := 1
	if 1 == randomList.Status {
		status = 2
		history.Key = "history.randomization.list.disableStatus"
	} else {
		oldStatus = 2
		status = 1
		history.Key = "history.randomization.list.enableStatus"
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		update := bson.M{"$set": bson.M{"status": status}}
		if _, err := tools.Database.Collection("random_list").UpdateOne(sctx, filter, update); err != nil {
			return nil, errors.WithStack(err)
		}

		_, historyEerr := tools.Database.Collection("history").InsertOne(sctx, history)
		if historyEerr != nil {
			return nil, errors.WithStack(historyEerr)
		}

		// 插入项目
		err = tools.Database.Collection("random_list").FindOne(sctx, filter).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)

		}
		history.Data = bson.M{"name": randomList.Name}
		history.OID = randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			history.OID = randomList.CohortID
		}
		history.Key = strings.Replace(history.Key, "history.randomization", "history.randomization.config", 1)
		_, historyEerr = tools.Database.Collection("history").InsertOne(sctx, history)
		if historyEerr != nil {
			return nil, errors.WithStack(historyEerr)
		}

		// 项目日志
		var OperationLogFieldGroups []models.OperationLogFieldGroup
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldStatus,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: status,
			},
		})
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.list",
			Value: randomList.Name,
			Blind: false,
		})
		OID := randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			OID = randomList.CohortID
		}
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", OID, 2, OperationLogFieldGroups, marks, randomList.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 如果是禁用了。需要解除模拟随机里面绑定的随机表ID（randomListID）
		if 2 == randomList.Status {
			match := bson.M{"simulate_random_info.random_list_ids": randomList.ID.Hex()}
			var simulateRandomList []models.SimulateRandom
			cursor, err := tools.Database.Collection("simulate_random").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &simulateRandomList)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if simulateRandomList != nil && len(simulateRandomList) > 0 {
				for _, srl := range simulateRandomList {
					var randomListIds []string
					for _, randomId := range srl.SimulateRandomInfo.RandomListIds {
						if randomId != randomList.ID.Hex() {
							randomListIds = append(randomListIds, randomId)
						}
					}

					filter = bson.M{"_id": srl.ID}
					update = bson.M{"$set": bson.M{"simulate_random_info.random_list_ids": randomListIds}}
					if _, err := tools.Database.Collection("simulate_random").UpdateOne(sctx, filter, update); err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		}

		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// UpdateRandomListInvalid 随机表作废
func (s *RandomizationService) UpdateRandomListInvalid(ctx *gin.Context, randomList models.RandomList) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		filter := bson.M{"_id": randomList.ID}
		update := bson.M{"$set": bson.M{"status": 3}}
		if _, err := tools.Database.Collection("random_list").UpdateOne(sctx, filter, update); err != nil {
			return nil, errors.WithStack(err)
		}

		// 修改random_number表的随机号状态为作废
		randomNumberFilter := bson.M{"random_list_id": randomList.ID, "status": bson.M{"$in": bson.A{1, 5}}}
		randomNumberUpdate := bson.M{"$set": bson.M{"status": 3}}
		if _, err := tools.Database.Collection("random_number").UpdateMany(sctx, randomNumberFilter, randomNumberUpdate); err != nil {
			return nil, errors.WithStack(err)
		}

		// 添加作废轨迹
		historyInvalid := models.History{
			Key:  "history.randomization.list.invalid",
			OID:  randomList.ID,
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}
		_, historyEerrInvalid := tools.Database.Collection("history").InsertOne(sctx, historyInvalid)
		if historyEerrInvalid != nil {
			return nil, errors.WithStack(historyEerrInvalid)
		}

		// 插入项目
		err = tools.Database.Collection("random_list").FindOne(nil, filter).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		historyInvalid.Data = bson.M{"name": randomList.Name}
		historyInvalid.OID = randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			historyInvalid.OID = randomList.CohortID
		}
		historyInvalid.Key = strings.Replace(historyInvalid.Key, "history.randomization", "history.randomization.config", 1)
		_, historyEerrInvalid = tools.Database.Collection("history").InsertOne(nil, historyInvalid)
		if historyEerrInvalid != nil {
			return nil, errors.WithStack(historyEerrInvalid)
		}

		// 项目日志
		OperationLogFieldGroups := []models.OperationLogFieldGroup{}
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.invalidList",
			Value: randomList.Name,
			Blind: false,
		})
		OID := randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			OID = randomList.CohortID
		}
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", OID, 3, OperationLogFieldGroups, marks, randomList.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

/*************************************************************************************随机配置所使用到的引用方法***********************************************************************/

/*************************************************************************************系统生成随机号逻辑开始***********************************************************************/

func randomNumberGenerate(ctx *gin.Context, random models.RandomList, blockNumber int) ([]interface{}, []models.RandomNumber, int, error) {
	// 获取总数
	count := 0
	blocks := random.Config.Blocks

	type BlockOrder struct {
		BlockLength int `json:"blockLength"` // 区组长度
		BlockNumber int `json:"blockNumber"` // 区组数量
		Order       int `json:"order"`       //顺序
	}
	orders := make([]BlockOrder, 0)

	if random.Design.Type == 1 {
		for i := 0; i < len(blocks); i++ {
			count += blocks[i].BlockLength * blocks[i].BlockNumber
		}
	} else {
		count = random.Config.Total
	}
	randomNumbers := make([]interface{}, count)
	var randomNumberArray []models.RandomNumber

	// 获取各实验组比例之和
	var groupRatioSum int
	for i := 0; i < len(random.Design.Groups); i++ {
		groupRatioSum += random.Design.Groups[i].Ratio
	}
	// 赋值随机号初始值
	//randomInitialValue := random.Config.InitialValue
	originalRandomNumber := make([]int, 0)
	if random.Config.Rule == 1 {
		var err error
		originalRandomNumber, err = generateReverseOrderRandomNumber(ctx, random.Config.InitialValue, random.Config.EndValue, count, random.Config.Seed)
		if err != nil {
			return nil, nil, 0, err
		}
	} else {
		originalRandomNumber = generateOrderRandomNumber(random.Config.InitialValue, count)
	}
	index := 0
	// 声明区组变量默0认1
	blockSign := 1
	if blockNumber > 1 {
		blockSign = blockNumber
	}

	switch random.Design.Type {
	case 1: // 区组随机
		for i := 0; i < len(blocks); i++ {
			// 获取区块数组
			blockArray := randomArray(blocks[i].BlockNumber, random.Config.Seed)
			for j := 0; j < len(blockArray); j++ {
				orders = append(orders, BlockOrder{
					BlockLength: blocks[i].BlockLength,
					BlockNumber: blocks[i].BlockNumber,
					Order:       blockArray[j],
				})
			}
		}
		//区组乱序
		if random.Config.BlocksRule == 1 {
			if random.Config.Seed == 0 {
				rand.Seed(time.Now().UnixNano())
			} else {
				rand.Seed(random.Config.Seed) // 种子必须是int64
			}
			rand.Shuffle(len(orders), func(i, j int) { orders[i], orders[j] = orders[j], orders[i] })
		}
		for _, order := range orders {
			// 获取组别对应的节点
			groupCodes := getGroupCode(groupRatioSum, random, order.BlockLength)
			// 标记是否填写了随机种子
			sendBl := random.Config.Seed > 0
			// 随机种子操作
			seed := int64(0)
			if sendBl {
				seed = random.Config.Seed + int64(order.Order)
			}
			// 获取每个区块内的随机数组
			blockRandomArray := randomArray(order.BlockLength, seed)
			// 匹配随机数对应的组别
			for n := 0; n < len(blockRandomArray); n++ {
				for m := 0; m < len(groupCodes); m++ {
					if blockRandomArray[n] <= groupCodes[m].GroupNode {
						combination, err := randomNumberCombination(ctx, random, originalRandomNumber[index])
						if err != nil {
							return nil, nil, 0, err
						}
						randomNumbers[index] = models.RandomNumber{
							ID:            primitive.NewObjectID(),
							CustomerID:    random.CustomerID,
							ProjectID:     random.ProjectID,
							EnvironmentID: random.EnvironmentID,
							CohortID:      random.CohortID,
							RandomListID:  random.ID,
							Block:         blockSign,
							Number:        combination,
							Group:         groupCodes[m].Group,
							ParName:       groupCodes[m].ParName,
							SubName:       groupCodes[m].SubName,
							Status:        1,
						}
						randomNumberArray = append(randomNumberArray, models.RandomNumber{
							Number: combination,
						})
						index++ // 下标
						//randomInitialValue++ // 随机号
						break
					}
				}
			}
			blockSign++
		}
	case 2: // 最小化随机
		// 校验
		if random.Config.Total%groupRatioSum != 0 {
			return nil, nil, 0, tools.BuildServerError(ctx, "random_total_error")
		}
		for i := 0; i < random.Config.Total; i++ {

			// 计划随机数
			//timeNow := int64(time.Now().Nanosecond() + i)
			timeNow := random.Config.Seed + int64(i)
			rand.Seed(timeNow)
			planNumber, _ := strconv.ParseFloat(strconv.FormatFloat(1*rand.Float64(), 'f', 3, 64), 64)
			combination, err := randomNumberCombination(ctx, random, originalRandomNumber[index])
			if err != nil {
				return nil, nil, 0, err
			}
			randomNumbers[index] = models.RandomNumber{
				ID:            primitive.NewObjectID(),
				CustomerID:    random.CustomerID,
				ProjectID:     random.ProjectID,
				EnvironmentID: random.EnvironmentID,
				CohortID:      random.CohortID,
				RandomListID:  random.ID,
				Number:        combination,
				PlanNumber:    planNumber, // 计划随机数
				Status:        1,
			}
			randomNumberArray = append(randomNumberArray, models.RandomNumber{
				Number: combination,
			})
			index++ // 下标
			//randomInitialValue++ // 随机号
		}
	}

	return randomNumbers, randomNumberArray, blockSign, nil
}

// 随机配置获取一个随机生成的数组（带种子）
func randomArray(number int, seed int64) []int {
	// 1.首先生成一个数组
	var randomNumbers []int
	for i := 1; i <= number; i++ {
		randomNumbers = append(randomNumbers, i)
	}

	// 判断是否输入随机种子
	if seed == 0 {
		rand.Seed(time.Now().UnixNano())
	} else {
		rand.Seed(seed) // 种子必须是int64
	}

	rand.Shuffle(len(randomNumbers), func(i, j int) { //调用算法
		randomNumbers[i], randomNumbers[j] = randomNumbers[j], randomNumbers[i]
	})
	return randomNumbers
}

/**
* 计算各组别对应的数字
* 如：AB两组,比例1:2，区组长度6 那么计算公式应为：
* A组：6 / (1+2) * (1)
* B组：6 / (1+2) * (1+2)
 */
func getGroupCode(groupRatioSum int, random models.RandomList, blockLength int) []models.GroupCode {
	var groupCodes []models.GroupCode
	var groupCode models.GroupCode

	for i := 0; i < len(random.Design.Groups); i++ {
		// 赋值组别
		groupCode.Group = random.Design.Groups[i].Name
		groupCode.ParName = random.Design.Groups[i].ParName
		groupCode.SubName = random.Design.Groups[i].SubName

		ratioSum := 0
		for j := 0; j <= i; j++ {
			ratioSum += random.Design.Groups[j].Ratio
		}
		// 赋值组别对应的节点数字
		groupCode.GroupNode = blockLength / groupRatioSum * ratioSum
		// 添加到集合
		groupCodes = append(groupCodes, groupCode)
	}
	return groupCodes
}
func generateOrderRandomNumber(initValue, count int) []int {
	arr := make([]int, count)
	for i := 0; i < count; i++ {
		arr[i] = initValue + i
	}
	return arr
}
func generateReverseOrderRandomNumber(ctx *gin.Context, initValue, endValue, count int, seed int64) ([]int, error) {
	if endValue-initValue+1 < count {
		return []int{}, tools.BuildServerError(ctx, "common.operation.fail")
	}
	// 初始化随机数种子
	// 判断是否输入随机种子
	if seed == 0 {
		rand.Seed(time.Now().UnixNano())
	} else {
		rand.Seed(seed) // 种子必须是int64
	}
	// 创建存储随机数的 map
	used := make(map[int]bool)
	// 创建存储随机数的切片
	arr := make([]int, 0, count)
	// 计算数值范围
	rangeSize := endValue - initValue + 1
	// 生成不重复的随机数并存入切片
	for len(arr) < count {
		num := rand.Intn(rangeSize) + initValue
		if !used[num] {
			used[num] = true
			arr = append(arr, num)
		}
	}
	// 打乱切片元素的顺序
	rand.Shuffle(len(arr), func(i, j int) {
		arr[i], arr[j] = arr[j], arr[i]
	})
	return arr, nil
}

// 拼接随机号
func randomNumberCombination(ctx *gin.Context, random models.RandomList, number int) (string, error) {
	numberStr := strconv.Itoa(number)
	if random.Config.Prefix != "" {
		numberStr = fmt.Sprintf("%s%s", random.Config.Prefix, strconv.Itoa(number))
	}
	randomNumber := ""
	if random.Config.NumberLength == len(numberStr) {
		randomNumber = numberStr
	} else if random.Config.NumberLength > len(numberStr) {
		supplement := "" // 补充字符
		for i := 0; i < random.Config.NumberLength-len(numberStr); i++ {
			supplement += "0"
		}
		randomNumber = fmt.Sprintf("%s%s%s", random.Config.Prefix, supplement, strconv.Itoa(number))
	} else if random.Config.NumberLength < len(numberStr) {
		return "", tools.BuildServerError(ctx, "random_length_error")
	}
	return randomNumber, nil
}

// 分层因素排列(新)
// func layeredArrangement(factors []models.RandomFactor) []models.FactorsCombination {
// 	var factorsCombinationArray []models.FactorsCombination
// 	var number = 1
// 	if factors != nil {
// 		for _, factor := range factors {
// 			for _, option := range factor.Options {
// 				// factors := models.Factors{
// 				// 	Name:  factor.Name,
// 				// 	Label: factor.Label,
// 				// 	Value: option.Value,
// 				// 	Text:  option.Label,
// 				// }
// 				fmt.Println(option)
// 				factorsCombinationArray = append(factorsCombinationArray, models.FactorsCombination{
// 					ID:     primitive.NewObjectID(),
// 					Number: strconv.Itoa(number),
// 					//LayeredFactors: factors,
// 				})

// 				number++ // 分层编号自增
// 			}
// 		}
// 	}
// 	return factorsCombinationArray
// }

func (s *RandomizationService) CalculateCombination(ctx *gin.Context, id string) ([]models.FactorsCombinations, error) {
	OID, _ := primitive.ObjectIDFromHex(id)
	var randomList models.RandomList
	err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": OID}).Decode(&randomList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return layeredArrangements(randomList, randomList.Design), nil
}

// LayeredArrangements
func layeredArrangements(random models.RandomList, info models.RandomListInfo) []models.FactorsCombinations {
	if info.Factors != nil {
		var effectFields []models.RandomFactor
		for i := 0; i < len(info.Factors); i++ {
			f := info.Factors[i]
			if f.Status == nil || *f.Status == 1 {
				effectFields = append(effectFields, f)
			}
		}
		var inputList [][]models.Factors
		for i := 0; i < len(effectFields); i++ {
			var factors []models.Factors
			if effectFields[i].Options != nil {
				for j := 0; j < len(effectFields[i].Options); j++ {
					factors = append(factors, models.Factors{
						Name:  effectFields[i].Name,
						Label: effectFields[i].Label,
						Value: effectFields[i].Options[j].Value,
						Text:  effectFields[i].Options[j].Label,
					})
				}
			}
			inputList = append(inputList, factors)
		}
		//分层因素全排列
		array := make([]models.Factors, len(inputList))
		var layeredFactor []models.FactorsParameter
		calculateCombination(inputList, 0, array, &layeredFactor)

		// 返回排列好的数据
		var factorsCombinations []models.FactorsCombinations
		// 分层因素排列组合
		if layeredFactor != nil {
			for g := 0; g < len(layeredFactor); g++ {
				// 取出当前层的数据
				var info []models.Factors
				for j := 0; j < len(random.Design.Factors); j++ {
					info = append(info, models.Factors{
						Name:  layeredFactor[g].Layered[(random.Design.Factors[j].Name + "Name")],
						Label: layeredFactor[g].Layered[(random.Design.Factors[j].Name + "Label")],
						Value: layeredFactor[g].Layered[(random.Design.Factors[j].Name + "Value")],
						Text:  layeredFactor[g].Layered[random.Design.Factors[j].Name+"Text"],
					})
				}

				factorsCombinations = append(factorsCombinations, models.FactorsCombinations{
					ID:             primitive.NewObjectID(),
					Number:         strconv.Itoa(g + 1),
					LayeredFactors: info,
				})
			}
		}
		return factorsCombinations
	}
	return nil
}

/**
* 利用递归全排列
* @inputList:原始数据
* @beginIndex:初始位置
* @arr:接收一次排列结果数组
* @factors:接收全排列结果数组
 */
func calculateCombination(inputList [][]models.Factors, beginIndex int, arr []models.Factors, layeredFactor *[]models.FactorsParameter) {
	if beginIndex == len(inputList) {
		var layered = make(map[string]string)
		for i := 0; i < len(arr); i++ {
			layered[(arr[i].Name + "Name")] = arr[i].Name
			layered[(arr[i].Name + "Label")] = arr[i].Label
			layered[(arr[i].Name + "Value")] = arr[i].Value
			layered[(arr[i].Name + "Text")] = arr[i].Text
		}
		*layeredFactor = append(*layeredFactor, models.FactorsParameter{
			Layered: layered,
		})
		return
	}

	for j := 0; j < len(inputList[beginIndex]); j++ {
		arr = append(arr, inputList[beginIndex][j])
		calculateCombination(inputList, beginIndex+1, arr, layeredFactor)
	}
}

// 随机号校验
func randomNumberVerification(ctx *gin.Context, newRandomNumber []models.RandomNumber, oldRandomNumber []models.RandomNumber) error {
	set := make(map[string]bool, len(oldRandomNumber))
	for _, number := range oldRandomNumber {
		set[number.Number] = true
	}
	for _, number := range newRandomNumber {
		if set[number.Number] {
			return tools.BuildServerError(ctx, "random_number_error")
		}
	}
	return nil
}

/*************************************************************************************系统生成随机号逻辑结束***********************************************************************/

func (s *RandomizationService) GetBlock(ctx *gin.Context) (map[string]interface{}, error) {

	var req models.BlockReq
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	if req.Limit == 0 {
		req.Limit = 20
	}
	sourceOID, _ := primitive.ObjectIDFromHex(req.ID)
	match := bson.M{
		"random_list_id": req.RandomListID,
	}
	status := make([]int, 0)
	countGroup := bson.M{
		"_id":    "$block",
		"status": bson.M{"$push": "$status"},
	}
	countProject := bson.M{
		"_id":    1,
		"status": 1,
	}
	group := bson.M{
		"_id":             bson.M{"block": "$block", "factors": "$factors"},
		"status":          bson.M{"$push": "$status"},
		"count":           bson.M{"$sum": 1},
		"project_site_id": bson.M{"$addToSet": "$project_site_id"},
		"id":              bson.M{"$first": "$_id"},
	}
	project := bson.M{
		"_id":             0,
		"count":           1,
		"block":           "$_id.block",
		"factors":         "$_id.factors",
		"project_site_id": "$project_site_id",
		"key":             "$_id.block",
		"id":              "$id",
		"status":          1,
	}
	groupMatchOr := bson.A{}
	if req.StatusType == 1 && req.Status != nil && len(req.Status) > 0 {
		if slice.Contain(req.Status, 2) {
			countProject["unused"] = bson.M{"$allElementsTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 1}}}}}
			project["unused"] = bson.M{"$allElementsTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 1}}}}}
			countProject["available"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 1}}}}}
			project["available"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 1}}}}}
			groupMatchOr = append(groupMatchOr, bson.M{"available": true, "unused": false})
		}
		if slice.Contain(req.Status, 1) {
			countProject["unused"] = bson.M{"$allElementsTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 1}}}}}
			project["unused"] = bson.M{"$allElementsTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 1}}}}}
			groupMatchOr = append(groupMatchOr, bson.M{"unused": true})
		}
		if slice.Contain(req.Status, 3) {
			countProject["used"] = bson.M{"$allElementsTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 2}}}}}
			project["used"] = bson.M{"$allElementsTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 2}}}}}
			groupMatchOr = append(groupMatchOr, bson.M{"used": true})
		}
		if slice.Contain(req.Status, 4) {
			countProject["invalid"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 3}}}}}
			project["invalid"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 3}}}}}
			groupMatchOr = append(groupMatchOr, bson.M{"invalid": true})
			countProject["disable"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 4}}}}}
			project["disable"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 4}}}}}
			groupMatchOr = append(groupMatchOr, bson.M{"disable": true})
		}
		if slice.Contain(req.Status, 5) {
			countProject["unavailable"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 5}}}}}
			project["unavailable"] = bson.M{"$anyElementTrue": bson.M{"$map": bson.M{"input": "$status", "as": "item", "in": bson.M{"$eq": bson.A{"$$item", 5}}}}}
			groupMatchOr = append(groupMatchOr, bson.M{"unavailable": true})
		}
	}
	if req.StatusType == 2 && req.Status != nil && len(req.Status) > 0 {
		matchStatus := bson.A{}
		if slice.Contain(req.Status, 1) {
			matchStatus = append(matchStatus, 1)
		}
		if slice.Contain(req.Status, 2) {
			matchStatus = append(matchStatus, 2)
		}
		if slice.Contain(req.Status, 3) {
			matchStatus = append(matchStatus, 3)
			matchStatus = append(matchStatus, 4)
		}
		if slice.Contain(req.Status, 5) {
			matchStatus = append(matchStatus, 5)
		}
		match["status"] = bson.M{"$in": matchStatus}
	}
	if req.RangeType == 2 {
		if req.StartStr != "" && req.EndStr != "" {
			match["number"] = bson.M{"$gte": req.StartStr, "$lte": req.EndStr}
		} else if req.StartStr != "" {
			match["number"] = bson.M{"$gte": req.StartStr}
		} else if req.EndStr != "" {
			match["number"] = bson.M{"$lte": req.EndStr}
		}
	}
	status = slice.Unique(status)
	var randomNumbers []map[string]interface{}
	var count int
	if req.ID != "all" {
		match["project_site_id"] = sourceOID
	}
	pipelineCount := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: countGroup}},
		{{Key: "$project", Value: countProject}},
	}

	if req.StatusType == 1 && req.Status != nil && len(req.Status) > 0 {
		pipelineCount = append(pipelineCount,
			bson.D{{Key: "$match", Value: bson.M{
				"$or": groupMatchOr,
			}}})
	}
	if req.RangeType == 1 {
		if req.Start != 0 {
			pipelineCount = append(pipelineCount,
				bson.D{{Key: "$match", Value: bson.M{
					"_id": bson.M{"$gte": req.Start},
				}}})
		}
		if req.End != 0 {
			pipelineCount = append(pipelineCount,
				bson.D{{Key: "$match", Value: bson.M{
					"_id": bson.M{"$lte": req.End},
				}}})
		}
	}

	cursor, err := tools.Database.Collection("random_number").Aggregate(nil, pipelineCount)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomNumbers)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	count = len(randomNumbers)

	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: group}},
		{{Key: "$project", Value: project}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$sort", Value: bson.D{{"id", 1}}}},
	}
	if req.StatusType == 1 && req.Status != nil && len(req.Status) > 0 {
		pipepine = append(pipepine,
			bson.D{{Key: "$match", Value: bson.M{
				"$or": groupMatchOr,
			}}})
	}
	if req.Start != 0 {
		pipepine = append(pipepine,
			bson.D{{Key: "$match", Value: bson.M{
				"block": bson.M{"$gte": req.Start},
			}}})
	}
	if req.End != 0 {
		pipepine = append(pipepine,
			bson.D{{Key: "$match", Value: bson.M{
				"block": bson.M{"$lte": req.End},
			}}})
	}
	pipepine = append(pipepine,
		bson.D{{Key: "$skip", Value: req.Begin}})
	pipepine = append(pipepine,
		bson.D{{Key: "$limit", Value: req.Limit}})

	var data []map[string]interface{}
	cursor, err = tools.Database.Collection("random_number").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var randomNumberBlock []map[string]interface{}
	for i, datum := range data {
		if len(datum["project_site"].(primitive.A)) == 1 {
			if datum["project_site"].(primitive.A)[0].(map[string]interface{})["short_name"] != nil && datum["project_site"].(primitive.A)[0].(map[string]interface{})["short_name"] != "" {
				datum["site"] = datum["project_site"].(primitive.A)[0].(map[string]interface{})["short_name"]
			} else {
				if ctx.GetHeader("Accept-Language") == "en" && datum["project_site"].(primitive.A)[0].(map[string]interface{})["name_en"] != nil && datum["project_site"].(primitive.A)[0].(map[string]interface{})["name_en"] != "" {
					datum["site"] = datum["project_site"].(primitive.A)[0].(map[string]interface{})["name_en"]
				} else {
					datum["site"] = datum["project_site"].(primitive.A)[0].(map[string]interface{})["name"]
				}
			}
		} else {
			datum["site"] = ""
		}

		if req.ID != "all" {
			if len(datum["project_site"].(primitive.A)) == 1 && datum["project_site"].(primitive.A)[0].(map[string]interface{})["_id"] == sourceOID {
				randomNumberBlock = append(randomNumberBlock, datum)
			} else {
				if sourceOID == primitive.NilObjectID && datum["site"] == "" {
					randomNumberBlock = append(randomNumberBlock, datum)
				}
			}
		} else {
			randomNumberBlock = append(randomNumberBlock, datum)
		}
		delete(data[i], "project_site")
		delete(data[i], "project_site_id")
		delete(data[i], "id")
	}

	response := map[string]interface{}{
		"item":  randomNumberBlock,
		"count": count,
	}
	return response, nil
}

func (s *RandomizationService) GetProjectSite(ctx *gin.Context, customerID string, projectID string, envID string) ([]map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"project_id":  projectOID,
		"deleted":     2,
	}
	//lookup := bson.M{
	//	"from":         "site",
	//	"localField":   "site_id",
	//	"foreignField": "_id",
	//	"as":           "ret",
	//}

	project := bson.M{
		"_id":     0,
		"site_id": 1,
		"id":      "$_id",
		"number":  "$number",

		"name": models.ProjectSiteNameBson(ctx),
	}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		//{{Key: "$lookup", Value: lookup}},
		{{Key: "$project", Value: project}},
		{{Key: "$sort", Value: bson.D{{"number", 1}}}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return data, nil
}

// UpdateProjectSite .. 区组分配中心
func (s *RandomizationService) UpdateProjectSite(ctx *gin.Context, randomListID string, data map[string]interface{}) error {
	randomListOID, _ := primitive.ObjectIDFromHex(randomListID)
	// 获取分层信息
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var randomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": randomListOID}).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var updateData bson.A
		update := bson.M{}
		var factorList []string
		historyOID := randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			historyOID = randomList.CohortID
		}
		factors := slice.Filter(randomList.Design.Factors, func(index int, item models.RandomFactor) bool {
			if item.Status == nil {
				return true
			}
			return *item.Status != 2
		})
		for _, factor := range factors {
			if data[factor.Name] != nil {
				updateFactors := models.Factors{
					Name:  factor.Name,
					Label: factor.Label,
					Value: data[factor.Name].(map[string]interface{})["value"].(string),
					Text:  data[factor.Name].(map[string]interface{})["label"].(string),
				}
				updateData = append(updateData, updateFactors)
				factorList = append(factorList, data[factor.Name].(map[string]interface{})["label"].(string))
			} else {
				break
			}
		}
		if len(updateData) > 0 {
			update["factors"] = updateData
			count, _ := tools.Database.Collection("random_number").CountDocuments(nil, bson.M{"random_list_id": randomListOID, "block": bson.M{"$in": data["block"]}, "status": 2})
			if count > 0 {
				return nil, tools.BuildServerError(ctx, "randomNumber.exist.used")
			}
		}
		// 轨迹key
		operationLogFieldGroups := []models.OperationLogFieldGroup{}
		blockStr := []string{}
		for _, block := range data["block"].([]interface{}) {
			blockStr = append(blockStr, convertor.ToString(block))
		}
		//key := "history.randomization.block.distributionFactor"
		value := ""
		if data["site"] != nil {
			projectSiteOID, _ := primitive.ObjectIDFromHex(data["site"].(string))
			update["project_site_id"] = projectSiteOID
			//key = "history.randomization.block.distributionSite"
			if projectSiteOID != primitive.NilObjectID {
				var projectSite models.ProjectSite
				err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": projectSiteOID}).Decode(&projectSite)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				value = models.GetProjectSiteName(ctx, projectSite)
				if projectSite.Country != nil && len(projectSite.Country) > 0 {
					update["country"] = projectSite.Country[0]
				}
				if !projectSite.RegionID.IsZero() {
					update["region_id"] = projectSite.RegionID
				}
				operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_list.set_site",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: strings.Join(blockStr, ",") + "/" + value,
					},
				})
			} else {
				value = ""
				update["country"] = ""
				operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_list.set_site",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: strings.Join(blockStr, ",") + "/Depot",
					},
				})
			}

		} else if data["country"] != nil {
			country := data["country"].(string)
			update["country"] = country
			operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design",
				TranKey: "operation_log.random_list.set_country",
				Old: models.OperationLogField{
					Type:  1,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  1,
					Value: strings.Join(blockStr, ",") + "/" + country,
				},
			})
		} else if data["region"] != nil {
			regionId := data["region"].(string)
			regionOID, _ := primitive.ObjectIDFromHex(regionId)
			update["region_id"] = regionOID
			var region models.Region
			err := tools.Database.Collection("region").FindOne(nil, bson.M{"_id": regionOID}).Decode(&region)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design",
				TranKey: "operation_log.random_list.set_region",
				Old: models.OperationLogField{
					Type:  1,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  1,
					Value: strings.Join(blockStr, ",") + "/" + region.Name,
				},
			})
		} else {
			value = fmt.Sprintf("(%s)", strings.Join(factorList, ","))
			operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design",
				TranKey: "operation_log.random_list.set_factor",
				Old: models.OperationLogField{
					Type:  1,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  1,
					Value: strings.Join(blockStr, ",") + "/" + strings.Join(factorList, ","),
				},
			})
		}
		match := bson.M{
			"random_list_id": randomListOID,
			"block":          bson.M{"$in": data["block"]},
			"status":         1,
		}

		_, err = tools.Database.Collection("random_number").UpdateMany(sctx, match, bson.M{
			"$set": update})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 项目日志

		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.list",
			Value: randomList.Name,
			Blind: false,
		})

		err = tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", historyOID, 2, operationLogFieldGroups, marks, randomList.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) GetTemplateFile(ctx *gin.Context) error {
	envId := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortId := ctx.Query("cohortId")
	cohortID, _ := primitive.ObjectIDFromHex(cohortId)
	filter := bson.M{"env_id": envOID}
	if !cohortID.IsZero() {
		filter["cohort_id"] = cohortID
	}
	var randomDesign models.RandomDesign
	err := tools.Database.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)
	if err != nil {
		return errors.WithStack(err)
	}
	f := excelize.NewFile()
	fileName := "Source Randomization List Template.xlsx"
	f.SetDefaultFont("黑体")
	title := make([]interface{}, 0)
	title = append(title, locales.Tr(ctx, "report.attributes.random.block"))
	title = append(title, locales.Tr(ctx, "report.attributes.random.number"))
	title = append(title, locales.Tr(ctx, "report.attributes.random.group"))
	_, b := slice.Find(randomDesign.Info.Groups, func(index int, item models.RandomGroup) bool {
		return item.SubGroup != nil && len(item.SubGroup) > 0
	})
	if b {
		title = append(title, locales.Tr(ctx, "report.attributes.random.sub.group"))
	}
	content := make([][]interface{}, 0)
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", buffer.Bytes())
	return errors.WithStack(err)
}

func (s *RandomizationService) AddFactorNumber(ctx *gin.Context, data map[string]interface{}, id string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		OID, _ := primitive.ObjectIDFromHex(id)
		match := bson.M{"_id": OID}
		var randomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(sctx, match).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		factorsName := make(map[string]interface{})
		factors := randomList.Design.Factors
		for _, factor := range factors {
			optionsMap := make(map[string]string)
			for _, option := range factor.Options {
				optionsMap[option.Value] = option.Label
			}
			value := make(map[string]interface{})
			value["label"] = factor.Label
			value["options"] = optionsMap
			factorsName[factor.Name] = value
		}

		layeredFactors := make([]models.Factors, 0)
		if data["mulFactors"] != nil {
			mulFactors := data["mulFactors"].([]interface{})
			for _, factor := range mulFactors {
				parts := strings.Split(factor.(string), " ")
				label := parts[0]
				optionValue := parts[1]
				factorName := factorsName[label].(map[string]interface{})
				options := factorName["options"].(map[string]string)
				factorData := models.Factors{
					Label: factorName["label"].(string),
					Name:  label,
					Value: optionValue,
					Text:  options[optionValue],
				}
				layeredFactors = append(layeredFactors, factorData)
			}
		}

		combinations := randomList.Design.Combination
		number := 0
		for _, combination := range combinations {
			num, _ := strconv.Atoi(combination.Number)
			if num > number {
				number = num
			}
			hlayeredFactors := combination.LayeredFactors
			if len(hlayeredFactors) == len(layeredFactors) { //判断元素是否一样
				thisFlag := true
				for _, h := range hlayeredFactors {
					same := false
					for _, n := range layeredFactors {
						if h == n {
							same = true
						}
					}
					if !same { //说明元素有不一样的，肯定不相等
						thisFlag = false
					}
				}
				if thisFlag {
					return nil, tools.BuildServerError(ctx, "random_duplicated_factor")
				}
			}
		}

		factorsCombination := models.FactorsCombination{
			ID:             primitive.NewObjectID(),
			Number:         strconv.Itoa(number + 1),
			LayeredFactors: layeredFactors,
			EstimateNumber: int(data["estimateNumber"].(float64)),
			WarnNumber:     int(data["warnNumber"].(float64)),
		}
		combinations = append(combinations, factorsCombination)
		update := bson.M{
			"$set": bson.M{
				"design.combination": combinations,
			},
		}
		_, err = tools.Database.Collection("random_list").UpdateOne(sctx, match, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 轨迹
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		factorStr := ""
		for _, f := range layeredFactors {
			factorStr = factorStr + fmt.Sprintf("%s(%s)", f.Label, f.Text) + ","
		}
		if len(factorStr) > 0 {
			factorStr = factorStr[:len(factorStr)-1]
		}

		var histories []models.History
		history := models.History{
			Key:  "history.randomization.factor.addNumber",
			OID:  randomList.ID,
			Data: bson.M{"name": randomList.Name, "factor": factorStr, "warnNumber": data["warnNumber"], "estimateNumber": data["estimateNumber"]},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		history.OID = randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			history.OID = randomList.CohortID
		}
		history.Key = strings.Replace(history.Key, "history.randomization", "history.randomization.config", 1)
		histories = append(histories, history)

		ctx.Set("HISTORY", histories)

		// 项目日志
		old := models.FactorsCombination{}
		insertFactorNumberLog(ctx, sctx, randomList.EnvironmentID, 1, old, factorsCombination, OID, history.OID, randomList.Name)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) DelFactorNumber(ctx *gin.Context, id string, combinationID string) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	combinationOID, _ := primitive.ObjectIDFromHex(combinationID)

	match := bson.M{"_id": OID}
	var randomList models.RandomList
	err := tools.Database.Collection("random_list").FindOne(nil, match).Decode(&randomList)
	if err != nil {
		return errors.WithStack(err)
	}

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	old := models.FactorsCombination{}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		factorStr := ""
		estimateNumber := 0
		warnNumber := 0
		for _, combination := range randomList.Design.Combination {
			if combination.ID == combinationOID {
				old = combination
				estimateNumber = combination.EstimateNumber
				warnNumber = combination.WarnNumber
				for _, factor := range combination.LayeredFactors {
					factorStr = factorStr + fmt.Sprintf("%s(%s),", factor.Label, factor.Text)
				}
			}
		}
		if len(factorStr) > 0 {
			factorStr = factorStr[:len(factorStr)-1]
		}
		var histories []models.History
		history := models.History{
			Key:  "history.randomization.factor.delNumber",
			OID:  randomList.ID,
			Data: bson.M{"name": randomList.Name, "factor": factorStr, "estimateNumber": estimateNumber, "warnNumber": warnNumber},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		history.OID = randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			history.OID = randomList.CohortID
		}
		history.Key = strings.Replace(history.Key, "history.randomization", "history.randomization.config", 1)
		histories = append(histories, history)

		ctx.Set("HISTORY", histories)
		// 项目日志
		new := models.FactorsCombination{}
		insertFactorNumberLog(ctx, sctx, randomList.EnvironmentID, 3, old, new, OID, history.OID, randomList.Name)
		//更新
		update := bson.M{
			"$pull": bson.M{
				"design.combination": bson.M{
					"id": combinationOID,
				},
			},
		}
		if _, err := tools.Database.Collection("random_list").UpdateOne(sctx, match, update); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) UpdateFactorNumber(ctx *gin.Context, data map[string]interface{}, id string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		OID, _ := primitive.ObjectIDFromHex(id)
		match := bson.M{"design.combination.id": OID}

		var oldRandomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(sctx, match).Decode(&oldRandomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		oldCombination := models.FactorsCombination{}
		for _, combination := range oldRandomList.Design.Combination {
			if combination.ID == OID {
				oldCombination = combination
				break
			}
		}

		factorsName := make(map[string]interface{})
		factors := oldRandomList.Design.Factors
		for _, factor := range factors {
			optionsMap := make(map[string]string)
			for _, option := range factor.Options {
				optionsMap[option.Value] = option.Label
			}
			value := make(map[string]interface{})
			value["label"] = factor.Label
			value["options"] = optionsMap
			factorsName[factor.Name] = value
		}
		layeredFactors := make([]models.Factors, 0)
		if data["layeredFactors"] != nil {
			mulFactors := data["layeredFactors"].([]interface{})
			for _, factor := range mulFactors {
				parts := strings.Split(factor.(string), " ")
				label := parts[0]
				optionValue := parts[1]
				factorName := factorsName[label].(map[string]interface{})
				options := factorName["options"].(map[string]string)
				factorData := models.Factors{
					Label: factorName["label"].(string),
					Name:  label,
					Value: optionValue,
					Text:  options[optionValue],
				}
				layeredFactors = append(layeredFactors, factorData)
			}
		}

		for _, combination := range oldRandomList.Design.Combination {
			if combination.ID != OID {
				hlayeredFactors := combination.LayeredFactors
				if len(hlayeredFactors) == len(layeredFactors) { //判断元素是否一样
					thisFlag := true
					for _, h := range hlayeredFactors {
						same := false
						for _, n := range layeredFactors {
							if h == n {
								same = true
							}
						}
						if !same { //说明元素有不一样的，肯定不相等
							thisFlag = false
						}
					}
					if thisFlag {
						return nil, tools.BuildServerError(ctx, "random_duplicated_factor")
					}
				}
			}
		}

		//更新数据
		update := bson.M{
			"$set": bson.M{
				"design.combination.$.layered_factors": layeredFactors,
				"design.combination.$.warn_number":     data["warnNumber"],
				"design.combination.$.estimate_number": data["estimateNumber"],
			},
		}

		_, err = tools.Database.Collection("random_list").UpdateOne(sctx, match, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 项目轨迹
		var randomList models.RandomList
		err = tools.Database.Collection("random_list").FindOne(sctx, match).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		newCombination := models.FactorsCombination{}
		factorStr := ""
		for _, combination := range randomList.Design.Combination {
			if combination.ID == OID {
				newCombination = combination
				for _, f := range combination.LayeredFactors {
					factorStr = factorStr + fmt.Sprintf("%s(%s)", f.Label, f.Text) + ","
				}
				if len(factorStr) > 0 {
					factorStr = factorStr[:len(factorStr)-1]
				}
				break
			}
		}

		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var histories []models.History
		history := models.History{
			Key:  "history.randomization.factor.editNumber",
			OID:  randomList.ID,
			Data: bson.M{"name": randomList.Name, "factor": factorStr, "warnNumber": data["warnNumber"], "estimateNumber": data["estimateNumber"]},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		history.OID = randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			history.OID = randomList.CohortID
		}
		history.Key = strings.Replace(history.Key, "history.randomization", "history.randomization.config", 1)
		histories = append(histories, history)

		ctx.Set("HISTORY", histories)

		// 项目日志
		insertFactorNumberLog(ctx, sctx, randomList.EnvironmentID, 2, oldCombination, newCombination, randomList.ID, history.OID, randomList.Name)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) GetLastGroup(ctx *gin.Context, projectID string, envID string, lastID string) ([]models.RandomListGroup, error) {
	var lastGroups []models.RandomListGroup
	var randomList models.RandomList
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	lastOID, _ := primitive.ObjectIDFromHex(lastID)
	match := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
		"cohort_id":  lastOID,
		"status":     1,
	}
	if err := tools.Database.Collection("random_list").FindOne(nil, match).Decode(&randomList); err != nil {
		return nil, errors.WithStack(err)
	}
	for _, group := range randomList.Design.Groups {
		lastGroups = append(lastGroups, group)
	}
	return lastGroups, nil
}

/**
* 随机号生成或者上传的轨迹记录
 */
func addRandomizationTrail(sctx mongo.SessionContext, sign int, user models.User, randomList models.RandomList, randomNumbers []models.RandomNumber) (bool, error) {
	// 创建轨迹类
	history := models.History{OID: randomList.ID}
	// 判断是区组随机还是最小化随机
	if randomList.Design.Type == 1 { // 区组随机
		// 判断上传还是系统生成
		if sign == 1 { // 上传
			var name = randomList.Name
			var factors bytes.Buffer
			var blockSize = "-"
			var numberText bytes.Buffer

			for _, f := range randomList.Design.Factors {
				factors.WriteString(f.Label)
				factors.WriteString("<")
				for _, o := range f.Options {
					factors.WriteString(" ")
					factors.WriteString(o.Label)
					factors.WriteString(" ")
				}
				factors.WriteString(">")
				factors.WriteString("、")
			}
			if len(randomList.Config.BlockSizes) > 0 {
				blockSize = strings.Join(randomList.Config.BlockSizes, ",")
			}

			for _, g1 := range randomList.Design.Groups {
				numberText.WriteString(g1.Name)
				numberText.WriteString("(")
				count := 0
				for _, r := range randomNumbers {
					if g1.Name == r.Group {
						count++
					}
				}
				numberText.WriteString(strconv.Itoa(count))
				numberText.WriteString(")")
				numberText.WriteString(" ")
			}
			history.Data = bson.M{"name": name, "factors": factors.String(), "blockSize": blockSize, "numberText": numberText.String()}
			history.Key = "history.randomization.block.upload"
		} else { // 系统生成
			var name = randomList.Name
			var initialValue = randomList.Config.InitialValue
			var groupRatio bytes.Buffer
			var blockNumber bytes.Buffer
			var factors bytes.Buffer
			var numberLength = randomList.Config.NumberLength
			var seed = "-"
			var numberPrefix = ""
			var numberText bytes.Buffer

			for _, g := range randomList.Design.Groups {
				groupRatio.WriteString(g.Name)
				groupRatio.WriteString("(")
				groupRatio.WriteString(strconv.Itoa(g.Ratio))
				groupRatio.WriteString(")")
				groupRatio.WriteString("、")
			}
			for _, b := range randomList.Config.Blocks {
				blockNumber.WriteString(strconv.Itoa(b.BlockLength))
				blockNumber.WriteString("(")
				blockNumber.WriteString(strconv.Itoa(b.BlockNumber))
				blockNumber.WriteString(")")
				blockNumber.WriteString("、")
			}
			for _, f := range randomList.Design.Factors {
				factors.WriteString(f.Label)
				factors.WriteString("<")
				for _, o := range f.Options {
					factors.WriteString(" ")
					factors.WriteString(o.Label)
					factors.WriteString(" ")
				}
				factors.WriteString(">")
				factors.WriteString("、")
			}
			if randomList.Config.Seed > 0 {
				seed = strconv.FormatInt(int64(randomList.Config.Seed), 10)
			}
			if randomList.Config.Prefix != "" {
				numberPrefix = randomList.Config.Prefix
			}

			for _, g1 := range randomList.Design.Groups {
				numberText.WriteString(g1.Name)
				numberText.WriteString("(")
				count := 0
				for _, r := range randomNumbers {
					if g1.Name == r.Group {
						count++
					}
				}
				numberText.WriteString(strconv.Itoa(count))
				numberText.WriteString(")")
				numberText.WriteString(" ")
			}
			history.Data = bson.M{"name": name, "initialValue": initialValue, "groupRatio": groupRatio.String(), "blockNumber": blockNumber.String(), "factors": factors.String(), "numberLength": numberLength, "seed": seed, "numberPrefix": numberPrefix, "numberText": numberText.String()}
			history.Key = "history.randomization.block.generate"
		}
	} else { // 最小化随机
		var name = randomList.Name
		var initialValue = randomList.Config.InitialValue
		var groupRatio bytes.Buffer
		var factors bytes.Buffer
		var numberLength = randomList.Config.NumberLength
		var probability = randomList.Config.Probability
		var total = randomList.Config.Total
		var seed = "-"
		var numberPrefix = "-"

		for _, g := range randomList.Design.Groups {
			groupRatio.WriteString(g.Name)
			groupRatio.WriteString("(")
			groupRatio.WriteString(strconv.Itoa(g.Ratio))
			groupRatio.WriteString(")")
			groupRatio.WriteString("、")
		}
		for _, f := range randomList.Design.Factors {
			factors.WriteString(f.Label)
			factors.WriteString("<")
			for _, o := range f.Options {
				factors.WriteString(" ")
				factors.WriteString(o.Label)
				factors.WriteString(" ")
			}
			factors.WriteString(">")
			factors.WriteString("(")
			factors.WriteString(strconv.Itoa(f.Ratio))
			factors.WriteString(")")
			factors.WriteString("、")
		}
		if randomList.Config.Seed > 0 {
			seed = strconv.FormatInt(int64(randomList.Config.Seed), 10)
		}
		if randomList.Config.Prefix != "" {
			numberPrefix = randomList.Config.Prefix
		}
		history.Data = bson.M{"name": name, "initialValue": initialValue, "groupRatio": groupRatio.String(), "factors": factors.String(), "probability": probability, "total": total, "numberLength": numberLength, "seed": seed, "numberPrefix": numberPrefix}
		history.Key = "history.randomization.minimize"
	}

	history.Time = time.Duration(time.Now().Unix())
	history.UID = user.ID
	history.User = user.Name
	_, err := tools.Database.Collection("history").InsertOne(sctx, history)
	if err != nil {
		return false, errors.WithStack(err)
	}

	history.OID = randomList.EnvironmentID
	if randomList.CohortID != primitive.NilObjectID {
		history.OID = randomList.CohortID
	}
	history.Key = strings.Replace(history.Key, "history.randomization", "history.randomization.config", 1)
	// 插入 项目日志页
	_, err = tools.Database.Collection("history").InsertOne(sctx, history)
	if err != nil {
		return false, errors.WithStack(err)
	}

	return true, nil
}

func (s *RandomizationService) ExportRandomList(ctx *gin.Context, id string) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	// 查找randomList获取属性配置
	var randomList models.RandomList
	var randomNumber []models.ExportNumber
	err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": OID}).Decode(&randomList)
	if err != nil {
		return errors.WithStack(err)
	}
	var attribute models.Attribute
	if randomList.CohortID != primitive.NilObjectID {
		err := tools.Database.Collection("attribute").FindOne(nil, bson.M{"cohort_id": randomList.CohortID}).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)
		}
	} else {
		err := tools.Database.Collection("attribute").FindOne(nil, bson.M{"env_id": randomList.EnvironmentID}).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return errors.WithStack(err)
		}
	}
	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"random_list_id": OID}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "subject",
			"localField":   "subject_id",
			"foreignField": "_id",
			"as":           "subject",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$subject", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project_site", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
		{{Key: "$project", Value: bson.M{
			"_id":             0,
			"block":           1,
			"number":          1,
			"group":           1,
			"factors":         1,
			"subject":         bson.M{"$first": "$subject.info.value"},
			"status":          1,
			"site":            models.ProjectSiteNameLookUpBson(ctx),
			"replace_subject": bson.M{"$ifNull": bson.A{"$replace_subject", ""}},
			"replace_number":  bson.M{"$ifNull": bson.A{"$replace_number", ""}},
			"country":         bson.M{"$first": "$project_site.country"},
		}}},
	}

	cursor, _ := tools.Database.Collection("random_number").Aggregate(nil, pipeline)
	err = cursor.All(nil, &randomNumber)
	if err != nil {
		return errors.WithStack(err)
	}
	title := []interface{}{
		locales.Tr(ctx, "randomList.export.block"),
		locales.Tr(ctx, "randomList.export.number"),
		locales.Tr(ctx, "randomList.export.group"),
	}
	if len(randomList.Design.Factors) > 0 {
		for _, factor := range randomList.Design.Factors {
			title = append(title, factor.Label)
		}
		title = append(title, subjectReplaceText)
		if attribute.AttributeInfo.CountryLayered {
			title = append(title, locales.Tr(ctx, "common.country"))
		}
		title = append(title, locales.Tr(ctx, "randomList.export.center"))
		title = append(title, locales.Tr(ctx, "randomList.export.status"))
	} else {
		title = append(title, subjectReplaceText)
		if attribute.AttributeInfo.CountryLayered {
			title = append(title, locales.Tr(ctx, "common.country"))
		}
		title = append(title, locales.Tr(ctx, "randomList.export.center"))
		title = append(title, locales.Tr(ctx, "randomList.export.status"))

	}
	title = append(title, locales.Tr(ctx, "subject.replace")+subjectReplaceText)
	title = append(title, locales.Tr(ctx, "subject.replace_number"))

	countries := bson.M{}
	if attribute.AttributeInfo.CountryLayered {
		countries, err = database.GetCountries(ctx)
	}

	var context [][]interface{}

	statusKey := []string{
		locales.Tr(ctx, "randomNumber.export.status.unused"),
		locales.Tr(ctx, "randomNumber.export.status.used"),
		locales.Tr(ctx, "randomNumber.export.status.invalid"),
		locales.Tr(ctx, "randomNumber.export.status.invalid"),
		locales.Tr(ctx, "randomNumber.export.status.unavailable"),
	}

	for _, number := range randomNumber {
		var tmp []interface{}
		tmp = append(tmp, strconv.Itoa(number.Block))
		tmp = append(tmp, number.Number)
		tmp = append(tmp, number.Group)
		if number.Factors != nil {
			for _, factor := range number.Factors {
				tmp = append(tmp, factor.Text)
			}
		} else {
			for range randomList.Design.Factors {
				tmp = append(tmp, "")
			}
		}

		tmp = append(tmp, number.Subject)
		if attribute.AttributeInfo.CountryLayered {
			if number.Country == "" {
				tmp = append(tmp, "")
			} else {
				tmp = append(tmp, countries[number.Country])
			}
		}
		tmp = append(tmp, number.Site)
		tmp = append(tmp, statusKey[number.Status-1])
		tmp = append(tmp, number.ReplaceSubject)
		tmp = append(tmp, number.ReplaceNumber)
		context = append(context, tmp)
	}
	var projectInfo []map[string]interface{}
	pipeline = mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": randomList.ProjectID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": randomList.EnvironmentID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}

	now := time.Now().UTC().Add(duration).Format("20060102")
	fileName := fmt.Sprintf("%s[%s]-%s-%s.xlsx", number, env, locales.Tr(ctx, "random_list_download_name"), now)

	err = tools.ExportExcelStream(ctx, fileName, title, context)
	if err != nil {
		return err
	}
	return nil
}

func (s *RandomizationService) CleanFactor(ctx *gin.Context, id string, block int) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	// 校验是否该区组已使用过，
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		count, _ := tools.Database.Collection("random_number").CountDocuments(sctx, bson.M{"random_list_id": OID, "block": block, "status": 2})
		if count > 0 {
			return nil, tools.BuildServerError(ctx, "randomNumber.exist.used.clean")
		}
		var randomNumber models.RandomNumber
		err := tools.Database.Collection("random_number").FindOne(sctx, bson.M{"random_list_id": OID, "block": block}).Decode(&randomNumber)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, err
		}

		update := bson.M{"$set": bson.M{"factors": nil}}
		_, err = tools.Database.Collection("random_number").UpdateMany(sctx, bson.M{"random_list_id": OID, "block": block}, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var randomList models.RandomList
		err = tools.Database.Collection("random_list").FindOne(sctx, bson.M{"_id": OID}).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var histories []models.History
		history := models.History{
			Key:  "history.randomization.factor.clean",
			OID:  OID,
			Data: bson.M{"name": randomList.Name, "block": block},
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		history.OID = randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			history.OID = randomList.CohortID
		}
		history.Key = strings.Replace(history.Key, "history.randomization", "history.randomization.config", 1)
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)

		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.list",
			Value: randomList.Name,
			Blind: false,
		})
		OperationLogFieldGroups := []models.OperationLogFieldGroup{}
		text := []string{}
		for _, factor := range randomNumber.Factors {
			text = append(text, factor.Text)
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.clean_factor",
			Old: models.OperationLogField{
				Type:  1,
				Value: convertor.ToString(block) + "/" + strings.Join(text, " "),
			},
			New: models.OperationLogField{
				Type:  1,
				Value: "",
			},
		})
		historyOID := randomList.EnvironmentID
		if randomList.CohortID != primitive.NilObjectID {
			historyOID = randomList.CohortID
		}

		err = tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", historyOID, 3, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) DownloadReport(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, roleId string) error {
	// 获取配置数据
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	var attribute models.Attribute
	var randomDesign models.RandomDesign

	var project models.Project

	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match["cohort_id"] = cohortOID
	}
	err := tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return errors.WithStack(err)
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	err = tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$lookup", Value: bson.M{
			"from": "visit_cycle",
			"let": bson.M{
				"visit_cycles": "$configures.visit_cycles",
			},
			"pipeline": bson.A{
				bson.M{"$unwind": "$infos"},
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$infos.id", "$$visit_cycles"}}}},
			},
			"as": "visit_cycle",
		}}},
		{{
			Key: "$project",
			Value: bson.M{
				"group":        "$configures.group",
				"drugName":     "$configures.values.drugname",
				"visit":        "$visit_cycle.infos.name",
				"count":        "$configures.values.dispensing_number",
				"formulas":     "$configures.values.calculation_info.formulas",
				"label":        "$configures.label",
				"desc":         "$configures.values.drug_spec",
				"open_setting": "$configures.open_setting",
			},
		}},
	}
	cursor, _ := tools.Database.Collection("drug_configure").Aggregate(nil, pipeline)
	var drugConfig []map[string]interface{}

	err = cursor.All(nil, &drugConfig)
	if err != nil {
		return errors.WithStack(err)
	}

	// 获取激活随机列表配置
	match["status"] = 1
	var randomList []map[string]interface{}
	randomListCursor, _ := tools.Database.Collection("random_list").Find(nil, match)
	err = randomListCursor.All(nil, &randomList)
	if err != nil {
		return errors.WithStack(err)
	}

	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return errors.WithStack(err)
	}
	hour := time.Duration(timeZone)
	minute := time.Duration((timeZone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	date := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")
	env := ""
	cohort := ""
	for _, environment := range project.Environments {
		if environment.ID == envOID {
			env = environment.Name
		}
		for _, item := range environment.Cohorts {
			if item.ID.Hex() == cohortID {
				cohort = item.Name
			}
		}
	}
	//
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return err
	}
	groups := models.RandomDesignToGroups(randomDesign, isBlindedRole, attribute)
	var factor []string
	for _, randomFactor := range randomDesign.Info.Factors {
		var tmp []string
		for _, option := range randomFactor.Options {
			tmp = append(tmp, option.Label)
		}
		temp := fmt.Sprintf("%s(%s)", randomFactor.Label, strings.Join(tmp, "、"))
		factor = append(factor, temp)
	}

	var medicineData []map[string]interface{}
	openMedicineData := []map[string]interface{}{}
	formulaMedicineData := []map[string]interface{}{}
	for _, configure := range drugConfig {
		var temVisit []string
		var drugName []string
		var count []string
		var desc []string
		var formuals []string
		for _, item := range configure["visit"].(primitive.A) {
			temVisit = append(temVisit, item.(string))
		}
		for i := range configure["drugName"].(primitive.A) {
			name := ""
			if !isBlindedRole || !attribute.AttributeInfo.Blind {
				name = configure["drugName"].(primitive.A)[i].(string)
			} else {
				name = tools.BlindData
			}
			drugName = append(drugName, name)

			count = append(count, strconv.Itoa(int(configure["count"].(primitive.A)[i].(int32))))
			desc = append(desc, configure["desc"].(primitive.A)[i].(string))
			formuals = append(formuals, name)
			for _, formula := range configure["formulas"].(primitive.A) {
				if formula != nil {
					for _, item := range formula.(primitive.A) {
						itemMap := item.(map[string]interface{})
						formuals = append(formuals, convertor.ToString(itemMap["expression"])+","+convertor.ToString(itemMap["value"]))
					}
				}
			}
		}
		groupCode := ""
		if !isBlindedRole || !attribute.AttributeInfo.Blind {
			groupCode = configure["group"].(string)
		} else {
			groupCode = tools.BlindData
		}

		descs := []string{}
		for _, item := range desc {
			if strings.ReplaceAll(item, " ", "") != "" {
				descs = append(descs, item)
			}
		}
		if configure["open_setting"] != nil && configure["open_setting"].(int32) == 2 {
			openMedicineData = append(openMedicineData, map[string]interface{}{
				"groupCode": groupCode,
				"drugName":  strings.Join(drugName, ","),
				"visit":     strings.Join(temVisit, ","),
				"count":     strings.Join(count, ","),
				"label":     configure["label"],
				"desc":      strings.Join(descs, ","),
			})
		} else if configure["open_setting"] != nil && configure["open_setting"].(int32) == 1 {
			medicineData = append(medicineData, map[string]interface{}{
				"groupCode": groupCode,
				"drugName":  strings.Join(drugName, ","),
				"visit":     strings.Join(temVisit, ","),
				"count":     strings.Join(count, ","),
				"label":     configure["label"],
				"desc":      strings.Join(descs, ","),
			})
		} else if configure["open_setting"] != nil && configure["open_setting"].(int32) == 3 {

			formulaMedicineData = append(formulaMedicineData, map[string]interface{}{
				"groupCode": groupCode,
				"drugName":  strings.Join(drugName, ","),
				"visit":     strings.Join(temVisit, ","),
				"count":     strings.Join(count, ","),
				"label":     "",
				"desc":      strings.Join(descs, ","),
			})
		}

	}
	accuracy := "ones"
	types := ""
	if attribute.AttributeInfo.Accuracy != 1 {
		accuracy = "twos"
	}
	if randomDesign.Info.Type == 2 {
		types = locales.Tr(ctx, "randomization.type.twos")

	} else {
	}
	if randomDesign.Info.Type == 1 {
		types = locales.Tr(ctx, "randomization.type.ones")

	}

	data := map[string]interface{}{
		"sponsor":          project.Sponsor,
		"projectName":      project.Name,
		"projectNumber":    project.Number,
		"env":              env,
		"cohort":           cohort,
		"createDate":       date,
		"createBy":         user.Name,
		"random":           attribute.AttributeInfo.Random,
		"blind":            attribute.AttributeInfo.Blind,
		"dispensing":       attribute.AttributeInfo.Dispensing,
		"instituteLayered": attribute.AttributeInfo.InstituteLayered,
		"countryLayered":   attribute.AttributeInfo.CountryLayered,
		"regionLayered":    attribute.AttributeInfo.RegionLayered,
		"prefix":           attribute.AttributeInfo.Prefix,
		//"sitePrefix":         attribute.AttributeInfo.SitePrefix,
		"SubjectReplaceText":     subjectReplaceText,
		"digit":                  attribute.AttributeInfo.Digit,
		"accuracy":               locales.Tr(ctx, fmt.Sprintf("randomization.accuracy.%s", accuracy)),
		"isFreeze":               attribute.AttributeInfo.IsFreeze,
		"isRandom":               attribute.AttributeInfo.IsRandom,
		"type":                   types,
		"group":                  strings.Join(groups, ","),
		"random_list":            randomList,
		"factor":                 factor,
		"openDrugConfiguresData": openMedicineData,
		"formulaConfiguresData":  formulaMedicineData,
		"drugConfiguresData":     medicineData,
	}
	pdf, err := tools.RandomizationToPdf(ctx, data)
	if err != nil {
		return errors.WithStack(err)

	}
	pdfName := url.PathEscape(fmt.Sprintf("%s_ConfigureReport", project.Number))
	err = pdf.WritePdf(pdfName)
	if err != nil {
		return errors.WithStack(err)
	}
	defer os.Remove(pdfName)
	ctx.Header("Content-Type",
		"application/pdf")
	fileName := url.PathEscape(fmt.Sprintf("%s_ConfigureReport_%s.pdf", project.Number, date[:10]))
	ctx.FileAttachment(pdfName, fileName)
	return nil
}

func (s *RandomizationService) GetRandomStatisticsPie(ctx *gin.Context) (interface{}, error) {
	roleId := ctx.Query("roleId")
	envId := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortID := ctx.Query("cohortId")
	cohortOID := primitive.NilObjectID
	if cohortID != "" {
		cohortOID, _ = primitive.ObjectIDFromHex(cohortID)
	}
	const countrySiteType = 0
	const factorType = 1
	const regionType = 2
	pieType, err := strconv.Atoi(ctx.Query("type"))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	type statistics struct {
		ID            primitive.ObjectID `json:"id" bson:"id"`
		CohortID      primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
		Factors       []models.Factors   `json:"factors" bson:"factors"`
		Country       string             `json:"country" bson:"country"`
		ProjectSiteId primitive.ObjectID `json:"projectSiteId" bson:"project_site_id"`
		RandomListId  primitive.ObjectID `json:"randomListId" bson:"random_list_id"`
		Info          []models.Info      `json:"info" bson:"info"`
		Region        string             `json:"region" bson:"region"`
	}
	statisticsData := make([]statistics, 0)
	match := bson.M{"group": bson.M{"$ne": ""}, "env_id": envOID, "status": bson.M{"$in": []int{3, 4, 6, 9}}}
	if !cohortOID.IsZero() {
		match["cohort_id"] = cohortOID
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "random_number",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "random_number",
		}}},
		{{"$unwind", bson.M{"path": "$random_number", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "region",
			"localField":   "project_site.region_id",
			"foreignField": "_id",
			"as":           "region",
		}}},
		{{"$unwind", bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"_id":             0,
			"id":              "$_id",
			"cohort_id":       "$cohort_id",
			"factors":         "$random_number.factors",
			"country":         bson.M{"$ifNull": bson.A{bson.M{"$first": "$project_site.country"}, ""}},
			"project_site_id": 1,
			"random_list_id":  1,
			"info":            1,
			"region":          "$region.name",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &statisticsData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var randomDesigns []models.RandomDesign
	cursor, err = tools.Database.Collection("random_design").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomDesigns)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	randomDesignsMap := make(map[primitive.ObjectID]models.RandomDesign)
	for _, design := range randomDesigns {
		randomDesignsMap[design.CohortID] = design
	}

	for i := 0; i < len(statisticsData); i++ {
		if statisticsData[i].RandomListId.IsZero() {
			replaceFactors := randomDesignsMap[statisticsData[i].CohortID].Info.Factors
			factorInfo := slice.Filter(statisticsData[i].Info, func(index int, item models.Info) bool {
				return strings.Contains(item.Name, "factor")
			})
			factors := make([]models.Factors, 0)
			for _, info := range factorInfo {
				if replaceFactors != nil && len(replaceFactors) > 0 {
					randomFactorP, b := slice.Find(replaceFactors, func(index int, item models.RandomFactor) bool {
						return item.Name == info.Name
					})
					if b {
						rf := *randomFactorP
						text := ""
						for _, o := range rf.Options {
							if info.Value != nil && o.Value == info.Value.(string) {
								text = o.Label
							}
						}
						if info.Value != nil {
							factors = append(factors, models.Factors{
								Name:  info.Name,
								Label: rf.Label,
								Value: info.Value.(string),
								Text:  text,
							})
						}
					}
				}
			}
			statisticsData[i].Factors = factors
		}
	}

	type respItem struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}
	type respone struct {
		Total int        `json:"total"`
		Array []respItem `json:"array"`
	}
	respArray := make([]respItem, 0)

	role, err := tools.GetRole(roleId)
	if err != nil {
		return nil, err
	}
	if role.Scope == "depot" {
		return respone{
			Total: 0,
			Array: respArray,
		}, nil
	} else if role.Scope == "site" {
		sites, err := tools.GetRoleSite(ctx, envId)
		if err != nil {
			return nil, err
		}
		statisticsData = slice.Filter(statisticsData, func(index int, item statistics) bool {
			return slice.Contain(sites, item.ProjectSiteId)
		})
	}
	if pieType == countrySiteType {
		attribute := models.Attribute{}
		find := bson.M{"env_id": envOID}
		if !cohortOID.IsZero() {
			find["cohort_id"] = cohortOID
		}
		err := tools.Database.Collection("attribute").FindOne(nil, find).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if attribute.AttributeInfo.CountryLayered {
			type country struct {
				Code string `json:"code" bson:"code"`
				CN   string `json:"cn" bson:"cn"`
				EN   string `json:"en" bson:"en"`
			}
			opts := &options.FindOptions{
				Projection: bson.M{
					"code": 1,
					"cn":   1,
					"en":   1,
				},
			}
			countries := make([]country, 0)
			cursor, err := tools.Database.Collection("country").Find(nil, bson.M{}, opts)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &countries)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			countryMap := make(map[string][]statistics)
			countryMap = slice.GroupWith(statisticsData, func(t statistics) string {
				return t.Country
			})
			lang := ctx.Request.Header.Get("Accept-Language")
			for k, v := range countryMap {
				cP, ok := slice.Find(countries, func(index int, c country) bool {
					return c.Code == k
				})
				if ok {
					c := *cP
					name := ""
					if lang == "zh" {
						name = c.CN
					} else {
						name = c.EN
					}

					respArray = append(respArray, respItem{
						Name:  name,
						Value: len(v),
					})
				}
			}
		}
	} else if pieType == factorType {
		type factor struct {
			ID     primitive.ObjectID `json:"id" bson:"id"`
			Factor models.Factors     `json:"factors" bson:"factors"`
		}
		factors := make([]factor, 0)
		for _, s := range statisticsData {
			for _, f := range s.Factors {
				_, ok := slice.Find(s.Info, func(index int, info models.Info) bool {
					return info.Name == f.Name
				})
				if ok {
					factors = append(factors, factor{
						ID:     s.ID,
						Factor: f,
					})
				}
			}
		}

		factorMap := make(map[string][]factor)
		factors = slice.Filter(factors, func(index int, item factor) bool {
			return item.Factor.Text != ""
		})
		factorMap = slice.GroupWith(factors, func(t factor) string {
			return t.Factor.Label + "-" + t.Factor.Text
		})

		for k, v := range factorMap {
			respArray = append(respArray, respItem{
				Name:  k,
				Value: len(v),
			})
		}
	} else if pieType == regionType {
		find := bson.M{"env_id": envOID}
		if !cohortOID.IsZero() {
			find["cohort_id"] = cohortOID
		}
		regionMap := make(map[string][]statistics)
		regionMap = slice.GroupWith(statisticsData, func(t statistics) string {
			return t.Region
		})
		for k, v := range regionMap {
			if k != "" {
				respArray = append(respArray, respItem{
					Name:  k,
					Value: len(v),
				})
			}
		}
	}
	err = slice.SortByField(respArray, "Name")
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return respone{
		Total: len(statisticsData),
		Array: respArray,
	}, nil
}

func (s *RandomizationService) GetRandomStatisticsBar(ctx *gin.Context) (interface{}, error) {
	projectId := ctx.Query("projectId")
	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	roleId := ctx.Query("roleId")
	envId := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortID := ctx.Query("cohortId")
	cohortOID := primitive.NilObjectID
	if cohortID != "" {
		cohortOID, _ = primitive.ObjectIDFromHex(cohortID)
	}

	projectTimeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return models.RemoteSubjectDispensing{}, errors.WithStack(err)
	}
	//strTimeZone := fmt.Sprintf("UTC%+d", projectTimeZone)
	//strTimeZone := tools.FormatOffsetToZoneStringUtc(projectTimeZone)
	timeZone := projectTimeZone
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	const monthType = 0
	const weekType = 1
	barType, err := strconv.Atoi(ctx.Query("type"))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	type randomTimeItem struct {
		ID            primitive.ObjectID `json:"id" bson:"id"`
		RandomTime    time.Duration      `json:"randomTime" bson:"random_time"`
		ProjectSiteId primitive.ObjectID `json:"projectSiteId" bson:"project_site_id"`
	}
	randomTimeArray := make([]randomTimeItem, 0)
	match := bson.M{"group": bson.M{"$ne": ""}, "env_id": envOID, "status": bson.M{"$in": []int{3, 4, 6, 9}}}
	if !cohortOID.IsZero() {
		match["cohort_id"] = cohortOID
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$project", Value: bson.M{
			"_id":             0,
			"id":              "$_id",
			"project_site_id": 1,
			"random_time":     1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomTimeArray)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	role, err := tools.GetRole(roleId)
	if err != nil {
		return nil, err
	}
	type respItem struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}
	type resp struct {
		Current    []respItem `json:"current"`
		Cumulative []respItem `json:"cumulative"`
	}
	current := make([]respItem, 0)
	cumulative := make([]respItem, 0)
	if role.Scope == "depot" {
		return resp{
			Current:    current,
			Cumulative: cumulative,
		}, nil
	} else if role.Scope == "site" {
		sites, err := tools.GetRoleSite(ctx, envId)
		if err != nil {
			return nil, err
		}
		randomTimeArray = slice.Filter(randomTimeArray, func(index int, item randomTimeItem) bool {
			return slice.Contain(sites, item.ProjectSiteId)
		})
	}

	type randomTimeStrItem struct {
		ID         primitive.ObjectID `json:"id"`
		RandomTime string             `json:"randomTime"`
	}
	randomTimeStrArray := slice.Map(randomTimeArray, func(index int, item randomTimeItem) randomTimeStrItem {
		randomTime := ""
		if barType == monthType {
			randomTime = time.Unix(item.RandomTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006.01")
		} else if barType == weekType {
			randomTime = tools.GetOfWeek(time.Unix(item.RandomTime.Nanoseconds(), 0).UTC().Add(duration), "2006.01.02", time.Monday) +
				"~" +
				tools.GetOfWeek(time.Unix(item.RandomTime.Nanoseconds(), 0).UTC().Add(duration), "2006.01.02", time.Sunday)
		}

		return randomTimeStrItem{
			ID:         item.ID,
			RandomTime: randomTime,
		}
	})
	randomTimeMap := make(map[string][]randomTimeStrItem)
	randomTimeMap = slice.GroupWith(randomTimeStrArray, func(t randomTimeStrItem) string {
		return t.RandomTime
	})

	for k, v := range randomTimeMap {
		current = append(current, respItem{
			Name:  k,
			Value: len(v),
		})
	}
	err = slice.SortByField(current, "Name")
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for i, c := range current {
		if i == 0 {
			cumulative = append(cumulative, respItem{
				Name:  c.Name,
				Value: c.Value,
			})
		} else {
			cumulative = append(cumulative, respItem{
				Name:  c.Name,
				Value: c.Value + cumulative[i-1].Value,
			})
		}
	}

	return resp{
		Current:    current,
		Cumulative: cumulative,
	}, nil
}

// 计算组间比例（获取最大公约数）
func gcd(m int, n int) int {
	for {
		if m == n {
			break
		} else {
			if m > n {
				m = m - n
			} else {
				n = n - m
			}
		}
	}
	return m
}

func multiGcd(array []int) int {
	l := len(array)
	if l == 1 {
		return array[0]
	}
	if l == 2 {
		return gcd(array[0], array[1])
	} else {
		return gcd(multiGcd(array[:l/2]), multiGcd(array[l/2:]))
	}
}

func getOidValue(factor map[string]interface{}, oid string) string {
	if factor[oid] != nil {
		return factor[oid].(string)
	} else {
		return ""
	}
}

func (s *RandomizationService) GetRandomListMany(ctx *gin.Context, envID string, cohorts []string) (interface{}, error) {
	var cohortOIDs []primitive.ObjectID
	envOID, _ := primitive.ObjectIDFromHex(envID)
	for _, cohortID := range cohorts {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		cohortOIDs = append(cohortOIDs, cohortOID)
	}
	match := bson.M{"env_id": envOID}
	if len(cohorts) > 0 {
		match["cohort_id"] = bson.M{"$in": cohortOIDs}
	}
	var randomList []models.RandomList
	cursor, err := tools.Database.Collection("random_list").Find(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return randomList, nil
}

func (s *RandomizationService) GetAttributeList(ctx *gin.Context, envID string) ([]*models.Attribute, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	var data []*models.Attribute
	match := bson.M{
		"env_id": envOID,
	}
	cursor, err := tools.Database.Collection("attribute").Find(ctx, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(ctx, &data); err != nil {
		return nil, errors.WithStack(err)
	}

	return data, nil
}

func (s *RandomizationService) GetRegion(ctx *gin.Context) (interface{}, error) {
	envID := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{
		"deleted": bson.M{"$ne": true},
		"env_id":  envOID,
	}
	//查询区域信息，同form
	regions := make([]models.Region, 0)
	cursor, err := tools.Database.Collection("region").Find(nil, match)
	if err != nil {
		return regions, errors.WithStack(err)
	}
	err = cursor.All(nil, &regions)
	if err != nil {
		return regions, errors.WithStack(err)
	}
	projectSites := make([]models.ProjectSite, 0)
	cursor, err = tools.Database.Collection("project_site").Find(nil, match)
	if err != nil {
		return regions, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return regions, errors.WithStack(err)
	}
	siteRegionIds := slice.Filter(projectSites, func(index int, item models.ProjectSite) bool {
		return !item.RegionID.IsZero()
	})
	regionIds := slice.Map(siteRegionIds, func(index int, item models.ProjectSite) primitive.ObjectID {
		return item.RegionID
	})

	regionIds = slice.Unique(regionIds)

	for i := range regions {
		option := regions[i]
		if slice.Contain(regionIds, option.ID) {
			regions[i].Disable = true
		}
	}
	return regions, nil
}

func (s *RandomizationService) AddRegion(ctx *gin.Context) error {
	req := struct {
		CustomerID primitive.ObjectID `json:"customerId"`
		ProjectID  primitive.ObjectID `json:"projectId"`
		EnvID      primitive.ObjectID `json:"envId"`
		Name       string             `json:"name"`
	}{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	match := bson.M{
		"env_id":  req.EnvID,
		"name":    req.Name,
		"deleted": bson.M{"$ne": true},
	}
	operationOID := req.EnvID
	count, err := tools.Database.Collection("region").CountDocuments(nil, match)
	if err != nil {
		return errors.WithStack(err)
	}
	if count > 0 {
		return tools.BuildServerError(ctx, "common.duplicated.names")
	}
	region := models.Region{
		ID:         primitive.NewObjectID(),
		CustomerID: req.CustomerID,
		ProjectID:  req.ProjectID,
		EnvID:      req.EnvID,
		Name:       req.Name,
		Deleted:    false,
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		_, err := tools.Database.Collection("region").InsertOne(sctx, region)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = insertRegionLog(ctx, sctx, operationOID, 1, models.Region{}, region, region.ID, "")
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}
func (s *RandomizationService) DeleteRegion(ctx *gin.Context) error {
	req := struct {
		ID    primitive.ObjectID `json:"id"`
		EnvID primitive.ObjectID `json:"envId"`
	}{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	operationOID := req.EnvID
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oldRegion := models.Region{}
		err := tools.Database.Collection("region").FindOneAndUpdate(sctx, bson.M{"_id": req.ID}, bson.M{"$set": bson.M{"deleted": true}}).Decode(&oldRegion)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = insertRegionLog(ctx, sctx, operationOID, 3, oldRegion, models.Region{}, req.ID, "")
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RandomizationService) SyncRandomList(ctx *gin.Context) error {
	envId := ctx.Query("envId")
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortID := ctx.Query("cohortId")
	cohortOID := primitive.NilObjectID
	OID := envOID
	filter := bson.M{"env_id": envOID}
	if cohortID != "" {
		cohortOID, _ = primitive.ObjectIDFromHex(cohortID)
		filter["cohort_id"] = cohortOID
		OID = cohortOID
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var randomDesign models.RandomDesign
		err := tools.Database.Collection("random_design").FindOne(sctx, filter).Decode(&randomDesign)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		filter["status"] = 1
		randomLists := make([]models.RandomList, 0)
		cursor, err := tools.Database.Collection("random_list").Find(sctx, filter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &randomLists)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		updateList := []string{}
		for _, list := range randomLists {
			if randomDesign.Info.Type != list.Design.Type {
				return nil, tools.BuildServerError(ctx, "random_type_error")
			}
			updateList = append(updateList, list.Name)
		}
		// combinations := layeredArrangement(randomDesign.Info.Factors)
		_, err = tools.Database.Collection("random_list").UpdateMany(sctx, filter, bson.M{"$set": bson.M{"design.factors": randomDesign.Info.Factors}})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		marks := []models.Mark{}

		var OperationLogFieldGroups []models.OperationLogFieldGroup
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_design.factorLabel",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  6,
				Value: "1",
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_design.list",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: strings.Join(updateList, ","),
			},
		})
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", OID, 1, OperationLogFieldGroups, marks, randomDesign.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func insertRegionLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldRegion models.Region, newRegion models.Region, OID primitive.ObjectID, copyEnv string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if copyEnv != "" {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.region",
			TranKey: "operation_log.region.env_name",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: copyEnv,
			},
		})
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design.region",
		TranKey: "operation_log.region.name",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldRegion.Name,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newRegion.Name,
		},
	})
	marks := []models.Mark{}
	if types == 1 {
		marks = append(marks, models.Mark{
			Label: "operation_log.label.name",
			Value: newRegion.Name,
			Blind: false,
		})
	} else {
		marks = append(marks, models.Mark{
			Label: "operation_log.label.name",
			Value: oldRegion.Name,
			Blind: false,
		})
	}

	if types == 3 || len(OperationLogFieldGroups) != 0 {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.region", envOID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (s *RandomizationService) GetAttributeConnectAli(ctx *gin.Context, projectID string, envId string, connectAli string, aliProjectNo string) (bool, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	var data []*models.Attribute
	connect, _ := strconv.ParseBool(connectAli)
	match := bson.M{
		"info.connect_ali":    connect,
		"info.ali_project_no": aliProjectNo,
	}
	cursor, err := tools.Database.Collection("attribute").Find(ctx, match)
	if err != nil {
		return true, errors.WithStack(err)
	}
	if err = cursor.All(ctx, &data); err != nil {
		return true, errors.WithStack(err)
	}

	if len(data) > 0 {
		id := data[0].ProjectID
		if id != projectOID {
			return false, nil
		} else {
			eId := data[0].EnvironmentID
			if eId != envOID {
				return false, nil
			} else {
				if data[0].CohortID != primitive.NilObjectID {
					return true, nil
				} else {
					return false, nil
				}
			}
		}
	}

	return true, nil
}

func (s *RandomizationService) GetAttributeConnect(ctx *gin.Context, projectID string, envId string) (models.Attribute, error) {
	var attribute models.Attribute

	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	var data []models.Attribute
	match := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	cursor, err := tools.Database.Collection("attribute").Find(ctx, match)
	if err != nil {
		return attribute, errors.WithStack(err)
	}
	if err = cursor.All(ctx, &data); err != nil {
		return attribute, errors.WithStack(err)
	}

	attribute = data[0]

	return attribute, nil
}

func (s *RandomizationService) GetFormType(ctx *gin.Context, customerID string, envID string, cohortID string, applicationType int) ([]models.Field, error) {
	newFields := make([]models.Field, 0)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		//"fields":      bson.M{"$elemMatch": bson.M{"application_type": applicationType}},
	}

	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}
	var form models.Form
	if err := tools.Database.Collection("form").FindOne(nil, filter).Decode(&form); err != nil && err != mongo.ErrNoDocuments {
		return newFields, errors.WithStack(err)
	}
	opts := options.Find().SetProjection(bson.M{
		"info": 1,
	})
	type subjectResp struct {
		ID   primitive.ObjectID `json:"id" bson:"_id"`
		Info []models.Info      `json:"info"`
	}
	subjects := make([]*subjectResp, 0)
	cursor, err := tools.Database.Collection("subject").Find(nil, filter, opts)
	if err != nil {
		return newFields, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return newFields, errors.WithStack(err)
	}
	for i := range form.Fields {
		//状态默认值
		if form.Fields[i].Status == nil {
			status := 1
			form.Fields[i].Status = &status
		}
		// 应用类型 默认值
		if form.Fields[i].ApplicationType == nil {
			at := 1
			form.Fields[i].ApplicationType = &at
		}

		//判断是否有subject引用
		if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" || form.Fields[i].Type == "checkbox" || form.Fields[i].IsCalc == true {
			for _, subject := range subjects {
				for _, info := range subject.Info {
					if info.Name == form.Fields[i].Name {
						for j := range form.Fields[i].Options {
							if form.Fields[i].Type == "checkbox" && info.Value != nil {
								for _, v := range info.Value.(primitive.A) {
									if form.Fields[i].Options[j].Value == v {
										form.Fields[i].Options[j].Disable = true
									}
								}
							} else if form.Fields[i].Options[j].Value == info.Value {
								form.Fields[i].Options[j].Disable = true
							}
						}
					}
				}
			}
		}
		apt := *form.Fields[i].ApplicationType
		status := *form.Fields[i].Status
		if apt == applicationType && status != 2 {
			newFields = append(newFields, form.Fields[i])
		}
	}
	return newFields, nil
}

func (s *RandomizationService) GetRandomCohort(ctx *gin.Context, proejctID, envID string) ([]models.Cohort, error) {
	cohort := []models.Cohort{}
	var project models.Project
	var attributes []models.Attribute
	projectOID, _ := primitive.ObjectIDFromHex(proejctID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP
	for _, c := range env.Cohorts {
		_, ok := slice.Find(attributes, func(index int, item models.Attribute) bool {
			return item.CohortID == c.ID && item.AttributeInfo.Random
		})
		if ok {
			cohort = append(cohort, c)
		}
	}
	return cohort, nil
}

// RandomNumberActivateInactivate 处理区组/随机号的状态更新
func (s *RandomizationService) RandomNumberActivateInactivate(ctx *gin.Context, req models.RandomNumberActivateInactivateReq) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var randomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(sctx, bson.M{"_id": req.RandomListID}).Decode(&randomList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var update bson.M
		var existingStatus int
		switch req.OperationType {
		case 0: // 激活
			update = bson.M{"$set": bson.M{"status": 1}}
			existingStatus = 5 // 当前状态需为5（失活）
		case 1: // 失活
			update = bson.M{"$set": bson.M{"status": 5}}
			existingStatus = 1 // 当前状态需为1（激活）
		default:
			return nil, tools.BuildServerError(ctx, "common.fail")
		}

		var filter bson.M
		if req.DataType == 0 {
			// 区组操作：根据random_list_id、block和当前状态过滤
			filter = bson.M{
				"random_list_id": req.RandomListID,
				"block":          req.Block,
				"status":         existingStatus,
			}
			if req.RangeType == 2 {
				if req.StartStr != "" && req.EndStr != "" {
					filter["number"] = bson.M{"$gte": req.StartStr, "$lte": req.EndStr}
				} else if req.StartStr != "" {
					filter["number"] = bson.M{"$gte": req.StartStr}
				} else if req.EndStr != "" {
					filter["number"] = bson.M{"$lte": req.EndStr}
				}
			}
		} else if req.DataType == 1 {
			// 单个随机号操作：根据ID和当前状态过滤
			filter = bson.M{
				"random_list_id": req.RandomListID,
				"number":         req.Number,
				"status":         existingStatus,
			}
			// 检查当前状态是否符合预期
			var count int64
			count, err = tools.Database.Collection("random_number").CountDocuments(sctx, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if count == 0 {
				return nil, tools.BuildServerError(ctx, "randomNumber_status_error")
			}
		}

		// 执行更新操作
		_, err = tools.Database.Collection("random_number").UpdateMany(sctx, filter, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		envCohortId := randomList.EnvironmentID
		if !randomList.CohortID.IsZero() {
			envCohortId = randomList.CohortID
		}
		err = insertBlockNumberActiveInactivateLog(ctx, sctx, envCohortId, req.DataType, req.OperationType, req.RandomListID, randomList.Name, req.Block, req.Number)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *RandomizationService) RandomNumberInactivate(ctx *gin.Context, req models.RandomNumberInactivateReq) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		OID := req.EnvID
		filter := bson.M{
			"env_id": req.EnvID,
		}

		if !req.CohortID.IsZero() {
			filter["cohort_id"] = req.CohortID
			OID = req.CohortID
		}
		var randomDesign models.RandomDesign
		err := tools.Database.Collection("random_design").FindOne(sctx, filter).Decode(&randomDesign)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		invalidGroups := make([]models.RandomGroup, 0)
		for _, group := range randomDesign.Info.Groups {
			if group.Status != nil && *group.Status == 2 {
				invalidGroups = append(invalidGroups, models.RandomGroup{
					Code: group.Code,
					Name: group.Name,
				})
			}
		}
		randomListFilter := bson.M{"env_id": req.EnvID, "status": bson.M{"$in": bson.A{1, 5}}}
		if !req.CohortID.IsZero() {
			randomListFilter["cohort_id"] = req.CohortID
		}
		randomLists := make([]models.RandomList, 0)
		cursor, err := tools.Database.Collection("random_list").Find(sctx, randomListFilter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &randomLists)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, list := range randomLists {
			if randomDesign.Info.Type == 2 || randomDesign.Info.Type != list.Design.Type {
				return nil, tools.BuildServerError(ctx, "random_type_error")
			}
		}

		groups := make([]string, 0)
		for _, group := range invalidGroups {
			matchList := slice.Filter(randomLists, func(index int, item models.RandomList) bool {
				_, b := slice.Find(item.Design.Groups, func(index int, g models.RandomListGroup) bool {
					if g.SubName != "" {
						return g.Code == group.Code && g.ParName == group.Name
					}
					return g.Code == group.Code && g.Name == group.Name
				})
				return b
			})
			matchIds := slice.Map(matchList, func(index int, item models.RandomList) primitive.ObjectID {
				return item.ID
			})
			opts := &options.UpdateOptions{
				ArrayFilters: &options.ArrayFilters{
					Filters: bson.A{
						bson.M{"group.code": group.Code,
							"$or": bson.A{
								bson.M{"group.name": group.Name},
								bson.M{"group.par_name": group.Name},
							}},
					},
				},
			}
			_, err := tools.Database.Collection("random_list").UpdateMany(sctx,
				bson.M{"_id": bson.M{"$in": matchIds}},
				bson.M{
					"$set": bson.M{"design.groups.$[group].status": 2},
				}, opts)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			_, err = tools.Database.Collection("random_number").UpdateMany(sctx, bson.M{"random_list_id": bson.M{"$in": matchIds}, "status": bson.M{"$in": bson.A{1, 5}}, "$or": bson.A{bson.M{"group": group.Name}, bson.M{"par_name": group.Name}}}, bson.M{"$set": bson.M{"status": 4}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			groups = append(groups, fmt.Sprintf("%s:%s", group.Code, group.Name))
		}

		marks := []models.Mark{}

		var OperationLogFieldGroups []models.OperationLogFieldGroup
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_design.inactivate",
			Old: models.OperationLogField{
				Type:  7,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  7,
				Value: strings.Join(groups, ","),
			},
		})
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", OID, 2, OperationLogFieldGroups, marks, primitive.NilObjectID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RandomizationService) GetGroupDistributionStatistics(ctx *gin.Context, randomListID string) (int64, error) {
	req := struct {
		Type          int                `json:"type"`          // 0 分层；1 中心；2 国家；3 地区
		Factors       []models.Factors   `json:"factors"`       // 分层因素等字段信息
		Country       string             `json:"country"`       // 国家code
		ProjectSiteID primitive.ObjectID `json:"projectSiteId"` // 中心ID
		RegionID      primitive.ObjectID `json:"regionId"`      // 区域ID
	}{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	randomListOID, _ := primitive.ObjectIDFromHex(randomListID)
	match := bson.M{"random_list_id": randomListOID}
	if req.Type == 0 {
		// 构建查询条件：factors 数组必须包含所有 newFactors 中的元素
		var andConditions []bson.M
		for _, factor := range req.Factors {
			if factor.Name != "" {
				andConditions = append(andConditions, bson.M{
					"factors": bson.M{
						"$elemMatch": bson.M{
							"name":  factor.Name,
							"label": factor.Label,
							"value": factor.Value,
							"text":  factor.Text,
						},
					},
				})
			}
		}

		match["$and"] = andConditions
	} else if req.Type == 1 {
		match["project_site_id"] = req.ProjectSiteID
	} else if req.Type == 2 {
		match["country"] = req.Country
	} else if req.Type == 3 {
		match["region_id"] = req.RegionID
	}

	// 统计数量
	count, err := tools.Database.Collection("random_number").CountDocuments(ctx, match)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}
