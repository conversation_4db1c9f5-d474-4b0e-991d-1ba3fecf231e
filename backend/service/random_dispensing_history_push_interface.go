package service

import (
	"bytes"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strconv"
	"strings"
	"time"
)

// 随机信息推送(转发方法)
func SubjectRandomHistoryPush(ctx *gin.Context, subjectId primitive.ObjectID, sourceTypeList []int, now time.Duration) {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询受试者信息
		subjectFilter := bson.M{"_id": subjectId}
		var findSubject models.Subject
		err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询项目
		projectFilter := bson.M{"_id": findSubject.ProjectID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询属性
		var attribute models.Attribute
		match := bson.M{
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"customer_id": findSubject.CustomerID,
		}
		if findSubject.CohortID != primitive.NilObjectID {
			match = bson.M{
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"customer_id": findSubject.CustomerID,
				"cohort_id":   findSubject.CohortID,
			}
		}
		err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询RandomList
		randomFilter := bson.M{
			"customer_id": findSubject.CustomerID,
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"status":      1,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": findSubject.ProjectSiteID},
			}}
		if findSubject.CohortID != primitive.NilObjectID {
			randomFilter = bson.M{
				"customer_id": findSubject.CustomerID,
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   findSubject.CohortID,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": findSubject.ProjectSiteID},
				}}
		}
		if project.ProjectInfo.Type == 3 && findSubject.LastGroup != "" { // 在随机逻辑
			randomFilter = bson.M{
				"customer_id": findSubject.CustomerID,
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   findSubject.CohortID,
				"last_group":  findSubject.LastGroup,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": findSubject.ProjectSiteID},
				}}
		}
		var randomList models.RandomList
		// 只有随机才查询random_list
		if attribute.AttributeInfo.Random {
			err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter).Decode(&randomList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// 查询表单
		var randomForm models.Form
		formFilter := bson.M{"env_id": findSubject.EnvironmentID}
		if findSubject.CohortID != primitive.NilObjectID {
			formFilter = bson.M{"env_id": findSubject.EnvironmentID, "cohort_id": findSubject.CohortID}
		}

		if err := tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm); err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 随机信息组装
		edcPush, err := RandomHistoryDataAssembly(sctx, findSubject, project, randomList, randomForm, sourceTypeList, attribute, now)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 数据推送
		err = Push(sctx, edcPush, project.EdcUrl)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		tools.SavePushEdcErrorLog(ctx, err)
	}
}

// 随机数据组装
func RandomHistoryDataAssembly(
	sctx mongo.SessionContext,
	subject models.Subject,
	project models.Project,
	randomList models.RandomList,
	randomForm models.Form,
	sourceTypeList []int,
	attribute models.Attribute,
	now time.Duration) (models.EdcPush, error) {
	// 查询环境
	var env models.Environment
	for _, environment := range project.Environments {
		if environment.ID == subject.EnvironmentID {
			env = environment
			break
		}
	}

	// 查询cohort
	var cohort models.Cohort
	if project.Type != 1 {
		for _, ch := range env.Cohorts {
			if ch.ID == subject.CohortID {
				cohort = ch
				break
			}
		}
	}

	// 查询中心
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return models.EdcPush{}, err
	}

	// 提取受试者号
	subjectNo := ""
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectNo = fmt.Sprintf("%v", info.Value)
			break
		}
	}
	// 分层因素数据
	var data []models.Data

	if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
		for _, factor := range randomList.Design.Factors { // 分层因素
			// 随机分层
			for _, info := range subject.Info {
				// 随机分层
				if info.Name == factor.Name {
					if info.Value != nil {
						// 提取选项值
						label := ""
						for _, opt := range factor.Options {
							if opt.Value == info.Value {
								label = opt.Label
							}
						}
						// 追加数据
						data = append(data, models.Data{
							Field: factor.Name,
							Value: label,
						})
					}
					break
				}
			}
			// 实际分层
			for _, actualInfo := range subject.ActualInfo {
				// 随机分层
				if actualInfo.Name == factor.Name {
					if actualInfo.Value != nil {
						// 提取选项值
						label := ""
						for _, opt := range factor.Options {
							if opt.Value == actualInfo.Value {
								label = opt.Label
							}
						}
						// 追加数据
						data = append(data, models.Data{
							Field: factor.Name + "_actual",
							Value: label,
						})
					}
					break
				}
			}
		}
	}

	// 表单数据
	if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
		for _, rf := range randomForm.Fields {
			if (rf.Status == nil || *rf.Status == 1) && (rf.ApplicationType == nil || *rf.ApplicationType == 1 || *rf.ApplicationType == 4) {
				if rf.Type == "radio" || rf.Type == "select" { // 单选框或者下拉框
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								// 提取选项值
								label := ""
								for _, opt := range rf.Options {
									if opt.Value == info.Value {
										label = opt.Label
									}
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: label,
								})
							}
							break
						}
					}
				}
				if rf.Type == "checkbox" { // 复选框
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								var checkboxBf bytes.Buffer
								str := info.Value.([]interface{})
								for _, option := range rf.Options {
									for j := 0; j < len(str); j++ {
										if option.Value == str[j].(string) {
											checkboxBf.WriteString(option.Label)
											checkboxBf.WriteString(",")
										}
									}
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: checkboxBf.String(),
								})
							}
							break
						}
					}
				}
				if rf.Type == "switch" { // 开关
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								v := ""
								if info.Value == true {
									v = "yes"
								} else {
									v = "no"
								}
								// 追加数据
								data = append(data, models.Data{
									Field: rf.Name,
									Value: v,
								})
							}
							break
						}
					}
				}

				if rf.Type == "input" || rf.Type == "inputNumber" || rf.Type == "textArea" || rf.Type == "datePicker" || rf.Type == "timePicker" { // 其它类型字段
					for _, info := range subject.Info { // 受试者信息
						if info.Name == rf.Name {
							if info.Value != nil {
								// 追加数据
								otherValue := info.Value
								if rf.Type == "inputNumber" && rf.FormatType == "decimalLength" && rf.Length != nil {
									lengthString := strconv.FormatFloat(*rf.Length, 'f', -1, 64)
									if strings.Contains(lengthString, ".") {
										digits, _ := getFractionDigits(*rf.Length)
										str := strconv.FormatFloat(info.Value.(float64), 'f', digits, 64)
										otherValue = str
									} else {
										otherValue = info.Value
									}
								} else {
									otherValue = info.Value
								}
								data = append(data, models.Data{
									Field: rf.Name,
									Value: otherValue,
								})
							}
							break
						}
					}
				}
			}
		}
	}

	// 筛选 推送场景勾选了筛选，且筛选结果不为空
	if project.PushScenario.ScreenPush && subject.IsScreen != nil {
		isScreenStr := ""
		if *subject.IsScreen == true {
			isScreenStr = "yes"
		} else {
			isScreenStr = "no"
		}

		data = append(data, models.Data{
			Field: "isScreen",
			Value: isScreenStr,
		})
		data = append(data, models.Data{
			Field: "screenTime",
			Value: subject.ScreenTime,
		})
		data = append(data, models.Data{
			Field: "icfTime",
			Value: subject.ICFTime,
		})
	}

	data = append(data, models.Data{
		Field: "cohortName",
		Value: cohort.Name,
	})

	// 随机   推送场景勾选了随机，除去已登记和已筛选状态其它状态都表示受试者已经随机. 判断data是否要追加(随机号/随机组别/随机时间)字段
	if project.PushScenario.RandomPush && subject.Status != 1 && subject.Status != 2 && subject.Status != 7 && subject.Status != 8 {
		data = append(data, models.Data{
			Field: "randomNo",
			Value: subject.RandomNumber,
		})

		value := subject.Group
		parGroupName := "" // 主组别
		subGroupName := "" // 子组别
		// 如果是盲法项目且分配了盲态权限则隐藏组别
		if subject.SubGroupName != "" {
			gP, b := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
				return item.Name == subject.Group
			})
			if b {
				group := *gP
				if attribute.AttributeInfo.Blind && group.Blind {
					value = tools.BlindData + " " + tools.BlindData
					parGroupName = tools.BlindData
					subGroupName = tools.BlindData
				} else if attribute.AttributeInfo.Blind && !group.Blind {
					value = tools.BlindData + " " + subject.SubGroupName
					parGroupName = tools.BlindData
					subGroupName = subject.SubGroupName
				} else if !attribute.AttributeInfo.Blind && group.Blind {
					value = subject.ParGroupName + " " + tools.BlindData
					parGroupName = subject.ParGroupName
					subGroupName = tools.BlindData
				}
			}
		} else {
			if attribute.AttributeInfo.Blind {
				value = tools.BlindData
			}
		}
		data = append(data, models.Data{
			Field: "group",
			Value: value, // 该数据推送的时候在写入
		})
		// 主组别
		data = append(data, models.Data{
			Field: "parGroupName",
			Value: parGroupName,
		})
		// 子组别
		data = append(data, models.Data{
			Field: "subGroupName",
			Value: subGroupName,
		})

		timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
		if err != nil {
			return models.EdcPush{}, err
		}
		if timeZone == "" {
			zone, err := tools.GetTimeZone(project.ID)
			if err != nil {
				return models.EdcPush{}, err
			}
			timeZone = tools.FormatOffsetToZoneStringUtc(zone)
			//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
		}
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		if err != nil {
			return models.EdcPush{}, err
		}

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		randomTime := time.Unix(subject.RandomTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		data = append(data, models.Data{
			Field: "randomTime",
			Value: randomTime + "(" + timeZone + ")",
		})
	}

	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串
	var irtSubjectID = subject.ID.Hex()
	if project.Type == 3 && cohort.LastID != primitive.NilObjectID { // 在随机项目
		irtSubjectID = ""
	}

	id := primitive.NewObjectID()
	remarks, err := MakeHistoryPushRemarks(sourceTypeList, subject, now)
	if err != nil {
		return models.EdcPush{}, err
	}

	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}
	// v2.11 新增逻辑 推送规则如果是受试者号就不传受试者ID
	if project.PushRules == 1 {
		irtSubjectID = ""
	}

	var content = models.Content{
		Env:              env.Name,
		Project:          project.Number,
		Sign:             md5Str,
		Site:             projectSite.Number,
		IrtSubjectID:     irtSubjectID,
		ReplaceSubjectID: "", // 被替换的受试者处于停用状态，历史数据不推送他
		ReplaceSubjectNo: "", // 被替换的受试者处于停用状态，历史数据不推送他
		EdcPushId:        id.Hex(),
		SubjectNoPrefix:  subjectNoPrefix,
		SubjectNo:        subjectNo,
		Timestamp:        timestamp,
		Type:             "",
		Remarks:          remarks,
		Cohort:           cohort.Name,
		SubjectData:      data,
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:             id,
		CustomerID:     subject.CustomerID,
		ProjectID:      subject.ProjectID,
		EnvironmentID:  subject.EnvironmentID,
		CohortID:       subject.CohortID,
		ProjectSiteID:  subject.ProjectSiteID,
		OID:            subject.ID,
		Content:        content,
		PushMode:       1,
		Source:         1,
		SourceType:     0, // 历史数据推送时不使用该字段
		SourceTypeList: sourceTypeList,
	}
	if len(projectSite.Tz) > 0 {
		edcPush.Location = projectSite.Tz
	}
	return edcPush, nil
}

// 发药信息推送(转发方法)
func SubjectDispensingHistoryPush(ctx *gin.Context, dispensingId primitive.ObjectID, sourceType int, now time.Duration) {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询发药信息
		dispensingFilter := bson.M{"_id": dispensingId}
		var findDispensing models.Dispensing
		err := tools.Database.Collection("dispensing").FindOne(sctx, dispensingFilter).Decode(&findDispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询受试者信息
		subjectFilter := bson.M{"_id": findDispensing.SubjectID}
		var findSubject models.Subject
		err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询项目
		projectFilter := bson.M{"_id": findSubject.ProjectID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询属性
		var attribute models.Attribute
		match := bson.M{
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"customer_id": findSubject.CustomerID,
		}
		if findSubject.CohortID != primitive.NilObjectID {
			match = bson.M{
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"customer_id": findSubject.CustomerID,
				"cohort_id":   findSubject.CohortID,
			}
		}
		err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询访视信息
		visitCycleFilter := bson.M{"customer_id": findSubject.CustomerID, "project_id": findDispensing.ProjectID, "env_id": findDispensing.EnvironmentID, "cohort_id": findDispensing.CohortID}
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(sctx, visitCycleFilter).Decode(&visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		visitInfo := models.VisitCycleInfo{}
		for _, info := range visitCycle.Infos {
			if info.ID == findDispensing.VisitInfo.VisitCycleInfoID {
				visitInfo = info
				break
			}
		}

		edcPush, err := DispensingHistoryDataAssembly(sctx, findDispensing, findSubject, project, visitInfo, sourceType, attribute, now)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 推送数据
		err = Push(sctx, edcPush, project.EdcUrl)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		tools.SavePushEdcErrorLog(ctx, err)
	}
}

// 发药数据组装
func DispensingHistoryDataAssembly(sctx mongo.SessionContext, dispensing models.Dispensing, subject models.Subject, project models.Project, visitInfo models.VisitCycleInfo, sourceType int, attribute models.Attribute, now time.Duration) (models.EdcPush, error) {
	// 查询环境
	var env models.Environment
	for _, environment := range project.Environments {
		if environment.ID == subject.EnvironmentID {
			env = environment
			break
		}
	}

	// 查询cohort
	var cohort models.Cohort
	if project.Type != 1 {
		for _, ch := range env.Cohorts {
			if ch.ID == subject.CohortID {
				cohort = ch
				break
			}
		}
	}

	// 查询中心
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return models.EdcPush{}, err
	}

	// 提取受试者号
	subjectNo := ""
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectNo = fmt.Sprintf("%v", info.Value)
			break
		}
	}

	// 组装推送的药物数据
	var data []models.Data
	// 药物号
	var drugNameNumberStr strings.Builder

	drug := make([]map[string]interface{}, 0)

	var labels []string
	// 对药物号进行排序
	err = slice.SortByField(dispensing.DispensingMedicines, "Number")
	if err != nil {
		return models.EdcPush{}, err
	}
	// 已编号药物
	for _, dm := range dispensing.DispensingMedicines {
		isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, dm.Name)
		if err != nil {
			return models.EdcPush{}, err
		}

		drugName := tools.BlindData // 药物名称
		if !isBlindedDrug {
			drugName = dm.Name
		}

		labels = append(labels, dm.Label)

		drugNameNumberStr.WriteString(dm.Number)
		drugNameNumberStr.WriteString("/")

		m := make(map[string]interface{})
		m["drugNo"] = dm.Number
		m["drugName"] = drugName
		drug = append(drug, m)

	}

	// 未编号药物
	for _, odm := range dispensing.OtherDispensingMedicines {
		isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, odm.Name)
		if err != nil {
			return models.EdcPush{}, err
		}

		drugName := tools.BlindData // 药物名称
		if !isBlindedDrug {
			drugName = odm.Name
		}

		labels = append(labels, odm.Label)

		//drugNameNumberStr.WriteString(drugName)
		drugNameNumberStr.WriteString("(")
		drugNameNumberStr.WriteString(strconv.Itoa(odm.Count))
		drugNameNumberStr.WriteString("/")
		drugNameNumberStr.WriteString(odm.Batch)
		drugNameNumberStr.WriteString("/")
		drugNameNumberStr.WriteString(odm.ExpireDate)
		drugNameNumberStr.WriteString(")")
		drugNameNumberStr.WriteString(" / ")

		m := make(map[string]interface{})
		m["drugName"] = drugName
		m["drugCount"] = odm.Count
		m["drugBatch"] = odm.Batch
		m["drugExpireDate"] = odm.ExpireDate
		drug = append(drug, m)
	}

	// 对药物号进行排序
	err = slice.SortByField(dispensing.RealDispensingMedicines, "Number")
	if err != nil {
		return models.EdcPush{}, err
	}
	//实际使用的药物
	if dispensing.RealDispensingMedicines != nil && len(dispensing.RealDispensingMedicines) > 0 {
		drugNameNumberStr.WriteString("【")
		for i, rdm := range dispensing.RealDispensingMedicines {
			index := i + 1

			isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, rdm.Name)
			if err != nil {
				return models.EdcPush{}, err
			}

			drugName := tools.BlindData // 药物名称
			if !isBlindedDrug {
				drugName = rdm.Name
			}
			drugNameNumberStr.WriteString(rdm.Number)
			if len(dispensing.RealDispensingMedicines) > index {
				drugNameNumberStr.WriteString("/ ")
			}

			m := make(map[string]interface{})
			m["drugNo"] = rdm.Number
			m["drugName"] = drugName
			drug = append(drug, m)
		}
		drugNameNumberStr.WriteString("】")
	}

	//实际使用的其它药物
	if dispensing.RealOtherDispensingMedicines != nil && len(dispensing.RealOtherDispensingMedicines) > 0 {
		drugNameNumberStr.WriteString("【")
		for _, rodm := range dispensing.RealOtherDispensingMedicines {
			isBlindedDrug, err := tools.IsBlindedDrug(subject.EnvironmentID, rodm.Name)
			if err != nil {
				return models.EdcPush{}, err
			}

			drugName := tools.BlindData // 药物名称
			if !isBlindedDrug {
				drugName = rodm.Name
			}

			//drugNameNumberStr.WriteString(drugName)
			drugNameNumberStr.WriteString("(")
			drugNameNumberStr.WriteString(strconv.Itoa(rodm.Count))
			drugNameNumberStr.WriteString("/")
			drugNameNumberStr.WriteString(rodm.Batch)
			drugNameNumberStr.WriteString("/")
			drugNameNumberStr.WriteString(rodm.ExpireDate)
			drugNameNumberStr.WriteString(")")
			drugNameNumberStr.WriteString(" / ")

			m := make(map[string]interface{})
			m["drugName"] = drugName
			m["drugCount"] = rodm.Count
			m["drugBatch"] = rodm.Batch
			m["drugExpireDate"] = rodm.ExpireDate
			drug = append(drug, m)
		}
		drugNameNumberStr.WriteString("】")
	}

	// 去除 /
	drugNo := ""
	if drugNameNumberStr.String() != "" {
		drugNo = drugNameNumberStr.String()
		last1 := drugNo[len(drugNo)-1:]
		last2 := drugNo[len(drugNo)-3:]
		if last1 == "/" {
			drugNo = drugNo[0 : len(drugNo)-1]
		} else if last2 == " / " {
			drugNo = drugNo[0 : len(drugNo)-3]
		}
	}

	drugValue := ""
	if drug != nil && len(drug) > 0 {
		drugStr, err := json.Marshal(drug)
		if err != nil {
			return models.EdcPush{}, err
		}
		drugValue = string(drugStr)
	} else {
		drugValue = "[]"
	}

	data = append(data, models.Data{
		Field:                        "drugNo",
		Value:                        drugNo,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	data = append(data, models.Data{
		Field:                        "drug",
		Value:                        drugValue,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	// 水平
	doseLevelValue := ""
	if dispensing.DoseInfo != nil && dispensing.DoseInfo.DoseLevelList != nil && dispensing.DoseInfo.DoseLevelList.Name != "" {
		doseLevelValue = dispensing.DoseInfo.DoseLevelList.Name
	}
	data = append(data, models.Data{
		Field:                        "doseLevel",
		Value:                        doseLevelValue,
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	//标签
	var labelValue strings.Builder
	labels = slice.Unique(labels)
	if labels != nil && len(labels) > 0 {
		for _, label := range labels {
			labelValue.WriteString(label)
			labelValue.WriteString(" ")
		}
	}
	data = append(data, models.Data{
		Field:                        "labels",
		Value:                        labelValue.String(),
		Visit:                        visitInfo.Number,
		InstanceRepeatNo:             dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:                dispensing.VisitInfo.BlockRepeatNo,
		DispensingMedicines:          dispensing.DispensingMedicines,
		OtherDispensingMedicines:     dispensing.OtherDispensingMedicines,
		RealDispensingMedicines:      dispensing.RealDispensingMedicines,
		RealOtherDispensingMedicines: dispensing.RealOtherDispensingMedicines,
	})

	dispensingTime := ""
	timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return models.EdcPush{}, err
	}

	if timeZone == "" {
		zone, err := tools.GetTimeZone(project.ID)
		if err != nil {
			return models.EdcPush{}, err
		}
		timeZone = tools.FormatOffsetToZoneStringUtc(zone)
		//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
	}
	if dispensing.DispensingTime != 0 {
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		if err != nil {
			return models.EdcPush{}, err
		}

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		dispensingTime = time.Unix(dispensing.DispensingTime.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		dispensingTime = dispensingTime + "(" + timeZone + ")"
	}
	data = append(data, models.Data{
		Field:            "drugTime",
		Value:            dispensingTime,
		Visit:            visitInfo.Number,
		InstanceRepeatNo: dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:    dispensing.VisitInfo.BlockRepeatNo,
	})

	data = append(data, models.Data{
		Field:            "cohortName",
		Value:            cohort.Name,
		Visit:            visitInfo.Number,
		InstanceRepeatNo: dispensing.VisitInfo.InstanceRepeatNo,
		BlockRepeatNo:    dispensing.VisitInfo.BlockRepeatNo,
	})

	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串

	var irtSubjectID = subject.ID.Hex()
	if project.Type == 3 && cohort.LastID != primitive.NilObjectID { // 在随机项目
		irtSubjectID = ""
	}

	id := primitive.NewObjectID()
	remarks, err := MakeHistoryPushRemarks([]int{sourceType}, subject, now)
	if err != nil {
		return models.EdcPush{}, err
	}

	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}
	var content = models.Content{
		Env:             env.Name,
		Project:         project.Number,
		Sign:            md5Str,
		Site:            projectSite.Number,
		IrtSubjectID:    irtSubjectID,
		EdcPushId:       id.Hex(),
		SubjectNo:       subjectNo,
		SubjectNoPrefix: subjectNoPrefix,
		Timestamp:       timestamp,
		Type:            "",
		Remarks:         remarks,
		Cohort:          cohort.Name,
		SubjectData:     data,
	}
	if sourceType == 5 { // 访视外发药
		content.Type = "Unscheduled"
	} else if sourceType == 7 { // 补发
		content.Type = "Re-dispense"
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:             id,
		CustomerID:     subject.CustomerID,
		ProjectID:      subject.ProjectID,
		EnvironmentID:  subject.EnvironmentID,
		CohortID:       subject.CohortID,
		ProjectSiteID:  subject.ProjectSiteID,
		OID:            dispensing.ID,
		Content:        content,
		PushMode:       1,
		Source:         2,
		SourceType:     0, // 历史数据推送时不使用该字段
		SourceTypeList: []int{sourceType},
	}
	if len(projectSite.Tz) > 0 {
		edcPush.Location = projectSite.Tz
	}

	return edcPush, nil
}

// 备注处理
func MakeHistoryPushRemarks(sourceTypeList []int, subject models.Subject, now time.Duration) (string, error) {
	var remarks strings.Builder
	remarks.WriteString("Operation: ")

	if sourceTypeList == nil || len(sourceTypeList) == 0 {
		remarks.WriteString("Unknown,")
	} else {
		// sourceType 来源具体细分 0.未知 1.登记 2.修改 3.随机 4.发药 5.访视外发药 6.药物替换 7.药物补发 8.药物撤销 9.药物取回 10.实际用药 11.受试者替换 12.编辑实际分层 13.筛选
		if slice.Contain(sourceTypeList, 1) {
			remarks.WriteString("History Register,")
		}
		if slice.Contain(sourceTypeList, 2) {
			remarks.WriteString("History Edit,")
		}
		if slice.Contain(sourceTypeList, 3) {
			remarks.WriteString("History Randomize,")
		}
		if slice.Contain(sourceTypeList, 4) {
			remarks.WriteString("History Dispense,")
		}
		if slice.Contain(sourceTypeList, 5) {
			remarks.WriteString("History Unscheduled Dispensing,")
		}
		if slice.Contain(sourceTypeList, 6) {
			remarks.WriteString("History Replace IP,")
		}
		if slice.Contain(sourceTypeList, 7) {
			remarks.WriteString("History Re-dispense,")
		}
		if slice.Contain(sourceTypeList, 8) {
			remarks.WriteString("History Withdraw IP,")
		}
		if slice.Contain(sourceTypeList, 9) {
			remarks.WriteString("History Retrieve IP,")
		}
		if slice.Contain(sourceTypeList, 10) {
			remarks.WriteString("History Actually Used IP,")
		}
		if slice.Contain(sourceTypeList, 11) {
			remarks.WriteString("History Subject Replacement,")
		}
		if slice.Contain(sourceTypeList, 12) {
			remarks.WriteString("History Subject Actual Stratification,")
		}
		if slice.Contain(sourceTypeList, 13) {
			remarks.WriteString("History Screening,")
		}
	}

	remarks.WriteString("Time: ")
	timeStr := ""
	timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return "", err
	}
	if timeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return "", err
		}
		timeZone = tools.FormatOffsetToZoneStringUtc(zone)
		//timeZone = fmt.Sprintf("UTC%+d", strTimeZone)
	}
	if now != 0 {
		//intTimeZone, err := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
		if err != nil {
			return "", err
		}

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		timeStr = time.Unix(now.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	}
	remarks.WriteString(timeStr + "(" + timeZone + ").")

	return remarks.String(), nil
}
