package service

import (
	"bytes"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"math"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/maputil"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/wxnacy/wgo/arrays"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type SubjectService struct {
	//approvalProcessService ApprovalProcessService
}

// Update 修改数据
func (s *SubjectService) Update(ctx *gin.Context) error {
	var subject map[string]interface{}
	u, _ := ctx.Get("user")
	user := u.(models.User)
	_ = ctx.ShouldBindBodyWith(&subject, binding.JSON)
	ID, _ := primitive.ObjectIDFromHex(subject["id"].(string))
	nowDuration := time.Duration(time.Now().Unix())

	var project models.Project
	var oldSubject models.Subject
	subjectFilter := bson.M{"_id": ID}
	err := tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&oldSubject)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		projectFilter := bson.M{"_id": oldSubject.ProjectID}
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		var cohort models.Cohort
		var env models.Environment
		envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == oldSubject.EnvironmentID
		})
		env = *envP
		//查询条件
		filter := bson.M{"customer_id": oldSubject.CustomerID, "project_id": oldSubject.ProjectID, "env_id": oldSubject.EnvironmentID}
		if oldSubject.CohortID != primitive.NilObjectID {
			filter = bson.M{"customer_id": oldSubject.CustomerID, "project_id": oldSubject.ProjectID, "env_id": oldSubject.EnvironmentID, "cohort_id": oldSubject.CohortID}

			cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.ID == oldSubject.CohortID
			})
			cohort = *cohortP
		}

		// 查询项目属性
		var attribute models.Attribute
		err = tools.Database.Collection("attribute").FindOne(sctx, filter).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if subject["isScreen"] != nil && subject["isScreen"].(bool) {
			//cohort项目仅发药,判断入组已满时，登记发过药/筛选成功之后不允许登记/筛选
			err = JustDispensingEnrollmentFull(ctx, sctx, project, attribute, filter, env, cohort, 1)
			if err != nil {
				return "", err
			}
		}

		if attribute.AttributeInfo.SubjectNumberRule == 2 || attribute.AttributeInfo.SubjectNumberRule == 3 {
			subject["shortname"] = oldSubject.Info[0].Value
		}

		// 受试者号校验// 针对环境或者cohort
		checkCondition := bson.M{
			"customer_id": oldSubject.CustomerID,
			"project_id":  oldSubject.ProjectID,
			"env_id":      oldSubject.EnvironmentID,
			"_id":         bson.M{"$ne": oldSubject.ID},
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": subject["shortname"],
				},
			},
			"deleted": bson.M{"$ne": true},
		}
		if project.ProjectInfo.Type == 3 {
			checkCondition["cohort_id"] = oldSubject.CohortID
		}
		if count, _ := tools.Database.Collection("subject").CountDocuments(sctx, checkCondition); count > 0 {
			return nil, tools.BuildServerError(ctx, "subject_number_repeat")
		}

		var form models.Form
		_ = tools.Database.Collection("form").FindOne(sctx, filter).Decode(&form)

		randomFilter := bson.M{
			"customer_id": oldSubject.CustomerID,
			"project_id":  oldSubject.ProjectID,
			"env_id":      oldSubject.EnvironmentID,
			"status":      1,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": oldSubject.ProjectSiteID},
			}}
		if oldSubject.CohortID != primitive.NilObjectID {
			randomFilter = bson.M{
				"customer_id": oldSubject.CustomerID,
				"project_id":  oldSubject.ProjectID,
				"env_id":      oldSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   oldSubject.CohortID,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": oldSubject.ProjectSiteID},
				}}
		}
		var lastSubject models.Subject
		if project.Type == 3 && subject["lastId"] != nil {
			lastOID, _ := primitive.ObjectIDFromHex(subject["lastId"].(string))
			if lastOID != primitive.NilObjectID {
				subjectFilter := bson.M{
					"customer_id": oldSubject.CustomerID,
					"project_id":  oldSubject.ProjectID,
					"env_id":      oldSubject.EnvironmentID,
					"cohort_id":   lastOID,
					"info": bson.M{
						"$elemMatch": bson.M{
							"name":  "shortname",
							"value": subject["shortname"],
						},
					},
					"$or": bson.A{
						bson.M{"site_ids": nil},
						bson.M{"site_ids": oldSubject.ProjectSiteID},
					},
					"deleted": bson.M{"$ne": true},
				}
				err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&lastSubject)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				if lastSubject.Group == "" {
					return nil, tools.BuildServerError(ctx, "subject_cohort_last_group")
				}
				randomFilter = bson.M{
					"customer_id": oldSubject.CustomerID,
					"project_id":  oldSubject.ProjectID,
					"env_id":      oldSubject.EnvironmentID,
					"status":      1,
					"cohort_id":   oldSubject.CohortID,
					"last_group":  lastSubject.Group,
					"$or": bson.A{
						bson.M{"site_ids": nil},
						bson.M{"site_ids": oldSubject.ProjectSiteID},
					},
				}
			}
		}

		var randomList models.RandomList
		err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter).Decode(&randomList)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 查询访视周期
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 下面的代码逻辑是防止分层字段没有存在数据库里会导致修改不成功
		var subjectInfoData = oldSubject.Info
		for _, factor := range randomList.Design.Factors {
			var bl = false
			for _, info := range subjectInfoData {
				if info.Name == factor.Name {
					bl = true
					break
				}
			}
			// 添加未写在数据库字段里的分层因素
			if !bl {
				subjectInfoData = append(subjectInfoData, models.Info{
					Name:  factor.Name,
					Value: "",
				})
			}
		}
		for _, field := range form.Fields {
			_, ok := slice.Find(subjectInfoData, func(index int, item models.Info) bool {
				return item.Name == field.Name
			})
			if !ok {
				subjectInfoData = append(subjectInfoData, models.Info{
					Name:  field.Name,
					Value: subject[field.Name],
				})
			}
		}

		var updateForm models.Form
		var fields []models.Field // 记录可以修改的字段
		//fields = append(fields, attribute.AttributeInfo.Field) // 受试者号
		form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
			return item.ApplicationType == nil || *item.ApplicationType == 1 || *item.ApplicationType == 4
		})
		for _, field := range form.Fields {
			infoNames := slice.Map(subjectInfoData, func(index int, item models.Info) string {
				return item.Name
			})
			if field.Status != nil && *field.Status == 2 && slice.Contain(infoNames, field.Name) {
				continue
			}
			fields = append(fields, field) // 可修改的字段
		}

		// factorSign为false时表示需要修改分层因素
		factorSign := LayeredBl(attribute, visitCycle, randomList.Design.Factors)
		atRandomFactors := false
		// TODO 在随机 不是第一阶段修改没有分层
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) && env.Cohorts[0].ID != oldSubject.CohortID {
			atRandomFactors = true
		}

		// 在随机第二阶段也不修改分层因素
		if !factorSign && !atRandomFactors {
			for _, ft := range randomList.Design.Factors {
				if ft.Status == nil || *ft.Status == 1 {
					if ft.IsCalc && oldSubject.Group == "" {
						fields, err = factorCalc(fields, ft, subject, ctx)
						if err != nil {
							return nil, err
						}
					} else if ft.IsCalc && oldSubject.Group != "" {
						for _, info := range subjectInfoData {
							if info.Name == ft.Name {
								subject[ft.Name] = info.Value
							}
						}
					} else {
						if subject[ft.Name] != nil {
							fields = append(fields, models.Field{
								Name:    ft.Name,
								Label:   ft.Label,
								Type:    ft.Type,
								Options: ft.Options,
								Status:  ft.Status,
							})
						}
					}
				}
			}
		} else {
			for _, ft := range randomList.Design.Factors {
				if ft.IsCalc && oldSubject.Group != "" {
					for _, info := range subjectInfoData {
						if info.Name == ft.Name {
							subject[ft.Name] = info.Value
						}
					}
				}
			}
		}
		updateForm.Fields = fields

		// 数据整合
		var subjectInfo []models.Info
		for _, info := range subjectInfoData {
			if subject[info.Name] != nil {
				subjectInfo = append(subjectInfo, models.Info{
					Name:  info.Name,
					Value: subject[info.Name],
				})
			}
		}

		needDeleteIndex := oldSubject.Info[0].Value != subjectInfo[0].Value
		update := bson.M{"info": subjectInfo, "last_group": lastSubject.Group, "register_random_id": randomList.ID}
		if subject["isScreen"] != nil {
			isScreen := subject["isScreen"].(bool)
			update["is_screen"] = isScreen
			if oldSubject.Status == 7 && !isScreen {
				update["status"] = 8
			} else if oldSubject.Status == 8 && isScreen {
				update["status"] = 7
			}
		} else {
			if oldSubject.IsScreen != nil {
				subject["isScreen"] = *oldSubject.IsScreen
			}
		}
		screenTime, screenTimeExists := subject["screenTime"]
		if screenTimeExists {
			update["screen_time"] = screenTime
		}
		icfTime, icfTimeExists := subject["icfTime"]
		if icfTimeExists {
			update["icf_time"] = icfTime
		}
		signOutRealTime, signOutRealTimeExists := subject["signOutRealTime"]
		if signOutRealTimeExists {
			update["sign_out_real_time"] = signOutRealTime
		}
		signOutReason, signOutReasonExists := subject["reason"]
		if signOutReasonExists {
			update["sign_out_reason"] = signOutReason
		}
		finishRemark, finishRemarkExists := subject["finishRemark"].(string)
		if finishRemarkExists {
			update["finish_remark"] = finishRemark
		}
		subjectUpdate := bson.M{"$set": update}
		opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
		var newSubject models.Subject
		err = tools.Database.Collection("subject").FindOneAndUpdate(sctx, bson.M{"_id": ID}, subjectUpdate, opts).Decode(&newSubject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if needDeleteIndex {
			//需要把唯一索引删了，不然删除受试者后重新登记会有问题
			_, err = tools.Database.Collection("subject_shortname").DeleteOne(sctx, bson.M{
				"env_id":    oldSubject.EnvironmentID,
				"cohort_id": oldSubject.CohortID,
				"shortname": oldSubject.Info[0].Value,
			})
			if err != nil {
				return nil, err
			}
		}
		// 添加轨迹
		_, err = SubjectTrail(sctx, 2, updateForm, subjectInfo, subject, user, ID, project, nowDuration, attribute)
		if err != nil {
			return nil, err
		}

		//判断是否需要发送邮件
		var updateNoticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": oldSubject.EnvironmentID, "key": "notice.subject.update"}).Decode(&updateNoticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, err
		}
		needUpdateSendMail := false
		if !updateNoticeConfig.ID.IsZero() {
			//只要有一个表单分层不一样就发送邮件
			if slice.Contain(updateNoticeConfig.State, "subject.update.form_factor") {
				for _, info := range subjectInfo {
					if info.Name == "shortname" {
						continue
					}
					find, b := slice.Find(oldSubject.Info, func(index int, item models.Info) bool {
						return info.Name == item.Name
					})
					if b {
						a, ok := info.Value.(primitive.A)
						if ok {
							if len(a) != len(find.Value.([]interface{})) {
								needUpdateSendMail = true
								break
							} else {
								for i, v := range a {
									if v != find.Value.([]interface{})[i] {
										needUpdateSendMail = true
										break
									}
								}
							}
						}
						if !ok && info.Value != find.Value {
							needUpdateSendMail = true
							break
						}
					} else {
						needUpdateSendMail = true
						break
					}
				}
			}
			if slice.Contain(updateNoticeConfig.State, "subject.update.screen") {
				if oldSubject.IsScreen != nil && subject["isScreen"] != nil && *oldSubject.IsScreen != subject["isScreen"].(bool) {
					needUpdateSendMail = true
				} else if oldSubject.IsScreen != nil && subject["isScreen"] == nil {
					needUpdateSendMail = true
				} else if oldSubject.IsScreen == nil && subject["isScreen"] != nil {
					needUpdateSendMail = true
				}
				if screenTimeExists && screenTime != oldSubject.ScreenTime {
					needUpdateSendMail = true
				}
				if icfTimeExists && icfTime != oldSubject.ICFTime {
					needUpdateSendMail = true
				}
			}
			if slice.Contain(updateNoticeConfig.State, "subject.update.stop") {
				if signOutRealTimeExists && signOutRealTime != oldSubject.SignOutRealTime {
					needUpdateSendMail = true
				}
				if signOutReasonExists && signOutReason != oldSubject.SignOutReason {
					needUpdateSendMail = true
				}
			}
			if slice.Contain(updateNoticeConfig.State, "subject.update.finish") {
				if finishRemarkExists && finishRemark != oldSubject.FinishRemark {
					needUpdateSendMail = true
				}
			}
			if slice.Contain(updateNoticeConfig.State, "subject.update.shortname") {
				for _, info := range oldSubject.Info {
					if info.Name == "shortname" {
						find, b := slice.Find(subjectInfo, func(index int, item models.Info) bool {
							return info.Name == item.Name
						})
						if b {
							if info.Value != find.Value {
								needUpdateSendMail = true
								break
							}
						}
						break
					}
				}
			}
		}
		var screenNoticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": oldSubject.EnvironmentID, "key": "notice.subject.screen"}).Decode(&screenNoticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, err
		}
		needScreenSendMail := false
		if !screenNoticeConfig.ID.IsZero() && subject["isScreen"] != nil {
			isScreen := subject["isScreen"].(bool)
			if slice.Contain(screenNoticeConfig.State, "subject.screen.success") && isScreen && (oldSubject.IsScreen == nil || *oldSubject.IsScreen != isScreen) {
				needScreenSendMail = true
			}
			if slice.Contain(screenNoticeConfig.State, "subject.screen.fail") && !isScreen && (oldSubject.IsScreen == nil || *oldSubject.IsScreen != isScreen) {
				needScreenSendMail = true
			}
		}

		//修改部分邮件
		alertUserMail, err := tools.GetRoleUsersMailWithRole(oldSubject.ProjectID, oldSubject.EnvironmentID, "notice.subject.alert.threshold", oldSubject.ProjectSiteID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if needUpdateSendMail || needScreenSendMail || len(alertUserMail) > 0 {
			//发送邮件
			updateUserMail, err := tools.GetRoleUsersMailWithRole(oldSubject.ProjectID, oldSubject.EnvironmentID, "notice.subject.update", oldSubject.ProjectSiteID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			screenUserMail, err := tools.GetRoleUsersMailWithRole(oldSubject.ProjectID, oldSubject.EnvironmentID, "notice.subject.screen", oldSubject.ProjectSiteID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(updateUserMail) > 0 || len(screenUserMail) > 0 || len(alertUserMail) > 0 {
				//environment
				var environment models.Environment
				for _, env := range project.Environments {
					if env.ID == oldSubject.EnvironmentID {
						environment = env
						break
					}
				}
				// site
				var projectSite models.ProjectSite
				err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": oldSubject.ProjectSiteID}).Decode(&projectSite)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				var mails []models.Mail
				if needUpdateSendMail && len(updateUserMail) > 0 {
					blind := attribute.AttributeInfo.Blind
					var forms []map[string]interface{}

					repSubject := make(map[string]interface{}, 0)
					oldFSubject := make(map[string]interface{}, 0)
					for _, v := range subjectInfo {
						repSubject[v.Name] = v.Value
					}

					for _, info := range oldSubject.Info {
						oldFSubject[info.Name] = info.Value
					}
					infoNames := slice.Map(subjectInfo, func(index int, item models.Info) string {
						return item.Name
					})
					updateForm.Fields = slice.Filter(updateForm.Fields, func(index int, item models.Field) bool {
						return (slice.Contain(infoNames, item.Name) && subject[item.Name] != nil) || item.Status == nil || *item.Status == 1
					})
					for i := 0; i < len(updateForm.Fields); i++ {
						if updateForm.Fields[i].IsCalc == true {
							otherValue := "-"
							oldValue := "-"
							option, b := slice.Find(updateForm.Fields[i].Options, func(index int, item models.Option) bool {
								return item.Value == oldFSubject[updateForm.Fields[i].Name]
							})
							if b {
								oldValue = option.Label
							}
							option, b = slice.Find(updateForm.Fields[i].Options, func(index int, item models.Option) bool {
								return item.Value == subject[updateForm.Fields[i].Name]
							})
							if b {
								otherValue = option.Label
							}
							forms = append(forms, map[string]interface{}{"field": updateForm.Fields[i].Label, "valueNew": otherValue, "valueOld": oldValue})
						} else {
							otherValue := "-"
							oldValue := "-"
							if updateForm.Fields[i].Type == "select" || updateForm.Fields[i].Type == "radio" { // 下拉框或者单选框
								if repSubject[updateForm.Fields[i].Name] != nil || oldFSubject[updateForm.Fields[i].Name] != nil {
									for _, option := range updateForm.Fields[i].Options {
										if repSubject[updateForm.Fields[i].Name] != nil {
											if option.Value == repSubject[updateForm.Fields[i].Name].(string) {
												otherValue = option.Label
											}
										}
										if oldFSubject[updateForm.Fields[i].Name] != nil {
											if option.Value == oldFSubject[updateForm.Fields[i].Name].(string) {
												oldValue = option.Label
											}
										}

									}
								}
							} else if updateForm.Fields[i].Type == "checkbox" { // 多选框
								if repSubject[updateForm.Fields[i].Name] != nil || oldFSubject[updateForm.Fields[i].Name] != nil {
									var checkboxBf bytes.Buffer
									str := repSubject[updateForm.Fields[i].Name].([]interface{})
									var oldCheckboxBf bytes.Buffer
									oldStr := primitive.A{}
									if oldFSubject[updateForm.Fields[i].Name] != nil {
										oldStr = oldFSubject[updateForm.Fields[i].Name].(primitive.A)
									}
									for _, option := range updateForm.Fields[i].Options {
										for j := 0; j < len(str); j++ {
											if option.Value == str[j].(string) {
												checkboxBf.WriteString(option.Label)
												if j < len(str)-1 {
													checkboxBf.WriteString(",")
												}
											}
										}
										for j := 0; j < len(oldStr); j++ {
											if option.Value == oldStr[j].(string) {
												oldCheckboxBf.WriteString(option.Label)
												if j < len(oldStr)-1 {
													oldCheckboxBf.WriteString(",")
												}
											}
										}
									}
									otherValue = checkboxBf.String()
									oldValue = oldCheckboxBf.String()
								}
							} else if updateForm.Fields[i].Type == "switch" { // 开关
								otherValue = "no"
								oldValue = "no"
								if repSubject[updateForm.Fields[i].Name] != nil {
									if repSubject[updateForm.Fields[i].Name] == true {
										otherValue = "yes"
									}
								}
								if oldFSubject[updateForm.Fields[i].Name] != nil {
									if oldFSubject[updateForm.Fields[i].Name] == true {
										oldValue = "yes"
									}
								}
							} else if updateForm.Fields[i].Type == "inputNumber" { // 其它
								if repSubject[updateForm.Fields[i].Name] != nil {
									if updateForm.Fields[i].FormatType == "decimalLength" && updateForm.Fields[i].Length != nil {
										lengthString := strconv.FormatFloat(*updateForm.Fields[i].Length, 'f', -1, 64)
										if strings.Contains(lengthString, ".") {
											digits, _ := getFractionDigits(*updateForm.Fields[i].Length)
											str := strconv.FormatFloat(repSubject[updateForm.Fields[i].Name].(float64), 'f', digits, 64)
											otherValue = str
										} else {
											otherValue = convertor.ToString(repSubject[updateForm.Fields[i].Name])
										}
									} else {
										otherValue = convertor.ToString(repSubject[updateForm.Fields[i].Name])
									}
								}
								if oldFSubject[updateForm.Fields[i].Name] != nil {
									if updateForm.Fields[i].FormatType == "decimalLength" && updateForm.Fields[i].Length != nil {
										lengthString := strconv.FormatFloat(*updateForm.Fields[i].Length, 'f', -1, 64)
										if strings.Contains(lengthString, ".") {
											digits, _ := getFractionDigits(*updateForm.Fields[i].Length)
											str := strconv.FormatFloat(oldFSubject[updateForm.Fields[i].Name].(float64), 'f', digits, 64)
											oldValue = str
										} else {
											oldValue = convertor.ToString(oldFSubject[updateForm.Fields[i].Name])
										}
									} else {
										oldValue = convertor.ToString(oldFSubject[updateForm.Fields[i].Name])
									}
								}
							} else if updateForm.Fields[i].Type == "datePicker" { // 日期选择器
								dateFormat := "YYYY-MM-DD"
								if updateForm.Fields[i].DateFormat != "" {
									dateFormat = updateForm.Fields[i].DateFormat
								}
								if repSubject[updateForm.Fields[i].Name] != nil || oldFSubject[updateForm.Fields[i].Name] != nil {
									otherValue = fmt.Sprint(repSubject[updateForm.Fields[i].Name])
									oldValue = fmt.Sprint(oldFSubject[updateForm.Fields[i].Name])
									if repSubject[updateForm.Fields[i].Name] != nil && repSubject[updateForm.Fields[i].Name] != "" {
										parse, err := time.Parse("2006-01-02", otherValue)
										if err != nil {
											return nil, errors.WithStack(err)
										}
										otherValue = parse.Format(tools.DateFormatParse(dateFormat))
									}
									if oldFSubject[updateForm.Fields[i].Name] != nil && oldFSubject[updateForm.Fields[i].Name] != "" {
										parse, err := time.Parse("2006-01-02", oldValue)
										if err != nil {
											return nil, errors.WithStack(err)
										}
										oldValue = parse.Format(tools.DateFormatParse(dateFormat))
									}
								}
							} else if updateForm.Fields[i].Type == "timePicker" {
								timeFormat := "YYYY-MM-DD HH:mm:ss"
								if updateForm.Fields[i].TimeFormat != "" {
									timeFormat = updateForm.Fields[i].TimeFormat
								}
								if repSubject[updateForm.Fields[i].Name] != nil || oldFSubject[updateForm.Fields[i].Name] != nil {
									otherValue = fmt.Sprint(repSubject[updateForm.Fields[i].Name])
									oldValue = fmt.Sprint(oldFSubject[updateForm.Fields[i].Name])
									if repSubject[updateForm.Fields[i].Name] != nil && repSubject[updateForm.Fields[i].Name] != "" {
										parse, err := time.Parse("2006-01-02 15:04:05", otherValue)
										if err != nil {
											return nil, errors.WithStack(err)
										}
										otherValue = parse.Format(tools.DateFormatParse(timeFormat))
									}
									if oldFSubject[updateForm.Fields[i].Name] != nil && oldFSubject[updateForm.Fields[i].Name] != "" {
										parse, err := time.Parse("2006-01-02 15:04:05", oldValue)
										if err != nil {
											return nil, errors.WithStack(err)
										}
										oldValue = parse.Format(tools.DateFormatParse(timeFormat))
									}
								}
							} else {
								if repSubject[updateForm.Fields[i].Name] != nil {
									otherValue = repSubject[updateForm.Fields[i].Name].(string)
								}
								if oldFSubject[updateForm.Fields[i].Name] != nil {
									oldValue = oldFSubject[updateForm.Fields[i].Name].(string)
								}
							}
							forms = append(forms, map[string]interface{}{"field": updateForm.Fields[i].Label, "valueNew": otherValue, "valueOld": oldValue})
						}
					}
					subjectData := bson.M{
						"projectNumber": project.Number,
						"envName":       environment.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
					}

					//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

					subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
					subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

					//筛选部分
					screenShow := false
					isScreenContentData := ""
					isScreenContentDataEn := ""
					screenTimeContentData := ""
					icfTimeContentData := ""
					if attribute.AttributeInfo.IsScreen {
						if subject["isScreen"] != nil {
							screenShow = true
							isScreen := subject["isScreen"].(bool)
							isScreenContentData = tools.GetIsScreenResultTranLang("zh", isScreen)
							isScreenContentDataEn = tools.GetIsScreenResultTranLang("en", isScreen)
							screenTime, screenTimeExists := subject["screenTime"]
							if screenTimeExists {
								screenTimeContentData = screenTime.(string)
							}
							icfTime, icfTimeExists := subject["icfTime"]
							if icfTimeExists {
								icfTimeContentData = icfTime.(string)
							}
						}
					}
					//停用部分
					signOutShow := false
					signOutTime := ""
					signOutReason := ""
					if oldSubject.Status == 5 || oldSubject.Status == 4 || (oldSubject.Status == 6 && oldSubject.SignOutTime != 0) {
						signOutShow = true
						signOutRealTime, signOutRealTimeExists := subject["signOutRealTime"]
						if signOutRealTimeExists {
							signOutTime = signOutRealTime.(string)
						}
						signOutReason = subject["reason"].(string)
					}
					//停用部分
					finishRemarkShow := false
					finishRemark := ""
					if oldSubject.Status == 9 && oldSubject.FinishTime != 0 {
						finishRemarkShow = true
						finishRemark = subject["finishRemark"].(string)
					}
					randomNumber := tools.BlindData
					randomNumberShow := false
					if attribute.AttributeInfo.IsRandomNumber && slice.Contain(updateNoticeConfig.FieldsConfig, "random_number") {
						randomNumber = oldSubject.RandomNumber
						if len(randomNumber) > 0 {
							randomNumberShow = true
						}
					}
					contentData := bson.M{
						"projectNumber":    project.Number,
						"projectName":      project.Name,
						"envName":          environment.Name,
						"siteNumber":       projectSite.Number,
						"siteName":         tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":       tools.GetProjectSiteLangName(projectSite, "en"),
						"label":            subjectEmailReplaceTextZh,
						"labelEn":          subjectEmailReplaceTextEn,
						"subjectNumber":    subjectInfo[0].Value,
						"randomNumberShow": randomNumberShow,
						"randomNumber":     randomNumber,
						"group":            oldSubject.Group,
						"subGroup":         oldSubject.SubGroupName,
						"parName":          oldSubject.ParGroupName,
						"forms":            forms,
						"screenShow":       screenShow,
						"isScreen":         isScreenContentData,
						"isScreenEn":       isScreenContentDataEn,
						"screenTime":       screenTimeContentData,
						"icfTime":          icfTimeContentData,
						"signOutShow":      signOutShow,
						"signOutReason":    signOutReason,
						"signOutTime":      signOutTime,
						"finishRemarkShow": finishRemarkShow,
						"finishRemark":     finishRemark,
					}
					if !cohort.ID.IsZero() {
						contentData["cohortName"] = models.GetCohortReRandomName(cohort)
					}
					if oldSubject.SubGroupName != "" {
						var randomList models.RandomList
						oldRandomListId := oldSubject.RandomListID
						if oldSubject.RandomListID.IsZero() {
							oldRandomListId = oldSubject.RegisterRandomListID
						}
						err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": oldRandomListId}).Decode(&randomList)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						groupP, b2 := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
							return oldSubject.ParGroupName == item.ParName && oldSubject.SubGroupName == item.SubName
						})
						if b2 {
							contentData["subGroupBlind"] = groupP.Blind
						}
					}

					mailBodyContet, err := tools.MailBodyContent(nil, oldSubject.EnvironmentID, "notice.subject.update")
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for key, v := range mailBodyContet {
						contentData[key] = v
					}
					var noticeConfig models.NoticeConfig
					err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": environment.ID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
					langList := make([]string, 0)
					html := "subject_modify_zh_en.html"
					if noticeConfig.Manual != 0 {
						if noticeConfig.Manual == 1 {
							langList = append(langList, "zh")
							html = "subject_modify_zh.html"
						} else if noticeConfig.Manual == 2 {
							langList = append(langList, "en")
							html = "subject_modify_en.html"
						} else if noticeConfig.Manual == 3 {
							langList = append(langList, "zh")
							langList = append(langList, "en")
							html = "subject_modify_zh_en.html"
						}
					} else {
						langList = append(langList, ctx.GetHeader("Accept-Language"))
						if locales.Lang(ctx) == "zh" {
							html = "subject_modify_zh.html"
						} else if locales.Lang(ctx) == "en" {
							html = "subject_modify_en.html"
						}
					}
					for _, email := range updateUserMail {
						insertData := make(map[string]interface{})
						for k, v := range contentData {
							insertData[k] = v
						}
						if email.IsBlind && blind {
							if oldSubject.Group != "" || (insertData["subGroup"] != nil && insertData["subGroup"] != "") {
								insertData["group"] = tools.BlindData
							}
						} else {
							if insertData["subGroup"] != nil && insertData["subGroup"] != "" {
								insertData["group"] = insertData["parName"]
							} else {
								insertData["group"] = oldSubject.Group
							}
						}
						if insertData["subGroupBlind"] != nil && insertData["subGroupBlind"].(bool) {
							if email.IsBlind {
								insertData["subGroup"] = tools.BlindData
							} else {
								insertData["subGroup"] = oldSubject.SubGroupName
							}
						}
						var toUserMail []string
						toUserMail = append(toUserMail, email.Email)
						mails = append(mails, models.Mail{
							ID:           primitive.NewObjectID(),
							Subject:      "subject.modify.title",
							SubjectData:  subjectData,
							ContentData:  insertData,
							HTML:         html,
							To:           toUserMail,
							Lang:         ctx.GetHeader("Accept-Language"),
							LangList:     langList,
							Status:       0,
							CreatedTime:  time.Duration(time.Now().Unix()),
							ExpectedTime: time.Duration(time.Now().Unix()),
							SendTime:     time.Duration(time.Now().Unix()),
						})
					}
				}
				if needScreenSendMail && len(screenUserMail) > 0 && subject["isScreen"] != nil {
					subjectData := bson.M{
						"projectNumber": project.Number,
						"envName":       environment.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
					}

					//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
					subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
					subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
					screenTimeContentData := ""
					icfTimeContentData := ""
					isScreen := subject["isScreen"].(bool)
					isScreenContentData := tools.GetIsScreenResultTranLang("zh", isScreen)
					isScreenContentDataEn := tools.GetIsScreenResultTranLang("en", isScreen)
					screenTime, screenTimeExists := subject["screenTime"]
					if screenTimeExists {
						screenTimeContentData = screenTime.(string)
					}
					icfTime, icfTimeExists := subject["icfTime"]
					if icfTimeExists {
						icfTimeContentData = icfTime.(string)
					}
					contentData := bson.M{
						"projectNumber": project.Number,
						"projectName":   project.Name,
						"envName":       environment.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
						"label":         subjectEmailReplaceTextZh,
						"labelEn":       subjectEmailReplaceTextEn,
						"subjectNumber": subjectInfo[0].Value,
						"isScreen":      isScreenContentData,
						"isScreenEn":    isScreenContentDataEn,
						"screenTime":    screenTimeContentData,
						"icfTime":       icfTimeContentData,
					}
					if !cohort.ID.IsZero() {
						contentData["cohortName"] = models.GetCohortReRandomName(cohort)
					}
					mailBodyContet, err := tools.MailBodyContent(nil, oldSubject.EnvironmentID, "notice.subject.screen")
					if err != nil {
						return nil, errors.WithStack(err)
					}
					for key, v := range mailBodyContet {
						contentData[key] = v
					}
					var nc models.NoticeConfig
					err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": environment.ID, "key": "notice.basic.settings"}).Decode(&nc)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
					langList := make([]string, 0)
					html := "subject_screen_zh_en.html"
					if nc.Manual != 0 {
						if nc.Manual == 1 {
							langList = append(langList, "zh")
							html = "subject_screen_zh.html"
						} else if nc.Manual == 2 {
							langList = append(langList, "en")
							html = "subject_screen_en.html"
						} else if nc.Manual == 3 {
							langList = append(langList, "zh")
							langList = append(langList, "en")
							html = "subject_screen_zh_en.html"
						}
					} else {
						langList = append(langList, ctx.GetHeader("Accept-Language"))
						if locales.Lang(ctx) == "zh" {
							html = "subject_screen_zh.html"
						} else if locales.Lang(ctx) == "en" {
							html = "subject_screen_en.html"
						}
					}
					for _, email := range screenUserMail {
						insertData := make(map[string]interface{})
						for k, v := range contentData {
							insertData[k] = v
						}
						var toUserMail []string
						toUserMail = append(toUserMail, email.Email)
						mails = append(mails, models.Mail{
							ID:           primitive.NewObjectID(),
							Subject:      "subject.screen.title",
							SubjectData:  subjectData,
							ContentData:  insertData,
							HTML:         html,
							To:           toUserMail,
							Lang:         ctx.GetHeader("Accept-Language"),
							LangList:     langList,
							Status:       0,
							CreatedTime:  time.Duration(time.Now().Unix()),
							ExpectedTime: time.Duration(time.Now().Unix()),
							SendTime:     time.Duration(time.Now().Unix()),
						})
					}
				}
				var nc models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": oldSubject.EnvironmentID, "key": "notice.basic.settings"}).Decode(&nc)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				html := "subject_limit_alert_screen_zh_en.html"
				if nc.Manual != 0 {
					if nc.Manual == 1 {
						html = "subject_limit_alert_screen_zh.html"
					} else if nc.Manual == 2 {
						html = "subject_limit_alert_screen_en.html"
					} else if nc.Manual == 3 {
						html = "subject_limit_alert_screen_zh_en.html"
					}
				} else {
					if locales.Lang(ctx) == "zh" {
						html = "subject_limit_alert_screen_zh.html"
					} else if locales.Lang(ctx) == "en" {
						html = "subject_limit_alert_screen_en.html"
					}
				}
				if len(alertUserMail) > 0 && subject["isScreen"] != nil && subject["isScreen"].(bool) {
					mails, err = alertThresholds(sctx, 7, project, env, cohort, attribute, newSubject, projectSite, ctx, html, mails)
					if err != nil {
						return models.RemoteSubjectRandom{}, err
					}
				}
				ctx.Set("MAIL", mails)
				if len(mails) > 0 {
					var envs []models.MailEnv
					for _, m := range mails {
						envs = append(envs, models.MailEnv{
							ID:         primitive.NewObjectID(),
							MailID:     m.ID,
							CustomerID: oldSubject.CustomerID,
							ProjectID:  oldSubject.ID,
							EnvID:      oldSubject.EnvironmentID,
							CohortID:   oldSubject.CohortID,
						})
					}
					ctx.Set("MAIL-ENV", envs)
				}
			}
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}
	pushScenario := false
	if oldSubject.Status == 3 || oldSubject.Status == 4 || oldSubject.Status == 5 || oldSubject.Status == 6 || oldSubject.Status == 9 {
		pushScenario = project.ProjectInfo.PushScenario.UpdateRandomAfterPush
	} else {
		pushScenario = project.ProjectInfo.PushScenario.UpdateRandomFrontPush
	}
	// 推给EDC
	if tools.PushScenarioFilter(project.ProjectInfo.ConnectEdc, project.ProjectInfo.PushMode, project.ProjectInfo.EdcSupplier, pushScenario) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectRandomPush(logData, ID, 2, nowDuration, "", "", "")
	}
	//if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 2 && (!tools.IrtPushEdcRegisterIsolation(project.ProjectInfo.Number) || (tools.IrtPushEdcRegisterIsolation(project.ProjectInfo.Number) && (oldSubject.Status == 3 || oldSubject.Status == 4 || oldSubject.Status == 5 || oldSubject.Status == 6 || oldSubject.Status == 9))) {
	//	SubjectRandomPush(ctx, ID, 2, nowDuration, "", "")
	//}
	return nil

}

// Transfer 转运新的中心
func (s *SubjectService) Transfer(ctx *gin.Context) error {
	var subject map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&subject, binding.JSON)
	ID, _ := primitive.ObjectIDFromHex(subject["id"].(string))
	newSiteId, _ := primitive.ObjectIDFromHex(subject["newSite"].(string))

	u, _ := ctx.Get("user")
	user := u.(models.User)
	now := time.Duration(time.Now().Unix())

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		subjectFilter := bson.M{"_id": ID}
		var oldSubject models.Subject
		err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&oldSubject)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		//if oldSubject.Status != 1 && oldSubject.Status != 2 {
		//	return nil, tools.BuildServerError(ctx, "subject_status_no_update")
		//}

		projectFilter := bson.M{"_id": oldSubject.ProjectID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 受试者号校验// 针对环境或者cohort
		checkCondition := bson.M{
			"customer_id":     oldSubject.CustomerID,
			"project_id":      oldSubject.ProjectID,
			"env_id":          oldSubject.EnvironmentID,
			"_id":             bson.M{"$ne": oldSubject.ID},
			"project_site_id": newSiteId,
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": subject["shortname"],
				},
			},
			"deleted": bson.M{"$ne": true},
		}

		if project.ProjectInfo.Type == 3 {
			checkCondition["cohort_id"] = oldSubject.CohortID
		}
		if count, _ := tools.Database.Collection("subject").CountDocuments(sctx, checkCondition); count > 0 {
			return nil, tools.BuildServerError(ctx, "subject_transfer_fail")
		}

		// TODO 在随机
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			var subjectList []models.Subject
			var subjectIds []primitive.ObjectID
			subjectFile := bson.M{
				"customer_id": oldSubject.CustomerID,
				"project_id":  oldSubject.ProjectID,
				"env_id":      oldSubject.EnvironmentID,
				"info": bson.M{
					"$elemMatch": bson.M{
						"name":  "shortname",
						"value": subject["shortname"],
					},
				},
				"deleted": bson.M{"$ne": true},
			}
			subjectCursor, err := tools.Database.Collection("subject").Find(sctx, subjectFile)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if err = subjectCursor.All(nil, &subjectList); err != nil {
				return nil, errors.WithStack(err)
			}
			for _, subject := range subjectList {
				subject.TransferSiteInfos = append(subject.TransferSiteInfos, models.TransferSiteInfo{
					TransTime: now,
					OldSiteID: subject.ProjectSiteID,
				})
				subjectUpdate := bson.M{"$set": bson.M{
					"project_site_id":     newSiteId,
					"transfer_site_infos": subject.TransferSiteInfos,
				}}
				if _, err := tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": subject.ID}, subjectUpdate); err != nil {
					return nil, errors.WithStack(err)
				}

				subjectIds = append(subjectIds, subject.ID)
			}

			subjectUpdate := bson.M{"$set": bson.M{"project_site_id": newSiteId}}
			if _, err := tools.Database.Collection("subject").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": subjectIds}, "deleted": bson.M{"$ne": true}}, subjectUpdate); err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			oldSubject.TransferSiteInfos = append(oldSubject.TransferSiteInfos, models.TransferSiteInfo{
				TransTime: now,
				OldSiteID: oldSubject.ProjectSiteID,
			})
			subjectUpdate := bson.M{"$set": bson.M{
				"project_site_id":     newSiteId,
				"transfer_site_infos": oldSubject.TransferSiteInfos,
			}}
			if _, err := tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": ID}, subjectUpdate); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//查询中心
		var oldProjectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": oldSubject.ProjectSiteID}).Decode(&oldProjectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		var newProjectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": newSiteId}).Decode(&newProjectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 查询项目属性
		filter := bson.M{"customer_id": oldSubject.CustomerID, "project_id": oldSubject.ProjectID, "env_id": oldSubject.EnvironmentID}
		if oldSubject.CohortID != primitive.NilObjectID {
			filter = bson.M{"customer_id": oldSubject.CustomerID, "project_id": oldSubject.ProjectID, "env_id": oldSubject.EnvironmentID, "cohort_id": oldSubject.CohortID}
		}
		var attribute models.Attribute
		err = tools.Database.Collection("attribute").FindOne(sctx, filter).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// TODO 在随机
		stage := ""
		key := "history.subject.transfer"
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			key = "history.subject.at-random-transfer"
			if oldSubject.CohortID != primitive.NilObjectID {
				for _, env := range project.Environments {
					if env.ID == oldSubject.EnvironmentID {
						for _, cohort := range env.Cohorts {
							if cohort.ID == oldSubject.CohortID {
								stage = cohort.Name
								break
							}
						}
						break
					}
				}
			}
		}
		// 添加轨迹
		var histories []models.History
		history := models.History{
			Key: key,
			OID: ID,
			Data: map[string]interface{}{
				"label":     attribute.AttributeInfo.SubjectReplaceText,
				"enLabel":   attribute.AttributeInfo.SubjectReplaceTextEn,
				"subjectNo": subject["shortname"],
				"oldSiteId": oldSubject.ProjectSiteID,
				//"oldSite": fmt.Sprintf("%s-%s", oldProjectSite.Number, oldProjectSite.Name),
				//"newSite": fmt.Sprintf("%s-%s", newProjectSite.Number, newProjectSite.Name),
				"newSiteId": newSiteId,
				"stage":     stage},
			Time: now,
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	// 推给EDC
	//SubjectRandomPush(ID, 2, now)
	return nil

}

// UpdateSubjectNo 修改受试者号（EDC专用接口）
func (s *SubjectService) UpdateSubjectNo(ctx *gin.Context, subject map[string]interface{}) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		ID, _ := primitive.ObjectIDFromHex(subject["id"].(string))
		subjectFilter := bson.M{"_id": ID}
		var findSubject models.Subject
		err := tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&findSubject)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		needDeleteIndex := findSubject.Info[0].Value != subject["shortname"]

		// 判断受试者是否已随机
		if findSubject.Status != 1 && findSubject.Status != 2 {
			return nil, tools.BuildServerError(ctx, "common.operation.edc.fail")
		}

		projectFilter := bson.M{"_id": findSubject.ProjectID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		if project.ProjectInfo.Type == 3 {
			return nil, tools.BuildServerError(ctx, "edc.unable.at.random")
		}

		// 受试者号校验// 针对环境或者cohort
		checkCondition := bson.M{
			"customer_id": findSubject.CustomerID,
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"_id":         bson.M{"$ne": findSubject.ID},
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": subject["shortname"],
				},
			},
			"deleted": bson.M{"$ne": true},
		}

		var attribute models.Attribute
		attributeMatch := bson.M{"env_id": findSubject.EnvironmentID}
		if !findSubject.CohortID.IsZero() {
			attributeMatch["cohort_id"] = findSubject.CohortID
		}

		err = tools.Database.Collection("attribute").FindOne(sctx, attributeMatch).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

		if count, _ := tools.Database.Collection("subject").CountDocuments(sctx, checkCondition); count > 0 {
			return nil, tools.BuildServerError(ctx, "subject_number_repeat")
		}

		// 数据整合
		var subjectInfo []models.Info
		for _, info := range findSubject.Info {
			if info.Name == "shortname" { // 只修改受试者号
				subjectInfo = append(subjectInfo, models.Info{
					Name:  info.Name,
					Value: subject["shortname"],
				})
			} else {
				subjectInfo = append(subjectInfo, info)
			}
		}

		subjectUpdate := bson.M{"$set": bson.M{"info": subjectInfo}}
		if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": ID}, subjectUpdate); err != nil {
			return nil, errors.WithStack(err)
		}

		if needDeleteIndex {
			//需要把唯一索引删了，不然删除受试者后重新登记会有问题
			_, err = tools.Database.Collection("subject_shortname").DeleteOne(sctx, bson.M{
				"env_id":    findSubject.EnvironmentID,
				"cohort_id": findSubject.CohortID,
				"shortname": findSubject.Info[0].Value,
			})
			if err != nil {
				return nil, err
			}
		}

		// 添加轨迹
		history := models.History{
			OID:  ID,
			Key:  "history.subject.label.updateSubjectNo",
			Data: map[string]interface{}{"label": subjectReplaceText, "oldSubjectNo": subject["oldSubjectNo"], "shortname": subject["shortname"]},
			Time: time.Duration(time.Now().Unix()),
			User: subject["edcUserName"].(string),
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, history)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// Delete 删除数据
func (s *SubjectService) Delete(ctx *gin.Context, id string) error {
	oid, _ := primitive.ObjectIDFromHex(id)
	subjectFilter := bson.M{"_id": oid}
	var subject models.Subject
	nowDuration := time.Duration(time.Now().Unix())

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if subject.Status != 1 && subject.Status != 2 {
			return nil, tools.BuildServerError(ctx, "subject_status_no_delete")
		}

		me, err := tools.Me(ctx)
		if err != nil {
			return nil, err
		}
		update := bson.M{"$set": bson.M{
			"status":          10, // 状态为无效
			"deleted":         true,
			"meta.deleted_at": nowDuration,
			"meta.deleted_by": me.ID,
		}}
		if _, err := tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": oid}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		//_, err = tools.Database.Collection("subject").DeleteOne(sctx, bson.M{"_id": oid})
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}
		//需要把唯一索引删了，不然删除受试者后重新登记会有问题
		_, err = tools.Database.Collection("subject_shortname").DeleteOne(sctx, bson.M{
			"env_id":    subject.EnvironmentID,
			"cohort_id": subject.CohortID,
			"shortname": subject.Info[0].Value,
		})
		if err != nil {
			return nil, err
		}

		// 添加轨迹
		attribute := models.Attribute{}
		filter := bson.M{"env_id": subject.EnvironmentID}
		err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": subject.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		key := "history.subject.delete"
		//
		//if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		//	key = "history.subject.at-random-delete"
		//}

		history := models.History{
			Key: key,
			OID: subject.ID,
			Data: map[string]interface{}{
				"label": attribute.AttributeInfo.SubjectReplaceText,
				"name":  subject.Info[0].Value,
			},
			Time: nowDuration,
			UID:  me.ID,
			User: me.Name,
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, history)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	return nil
}

// GetList 查询数据
func (s *SubjectService) GetAppList(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, siteID string, roleID string, subjectNo string) (map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	siteOID, _ := primitive.ObjectIDFromHex(siteID)
	cohortOID := primitive.NilObjectID
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
		"deleted":     bson.M{"$ne": true},
	}

	dispatchMatch := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}

	if cohortID != "" {
		cohortOID, _ = primitive.ObjectIDFromHex(cohortID)
		match["cohort_id"] = cohortOID
		dispatchMatch["cohort_id"] = cohortOID
	}
	//查询项目
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var environment models.Environment
	for _, env := range project.Environments {
		if env.ID == envOID {
			environment = env
			break
		}
	}

	// 查询项目属性配置
	var attribute models.Attribute
	var attributes []models.Attribute
	attributeCursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	})
	err = attributeCursor.All(nil, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	match["project_site_id"] = siteOID
	if subjectNo != "" {
		match["info.value"] = bson.M{"$regex": subjectNo}
	}
	total, err := tools.Database.Collection("subject").CountDocuments(ctx, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjectData []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$sort", Value: bson.D{{"_id", -1}}}},
		{{Key: "$project", Value: bson.M{
			"_id":         1,
			"customer_id": 1,
			"project_id":  1,
			"env_id":      1,
			"cohort_id":   1,
			"shortname":   "$info.value",
			"status":      1,
			"random_time": 1,
		}}},
	}

	cursor, err := tools.Database.Collection("subject").Aggregate(ctx, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var visitCycle models.VisitCycle
	var visitCycles []models.VisitCycle
	visitCycleCursor, err := tools.Database.Collection("visit_cycle").Find(nil, bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	})
	err = visitCycleCursor.All(nil, &visitCycles)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
		}
	}

	// 查询待发药记录
	for _, subject := range subjectData {
		if project.ProjectInfo.Type == 1 {
			attribute = attributes[0]
		} else if project.ProjectInfo.Type == 2 || tools.InRandomIsolation(project.ProjectInfo.Number) {
			for _, ab := range attributes {
				if ab.CohortID == cohortOID {
					attribute = ab
					break
				}
			}
		} else { // 在随机项目查询2阶段的受试者是否存在
			var sbj models.Subject
			err := tools.Database.Collection("subject").FindOne(nil,
				bson.M{
					"cohort_id": environment.Cohorts[1].ID,
					"info": bson.M{
						"$elemMatch": bson.M{
							"name":  "shortname",
							"value": subject["shortname"],
						}},
					"deleted": bson.M{"$ne": true}}).Decode(&sbj)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			subject["cohortName"] = environment.Cohorts[0].Name // 默认为第一阶段的名称
			if sbj.Info != nil && len(sbj.Info) > 0 {
				subject["_id"] = sbj.ID
				subject["cohort_id"] = sbj.CohortID
				subject["cohortName"] = environment.Cohorts[1].Name
				subject["status"] = sbj.Status
				subject["random_time"] = convertor.ToString(sbj.RandomTime)
			}

			// 属性配置
			for _, ab := range attributes {
				if ab.CohortID == subject["cohort_id"].(primitive.ObjectID) {
					attribute = ab
					break
				}
			}
		}

		if attribute.AttributeInfo.Dispensing {
			if project.ProjectInfo.Type == 1 {
				visitCycle = visitCycles[0]
			} else {
				chOID := subject["cohort_id"].(primitive.ObjectID)
				for _, vc := range visitCycles {
					if vc.CohortID == chOID {
						visitCycle = vc
						break
					}
				}
			}

			strTimeZone, err := tools.GetSiteTimeZone(siteOID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if strTimeZone == "" {
				zone, err := tools.GetTimeZone(projectOID)
				if err != nil {
					return nil, err
				}
				//strTimeZone = fmt.Sprintf("UTC%+d", zone)
				strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
			}

			//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
			intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
			hours := time.Duration(intTimeZone)
			minutes := time.Duration((intTimeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute

			var dispensingDatas []models.Dispensing
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"subject_id": subject["_id"], "visit_sign": false}}},
				{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
			}
			cursor, err := tools.Database.Collection("dispensing").Aggregate(ctx, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &dispensingDatas)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			visitName := ""
			//基准时间
			lineTime := int64(0)
			firstTime := time.Duration(0)
			//最后一次发药的访视
			lastDispensingVisit := ""
			if len(dispensingDatas) > 0 {
				for _, dispensing := range dispensingDatas {
					if visitCycle.VisitType != 0 && dispensing.VisitInfo.Dispensing && dispensing.Status == 2 { //lastDate 最后一次的发药时间
						lineTime = int64(dispensing.DispensingTime)
						lastDispensingVisit = dispensing.VisitInfo.Number
					}
					if dispensing.Status != 1 {
						visitCycleInfoP, _ := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
							return item.Number == dispensing.VisitInfo.Number
						})
						visitCycleInfo := *visitCycleInfoP
						randomTime, err := convertor.ToInt(subject["random_time"])
						if err != nil {
							return nil, errors.WithStack(err)
						}
						if firstTime == 0 && dispensing.DispensingTime != 0 && visitCycleInfo.Interval != nil && randomTime == 0 {
							firstTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfo.Interval)).Unix())
						}
					}
					if dispensing.Status == 1 { //未发药数据
						visitInfo := dispensing.VisitInfo
						visitName = visitInfo.Name
						break
					}
				}
			}

			if visitCycle.VisitType == 0 { //Baseline
				// 判断状态是否已随机，以随机时间为基线
				var lineTime int64
				if project.ProjectInfo.Type == 3 && subject["cohort_id"].(primitive.ObjectID) == environment.Cohorts[1].ID {
					lineTime, _ = strconv.ParseInt(subject["random_time"].(string), 10, 64)
				} else {
					lineTime = subject["random_time"].(int64)
				}
				// TODO 7064
				if subjectMap[subject["shortname"].(string)].RandomTime != 0 {
					lineTime = int64(subjectMap[subject["shortname"].(string)].RandomTime)
				}
				if firstTime != 0 {
					lineTime = int64(firstTime)
				}

				if lineTime > 0 {
					for _, info := range visitCycle.Infos {
						// 当前受试者是否有这个访视，是否发过药,是否当前访视随机
						_, ok := slice.Find(dispensingDatas, func(index int, item models.Dispensing) bool {
							return info.Number == item.VisitInfo.Number && item.Status == 1 && (item.VisitInfo.Dispensing || (item.VisitInfo.Random && subject["random_time"].(int64) <= 0))
						})
						if ok {
							if info.Interval != nil {
								// 非小时制的转成小时
								Unit, Interval, _ := tools.ReturnUnitIntervalPeriod(info.Unit, info.Interval, nil)
								converTimes := tools.ConvertTime(Unit, Interval, 0)
								//lineTime = lineTime + int64(converTimes*3600)
								standardTime := time.Unix(int64(lineTime), 0).Add(time.Hour * time.Duration(converTimes)).Add(duration)
								subject["lineTime"] = standardTime.UTC().Format("2006-01-02")
							}
							//有随机时间了，访视才展示
							subject["visitName"] = info.Name
							break
						}
					}
				}
			} else { //LastDate
				if lineTime <= 0 && visitName != "" { // 受试者没有发过药，并且存在没有发过药的主访视
					continue
				}
				// 是否是最后一次发药之后的访视
				afterLastDispensing := false
				intervals := float64(0)
				// 以发药时间为基线
				for _, info := range visitCycle.Infos {
					// 当前受试者是否有这个访视，是否发过药
					_, ok := slice.Find(dispensingDatas, func(index int, item models.Dispensing) bool {
						if info.Number == lastDispensingVisit {
							afterLastDispensing = true
						}
						if afterLastDispensing && info.Number == item.VisitInfo.Number && item.Status == 3 { // 最后一次发药之后，有访视跳过，不参加访视，访视需要加上间隔时间
							// 非小时制的转成小时
							Unit, Interval, _ := tools.ReturnUnitIntervalPeriod(info.Unit, info.Interval, nil)
							converTimes := tools.ConvertTime(Unit, Interval, 0)
							intervals = intervals + converTimes
						}
						return info.Number == item.VisitInfo.Number && item.Status == 1 && (item.VisitInfo.Dispensing || (item.VisitInfo.Random && subject["random_time"].(int64) <= 0))
					})
					if ok {
						if info.Interval != nil {
							// 非小时制的转成小时
							Unit, Interval, _ := tools.ReturnUnitIntervalPeriod(info.Unit, info.Interval, nil)
							converTimes := tools.ConvertTime(Unit, Interval, 0)
							standardTime := time.Unix(int64(lineTime), 0).Add(time.Hour * time.Duration(converTimes+intervals)).Add(duration)
							//lineTime = lineTime + int64(converTimes*3600)
							subject["lineTime"] = standardTime.UTC().Format("2006-01-02")
						}
						//有随机时间了，访视才展示
						subject["visitName"] = info.Name
						break
					}
				}
			}
		}
	}
	return map[string]interface{}{"total": total, "items": subjectData, "attribute": attributes[0]}, nil
}

// GetList 查询数据
func (s *SubjectService) GetListCount(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string) (int, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "deleted": bson.M{"$ne": true}}
	if project.Type != 1 {
		ids, exist := models.GetCohortIds(project, envID)
		if exist {
			filter["cohort_id"] = bson.M{"$in": ids}
		}
	}
	documents, err := tools.Database.Collection("subject").CountDocuments(ctx, filter)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return int(documents), nil
}

// GetSubject 查询单个受试者
func (s *SubjectService) GetSubject(ctx *gin.Context, id string) (models.Subject, error) {
	ID, _ := primitive.ObjectIDFromHex(id)
	subjectFilter := bson.M{"_id": ID}
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&subject)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.Subject{}, errors.WithStack(err)
	}
	if subject.ParGroupName == "" {
		subject.ParGroupName = subject.Group
	}

	// 查询项目
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
	if err != nil {
		return models.Subject{}, errors.WithStack(err)
	}
	// TODO 在随机
	if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		subjectNo := ""
		var parGroupName strings.Builder
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				subjectNo = info.Value.(string)
			}
		}
		for _, env := range project.Environments {
			if env.ID == subject.EnvironmentID {
				for _, cohort := range env.Cohorts {
					subjectFilter := bson.M{
						"customer_id": subject.CustomerID,
						"project_id":  subject.ProjectID,
						"env_id":      subject.EnvironmentID,
						"cohort_id":   cohort.ID,
						"info": bson.M{
							"$elemMatch": bson.M{
								"name":  "shortname",
								"value": subjectNo,
							},
						},
						"deleted": bson.M{"$ne": true},
					}
					var atSubject models.Subject
					err = tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&atSubject)
					if err != nil && err != mongo.ErrNoDocuments {
						return models.Subject{}, errors.WithStack(err)
					}
					if atSubject.Group != "" || atSubject.ParGroupName != "" {
						if atSubject.ParGroupName == "" {
							parGroupName.WriteString(cohort.Name)
							parGroupName.WriteString("/")
							parGroupName.WriteString(atSubject.Group)
							parGroupName.WriteString(" ")
						} else {
							parGroupName.WriteString(cohort.Name)
							parGroupName.WriteString("/")
							parGroupName.WriteString(atSubject.ParGroupName)
							parGroupName.WriteString(" ")
						}
					}
				}
			}
		}
		subject.ParGroupName = parGroupName.String()
	}

	// Attribute
	filter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
	if subject.CohortID != primitive.NilObjectID {
		filter = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.Subject{}, errors.WithStack(err)
	}

	if !attribute.AttributeInfo.IsRandomNumber {
		subject.RandomNumber = ""
	}

	return subject, nil
}

// SubjectRandom 受试者随机
func (s *SubjectService) SubjectRandom(ctx *gin.Context, id string, sign int, edcUserName string, roleId string, ssctx mongo.SessionContext) (models.RemoteSubjectRandom, error) {
	var remoteSubjectRandom models.RemoteSubjectRandom
	// subject
	var subject models.Subject
	ID, _ := primitive.ObjectIDFromHex(id)
	// 数据操作
	var mails []models.Mail
	now := time.Now()
	nowDuration := time.Duration(now.Unix())
	var randomList models.RandomList
	var project models.Project
	var env models.Environment
	var cohort models.Cohort
	var attribute models.Attribute
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if ssctx != nil {
			sctx = ssctx
		}
		subjectFilter := bson.M{"_id": ID}
		err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&subject)
		if err != nil && err != mongo.ErrNoDocuments {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		if subject.Status != 1 && subject.Status != 2 && subject.Status != 7 {
			return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "subject_status_no_random")
		}
		// dispensing
		var dispensingArray []models.Dispensing
		opts := &options.FindOptions{
			Sort: bson.D{{"serial_number", 1}},
		}
		cursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"subject_id": subject.ID}, opts)
		if err != nil {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		err = cursor.All(nil, &dispensingArray)
		if err != nil {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		for _, dispensing := range dispensingArray {
			if dispensing.VisitInfo.Random { // 判断是否随机
				break
			}
			if dispensing.VisitInfo.Dispensing { // 判断是否发药
				if dispensing.Status == 1 {
					return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "subject_random_error")
				}
			}
		}

		// project
		projectFilter := bson.M{"_id": subject.ProjectID}
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		//env
		for _, environment := range project.Environments {
			if environment.ID == subject.EnvironmentID {
				env = environment
				break
			}
		}
		if project.ProjectInfo.Type != 1 {
			cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.ID == subject.CohortID
			})
			cohort = *cohortP
		}
		//校验入组上限
		needCapacity := false
		alertThresholdP := &models.AlertThreshold{}
		if project.ProjectInfo.Type == 1 {
			alertThresholdP, needCapacity = slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				return item.Type == 3
			})
		} else {
			alertThresholdP, needCapacity = slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				return item.Type == 3
			})
		}
		if needCapacity {
			countFilter := bson.M{
				"customer_id": subject.CustomerID,
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"group":       bson.M{"$ne": ""},
				"status":      bson.M{"$in": bson.A{3, 4, 6, 9}},
			}
			if subject.CohortID != primitive.NilObjectID {
				countFilter["cohort_id"] = subject.CohortID
			}
			count, err := tools.Database.Collection("subject").CountDocuments(sctx, countFilter)
			if err != nil {
				return models.RemoteSubjectRandom{}, errors.WithStack(err)
			}
			plannedCase := int64(alertThresholdP.Capacity)
			if count >= plannedCase {
				return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "planned_case_random_error")
			}
		}

		// site
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		//siteName := models.GetProjectSiteName(ctx, projectSite)
		user := models.User{}
		u, _ := ctx.Get("user")
		if u != nil {
			user = u.(models.User)
		}

		// cohort项目或者在随机项目
		if project.ProjectInfo.Type == 2 || project.ProjectInfo.Type == 3 {
			if cohort.Status == 1 || cohort.Status == 3 || cohort.Status == 4 || cohort.Status == 5 {
				return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "subject_factor_check_error")
			}
		} else {
			if *env.Status == 1 || *env.Status == 3 || *env.Status == 4 || *env.Status == 5 {
				return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "subject_factor_check_error")
			}
		}
		// Attribute
		filter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
		if subject.CohortID != primitive.NilObjectID {
			filter = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
		}
		err = tools.Database.Collection("attribute").FindOne(sctx, filter).Decode(&attribute)
		if err != nil && err != mongo.ErrNoDocuments {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}

		// RandomDesign
		var randomDesign models.RandomDesign
		err = tools.Database.Collection("random_design").FindOne(sctx, filter).Decode(&randomDesign)
		if err != nil && err != mongo.ErrNoDocuments {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		randomDesign.Info.Groups = slice.Filter(randomDesign.Info.Groups, func(index int, item models.RandomGroup) bool {
			return item.Status == nil || *item.Status == 1
		})
		// VisitCycle
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
		if err != nil && err != mongo.ErrNoDocuments {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}

		// RandomList
		randomFilter := bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"status":      1,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": subject.ProjectSiteID},
			},
		}
		if subject.CohortID != primitive.NilObjectID {
			randomFilter = bson.M{
				"customer_id": subject.CustomerID,
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"status":      1,
				"cohort_id":   subject.CohortID,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": subject.ProjectSiteID},
				},
			}
		}
		if project.ProjectInfo.Type == 3 && subject.LastGroup != "" { // 再随机逻辑
			randomFilter = bson.M{
				"customer_id": subject.CustomerID,
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"status":      1,
				"cohort_id":   subject.CohortID,
				"last_group":  subject.LastGroup,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": subject.ProjectSiteID},
				},
			}
		}

		var randomLists []models.RandomList
		cursor, err = tools.Database.Collection("random_list").Aggregate(sctx, mongo.Pipeline{
			{{Key: "$match", Value: randomFilter}},
			{{Key: "$lookup", Value: bson.M{
				"from": "random_number",
				"let": bson.M{
					"random_list_id": "$_id",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{
						"$expr":  bson.M{"$eq": bson.A{"$random_list_id", "$$random_list_id"}},
						"status": 1,
					}}},
					{{Key: "$group", Value: bson.M{"_id": "$random_list_id", "count": bson.M{"$sum": 1}}}},
				},
				"as": "random_numbers",
			}}},
			{{Key: "$match", Value: bson.M{"random_numbers": bson.M{"$size": 1}}}},
		})
		if err != nil {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		err = cursor.All(nil, &randomLists)
		if err != nil {
			return models.RemoteSubjectRandom{}, errors.WithStack(err)
		}
		if len(randomLists) == 0 {
			return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "subject_no_random")
		}
		//多随机表校验分层

		for _, list := range randomLists {
			listFactors := list.Design.Factors
			listFactors = slice.Filter(listFactors, func(index int, item models.RandomFactor) bool {
				if item.Status == nil {
					return true
				}
				return *item.Status != 2
			})
			for _, factor := range listFactors {
				_, ok := slice.Find(subject.Info, func(index int, item models.Info) bool {
					return item.Name == factor.Name && item.Value != nil
				})
				if !ok {
					return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "subject_random_factor_error")
				}
			}
		}

		//如果有多张随机表则校验组别 再随机不校验，再随机的逻辑允许治疗组别和随机列表中的不一致
		if project.ProjectInfo.Type != 3 {
			groups := randomDesign.Info.Groups
			groupLen := 0
			for _, group := range groups {
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					groupLen = groupLen + len(group.SubGroup)
				} else {
					groupLen++
				}
			}
			for _, list := range randomLists {
				list.Design.Groups = slice.Filter(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
					return item.Status == nil || *item.Status == 1
				})
				if list.Design.Type != randomDesign.Info.Type {
					return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_type_error")
				}
				if len(list.Design.Groups) != groupLen {
					return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_group_error")
				}
				for _, group := range groups {
					if group.SubGroup != nil && len(group.SubGroup) > 0 {
						for _, subGroup := range group.SubGroup {
							_, b := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
								return item.ParName == group.Name && item.SubName == subGroup.Name
							})
							if !b {
								return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_group_error")
							}
						}
					} else {
						_, b := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
							return item.Name == group.Name
						})
						if !b {
							return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_group_error")
						}
					}
				}
			}
		}

		//校验分层因素
		if randomDesign.Info.Type == 2 {
			factors := randomLists[0].Design.Factors
			factors = slice.Filter(factors, func(index int, item models.RandomFactor) bool {
				if item.Status == nil {
					return true
				}
				return *item.Status != 2
			})
			for _, list := range randomLists {
				listFactors := list.Design.Factors
				listFactors = slice.Filter(listFactors, func(index int, item models.RandomFactor) bool {
					if item.Status == nil {
						return true
					}
					return *item.Status != 2
				})
				if len(listFactors) != len(factors) {
					return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_factor_error")
				}
				for i := 0; i < len(factors); i++ {
					if factors[i].Name != listFactors[i].Name || factors[i].Label != listFactors[i].Label {
						return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_factor_error")
					}
					if len(factors[i].Options) != len(listFactors[i].Options) {
						return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_factor_error")
					}
					for j := 0; j < len(factors[i].Options); j++ {
						if factors[i].Options[j].Label != listFactors[i].Options[j].Label ||
							factors[i].Options[j].Value != listFactors[i].Options[j].Value {
							return models.RemoteSubjectRandom{}, tools.BuildServerError(ctx, "random_factor_error")
						}
					}
				}
			}
		}

		var form models.Form
		_ = tools.Database.Collection("form").FindOne(sctx, filter).Decode(&form)

		// 随机号提取
		err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&subject)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if subject.Status != 1 && subject.Status != 2 && subject.Status != 7 {
			return nil, tools.BuildServerError(ctx, "subject_status_no_random")
		}
		// form
		var fields []models.Field
		fields = append(fields, attribute.AttributeInfo.Field)
		if form.Fields != nil {
			for _, fm := range form.Fields {
				if fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4 {
					fields = append(fields, fm)
				}
			}
		}
		// 转换受试者数据
		if nil != randomLists[0].Design.Factors {
			subjectInfo := make(map[string]interface{})
			for _, info := range subject.Info {
				subjectInfo[info.Name] = info.Value
			}
		}
		form.Fields = fields
		var result models.RandomNumberReturn
		estimateBl := false
		for _, list := range randomLists {
			randomList = list
			// 检查是否达到预计录入人数
			estimateBl, _, err = CheckSubject(sctx, randomList, subject, 0)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if estimateBl {
				continue
			}
			// 随机入组
			result, err = extractRandomNumber(sctx, ctx, subject, projectSite, attribute, randomDesign, randomList, nil, project.Number)
			if err != nil {
				return nil, err
			}
			if result.RandomNumber == "" {
				continue
			} else {
				break
			}
		}
		if estimateBl {
			return nil, tools.BuildServerError(ctx, "subject_factor_check_error")
		}
		if result.RandomNumber == "" {
			return nil, tools.BuildServerError(ctx, "subject_no_random")
		}

		//检查随机控制打开的时候
		randomControl := attribute.AttributeInfo.RandomControl
		randomControlRule := attribute.AttributeInfo.RandomControlRule
		randomControlGroup := attribute.AttributeInfo.RandomControlGroup

		if randomControl { //判断研究产品配置和研究中心药物
			newGroups := make([]string, 0)
			groups := randomDesign.Info.Groups
			for _, group := range groups {
				subGroups := group.SubGroup
				if len(subGroups) > 0 {
					for _, subGroup := range subGroups {
						newGroups = append(newGroups, group.Name+" "+subGroup.Name)
					}
				} else {
					newGroups = append(newGroups, group.Name)
				}
			}
			if project.ResearchAttribute == 1 && len(projectSite.StoreHouseID) <= 0 { //DTP
				return nil, tools.BuildServerError(ctx, "subject_no_random")
			}
			var projectSiteStorehouseID primitive.ObjectID
			if len(projectSite.StoreHouseID) > 0 {
				projectSiteStorehouseID = projectSite.StoreHouseID[0]
			}
			checkRcBl, canGroups, err := CheckRandomControl(ctx, randomControlRule, randomControlGroup, visitCycle, newGroups, subject, project.ResearchAttribute, projectSiteStorehouseID, result.Group)
			if !checkRcBl {
				return nil, errors.WithStack(err)
			}
			//可以随机到的分组
			if checkRcBl && randomControlRule == 3 {
				exist := arrays.ContainsString(canGroups, result.Group)
				if exist == -1 { //-1 说明没有随机到有药的组别，需要重新随机
					for _, list := range randomLists {
						randomList = list
						// 检查是否达到预计录入人数
						estimateBl, _, err = CheckSubject(sctx, randomList, subject, 0)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						if estimateBl {
							continue
						}
						// 随机入组
						result, err = extractRandomNumber(sctx, ctx, subject, projectSite, attribute, randomDesign, randomList, canGroups, project.Number)
						if err != nil {
							return nil, err
						}
						if result.RandomNumber == "" {
							continue
						} else {
							break
						}
					}
					if estimateBl {
						return nil, tools.BuildServerError(ctx, "subject_factor_check_error")
					}
					if result.RandomNumber == "" {
						return nil, tools.BuildServerError(ctx, "subject_no_random")
					}
				}
			}
		}
		randomSequenceNumber := ""
		if attribute.AttributeInfo.IsRandomSequenceNumber {
			lastSequenceNumberSubjects := make([]models.Subject, 0)
			lastFilter := bson.M{
				"env_id":                 subject.EnvironmentID,
				"group":                  bson.M{"$ne": ""},
				"random_sequence_number": bson.M{"$ne": ""},
			}
			if !subject.CohortID.IsZero() {
				lastFilter["cohort_id"] = subject.CohortID
			}
			//按随机时间排序
			var limit int64 = 1
			subjectOpts := options.FindOptions{
				//id和随机时间
				Projection: bson.M{"_id": 1, "random_time": 1, "random_sequence_number": 1},
				Sort:       bson.M{"random_time": -1},
				Limit:      &limit,
			}
			cursor, err = tools.Database.Collection("subject").Find(sctx, lastFilter, &subjectOpts)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &lastSequenceNumberSubjects)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(lastSequenceNumberSubjects) > 0 {
				lastRandomSequenceNumber := lastSequenceNumberSubjects[0].RandomSequenceNumber
				number, b := tools.GetNextRandomSequenceNumberWithPrefix(lastRandomSequenceNumber, attribute.AttributeInfo.RandomSequenceNumberPrefix, attribute.AttributeInfo.RandomSequenceNumberDigit)
				if b {
					randomSequenceNumber = number
				}
			} else {
				number, b := tools.GenerateRandomSequenceNumber(attribute.AttributeInfo.RandomSequenceNumberPrefix, attribute.AttributeInfo.RandomSequenceNumberDigit, attribute.AttributeInfo.RandomSequenceNumberStart)
				if b {
					randomSequenceNumber = number
				}
			}
		}

		// 1. 修改登记表的受试者状态为已随机
		subjectUpdate := bson.M{"$set": bson.M{"random_list_id": result.RandomListID, "status": 3, "random_number": result.RandomNumber, "group": result.Group, "par_group_name": result.ParName, "sub_group_name": result.SubName, "random_time": nowDuration, "random_people": user.ID}}
		if attribute.AttributeInfo.IsRandomSequenceNumber && randomSequenceNumber != "" {
			subjectUpdate["$set"].(bson.M)["random_sequence_number"] = randomSequenceNumber
			subject.RandomSequenceNumber = randomSequenceNumber
		}
		if _, err := tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": subject.ID}, subjectUpdate); err != nil {
			return nil, errors.WithStack(err)
		}

		// 2.修改随机号状态等
		if randomList.Design.Type == 1 {
			// 区组随机修改
			country, err := countryReturn(subject.ProjectSiteID)
			if err != nil {
				return nil, err
			}
			randomUpdate := bson.M{"$set": bson.M{"status": 2, "subject_id": subject.ID, "project_site_id": subject.ProjectSiteID, "country": country, "region_id": projectSite.RegionID}}
			if _, err := tools.Database.Collection("random_number").UpdateOne(sctx, bson.M{"_id": result.ID}, randomUpdate); err != nil {
				return nil, errors.WithStack(err)
			}

			if !attribute.AttributeInfo.IsRandom && attribute.AttributeInfo.InstituteLayered { // 中心无入组限制就要占用区组
				// 判断是否需要中心占区组
				randomBatchUpdate := bson.M{"$set": bson.M{"project_site_id": subject.ProjectSiteID, "country": country, "region_id": projectSite.RegionID}}
				if _, err := tools.Database.Collection("random_number").UpdateMany(sctx, bson.M{"random_list_id": randomList.ID, "block": result.BlockNumber, "status": 1}, randomBatchUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
			}
			if !attribute.AttributeInfo.IsCountryRandom && attribute.AttributeInfo.CountryLayered { // 国家无入组限制就要占用区组
				// 判断是否需要国家占区组
				country, err := countryReturn(subject.ProjectSiteID)
				if err != nil {
					return nil, err
				}
				randomBatchUpdate := bson.M{"$set": bson.M{"country": country}}
				if _, err = tools.Database.Collection("random_number").UpdateMany(sctx, bson.M{"random_list_id": randomList.ID, "block": result.BlockNumber, "status": 1}, randomBatchUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
			}
			if !attribute.AttributeInfo.IsRegionRandom && attribute.AttributeInfo.RegionLayered { // 区域无入组限制就要占用区组
				// 判断是否需要区域占区组
				randomBatchUpdate := bson.M{"$set": bson.M{"region_id": projectSite.RegionID}}
				if _, err = tools.Database.Collection("random_number").UpdateMany(sctx, bson.M{"random_list_id": randomList.ID, "block": result.BlockNumber, "status": 1}, randomBatchUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
			}

			// 判断是否要写入分层因素
			if result.LayeredSign { // 为true表示要写入分层因素
				var factors []models.Factors
				for _, factor := range randomList.Design.Factors {
					value := ""
					text := ""
					for _, info := range subject.Info {
						if factor.Name == info.Name && info.Value != nil {
							//text = info.Name
							value = info.Value.(string)
							for _, option := range factor.Options {
								if option.Value == value {
									text = option.Label
								}
							}
						}
					}
					factors = append(factors, models.Factors{
						Name:  factor.Name,
						Label: factor.Label,
						Value: value,
						Text:  text,
					})
				}

				randomNumberBlockUpdate := bson.M{"$set": bson.M{"factors": factors}}
				if _, err := tools.Database.Collection("random_number").UpdateMany(sctx, bson.M{"random_list_id": randomList.ID, "block": result.BlockNumber}, randomNumberBlockUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
			}
		} else {
			// 最小化随机修改
			country, err := countryReturn(subject.ProjectSiteID)
			if err != nil {
				return nil, err
			}
			randomMinUpdate := bson.M{"$set": bson.M{"status": 2, "subject_id": subject.ID, "project_site_id": subject.ProjectSiteID, "group": result.Group, "actual_number": result.RandomNumberModel.ActualNumber, "factors": result.RandomNumberModel.Factors, "group_value": result.RandomNumberModel.GroupValue, "country": country, "region_id": projectSite.RegionID}}
			if _, err := tools.Database.Collection("random_number").UpdateOne(sctx, bson.M{"_id": result.ID}, randomMinUpdate); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// 添加轨迹 判断是否是EDC对接项目
		var userName string
		if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 1 && sign == 1 { // EDC对接项目
			userName = edcUserName
		} else {
			userName = user.Name
		}
		group := result.Group
		if result.SubName != "" {
			group = result.ParName
		}

		// TODO 在随机
		stage := ""
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			if subject.CohortID != primitive.NilObjectID {
				for _, env := range project.Environments {
					if env.ID == subject.EnvironmentID {
						for _, cohort := range env.Cohorts {
							if cohort.ID == subject.CohortID {
								stage = cohort.Name
								break
							}
						}
						break
					}
				}
			}
		}

		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

		history := models.History{
			OID:  subject.ID,
			Data: map[string]interface{}{"label": subjectReplaceText, "name": subject.Info[0].Value, "randomNumber": result.RandomNumber, "group": group, "subGroup": result.SubName, "stage": stage},
			Time: nowDuration,
			UID:  user.ID,
			User: userName,
		}
		if result.SubName == "" {
			// TODO 在随机
			if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
				history.Key = "history.subject.label.at-random-random" // 展示随机号
				if !attribute.AttributeInfo.IsRandomNumber {
					history.Key = "history.subject.label.at-random-randomNoNumber" // 不展示随机号
				}
			} else {
				history.Key = "history.subject.label.random" // 展示随机号
				if !attribute.AttributeInfo.IsRandomNumber {
					history.Key = "history.subject.label.randomNoNumber" // 不展示随机号
				}
			}
		} else {
			// TODO 在随机
			if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
				history.Key = "history.subject.label.at-random-randomSub" // 展示随机号
				if !attribute.AttributeInfo.IsRandomNumber {
					history.Key = "history.subject.label.at-random-randomNoNumberSub" // 不展示随机号
				}
			} else {
				history.Key = "history.subject.label.randomSub" // 展示随机号
				if !attribute.AttributeInfo.IsRandomNumber {
					history.Key = "history.subject.label.randomNoNumberSub" // 不展示随机号
				}
			}
		}

		_, err = tools.Database.Collection("history").InsertOne(sctx, history)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 如果是在随机项目或者cohort项目判断是否入组完成
		if project.ProjectInfo.Type == 2 || project.ProjectInfo.Type == 3 {
			// 小于0说明没设置人数
			find, b := slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				return item.Type == 3
			})

			if b && find.Capacity > 0 {
				// 查询已随机列表
				subjectFilter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID, "group": bson.M{"$ne": ""}, "status": bson.M{"$in": [5]int{3, 4, 6, 9}}}
				var subjectAll []models.Subject
				subjectAllCursor, err := tools.Database.Collection("subject").Find(nil, subjectFilter)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if err = subjectAllCursor.All(nil, &subjectAll); err != nil {
					return nil, errors.WithStack(err)
				}

				if len(subjectAll)+1 == find.Capacity {
					// 修改cohort状态为入组已满
					opts := &options.UpdateOptions{
						ArrayFilters: &options.ArrayFilters{
							Filters: bson.A{bson.M{"env.id": subject.EnvironmentID}, bson.M{"cohort.id": subject.CohortID}},
						},
					}
					projectUpdate := bson.M{"$set": bson.M{"envs.$[env].cohorts.$[cohort].status": 5}}
					if _, err := tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": subject.ProjectID}, projectUpdate, opts); err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		} else {
			// 小于0说明没设置人数
			find, b := slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				return item.Type == 3
			})

			if b && find.Capacity > 0 {
				// 查询已随机列表
				subjectFilter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "group": bson.M{"$ne": ""}, "status": bson.M{"$in": [5]int{3, 4, 6, 9}}}
				var subjectAll []models.Subject
				subjectAllCursor, err := tools.Database.Collection("subject").Find(nil, subjectFilter)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if err = subjectAllCursor.All(nil, &subjectAll); err != nil {
					return nil, errors.WithStack(err)
				}

				if len(subjectAll)+1 == find.Capacity {
					// 修改cohort状态为入组已满
					opts := &options.UpdateOptions{
						ArrayFilters: &options.ArrayFilters{
							Filters: bson.A{bson.M{"env.id": subject.EnvironmentID}},
						},
					}
					projectUpdate := bson.M{"$set": bson.M{"envs.$[env].status": 5}}
					if _, err := tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": subject.ProjectID}, projectUpdate, opts); err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		}
		// 随机发送邮件

		// 添加邮件
		randomMails, err := sendSubjectRandomMail(ctx, attribute, form, subject, result, project, subject.EnvironmentID, subject.CohortID, subject.ProjectSiteID, nowDuration, randomList)
		if err != nil {
			return nil, err
		}
		mails = append(mails, randomMails...)

		strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
		if err != nil {
			return nil, err
		}
		if strTimeZone == "" {
			zone, err := tools.GetTimeZone(subject.ProjectID)
			if err != nil {
				return nil, err
			}
			//strTimeZone = fmt.Sprintf("UTC%+d", zone)
			strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
		}
		//timeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
		timeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
		hours := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute

		//  更新受试者访视数据
		err = updateDispensing(ctx, sctx, subject, result.Group)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 返回结果
		remoteSubjectRandom.ID = ID
		remoteSubjectRandom.RandomNo = result.RandomNumber
		remoteSubjectRandom.RandomTime = now.UTC().Add(duration).Format("2006-01-02 15:04:05")
		remoteSubjectRandom.TimeZone = strTimeZone
		remoteSubjectRandom.Timestamp = nowDuration
		remoteSubjectRandom.Status = 3
		remoteSubjectRandom.Group = result.Group
		remoteSubjectRandom.ParName = result.ParName
		remoteSubjectRandom.SubName = result.SubName
		remoteSubjectRandom.SiteNo = projectSite.Number
		remoteSubjectRandom.StandardSiteName = projectSite.Name
		remoteSubjectRandom.Project = project
		if tools.BlockProject(project.ID.Hex()) && attribute.AttributeInfo.Blind {
			if remoteSubjectRandom.SubName != "" {
				sgP, b := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
					return item.Name == remoteSubjectRandom.Group && item.SubName == remoteSubjectRandom.SubName
				})
				if b {
					sg := *sgP
					if sg.Blind {
						remoteSubjectRandom.SubName = tools.BlindData
					}
				}
			}
			remoteSubjectRandom.Group = tools.BlindData
			remoteSubjectRandom.ParName = tools.BlindData
		}

		// 如果配置
		err = task.UpdateNotice(sctx, 4, visitCycle.EnvironmentID, visitCycle.CohortID, subject.ProjectSiteID, subject.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var noticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		langList := make([]string, 0)
		html := "subject_limit_alert_zh_en.html"
		if noticeConfig.Manual != 0 {
			if noticeConfig.Manual == 1 {
				langList = append(langList, "zh")
				html = "subject_limit_alert_zh.html"
			} else if noticeConfig.Manual == 2 {
				langList = append(langList, "en")
				html = "subject_limit_alert_en.html"
			} else if noticeConfig.Manual == 3 {
				langList = append(langList, "zh")
				langList = append(langList, "en")
				html = "subject_limit_alert_zh_en.html"
			}
		} else {
			langList = append(langList, ctx.GetHeader("Accept-Language"))
			if locales.Lang(ctx) == "zh" {
				html = "subject_limit_alert_zh.html"
			} else if locales.Lang(ctx) == "en" {
				html = "subject_limit_alert_en.html"
			}
		}

		// 检查是否达到警戒人数
		alarmBl, alarmInfo, err := CheckSubject(sctx, randomList, subject, 1)
		if err != nil {
			return models.RemoteSubjectRandom{}, err
		}
		if alarmBl {
			// 警戒发送邮件
			alertMail, err := tools.GetRoleUsersMail(project.ID, env.ID, "notice.subject.alarm", subject.ProjectSiteID)
			if err != nil {
				return models.RemoteSubjectRandom{}, err
			}
			if len(alertMail) > 0 {
				contentData := map[string]interface{}{"projectNumber": project.Number, "projectName": project.Name, "envName": env.Name, "info": alarmInfo}
				if !cohort.ID.IsZero() {
					contentData["cohortName"] = models.GetCohortReRandomName(cohort)
				}
				bodyContentKeys, _, err := tools.MailCustomContent(ctx, env.ID, "notice.subject.alarm", contentData, nil)
				if err != nil {
					return models.RemoteSubjectRandom{}, err
				}
				for _, userEmail := range alertMail {
					var toUserMail []string
					toUserMail = append(toUserMail, userEmail)
					mails = append(mails, models.Mail{
						ID:             primitive.NewObjectID(),
						Subject:        "subject.alarm.title",
						SubjectData:    map[string]interface{}{"projectNumber": project.Number, "envName": env.Name},
						Content:        "subject.alarm.content",
						ContentData:    contentData,
						To:             toUserMail,
						Lang:           ctx.GetHeader("Accept-Language"),
						LangList:       langList,
						Status:         0,
						CreatedTime:    time.Duration(time.Now().Unix()),
						ExpectedTime:   time.Duration(time.Now().Unix()),
						SendTime:       time.Duration(time.Now().Unix()),
						BodyContentKey: bodyContentKeys,
					})
				}
			}
		}

		mails, err = alertThresholds(sctx, 3, project, env, cohort, attribute, subject, projectSite, ctx, html, mails)
		if err != nil {
			return models.RemoteSubjectRandom{}, err
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return models.RemoteSubjectRandom{}, err
	}

	ctx.Set("MAIL", mails)
	if len(mails) > 0 {
		var envs []models.MailEnv
		for _, m := range mails {
			envs = append(envs, models.MailEnv{
				ID:         primitive.NewObjectID(),
				MailID:     m.ID,
				CustomerID: project.CustomerID,
				ProjectID:  project.ID,
				EnvID:      env.ID,
				CohortID:   cohort.ID,
			})
		}
		ctx.Set("MAIL-ENV", envs)
	}

	//添加app发药通知 fixme 仅发药不需要通知？
	if len(roleId) > 0 && attribute.AttributeInfo.Dispensing {
		err = PatchAppDispenseTask(ctx, subject.ID.Hex(), roleId)
		if err != nil {
			return remoteSubjectRandom, err
		}
	}

	return remoteSubjectRandom, nil
}

// alertType 已登记1 筛选成功7 入组3
func alertThresholds(sctx mongo.SessionContext, alertType int, project models.Project, env models.Environment, cohort models.Cohort, attribute models.Attribute, subject models.Subject, projectSite models.ProjectSite, ctx *gin.Context, html string, mails []models.Mail) ([]models.Mail, error) {
	hasThresholds := false
	capacity := 0
	thresholds := 0

	if project.Type == 1 && env.AlertThresholds != nil {
		find, b := slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
			return item.Type == alertType
		})
		if b && find.Thresholds != nil {
			hasThresholds = true
			capacity = find.Capacity
			thresholds = *find.Thresholds
		}
	} else if project.ProjectInfo.Type != 1 {
		find, b := slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
			return item.Type == alertType
		})
		if b && find.Thresholds != nil {
			hasThresholds = true
			capacity = find.Capacity
			thresholds = *find.Thresholds
		}
	}

	if hasThresholds && (alertType == 1 || alertType == 3 || alertType == 7) {
		// 检查是否达到提醒阈值
		unRandomCountFilter := bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"deleted":     bson.M{"$ne": true},
		}
		currentlyCountFilter := bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"deleted":     bson.M{"$ne": true},
		}
		if subject.CohortID != primitive.NilObjectID {
			unRandomCountFilter["cohort_id"] = subject.CohortID
			currentlyCountFilter["cohort_id"] = subject.CohortID
		}
		unRandomCount := int64(0)
		currentlyCount := int64(0)
		if alertType == 3 && !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing {
			count, err := tools.CountSubject(sctx, attribute, currentlyCountFilter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			currentlyCount = int64(count)
			allCount, err := tools.Database.Collection("subject").CountDocuments(sctx, unRandomCountFilter)
			unRandomCount = allCount - int64(count)
		} else {
			if alertType == 3 {
				unRandomCountFilter["status"] = bson.M{"$in": [4]int{1, 7}}
				currentlyCountFilter["status"] = bson.M{"$in": [5]int{3, 4, 6, 9}}
				currentlyCountFilter["group"] = bson.M{"$ne": ""}
			} else if alertType == 1 {
				currentlyCountFilter["status"] = bson.M{"$ne": 5}
			} else if alertType == 7 {
				unRandomCountFilter["is_screen"] = bson.M{"$ne": true}
				currentlyCountFilter["is_screen"] = true
			}

			var err error
			if alertType == 3 || alertType == 7 {
				unRandomCount, err = tools.Database.Collection("subject").CountDocuments(sctx, unRandomCountFilter)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
			currentlyCount, err = tools.Database.Collection("subject").CountDocuments(sctx, currentlyCountFilter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		needAlert := false
		name := ""
		r := 0
		if project.ProjectInfo.Type == 1 {
			name = project.ProjectInfo.Name
		} else {
			name = cohort.Name
		}
		r = thresholds
		c := capacity
		alertCount := float64(r) / 100 * float64(c)
		if float64(currentlyCount) >= alertCount {
			needAlert = true
		}

		var noticeConfig models.NoticeConfig
		err := tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": env.ID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		langList := make([]string, 0)
		if noticeConfig.Manual != 0 {
			if noticeConfig.Manual == 1 {
				langList = append(langList, "zh")
			} else if noticeConfig.Manual == 2 {
				langList = append(langList, "en")
			} else if noticeConfig.Manual == 3 {
				langList = append(langList, "zh")
				langList = append(langList, "en")
			}
		} else {
			langList = append(langList, ctx.GetHeader("Accept-Language"))
		}

		if needAlert {
			// 受试者上限提醒发送邮件
			thresholdMail, err := tools.GetRoleUsersMail(project.ID, env.ID, "notice.subject.alert.threshold", subject.ProjectSiteID)
			if err != nil {
				return nil, err
			}
			plannedCases := strconv.Itoa(capacity)
			if len(thresholdMail) > 0 {
				contentData := map[string]interface{}{
					"projectNumber":      project.Number,
					"projectName":        project.Name,
					"envName":            env.Name,
					"siteNumber":         projectSite.Number,
					"siteName":           tools.GetProjectSiteLangName(projectSite, "zh"),
					"siteNameEn":         tools.GetProjectSiteLangName(projectSite, "en"),
					"subjectNumber":      subject.Info[0].Value,
					"reminderThresholds": r,
					"name":               name,
					"currently":          currentlyCount,
					"additional":         unRandomCount,
					"plannedCases":       plannedCases,
				}
				if !cohort.ID.IsZero() {
					contentData["cohortName"] = models.GetCohortReRandomName(cohort)
				}
				bodyContentKeys, _, err := tools.MailCustomContent(ctx, env.ID, "notice.subject.alert.threshold", contentData, nil)
				if err != nil {
					return nil, err
				}
				mailBodyContent, err := tools.MailBodyContent(nil, env.ID, "notice.subject.alert.threshold")
				for key, v := range mailBodyContent {
					contentData[key] = v
				}
				subjectTitle := "subject.alert_threshold.title"
				if project.ProjectInfo.Type != 1 {
					subjectTitle = "subject.alert_threshold.cohort_title"
				}
				for _, userEmail := range thresholdMail {
					var toUserMail []string
					toUserMail = append(toUserMail, userEmail)
					mails = append(mails, models.Mail{
						ID:             primitive.NewObjectID(),
						Subject:        subjectTitle,
						SubjectData:    map[string]interface{}{"projectNumber": project.Number, "envName": env.Name, "cohortName": cohort.Name},
						ContentData:    contentData,
						To:             toUserMail,
						Lang:           ctx.GetHeader("Accept-Language"),
						LangList:       langList,
						Status:         0,
						CreatedTime:    time.Duration(time.Now().Unix()),
						ExpectedTime:   time.Duration(time.Now().Unix()),
						SendTime:       time.Duration(time.Now().Unix()),
						BodyContentKey: bodyContentKeys,
						HTML:           html,
					})
				}
			}
		}
	}
	return mails, nil
}

func (s *SubjectService) GetSubjectNumber(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, siteID string) (string, error) {
	subjectNumber := ""
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectSiteOID, _ := primitive.ObjectIDFromHex(siteID)

	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortID != "" && cohortID != "000000000000000000000000" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		filter = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return "", errors.WithStack(err)
	}
	//受试者号录入规则
	subjectNumberRule := attribute.AttributeInfo.SubjectNumberRule
	if subjectNumberRule == 2 || subjectNumberRule == 3 {
		subjectNumber, err = getSubjectNumber(ctx, projectOID, envOID, projectSiteOID, attribute.AttributeInfo)
		if err != nil {
			return "", errors.WithStack(err)
		}
	} else {
		//受试者号前缀
		if attribute.AttributeInfo.Prefix {
			prefixExpression := attribute.AttributeInfo.PrefixExpression
			if len(prefixExpression) > 0 {
				if strings.Contains(prefixExpression, "{siteNO}") {
					siteNumber := ""
					var projectSite models.ProjectSite
					err := tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteOID}).Decode(&projectSite)
					if err != nil {
						return "", errors.WithStack(err)
					}
					if projectSite.Number != "" {
						siteNumber = projectSite.Number
					}
					subjectNumber = strings.Replace(prefixExpression, "{siteNO}", siteNumber, 1)
				}
			}
		}
	}
	return subjectNumber, nil
}

// SubjectEdcVerification edc对接项目受试者随机前校验
func (s *SubjectService) SubjectEdcVerification(ctx *gin.Context) (models.SubjectReturnResults, error) {
	// 返回数据
	var subjectReturnResults models.SubjectReturnResults

	// 参数转换
	var parameterData map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&parameterData, binding.JSON)
	subjectId := primitive.NilObjectID
	if parameterData["id"] != nil {
		subjectId, _ = primitive.ObjectIDFromHex(parameterData["id"].(string))
	}

	// 是否为筛选场景的校验
	screenFlag := true
	// 筛选校验的parameterData里仅包含 id, isScreen, screenTime, icfTime
	screenAllowedFields := map[string]bool{
		"id":         true,
		"isScreen":   true,
		"screenTime": true,
		"icfTime":    true,
	}

	for key := range parameterData {
		if !screenAllowedFields[key] {
			// 发现额外的字段
			screenFlag = false
			break
		}
	}

	// 前端传来的受试者号
	newSubjectNo := ""
	if shortname, ok := parameterData["shortname"].(string); ok {
		newSubjectNo = shortname
	}

	//mark := ""
	//if parameterData["mark"] != nil { // 登记或者修改的标记
	//	mark = parameterData["mark"].(string)
	//}

	//codeStr := ""
	//if parameterData["codeStr"] != nil { // 登记标记
	//	codeStr = parameterData["codeStr"].(string)
	//}

	// 查询受试者、项目、环境、中心信息
	// 受试者
	var subject models.Subject
	if subjectId != primitive.NilObjectID {
		err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectId}).Decode(&subject)
		if err != nil && err != mongo.ErrNoDocuments {
			return subjectReturnResults, errors.WithStack(err)
		}
	}

	// 如果是替换受试者，这个参数不可能为空(根据ID会查询到原受试者，所以此处要用受试者号在查询一次)
	replaceSign := true // 替换标记(true不是替换操作 false是替换操作)
	subjectNumber := parameterData["subjectNumber"]
	if subjectNumber != nil {
		replaceSign = false
		subjectFilter := bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": subjectNumber.(string),
				},
			},
			"deleted": bson.M{"$ne": true},
		}
		if subject.CohortID != primitive.NilObjectID {
			subjectFilter["cohort_id"] = subject.CohortID
		}
		err := tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&subject)
		if err != nil && err != mongo.ErrNoDocuments {
			return subjectReturnResults, errors.WithStack(err)
		}
	}

	// 项目
	projectId := primitive.NilObjectID
	if parameterData["projectId"] != nil {
		projectId, _ = primitive.ObjectIDFromHex(parameterData["projectId"].(string))
	} else {
		projectId = subject.ProjectID
	}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectId}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return subjectReturnResults, errors.WithStack(err)
	}

	// 环境
	envId := primitive.NilObjectID
	if parameterData["envId"] != nil {
		envId, _ = primitive.ObjectIDFromHex(parameterData["envId"].(string))
	} else {
		envId = subject.EnvironmentID
	}
	var environment models.Environment
	for _, env := range project.Environments {
		if env.ID == envId {
			environment = env
			break
		}
	}

	// 中心
	projectSiteId := primitive.NilObjectID
	if parameterData["projectSiteId"] != nil {
		projectSiteId, _ = primitive.ObjectIDFromHex(parameterData["projectSiteId"].(string))
	} else {
		projectSiteId = subject.ProjectSiteID
	}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteId}).Decode(&projectSite)
	if err != nil && err != mongo.ErrNoDocuments {
		return subjectReturnResults, errors.WithStack(err)
	}

	// customerID
	customerId := primitive.NilObjectID
	if parameterData["customerId"] != nil {
		customerId, _ = primitive.ObjectIDFromHex(parameterData["customerId"].(string))
	} else {
		customerId = subject.CustomerID
	}

	// cohortID
	cohortId := primitive.NilObjectID
	if parameterData["cohortId"] != nil {
		cohortId, _ = primitive.ObjectIDFromHex(parameterData["cohortId"].(string))
	} else {
		cohortId = subject.CohortID
	}
	randomFilter := bson.M{
		"customer_id": customerId,
		"env_id":      envId,
		"status":      1,
		"$or": bson.A{
			bson.M{"site_ids": nil},
			bson.M{"site_ids": projectSiteId},
		}}
	if project.Type != 1 {
		randomFilter = bson.M{
			"customer_id": customerId,
			"env_id":      envId,
			"status":      1,
			"cohort_id":   cohortId,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": projectSiteId},
			}}
	}
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)
	if err != nil && err != mongo.ErrNoDocuments {
		return subjectReturnResults, errors.WithStack(err)
	}

	// 查询表单
	var randomForm models.Form
	formFilter := bson.M{"env_id": envId}
	if project.Type != 1 {
		formFilter = bson.M{"env_id": envId, "cohort_id": cohortId}
	}
	err = tools.Database.Collection("form").FindOne(nil, formFilter).Decode(&randomForm)
	if err != nil && err != mongo.ErrNoDocuments {
		return subjectReturnResults, errors.WithStack(err)
	}

	// 查询项目属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, formFilter).Decode(&attribute)
	if err != nil {
		return subjectReturnResults, errors.WithStack(err)
	}
	subjectNumberRule := attribute.AttributeInfo.SubjectNumberRule
	if subjectNumberRule == 2 || subjectNumberRule == 3 {
		parameterData["shortname"], err = getSubjectNumber(ctx, projectId, envId, projectSiteId, attribute.AttributeInfo)
		if err != nil {
			return subjectReturnResults, errors.WithStack(err)
		}
	}

	// 调用EDC接口
	parameter := make(map[string]interface{})
	timestamp := time.Now().UnixMilli()

	irtSubjectID := "" // 这个字段在登记状态下是空的
	if parameterData["id"] != nil {
		if replaceSign == false { // false 代表是替换操作
			irtSubjectID = subject.ID.Hex()
		} else {
			irtSubjectID = parameterData["id"].(string)
		}
	}

	var cohort models.Cohort
	if project.Type != 1 {
		for _, ch := range environment.Cohorts {
			if ch.ID == cohortId {
				cohort = ch
				break
			}
		}
	}
	// 在随机项目非第一阶段或者推送规则为受试者号的不要传受试者ID
	if (project.Type == 3 && cohort.LastID != primitive.NilObjectID) || (project.PushRules == 1) {
		irtSubjectID = ""
	}

	// 筛选出受试者号
	subjectNo := ""
	if subject.Info != nil && len(subject.Info) > 0 {
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				subjectNo = info.Value.(string)
				// 如果页面输入了新的受试者号，则采用新的
				if newSubjectNo != "" && newSubjectNo != subjectNo {
					subjectNo = newSubjectNo
				}
				break
			}
		}
	} else {
		subjectNo = parameterData["shortname"].(string)
	}

	parameter["env"] = environment.Name
	parameter["project"] = project.Number
	parameter["sign"] = GetMD5Hash(timestamp) // 签名串
	//parameter["site"] = projectSite.Number    // 这个参数要在2024.3月底EDC更新的时候移除
	parameter["subjectNo"] = subjectNo
	parameter["irtSubjectId"] = irtSubjectID
	if project.Type != 1 {
		parameter["cohort"] = cohort.Name
	} else {
		parameter["cohort"] = ""
	}
	parameter["timestamp"] = timestamp
	httpObtainEdcStatus, reqParam, rspBody, err := tools.HttpPostStatus(project.EdcUrl, parameter) // 调用EDC接口获取受试者状态

	// 记录日志
	recoveryRequestInterface := models.RequestInterface{
		ID:        primitive.NewObjectID(),
		Type:      "subject.edc.verification",
		API:       reqParam,
		Body:      rspBody,
		CreatedAt: time.Duration(time.Now().Unix()),
		Status:    0,
		SendAt:    time.Duration(time.Now().Unix()),
	}
	if err != nil {
		recoveryRequestInterface.Reason = err.Error()
		recoveryRequestInterface.Status = 2 // 请求失败
	} else {
		recoveryRequestInterface.Status = 1 // 请求成功
	}

	if _, err := tools.Database.Collection("request_interface").InsertOne(nil, recoveryRequestInterface); err != nil {
		tools.SaveErrorLog("SubjectEdcVerification_add_request_interface", err)
	}

	// 替换功能只传了一个受试者ID没有传分层因素文本，这里要加进去
	for _, ft := range randomList.Design.Factors {
		if parameterData[ft.Name] == nil || parameterData[ft.Name+"Item"] != nil {
			for _, sbj := range subject.Info {
				if ft.Name == sbj.Name {
					parameterData[ft.Name] = sbj.Value
					break
				}
			}
		}
	}
	// 替换功能只传了一个受试者ID没有传表单，这里要加进去
	if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
		for _, ff := range randomForm.Fields {
			if parameterData[ff.Name] == nil && (ff.Status == nil || *ff.Status == 1) { // 有效或有值
				for _, sbj := range subject.Info {
					if ff.Name == sbj.Name {
						parameterData[ff.Name] = sbj.Value
						break
					}
				}
			}
		}
	}

	// 模拟数据开始
	//err = nil
	//var httpObtainEdcStatus models.HTTPObtainEdcStatus
	//httpObtainEdcStatus.Success = true
	//var sd []models.SubjectData
	//sd = append(sd, models.SubjectData{
	//	Cohort:   "",
	//	IrtKey:   "field0",
	//	IrtValue: "",
	//	EdcValue: "输入框数据",
	//	Label:    "",
	//})
	//sd = append(sd, models.SubjectData{
	//	//Cohort:   "",
	//	IrtKey:   "SEX",
	//	IrtValue: "",
	//	EdcValue: "男",
	//	Label:    "",
	//})
	//sd = append(sd, models.SubjectData{
	//	//Cohort:   "",
	//	IrtKey:   "AGE",
	//	IrtValue: "",
	//	EdcValue: "30",
	//	Label:    "",
	//})
	//httpObtainEdcStatus.Data.SubjectData = sd
	//httpObtainEdcStatus.Data.SubjectNo = "SSZ10"
	//httpObtainEdcStatus.Data.IrtSubjectStatus = 3
	//httpObtainEdcStatus.Data.SiteNo = "100"
	//httpObtainEdcStatus.Data.SiteName = "齐齐哈尔医院"
	// 模拟数据结束
	subjectReturnResults.IrtSubjectNo = subjectNo
	if httpObtainEdcStatus.Success == true && err == nil { // 调用成功
		// EDC接口请求成功
		subjectReturnResults.LinkStatus = true

		// 查询数据是否还在处理中
		if subject.ID != primitive.NilObjectID {
			var edcPush models.EdcPush
			err := tools.Database.Collection("edc_push").FindOne(nil, bson.M{"oid": subject.ID, "status": bson.M{"$in": bson.A{0, 4}}}).Decode(&edcPush)
			if err != nil && err != mongo.ErrNoDocuments {
				return subjectReturnResults, errors.WithStack(err)
			}
			if edcPush.ID != primitive.NilObjectID {
				if edcPush.SourceType == 1 { // 登记
					// 登记的数据是否在推送中
					subjectReturnResults.SubjectRegisterPushIn = true
				} else {
					// 数据是否在推送中
					subjectReturnResults.SubjectPushIn = true
				}
			}
		}

		if httpObtainEdcStatus.Data.SubjectNo != "" {
			// 校验中心
			if httpObtainEdcStatus.Data.SiteNo == "" {
				subjectReturnResults.SiteStatus = 3
			} else {
				if httpObtainEdcStatus.Data.SiteNo != projectSite.Number {
					subjectReturnResults.SiteStatus = 2
				} else {
					subjectReturnResults.SiteStatus = 1
				}
			}
			// EDC存在当前受试者
			subjectReturnResults.SubjectIsExist = true
			// EDC是否配置了映射关系
			if httpObtainEdcStatus.Data.SubjectData != nil && len(httpObtainEdcStatus.Data.SubjectData) > 0 {
				// 对筛选做特殊处理
				if screenFlag {
					screenKeys := []string{"isScreen", "screenTime", "icfTime"}
					for _, sd := range httpObtainEdcStatus.Data.SubjectData {
						if slice.Contain(screenKeys, sd.IrtKey) {
							subjectReturnResults.EdcMapping = true
							break
						}
					}
				} else {
					subjectReturnResults.EdcMapping = true
				}
			}
			// EDC返回的受试者状态
			subjectReturnResults.EdcSubjectStatus = httpObtainEdcStatus.Data.IrtSubjectStatus
			// 分层表单等数据处理
			subjectReturnResults, err = layerrdContrast(ctx, parameterData, randomList, randomForm, httpObtainEdcStatus, subjectReturnResults, project, cohort, screenFlag, attribute)
			if err != nil {
				return subjectReturnResults, errors.WithStack(err)
			}

			// 受试者数据筛选
			//var edcSubjectData []models.SubjectData
			//for _, hds := range httpObtainEdcStatus.Data.SubjectData {
			//	if hds.IrtKey != "randomNo" &&
			//		hds.IrtKey != "randomTime" &&
			//		hds.IrtKey != "group" &&
			//		hds.IrtKey != "drugNo" &&
			//		hds.IrtKey != "drugTime" &&
			//		hds.IrtKey != "drug" &&
			//		hds.IrtKey != "doseLevel" &&
			//		hds.IrtKey != "labels" &&
			//		hds.IrtKey != "parGroupName" &&
			//		hds.IrtKey != "subGroupName" {
			//		edcSubjectData = append(edcSubjectData, hds)
			//	}
			//}
			//
			//// 判断EDC是否配置了映射关系
			//if edcSubjectData != nil && len(edcSubjectData) > 0 {
			//	cohortSign := true
			//	if project.ProjectInfo.Type != 1 {
			//		for _, esd := range edcSubjectData {
			//			if esd.IrtKey == "cohortName" {
			//				cohortSign = false
			//			}
			//		}
			//	}
			//	// 判断当前项目是否配置了分层因素/表单
			//	if (randomList.Design.Factors == nil || len(randomList.Design.Factors) == 0) && (randomForm.Fields == nil || len(randomForm.Fields) == 0) && cohortSign { // 未配置分层因素/表单/cohortName
			//		//  判断受试者状态
			//		if httpObtainEdcStatus.Data.IrtSubjectStatus == 2 { // 筛选失败
			//			// 状态5 edc筛选失败
			//			subjectReturnResults.IrtSubjectStatus = 5
			//		} else if httpObtainEdcStatus.Data.IrtSubjectStatus == 5 { // 退出
			//			// 状态6 EDC已退出
			//			subjectReturnResults.IrtSubjectStatus = 6
			//		} else if httpObtainEdcStatus.Data.IrtSubjectStatus == 6 { // 完成研究
			//			// 状态7 EDC完成研究
			//			subjectReturnResults.IrtSubjectStatus = 7
			//		} else { // 剩余的状态 0edc未返回状态 1筛选中 3筛选成功 4已入组 7完成研究(允许进入下一阶段) 都是可以正常随机/登记/修改的
			//			// 正常随机
			//			// (tools.IrtPushEdcQuarantineNoRandom(project.ProjectInfo.Number) && httpObtainEdcStatus.Data.IrtSubjectStatus == 1) ||
			//			if httpObtainEdcStatus.Data.IrtSubjectStatus == 8 { // 8 筛选中-不可随机
			//				subjectReturnResults.ScreenSign = true // true不允许随机
			//			}
			//			subjectReturnResults.IrtSubjectStatus = 1
			//		}
			//	} else { // 配置了分层因素/表单/cohort
			//		//  判断受试者状态
			//		if httpObtainEdcStatus.Data.IrtSubjectStatus == 2 { // 筛选失败
			//			if mark != "" { // 登记或者修改筛选失败是可以继续操作的。并且要对分层因素做出比对
			//				// 分层因素对比
			//				subjectReturnResults, err = layerrdContrast(parameterData, randomList, randomForm, edcSubjectData, subjectReturnResults, project, cohort, codeStr, replaceSign, httpObtainEdcStatus.Data.SubjectNo, attribute)
			//				if err != nil {
			//					return subjectReturnResults, errors.WithStack(err)
			//				}
			//			} else {
			//				subjectReturnResults.IrtSubjectStatus = 5 // 状态5 edc筛选失败
			//			}
			//		} else if httpObtainEdcStatus.Data.IrtSubjectStatus == 5 { // 退出
			//			if mark != "" { // 登记或者修改退出是可以继续操作的。并且要对分层因素做出比对
			//				// 分层因素对比
			//				subjectReturnResults, err = layerrdContrast(parameterData, randomList, randomForm, edcSubjectData, subjectReturnResults, project, cohort, codeStr, replaceSign, httpObtainEdcStatus.Data.SubjectNo, attribute)
			//				if err != nil {
			//					return subjectReturnResults, errors.WithStack(err)
			//				}
			//			} else {
			//				subjectReturnResults.IrtSubjectStatus = 6 // 状态6 EDC已退出
			//			}
			//		} else if httpObtainEdcStatus.Data.IrtSubjectStatus == 6 { // 完成研究
			//			if mark != "" { // 登记或者修改完成研究是可以继续操作的。并且要对分层因素做出比对
			//				// 分层因素对比
			//				subjectReturnResults, err = layerrdContrast(parameterData, randomList, randomForm, edcSubjectData, subjectReturnResults, project, cohort, codeStr, replaceSign, httpObtainEdcStatus.Data.SubjectNo, attribute)
			//				if err != nil {
			//					return subjectReturnResults, errors.WithStack(err)
			//				}
			//			} else {
			//				subjectReturnResults.IrtSubjectStatus = 7 // 状态7 EDC完成研究
			//			}
			//		} else { // 剩余的状态 0edc未返回状态 1筛选中 3筛选成功 4已入组 7完成研究(允许进入下一阶段) 都是可以正常登记/随机的
			//			// 分层因素对比
			//			subjectReturnResults, err = layerrdContrast(parameterData, randomList, randomForm, edcSubjectData, subjectReturnResults, project, cohort, codeStr, replaceSign, httpObtainEdcStatus.Data.SubjectNo, attribute)
			//			if err != nil {
			//				return subjectReturnResults, errors.WithStack(err)
			//			}
			//			// (tools.IrtPushEdcQuarantineNoRandom(project.ProjectInfo.Number) && httpObtainEdcStatus.Data.IrtSubjectStatus == 1) ||
			//			if httpObtainEdcStatus.Data.IrtSubjectStatus == 8 { //1筛选中-可随机(T-2104-2301-2403项目特殊处理) 8筛选中-不可随机
			//				subjectReturnResults.ScreenSign = true // true不允许随机
			//			}
			//		}
			//	}
			//} else {
			//	if httpObtainEdcStatus.Data.IrtSubjectStatus == 2 { // 筛选失败
			//		// 状态5 edc筛选失败
			//		subjectReturnResults.IrtSubjectStatus = 5
			//	} else if httpObtainEdcStatus.Data.IrtSubjectStatus == 5 { // 退出
			//		// 状态6 EDC已退出
			//		subjectReturnResults.IrtSubjectStatus = 6
			//	} else if httpObtainEdcStatus.Data.IrtSubjectStatus == 6 { // 完成研究
			//		// 状态7 EDC完成研究
			//		subjectReturnResults.IrtSubjectStatus = 7
			//	} else {
			//		if httpObtainEdcStatus.Data.SubjectData != nil && len(httpObtainEdcStatus.Data.SubjectData) > 0 {
			//			subjectReturnResults.IrtSubjectStatus = 1 // 正常随机（这种情况可能是配置了随机号 药物号等固定字段信息）
			//			// (tools.IrtPushEdcQuarantineNoRandom(project.ProjectInfo.Number) && httpObtainEdcStatus.Data.IrtSubjectStatus == 1) ||
			//			if httpObtainEdcStatus.Data.IrtSubjectStatus == 8 { //1筛选中-可随机(T-2104-2301-2403项目特殊处理) 8筛选中-不可随机
			//				subjectReturnResults.ScreenSign = true // true不允许随机
			//			}
			//		} else {
			//			subjectReturnResults.IrtSubjectStatus = 9 // EDC未配置映射关系
			//		}
			//	}
			//}
		}
	}
	return subjectReturnResults, nil
}

// UpdateStatus 修改状态（受试者状态，紧急揭盲，pv揭盲）
func (s *SubjectService) UpdateStatus(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		req := struct {
			ID              string `json:"id"`
			Sign            string `json:"sign"`
			Remark          string `json:"remark"`
			Reason          string `json:"reason"`
			ReasonStr       string `json:"reasonStr"`
			Password        string `json:"password"`
			IsSponsor       bool   `json:"isSponsor"`
			SignOutRealTime string `json:"signOutRealTime"`
			FinishRemark    string `json:"finishRemark"`
		}{}

		_ = ctx.ShouldBindBodyWith(&req, binding.JSON)

		id, _ := primitive.ObjectIDFromHex(req.ID)
		// subject
		var subject models.Subject
		err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": id}).Decode(&subject)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		// project
		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		unblindingControl := project.ProjectInfo.UnblindingControl
		//environment
		var environment models.Environment
		var cohort models.Cohort
		for _, env := range project.Environments {
			if env.ID == subject.EnvironmentID {
				environment = env
				if env.Cohorts != nil && len(env.Cohorts) > 0 {
					for _, co := range env.Cohorts {
						if co.ID == subject.CohortID {
							cohort = co
							break
						}
					}
				} else {
					break
				}
			}
		}
		// site
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		//attribute
		var attribute models.Attribute
		attributeFilter := bson.M{"env_id": subject.EnvironmentID}
		if !subject.CohortID.IsZero() {
			attributeFilter = bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
		}
		err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		blind := attribute.AttributeInfo.Blind

		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
		subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
		subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

		siteName := tools.GetProjectSiteLangName(projectSite, "zh")
		siteNameEn := tools.GetProjectSiteLangName(projectSite, "en")
		//var site models.Site
		//err = tools.Database.Collection("site").FindOne(nil, bson.M{"_id": projectSite.SiteID}).Decode(&site)
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}
		user, err := tools.Me(ctx)
		if err != nil {
			return nil, err
		}
		// sign前端传来的标记 1表示退出 2表示紧急揭盲 3表示pv揭盲
		sign := req.Sign

		// TODO 在随机
		stage := ""
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			if attribute.CohortID != primitive.NilObjectID {
				for _, env := range project.Environments {
					if env.ID == attribute.EnvironmentID {
						for _, cohort := range env.Cohorts {
							if cohort.ID == attribute.CohortID {
								stage = cohort.Name
								break
							}
						}
						break
					}
				}
			}
		}

		// 添加轨迹
		history := models.History{
			OID:  id,
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: user.Name,
		}

		// 获取时间
		shTime, err := tools.GetTimeZoneTime(time.Now().UTC(), projectSite, project)
		if err != nil {
			return nil, err
		}

		var noticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": environment.ID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		langList := make([]string, 0)
		if noticeConfig.Manual != 0 {
			if noticeConfig.Manual == 1 {
				langList = append(langList, "zh")
			} else if noticeConfig.Manual == 2 {
				langList = append(langList, "en")
			} else if noticeConfig.Manual == 3 {
				langList = append(langList, "zh")
				langList = append(langList, "en")
			}
		} else {
			langList = append(langList, ctx.GetHeader("Accept-Language"))
		}

		switch sign {
		case "1":
			{ // 退出
				if !(subject.Status == 3 || subject.Status == 1 || subject.Status == 6 || subject.Status == 7) {
					return nil, tools.BuildServerError(ctx, "subject_status_no_sign_out")
				}
				outTime := time.Duration(time.Now().Unix())
				subjectUpdate := bson.M{
					"$set": bson.M{
						"status":             4,
						"sign_out_time":      outTime,
						"sign_out_real_time": req.SignOutRealTime,
						"sign_out_people":    user.ID,
						"sign_out_reason":    req.Reason}}
				if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": id}, subjectUpdate); err != nil {
					return nil, errors.WithStack(err)
				}

				//if project.ResearchAttribute == 1 {
				//	// DTP 模式下 受试者退出 取消申请状态下的订单
				//	err := cancelOrder(ctx, sctx, id, subject.ProjectSiteID)
				//	if err != nil {
				//		return nil, errors.WithStack(err)
				//	}
				//}

				history.Data = map[string]interface{}{
					"label":           attribute.AttributeInfo.SubjectReplaceText,
					"name":            subject.Info[0].Value,
					"reason":          req.Reason,
					"signOutRealTime": req.SignOutRealTime,
					"stage":           stage,
				}
				//发送邮件
				userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.signOut", subject.ProjectSiteID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if len(userMail) > 0 {
					subjectData := bson.M{
						"projectNumber": project.Number,
						"envName":       environment.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      siteName,
						"siteNameEn":    siteNameEn,
					}
					randomNumber := tools.BlindData
					if attribute.AttributeInfo.IsRandomNumber {
						randomNumber = subject.RandomNumber
					}

					//strTimeZone := projectSite.TimeZone
					//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
					//stopTime := time.Unix(int64(outTime), 0).UTC().Add(time.Hour * time.Duration(intTimeZone)).Format("2006-01-02 15:04:05")
					strTimeZone, err := tools.GetSiteTimeZoneInfo(projectSite)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					if strTimeZone == "" {
						tz, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
						strTimeZone = tz
					}
					offset, _ := tools.ParseTimezoneOffset(strTimeZone)
					stopTime := tools.FormatFloatTime(fmt.Sprintf("%f", offset), "", "", int64(outTime), "2006-01-02 15:04:05")
					stopTime = stopTime + "(" + strTimeZone + ")"

					data := map[string]interface{}{
						"projectNumber": project.Number,
						"projectName":   project.Name,
						"envName":       environment.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      siteName,
						"siteNameEn":    siteNameEn,
						"label":         subjectEmailReplaceTextZh,
						"labelEn":       subjectEmailReplaceTextEn,
						"subjectNumber": subject.Info[0].Value,
						"randomNumber":  randomNumber,
						"group":         subject.Group,
						"subGroup":      subject.SubGroupName,
						"parName":       subject.ParGroupName,
						"stopTime":      stopTime,
						"reason":        req.Reason,
					}
					if !cohort.ID.IsZero() {
						data["cohortName"] = models.GetCohortReRandomName(cohort)
					}

					var mails []models.Mail
					for _, email := range userMail {
						insertData := make(map[string]interface{})
						for k, v := range data {
							insertData[k] = v
						}

						if subject.SubGroupName != "" {
							var randomList models.RandomList
							err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": subject.RandomListID}).Decode(&randomList)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							groupP, b2 := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
								return subject.ParGroupName == item.ParName && subject.SubGroupName == item.SubName
							})
							if b2 {
								insertData["subGroupBlind"] = groupP.Blind
							}
						}

						bodyContentKeys, contentKeys, err := tools.MailCustomContent(ctx, subject.EnvironmentID, "notice.subject.signOut", insertData, map[string]bool{})
						if err != nil {
							return nil, errors.WithStack(err)
						}

						//这里因为要固定顺序没办法在bodyContent合Content里面处理group只能用模板处理
						_, b := slice.Find(bodyContentKeys, func(index int, item models.ContentKey) bool {
							return item.Key == "dispensing.group"
						})
						if b {
							bodyContentKeys = slice.Filter(bodyContentKeys, func(index int, item models.ContentKey) bool {
								return item.Key != "dispensing.group"
							})
						}

						if email.IsBlind && blind {
							insertData["group"] = tools.BlindData
						} else {
							if insertData["subGroup"] != nil && insertData["subGroup"] != "" {
								insertData["group"] = insertData["parName"]
							} else {
								insertData["group"] = subject.Group
							}
						}

						if insertData["subGroupBlind"] != nil && insertData["subGroupBlind"].(bool) {
							if email.IsBlind {
								insertData["subGroup"] = tools.BlindData
							} else {
								insertData["subGroup"] = subject.SubGroupName
							}
						}

						contentTemplate := ""
						//受试者登记/筛选成功状态下的停用，受试者停用邮件中组别字段应该隐藏
						if b && subject.Status != 1 && subject.Status != 7 {
							if attribute.AttributeInfo.IsRandomNumber && subject.SubGroupName == "" {
								contentTemplate = "subject.signOut.content"
							} else if attribute.AttributeInfo.IsRandomNumber && subject.SubGroupName != "" {
								contentTemplate = "subject.signOut.content_sub"
							} else if !attribute.AttributeInfo.IsRandomNumber && subject.SubGroupName == "" {
								contentTemplate = "subject.signOut.content_no_random_number"
							} else if !attribute.AttributeInfo.IsRandomNumber && subject.SubGroupName != "" {
								contentTemplate = "subject.signOut.content_no_random_number_sub"
							}
						} else {
							if attribute.AttributeInfo.IsRandomNumber {
								contentTemplate = "subject.signOut.content_no_group"
								if subject.Status == 1 || subject.Status == 7 {
									contentTemplate = "subject.signOut.content_no_random_number_no_group"
								}
							} else {
								contentTemplate = "subject.signOut.content_no_random_number_no_group"
							}
						}
						var toUserMail []string
						toUserMail = append(toUserMail, email.Email)
						mails = append(mails, models.Mail{
							ID:             primitive.NewObjectID(),
							Subject:        "subject.signOut.title",
							SubjectData:    subjectData,
							ContentKey:     contentKeys,
							Content:        contentTemplate,
							ContentData:    insertData,
							BodyContentKey: bodyContentKeys,
							To:             toUserMail,
							Lang:           ctx.GetHeader("Accept-Language"),
							LangList:       langList,
							Status:         0,
							CreatedTime:    time.Duration(time.Now().Unix()),
							ExpectedTime:   time.Duration(time.Now().Unix()),
							SendTime:       time.Duration(time.Now().Unix()),
						})
					}
					ctx.Set("MAIL", mails)
					if len(mails) > 0 {
						var envs []models.MailEnv
						for _, m := range mails {
							envs = append(envs, models.MailEnv{
								ID:         primitive.NewObjectID(),
								MailID:     m.ID,
								CustomerID: project.CustomerID,
								ProjectID:  project.ID,
								EnvID:      subject.EnvironmentID,
								CohortID:   subject.CohortID,
							})
						}
						ctx.Set("MAIL-ENV", envs)
					}
				}

				// TODO 在随机
				if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
					history.Key = "history.subject.label.at-random-signOut"
					if req.SignOutRealTime != "" {
						history.Key = "history.subject.label.at-random-signOutReal"
					}
				} else {
					history.Key = "history.subject.label.signOut"
					if req.SignOutRealTime != "" {
						history.Key = "history.subject.label.signOutReal"
					}
				}
			}
		case "2":
			{ // 紧急揭盲（修改紧急揭盲状态为已揭盲，修改受试者状态为已揭盲）
				if subject.Status != 3 && subject.Status != 4 && subject.Status != 5 && subject.Status != 9 {
					return nil, tools.BuildServerError(ctx, "subject_status_no_unblinding")
				}
				// 检测揭盲密码输入是否正确
				password := req.Password
				remark := ""
				err := tools.PasswordDetection(ctx, user.Email, password)
				if err != nil {
					return nil, err
				}
				// 是否通知申办方
				isSponsor := "No"
				updateRemark := ""
				if req.IsSponsor { //是
					isSponsor = "Yes"
				} else { //否
					remark = req.Remark
					updateRemark = req.Remark
				}
				subjectUpdate := bson.M{
					"$set": bson.M{
						"urgent_unblinding_status":     1,
						"status":                       6,
						"urgent_unblinding_time":       time.Duration(time.Now().Unix()),
						"urgent_unblinding_people":     user.ID,
						"urgent_unblinding_reason":     req.Reason,
						"urgent_unblinding_reason_str": req.ReasonStr,
						"is_sponsor":                   req.IsSponsor,
						"remark":                       updateRemark}}

				if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": id}, subjectUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
				//项目动态
				{
					now := time.Duration(time.Now().Unix())
					siteName := tools.GetProjectSiteLangName(projectSite, "zh")
					siteNameEn := tools.GetProjectSiteLangName(projectSite, "en")
					OID := subject.EnvironmentID
					if !subject.CohortID.IsZero() {
						OID = subject.CohortID
					}
					dynamics := models.ProjectDynamics{
						ID:          primitive.NewObjectID(),
						Operator:    user.ID,
						OID:         OID,
						Time:        now,
						SceneTran:   "project_dynamics_scene_unblinding",
						TypeTran:    "project_dynamics_type_emergency_unblinding",
						ContentTran: "project_dynamics_content_emergency_unblinding_emergency",
						ContentData: map[string]interface{}{
							"subjectId":   subject.ID,
							"subjectName": subject.Info[0].Value,
							"siteId":      projectSite.ID,
							"siteName":    siteName,
							"siteNameEn":  siteNameEn,
						},
					}
					_, err := tools.Database.Collection("project_dynamics").InsertOne(sctx, dynamics)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

				history.Data = map[string]interface{}{"label": subjectReplaceText, "name": subject.Info[0].Value, "reasonStr": req.ReasonStr, "reason": req.Reason, "isSponsor": isSponsor, "remark": remark, "stage": stage}
				// TODO 在随机
				if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
					history.Key = "history.subject.label.at-random-unblinding"
				} else {
					history.Key = "history.subject.label.unblinding"
				}

				// 揭盲发送邮件
				userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.unblinding", subject.ProjectSiteID)
				if err != nil {
					return nil, err
				}
				var mails []models.Mail
				if len(userMail) > 0 {
					mailSubject := "subject.unblinding.content"
					if unblindingControl == 1 {
						mailSubject = "subject.unblinding.controlContent"
					}
					for _, userEmail := range userMail {
						var toUserMail []string
						toUserMail = append(toUserMail, userEmail.Email)
						mail := models.Mail{
							ID:           primitive.NewObjectID(),
							To:           toUserMail,
							Lang:         ctx.GetHeader("Accept-Language"),
							LangList:     langList,
							Status:       0,
							CreatedTime:  time.Duration(time.Now().Unix()),
							ExpectedTime: time.Duration(time.Now().Unix()),
							SendTime:     time.Duration(time.Now().Unix()),
						}
						mail.Subject = "subject.unblinding.title"
						mail.SubjectData = map[string]interface{}{"projectNumber": project.Number, "envName": environment.Name, "siteNumber": projectSite.Number, "siteName": siteName, "siteNameEn": siteNameEn}
						mail.Content = mailSubject
						randomNumber := tools.BlindData
						if attribute.AttributeInfo.IsRandomNumber {
							randomNumber = subject.RandomNumber
						}
						reason := req.ReasonStr
						if req.Reason != "" {
							reason = req.ReasonStr + " " + req.Reason
						}
						contentData := map[string]interface{}{"label": subjectEmailReplaceTextZh, "labelEn": subjectEmailReplaceTextEn, "projectNumber": project.Number, "projectName": project.Name, "envName": environment.Name, "siteNumber": projectSite.Number, "siteName": siteName, "siteNameEn": siteNameEn, "subjectNumber": subject.Info[0].Value, "randomNumber": randomNumber, "time": shTime, "reason": reason, "isSponsor": isSponsor, "remark": remark}
						if !cohort.ID.IsZero() {
							contentData["cohortName"] = models.GetCohortReRandomName(cohort)
						}
						bodyContentKeys, _, err := tools.MailCustomContent(ctx, environment.ID, "notice.subject.unblinding", contentData, nil)
						if err != nil {
							return nil, err
						}
						mail.ContentData = contentData
						mail.BodyContentKey = bodyContentKeys
						mails = append(mails, mail)
					}
				}
				ctx.Set("MAIL", mails)
				if len(mails) > 0 {
					var envs []models.MailEnv
					for _, m := range mails {
						envs = append(envs, models.MailEnv{
							ID:         primitive.NewObjectID(),
							MailID:     m.ID,
							CustomerID: subject.CustomerID,
							ProjectID:  subject.ProjectID,
							EnvID:      subject.EnvironmentID,
							CohortID:   subject.CohortID,
						})
					}
					ctx.Set("MAIL-ENV", envs)
				}
			}
		case "3":
			{ // pv揭盲
				if subject.Status != 3 && subject.Status != 4 && subject.Status != 5 && subject.Status != 6 && subject.Status != 9 {
					return nil, tools.BuildServerError(ctx, "subject_status_no_unblinding")
				}
				// 检测揭盲密码输入是否正确
				password := req.Password
				err = tools.PasswordDetection(ctx, user.Email, password)
				if err != nil {
					return nil, err
				}
				subjectUpdate := bson.M{
					"$set": bson.M{
						"pv_unblinding_status":     1,
						"pv_unblinding_time":       time.Duration(time.Now().Unix()),
						"pv_unblinding_people":     user.ID,
						"pv_unblinding_reason":     req.Reason,
						"pv_unblinding_reason_str": req.ReasonStr,
					}}
				if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": id}, subjectUpdate); err != nil {
					return nil, errors.WithStack(err)
				}

				history.Data = map[string]interface{}{"label": subjectReplaceText, "name": subject.Info[0].Value, "reason": req.ReasonStr, "remark": req.Reason, "stage": stage}
				// TODO 在随机
				if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
					history.Key = "history.subject.label.at-random-pvUnblinding"
				} else {
					history.Key = "history.subject.label.pvUnblinding"
				}
				configP, ok := slice.Find(attribute.AttributeInfo.UnblindingReasonConfig, func(index int, item models.UnblindingReasonConfig) bool {
					return item.Reason == req.ReasonStr
				})
				config := *configP
				if ok && config.AllowRemark {
					// TODO 在随机
					if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
						history.Key = "history.subject.label.at-random-remark-pvUnblinding"
					} else {
						history.Key = "history.subject.label.remark-pvUnblinding"
					}
				}
			}
		case "4":
			{ // 完成研究
				if attribute.AttributeInfo.Random && !(subject.Status == 3 || subject.Status == 6) ||
					(!attribute.AttributeInfo.Random && !(subject.Status == 1 || subject.Status == 7)) {
					return nil, tools.BuildServerError(ctx, "subject_status_error")
				}
				subjectUpdate := bson.M{
					"$set": bson.M{
						"status":        9,
						"finish_time":   time.Duration(time.Now().Unix()),
						"finish_people": user.ID,
						"finish_remark": req.FinishRemark}}
				if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": id}, subjectUpdate); err != nil {
					return nil, errors.WithStack(err)
				}

				history.Data = map[string]interface{}{
					"label":        attribute.AttributeInfo.SubjectReplaceText,
					"name":         subject.Info[0].Value,
					"remark":       req.FinishRemark,
					"randomNumber": subject.RandomNumber,
					"stage":        stage,
				}
				// TODO 在随机
				if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
					history.Key = "history.subject.label.at-random-finish"
				} else {
					history.Key = "history.subject.label.finish"
				}
			}
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, history)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// GetSubjectReplaceSite 受试者替换获取当前中心是否有该受试者
func (s *SubjectService) GetSubjectReplaceSite(ctx *gin.Context) (bool, error) {
	d := struct {
		EnvId         primitive.ObjectID `json:"envId"`
		ProjectSiteId primitive.ObjectID `json:"projectSiteId"`
		ShortName     string             `json:"shortName"`
	}{}

	_ = ctx.ShouldBindBodyWith(&d, binding.JSON)
	newFilter := bson.M{"env_id": d.EnvId, "info.0.value": d.ShortName, "deleted": bson.M{"$ne": true}}
	var newSubject models.Subject
	err := tools.Database.Collection("subject").FindOne(ctx, newFilter).Decode(&newSubject)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}
	if newSubject.ID.IsZero() {
		return false, tools.BuildServerError(ctx, "subject_no_register")
	} else if !newSubject.ID.IsZero() && newSubject.ProjectSiteID != d.ProjectSiteId {
		return false, nil
	}
	return true, nil
}

func (s *SubjectService) GetSubjectReplaceFactor(ctx *gin.Context) (bool, error) {
	d := make(map[string]interface{})
	_ = ctx.ShouldBindBodyWith(&d, binding.JSON)
	subjectId := d["subjectId"].(string)
	subjectOID, _ := primitive.ObjectIDFromHex(subjectId)

	_ = ctx.ShouldBindBodyWith(&d, binding.JSON)
	envId := d["envId"].(string)
	envOId, _ := primitive.ObjectIDFromHex(envId)

	shortname := d["shortName"].(string)
	var oldSubject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&oldSubject)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}
	//new一阶段
	newFilter := bson.M{"env_id": envOId, "info.0.value": shortname, "deleted": bson.M{"$ne": true}}
	var newSubject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, newFilter).Decode(&newSubject)
	if err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}
	project := models.Project{}
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": oldSubject.ProjectID}).Decode(&project)
	if err != nil {
		return false, errors.WithStack(err)
	}
	if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		var env models.Environment
		envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == envOId
		})
		env = *envP
		if env.Cohorts[0].ID == oldSubject.CohortID {
			b, err := noAtRandomSubjectFactor(oldSubject, newSubject)
			if err != nil {
				return false, errors.WithStack(err)
			}
			return b, nil
		} else {
			//查询第一阶段的替换的受试者
			var stage1OldSubject models.Subject
			err := tools.Database.Collection("subject").FindOne(nil, bson.M{"env_id": oldSubject.EnvironmentID, "cohort_id": env.Cohorts[0].ID, "info.0.value": oldSubject.Info[0].Value, "deleted": bson.M{"$ne": true}}).Decode(&stage1OldSubject)
			if err != nil && err != mongo.ErrNoDocuments {
				return false, errors.WithStack(err)
			}
			//查询被替换受试者
			newFilter := bson.M{"env_id": envOId, "info.0.value": shortname, "deleted": bson.M{"$ne": true}}
			newSubjects := make([]models.Subject, 0)
			cursor, err := tools.Database.Collection("subject").Find(nil, newFilter)
			if err != nil && err != mongo.ErrNoDocuments {
				return false, errors.WithStack(err)
			}
			err = cursor.All(nil, &newSubjects)
			if err != nil {
				return false, errors.WithStack(err)
			}

			//阶段一对比
			stage1OldInfos := slice.Filter(stage1OldSubject.Info, func(index int, item models.Info) bool {
				return item.Name != "shortname" && !strings.HasPrefix(item.Name, "field")
			})
			newInfos := slice.Filter(newSubject.Info, func(index int, item models.Info) bool {
				return item.Name != "shortname" && !strings.HasPrefix(item.Name, "field")
			})
			stage1Bool := true
			stage2Bool := true
			if len(stage1OldInfos) != len(newInfos) {
				stage1Bool = false
			} else {
				for _, old := range stage1OldInfos {
					find, b := slice.Find(newInfos, func(index int, item models.Info) bool {
						return item.Name == old.Name
					})
					if !b || find.Value != old.Value {
						stage1Bool = false
					}
				}
			}

			//阶段二对比
			stage2NewInfos := make([]models.Info, 0)
			for k, v := range d {
				if strings.HasPrefix(k, env.Cohorts[1].ID.Hex()+"factor") {
					factor := strings.Replace(k, env.Cohorts[1].ID.Hex(), "", -1)
					stage2NewInfos = append(stage2NewInfos, models.Info{
						Name:  factor,
						Value: v,
					})
				}
			}
			oldInfos := slice.Filter(oldSubject.Info, func(index int, item models.Info) bool {
				return item.Name != "shortname" && !strings.HasPrefix(item.Name, "field")
			})
			if len(oldInfos) != len(stage2NewInfos) {
				stage2Bool = false
			} else {
				for _, old := range oldInfos {
					find, b := slice.Find(stage2NewInfos, func(index int, item models.Info) bool {
						return item.Name == old.Name
					})
					if !b || find.Value != old.Value {
						stage2Bool = false
					}
				}
			}
			return stage1Bool && stage2Bool, nil
		}
	} else {
		b, err := noAtRandomSubjectFactor(oldSubject, newSubject)
		if err != nil {
			return false, errors.WithStack(err)
		}
		return b, nil
	}
}

func noAtRandomSubjectFactor(oldSubject models.Subject, newSubject models.Subject) (bool, error) {
	oldInfos := slice.Filter(oldSubject.Info, func(index int, item models.Info) bool {
		return item.Name != "shortname" && !strings.HasPrefix(item.Name, "field")
	})
	newInfos := slice.Filter(newSubject.Info, func(index int, item models.Info) bool {
		return item.Name != "shortname" && !strings.HasPrefix(item.Name, "field")
	})
	if len(oldInfos) != len(newInfos) {
		return false, nil
	} else {
		for _, old := range oldInfos {
			find, b := slice.Find(newInfos, func(index int, item models.Info) bool {
				return item.Name == old.Name
			})
			if !b || find.Value != old.Value {
				return false, nil
			}
		}
	}
	return true, nil
}

// SubjectReplace 受试者替换
func (s *SubjectService) SubjectReplace(ctx *gin.Context) error {
	d := make(map[string]interface{})
	_ = ctx.ShouldBindBodyWith(&d, binding.JSON)
	factorSign := false
	if d["factorSign"] != nil {
		factorSign = d["factorSign"].(bool)
	}
	subjectNumber := d["subjectNumber"].(string)
	randomNumber := ""
	if d["randomNumber"] != nil {
		randomNumber = d["randomNumber"].(string)
	}
	id := d["id"].(string)
	ID, _ := primitive.ObjectIDFromHex(id)
	attributeId := d["attributeId"].(string)
	replaceOID := primitive.NilObjectID
	u, _ := ctx.Get("user")
	user := u.(models.User)
	// 查询被替换的受试者数据
	subjectFilter := bson.M{"_id": ID}
	var beReplaceSubject models.Subject
	_ = tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&beReplaceSubject)
	if beReplaceSubject.Status != 3 {
		return tools.BuildServerError(ctx, "subject_status_no_replace")
	}

	// project
	var project models.Project
	projectFilter := bson.M{"_id": beReplaceSubject.ProjectID}
	err := tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	attribute, err := database.GetAttribute(nil, attributeId)
	if err != nil {
		return err
	}
	if attribute.AttributeInfo.ReplaceRule == 1 && (randomNumber == "" || randomNumber == tools.BlindData) {
		randomNumber, err = sysRandomNumber(beReplaceSubject, attribute)
		if err != nil {
			return err
		}
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
	subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

	// 查询条件
	filter := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "deleted": bson.M{"$ne": true}}
	if beReplaceSubject.CohortID != primitive.NilObjectID {
		filter = bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID}
	}

	var subjectAll []models.Subject
	subjectAllCursor, err := tools.Database.Collection("subject").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)
	}
	if err = subjectAllCursor.All(nil, &subjectAll); err != nil {
		return errors.WithStack(err)
	}

	newFilter := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "info.0.value": subjectNumber, "deleted": bson.M{"$ne": true}}
	var newSubject models.Subject
	err = tools.Database.Collection("subject").FindOne(ctx, newFilter).Decode(&newSubject)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	if newSubject.CohortID != primitive.NilObjectID && newSubject.CohortID != beReplaceSubject.CohortID {
		if project.ProjectInfo.Type == 2 {
			//群组
			return tools.BuildServerError(ctx, "subject_replace_no_site_fail_cohort")
		} else if project.ProjectInfo.Type == 3 {
			//阶段
			return tools.BuildServerError(ctx, "subject_replace_no_site_fail_stage")
		}
	}

	// 筛选替换的受试者
	var replaceSubject models.Subject
	for _, sbj := range subjectAll {
		if sbj.Info[0].Value == subjectNumber {
			replaceSubject = sbj
			break
		}
	}

	if attribute.AttributeInfo.IsScreen && replaceSubject.Status == 1 {
		return tools.BuildServerError(ctx, "subject_replace_register")
	}

	if replaceSubject.Status == 8 {
		return tools.BuildServerError(ctx, "subject_replace_no_register_screenFail")
	}
	if replaceSubject.Status == 4 || replaceSubject.Status == 5 {
		return tools.BuildServerError(ctx, "subject_replace_sign_out")
	}

	if replaceSubject.Status == 3 || replaceSubject.Status == 6 || replaceSubject.Status == 9 {
		return tools.BuildServerError(ctx, "subject_replace_random")
	}

	// 录入分层因素
	randomFilter := bson.M{
		"customer_id": replaceSubject.CustomerID,
		"env_id":      replaceSubject.EnvironmentID,
		"status":      1,
		"$or": bson.A{
			bson.M{"site_ids": nil},
			bson.M{"site_ids": replaceSubject.ProjectSiteID},
		}}
	if replaceSubject.CohortID != primitive.NilObjectID {
		randomFilter = bson.M{
			"customer_id": replaceSubject.CustomerID,
			"env_id":      replaceSubject.EnvironmentID,
			"status":      1,
			"cohort_id":   replaceSubject.CohortID,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": replaceSubject.ProjectSiteID},
			}}
	}
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	// 编辑分层因素
	var subjectInfo = replaceSubject.Info
	if factorSign {
		for _, factor := range randomList.Design.Factors {
			// 标记数据库是否存在分层因素字段，如果为false表示没有则追加subjectInfo  true表示有则修改subjectInfo
			var bl = false
			for i := 0; i < len(subjectInfo); i++ {
				if factor.Name == subjectInfo[i].Name {
					if d[factor.Name] != nil {
						subjectInfo[i].Value = d[factor.Name]
					}

					bl = true
					break
				}
			}

			if !bl && d[factor.Name] != nil { // 追加
				subjectInfo = append(subjectInfo, models.Info{
					Name:  factor.Name,
					Value: d[factor.Name],
				})
			}
		}
	}

	replaceOID = replaceSubject.ID

	// 查询输入的随机号是否在系统中存在
	match := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "number": randomNumber}
	if beReplaceSubject.CohortID != primitive.NilObjectID {
		match = bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID, "number": randomNumber}
	}
	var randomNumbers []models.RandomNumber
	randomNumberCursor, err := tools.Database.Collection("random_number").Find(nil, match)
	if err != nil {
		return errors.WithStack(err)
	}
	if err = randomNumberCursor.All(nil, &randomNumbers); err != nil {
		return errors.WithStack(err)
	}
	if randomNumbers != nil && len(randomNumbers) > 0 {
		if attribute.AttributeInfo.ReplaceRule == 1 {
			return tools.BuildServerError(ctx, "subject_replace_auto_error")
		}
		return tools.BuildServerError(ctx, "subject_random_number_existence")
	}
	for _, subject := range subjectAll {
		if subject.RandomNumber == randomNumber {
			if attribute.AttributeInfo.ReplaceRule == 1 {
				return tools.BuildServerError(ctx, "subject_replace_auto_error")
			}
			return tools.BuildServerError(ctx, "subject_random_number_existence")
		}
	}
	now := time.Now()
	nowDuration := time.Duration(now.Unix())
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 修改被替换的受试者状态为 '被替换'   // jira 983 记录替换后的受试者数据在被替换受试者上
		beReplacedSubjectUpdate := bson.M{"$set": bson.M{"status": 5, "replace_subject_id": replaceSubject.ID, "replace_subject": subjectNumber, "replace_number": randomNumber}}
		if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": beReplaceSubject.ID}, beReplacedSubjectUpdate); err != nil {
			return nil, errors.WithStack(err)
		}
		// 修改替换的受试者信息
		replacedSubjectUpdate := bson.M{"$set": bson.M{"info": subjectInfo, "random_number": randomNumber, "group": beReplaceSubject.Group, "par_group_name": beReplaceSubject.ParGroupName, "sub_group_name": beReplaceSubject.SubGroupName, "status": beReplaceSubject.Status, "random_time": time.Duration(time.Now().Unix()), "random_people": user.ID}}
		if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": replaceSubject.ID}, replacedSubjectUpdate); err != nil {
			return nil, errors.WithStack(err)
		}

		if beReplaceSubject.RandomListID != primitive.NilObjectID {
			// 当前受试者替换的随机号盲底表更新
			beReplacedSubjectRandomNumberUpdate := bson.M{"$set": bson.M{"replace_subject_id": replaceSubject.ID, "replace_subject": subjectNumber, "replace_number": randomNumber}}
			if _, err := tools.Database.Collection("random_number").UpdateOne(sctx, bson.M{"random_list_id": beReplaceSubject.RandomListID, "env_id": beReplaceSubject.EnvironmentID, "number": beReplaceSubject.RandomNumber}, beReplacedSubjectRandomNumberUpdate); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// 添加被替换受试者轨迹
		beReplaceHistory := models.History{
			OID: beReplaceSubject.ID,
			Key: "history.subject.label.replaced-new",
			Data: map[string]interface{}{
				"name":                  beReplaceSubject.Info[0].Value,
				"beReplaceRandomNumber": beReplaceSubject.RandomNumber,
				"replaceName":           replaceSubject.Info[0].Value,
				"label":                 subjectReplaceText,
				"replaceRandomNumber":   randomNumber},
			Time: nowDuration,
			UID:  user.ID,
			User: user.Name,
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, beReplaceHistory)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 添加替换受试者轨迹
		replaceHistory := models.History{
			OID: replaceSubject.ID,
			Key: "history.subject.label.replaced-new",
			Data: map[string]interface{}{
				"name":                  beReplaceSubject.Info[0].Value,
				"beReplaceRandomNumber": beReplaceSubject.RandomNumber,
				"replaceName":           replaceSubject.Info[0].Value,
				"label":                 subjectReplaceText,
				"replaceRandomNumber":   randomNumber},
			Time: nowDuration,
			UID:  user.ID,
			User: user.Name,
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, replaceHistory)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//发送邮件
		userMail, err := tools.GetRoleUsersMailWithRole(beReplaceSubject.ProjectID, beReplaceSubject.EnvironmentID, "notice.subject.replace", beReplaceSubject.ProjectSiteID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(userMail) > 0 {
			//environment
			var environment models.Environment
			var cohort models.Cohort
			for _, env := range project.Environments {
				if env.ID == beReplaceSubject.EnvironmentID {
					environment = env
					if env.Cohorts != nil && len(env.Cohorts) > 0 {
						for _, co := range env.Cohorts {
							if co.ID == beReplaceSubject.CohortID {
								cohort = co
								break
							}
						}
					} else {
						break
					}
				}
			}
			// site
			var projectSite models.ProjectSite
			err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": beReplaceSubject.ProjectSiteID}).Decode(&projectSite)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			//attribute
			var attribute models.Attribute
			attributeFilter := bson.M{"env_id": beReplaceSubject.EnvironmentID}
			if !beReplaceSubject.CohortID.IsZero() {
				attributeFilter = bson.M{"env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID}
			}
			err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			blind := attribute.AttributeInfo.Blind
			//时区
			strTimeZone, err := tools.GetSiteTimeZone(beReplaceSubject.ProjectSiteID)
			if err != nil {
				return nil, err
			}
			if strTimeZone == "" {
				zone, err := tools.GetTimeZone(beReplaceSubject.ProjectID)
				if err != nil {
					return nil, err
				}
				//strTimeZone = fmt.Sprintf("UTC%+d", zone)
				strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
			}
			//timeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
			timeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			dataTime := now.UTC().Add(duration).Format("2006-01-02 15:04:05")
			var showTimeZone string
			showTimeZone = tools.FormatOffsetToZoneStringUtc(timeZone)
			//if timeZone > 0 {
			//	showTimeZone = "+" + strconv.FormatInt(int64(timeZone), 10)
			//} else {
			//	showTimeZone = strconv.FormatInt(int64(timeZone), 10)
			//}
			dataTime = dataTime + " (" + showTimeZone + ")"

			// 查询条件
			filter := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID}
			if beReplaceSubject.CohortID != primitive.NilObjectID {
				filter = bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID}
			}
			var form models.Form
			_ = tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
			// 声明接受字段信息
			var fields []models.Field
			if form.Fields != nil {
				for _, fm := range form.Fields {
					if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && (fm.Status == nil || *fm.Status == 1) {
						fields = append(fields, fm)
					}
				}
			}
			factors := randomList.Design.Factors
			factors = slice.Filter(factors, func(index int, item models.RandomFactor) bool {
				if item.Status == nil {
					return true
				}
				return *item.Status != 2
			})
			// 查询访视周期
			var visitCycle models.VisitCycle
			err = tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			repSubject := make(map[string]interface{}, 0)
			for _, v := range subjectInfo {
				repSubject[v.Name] = v.Value
			}

			factorSign := LayeredBl(attribute, visitCycle, factors)
			if !factorSign {
				for i := 0; i < len(factors); i++ {
					if factors[i].IsCalc {
						fields, err = factorCalc(fields, factors[i], repSubject, ctx)
						if err != nil {
							return nil, err
						}
					} else {
						if repSubject[factors[i].Name] == nil {
							return nil, tools.BuildServerError(ctx, "subject_factor_no_null")
						}
						fields = append(fields, models.Field{
							Name:    factors[i].Name,
							Label:   factors[i].Label,
							Type:    factors[i].Type,
							Options: factors[i].Options,
						})
					}
				}
			}
			form.Fields = fields

			form.Fields = fields
			var forms []map[string]interface{}

			for i := 0; i < len(form.Fields); i++ { //
				if form.Fields[i].IsCalc == true {
					option, b := slice.Find(form.Fields[i].Options, func(index int, item models.Option) bool {
						return item.Value == repSubject[form.Fields[i].Name]
					})
					if b {
						forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": option.Label})
					}
				} else {
					otherValue := "-"
					if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" { // 下拉框或者单选框
						if repSubject[form.Fields[i].Name] != nil {
							for _, option := range form.Fields[i].Options {
								if option.Value == repSubject[form.Fields[i].Name].(string) {
									otherValue = option.Label
								}
							}
						}
					} else if form.Fields[i].Type == "checkbox" { // 多选框
						if repSubject[form.Fields[i].Name] != nil {
							var checkboxBf bytes.Buffer
							str := repSubject[form.Fields[i].Name].([]interface{})
							for _, option := range form.Fields[i].Options {
								for j := 0; j < len(str); j++ {
									if option.Value == str[j].(string) {
										checkboxBf.WriteString(option.Label)
										checkboxBf.WriteString(",")
									}
								}
							}
							otherValue = checkboxBf.String()
						}
					} else if form.Fields[i].Type == "switch" { // 开关
						otherValue = "no"
						if repSubject[form.Fields[i].Name] != nil {
							if repSubject[form.Fields[i].Name] == true {
								otherValue = "yes"
							}
						}
					} else if form.Fields[i].Type == "inputNumber" { // 其它
						if repSubject[form.Fields[i].Name] != nil {
							if form.Fields[i].FormatType == "decimalLength" && form.Fields[i].Length != nil {
								lengthString := strconv.FormatFloat(*form.Fields[i].Length, 'f', -1, 64)
								if strings.Contains(lengthString, ".") {
									digits, _ := getFractionDigits(*form.Fields[i].Length)
									str := strconv.FormatFloat(repSubject[form.Fields[i].Name].(float64), 'f', digits, 64)
									otherValue = str
								} else {
									otherValue = convertor.ToString(repSubject[form.Fields[i].Name])
								}
							} else {
								otherValue = convertor.ToString(repSubject[form.Fields[i].Name])
							}
						}
					} else if form.Fields[i].Type == "datePicker" { // 日期选择器
						dateFormat := "YYYY-MM-DD"
						if form.Fields[i].DateFormat != "" {
							dateFormat = form.Fields[i].DateFormat
						}
						if repSubject[form.Fields[i].Name] != nil {
							otherValue = fmt.Sprint(repSubject[form.Fields[i].Name])
							if repSubject[form.Fields[i].Name] != "" {
								parse, err := time.Parse("2006-01-02", otherValue)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								otherValue = parse.Format(tools.DateFormatParse(dateFormat))
							}
						}
					} else if form.Fields[i].Type == "timePicker" {

						timeFormat := "YYYY-MM-DD HH:mm:ss"
						if form.Fields[i].TimeFormat != "" {
							timeFormat = form.Fields[i].TimeFormat
						}
						if repSubject[form.Fields[i].Name] != nil {
							otherValue = fmt.Sprint(repSubject[form.Fields[i].Name])
							if repSubject[form.Fields[i].Name] != "" {
								parse, err := time.Parse("2006-01-02 15:04:05", otherValue)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								otherValue = parse.Format(tools.DateFormatParse(timeFormat))
							}
						}
					} else {
						if repSubject[form.Fields[i].Name] != nil {
							otherValue = repSubject[form.Fields[i].Name].(string)
						}
					}
					forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": otherValue})
				}
			}

			subjectData := bson.M{
				"projectNumber": project.Number,
				"envName":       environment.Name,
				"siteNumber":    projectSite.Number,
				"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
				"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
			}

			randomNumberShow := false
			if attribute.AttributeInfo.IsRandomNumber {
				randomNumberShow = true
			} else {
				randomNumber = tools.BlindData
			}

			contentData := bson.M{
				"projectNumber":    project.Number,
				"projectName":      project.Name,
				"envName":          environment.Name,
				"siteNumber":       projectSite.Number,
				"siteName":         tools.GetProjectSiteLangName(projectSite, "zh"),
				"siteNameEn":       tools.GetProjectSiteLangName(projectSite, "en"),
				"label":            subjectEmailReplaceTextZh,
				"labelEn":          subjectEmailReplaceTextEn,
				"subjectNumber":    beReplaceSubject.Info[0].Value,
				"randomNumberShow": randomNumberShow,
				"randomNumber":     beReplaceSubject.RandomNumber,
				"group":            beReplaceSubject.Group,
				"subGroup":         beReplaceSubject.SubGroupName,
				"parName":          beReplaceSubject.ParGroupName,
				//"replaceSubjectID":     subjectReplaceText,
				"replaceSubjectNumber": replaceSubject.Info[0].Value,
				"replaceRandomNumber":  randomNumber,
				"replaceTime":          dataTime,
				"forms":                forms,
			}
			if !cohort.ID.IsZero() {
				contentData["cohortName"] = models.GetCohortReRandomName(cohort)
			}

			if beReplaceSubject.SubGroupName != "" {
				var randomList models.RandomList
				err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": beReplaceSubject.RandomListID}).Decode(&randomList)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				groupP, b2 := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
					return beReplaceSubject.ParGroupName == item.ParName && beReplaceSubject.SubGroupName == item.SubName
				})
				if b2 {
					contentData["subGroupBlind"] = groupP.Blind
				}
			}

			mailBodyContet, err := tools.MailBodyContent(nil, beReplaceSubject.EnvironmentID, "notice.subject.replace")
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for key, v := range mailBodyContet {
				contentData[key] = v
			}

			var mails []models.Mail
			for _, email := range userMail {
				insertData := make(map[string]interface{})
				for key, v := range contentData {
					insertData[key] = v
				}
				if email.IsBlind && blind {
					insertData["group"] = tools.BlindData
				} else {
					if insertData["subGroup"] != nil && insertData["subGroup"] != "" {
						insertData["group"] = insertData["parName"]
					} else {
						insertData["group"] = beReplaceSubject.Group
					}
				}
				if insertData["subGroupBlind"] != nil && insertData["subGroupBlind"].(bool) {
					if email.IsBlind {
						insertData["subGroup"] = tools.BlindData
					} else {
						insertData["subGroup"] = beReplaceSubject.SubGroupName
					}
				}
				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": beReplaceSubject.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				langList := make([]string, 0)
				html := "subject_replacement_zh_en.html"
				if noticeConfig.Manual != 0 {
					if noticeConfig.Manual == 1 {
						langList = append(langList, "zh")
						html = "subject_replacement_zh.html"
					} else if noticeConfig.Manual == 2 {
						langList = append(langList, "en")
						html = "subject_replacement_en.html"
					} else if noticeConfig.Manual == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						html = "subject_replacement_zh_en.html"
					}
				} else {
					langList = append(langList, ctx.GetHeader("Accept-Language"))
					if locales.Lang(ctx) == "zh" {
						html = "subject_replacement_zh.html"
					} else if locales.Lang(ctx) == "en" {
						html = "subject_replacement_en.html"
					}
				}
				var toUserMail []string
				toUserMail = append(toUserMail, email.Email)
				mails = append(mails, models.Mail{
					ID:           primitive.NewObjectID(),
					Subject:      "subject.replacement.title",
					SubjectData:  subjectData,
					ContentData:  insertData,
					HTML:         html,
					To:           toUserMail,
					Lang:         ctx.GetHeader("Accept-Language"),
					LangList:     langList,
					Status:       0,
					CreatedTime:  time.Duration(time.Now().Unix()),
					ExpectedTime: time.Duration(time.Now().Unix()),
					SendTime:     time.Duration(time.Now().Unix()),
				})
			}
			ctx.Set("MAIL", mails)
			if len(mails) > 0 {
				var envs []models.MailEnv
				for _, m := range mails {
					envs = append(envs, models.MailEnv{
						ID:         primitive.NewObjectID(),
						MailID:     m.ID,
						CustomerID: beReplaceSubject.CustomerID,
						ProjectID:  beReplaceSubject.ID,
						EnvID:      beReplaceSubject.EnvironmentID,
						CohortID:   beReplaceSubject.CohortID,
					})
				}
				ctx.Set("MAIL-ENV", envs)
			}
		}

		// 更新subject访视
		//  更新受试者访视数据
		err = updateDispensing(ctx, sctx, replaceSubject, beReplaceSubject.Group)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 更新冻结的受试者药物
		err = replaceMedicine(ctx, sctx, beReplaceSubject.ID, replaceSubject.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}

	// 查询项目
	//projectFilter := bson.M{"_id": beReplaceSubject.ProjectID}
	//var project models.Project
	//tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	//err = tools.Database.Collection("project").FindOne(ctx, projectFilter).Decode(&project)
	//if err != nil && err != mongo.ErrNoDocuments {
	//	return errors.WithStack(err)
	//}

	// 推给EDC
	if tools.PushScenarioFilter(project.ProjectInfo.ConnectEdc, project.ProjectInfo.PushMode, project.ProjectInfo.EdcSupplier, project.ProjectInfo.PushScenario.RandomPush) {
		replaceSubjectId := beReplaceSubject.ID.Hex()
		replaceSubjectNo := ""
		for _, info := range beReplaceSubject.Info {
			if info.Name == "shortname" {
				replaceSubjectNo = info.Value.(string)
			}
		}
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectRandomPush(logData, replaceOID, 11, nowDuration, replaceSubjectId, replaceSubjectNo, "")
	}
	//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyRandom" || project.PushTypeEdc == "RandomAndDrug") {
	//	replaceSubjectId := beReplaceSubject.ID.Hex()
	//	replaceSubjectNo := ""
	//	for _, info := range beReplaceSubject.Info {
	//		if info.Name == "shortname" {
	//			replaceSubjectNo = info.Value.(string)
	//		}
	//	}
	//	SubjectRandomPush(ctx, replaceOID, 11, nowDuration, replaceSubjectId, replaceSubjectNo)
	//}
	return nil
}

// 在随机的受试者替换
func (s *SubjectService) SubjectReplaceAtRandom(ctx *gin.Context) error {
	d := make(map[string]interface{})
	_ = ctx.ShouldBindBodyWith(&d, binding.JSON)
	factorSign := false
	if d["factorSign"] != nil {
		factorSign = d["factorSign"].(bool)
	}
	id := d["id"].(string)
	ID, _ := primitive.ObjectIDFromHex(id)
	attributeId := d["attributeId"].(string)
	u, _ := ctx.Get("user")
	user := u.(models.User)

	now := time.Now()
	nowDuration := time.Duration(now.Unix())

	// 查询被替换的受试者数据
	subjectFilter := bson.M{"_id": ID}
	var subject models.Subject
	_ = tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&subject)
	if subject.Status != 3 {
		return tools.BuildServerError(ctx, "subject_status_no_replace")
	}

	// project
	var project models.Project
	projectFilter := bson.M{"_id": subject.ProjectID}
	err := tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	// 筛选在随机阶段
	var environment models.Environment
	var cohorts []models.Cohort
	for _, env := range project.Environments {
		if env.ID == subject.EnvironmentID {
			environment = env
			cohorts = env.Cohorts
			break
		}
	}

	// 查询被替换的受试者一共有几个阶段
	count, _ := tools.Database.Collection("subject").CountDocuments(nil,
		bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": subject.Info[0].Value,
				},
			},
			"deleted": bson.M{"$ne": true},
		},
	)

	subjectNumber := d["subjectNumber"].(string)
	//校验替换的受试者状态是否正确
	var rleSubject models.Subject
	rleFilter := bson.M{
		"customer_id": subject.CustomerID,
		"project_id":  subject.ProjectID,
		"env_id":      subject.EnvironmentID,
		"cohort_id":   cohorts[0].ID,
		"info": bson.M{
			"$elemMatch": bson.M{
				"name":  "shortname",
				"value": subjectNumber,
			},
		},
		"deleted": bson.M{"$ne": true},
	}
	err = tools.Database.Collection("subject").FindOne(nil, rleFilter).Decode(&rleSubject)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	if err == mongo.ErrNoDocuments {
		return tools.BuildServerError(ctx, "subject_no_register")
	}
	// 如果不是已登记的状态报错
	if rleSubject.Status != 1 && rleSubject.Status != 7 {
		return tools.BuildServerError(ctx, "subject_status_no_replace")
	}
	lastGroup := ""
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		//替换结构体
		type ReplaceSubjectTrail struct {
			BeReplaceSubjectID     primitive.ObjectID `json:"beReplaceSubjectID"`
			ReplaceSubjectID       primitive.ObjectID `json:"replaceSubjectID"`
			Label                  string             `json:"label"`
			Name                   string             `json:"name"`
			ReplaceName            string             `json:"replaceName"`
			Stage                  string             `json:"stage"`
			BeReplaceRandomNumber  string             `json:"beReplaceRandomNumber"`
			ReplaceRandomNumber    string             `json:"replaceRandomNumber"`
			Stage2                 string             `json:"stage2"`
			BeReplaceRandomNumber2 string             `json:"beReplaceRandomNumber2"`
			ReplaceRandomNumber2   string             `json:"replaceRandomNumber2"`
		}
		var replaceSubjectTrail ReplaceSubjectTrail
		//发送邮件
		userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.replace", subject.ProjectSiteID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var mails []models.Mail
		contentData := bson.M{}
		for index, cohort := range cohorts {
			// 查询受试者
			var beReplaceSubject models.Subject
			beReplaceFilter := bson.M{
				"customer_id": subject.CustomerID,
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"cohort_id":   cohort.ID,
				"info": bson.M{
					"$elemMatch": bson.M{
						"name":  "shortname",
						"value": subject.Info[0].Value,
					},
				},
				"deleted": bson.M{"$ne": true},
			}
			err = tools.Database.Collection("subject").FindOne(sctx, beReplaceFilter).Decode(&beReplaceSubject)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			if beReplaceSubject.Info != nil && len(beReplaceSubject.Info) > 0 {
				randomNumber := ""

				if d[cohort.ID.Hex()+"randomNumber"] != nil {
					randomNumber = d[cohort.ID.Hex()+"randomNumber"].(string)
				}

				replaceOID := primitive.NilObjectID
				attribute, err := database.GetAttribute(nil, attributeId)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if attribute.AttributeInfo.ReplaceRule == 1 && (randomNumber == "" || randomNumber == tools.BlindData) {
					randomNumber, err = sysRandomNumber(beReplaceSubject, attribute)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

				subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

				subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
				subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

				//newFilter := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "project_site_id": beReplaceSubject.ProjectSiteID, "info.0.value": subjectNumber}
				//var newSubject models.Subject
				//err = tools.Database.Collection("subject").FindOne(ctx, newFilter).Decode(&newSubject)
				//if err != nil && err != mongo.ErrNoDocuments {
				//	return nil, errors.WithStack(err)
				//}
				//if newSubject.Info == nil {
				//	return nil, tools.BuildServerError(ctx, "subject_replace_no_site_fail")
				//} else {
				//	if newSubject.CohortID != primitive.NilObjectID && newSubject.CohortID != beReplaceSubject.CohortID {
				//		if project.ProjectInfo.Type == 2 {
				//			//群组
				//			return nil, tools.BuildServerError(ctx, "subject_replace_no_site_fail_cohort")
				//		} else if project.ProjectInfo.Type == 3 {
				//			//阶段
				//			return nil, tools.BuildServerError(ctx, "subject_replace_no_site_fail_stage")
				//		}
				//	}
				//}

				// 查询条件
				filter := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID, "deleted": bson.M{"$ne": true}}
				var subjectAll []models.Subject
				subjectAllCursor, err := tools.Database.Collection("subject").Find(nil, filter)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if err = subjectAllCursor.All(nil, &subjectAll); err != nil {
					return nil, errors.WithStack(err)
				}
				// 筛选替换的受试者
				var replaceSubject models.Subject
				for _, sbj := range subjectAll {
					if sbj.Info[0].Value == subjectNumber {
						replaceSubject = sbj
						break
					}
				}

				randomFilter := bson.M{
					"customer_id": subject.CustomerID,
					"env_id":      subject.EnvironmentID,
					"cohort_id":   cohort.ID,
					"status":      1,
					"$or": bson.A{
						bson.M{"site_ids": nil},
						bson.M{"site_ids": subject.ProjectSiteID},
					}}
				opts := &options.FindOneOptions{Sort: bson.D{{"meta.created_at", 1}}}
				var randomList models.RandomList
				err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter, opts).Decode(&randomList)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}

				// 如果不是第一阶段的需要添加受试者
				if replaceSubject.Info == nil {
					randomFilter := bson.M{
						"customer_id": subject.CustomerID,
						"env_id":      subject.EnvironmentID,
						"cohort_id":   cohort.ID,
						"last_group":  lastGroup,
						"status":      1,
						"$or": bson.A{
							bson.M{"site_ids": nil},
							bson.M{"site_ids": subject.ProjectSiteID},
						}}
					opts := &options.FindOneOptions{Sort: bson.D{{"meta.created_at", 1}}}
					var secRandomList models.RandomList
					err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter, opts).Decode(&secRandomList)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}

					var info []models.Info
					info = append(info, models.Info{
						Name:  "shortname",
						Value: subjectNumber,
					})

					status := 1
					if attribute.AttributeInfo.IsScreen {
						status = 7
					}
					subjectData := models.Subject{
						ID:                     primitive.NewObjectID(),
						CustomerID:             subject.CustomerID,
						ProjectID:              subject.ProjectID,
						EnvironmentID:          subject.EnvironmentID,
						CohortID:               cohort.ID,
						ProjectSiteID:          rleSubject.ProjectSiteID,
						LastGroup:              lastGroup,
						Info:                   info,
						Status:                 status,
						RegisterRandomListID:   secRandomList.ID, //直接设置随机id,替换之后，第二阶段是已随机状态
						PvUnblindingStatus:     0,
						UrgentUnblindingStatus: 0,
						Meta: models.Meta{
							CreatedAt: nowDuration,
							CreatedBy: user.ID,
						},
						UrgentUnblindingApprovals:   []models.UrgentUnblindingApproval{},
						PvUrgentUnblindingApprovals: []models.UrgentUnblindingApproval{},
					}

					replaceSubject.ID = subjectData.ID
					replaceSubject.CustomerID = subjectData.CustomerID
					replaceSubject.ProjectID = subjectData.ProjectID
					replaceSubject.EnvironmentID = subjectData.EnvironmentID
					replaceSubject.CohortID = subjectData.CohortID
					replaceSubject.ProjectSiteID = subjectData.ProjectSiteID
					replaceSubject.LastGroup = subjectData.LastGroup
					replaceSubject.Info = info
					replaceSubject.Status = subjectData.Status
					replaceSubject.PvUnblindingStatus = subjectData.PvUnblindingStatus
					replaceSubject.UrgentUnblindingStatus = subjectData.UrgentUnblindingStatus
					replaceSubject.Meta = subjectData.Meta
					replaceSubject.UrgentUnblindingApprovals = subjectData.UrgentUnblindingApprovals
					replaceSubject.PvUrgentUnblindingApprovals = subjectData.PvUrgentUnblindingApprovals

					result, err := tools.Database.Collection("subject").InsertOne(sctx, subjectData)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					_, err = tools.Database.Collection("subject_shortname").InsertOne(sctx, models.SubjectShortname{
						EnvID:     subjectData.EnvironmentID,
						CohortID:  subjectData.CohortID,
						Shortname: subjectData.Info[0].Value.(string),
					})
					if err != nil {
						return nil, errors.WithStack(err)
					}

					// 创建发药访视周期（只有发药项目才需要设置访视周期）
					visitFilter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": cohort.ID}
					var visitCycle models.VisitCycle
					err = tools.Database.Collection("visit_cycle").FindOne(sctx, visitFilter).Decode(&visitCycle)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
					if attribute.AttributeInfo.Dispensing {
						if visitCycle.Infos == nil || len(visitCycle.Infos) == 0 {
							return nil, tools.BuildServerError(ctx, "subject_no_visit")
						}
						dispensingVisit := make([]interface{}, len(visitCycle.Infos))
						serialNumber := 100
						var reasons []models.Reason
						for index, visit := range visitCycle.Infos {
							dispensingVisit[index] = models.Dispensing{
								ID:            primitive.NewObjectID(),
								CustomerID:    subjectData.CustomerID,
								ProjectID:     subjectData.ProjectID,
								EnvironmentID: subjectData.EnvironmentID,
								CohortID:      subjectData.CohortID,
								SubjectID:     result.InsertedID.(primitive.ObjectID),
								VisitInfo: models.VisitInfo{
									VisitCycleInfoID: visit.ID,
									Number:           visit.Number,
									InstanceRepeatNo: "0",
									BlockRepeatNo:    "0",
									Name:             visit.Name,
									Random:           visit.Random,
									Dispensing:       visit.Dispensing,
								},
								SerialNumber: serialNumber,
								VisitSign:    false,
								Status:       1,
								Reasons:      reasons,
							}
							serialNumber += 100
						}

						// 批量添加发药访视计划
						if _, err := tools.Database.Collection("dispensing").InsertMany(sctx, dispensingVisit); err != nil {
							return nil, errors.WithStack(err)
						}
					}
				} else {
					// 如果是发药项目需要把替换受试者的第一阶段发药访视修改为未参加 否则会导致第二阶段无法发药
					if count > 1 && attribute.AttributeInfo.Dispensing {
						visitUpdateFilter := bson.M{"subject_id": replaceSubject.ID, "status": 1, "visit_info.dispensing": true}
						visitUpdate := bson.M{"$set": bson.M{"status": 3}}
						if _, err := tools.Database.Collection("dispensing").UpdateMany(sctx, visitUpdateFilter, visitUpdate); err != nil {
							return nil, errors.WithStack(err)
						}
					}
				}

				if attribute.AttributeInfo.IsScreen && replaceSubject.Status == 1 {
					return nil, tools.BuildServerError(ctx, "subject_replace_register")
				}

				if replaceSubject.Status == 8 {
					return nil, tools.BuildServerError(ctx, "subject_replace_no_register_screenFail")
				}
				if replaceSubject.Status == 4 || replaceSubject.Status == 5 {
					return nil, tools.BuildServerError(ctx, "subject_replace_sign_out")
				}

				if replaceSubject.Status == 3 || replaceSubject.Status == 6 || replaceSubject.Status == 9 {
					return nil, tools.BuildServerError(ctx, "subject_replace_random")
				}

				// 编辑分层因素
				var subjectInfo = replaceSubject.Info

				if factorSign || cohorts[0].ID != replaceSubject.CohortID {
					for _, factor := range randomList.Design.Factors {
						// 标记数据库是否存在分层因素字段，如果为false表示没有则追加subjectInfo  true表示有则修改subjectInfo
						//var bl = false
						//for i := 0; i < len(subjectInfo); i++ {
						//	if factor.Name == subjectInfo[i].Name {
						//		if d[cohort.ID.Hex()+factor.Name] != nil {
						//			subjectInfo[i].Value = d[cohort.ID.Hex()+factor.Name]
						//		}
						//		bl = true
						//		break
						//	}
						//}

						//if !bl && d[cohort.ID.Hex()+factor.Name] != nil { // 追加
						//	subjectInfo = append(subjectInfo, models.Info{
						//		Name:  factor.Name,
						//		Value: d[cohort.ID.Hex()+factor.Name],
						//	})
						//}
						if d[cohort.ID.Hex()+factor.Name] != nil { // 追加
							subjectInfo = append(subjectInfo, models.Info{
								Name:  factor.Name,
								Value: d[cohort.ID.Hex()+factor.Name],
							})
						}
					}
					// 查询表单
					var form models.Form
					_ = tools.Database.Collection("form").FindOne(nil, bson.M{"cohort_id": replaceSubject.CohortID}).Decode(&form)
					for _, field := range form.Fields {
						if field.Status == nil || *field.Status == 1 {
							if d[cohort.ID.Hex()+field.Name] != nil { // 追加
								subjectInfo = append(subjectInfo, models.Info{
									Name:  field.Name,
									Value: d[cohort.ID.Hex()+field.Name],
								})
							}
						}
					}
				}
				replaceOID = replaceSubject.ID
				// 查询输入的随机号是否在系统中存在
				match := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID, "number": randomNumber}
				var randomNumbers []models.RandomNumber
				randomNumberCursor, err := tools.Database.Collection("random_number").Find(sctx, match)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if err = randomNumberCursor.All(nil, &randomNumbers); err != nil {
					return nil, errors.WithStack(err)
				}
				if randomNumbers != nil && len(randomNumbers) > 0 {
					if attribute.AttributeInfo.ReplaceRule == 1 {
						return nil, tools.BuildServerError(ctx, "subject_replace_auto_error")
					}
					return nil, tools.BuildServerError(ctx, "subject_random_number_existence")
				}
				for _, subject := range subjectAll {
					if subject.RandomNumber == randomNumber {
						if attribute.AttributeInfo.ReplaceRule == 1 {
							return nil, tools.BuildServerError(ctx, "subject_replace_auto_error")
						}
						return nil, tools.BuildServerError(ctx, "subject_random_number_existence")
					}
				}

				// 修改被替换的受试者状态为 '被替换'   // jira 983 记录替换后的受试者数据在被替换受试者上
				beReplacedSubjectUpdate := bson.M{"$set": bson.M{"status": 5, "replace_subject_id": replaceSubject.ID, "replace_subject": subjectNumber, "replace_number": randomNumber}}
				if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": beReplaceSubject.ID}, beReplacedSubjectUpdate); err != nil {
					return nil, errors.WithStack(err)
				}

				// 修改替换的受试者信息
				replacedSubjectUpdate := bson.M{"$set": bson.M{"info": subjectInfo, "random_number": randomNumber, "group": beReplaceSubject.Group, "par_group_name": beReplaceSubject.ParGroupName, "sub_group_name": beReplaceSubject.SubGroupName, "status": beReplaceSubject.Status, "random_time": nowDuration, "random_people": user.ID}}
				if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": replaceSubject.ID}, replacedSubjectUpdate); err != nil {
					return nil, errors.WithStack(err)
				}
				// 记录上阶段随机组别
				lastGroup = beReplaceSubject.Group

				if beReplaceSubject.RandomListID != primitive.NilObjectID {
					// 当前受试者替换的随机号盲底表更新
					beReplacedSubjectRandomNumberUpdate := bson.M{"$set": bson.M{"replace_subject_id": replaceSubject.ID, "replace_subject": subjectNumber, "replace_number": randomNumber}}
					if _, err := tools.Database.Collection("random_number").UpdateOne(sctx, bson.M{"random_list_id": beReplaceSubject.RandomListID, "env_id": beReplaceSubject.EnvironmentID, "number": beReplaceSubject.RandomNumber}, beReplacedSubjectRandomNumberUpdate); err != nil {
						return nil, errors.WithStack(err)
					}
				}

				// 查询属性配置
				//var projectAttribute models.Attribute
				//err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"cohort_id": cohort.ID}).Decode(&projectAttribute)
				//if err != nil {
				//	return nil, errors.WithStack(err)
				//}

				replaceSubjectTrail.BeReplaceSubjectID = beReplaceSubject.ID
				replaceSubjectTrail.ReplaceSubjectID = replaceSubject.ID
				replaceSubjectTrail.Label = subjectReplaceText
				replaceSubjectTrail.Name = beReplaceSubject.Info[0].Value.(string)
				replaceSubjectTrail.ReplaceName = replaceSubject.Info[0].Value.(string)
				if index == 0 {
					replaceSubjectTrail.Stage = cohort.Name
					replaceSubjectTrail.BeReplaceRandomNumber = beReplaceSubject.RandomNumber
					replaceSubjectTrail.ReplaceRandomNumber = randomNumber
					//if !projectAttribute.AttributeInfo.IsRandomNumber {
					//	replaceSubjectTrail.BeReplaceRandomNumber = tools.BlindData
					//	replaceSubjectTrail.ReplaceRandomNumber = tools.BlindData
					//}
				} else {
					replaceSubjectTrail.Stage2 = cohort.Name
					replaceSubjectTrail.BeReplaceRandomNumber2 = beReplaceSubject.RandomNumber
					replaceSubjectTrail.ReplaceRandomNumber2 = randomNumber
					//if !projectAttribute.AttributeInfo.IsRandomNumber {
					//	replaceSubjectTrail.BeReplaceRandomNumber2 = tools.BlindData
					//	replaceSubjectTrail.ReplaceRandomNumber2 = tools.BlindData
					//}
				}

				if len(userMail) > 0 {
					//environment
					//var environment models.Environment
					//var cohort models.Cohort
					//for _, env := range project.Environments {
					//	if env.ID == beReplaceSubject.EnvironmentID {
					//		environment = env
					//		if env.Cohorts != nil && len(env.Cohorts) > 0 {
					//			for _, co := range env.Cohorts {
					//				if co.ID == beReplaceSubject.CohortID {
					//					cohort = co
					//					break
					//				}
					//			}
					//		} else {
					//			break
					//		}
					//	}
					//}

					//attribute
					var attribute models.Attribute
					attributeFilter := bson.M{"env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID}
					err = tools.Database.Collection("attribute").FindOne(sctx, attributeFilter).Decode(&attribute)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					// 查询条件
					formFilter := bson.M{"customer_id": beReplaceSubject.CustomerID, "project_id": beReplaceSubject.ProjectID, "env_id": beReplaceSubject.EnvironmentID, "cohort_id": beReplaceSubject.CohortID}
					var form models.Form
					_ = tools.Database.Collection("form").FindOne(sctx, formFilter).Decode(&form)
					// 声明接受字段信息
					var fields []models.Field
					factors := randomList.Design.Factors
					factors = slice.Filter(factors, func(index int, item models.RandomFactor) bool {
						if item.Status == nil {
							return true
						}
						return *item.Status != 2
					})
					// 查询访视周期
					var visitCycle models.VisitCycle
					err = tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}

					repSubject := make(map[string]interface{}, 0)
					for _, v := range subjectInfo {
						repSubject[v.Name] = v.Value
					}

					factorSign := LayeredBl(attribute, visitCycle, factors)

					if !factorSign {
						for i := 0; i < len(factors); i++ {
							if factors[i].IsCalc {
								fields, err = factorCalc(fields, factors[i], repSubject, ctx)
								if err != nil {
									return nil, err
								}
							} else {
								if repSubject[factors[i].Name] == nil {
									return nil, tools.BuildServerError(ctx, "subject_factor_no_null")
								}
								fields = append(fields, models.Field{
									Name:    factors[i].Name,
									Label:   factors[i].Label,
									Type:    factors[i].Type,
									Options: factors[i].Options,
								})
							}
						}
					}
					form.Fields = fields
					//form.Fields = fields
					var forms []map[string]interface{}
					for i := 0; i < len(form.Fields); i++ { //
						if form.Fields[i].IsCalc == true {
							option, b := slice.Find(form.Fields[i].Options, func(index int, item models.Option) bool {
								return item.Value == repSubject[form.Fields[i].Name]
							})
							if b {
								forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": option.Label})
							}
						} else {
							otherValue := "-"
							if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" { // 下拉框或者单选框
								if repSubject[form.Fields[i].Name] != nil {
									for _, option := range form.Fields[i].Options {
										if option.Value == repSubject[form.Fields[i].Name].(string) {
											otherValue = option.Label
										}
									}
								}
							} else if form.Fields[i].Type == "checkbox" { // 多选框
								if repSubject[form.Fields[i].Name] != nil {
									var checkboxBf bytes.Buffer
									str := repSubject[form.Fields[i].Name].([]interface{})
									for _, option := range form.Fields[i].Options {
										for j := 0; j < len(str); j++ {
											if option.Value == str[j].(string) {
												checkboxBf.WriteString(option.Label)
												checkboxBf.WriteString(",")
											}
										}
									}
									otherValue = checkboxBf.String()
								}
							} else if form.Fields[i].Type == "switch" { // 开关
								otherValue = "no"
								if repSubject[form.Fields[i].Name] != nil {
									if repSubject[form.Fields[i].Name] == true {
										otherValue = "yes"
									}
								}
							} else if form.Fields[i].Type == "inputNumber" { // 其它

								if repSubject[form.Fields[i].Name] != nil {
									otherValue = convertor.ToString(repSubject[form.Fields[i].Name])
								}
							} else if form.Fields[i].Type == "datePicker" { // 日期选择器
								dateFormat := "YYYY-MM-DD"
								if form.Fields[i].DateFormat != "" {
									dateFormat = form.Fields[i].DateFormat
								}
								if repSubject[form.Fields[i].Name] != nil {
									otherValue = fmt.Sprint(repSubject[form.Fields[i].Name])
									if repSubject[form.Fields[i].Name] != "" {
										parse, err := time.Parse("2006-01-02", otherValue)
										if err != nil {
											return nil, errors.WithStack(err)
										}
										otherValue = parse.Format(tools.DateFormatParse(dateFormat))
									}
								}
							} else if form.Fields[i].Type == "timePicker" {

								timeFormat := "YYYY-MM-DD HH:mm:ss"
								if form.Fields[i].TimeFormat != "" {
									timeFormat = form.Fields[i].TimeFormat
								}
								if repSubject[form.Fields[i].Name] != nil {
									otherValue = fmt.Sprint(repSubject[form.Fields[i].Name])
									if repSubject[form.Fields[i].Name] != "" {
										parse, err := time.Parse("2006-01-02 15:04:05", otherValue)
										if err != nil {
											return nil, errors.WithStack(err)
										}
										otherValue = parse.Format(tools.DateFormatParse(timeFormat))
									}
								}
							} else {
								if repSubject[form.Fields[i].Name] != nil {
									otherValue = repSubject[form.Fields[i].Name].(string)
								}
							}
							forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": otherValue})
						}
					}
					randomNumberShow := false
					if attribute.AttributeInfo.IsRandomNumber {
						randomNumberShow = true
					} else {
						randomNumber = tools.BlindData
					}

					indexStr := strconv.Itoa(index)

					if beReplaceSubject.SubGroupName != "" {
						var randomList models.RandomList
						err := tools.Database.Collection("random_list").FindOne(sctx, bson.M{"_id": beReplaceSubject.RandomListID}).Decode(&randomList)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						groupP, b2 := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
							return beReplaceSubject.ParGroupName == item.ParName && beReplaceSubject.SubGroupName == item.SubName
						})
						if b2 {
							contentData["subGroupBlind"+indexStr] = groupP.Blind
						}
					}

					contentData["blind"+indexStr] = attribute.AttributeInfo.Blind
					contentData["label"] = subjectEmailReplaceTextZh
					contentData["labelEn"] = subjectEmailReplaceTextEn
					contentData["cohortName"+indexStr] = cohort.Name
					contentData["randomNumberShow"+indexStr] = randomNumberShow
					contentData["randomNumber"+indexStr] = beReplaceSubject.RandomNumber
					contentData["group"+indexStr] = beReplaceSubject.Group
					contentData["subGroup"+indexStr] = beReplaceSubject.SubGroupName
					contentData["parName"+indexStr] = beReplaceSubject.ParGroupName
					contentData["replaceSubjectNumber"] = replaceSubject.Info[0].Value
					contentData["replaceRandomNumber"+indexStr] = randomNumber
					contentData["forms"] = forms

				}
				// 更新subject访视
				//  更新受试者访视数据
				err = updateDispensing(ctx, sctx, replaceSubject, beReplaceSubject.Group)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 更新冻结的受试者药物
				err = replaceMedicine(ctx, sctx, beReplaceSubject.ID, replaceSubject.ID)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 推给EDC
				if tools.PushScenarioFilter(project.ProjectInfo.ConnectEdc, project.ProjectInfo.PushMode, project.ProjectInfo.EdcSupplier, project.ProjectInfo.PushScenario.RandomPush) {
					replaceSubjectId := beReplaceSubject.ID.Hex()
					replaceSubjectNo := ""
					for _, info := range beReplaceSubject.Info {
						if info.Name == "shortname" {
							replaceSubjectNo = info.Value.(string)
						}
					}
					logData := tools.PrepareLogData(ctx)
					AsyncSubjectRandomPush(logData, replaceOID, 11, nowDuration, replaceSubjectId, replaceSubjectNo, "")
				}
				//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyRandom" || project.PushTypeEdc == "RandomAndDrug") {
				//	replaceSubjectId := beReplaceSubject.ID.Hex()
				//	replaceSubjectNo := ""
				//	for _, info := range beReplaceSubject.Info {
				//		if info.Name == "shortname" {
				//			replaceSubjectNo = info.Value.(string)
				//		}
				//	}
				//	SubjectRandomPush(ctx, replaceOID, 11, nowDuration, replaceSubjectId, replaceSubjectNo)
				//}
			}
		}

		//添加轨迹
		key := "history.subject.label.at-random-replaced-new-a"
		if count > 1 {
			key = "history.subject.label.at-random-replaced-new-b"
		}

		// 添加原受试者轨迹
		beReplaceHistory := models.History{
			OID: replaceSubjectTrail.BeReplaceSubjectID,
			Key: key,
			Data: map[string]interface{}{
				"name":                   replaceSubjectTrail.Name,
				"replaceName":            replaceSubjectTrail.ReplaceName,
				"label":                  replaceSubjectTrail.Label,
				"stage":                  replaceSubjectTrail.Stage,
				"beReplaceRandomNumber":  replaceSubjectTrail.BeReplaceRandomNumber,
				"replaceRandomNumber":    replaceSubjectTrail.ReplaceRandomNumber,
				"stage2":                 replaceSubjectTrail.Stage2,
				"beReplaceRandomNumber2": replaceSubjectTrail.BeReplaceRandomNumber2,
				"replaceRandomNumber2":   replaceSubjectTrail.ReplaceRandomNumber2,
			},
			Time: nowDuration,
			UID:  user.ID,
			User: user.Name,
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, beReplaceHistory)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 添加替换受试者轨迹
		replaceHistory := models.History{
			OID: replaceSubjectTrail.ReplaceSubjectID,
			Key: key,
			Data: map[string]interface{}{
				"name":                   replaceSubjectTrail.Name,
				"replaceName":            replaceSubjectTrail.ReplaceName,
				"label":                  replaceSubjectTrail.Label,
				"stage":                  replaceSubjectTrail.Stage,
				"beReplaceRandomNumber":  replaceSubjectTrail.BeReplaceRandomNumber,
				"replaceRandomNumber":    replaceSubjectTrail.ReplaceRandomNumber,
				"stage2":                 replaceSubjectTrail.Stage2,
				"beReplaceRandomNumber2": replaceSubjectTrail.BeReplaceRandomNumber2,
				"replaceRandomNumber2":   replaceSubjectTrail.ReplaceRandomNumber2},
			Time: nowDuration,
			UID:  user.ID,
			User: user.Name,
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, replaceHistory)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if len(userMail) > 0 {
			// site
			var projectSite models.ProjectSite
			err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			//时区
			strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if strTimeZone == "" {
				zone, err := tools.GetTimeZone(subject.ProjectID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				//strTimeZone = fmt.Sprintf("UTC%+d", zone)
				strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
			}
			//timeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
			timeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			dateTime := now.UTC().Add(duration).Format("2006-01-02 15:04:05")
			var showTimeZone string
			showTimeZone = tools.FormatOffsetToZoneStringUtc(timeZone)
			//if timeZone > 0 {
			//	showTimeZone = "+" + strconv.FormatInt(int64(timeZone), 10)
			//} else {
			//	showTimeZone = strconv.FormatInt(int64(timeZone), 10)
			//}
			dateTime = dateTime + " (" + showTimeZone + ")"

			subjectData := bson.M{
				"projectNumber": project.Number,
				"envName":       environment.Name,
				"siteNumber":    projectSite.Number,
				"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
				"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
			}

			contentData["projectNumber"] = project.Number
			contentData["projectName"] = project.Name
			contentData["envName"] = environment.Name
			contentData["siteNumber"] = projectSite.Number
			contentData["siteName"] = tools.GetProjectSiteLangName(projectSite, "zh")
			contentData["siteNameEn"] = tools.GetProjectSiteLangName(projectSite, "en")
			contentData["subjectNumber"] = subject.Info[0].Value
			contentData["replaceTime"] = dateTime

			if !subject.CohortID.IsZero() {
				for _, cohort := range cohorts {
					if cohort.ID == subject.CohortID {
						contentData["cohortName"] = models.GetCohortReRandomName(cohort)
						break
					}
				}
			}

			mailBodyContet, err := tools.MailBodyContent(nil, subject.EnvironmentID, "notice.subject.replace")
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for key, v := range mailBodyContet {
				contentData[key] = v
			}

			for _, email := range userMail {
				insertData := make(map[string]interface{})
				for key, v := range contentData {
					insertData[key] = v
				}
				for index := 0; index < 2; index++ {
					indexStr := strconv.Itoa(index)
					//insertData["cohortName"+indexStr] = contentData["cohortName"]
					if email.IsBlind && insertData["blind"+indexStr] != nil && insertData["blind"+indexStr].(bool) {
						insertData["group"+indexStr] = tools.BlindData
					} else {
						if insertData["subGroup"+indexStr] != nil && insertData["subGroup"+indexStr] != "" {
							insertData["group"+indexStr] = insertData["parName"+indexStr]
						} else {
							insertData["group"+indexStr] = contentData["group"+indexStr]
						}
					}
					if insertData["subGroupBlind"+indexStr] != nil && insertData["subGroupBlind"+indexStr].(bool) {
						if email.IsBlind {
							insertData["subGroup"+indexStr] = tools.BlindData
						} else {
							insertData["subGroup"+indexStr] = contentData["subGroup"+indexStr]
						}
					}
				}

				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				langList := make([]string, 0)
				html := "subject_replacement_at_random_zh_en.html"
				if noticeConfig.Manual != 0 {
					if noticeConfig.Manual == 1 {
						langList = append(langList, "zh")
						html = "subject_replacement_at_random_zh.html"
					} else if noticeConfig.Manual == 2 {
						langList = append(langList, "en")
						html = "subject_replacement_at_random_en.html"
					} else if noticeConfig.Manual == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						html = "subject_replacement_at_random_zh_en.html"
					}
				} else {
					langList = append(langList, ctx.GetHeader("Accept-Language"))
					if locales.Lang(ctx) == "zh" {
						html = "subject_replacement_at_random_zh.html"
					} else if locales.Lang(ctx) == "en" {
						html = "subject_replacement_at_random_en.html"
					}
				}
				var toUserMail []string
				toUserMail = append(toUserMail, email.Email)
				mails = append(mails, models.Mail{
					ID:           primitive.NewObjectID(),
					Subject:      "subject.replacement.title",
					SubjectData:  subjectData,
					ContentData:  insertData,
					HTML:         html,
					To:           toUserMail,
					Lang:         ctx.GetHeader("Accept-Language"),
					LangList:     langList,
					Status:       0,
					CreatedTime:  time.Duration(time.Now().Unix()),
					ExpectedTime: time.Duration(time.Now().Unix()),
					SendTime:     time.Duration(time.Now().Unix()),
				})
			}
			ctx.Set("MAIL", mails)
			if len(mails) > 0 {
				var envs []models.MailEnv
				for _, m := range mails {
					envs = append(envs, models.MailEnv{
						ID:         primitive.NewObjectID(),
						MailID:     m.ID,
						CustomerID: subject.CustomerID,
						ProjectID:  subject.ID,
						EnvID:      subject.EnvironmentID,
					})
				}
				ctx.Set("MAIL-ENV", envs)
			}
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func sysRandomNumber(beReplaceSubject models.Subject, attribute models.Attribute) (string, error) {
	var randomList models.RandomList
	err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": beReplaceSubject.RandomListID}, &options.FindOneOptions{Projection: bson.M{"config": 1}}).Decode(&randomList)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", errors.WithStack(err)
	}
	randomNumber := ""
	if randomList.Config.Prefix != "" {
		beReplaceNumberStr := strings.TrimLeft(beReplaceSubject.RandomNumber, randomList.Config.Prefix)
		beReplaceNumber, _ := strconv.Atoi(beReplaceNumberStr)
		afterNumber := beReplaceNumber + attribute.AttributeInfo.ReplaceRuleNumber
		randomNumber = fmt.Sprintf("%s%d", randomList.Config.Prefix, afterNumber)
		if randomList.Config.NumberLength > len(randomNumber) {
			supplement := "" // 补充字符
			for i := 0; i < randomList.Config.NumberLength-len(randomNumber); i++ {
				supplement += "0"
			}
			randomNumber = fmt.Sprintf("%s%s%d", randomList.Config.Prefix, supplement, afterNumber)
		}
	} else {
		prefix := ""
		re := regexp.MustCompile(`\d+$`)
		match := re.FindString(beReplaceSubject.RandomNumber)
		prefix = beReplaceSubject.RandomNumber[:len(beReplaceSubject.RandomNumber)-len(match)]
		beReplaceNumber, _ := strconv.Atoi(match)
		afterNumber := beReplaceNumber + attribute.AttributeInfo.ReplaceRuleNumber
		randomNumber = fmt.Sprintf("%s%d", prefix, afterNumber)
		if len(beReplaceSubject.RandomNumber) > len(randomNumber) {
			supplement := "" // 补充字符
			for i := 0; i < len(beReplaceSubject.RandomNumber)-len(randomNumber); i++ {
				supplement += "0"
			}
			randomNumber = fmt.Sprintf("%s%s%d", prefix, supplement, afterNumber)
		}
	}
	return randomNumber, nil
}

// DownloadUnblindingData
func (s *SubjectService) DownloadUnblindingData(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) error {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	// project
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	projectSiteID := ctx.DefaultQuery("projectSiteId", "")
	cohortID := ctx.DefaultQuery("cohortId", "")

	match := bson.A{}
	filter := bson.M{"urgent_unblinding_status": 1, "customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "deleted": bson.M{"$ne": true}}
	attrFilter := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	// 是否为cohort项目
	if project.Type != 1 {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		filter["cohort_id"] = cohortOID
		attrFilter["cohort_id"] = cohortOID
	}

	if projectSiteID != "" {
		projectSiteOID, _ := primitive.ObjectIDFromHex(projectSiteID)
		filter["project_site_id"] = projectSiteOID
	} else {
		// study 角色不过滤
		study, err := tools.RoleIsStudy(roleID)
		if err != nil {
			return errors.WithStack(err)
		}
		if !study {
			siteOID, err := tools.GetRoleSite(ctx, envID)
			if err != nil {
				return errors.WithStack(err)
			}
			if len(siteOID) > 0 {
				filter["project_site_id"] = bson.M{"$in": siteOID}
			}
		}
	}

	// 查询项目属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, attrFilter).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	match = append(match, filter)
	pagePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "urgent_unblinding_people", "foreignField": "_id", "as": "user"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$user", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$urgent_unblinding_approvals", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$match", Value: bson.M{"$or": bson.A{bson.M{"urgent_unblinding_people": primitive.NilObjectID, "urgent_unblinding_approvals.status": 1}, bson.M{"urgent_unblinding_people": bson.M{"$ne": primitive.NilObjectID}}}}}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "urgent_unblinding_approvals.application_by", "foreignField": "_id", "as": "application_by"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$application_by", "preserveNullAndEmptyArrays": true}}},
		//
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"country":    bson.M{"$first": "$project_site.country"},
				"siteNumber": "$project_site.number",

				"siteName":                           models.ProjectSiteNameLookUpBson(ctx),
				"timeZone":                           "$project_site.time_zone",
				"randomNumber":                       "$random_number",
				"info":                               "$info",
				"urgentUnblindingPeople":             "$user.info.name",
				"urgentUnblindingTime":               "$urgent_unblinding_time",
				"urgentUnblindingReason":             "$urgent_unblinding_reason",
				"urgentUnblindingReasonStr":          "$urgent_unblinding_reason_str",
				"isSponsor":                          "$is_sponsor",
				"remark":                             "$remark",
				"urgentUnblindingPeopleApply":        "$application_by.info.name",
				"urgentUnblindingApprovalsReasonStr": "$urgent_unblinding_approvals.reason_str",
				"urgentUnblindingApprovalsRemark":    "$urgent_unblinding_approvals.remark",
			},
		}},
	}

	var subjectData []map[string]interface{}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pagePipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return errors.WithStack(err)
	}
	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
	title := make([]interface{}, 0)
	title = append(title, locales.Tr(ctx, "site.number"))
	title = append(title, locales.Tr(ctx, "site.name"))
	title = append(title, subjectReplaceText)
	title = append(title, locales.Tr(ctx, "subject.unblinding.sponsor"))
	title = append(title, locales.Tr(ctx, "common.remark"))
	if attribute.AttributeInfo.IsRandomNumber { // 展示随机号
		title = append(title, locales.Tr(ctx, "random.number"))
		title = append(title, locales.Tr(ctx, "operator.people"))
		title = append(title, locales.Tr(ctx, "operator.time"))
		title = append(title, locales.Tr(ctx, "operator.reason"))
	} else {
		title = append(title, locales.Tr(ctx, "operator.people"))
		title = append(title, locales.Tr(ctx, "operator.time"))
		title = append(title, locales.Tr(ctx, "operator.reason"))
	}
	countries := bson.M{}
	if attribute.AttributeInfo.CountryLayered {
		countries, err = database.GetCountries(ctx)
		title = append([]interface{}{locales.Tr(ctx, "common.country")}, title...)
	}
	content := make([][]interface{}, len(subjectData))
	zone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return err
	}
	timezone := time.Hour * time.Duration(zone)
	for i := 0; i < len(subjectData); i++ {
		item := subjectData[i]
		isSponsorStr := locales.Tr(ctx, "common.no")
		if item["isSponsor"] != nil && item["isSponsor"].(bool) {
			isSponsorStr = locales.Tr(ctx, "common.yes")
		}
		row := make([]interface{}, 0)
		if attribute.AttributeInfo.CountryLayered {
			if _, ok := item["country"]; ok {
				row = append(row, countries[item["country"].(string)])
			} else {
				row = append(row, "")
			}
		}
		row = append(row, item["siteNumber"])
		row = append(row, item["siteName"])
		row = append(row, item["info"].(primitive.A)[0].(map[string]interface{})["value"].(string))
		row = append(row, isSponsorStr)
		if item["urgentUnblindingPeople"] != nil {
			row = append(row, item["urgentUnblindingReason"])
		} else {
			row = append(row, item["urgentUnblindingApprovalsRemark"])
		}
		if attribute.AttributeInfo.IsRandomNumber { // 展示随机号
			row = append(row, item["randomNumber"])
		}
		if item["urgentUnblindingPeople"] != nil {
			row = append(row, item["urgentUnblindingPeople"])
		} else {
			row = append(row, item["urgentUnblindingPeopleApply"])
		}

		if item["timeZone"] != nil && item["timeZone"] != "" {
			//timeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
			offset, _ := tools.ParseTimezoneOffset(item["timeZone"].(string))
			hours := time.Duration(offset)
			minutes := time.Duration((offset - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			var randomTime string
			randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04")
			randomTime = randomTime + "(" + item["timeZone"].(string) + ")"
			row = append(row, randomTime)

		} else {
			hours := time.Duration(zone)
			minutes := time.Duration((zone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			var randomTime string
			randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04")
			//strTimeZone := fmt.Sprintf("UTC%+d", zone)
			strTimeZone := tools.FormatOffsetToZoneStringUtc(zone)
			randomTime = randomTime + "(" + strTimeZone + ")"
			row = append(row, randomTime)

		}
		if item["urgentUnblindingPeople"] != nil {
			//if item["urgentUnblindingReasonType"] != nil {
			//	row = append(row, tools.GetUnblindingReasonTran(ctx, int(item["urgentUnblindingReasonType"].(int32))))
			//} else {
			//	row = append(row, item["urgentUnblindingReason"])
			//}
			row = append(row, item["urgentUnblindingReasonStr"])
		} else {
			row = append(row, item["urgentUnblindingApprovalsReasonStr"])
		}
		content[i] = row
	}

	// 查询项目信息
	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":    0,
					"number": "$info.number",
					"name":   "$info.name",
					"env":    "$envs.name",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	now := time.Now().UTC().Add(timezone).Format("20060102")
	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}
	fileName := fmt.Sprintf("%s[%s]—%s—%s.xlsx", number, env, locales.Tr(ctx, "subject.unblinding.fileName"), now)
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *SubjectService) DownloadRandomData(ctx *gin.Context, customerID string, projectID string, envID string, roleID string) error {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	attrFilter := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	randomFilter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"project_id":  projectOID,
		"status":      1,
	}

	// project
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	projectSiteID := ctx.DefaultQuery("projectSiteId", "")
	cohortID := ctx.DefaultQuery("cohortId", "")

	match := bson.A{}
	var statusArray = [...]int{3, 4, 5, 6}
	filter := bson.M{"group": bson.M{"$ne": ""}, "status": bson.M{"$in": statusArray}, "customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	// 是否为cohort项目
	if project.Type != 1 {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		filter["cohort_id"] = cohortOID
		attrFilter["cohort_id"] = cohortOID
		randomFilter["cohort_id"] = cohortOID
	}
	projectSiteOID := primitive.NilObjectID
	if projectSiteID != "" {
		projectSiteOID, _ = primitive.ObjectIDFromHex(projectSiteID)
		filter["project_site_id"] = projectSiteOID
	} else {
		// study 角色不过滤
		study, err := tools.RoleIsStudy(roleID)
		if err != nil {
			return errors.WithStack(err)
		}
		if !study {
			siteOID, err := tools.GetRoleSite(ctx, envID)
			if err != nil {
				return errors.WithStack(err)
			}
			if len(siteOID) > 0 {
				filter["project_site_id"] = bson.M{"$in": siteOID}
			}
		}
	}
	match = append(match, filter)

	pagePipeline := mongo.Pipeline{
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"siteNumber": "$project_site.number",

				"siteName":        models.ProjectSiteNameLookUpBson(ctx),
				"timeZone":        "$project_site.time_zone",
				"randomNumber":    "$random_number",
				"info":            "$info",
				"randomTime":      "$random_time",
				"status":          "$status",
				"group":           "$group",
				"replace_subject": bson.M{"$ifNull": bson.A{"$replace_subject", ""}},
				"replace_number":  bson.M{"$ifNull": bson.A{"$replace_number", ""}},
				"country":         bson.M{"$first": "$project_site.country"},
			},
		}},
	}

	var subjectData []map[string]interface{}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pagePipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return errors.WithStack(err)
	}
	// 查询随机表配置(用于取出分层因素)
	var randomList models.RandomList
	err = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, attrFilter).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	// 查询项目属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, attrFilter).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return err
	}
	//写入excel文件
	countries := bson.M{}
	title := make([]interface{}, 0)
	if attribute.AttributeInfo.CountryLayered {
		countries, err = database.GetCountries(ctx)
		title = append(title, locales.Tr(ctx, "common.country"))
	}
	title = append(title, locales.Tr(ctx, "site.number"))
	title = append(title, locales.Tr(ctx, "site.name"))
	title = append(title, attribute.AttributeInfo.Field.Label)

	for _, field := range form.Fields {
		title = append(title, field.Label)
	}

	if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
		for _, factor := range randomList.Design.Factors {
			title = append(title, factor.Label)
		}
	}
	if attribute.AttributeInfo.IsRandomNumber {
		title = append(title, locales.Tr(ctx, "random.number"))
	}
	title = append(title, locales.Tr(ctx, "operator.time"))
	title = append(title, locales.Tr(ctx, "subject.status"))
	if !isBlindedRole || !attribute.AttributeInfo.Blind {
		title = append(title, locales.Tr(ctx, "subject.group"))
	}
	title = append(title, locales.Tr(ctx, "subject.replace")+subjectReplaceText)
	if attribute.AttributeInfo.IsRandomNumber {
		title = append(title, locales.Tr(ctx, "subject.replace_number"))
	}
	content := make([][]interface{}, len(subjectData))
	zone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return err
	}
	for i := 0; i < len(subjectData); i++ {
		var randomTime string
		item := subjectData[i]
		c := make([]interface{}, 0)
		if attribute.AttributeInfo.CountryLayered {
			if _, ok := item["country"]; ok {
				c = append(c, countries[item["country"].(string)])
			} else {
				c = append(c, "")
			}
		}
		c = append(c, item["siteNumber"])
		c = append(c, item["siteName"])
		c = append(c, item["info"].(primitive.A)[0].(map[string]interface{})["value"].(string))
		for _, field := range form.Fields {
			fieldValue := ""
			for _, info := range item["info"].(primitive.A) {
				if info.(map[string]interface{})["value"] == nil {
					continue
				}
				if field.Name == info.(map[string]interface{})["name"].(string) {
					if field.Type == "radio" || field.Type == "select" {
						for _, option := range field.Options {
							if info.(map[string]interface{})["value"].(string) == option.Value {
								fieldValue = option.Label
							}
						}
					} else if field.Type == "checkbox" {
						var options []string
						for _, option := range field.Options {
							if info.(map[string]interface{})["value"] != nil {
								for _, check := range info.(map[string]interface{})["value"].(primitive.A) {
									if check == option.Value {
										options = append(options, option.Label)
									}
								}
							}

						}
						fieldValue = strings.Join(options, ",")
					} else if field.Type == "switch" {
						if info.(map[string]interface{})["value"] == nil {
							fieldValue = ""
						} else {
							if info.(map[string]interface{})["value"].(bool) {
								fieldValue = "yes"
							} else {
								fieldValue = "no"
							}
						}

					} else if field.Type == "datePicker" {
						value := info.(map[string]interface{})["value"].(string)
						if value == "" {
							continue
						}
						parse, err := time.Parse("2006-01-02", value)
						if err != nil {
							return errors.WithStack(err)
						}
						fieldValue = parse.Format(tools.DateFormatParse(field.DateFormat))
					} else if field.Type == "timePicker" {
						value := info.(map[string]interface{})["value"].(string)
						if value == "" {
							continue
						}
						parse, err := time.Parse("2006-01-02 15:04:05", value)
						if err != nil {
							return errors.WithStack(err)
						}
						fieldValue = parse.Format(tools.DateFormatParse(field.TimeFormat))
					} else {
						fieldValue = convertor.ToString(info.(map[string]interface{})["value"])
					}
				}

			}
			c = append(c, fieldValue)
		}
		if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
			// 获取分层因素的label
			for _, factor := range randomList.Design.Factors {
				labelValue := ""
				for _, info := range item["info"].(primitive.A) {
					if factor.Name == info.(map[string]interface{})["name"].(string) {
						for _, option := range factor.Options {
							if option.Value == info.(map[string]interface{})["value"].(string) {
								labelValue = option.Label
								break
							}
						}
						break
					}
				}
				c = append(c, labelValue)
			}
		}
		if attribute.AttributeInfo.IsRandomNumber {
			c = append(c, item["randomNumber"])
		}
		if item["randomTime"] != nil && item["randomTime"] != "" {
			if item["timeZone"] != nil && item["timeZone"] != "" {
				//timeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
				offset, _ := tools.ParseTimezoneOffset(item["timeZone"].(string))
				hours := time.Duration(offset)
				minutes := time.Duration((offset - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				randomTime = time.Unix(item["randomTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				randomTime = randomTime + "(" + item["timeZone"].(string) + ")"
			} else {
				hours := time.Duration(zone)
				minutes := time.Duration((zone - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				randomTime = time.Unix(item["randomTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				//strTimeZone := fmt.Sprintf("UTC%+d", zone)
				strTimeZone := tools.FormatOffsetToZoneStringUtc(zone)
				randomTime = randomTime + "(" + strTimeZone + ")"
			}
		}
		c = append(c, randomTime)
		c = append(c, statusItem(ctx, item["status"].(int32)))
		if !isBlindedRole || !attribute.AttributeInfo.Blind {
			c = append(c, item["group"])
		}
		c = append(c, item["replace_subject"])
		if attribute.AttributeInfo.IsRandomNumber {
			c = append(c, item["replace_number"])
		}
		content[i] = c
	}

	// 查询项目信息
	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":    0,
					"number": "$info.number",
					"name":   "$info.name",
					"env":    "$envs.name",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	hours := time.Duration(zone)
	minutes := time.Duration((zone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	now := time.Now().UTC().Add(duration).Format("20060102")
	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}
	fileName := fmt.Sprintf("%s[%s]—%s—%s.xlsx", number, env, locales.Tr(ctx, "subject.random.fileName"), now)
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *SubjectService) UrgentUnblindingApplication(ctx *gin.Context) (interface{}, error) {
	//参数转化
	req := struct {
		ProjectId      string `json:"projectId"`
		EnvId          string `json:"envId"`
		AttributeId    string `json:"attributeId"`
		SubjectId      string `json:"subjectId"`
		RoleId         string `json:"roleId"`
		ReasonStr      string `json:"reasonStr"`
		Remark         string `json:"remark"`
		Password       string `json:"password"`
		ApprovalType   int    `json:"approvalType"`
		UnblindingCode string `json:"unblindingCode"`
		UnblindingSign string `json:"unblindingSign"` // 2 紧急揭盲  3pv揭盲
	}{}
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envOID, err := primitive.ObjectIDFromHex(req.EnvId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	projectOID, err := primitive.ObjectIDFromHex(req.ProjectId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	subjectOID, err := primitive.ObjectIDFromHex(req.SubjectId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	roleOID, err := primitive.ObjectIDFromHex(req.RoleId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//配置校验
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envp, ok := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envp
	if !ok {
		return nil, tools.BuildServerError(ctx, "common_configuration_error")
	}

	if project.UnblindingControl == 0 || (project.UnblindingType == 0 && project.PvUnblindingType == 0) {
		return nil, tools.BuildServerError(ctx, "common_configuration_error")
	}
	if req.ApprovalType == 0 {
		return nil, tools.BuildServerError(ctx, "common_configuration_error")
	}
	if req.UnblindingSign == "2" { // 紧急揭盲
		if (req.ApprovalType == 1 && project.UnblindingSms != 1 && project.UnblindingProcess != 1) || (req.ApprovalType == 2 && project.UnblindingCode != 1) {
			return nil, tools.BuildServerError(ctx, "common_configuration_error")
		}
	} else { // pv揭盲
		if req.ApprovalType == 1 && project.PvUnblindingSms != 1 && project.PvUnblindingProcess != 1 {
			return nil, tools.BuildServerError(ctx, "common_configuration_error")
		}
	}

	attribute, err := database.GetAttribute(nil, req.AttributeId)
	if err != nil {
		return nil, err
	}

	//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
	subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
	subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

	//获取当前用户登录信息
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}
	//校验密码
	//不是揭盲码时 提前校验密码
	if req.ApprovalType != 2 {
		err = tools.PasswordDetection(ctx, me.Email, req.Password)
		if err != nil {
			return nil, tools.BuildServerError(ctx, "unblinding_password_error", tools.UnblindingPasswordError)
		}
	}
	var now time.Time
	approvalNumber := ""
	stage := ""
	var cohort models.Cohort
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		now = time.Now()
		var subject models.Subject
		err := tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if env.Cohorts != nil && len(env.Cohorts) > 0 {
			for _, co := range env.Cohorts {
				if co.ID == subject.CohortID {
					cohort = co
					break
				}
			}
		}
		if req.UnblindingSign == "2" { // 紧急揭盲
			if subject.UrgentUnblindingApprovals != nil || len(subject.UrgentUnblindingApprovals) > 0 {
				for _, approval := range subject.UrgentUnblindingApprovals {
					if approval.Status == 0 {
						return nil, tools.BuildServerError(ctx, "subject_urgentUnblindingApproval_applicationed")
					}
				}
			}
		} else { // pv揭盲
			if subject.PvUrgentUnblindingApprovals != nil || len(subject.PvUrgentUnblindingApprovals) > 0 {
				for _, pvApproval := range subject.PvUrgentUnblindingApprovals {
					if pvApproval.Status == 0 {
						return nil, tools.BuildServerError(ctx, "subject_urgentUnblindingApproval_pv_applicationed")
					}
				}
			}
		}

		var userEnv models.UserProjectEnvironment
		err = tools.Database.Collection("user_project_environment").FindOne(sctx, bson.M{"env_id": envOID, "user_id": me.ID}).Decode(&userEnv)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		shTime, err := tools.GetTimeZoneTime(now.UTC(), projectSite, project)
		if err != nil {
			return nil, err
		}

		var noticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		langList := make([]string, 0)
		if noticeConfig.Manual != 0 {
			if noticeConfig.Manual == 1 {
				langList = append(langList, "zh")
			} else if noticeConfig.Manual == 2 {
				langList = append(langList, "en")
			} else if noticeConfig.Manual == 3 {
				langList = append(langList, "zh")
				langList = append(langList, "en")
			}
		} else {
			langList = append(langList, ctx.GetHeader("Accept-Language"))
		}

		// 添加轨迹
		history := models.History{
			OID:  subjectOID,
			Time: time.Duration(now.Unix()),
			UID:  me.ID,
			User: me.Name,
		}
		applicationTime := time.Duration(now.Unix())
		//审批方式为揭盲码
		if req.ApprovalType == 2 {
			passwordError := false
			codeError := false
			err = tools.PasswordDetection(ctx, me.Email, req.Password)
			if err != nil {
				passwordError = true
			}
			contain := slice.Contain(userEnv.UnblindingCode.AvailableCode, req.UnblindingCode)
			if !contain {
				codeError = true
			}
			if passwordError && codeError {
				return nil, tools.BuildServerValidateError(ctx, []string{"unblinding_password_error", "unblinding_code_error"}, nil, tools.UnblindingCodeError_UnblindingPasswordError)
			}
			if passwordError {
				return nil, tools.BuildServerError(ctx, "unblinding_password_error", tools.UnblindingPasswordError)
			}
			if codeError {
				return nil, tools.BuildServerError(ctx, "unblinding_code_error", tools.UnblindingCodeError)
			}
			//更新揭盲码把可用变成已用
			envUpdate := bson.M{
				"$push": bson.M{"unblinding_code.used_code": req.UnblindingCode},
				"$pull": bson.M{"unblinding_code.available_code": req.UnblindingCode},
			}
			_, err = tools.Database.Collection("user_project_environment").UpdateOne(sctx, bson.M{"_id": userEnv.ID}, envUpdate)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			subjectUpdate := bson.M{
				"$set": bson.M{
					"urgent_unblinding_status":     1,
					"status":                       6,
					"urgent_unblinding_time":       time.Duration(now.Unix()),
					"urgent_unblinding_people":     me.ID,
					"urgent_unblinding_reason_str": req.ReasonStr,
					"urgent_unblinding_reason":     req.Remark,
				}}

			if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": subjectOID}, subjectUpdate); err != nil {
				return nil, errors.WithStack(err)
			}

			//轨迹
			history.Key = "history.subject.unblinding-success"
			// TODO 在随机
			if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
				history.Key = "history.subject.at-random-unblinding-success"
				if subject.CohortID != primitive.NilObjectID {
					for _, env := range project.Environments {
						if env.ID == subject.EnvironmentID {
							for _, cohort := range env.Cohorts {
								if cohort.ID == subject.CohortID {
									stage = cohort.Name
									break
								}
							}
							break
						}
					}
				}
			}
			history.Data = map[string]interface{}{"stage": stage}

			// 揭盲发送邮件
			userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.unblinding", subject.ProjectSiteID)
			if err != nil {
				return nil, err
			}
			var mails []models.Mail
			if len(userMail) > 0 {
				for _, userEmail := range userMail {
					var toUserMail []string
					toUserMail = append(toUserMail, userEmail.Email)
					mail := models.Mail{
						ID:           primitive.NewObjectID(),
						To:           toUserMail,
						Lang:         ctx.GetHeader("Accept-Language"),
						LangList:     langList,
						Status:       0,
						CreatedTime:  time.Duration(now.Unix()),
						ExpectedTime: time.Duration(now.Unix()),
						SendTime:     time.Duration(now.Unix()),
					}
					mail.Subject = "subject.unblinding.title"
					mail.SubjectData = map[string]interface{}{
						"projectNumber": project.Number,
						"envName":       env.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      models.GetProjectSiteName(ctx, projectSite),
					}
					mail.Content = "subject.unblinding.controlContent"
					randomNumber := tools.BlindData
					if attribute.AttributeInfo.IsRandomNumber {
						randomNumber = subject.RandomNumber
					}
					reason := req.ReasonStr
					if req.Remark != "" {
						reason = req.ReasonStr + " " + req.Remark
					}
					contentData := map[string]interface{}{
						"projectNumber": project.Number,
						"projectName":   project.Name,
						"envName":       env.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      models.GetProjectSiteName(ctx, projectSite),
						"subjectNumber": subject.Info[0].Value,
						"randomNumber":  randomNumber,
						"time":          shTime,
						"reason":        reason,
						"label":         subjectEmailReplaceTextZh,
						"labelEn":       subjectEmailReplaceTextEn,
					}
					if !cohort.ID.IsZero() {
						contentData["cohortName"] = models.GetCohortReRandomName(cohort)
					}
					bodyContentKeys, _, err := tools.MailCustomContent(ctx, env.ID, "notice.subject.unblinding", contentData, nil)
					if err != nil {
						return nil, err
					}
					mail.ContentData = contentData
					mail.BodyContentKey = bodyContentKeys
					mails = append(mails, mail)
				}
			}
			ctx.Set("MAIL", mails)
			if len(mails) > 0 {
				var envs []models.MailEnv
				for _, m := range mails {
					envs = append(envs, models.MailEnv{
						ID:         primitive.NewObjectID(),
						MailID:     m.ID,
						CustomerID: subject.CustomerID,
						ProjectID:  subject.ProjectID,
						EnvID:      subject.EnvironmentID,
						CohortID:   subject.CohortID,
					})
				}
				ctx.Set("MAIL-ENV", envs)
			}
		} else if req.ApprovalType == 1 { // 审批确认
			//生成审批编号
			approvalNumber, err = GetApprovalNumber(ctx, subject.ProjectID)
			if err != nil {
				return nil, err
			}
			//创建一个揭盲审批为待审批状态
			approval := models.UrgentUnblindingApproval{
				Number:             approvalNumber,
				Status:             0,
				ApprovalType:       req.ApprovalType,
				ApplicationTime:    applicationTime,
				ApplicationBy:      me.ID,
				ApplicationByEmail: me.Email,
				ApprovalTime:       0,
				ApprovalBy:         primitive.NilObjectID,
				ApprovalByEmail:    "",
				ReasonStr:          req.ReasonStr,
				Remark:             req.Remark,
				RejectReason:       "",
				Lang:               ctx.GetHeader("Accept-Language"),
			}
			//确认方式为流程或短信
			//短信
			if (req.UnblindingSign == "2" && project.ProjectInfo.UnblindingSms == 1) || (req.UnblindingSign == "3" && project.ProjectInfo.PvUnblindingSms == 1) {
				//查询拥有审批权限的用户
				//template := 1
				//if project.ResearchAttribute == 1 {
				//	template = 2
				//}
				type cloudOIdStruct struct {
					CloudId primitive.ObjectID `bson:"cloud_id"`
					ID      primitive.ObjectID `bson:"id"`
				}
				cloudOIds := []cloudOIdStruct{}
				randomNumber := tools.BlindData
				if attribute.AttributeInfo.IsRandomNumber {
					randomNumber = subject.RandomNumber
				}

				// hotfix-20241112移除逻辑（此逻辑用户如果是中心角色有揭盲审批权限，假设该用户只分配了A中心，B中心揭盲审批的短信该用户也会收到）
				//***********************开始****************************
				//p := bson.M{"$in": bson.A{"operation.subject.unblinding-approval", "operation.subject-dtp.unblinding-approval"}}
				//if req.UnblindingSign == "3" { // pv揭盲
				//	p = bson.M{"$in": bson.A{"operation.subject.unblinding-pv-approval"}}
				//}
				//pipeline := mongo.Pipeline{
				//	{{Key: "$match", Value: bson.M{"project_id": projectOID, "template": template, "status": 1, "permissions": p}}},
				//	{{Key: "$lookup", Value: bson.M{
				//		"from":         "user_project_environment",
				//		"localField":   "_id",
				//		"foreignField": "roles",
				//		"as":           "user_project_environment",
				//	}}},
				//	{{Key: "$unwind", Value: "$user_project_environment"}},
				//	{{Key: "$match", Value: bson.M{"user_project_environment.env_id": envOID, "user_project_environment.unbind": false}}},
				//	{{Key: "$lookup", Value: bson.M{
				//		"from":         "user",
				//		"localField":   "user_project_environment.user_id",
				//		"foreignField": "_id",
				//		"as":           "user",
				//	}}},
				//	{{Key: "$unwind", Value: "$user"}},
				//	{{Key: "$project", Value: bson.M{
				//		"id":       "$user._id",
				//		"cloud_id": "$user.cloud_id",
				//	}}},
				//}

				//cursor, err := tools.Database.Collection("project_role_permission").Aggregate(sctx, pipeline)
				//if err != nil {
				//	return nil, errors.WithStack(err)
				//}
				//err = cursor.All(sctx, &cloudOIds)
				//if err != nil {
				//	return nil, errors.WithStack(err)
				//}
				//***********************结束****************************

				//去cloud查询手机号
				// hotfix-20241112新增逻辑（解决问题：用户如果是中心角色有揭盲审批权限，假设该用户只分配了A中心，B中心揭盲审批的短信该用户也会收到）
				// ***********************开始****************************
				// 查询该环境关联的所有用户
				userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
				userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": subject.EnvironmentID})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				var userIDs []primitive.ObjectID
				for _, upel := range userProjectEnvironmentList {
					userIDs = append(userIDs, upel.UserID)
				}
				// 查询user
				var userList []models.User
				userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = userCursor.All(nil, &userList)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 查询项目角色权限
				projectRolePermissionList := make([]models.ProjectRolePermission, 0)
				projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": subject.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 查询用户关联的角色
				userSiteList := make([]models.UserSite, 0)
				userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": subject.EnvironmentID})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = userSiteCursor.All(nil, &userSiteList)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				unblindingApprovalUser, pvUnblindingApprovalUser, _ := queryApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subject.ProjectSiteID)
				if req.UnblindingSign == "2" { // 紧急揭盲
					for _, uau := range unblindingApprovalUser {
						cloudOIds = append(cloudOIds, cloudOIdStruct{ID: uau.UserId, CloudId: uau.CloudId})
					}
				} else {
					for _, puau := range pvUnblindingApprovalUser {
						cloudOIds = append(cloudOIds, cloudOIdStruct{ID: puau.UserId, CloudId: puau.CloudId})
					}
				}
				// ***********************结束****************************

				cloudIds := slice.Map(cloudOIds, func(index int, item cloudOIdStruct) string {
					return item.CloudId.Hex()
				})

				users, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
				if err != nil {
					return nil, errors.WithStack(err)
				}
				usersIdMap := slice.GroupWith(users, func(t *models.UserData) string {
					return t.Id
				})
				cloudOIds = slice.Filter(cloudOIds, func(index int, item cloudOIdStruct) bool {
					u := usersIdMap[item.CloudId.Hex()]
					return u != nil
				})
				smsUsers := slice.Map(cloudOIds, func(index int, item cloudOIdStruct) models.SmsUser {
					cloudUser := usersIdMap[item.CloudId.Hex()][0]
					cloudOID, _ := primitive.ObjectIDFromHex(cloudUser.Id)
					smsUser := models.SmsUser{
						Phone:   cloudUser.Info.Mobile,
						UserId:  item.ID,
						CloudId: cloudOID,
						Name:    cloudUser.Info.Name,
						Email:   cloudUser.Info.Email,
					}
					return smsUser
				})
				phones := slice.Map(users, func(index int, item *models.UserData) string {
					i := item
					return i.Info.Mobile
				})
				phones = slice.Compact(phones)
				// 查询操作角色
				var projectRolePermission models.ProjectRolePermission
				err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&projectRolePermission)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//roleName, err := getUserPermissions(sctx, envOID, me.ID, projectSite.ID, "operation.subject.unblinding-application")
				//if req.UnblindingSign == "3" {
				//	roleName, err = getUserPermissions(sctx, envOID, me.ID, projectSite.ID, "operation.subject.unblinding-pv-application")
				//}

				if err != nil {
					return nil, errors.WithStack(err)
				}
				//（${unblindingType}）项目：{$projectName}，中心：{$siteName}，申请人：${applicant}, 受试者号：{$subjectNumber}，随机号：{$randomNumber}，揭盲原因：{$reason}，审批编号：{$approvalNumber}。通过请回复{$agreeCode}，拒绝请回复：{$rejectCode}。
				//判断揭盲类型
				unblindingType := "紧急"
				if req.UnblindingSign == "3" {
					unblindingType = "pv"
				}
				templateParam := struct {
					UnblindingType string `json:"unblindingType"`
					ProjectName    string `json:"projectName"`
					SiteName       string `json:"siteName"`
					UserName       string `json:"userName"`
					SubjectNumber  string `json:"subjectNumber"`
					RandomNumber   string `json:"randomNumber"`
					Reason         string `json:"reason"`
					ApprovalNumber string `json:"approvalNumber"`
					AgreeCode      string `json:"agreeCode"`
					RejectCode     string `json:"rejectCode"`
				}{
					UnblindingType: unblindingType,
					ProjectName:    project.Number + "(" + env.Name + ")",
					SiteName:       models.GetProjectSiteName(ctx, projectSite),
					UserName:       me.Name + "(" + projectRolePermission.Name + ")",
					SubjectNumber:  subject.Info[0].Value.(string),
					RandomNumber:   randomNumber,
					Reason:         req.ReasonStr,
					ApprovalNumber: approvalNumber,
					AgreeCode:      approvalNumber[8:] + "1",
					RejectCode:     approvalNumber[8:] + "2",
				}
				paramJsonStr, err := convertor.ToJson(templateParam)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = tools.SendUrgentUnblindingApproval(paramJsonStr, phones)
				if err != nil {
					return nil, err
				}
				approval.Msg = paramJsonStr
				approval.SmsUsers = smsUsers
			}

			subjectUpdate := bson.M{
				"$set": bson.M{
					"urgent_unblinding_reason_str": req.ReasonStr,
					"urgent_unblinding_reason":     req.Remark,
				},
				"$push": bson.M{"urgent_unblinding_approvals": approval},
			}
			if req.UnblindingSign == "3" {
				subjectUpdate = bson.M{
					"$set": bson.M{
						"pv_unblinding_reason_str": req.ReasonStr,
						"pv_unblinding_reason":     req.Remark,
					},
					"$push": bson.M{"pv_urgent_unblinding_approvals": approval},
				}
			}
			_, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": subjectOID}, subjectUpdate)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// TODO 在随机
			if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
				history.Key = "history.subject.at-random-unblinding-application"
				if req.UnblindingSign == "3" { // pv揭盲
					history.Key = "history.subject.at-random-unblinding-application-pv"
				}
				if subject.CohortID != primitive.NilObjectID {
					for _, env := range project.Environments {
						if env.ID == subject.EnvironmentID {
							for _, cohort := range env.Cohorts {
								if cohort.ID == subject.CohortID {
									stage = cohort.Name
									break
								}
							}
							break
						}
					}
				}
			} else {
				history.Key = "history.subject.unblinding-application"
				if req.UnblindingSign == "3" { // pv揭盲
					history.Key = "history.subject.unblinding-application-pv"
				}
			}
			history.Data = map[string]interface{}{"approvalNumber": approvalNumber, "stage": stage}
			// 创建app任务
			// 查询权限 紧急揭盲审批权限的用户
			permissions := []string{"operation.subject.unblinding-approval"}
			if req.UnblindingSign == "3" { // pv揭盲
				permissions = []string{"operation.subject.unblinding-pv-approval"}
			}
			var siteOrStoreIDs = []primitive.ObjectID{subject.ProjectSiteID}
			userIds, err := tools.GetPermissionUserIds(sctx, permissions, subject.ProjectID, subject.EnvironmentID, siteOrStoreIDs...)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if userIds != nil {
				//创建app任务
				workType := 8
				if req.UnblindingSign == "3" { // pv揭盲
					workType = 10
				}

				workTask := models.WorkTask{
					ID:            primitive.NewObjectID(),
					CustomerID:    subject.CustomerID,
					ProjectID:     subject.ProjectID,
					EnvironmentID: subject.EnvironmentID,
					CohortID:      subject.CohortID,
					UserIDs:       userIds,
					Info: models.WorkTaskInfo{
						WorkType:    workType,
						Status:      0,
						CreatedTime: time.Duration(time.Now().Unix()),
						Deadline:    time.Duration(time.Now().AddDate(0, 0, 0).Unix()),
						MedicineIDs: []primitive.ObjectID{},
						//SubjectApproval: models.SubjectApproval{
						//	SubjectID: subject.ID,
						//	Number:    approvalNumber,
						//},
					},
				}
				if req.UnblindingSign == "2" { // 紧急揭盲
					workTask.Info.SubjectApproval = models.SubjectApproval{
						SubjectID: subject.ID,
						Number:    approvalNumber,
					}
				} else { // pv揭盲
					workTask.Info.SubjectPvApproval = models.SubjectApproval{
						SubjectID: subject.ID,
						Number:    approvalNumber,
					}
				}
				_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
			//创建web端项目任务

			if (req.UnblindingSign == "2" && project.ProjectInfo.UnblindingProcess == 1) || (req.UnblindingSign == "3" && project.ProjectInfo.PvUnblindingProcess == 1) {
				orderAddTaskId := primitive.NewObjectID()

				approvalTitle := "project.task.urgent-unblinding.title"
				approvalType := 2
				if req.UnblindingSign != "2" {
					approvalTitle = "project.task.pv-unblinding.title"
					approvalType = 3
				}

				orderAddTask := models.OrderAddTask{
					ID:            orderAddTaskId,
					CustomerID:    subject.CustomerID,
					ProjectID:     projectOID,
					EnvironmentID: envOID,
					CohortID:      subject.CohortID,
					ProjectSiteID: subject.ProjectSiteID,
					//App:           orderInfo.App,
					ApprovalProcess: models.ApprovalProcess{
						Number:                  approvalNumber,
						Name:                    approvalTitle,
						Type:                    approvalType,
						Status:                  0,
						EstimatedCompletionTime: time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
						ApplicationTime:         applicationTime,
						ApplicationBy:           me.ID,
						ApplicationByEmail:      me.Email,
						ApplicationRoleID:       roleOID,
					},
					UnblindingData: models.UnblindingDataInfo{
						SubjectID:           subject.ID,
						SubjectNumber:       subject.Info[0].Value.(string),
						SubjectRandomNumber: subject.RandomNumber,
						Number:              approval.Number,
						Status:              0, //审批状态 0提交申请 1已通过 2已拒绝
						ApprovalType:        req.ApprovalType,
						ApplicationTime:     applicationTime,
						ApplicationBy:       me.ID,
						ApplicationByEmail:  me.Email,
						ApprovalTime:        0,
						ApprovalBy:          primitive.NilObjectID,
						ApprovalByEmail:     "",
						ReasonStr:           req.ReasonStr,
						Remark:              req.Remark,
						RejectReason:        "",
					},
				}

				if _, err := tools.Database.Collection("approval_process").InsertOne(sctx, orderAddTask); err != nil {
					return nil, errors.WithStack(err)
				}
			}

			//更新任务号
			approvalNumberSave := models.ApprovalNumber{
				ID:         primitive.NewObjectID(),
				CustomerID: subject.CustomerID,
				ProjectID:  projectOID,
				Number:     approvalNumber,
			}
			if _, err := tools.Database.Collection("approval_number").InsertOne(sctx, approvalNumberSave); err != nil {
				return nil, errors.WithStack(err)
			}
		}
		ctx.Set("HISTORY", []models.History{history})
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	type approvalResult struct {
		ApprovalNumber  string        `json:"approvalNumber"`
		ApplicationTime time.Duration `json:"applicationTime"`
	}
	result := approvalResult{
		ApprovalNumber:  approvalNumber,
		ApplicationTime: time.Duration(now.Unix()),
	}
	return result, nil
}

func GetApprovalNumber(ctx *gin.Context, projectOID primitive.ObjectID) (string, error) {
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return "", errors.WithStack(err)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	now := time.Now().UTC().Add(duration).Format("20060102")
	var approvalNumber string
	var data []models.ApprovalNumber
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"number": bson.M{"$regex": "^" + now},
			"$expr": bson.M{
				"$eq": bson.A{bson.M{"$strLenCP": "$number"}, 11},
			},
		}}},
		{{Key: "$sort", Value: bson.D{{"number", -1}}}},
		{{Key: "$limit", Value: 1}},
	}
	cursor, err := tools.Database.Collection("approval_number").Aggregate(nil, pipepine)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	if data != nil {
		maxApprovalNumber := data[0].Number
		if maxApprovalNumber[:8] == now {
			maxNumber, _ := strconv.Atoi(maxApprovalNumber[9:])
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%03s", number)
			approvalNumber = now + formatNumber
		} else {
			approvalNumber = now + "001"
		}
	} else {
		approvalNumber = now + "001"
	}

	return approvalNumber, nil
}

func (s *SubjectService) UnbindingApproval(ctx *gin.Context, sctx mongo.SessionContext, projectOID primitive.ObjectID,
	envOID primitive.ObjectID, subjectOID primitive.ObjectID, approvalNumber string, unblindingSign string, agree int,
	rejectReason string, roleOID primitive.ObjectID, now time.Time) (interface{}, error) {
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envp, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envp

	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}

	updateFilter := bson.M{"_id": subjectOID, "urgent_unblinding_approvals.number": approvalNumber}
	if unblindingSign == "3" { // pv揭盲
		updateFilter = bson.M{"_id": subjectOID, "pv_urgent_unblinding_approvals.number": approvalNumber}
	}
	update := bson.M{}
	var cohort models.Cohort

	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	match := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
	if subject.CohortID != primitive.NilObjectID {
		match = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}

	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
	subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
	subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

	if env.Cohorts != nil && len(env.Cohorts) > 0 {
		for _, co := range env.Cohorts {
			if co.ID == subject.CohortID {
				cohort = co
				break
			}
		}
	}

	if unblindingSign == "2" { // 紧急揭盲
		if subject.Status != 3 && subject.Status != 9 && (subject.Status != 4 && subject.Group != "") {
			return nil, tools.BuildServerError(ctx, "subject_status_error")
		}
	} else {
		if subject.Status != 3 && subject.Status != 6 && subject.Status != 9 && (subject.Status != 4 && subject.Group != "") {
			return nil, tools.BuildServerError(ctx, "subject_status_error")
		}
	}

	approvalP, b := slice.Find(subject.UrgentUnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
		return item.Number == approvalNumber
	})
	if unblindingSign == "3" { // pv揭盲
		approvalP, b = slice.Find(subject.PvUrgentUnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
			return item.Number == approvalNumber
		})
	}
	if b {
		approval := *approvalP
		if approval.Status == 1 || approval.Status == 2 {
			return nil, tools.BuildServerError(ctx, "urgentUnblinding_approval_task_error")
		}
	}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	langList := make([]string, 0)
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			langList = append(langList, "zh")
		} else if noticeConfig.Manual == 2 {
			langList = append(langList, "en")
		} else if noticeConfig.Manual == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
		}
	} else {
		langList = append(langList, ctx.GetHeader("Accept-Language"))
	}

	// 添加轨迹
	history := models.History{
		OID:  subjectOID,
		Time: time.Duration(now.Unix()),
		UID:  me.ID,
		User: me.Name,
	}
	//通过
	if agree == 1 {
		update = bson.M{"$set": bson.M{
			"status":                                          6,
			"urgent_unblinding_approvals.$.status":            1,
			"urgent_unblinding_status":                        1,
			"urgent_unblinding_time":                          time.Duration(now.Unix()),
			"urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
			"urgent_unblinding_approvals.$.approval_by":       me.ID,
			"urgent_unblinding_approvals.$.approval_by_email": me.Email,
		}}
		if unblindingSign == "3" { // pv揭盲
			update = bson.M{"$set": bson.M{
				//"status":                                           6,		// pv揭盲不改受试者状态
				"pv_urgent_unblinding_approvals.$.status":            1,
				"pv_unblinding_status":                               1,
				"pv_unblinding_time":                                 time.Duration(now.Unix()),
				"pv_urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
				"pv_urgent_unblinding_approvals.$.approval_by":       me.ID,
				"pv_urgent_unblinding_approvals.$.approval_by_email": me.Email,
			}}
		}

		history.Data = map[string]interface{}{"approvalNumber": approvalNumber}
		history.Key = "history.subject.unblinding-approval-agree"
		if unblindingSign == "3" {
			history.Key = "history.subject.unblinding-approval-agree-pv"
		}
		//项目动态
		{
			now := time.Duration(time.Now().Unix())
			siteName := tools.GetProjectSiteLangName(projectSite, "zh")
			siteNameEn := tools.GetProjectSiteLangName(projectSite, "en")
			OID := subject.EnvironmentID
			if !subject.CohortID.IsZero() {
				OID = subject.CohortID
			}
			typeTran := "project_dynamics_type_emergency_unblinding"
			contentTran := "project_dynamics_content_emergency_unblinding_emergency"
			if unblindingSign == "3" {
				typeTran = "project_dynamics_type_emergency_unblinding_pv"
				contentTran = "project_dynamics_content_emergency_unblinding_pv"
			}
			dynamics := models.ProjectDynamics{
				ID:          primitive.NewObjectID(),
				Operator:    me.ID,
				OID:         OID,
				Time:        now,
				SceneTran:   "project_dynamics_scene_unblinding",
				TypeTran:    typeTran,
				ContentTran: contentTran,
				ContentData: map[string]interface{}{
					"subjectId":   subject.ID,
					"subjectName": subject.Info[0].Value,
					"siteId":      projectSite.ID,
					"siteName":    siteName,
					"siteNameEn":  siteNameEn,
				},
			}
			_, err := tools.Database.Collection("project_dynamics").InsertOne(sctx, dynamics)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

	} else if agree == 2 {
		update = bson.M{"$set": bson.M{
			"urgent_unblinding_approvals.$.status":            2,
			"urgent_unblinding_approvals.$.reject_reason":     rejectReason,
			"urgent_unblinding_time":                          time.Duration(now.Unix()),
			"urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
			"urgent_unblinding_approvals.$.approval_by":       me.ID,
			"urgent_unblinding_approvals.$.approval_by_email": me.Email,
		}}
		if unblindingSign == "3" { // pv揭盲
			update = bson.M{"$set": bson.M{
				"pv_urgent_unblinding_approvals.$.status":            2,
				"pv_urgent_unblinding_approvals.$.reject_reason":     rejectReason,
				"pv_unblinding_time":                                 time.Duration(now.Unix()),
				"pv_urgent_unblinding_approvals.$.approval_time":     time.Duration(now.Unix()),
				"pv_urgent_unblinding_approvals.$.approval_by":       me.ID,
				"pv_urgent_unblinding_approvals.$.approval_by_email": me.Email,
			}}
		}

		history.Data = map[string]interface{}{"approvalNumber": approvalNumber, "reason": rejectReason}
		history.Key = "history.subject.unblinding-approval-reject"
		if unblindingSign == "3" {
			history.Key = "history.subject.unblinding-approval-reject-pv"
		}
	}
	updateResult, err := tools.Database.Collection("subject").UpdateOne(sctx, updateFilter, update)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	fmt.Print(updateResult)

	ctx.Set("HISTORY", []models.History{history})

	// 揭盲审批发送的邮件通知（通知对象：操作人+审批人）
	userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
	userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": subject.EnvironmentID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var userIDs []primitive.ObjectID
	for _, upel := range userProjectEnvironmentList {
		userIDs = append(userIDs, upel.UserID)
	}
	// 查询user
	var userList []models.User
	userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userCursor.All(nil, &userList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目角色权限
	projectRolePermissionList := make([]models.ProjectRolePermission, 0)
	projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": subject.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询用户关联的角色
	userSiteList := make([]models.UserSite, 0)
	userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": subject.EnvironmentID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userSiteCursor.All(nil, &userSiteList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var nc models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": env.ID, "key": "notice.subject.unblinding"}).Decode(&nc)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, err
	}

	excludeRecipientMap := make(map[string]bool)
	if nc.ExcludeRecipientList != nil && len(nc.ExcludeRecipientList) > 0 {
		excludeRecipientMap = getArrMap(nc.ExcludeRecipientList)
	}

	emailList := make([]string, 0)
	unblindingApprovalUser, pvUnblindingApprovalUser, _ := queryApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subject.ProjectSiteID)
	if unblindingSign == "2" { // 紧急揭盲
		for _, uau := range unblindingApprovalUser {
			emailList = append(emailList, uau.Email)
		}
	} else {
		for _, puau := range pvUnblindingApprovalUser {
			emailList = append(emailList, puau.Email)
		}
	}

	// 查询揭盲审批发起人
	emailSign := true
	var applicationUser models.User
	ap := *approvalP
	err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": ap.ApplicationBy}).Decode(&applicationUser)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if emailList != nil && len(emailList) > 0 {
		for _, e := range emailList {
			if e == applicationUser.Email {
				emailSign = false
				break
			}
		}
	}
	if emailSign {
		emailList = append(emailList, applicationUser.Email)
	}

	emails := make([]string, 0)
	for _, email := range emailList {
		isExists := false
		if _, is := excludeRecipientMap[email]; is {
			isExists = true
		}
		if !isExists {
			emails = append(emails, email)
		}
	}

	shTime, err := tools.GetTimeZoneTime(now.UTC(), projectSite, project)
	if err != nil {
		return nil, err
	}

	// 收件人
	var mails []models.Mail
	var envs []models.MailEnv

	var mailsA []models.Mail
	if emails != nil && len(emails) > 0 {
		remark := ""
		for _, approval := range subject.UrgentUnblindingApprovals {
			if approval.Number == approvalNumber {
				remark = approval.Remark
				break
			}
		}
		if unblindingSign == "3" {
			for _, approval := range subject.PvUrgentUnblindingApprovals {
				if approval.Number == approvalNumber {
					remark = approval.Remark
					break
				}
			}
		}

		mailBodyContet, err := tools.MailBodyContent(ctx, env.ID, "notice.subject.unblinding")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, email := range emails {
			var toUserMail []string
			toUserMail = append(toUserMail, email)
			mail := models.Mail{
				ID:           primitive.NewObjectID(),
				To:           toUserMail,
				Lang:         ctx.GetHeader("Accept-Language"),
				LangList:     langList,
				Status:       0,
				CreatedTime:  time.Duration(now.Unix()),
				ExpectedTime: time.Duration(now.Unix()),
				SendTime:     time.Duration(now.Unix()),
			}
			mail.Subject = "subject.unblinding-approval.title"
			if unblindingSign == "3" {
				mail.Subject = "subject.pv-unblinding-approval.title"
			}
			mail.SubjectData = map[string]interface{}{
				"projectNumber": project.Number,
				"envName":       env.Name,
				"siteNumber":    projectSite.Number,
				"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
				"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
			}
			mail.Content = "subject.unblinding-approval.content"
			if unblindingSign == "3" {
				mail.Content = "subject.pv-unblinding-approval.content"
			}
			randomNumber := tools.BlindData
			if attribute.AttributeInfo.IsRandomNumber {
				randomNumber = subject.RandomNumber
			}
			reason := subject.UrgentUnblindingReasonStr
			if unblindingSign == "3" {
				reason = subject.PvUnblindingReasonStr
			}
			contentData := map[string]interface{}{
				"projectNumber":    project.Number,
				"projectName":      project.Name,
				"envName":          env.Name,
				"siteNumber":       projectSite.Number,
				"siteName":         tools.GetProjectSiteLangName(projectSite, "zh"),
				"siteNameEn":       tools.GetProjectSiteLangName(projectSite, "en"),
				"subjectNumber":    subject.Info[0].Value,
				"randomNumber":     randomNumber,
				"time":             shTime,
				"reason":           reason,
				"approvalResult":   tools.GetApprovalResultTranLang("zh", agree),
				"approvalResultEn": tools.GetApprovalResultTranLang("en", agree),
				"rejectReason":     rejectReason,
				"remark":           remark,
				"approvalNumber":   approvalNumber,
				"label":            subjectEmailReplaceTextZh,
				"labelEn":          subjectEmailReplaceTextEn,
			}
			if !cohort.ID.IsZero() {
				contentData["cohortName"] = models.GetCohortReRandomName(cohort)
			}
			for key, v := range mailBodyContet {
				contentData[key] = v
			}
			bodyContentKeys, _, err := tools.MailCustomContent(&gin.Context{}, env.ID, "notice.subject.unblinding", contentData, nil)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			mail.BodyContentKey = bodyContentKeys
			mail.ContentData = contentData
			mailsA = append(mailsA, mail)
			mails = append(mails, mail)
		}
	}
	if len(mailsA) > 0 {
		for _, m := range mailsA {
			envs = append(envs, models.MailEnv{
				ID:         primitive.NewObjectID(),
				MailID:     m.ID,
				CustomerID: subject.CustomerID,
				ProjectID:  subject.ProjectID,
				EnvID:      subject.EnvironmentID,
				CohortID:   subject.CohortID,
			})
		}
	}

	// 审批通过并且是紧急揭盲才发送审批通过邮件提醒（通知对象：通知设置-紧急揭盲的角色）
	if agree == 1 && unblindingSign == "2" {
		// 揭盲发送邮件
		userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.unblinding", subject.ProjectSiteID)
		if err != nil {
			return nil, err
		}
		var mailsB []models.Mail
		if len(userMail) > 0 {
			for _, userEmail := range userMail {
				var toUserMail []string
				toUserMail = append(toUserMail, userEmail.Email)
				mail := models.Mail{
					ID:           primitive.NewObjectID(),
					To:           toUserMail,
					Lang:         ctx.GetHeader("Accept-Language"),
					LangList:     langList,
					Status:       0,
					CreatedTime:  time.Duration(now.Unix()),
					ExpectedTime: time.Duration(now.Unix()),
					SendTime:     time.Duration(now.Unix()),
				}
				mail.Subject = "subject.ordinary-unblinding-approval.title"
				mail.SubjectData = map[string]interface{}{
					"projectNumber": project.Number,
					"envName":       env.Name,
					"siteNumber":    projectSite.Number,
					"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
					"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
				}
				mail.Content = "subject.ordinary-unblinding-approval.content"
				randomNumber := tools.BlindData
				if attribute.AttributeInfo.IsRandomNumber {
					randomNumber = subject.RandomNumber
				}
				reason := subject.UrgentUnblindingReasonStr
				contentData := map[string]interface{}{
					"projectNumber": project.Number,
					"projectName":   project.Name,
					"envName":       env.Name,
					"siteNumber":    projectSite.Number,
					"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
					"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
					"label":         subjectEmailReplaceTextZh,
					"labelEn":       subjectEmailReplaceTextEn,
					"subjectNumber": subject.Info[0].Value,
					"randomNumber":  randomNumber,
					"time":          shTime,
					"reason":        reason,
				}
				mail.ContentData = contentData
				mailsB = append(mailsB, mail)
				mails = append(mails, mail)
			}
		}

		if len(mailsB) > 0 {
			for _, m := range mailsB {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: subject.CustomerID,
					ProjectID:  subject.ProjectID,
					EnvID:      subject.EnvironmentID,
					CohortID:   subject.CohortID,
				})
			}
		}
	}

	if mails != nil && len(mails) > 0 {
		ctx.Set("MAIL", mails)
	}
	if envs != nil && len(envs) > 0 {
		ctx.Set("MAIL-ENV", envs)
	}

	// 同步app任务表
	// 查询当前受试者的app任务表是否有未完成的揭盲审批任务
	existFilter := bson.M{
		"customer_id":    subject.CustomerID,
		"project_id":     subject.ProjectID,
		"env_id":         subject.EnvironmentID,
		"info.work_type": 8, // 揭盲审批任务
		"info.status":    0, // 状态未完成
	}
	if unblindingSign == "3" { // pv揭盲
		existFilter["info.subject_pv_approval.subject_id"] = subject.ID
		existFilter["info.work_type"] = 10
	} else { // 紧急揭盲
		existFilter["info.subject_approval.subject_id"] = subject.ID
	}

	if !subject.CohortID.IsZero() {
		existFilter["cohort_id"] = subject.CohortID
	}
	var existWorkTask models.WorkTask
	err = tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&existWorkTask)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	if !existWorkTask.ID.IsZero() { // 有未完成的任务 要把状态同步为完成
		workTaskUpdate := bson.M{"$set": bson.M{
			"info.status":         1,
			"info.finish_time":    time.Duration(time.Now().Unix()),
			"info.finish_user_id": me.ID,
			"info.finish_role_id": roleOID,
		}}
		_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": existWorkTask.ID}, workTaskUpdate)
	}

	return nil, nil
}

func (s *SubjectService) UrgentUnblindingApproval(ctx *gin.Context) (interface{}, error) {
	//参数转化
	req := struct {
		ProjectId      string `json:"projectId"`
		EnvId          string `json:"envId"`
		RoleId         string `json:"roleId"`
		AttributeId    string `json:"attributeId"`
		SubjectId      string `json:"subjectId"`
		Agree          int    `json:"agree"`
		RejectReason   string `json:"rejectReason"`
		ApprovalNumber string `json:"approvalNumber"`
		UnblindingSign string `json:"unblindingSign"`
	}{}
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	roleOID, err := primitive.ObjectIDFromHex(req.RoleId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envOID, err := primitive.ObjectIDFromHex(req.EnvId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	subjectOID, err := primitive.ObjectIDFromHex(req.SubjectId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	projectOID, err := primitive.ObjectIDFromHex(req.ProjectId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var now time.Time
	now = time.Now()
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		s.UnbindingApproval(ctx, sctx, projectOID, envOID, subjectOID, req.ApprovalNumber, req.UnblindingSign, req.Agree, req.RejectReason, roleOID, now)

		me, err := tools.Me(ctx)
		if err != nil {
			return nil, err
		}
		//查询对应的项目任务
		taskType := 2
		if req.UnblindingSign == "3" {
			taskType = 3
		}
		//更新审批任务
		match := bson.M{"project_id": projectOID, "env_id": envOID, "type": taskType, "status": 0, "unblinding_data.subject_id": subjectOID, "unblinding_data.number": req.ApprovalNumber}

		update := bson.M{
			"$set": bson.M{
				"approval_status":                   req.Agree,
				"approval_time":                     time.Duration(time.Now().Unix()),
				"approval_by":                       me.ID,
				"approval_by_email":                 me.Email,
				"reason":                            req.RejectReason,
				"status":                            1,
				"unblinding_data.approval_time":     time.Duration(time.Now().Unix()),
				"unblinding_data.approval_by":       me.ID,
				"unblinding_data.approval_by_email": me.Email,
				"unblinding_data.reason":            req.RejectReason,
				"unblinding_data.status":            req.Agree,
			},
		}
		_, err = tools.Database.Collection("approval_process").UpdateOne(sctx, match, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	type approvalResult struct {
		ApprovalNumber string        `json:"approvalNumber"`
		ApprovalTime   time.Duration `json:"approvalTime"`
	}
	result := approvalResult{
		ApprovalTime: time.Duration(now.Unix()),
	}
	return result, nil
}

func (s *SubjectService) ResendSms(ctx *gin.Context) error {
	//参数转化
	req := struct {
		SubjectId             string `json:"subjectId"`
		ApprovalNumber        string `json:"approvalNumber"`
		UnblindingDetailsSign string `json:"unblindingDetailsSign"`
		MedicineID            string `json:"medicineId"`
	}{}
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return errors.WithStack(err)
	}
	subjectOID, err := primitive.ObjectIDFromHex(req.SubjectId)
	if err != nil {
		return errors.WithStack(err)
	}
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return errors.WithStack(err)
	}

	approvalP, _ := slice.Find(subject.UrgentUnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
		return item.Number == req.ApprovalNumber
	})
	if req.UnblindingDetailsSign == "3" { // pv揭盲
		approvalP, _ = slice.Find(subject.PvUrgentUnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
			return item.Number == req.ApprovalNumber
		})
	}
	if req.UnblindingDetailsSign == "4" { // ip揭盲
		medicineOID, err := primitive.ObjectIDFromHex(req.MedicineID)
		if err != nil {
			return errors.WithStack(err)
		}
		var medicine models.Medicine
		err = tools.Database.Collection("medicine").FindOne(nil, bson.M{"_id": medicineOID}).Decode(&medicine)
		if err != nil {
			return err
		}
		approvalP, _ = slice.Find(medicine.UnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
			return item.Number == req.ApprovalNumber
		})
	}
	approval := *approvalP
	cloudIds := slice.Map(approval.SmsUsers, func(index int, i models.SmsUser) string {
		return i.CloudId.Hex()
	})
	userData, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
	phones := slice.Map(userData, func(index int, item *models.UserData) string {
		return item.Info.Mobile
	})
	phones = slice.Compact(phones)
	if req.UnblindingDetailsSign == "4" {
		err = tools.SendMedicineUnblindingApproval(approval.Msg, phones)
		if err != nil {
			return err
		}
	} else {
		err = tools.SendUrgentUnblindingApproval(approval.Msg, phones)
		if err != nil {
			return err
		}
	}

	return nil
}

// 生成审批编号
func generateUrgentUnblindingApprovalNumber(sctx mongo.SessionContext, subject models.Subject) (string, error) {
	// 查询紧急揭盲
	var approvalNumber string
	var d []struct {
		Number string `bson:"number"`
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"deleted": bson.M{"$ne": true}}}},
		{{Key: "$unwind", Value: "$urgent_unblinding_approvals"}},
		{{Key: "$sort", Value: bson.D{{"urgent_unblinding_approvals.number", -1}}}},
		{{Key: "$limit", Value: 1}},
		{{Key: "$project", Value: bson.M{
			"number": "$urgent_unblinding_approvals.number",
		}}},
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(sctx, pipeline)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &d)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 查询pv揭盲
	var p []struct {
		Number string `bson:"number"`
	}
	pipeline = mongo.Pipeline{
		{{Key: "$unwind", Value: "$pv_urgent_unblinding_approvals"}},
		{{Key: "$sort", Value: bson.D{{"pv_urgent_unblinding_approvals.number", -1}}}},
		{{Key: "$limit", Value: 1}},
		{{Key: "$project", Value: bson.M{
			"number": "$pv_urgent_unblinding_approvals.number",
		}}},
	}
	cursor, err = tools.Database.Collection("subject").Aggregate(sctx, pipeline)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &p)
	if err != nil {
		return "", errors.WithStack(err)
	}

	now := time.Now().UTC().Format("20060102")
	maxNumber := 0
	if d != nil && p != nil {
		dpn := ""
		dNumber, _ := strconv.Atoi(d[0].Number)
		pNumber, _ := strconv.Atoi(p[0].Number)
		if dNumber > pNumber {
			dpn = d[0].Number
			maxNumber, _ = strconv.Atoi(d[0].Number[9:])
		} else {
			dpn = p[0].Number
			maxNumber, _ = strconv.Atoi(p[0].Number[9:])
		}
		if dpn[:8] == now {
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%03s", number)
			approvalNumber = now + formatNumber
		} else {
			approvalNumber = now + "001"
		}
	} else if d != nil && p == nil {
		if d[0].Number[:8] == now {
			maxNumber, _ = strconv.Atoi(d[0].Number[9:])
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%03s", number)
			approvalNumber = now + formatNumber
		} else {
			approvalNumber = now + "001"
		}
	} else if d == nil && p != nil {
		if p[0].Number[:8] == now {
			maxNumber, _ = strconv.Atoi(p[0].Number[9:])
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%03s", number)
			approvalNumber = now + formatNumber
		} else {
			approvalNumber = now + "001"
		}
	} else {
		approvalNumber = now + "001"
	}

	//if d != nil {
	//	maxApprovalNumber := d[0].Number
	//	if maxApprovalNumber[:8] == now {
	//		maxNumber, _ := strconv.Atoi(maxApprovalNumber[9:])
	//		number := strconv.Itoa(maxNumber + 1)
	//		formatNumber := fmt.Sprintf("%03s", number)
	//		approvalNumber = now + formatNumber
	//	} else {
	//		approvalNumber = now + "001"
	//	}
	//} else {
	//	approvalNumber = now + "001"
	//}

	return approvalNumber, nil
}

/************************************************************************** 以下是随机入组公共方法 ********************************************************************/

// 提取随机号
func extractRandomNumber(sctx mongo.SessionContext, ctx *gin.Context, subject models.Subject, projectSite models.ProjectSite, attribute models.Attribute, randomDesign models.RandomDesign, randomList models.RandomList, canGroups []string, projectNumber string) (models.RandomNumberReturn, error) {
	var randomNumberReturn models.RandomNumberReturn // 返回的数据
	var randomNumbers []models.RandomNumber          // 查询数据库返回的随机号
	var err error
	switch randomDesign.Info.Type {
	// 区组或者分层区组随机
	case 1:
		{
			// 中心/国家/区域没有随机号不可以随机入组
			if attribute.AttributeInfo.IsRandom || attribute.AttributeInfo.IsCountryRandom || attribute.AttributeInfo.IsRegionRandom {
				randomNumbers, err = findRandomNumbersIsRandom(sctx, attribute, projectSite, randomList, canGroups)
				if err != nil {
					return models.RandomNumberReturn{}, err
				}
				if randomNumbers != nil && len(randomNumbers) > 0 { // 根据中心ID查到了随机号
					randomNumberReturn, err = randomNumberScreening(sctx, attribute, subject, randomList, randomNumbers)
					if err != nil {
						return models.RandomNumberReturn{}, err
					}
				}
			} else { // 中心/国家/区域没有随机号可以随机入组
				randomNumbers, err = findRandomNumbersNotRandom(sctx, attribute, projectSite, randomList, canGroups)
				if err != nil {
					return models.RandomNumberReturn{}, err
				}
				if randomNumbers != nil && len(randomNumbers) > 0 { // 根据中心ID查到了随机号
					randomNumberReturn, err = randomNumberScreening(sctx, attribute, subject, randomList, randomNumbers)
					if err != nil {
						return models.RandomNumberReturn{}, err
					}
				}
			}
		}
	// 最小化随机
	case 2:
		{
			// 查询随机号
			randomNumbers, err = findRandomNumbers(sctx, primitive.NilObjectID, randomList, canGroups)
			subjectList, err := findSubject(sctx, subject, randomList, projectNumber)
			if err != nil {
				return models.RandomNumberReturn{}, err
			}
			randomNumberCount, err := findRandomNumbersCount(sctx, randomList)
			if err != nil {
				return models.RandomNumberReturn{}, err
			}
			randomNumberReturn, err = obtainNumber(ctx, sctx, attribute, randomList, randomNumbers, subject, subjectList, randomNumberCount, canGroups)
			if err != nil {
				return models.RandomNumberReturn{}, err
			}
		}
	}

	return randomNumberReturn, nil
}

// 中心/国家/区域没有随机号不可以随机入组
func findRandomNumbersIsRandom(sctx mongo.SessionContext, attribute models.Attribute, projectSite models.ProjectSite, random models.RandomList, canGroups []string) ([]models.RandomNumber, error) {
	// RandomNumber
	randomNumbers := make([]models.RandomNumber, 0)
	// 排序
	opts := &options.FindOptions{
		Sort: bson.D{{"_id", 1}},
	}
	country := ""
	if projectSite.Country != nil && len(projectSite.Country) > 0 {
		country = projectSite.Country[0]
	}
	// 不分层的情况
	match := bson.M{
		"random_list_id": random.ID,
		"status":         1,
	}
	if canGroups != nil {
		match["group"] = bson.M{"$in": canGroups}
	}
	if attribute.AttributeInfo.IsRandom { //中心入组限制
		match["project_site_id"] = projectSite.ID
		match["$and"] = []bson.M{
			{"$or": []bson.M{
				{"country": country},
				{"country": ""},
				{"country": bson.M{"$eq": nil}},
				{"country": bson.M{"$exists": false}},
			}},
			{"$or": []bson.M{
				{"region_id": bson.M{"$in": bson.A{projectSite.RegionID, primitive.NilObjectID}}},
				{"region_id": bson.M{"$eq": nil}},
				{"region_id": bson.M{"$exists": false}},
			}},
		}
	} else if attribute.AttributeInfo.IsCountryRandom { //国家入组限制
		match["project_site_id"] = bson.M{"$in": bson.A{projectSite.ID, primitive.NilObjectID}}
		if country != "" {
			match["country"] = country
			match["$or"] = []bson.M{
				{"region_id": bson.M{"$in": bson.A{projectSite.RegionID, primitive.NilObjectID}}},
				{"region_id": bson.M{"$eq": nil}},
				{"region_id": bson.M{"$exists": false}},
			}
		} else {
			match["$and"] = []bson.M{
				{"$or": []bson.M{
					{"country": ""},
					{"country": bson.M{"$eq": nil}},
					{"country": bson.M{"$exists": false}},
				}},
				{"$or": []bson.M{
					{"region_id": bson.M{"$in": bson.A{projectSite.RegionID, primitive.NilObjectID}}},
					{"region_id": bson.M{"$eq": nil}},
					{"region_id": bson.M{"$exists": false}},
				}},
			}
		}
	} else if attribute.AttributeInfo.IsRegionRandom { //区域入组限制
		match["project_site_id"] = bson.M{"$in": bson.A{projectSite.ID, primitive.NilObjectID}}
		if !projectSite.RegionID.IsZero() {
			match["region_id"] = projectSite.RegionID
			match["$or"] = []bson.M{
				{"country": country},
				{"country": ""},
				{"country": bson.M{"$eq": nil}},
				{"country": bson.M{"$exists": false}},
			}
		} else {
			match["$and"] = []bson.M{
				{"$or": []bson.M{
					{"region_id": bson.M{"$eq": primitive.NilObjectID}},
					{"region_id": bson.M{"$eq": nil}},
					{"region_id": bson.M{"$exists": false}},
				}},
				{"$or": []bson.M{
					{"country": country},
					{"country": ""},
					{"country": bson.M{"$eq": nil}},
					{"country": bson.M{"$exists": false}},
				}},
			}
		}
	} else {
		return []models.RandomNumber{}, nil
	}
	//不分层或中心分层
	if !attribute.AttributeInfo.CountryLayered && !attribute.AttributeInfo.RegionLayered {
		opts = &options.FindOptions{
			Sort: bson.D{{"project_site_id", -1}, {"country", -1}, {"region_id", -1}, {"_id", 1}},
		}
	} else if attribute.AttributeInfo.CountryLayered {
		opts = &options.FindOptions{
			Sort: bson.D{{"project_site_id", -1}, {"country", -1}, {"region_id", -1}, {"_id", 1}},
		}
	} else if attribute.AttributeInfo.RegionLayered {
		opts = &options.FindOptions{
			Sort: bson.D{{"project_site_id", -1}, {"region_id", -1}, {"country", -1}, {"_id", 1}},
		}
	} else {
		return []models.RandomNumber{}, nil
	}
	cursor, err := tools.Database.Collection("random_number").Find(sctx, match, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(nil, &randomNumbers); err != nil {
		return nil, errors.WithStack(err)
	}
	return randomNumbers, nil
}

// 中心/国家/区域没有随机号可以随机入组
func findRandomNumbersNotRandom(sctx mongo.SessionContext, attribute models.Attribute, projectSite models.ProjectSite, random models.RandomList, canGroups []string) ([]models.RandomNumber, error) {
	// RandomNumber
	randomNumbers := make([]models.RandomNumber, 0)
	// 排序
	opts := &options.FindOptions{
		Sort: bson.D{{"_id", 1}},
	}
	// 条件
	country := ""
	if projectSite.Country != nil && len(projectSite.Country) > 0 {
		country = projectSite.Country[0]
	}
	match := bson.M{
		"random_list_id":  random.ID,
		"project_site_id": bson.M{"$in": bson.A{projectSite.ID, primitive.NilObjectID}},
		"$and": []bson.M{
			{"$or": []bson.M{
				{"country": country},
				{"country": ""},
				{"country": bson.M{"$eq": nil}},
				{"country": bson.M{"$exists": false}},
			}},
			{"$or": []bson.M{
				{"region_id": bson.M{"$in": bson.A{projectSite.RegionID, primitive.NilObjectID}}},
				{"region_id": bson.M{"$eq": nil}},
				{"region_id": bson.M{"$exists": false}},
			}},
		},
		"status": 1,
	}
	if canGroups != nil {
		match["group"] = bson.M{"$in": canGroups}
	}
	//不分层或中心分层
	if !attribute.AttributeInfo.CountryLayered && !attribute.AttributeInfo.RegionLayered {
		opts = &options.FindOptions{
			Sort: bson.D{{"project_site_id", -1}, {"_id", 1}},
		}
	} else if attribute.AttributeInfo.CountryLayered {
		opts = &options.FindOptions{
			Sort: bson.D{{"project_site_id", -1}, {"country", -1}, {"_id", 1}},
		}
	} else if attribute.AttributeInfo.RegionLayered {
		opts = &options.FindOptions{
			Sort: bson.D{{"project_site_id", -1}, {"region_id", -1}, {"_id", 1}},
		}
	} else {
		return []models.RandomNumber{}, nil
	}
	cursor, err := tools.Database.Collection("random_number").Find(sctx, match, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(nil, &randomNumbers); err != nil {
		return nil, errors.WithStack(err)
	}
	return randomNumbers, nil
}

// 查询随机公共方法
func findRandomNumbers(sctx mongo.SessionContext, projectSiteID primitive.ObjectID, random models.RandomList, canGroups []string) ([]models.RandomNumber, error) {
	// RandomNumber
	var d []models.RandomNumber

	// 排序
	opts := &options.FindOptions{
		Sort: bson.D{{"_id", 1}},
	}
	// 条件
	match := bson.M{}
	if canGroups != nil {
		match["group"] = bson.M{"$in": canGroups}
	}
	if projectSiteID != primitive.NilObjectID {
		match = bson.M{
			"random_list_id":  random.ID,
			"project_site_id": projectSiteID,
			"status":          1,
		}
	} else {
		match = bson.M{
			"random_list_id":  random.ID,
			"project_site_id": primitive.NilObjectID,
			"status":          1,
		}
	}
	cursor, err := tools.Database.Collection("random_number").Find(sctx, match, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(nil, &d); err != nil {
		return nil, errors.WithStack(err)
	}
	return d, nil
}

// 查询随机号数量(最小化随机需要)
func findRandomNumbersCount(sctx mongo.SessionContext, random models.RandomList) (int, error) {
	var d []models.RandomNumber
	match := bson.M{
		"random_list_id": random.ID,
	}
	cursor, err := tools.Database.Collection("random_number").Find(sctx, match)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if err = cursor.All(nil, &d); err != nil {
		return 0, errors.WithStack(err)
	}
	return len(d), nil
}

// 查询已入组的受试者公共方法

func findSubject(sctx mongo.SessionContext, subject models.Subject, randomList models.RandomList, projectNumber string) ([]models.Subject, error) {
	var subjectList []models.Subject
	match := bson.M{
		"customer_id": subject.CustomerID,
		"project_id":  subject.ProjectID,
		"env_id":      subject.EnvironmentID,
		"status":      bson.M{"$nin": [3]int{1, 2, 5}},
	}
	if subject.CohortID != primitive.NilObjectID {
		match["cohort_id"] = subject.CohortID
	}
	//特殊项目处理 SR1375-204
	if projectNumber != "SR1375-204" {
		match["random_list_id"] = randomList.ID
	}
	cursor, err := tools.Database.Collection("subject").Find(sctx, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err = cursor.All(nil, &subjectList); err != nil {
		return nil, errors.WithStack(err)
	}
	return subjectList, nil
}

// 随机号筛选
func randomNumberScreening(sctx mongo.SessionContext, attribute models.Attribute, subject models.Subject, randomList models.RandomList, randomNumbers []models.RandomNumber) (models.RandomNumberReturn, error) {
	var randomNumberReturn models.RandomNumberReturn                             // 返回的数据
	if randomList.Design.Factors != nil || len(randomList.Design.Factors) != 0 { // 带分层因素
		info := make([]models.Info, 0)
		// 得到subject表单里填写的分层因素
		for _, designFactor := range randomList.Design.Factors {
			if designFactor.Status == nil || *designFactor.Status == 1 {
				for _, subjectInfo := range subject.Info {
					if designFactor.Name == subjectInfo.Name {
						info = append(info, subjectInfo)
						break
					}
				}
			}
		}
		// 筛选
		factorCount := slice.Count(randomList.Design.Factors, func(index int, item models.RandomFactor) bool {
			return item.Status == nil || *item.Status == 1
		})
		for _, randomNumber := range randomNumbers {
			if factorMatching(info, randomNumber.Factors, factorCount) {
				return models.RandomNumberReturn{
					ID:           randomNumber.ID,
					RandomListID: randomNumber.RandomListID,
					RandomNumber: randomNumber.Number,
					Group:        randomNumber.Group,
					ParName:      randomNumber.ParName,
					SubName:      randomNumber.SubName,
					BlockNumber:  randomNumber.Block,
					OccupySign:   occupySign(attribute, randomNumber),
					LayeredSign:  false,
				}, nil
			}
		}

		// 走到这里说明还是没有筛选到，此时应该排除分层因素继续筛选
		breakBlock := 0
		for _, randomNumber := range randomNumbers {
			if randomNumber.Factors == nil || len(randomNumber.Factors) == 0 {
				if breakBlock == randomNumber.Block {
					continue
				}
				numbers := make([]models.RandomNumber, 0)
				cursor, err := tools.Database.Collection("random_number").Find(sctx, bson.M{"random_list_id": randomList.ID, "block": randomNumber.Block, "status": 2})
				if err != nil {
					return models.RandomNumberReturn{}, err
				}
				err = cursor.All(sctx, &numbers)
				if err != nil {
					return models.RandomNumberReturn{}, err
				}
				_, ok := slice.Find(numbers, func(index int, item models.RandomNumber) bool {
					return item.Factors == nil || len(item.Factors) == 0
				})
				if ok {
					breakBlock = randomNumber.Block
					continue
				}
				return models.RandomNumberReturn{
					ID:           randomNumber.ID,
					RandomListID: randomNumber.RandomListID,
					RandomNumber: randomNumber.Number,
					Group:        randomNumber.Group,
					ParName:      randomNumber.ParName,
					SubName:      randomNumber.SubName,
					BlockNumber:  randomNumber.Block,
					OccupySign:   occupySign(attribute, randomNumber),
					LayeredSign:  true,
				}, nil
			}
		}
	} else { // 不带分层因素
		for _, randomNumber := range randomNumbers {
			return models.RandomNumberReturn{
				ID:           randomNumber.ID,
				RandomListID: randomNumber.RandomListID,
				RandomNumber: randomNumber.Number,
				Group:        randomNumber.Group,
				ParName:      randomNumber.ParName,
				SubName:      randomNumber.SubName,
				BlockNumber:  randomNumber.Block,
				OccupySign:   occupySign(attribute, randomNumber),
				LayeredSign:  false,
			}, nil
		}
	}
	return randomNumberReturn, nil
}
func occupySign(attribute models.Attribute, randomNumber models.RandomNumber) bool {
	//if !attribute.AttributeInfo.InstituteLayered && !attribute.AttributeInfo.CountryLayered && !attribute.AttributeInfo.RegionLayered {
	//	return false
	//} else {
	//	if attribute.AttributeInfo.InstituteLayered { //中心分层
	//		if randomNumber.ProjectSiteID.IsZero() {
	//			return true
	//		}
	//	} else if attribute.AttributeInfo.CountryLayered { //国家分层
	//		if randomNumber.Country == "" {
	//			return true
	//		}
	//	} else if attribute.AttributeInfo.RegionLayered { //区域分层
	//		if randomNumber.RegionID.IsZero() {
	//			return true
	//		}
	//	}
	//	return false
	//}
	return true
}

// 分层因素筛选辅助方法
func factorMatching(subjectInfo []models.Info, factor []models.Factors, factorCount int) bool {
	if factor == nil {
		factor = make([]models.Factors, 0)
	}
	factor = slice.Filter(factor, func(index int, item models.Factors) bool {
		return item.Value != ""
	})
	matchLength := 0
	for _, f := range factor {
		for _, info := range subjectInfo {
			if info.Name == f.Name {
				if info.Value == f.Value {
					matchLength++
				} else {
					return false
				}
			}
		}
	}
	return (len(subjectInfo) == 0 && len(factor) == 0) || (matchLength == len(subjectInfo) && matchLength == len(factor) && matchLength == factorCount)
}

// 最小化随机提取随机号
func obtainNumber(ctx *gin.Context, sctx mongo.SessionContext, attribute models.Attribute, random models.RandomList, randomNumberList []models.RandomNumber, subject models.Subject, subjectList []models.Subject, randomNumberCount int, canGroups []string) (models.RandomNumberReturn, error) {
	var randomNumberReturn models.RandomNumberReturn
	for _, randomNumber := range randomNumberList {
		return minimizingRandom(ctx, sctx, attribute, random, randomNumber, subjectList, subject, randomNumberCount, canGroups)
	}
	return randomNumberReturn, nil
}

func minimizingRandom(ctx *gin.Context, sctx mongo.SessionContext, attribute models.Attribute, random models.RandomList, randomNumber models.RandomNumber, subjectList []models.Subject, subject models.Subject, randomNumberCount int, canGroups []string) (models.RandomNumberReturn, error) {

	// 判断每组已入组人数是否达到比例要求（假设A组的入组人数已经达到要求，就要把A组以及对应的比例剔除）
	nodesNumberList := obtainMinimizeNumber(random, randomNumberCount)
	var groups []models.Group
	var groupRatio []int

	for i, node := range nodesNumberList {
		count := 0
		for _, subject := range subjectList {
			if subject.Group == random.Design.Groups[i].Name {
				count++
			}
		}
		if count < node {
			if canGroups != nil {
				exist := arrays.ContainsString(canGroups, random.Design.Groups[i].Name)
				if exist != -1 { //-1 说明存在
					groups = append(groups, models.Group{
						Group:   random.Design.Groups[i].Name,
						ParName: random.Design.Groups[i].ParName,
						SubName: random.Design.Groups[i].SubName,
					})
					groupRatio = append(groupRatio, random.Design.Groups[i].Ratio)
				}
			} else {
				groups = append(groups, models.Group{
					Group:   random.Design.Groups[i].Name,
					ParName: random.Design.Groups[i].ParName,
					SubName: random.Design.Groups[i].SubName,
				})
				groupRatio = append(groupRatio, random.Design.Groups[i].Ratio)
			}
		}
	}

	// 强制随机
	if len(groups) == 1 {
		return minimizingGetRandomNumber(attribute, groups[0], random, randomNumber, subject, nil, groups), nil
	}

	// 判断是否已有病人入组
	if subjectList == nil || len(subjectList) == 0 { // 没有受试者入组
		return distributeGroup(attribute, random, randomNumber, subject, nil, groups), nil
	}
	// 有受试者入组
	return distributionGroups(ctx, sctx, attribute, random, randomNumber, subjectList, subject, groups, groupRatio)
}

// 在Map中提取随机号(最小化随机)
func minimizingGetRandomNumber(attribute models.Attribute, group models.Group, random models.RandomList, randomNumber models.RandomNumber, subject models.Subject, gValueList []float64, groups []models.Group) models.RandomNumberReturn {
	randomNumber.Group = group.Group     // 返回组别
	randomNumber.ParName = group.ParName // 返回组别
	randomNumber.SubName = group.SubName // 返回组别
	if len(groups) == 1 {
		randomNumber.ActualNumber = 0.001
	} else if len(groups) > 1 {
		var groupValues []models.GroupValue
		for index, g := range groups {
			if gValueList == nil || len(gValueList) == 0 {
				groupValues = append(groupValues, models.GroupValue{
					Group:   g.Group,
					ParName: g.ParName,
					SubName: g.SubName,
					Value:   0.5,
				})
			} else {
				groupValues = append(groupValues, models.GroupValue{
					Group:   g.Group,
					ParName: g.ParName,
					SubName: g.SubName,
					Value:   gValueList[index],
				})
			}
		}
		randomNumber.ActualNumber = randomNumber.PlanNumber // 实际随机数
		randomNumber.GroupValue = groupValues               // 各组别G值
	}

	// 返回分层和分层因素
	var factors []models.Factors
	for _, factor := range random.Design.Factors {
		for _, option := range factor.Options {
			_, b := slice.Find(subject.Info, func(index int, item models.Info) bool {
				return item.Name == factor.Name && item.Value.(string) == option.Value
			})
			if b {
				factors = append(factors, models.Factors{
					Name:  factor.Name,
					Label: factor.Label,
					Value: option.Value,
					Text:  option.Label,
				})
			}
		}
	}
	randomNumber.Factors = factors

	var randomNumberReturn models.RandomNumberReturn
	randomNumberReturn.ID = randomNumber.ID
	randomNumberReturn.RandomListID = randomNumber.RandomListID
	randomNumberReturn.RandomNumber = randomNumber.Number
	randomNumberReturn.Group = randomNumber.Group
	randomNumberReturn.ParName = randomNumber.ParName
	randomNumberReturn.SubName = randomNumber.SubName
	randomNumberReturn.BlockNumber = randomNumber.Block
	randomNumberReturn.OccupySign = false
	randomNumberReturn.RandomNumberModel = randomNumber
	return randomNumberReturn
}

/**
* 根据组间比例获取最小化随机每个组的应入组人数
* 比例计算公式:如总人数9,分ABC三组,占比1:2:1
* 那么计算公式应该为：9 / (1 + 2 + 1) * 1
* 解释（总人数 / (各组占比总合) * 当前组占比）
 */
func obtainMinimizeNumber(randomList models.RandomList, randomNumberCount int) []int {
	var count []int
	var ratioCount = 0
	for _, group := range randomList.Design.Groups {
		ratioCount += group.Ratio
	}

	for _, group := range randomList.Design.Groups {
		count = append(count, randomNumberCount/ratioCount*group.Ratio)
	}
	return count
}

/**
* 等概率分配组别信息
 */
func distributeGroup(attribute models.Attribute, random models.RandomList, randomNumber models.RandomNumber, subject models.Subject, gValueList []float64, groups []models.Group) models.RandomNumberReturn {
	var randomNumberReturn models.RandomNumberReturn
	minimizeGroupCode := obtainGroupNode(groups, 1.0)
	for _, groupCode := range minimizeGroupCode {
		if randomNumber.PlanNumber <= groupCode.GroupNode {
			return minimizingGetRandomNumber(attribute, models.Group{
				Group:   groupCode.Group,
				ParName: groupCode.ParName,
				SubName: groupCode.SubName,
			}, random, randomNumber, subject, gValueList, groups)
		}
	}
	return randomNumberReturn
}

/**
* 组别分配
 */
func distributionGroups(ctx *gin.Context, sctx mongo.SessionContext, attribute models.Attribute, random models.RandomList, randomNumber models.RandomNumber, subjectList []models.Subject, subject models.Subject, groups []models.Group, groupRatio []int) (models.RandomNumberReturn, error) {

	acceptList, err := calculationGroupLayeredDeviation(sctx, attribute, random, subjectList, subject, groups, groupRatio)
	if err != nil {
		return models.RandomNumberReturn{}, err
	}
	if acceptList == nil || len(acceptList) == 0 {
		return models.RandomNumberReturn{}, tools.BuildServerError(ctx, "minimize_layered_tips")
	}

	var min = acceptList[0] // 最大值
	var max = min           // 最小值

	for _, accept := range acceptList {
		if min > accept {
			min = accept
		}
		if max < accept {
			max = accept
		}
	}

	if max == min {
		return distributeGroup(attribute, random, randomNumber, subject, acceptList, groups), nil
	}
	return obtainBiasGroupNode(ctx, attribute, random, acceptList, min, randomNumber, subject, groups)
}

/**
* 获取组别数字节点
* 比例计算公式：1 /（比例之和）* (当前组别比例和之前组别比例之和)
* 如：AB两组,比例1:2，那么计算公式应为：
* A组：1 / (1+2) * (1)
* B组：1 / (1+2) * (1+2)
 */
func obtainGroupNode(groups []models.Group, number float64) []models.MinimizeGroupCode {
	var minimizeGroupCode []models.MinimizeGroupCode
	for index, group := range groups {
		// 组间 比例全部按1:1等比例分配
		var sum1 float64
		for i := 0; len(groups) > i; i++ {
			sum1++
		}

		var sum2 float64
		for j := 0; len(groups) > j; j++ {
			if j <= index {
				sum2++
			}
		}
		nodeNumber, _ := strconv.ParseFloat(fmt.Sprintf("%.3f", number/sum1*sum2), 64) // 保留三位小数 并转float64
		minimizeGroupCode = append(minimizeGroupCode, models.MinimizeGroupCode{
			Group:     group.Group,
			ParName:   group.ParName,
			SubName:   group.SubName,
			GroupNode: nodeNumber,
		})
	}
	return minimizeGroupCode
}

/**
* 根据各组已入组人数计算组别分层偏差
 */
func calculationGroupLayeredDeviation(sctx mongo.SessionContext, attribute models.Attribute, random models.RandomList, subjectList []models.Subject, subject models.Subject, groups []models.Group, groupRatio []int) ([]float64, error) {
	// 获取组别总比例数的和
	var groupsRatioSum = 0
	for _, ratio := range groupRatio {
		groupsRatioSum += ratio
	}
	// 返回list
	var returnList []float64
	for groupOuterLayerIndex := range groups {
		var layerList []float64
		// 循环分层因素
		for _, factor := range random.Design.Factors {
			// 存放各组已入组人数List
			var groupNumberList []int
			// 记录指定分层因素下各个组别已入组人数的总和
			sum := 0
			// 内层第二次循环组别
			for _, groupInnerLayer := range groups {
				// 获取当前组别和本层分层因因素下已入组人数
				count := 0
				for _, sbj := range subjectList {
					if attribute.AttributeInfo.MinimizeCalc == 1 {
						b := false
						for _, info1 := range sbj.ActualInfo {
							if sbj.Group == groupInnerLayer.Group && factor.Name == info1.Name {
								for _, info2 := range subject.Info {
									if factor.Name == info2.Name && info1.Value == info2.Value {
										b = true
										count++
									}
								}
							}
						}
						if !b {
							for _, info1 := range sbj.Info {
								if sbj.Group == groupInnerLayer.Group && factor.Name == info1.Name {
									for _, info2 := range subject.Info {
										if factor.Name == info2.Name && info1.Value == info2.Value {
											count++
										}
									}
								}
							}
						}
					} else {
						for _, info1 := range sbj.Info {
							if sbj.Group == groupInnerLayer.Group && factor.Name == info1.Name {
								for _, info2 := range subject.Info {
									if factor.Name == info2.Name && info1.Value == info2.Value {
										count++
									}
								}
							}
						}
					}
				}
				// 获取指定分层因素下各个组别已入组人数的总和
				sum += count
				// 记录当前组已入组人数
				groupNumberList = append(groupNumberList, count)
			}

			// 计算当前层权重因子
			var n = 0.0
			for groupNumberIndex, groupNumber := range groupNumberList {
				// 计算第N层预期值
				var expectNumber = float64(sum+1) * float64(groupRatio[groupNumberIndex]) / float64(groupsRatioSum)

				if groupNumberIndex == groupOuterLayerIndex {
					n += math.Pow((float64(groupNumber)+1)-expectNumber, 2.0)
				} else {
					n += math.Pow(float64(groupNumber)-expectNumber, 2.0)
				}
			}

			// 获取当前层权重因子
			factorV := n * 1 / float64(len(groups))
			layerList = append(layerList, factorV)
		}
		// 地区分层因素
		siteMap := make(map[primitive.ObjectID]models.ProjectSite)
		if attribute.AttributeInfo.CountryLayered || attribute.AttributeInfo.RegionLayered {
			siteSubjects := append(subjectList, subject)
			siteIds := slice.Map(siteSubjects, func(index int, item models.Subject) primitive.ObjectID {
				return item.ProjectSiteID
			})
			siteIds = slice.Unique(siteIds)
			sites := make([]models.ProjectSite, 0)
			opts := &options.FindOptions{
				Projection: bson.M{
					"_id":       1,
					"country":   1,
					"region_id": 1,
				},
			}
			cursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": siteIds}}, opts)
			if err != nil {
				return returnList, errors.WithStack(err)
			}
			err = cursor.All(sctx, &sites)
			if err != nil {
				return returnList, errors.WithStack(err)
			}
			for _, site := range sites {
				siteMap[site.ID] = site
			}
		}
		if attribute.AttributeInfo.InstituteLayered || attribute.AttributeInfo.CountryLayered || attribute.AttributeInfo.RegionLayered {
			// 存放各组已入组人数List
			var groupNumberList []int
			// 记录指定分层因素下各个组别已入组人数的总和
			sum := 0
			// 内层第二次循环组别
			for _, groupInnerLayer := range groups {
				// 获取当前组别和本层分层因因素下已入组人数
				count := 0
				for _, sbj := range subjectList {
					if sbj.Group == groupInnerLayer.Group {
						if attribute.AttributeInfo.InstituteLayered && sbj.ProjectSiteID == subject.ProjectSiteID {
							count++
						} else if attribute.AttributeInfo.CountryLayered {
							if siteMap[sbj.ProjectSiteID].Country == nil && siteMap[subject.ProjectSiteID].Country == nil {
								count++
							} else if siteMap[sbj.ProjectSiteID].Country != nil && len(siteMap[sbj.ProjectSiteID].Country) > 0 &&
								siteMap[subject.ProjectSiteID].Country != nil && len(siteMap[subject.ProjectSiteID].Country) > 0 &&
								siteMap[sbj.ProjectSiteID].Country[0] == siteMap[subject.ProjectSiteID].Country[0] {
								count++
							}
						} else if attribute.AttributeInfo.RegionLayered && siteMap[sbj.ProjectSiteID].RegionID == siteMap[subject.ProjectSiteID].RegionID {
							count++
						}
					}
				}
				// 获取指定分层因素下各个组别已入组人数的总和
				sum += count
				// 记录当前组已入组人数
				groupNumberList = append(groupNumberList, count)
			}

			// 计算当前层权重因子
			var n = 0.0
			for groupNumberIndex, groupNumber := range groupNumberList {
				// 计算第N层预期值
				var expectNumber = float64(sum+1) * float64(groupRatio[groupNumberIndex]) / float64(groupsRatioSum)

				if groupNumberIndex == groupOuterLayerIndex {
					n += math.Pow((float64(groupNumber)+1)-expectNumber, 2.0)
				} else {
					n += math.Pow(float64(groupNumber)-expectNumber, 2.0)
				}
			}

			// 获取当前层权重因子
			factorV := n * 1 / float64(len(groups))
			layerList = append(layerList, factorV)
		}

		// 计算结果
		var result = 0.0
		for layerIndex, layer := range layerList {
			if len(random.Design.Factors) > layerIndex {
				result += layer * float64(random.Design.Factors[layerIndex].Ratio)
			} else {
				result += layer * float64(random.Design.Ratio)
			}
		}

		// 返回list(G值四舍五入保留三位小数)
		resultFloat, _ := strconv.ParseFloat(fmt.Sprintf("%.3f", result), 64)
		returnList = append(returnList, resultFloat)
	}
	return returnList, nil
}

/**
 * 根据偏倚概率分配组别
 */
func obtainBiasGroupNode(ctx *gin.Context, attribute models.Attribute, random models.RandomList, acceptList []float64, min float64, randomNumber models.RandomNumber, subject models.Subject, groups []models.Group) (models.RandomNumberReturn, error) {

	var randomNumberReturn models.RandomNumberReturn

	if random.Config.Probability == 0 {
		return models.RandomNumberReturn{}, tools.BuildServerError(ctx, "minimize_bias_probability_tips")
	}

	// 找出最小值将偏倚概率0.9分配给他(多个最小值相同按比例将偏倚概率0.9进行分配)
	// 声明最小值和大于最小值组别以及集合比例集合
	var minGroup []models.Group
	var maxGroup []models.Group

	for index, accept := range acceptList {
		if min == accept {
			minGroup = append(minGroup, groups[index])
		} else {
			maxGroup = append(maxGroup, groups[index])
		}
	}

	minNumberNode := obtainGroupNode(minGroup, random.Config.Probability)
	maxNumberNode := obtainGroupNode(maxGroup, 1-random.Config.Probability)
	// 先找是否符合最小值
	for _, minNode := range minNumberNode {
		if randomNumber.PlanNumber <= minNode.GroupNode {
			return minimizingGetRandomNumber(attribute, models.Group{
				Group:   minNode.Group,
				ParName: minNode.ParName,
				SubName: minNode.SubName,
			}, random, randomNumber, subject, acceptList, groups), nil
		}
	}

	// 后找是否符合最大值
	for _, maxNode := range maxNumberNode {
		nodeFloat, _ := strconv.ParseFloat(fmt.Sprintf("%.3f", random.Config.Probability+maxNode.GroupNode), 64)
		if randomNumber.PlanNumber <= nodeFloat {
			return minimizingGetRandomNumber(attribute, models.Group{
				Group:   maxNode.Group,
				ParName: maxNode.ParName,
				SubName: maxNode.SubName,
			}, random, randomNumber, subject, acceptList, groups), nil
		}
	}

	return randomNumberReturn, nil
}

// 处理list查询的数据

func getJoinTime(attribute models.Attribute, subjectView models.SubjectView) (string, error) {
	joinTime := ""
	if attribute.AttributeInfo.Random {
		if subjectView.RandomTime != 0 {
			if subjectView.Tz == "" {
				utc := tools.FormatOffsetToZoneStringUtc(subjectView.TimeZone)
				//if subjectView.TimeZone > 0 {
				//	utc = fmt.Sprintf("%s%d%s", "(UTC+", subjectView.TimeZone, ")")
				//} else {
				//	utc = fmt.Sprintf("%s%d%s", "(UTC", subjectView.TimeZone, ")")
				//}
				t := tools.FormatFloatTime(fmt.Sprintf("%f", subjectView.TimeZone), "", "", int64(subjectView.RandomTime), "2006-01-02 15:04:05")
				joinTime = t + utc
				//joinTime = time.Unix(int64(subjectView.RandomTime), 0).UTC().Add(time.Hour*time.Duration(subjectView.TimeZone)).UTC().Format("2006-01-02 15:04:05") + utc
			} else {
				utc, err := tools.GetLocationUtc(subjectView.Tz, int64(subjectView.RandomTime))
				if err != nil {
					return "", err
				}
				joinTime = utc
			}
		}
	} else {
		if subjectView.JoinTimeForTime == 0 && subjectView.JoinTime != "" {
			joinTime = subjectView.JoinTime
		} else if subjectView.JoinTimeForTime != 0 {
			if subjectView.Tz == "" {
				utc := tools.FormatOffsetToZoneStringUtc(subjectView.TimeZone)
				//if subjectView.TimeZone > 0 {
				//	utc = fmt.Sprintf("%s%d%s", "(UTC+", zoneString, ")")
				//} else {
				//	utc = fmt.Sprintf("%s%d%s", "(UTC", zoneString, ")")
				//}
				t := tools.FormatFloatTime(fmt.Sprintf("%f", subjectView.TimeZone), "", "", int64(subjectView.RandomTime), "2006-01-02 15:04:05")
				joinTime = t + utc
				//joinTime = time.Unix(int64(subjectView.JoinTimeForTime), 0).UTC().Add(time.Hour*time.Duration(subjectView.TimeZone)).UTC().Format("2006-01-02 15:04:05") + utc
			} else {
				utc, err := tools.GetLocationUtc(subjectView.Tz, int64(subjectView.JoinTimeForTime))
				if err != nil {
					return "", err
				}
				joinTime = utc
			}
		}
	}
	return joinTime, nil
}

// 校验访视
func checkVisitCycle(visitCycleInfo []models.VisitCycleInfo, visitInfoId primitive.ObjectID) bool {
	for _, info := range visitCycleInfo {
		if info.ID == visitInfoId {
			return true
		}
	}
	return false
}

// 状态转换
func statusItem(ctx *gin.Context, status int32) string {
	if status == 1 {
		return locales.Tr(ctx, "subject.status.registered")
	} else if status == 3 {
		return locales.Tr(ctx, "subject.status.random")
	} else if status == 4 {
		return locales.Tr(ctx, "subject.status.sign.out")
	} else if status == 5 {
		return locales.Tr(ctx, "subject.status.sign.out")
	} else if status == 6 {
		return locales.Tr(ctx, "subject.status.unblinding")
	} else if status == 7 {
		return locales.Tr(ctx, "subject.status.screen.success")
	} else if status == 8 {
		return locales.Tr(ctx, "subject.status.screen.fail")
	} else if status == 9 {
		return locales.Tr(ctx, "subject.status.finish")
	} else if status == 10 {
		return locales.Tr(ctx, "subject.status.to.be.random")
	} else if status == 11 {
		return locales.Tr(ctx, "subject.status.join")
	} else {
		return ""
	}
}

func countryReturn(projectSiteID primitive.ObjectID) (string, error) {
	var projectSite models.ProjectSite
	// 条件
	match := bson.M{}

	// 根据当前中心 查询 所属国家
	match = bson.M{"_id": projectSiteID}
	err := tools.Database.Collection("project_site").FindOne(nil, match).Decode(&projectSite)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if len(projectSite.Country) == 0 {
		return "", nil
	}
	return projectSite.Country[0], nil
}

func (s *SubjectService) GetSubjectInfo(ctx *gin.Context, envID string, cohortIDs []interface{}, projectSiteID []interface{}) (interface{}, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{"env_id": envOID} // 包含已删除的数据
	if len(cohortIDs) > 0 {
		var cohortOIDs = primitive.A{}
		for _, d := range cohortIDs {
			cohortOID, _ := primitive.ObjectIDFromHex(d.(string))
			cohortOIDs = append(cohortOIDs, cohortOID)
		}
		match["cohort_id"] = bson.M{"$in": cohortOIDs}
	}
	if len(projectSiteID) > 0 {
		var projectSiteOIDs = primitive.A{}
		for _, d := range projectSiteID {
			cohortOID, _ := primitive.ObjectIDFromHex(d.(string))
			projectSiteOIDs = append(projectSiteOIDs, cohortOID)
		}
		match["project_site_id"] = bson.M{"$in": projectSiteOIDs}
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$sort", bson.D{{"_id", 1}}}},
		{{"$project", bson.M{
			"id":      "$_id",
			"_id":     0,
			"number":  bson.M{"$first": "$info.value"},
			"deleted": 1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	return data, nil
}

func (s *SubjectService) SubjectReplaceRandomNumber(ctx *gin.Context) (string, error) {
	id := ctx.Query("id")
	attributeId := ctx.Query("attributeId")
	ID, _ := primitive.ObjectIDFromHex(id)
	randomNumber := ""
	subjectFilter := bson.M{"_id": ID}
	var beReplaceSubject models.Subject
	_ = tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&beReplaceSubject)
	if beReplaceSubject.Status != 3 {
		return "", tools.BuildServerError(ctx, "subject_status_no_replace")
	}
	attribute, err := database.GetAttribute(nil, attributeId)
	if err != nil {
		return "", err
	}
	if attribute.AttributeInfo.ReplaceRule == 1 {
		randomNumber, err = sysRandomNumber(beReplaceSubject, attribute)
		if err != nil {
			return randomNumber, err
		}
	}
	return randomNumber, err
}

func updateDispensing(ctx *gin.Context, sctx mongo.SessionContext, subject models.Subject, group string) error {
	var visitCycle models.VisitCycle
	var visitIDs []primitive.ObjectID
	tools.Database.Collection("visit_cycle").FindOne(sctx, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&visitCycle)
	for _, configure := range visitCycle.Infos {
		_, ok := slice.Find(configure.Group, func(index int, item interface{}) bool {
			return item == group
		})
		if ok {
			visitIDs = append(visitIDs, configure.ID)
		}
	}
	visitIDs = slice.Unique(visitIDs)
	var removeDispensingID []primitive.ObjectID
	var dispensings []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"subject_id": subject.ID})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return errors.WithStack(err)
	}
	for _, dispensing := range dispensings {
		_, ok := slice.Find(visitIDs, func(index int, item primitive.ObjectID) bool {
			return item == dispensing.VisitInfo.VisitCycleInfoID
		})
		if !ok {
			removeDispensingID = append(removeDispensingID, dispensing.ID)
		}
	}

	if len(removeDispensingID) > 0 {
		_, err = tools.Database.Collection("dispensing").DeleteMany(sctx, bson.M{"_id": bson.M{"$in": removeDispensingID},
			"status":            1,
			"subject_id":        subject.ID,
			"visit_info.random": false, //随机不发药访视不过滤
		})
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func replaceMedicine(ctx *gin.Context, sctx mongo.SessionContext, beReplaceSubjectOID, replaceSubjectOID primitive.ObjectID) error {
	_, err := tools.Database.Collection("medicine").UpdateMany(sctx,
		bson.M{"status": 14, "subject_id": beReplaceSubjectOID},
		bson.M{"$set": bson.M{
			"subject_id": replaceSubjectOID,
		}},
	)
	if err != nil {
		return errors.WithStack(err)
	}
	return err
}

// 查询有揭盲权限的用户
func findApprovalUser(
	userProjectEnvironmentList []models.UserProjectEnvironment,
	userList []models.User,
	projectRolePermissionList []models.ProjectRolePermission,
	userSiteList []models.UserSite,
	siteOID primitive.ObjectID,
) ([]models.ApprovalUser, []models.ApprovalUser, []models.ApprovalUser) {

	var unblindingApprovalUser []models.ApprovalUser
	var pvUnblindingApprovalUser []models.ApprovalUser
	var ipUnblindingApprovalUser []models.ApprovalUser

	for _, upel := range userProjectEnvironmentList {
		if !upel.Unbind { // 排除无效用户
			for _, rid := range upel.Roles {
				for _, prpl := range projectRolePermissionList {
					var user models.User
					if rid == prpl.ID {
						if prpl.Scope == "study" { // 全权限
							// 筛选用户
							for _, u := range userList {
								if u.ID == upel.UserID {
									user = u
									break
								}
							}

							// 紧急揭盲
							if findApprovalUserOperationPermissions(prpl.Permissions, 1) {
								// 数据去重
								bl := true
								for _, uau := range unblindingApprovalUser {
									if uau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									unblindingApprovalUser = append(unblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId})
								}
							}

							// pv揭盲
							if findApprovalUserOperationPermissions(prpl.Permissions, 2) {
								// 数据去重
								bl := true
								for _, puau := range pvUnblindingApprovalUser {
									if puau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									pvUnblindingApprovalUser = append(pvUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId})
								}
							}

							// ip揭盲
							if findApprovalUserOperationPermissions(prpl.Permissions, 3) {
								// 数据去重
								bl := true
								for _, puau := range ipUnblindingApprovalUser {
									if puau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									ipUnblindingApprovalUser = append(ipUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId})
								}
							}
						} else if prpl.Scope == "site" {
							// 判断数据权限
							_, ok := slice.Find(userSiteList, func(index int, item models.UserSite) bool {
								return item.SiteID == siteOID && item.UserID == upel.UserID
							})
							if ok {
								// 筛选用户
								for _, u := range userList {
									if u.ID == upel.UserID {
										user = u
										break
									}
								}

								// 紧急揭盲
								if findApprovalUserOperationPermissions(prpl.Permissions, 1) {
									// 数据去重
									bl := true
									for _, uau := range unblindingApprovalUser {
										if uau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										unblindingApprovalUser = append(unblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId})
									}
								}

								// pv揭盲
								if findApprovalUserOperationPermissions(prpl.Permissions, 2) {
									// 数据去重
									bl := true
									for _, puau := range pvUnblindingApprovalUser {
										if puau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										pvUnblindingApprovalUser = append(pvUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId})
									}
								}

								// ip揭盲
								if findApprovalUserOperationPermissions(prpl.Permissions, 2) {
									// 数据去重
									bl := true
									for _, puau := range ipUnblindingApprovalUser {
										if puau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										ipUnblindingApprovalUser = append(ipUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId})
									}
								}
							}
						}
						break
					}
				}
			}
		}

	}
	return unblindingApprovalUser, pvUnblindingApprovalUser, ipUnblindingApprovalUser
}

// 查询有揭盲权限的用户
func queryApprovalUser(
	userProjectEnvironmentList []models.UserProjectEnvironment,
	userList []models.User,
	projectRolePermissionList []models.ProjectRolePermission,
	userSiteList []models.UserSite,
	siteOID primitive.ObjectID,
) ([]models.ApprovalUser, []models.ApprovalUser, []models.ApprovalUser) {

	var unblindingApprovalUser []models.ApprovalUser
	var pvUnblindingApprovalUser []models.ApprovalUser
	var ipUnblindingApprovalUser []models.ApprovalUser

	for _, upel := range userProjectEnvironmentList {
		if !upel.Unbind { // 排除无效用户
			for _, rid := range upel.Roles {
				for _, prpl := range projectRolePermissionList {
					var user models.User
					if rid == prpl.ID {
						if prpl.Scope == "study" { // 全权限
							// 筛选用户
							for _, u := range userList {
								if u.ID == upel.UserID {
									user = u
									break
								}
							}

							// 紧急揭盲
							if queryApprovalUserOperationPermissions(prpl.Permissions, 1) {
								// 数据去重
								bl := true
								for _, uau := range unblindingApprovalUser {
									if uau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									unblindingApprovalUser = append(unblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
								}
							}

							// pv揭盲
							if queryApprovalUserOperationPermissions(prpl.Permissions, 2) {
								// 数据去重
								bl := true
								for _, puau := range pvUnblindingApprovalUser {
									if puau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									pvUnblindingApprovalUser = append(pvUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
								}
							}
							// ip揭盲
							if queryApprovalUserOperationPermissions(prpl.Permissions, 3) {
								// 数据去重
								bl := true
								for _, puau := range ipUnblindingApprovalUser {
									if puau.UserId == user.ID {
										bl = false
										break
									}
								}
								if bl {
									ipUnblindingApprovalUser = append(ipUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
								}
							}
						} else if prpl.Scope == "site" {
							// 判断数据权限
							_, ok := slice.Find(userSiteList, func(index int, item models.UserSite) bool {
								return item.SiteID == siteOID && item.UserID == upel.UserID
							})
							if ok {
								// 筛选用户
								for _, u := range userList {
									if u.ID == upel.UserID {
										user = u
										break
									}
								}

								// 紧急揭盲
								if queryApprovalUserOperationPermissions(prpl.Permissions, 1) {
									// 数据去重
									bl := true
									for _, uau := range unblindingApprovalUser {
										if uau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										unblindingApprovalUser = append(unblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
									}
								}

								// pv揭盲
								if queryApprovalUserOperationPermissions(prpl.Permissions, 2) {
									// 数据去重
									bl := true
									for _, puau := range pvUnblindingApprovalUser {
										if puau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										pvUnblindingApprovalUser = append(pvUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
									}
								}
								// ip揭盲
								if queryApprovalUserOperationPermissions(prpl.Permissions, 3) {
									// 数据去重
									bl := true
									for _, puau := range ipUnblindingApprovalUser {
										if puau.UserId == user.ID {
											bl = false
											break
										}
									}
									if bl {
										ipUnblindingApprovalUser = append(ipUnblindingApprovalUser, models.ApprovalUser{UserId: user.ID, ApprovalName: user.Name, ApprovalPhone: user.Phone, CloudId: user.CloudId, Email: user.Email})
									}
								}
							}
						}
						break
					}
				}
			}
		}

	}
	return unblindingApprovalUser, pvUnblindingApprovalUser, ipUnblindingApprovalUser
}

// 操作权限筛选（sign 1/筛选紧急揭盲 2/筛选pv揭盲）
func findApprovalUserOperationPermissions(permissions []string, sign int) bool {
	bl := true
	if permissions == nil || len(permissions) == 0 {
		bl = false
	} else {
		if sign == 1 {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding" || item == "operation.subject.unblinding-approval"
			})
			if len(p) < 2 {
				bl = false
			}
		} else if sign == 2 {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding-pv-view" || item == "operation.subject.unblinding-pv-approval"
			})
			if len(p) < 2 {
				bl = false
			}
		} else if sign == 3 {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding-ip-view" || item == "operation.subject.unblinding-ip-approval"
			})
			if len(p) < 2 {
				bl = false
			}
		}
	}
	return bl
}

func queryApprovalUserOperationPermissions(permissions []string, sign int) bool {
	bl := true
	if permissions == nil || len(permissions) == 0 {
		bl = false
	} else {
		if sign == 1 {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding-approval" || item == "operation.subject-dtp.unblinding-approval"
			})
			if len(p) == 0 {
				bl = false
			}
		} else if sign == 2 {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding-pv-approval"
			})
			if len(p) == 0 {
				bl = false
			}
		} else if sign == 3 {
			p := slice.Filter(permissions, func(index int, item string) bool {
				return item == "operation.subject.unblinding-ip-approval"
			})
			if len(p) == 0 {
				bl = false
			}
		}
	}
	return bl
}

func (s *SubjectService) RealityCapacity(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string) (int, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	realityCapacity := 0
	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	attribute := models.Attribute{}
	err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return 0, errors.WithStack(err)
	}
	//仅发药项目
	if !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing {
		filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
		count, err := tools.CountSubject(nil, attribute, filter)
		if err != nil {
			return realityCapacity, errors.WithStack(err)
		}
		realityCapacity = count
	} else {
		subjectAll := make([]models.Subject, 0)
		subjectFilter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID, "status": bson.M{"$in": [3]int{3, 4, 6}}}
		subjectAllCursor, err := tools.Database.Collection("subject").Find(nil, subjectFilter)
		if err != nil {
			return realityCapacity, errors.WithStack(err)
		}
		if err = subjectAllCursor.All(nil, &subjectAll); err != nil {
			return realityCapacity, errors.WithStack(err)
		}
		realityCapacity = len(subjectAll)
	}
	return realityCapacity, nil
}

func (s *SubjectService) UpdateSubjectJoinTime(ctx *gin.Context, ID string, joinTime string, roleId string) error {
	var subject models.Subject

	OID, _ := primitive.ObjectIDFromHex(ID)
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": OID}).Decode(&subject)
	if err != nil {
		return errors.WithStack(err)
	}

	attribute, err := database.GetAttributeWithEnvCohortID(nil, subject.EnvironmentID, subject.CohortID)

	_, err = tools.Database.Collection("subject").UpdateOne(nil, bson.M{"_id": OID},
		bson.M{
			"$set": bson.M{
				"join_time": joinTime,
			}},
	)
	if err != nil {
		return errors.WithStack(err)
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	// TODO 在随机
	key := "history.subject.joinTime"
	stage := ""
	if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		key = "history.subject.at-random-joinTime"
		if subject.CohortID != primitive.NilObjectID {
			for _, env := range project.Environments {
				if env.ID == subject.EnvironmentID {
					for _, cohort := range env.Cohorts {
						if cohort.ID == subject.CohortID {
							stage = cohort.Name
							break
						}
					}
					break
				}
			}
		}
	}

	me, err := tools.Me(ctx)
	var histories []models.History
	history := models.History{
		Key:  key,
		OID:  OID,
		Data: map[string]interface{}{"label": attribute.AttributeInfo.SubjectReplaceText, "subjectNo": subject.Info[0].Value, "joinTime": joinTime, "stage": stage},
		Time: time.Duration(time.Now().Unix()),
		UID:  me.ID,
		User: me.Name,
	}
	histories = append(histories, history)
	ctx.Set("HISTORY", histories)

	err = PatchAppDispenseTask(ctx, ID, roleId)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// 在随机查询表单
func (s *SubjectService) AtRandomForm(ctx *gin.Context, subjectID string) (interface{}, error) {
	// 返回数据
	var returnData []map[string]interface{}

	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//筛选cohort
	var cohort []models.Cohort
	for _, env := range project.Environments {
		if env.ID == subject.EnvironmentID {
			cohort = env.Cohorts
		}
	}

	// 查询分层因素
	for _, ch := range cohort {
		var fields []models.Field
		// 查询受试者
		var atSubject models.Subject
		subjectFilter := bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"cohort_id":   ch.ID,
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": subject.Info[0].Value,
				},
			},
			"deleted": bson.M{"$ne": true},
		}
		err = tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&atSubject)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if atSubject.Info != nil && len(atSubject.Info) > 0 {
			var randomList models.RandomList
			randomFilter := bson.M{
				"cohort_id": ch.ID,
				"status":    1,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": atSubject.ProjectSiteID},
				}}
			err = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, factor := range randomList.Design.Factors {
				if factor.Status == nil || *factor.Status == 1 {
					fields = append(fields, models.Field{
						ID:      factor.ID,
						Name:    factor.Name,
						Label:   factor.Label,
						Type:    factor.Type,
						Options: factor.Options,
					})
				}
			}
			// 查询表单
			var form models.Form
			_ = tools.Database.Collection("form").FindOne(nil, bson.M{"cohort_id": ch.ID}).Decode(&form)
			for _, field := range form.Fields {
				if field.Status == nil || *field.Status == 1 {
					fields = append(fields, models.Field{
						ID:      field.ID,
						Name:    field.Name,
						Label:   field.Label,
						Type:    field.Type,
						Options: field.Options,
					})
				}
			}

			// 查询cohort属性配置
			var attribute models.Attribute
			err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"cohort_id": ch.ID}).Decode(&attribute)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

			replaceNumber := ""
			if attribute.AttributeInfo.ReplaceRule == 1 {
				// 展示随机号
				replaceNumber, err = sysRandomNumber(atSubject, attribute)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			if attribute.AttributeInfo.IsRandomNumber {
				returnData = append(returnData, map[string]interface{}{"fields": fields, "cohort": ch, "randomNumber": atSubject.RandomNumber, "replaceNumber": replaceNumber})
			} else {
				returnData = append(returnData, map[string]interface{}{"fields": fields, "cohort": ch, "randomNumber": tools.BlindData, "replaceNumber": tools.BlindData})
			}
		}
	}
	return returnData, nil
}

func (s *SubjectService) UpdateActualFactor(ctx *gin.Context) error {
	req := struct {
		ID         primitive.ObjectID `json:"id"`
		ActualInfo []models.Info      `json:"actualInfo" bson:"actual_info"`
		JoinTime   time.Duration      `json:"joinTime" bson:"join_time"`
		RoleId     primitive.ObjectID `json:"roleId"`
	}{}
	_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": req.ID}).Decode(&subject)
	if err != nil {
		return errors.WithStack(err)
	}
	var site models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&site)
	if err != nil {
		return errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	nowTime := time.Duration(time.Now().Unix())
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		attribute, err := database.GetAttributeWithEnvCohortID(nil, subject.EnvironmentID, subject.CohortID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		filter := bson.M{"env_id": subject.EnvironmentID}
		if !subject.CohortID.IsZero() {
			filter["cohort_id"] = subject.CohortID
		}
		var actual = subject.ActualInfo
		if actual == nil {
			actual = make([]models.Info, 0)
		}
		historyInfo := make([]models.Info, 0) // 记录轨迹
		if attribute.AttributeInfo.Random {
			var form models.Form
			err = tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			calcFormFields := slice.Filter(form.Fields, func(index int, item models.Field) bool {
				return item.ApplicationType != nil && (*item.ApplicationType == 1 || *item.ApplicationType == 4)
			})
			var randomList models.RandomList
			randomListId := subject.RandomListID
			if subject.RandomListID.IsZero() {
				randomListId = subject.RegisterRandomListID
			}
			err = tools.Database.Collection("random_list").FindOne(sctx, bson.M{"_id": randomListId}).Decode(&randomList)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			randomList.Design.Factors = slice.Filter(randomList.Design.Factors, func(index int, item models.RandomFactor) bool {
				if item.Status == nil {
					return true
				}
				return *item.Status != 2
			})
			//先构建字段key
			for _, factor := range randomList.Design.Factors {
				if factor.Status == nil || *factor.Status == 1 {
					exist := false
					for _, info := range actual {
						if info.Name == factor.Name {
							exist = true
							break
						}
					}
					if !exist {
						actual = append(actual, models.Info{
							Name:  factor.Name,
							Value: "",
						})
					}
				}
			}
			for _, factor := range calcFormFields {
				if factor.Status == nil || *factor.Status == 1 {
					exist := false
					for _, info := range actual {
						if info.Name == factor.Name {
							exist = true
							break
						}
					}
					if !exist {
						actual = append(actual, models.Info{
							Name:  factor.Name,
							Value: "",
						})
					}
				}
			}

			for _, ft := range randomList.Design.Factors {
				if ft.Status == nil || *ft.Status == 1 {
					if ft.IsCalc {
						re := regexp.MustCompile(`\{([\x{4e00}-\x{9fa5}\w]+)\}`)
						customFormulas := ft.CustomFormulas
						matches := re.FindAllStringSubmatch(customFormulas, -1)
						fieldNames := slice.Map(matches, func(index int, item []string) string {
							return item[1]
						})
						customerFields := make([]models.CustomerFormula, 0)
						for _, fieldName := range fieldNames {
							if fieldName == "CurrentTime" {
								customerFields = append(customerFields, models.CustomerFormula{
									Key:   "{" + fieldName + "}",
									Value: float64(time.Now().Unix()) / (24 * 60 * 60),
								})
							} else {
								find, b := slice.Find(calcFormFields, func(index int, item models.Field) bool {
									return item.Variable == fieldName
								})
								reqInfoP, b1 := slice.Find(req.ActualInfo, func(index int, item models.Info) bool {
									return item.Name == find.Name
								})
								aInfoP, b2 := slice.Find(actual, func(index int, item models.Info) bool {
									return item.Name == find.Name
								})
								if b2 {
									aInfoP.Value = reqInfoP.Value
								}
								historyInfo = append(historyInfo, models.Info{
									Name:  find.Label,
									Value: reqInfoP.Value,
								})

								if !b || !b1 {
									return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_form")
								}
								_, ok := reqInfoP.Value.(string)
								if (!ok && reqInfoP.Value != nil) || (ok && reqInfoP.Value.(string) != "") {
									if find.Type == "timePicker" || find.Type == "datePicker" {
										format := "YYYY-MM-DD"
										if find.Type == "timePicker" {
											format = "YYYY-MM-DD HH:mm:ss"
										}
										parse, err := time.Parse(tools.DateFormatParse(format), reqInfoP.Value.(string))
										if err != nil {
											return nil, errors.WithStack(err)
										}
										customerFields = append(customerFields, models.CustomerFormula{
											Key:   "{" + fieldName + "}",
											Value: float64(parse.Unix()) / (24 * 60 * 60),
										})
									} else if find.Type == "inputNumber" {
										customerFields = append(customerFields, models.CustomerFormula{
											Key:   "{" + fieldName + "}",
											Value: reqInfoP.Value.(float64),
										})
									}
								}
							}
						}
						isSuccess, res := tools.CustomMultipleFormulaExecNoToLower(customFormulas, customerFields)
						if isSuccess {
							infoP, b3 := slice.Find(actual, func(index int, item models.Info) bool {
								return item.Name == ft.Name
							})
							bmi := tools.TruncateDecimal(res, *ft.Precision, ft.Round)
							bmiString := strconv.FormatFloat(bmi, 'f', *ft.Precision, 64)
							matchOption := false
							for _, option := range ft.Options {
								b, err := tools.MatchRangeFormula(bmiString, *option.Formula)
								if err != nil {
									return nil, err
								}
								if b && b3 {
									infoP.Value = option.Value
									historyInfo = append(historyInfo, models.Info{
										Name:  ft.Label,
										Value: option.Label,
									})
									matchOption = true
									break
								}
							}
							if !matchOption {
								return nil, tools.BuildServerError(ctx, "factor_calc_not_match")
							}
						} else {
							aInfoP, b2 := slice.Find(actual, func(index int, item models.Info) bool {
								return item.Name == ft.Name
							})
							if b2 {
								aInfoP.Value = nil
							}
						}
					} else {
						reqInfoP, b := slice.Find(req.ActualInfo, func(index int, item models.Info) bool {
							return item.Name == ft.Name && (item.Value != nil || item.Value != "")
						})
						if b {
							infoP, b2 := slice.Find(actual, func(index int, item models.Info) bool {
								return item.Name == ft.Name
							})
							if b2 {
								infoP.Value = reqInfoP.Value
								if ft.Type == "select" || ft.Type == "radio" {
									for _, option := range ft.Options {
										if option.Value == reqInfoP.Value {
											historyInfo = append(historyInfo, models.Info{
												Name:  ft.Label,
												Value: option.Label,
											})
											break
										}
									}
								} else if ft.Type == "checkbox" {
									values := reqInfoP.Value.([]interface{})
									for _, value := range values {
										for _, option := range ft.Options {
											if option.Value == value.(string) {
												historyInfo = append(historyInfo, models.Info{
													Name:  ft.Label,
													Value: option.Label,
												})
												break
											}
										}
									}

								} else if ft.Type == "switch" {
									label := locales.Tr(ctx, "common.no")
									if reqInfoP.Value == true {
										label = locales.Tr(ctx, "common.yes")
									}
									historyInfo = append(historyInfo, models.Info{
										Name:  ft.Label,
										Value: label,
									})
								} else if ft.Type == "inputNumber" {
									label := convertor.ToString(reqInfoP.Value)
									historyInfo = append(historyInfo, models.Info{
										Name:  ft.Label,
										Value: label,
									})
								} else if ft.Type == "datePicker" {
									dateFormat := "YYYY-MM-DD"
									if ft.DateFormat != nil && *ft.DateFormat != "" {
										dateFormat = *ft.DateFormat
									}
									label := ""
									if reqInfoP.Value != nil {
										label = fmt.Sprint(reqInfoP.Value)
										parse, err := time.Parse("2006-01-02", label)
										if err != nil {
											return false, errors.WithStack(err)
										}
										label = parse.Format(tools.DateFormatParse(dateFormat))
									}
									historyInfo = append(historyInfo, models.Info{
										Name:  ft.Label,
										Value: label,
									})
								} else if ft.Type == "timePicker" {
									dateFormat := "YYYY-MM-DD HH:mm:ss"
									if ft.DateFormat != nil && *ft.DateFormat != "" {
										dateFormat = *ft.DateFormat
									}
									label := ""
									if reqInfoP.Value != nil {
										label = fmt.Sprint(reqInfoP.Value)
										parse, err := time.Parse("2006-01-02 15:04:05", label)
										if err != nil {
											return false, errors.WithStack(err)
										}
										label = parse.Format(tools.DateFormatParse(dateFormat))
									}
									historyInfo = append(historyInfo, models.Info{
										Name:  ft.Label,
										Value: label,
									})
								} else {
									historyInfo = append(historyInfo, models.Info{
										Name:  ft.Label,
										Value: reqInfoP.Value,
									})
								}
							}
						}
					}
				}
			}
		}

		update := bson.M{"$set": bson.M{"actual_info": actual}}
		if req.JoinTime != 0 {
			update = bson.M{"$set": bson.M{"actual_info": actual, "join_time_for_time": req.JoinTime}}
		}
		_, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": req.ID}, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		key := "history.subject.label.update2Customize"
		stage := ""
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			if subject.CohortID != primitive.NilObjectID {
				for _, env := range project.Environments {
					if env.ID == subject.EnvironmentID {
						for _, cohort := range env.Cohorts {
							if cohort.ID == subject.CohortID {
								stage = cohort.Name
								break
							}
						}
						break
					}
				}
			}
		}

		me, err := tools.Me(ctx)
		var histories []models.History
		tempOptions := make([]models.CustomTempOption, 0)
		index := 0
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			tempOptions = append(tempOptions, models.CustomTempOption{
				Index: index,
				Key:   "history.subject.label.updateCustomizeStage",
				Data: map[string]interface{}{
					"stage": stage,
				},
			})
			index++
		}
		replacement := GetSubjectReplaceText(ctx, attribute)
		tempOptions = append(tempOptions, models.CustomTempOption{
			Index: index,
			Key:   "history.subject.label.common-key-value1",
			Data: map[string]interface{}{
				"name":  replacement,
				"value": subject.Info[0].Value,
			},
		})
		index++
		if req.JoinTime != 0 {
			joinTimeStr := ""
			if site.Tz == "" {
				utc := ""
				timezone, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
				//timezone := project.ProjectInfo.TimeZone
				if site.TimeZone != "" {
					strTimeZone, err := tools.GetSiteTimeZoneInfo(site)
					siteTimeZone, err := tools.ParseTimezoneOffset(strTimeZone)
					//timezoneStr := strings.ReplaceAll(site.TimeZone, "UTC", "")
					//siteTimeZone, err := strconv.ParseFloat(timezoneStr, 64)
					if err != nil {
						siteTimeZone = float64(8)
					}
					timezone = siteTimeZone
				}
				zoneString := tools.FormatOffsetToZoneString(timezone)
				if timezone > 0 {
					utc = fmt.Sprintf("%s%d%s", "(UTC+", zoneString, ")")
				} else {
					utc = fmt.Sprintf("%s%d%s", "(UTC", zoneString, ")")
				}
				joinTimeStr = time.Unix(int64(req.JoinTime), 0).UTC().Add(time.Hour*time.Duration(timezone)).UTC().Format("2006-01-02 15:04:05") + utc
			} else {
				utc, err := tools.GetLocationUtc(site.Tz, int64(req.JoinTime))
				if err != nil {
					return "", err
				}
				joinTimeStr = utc
			}
			tempOptions = append(tempOptions, models.CustomTempOption{
				Index: index,
				Key:   "history.subject.label.updateCustomizeJoinTime",
				Data: map[string]interface{}{
					"joinTime": joinTimeStr,
				},
			})
			index++
		}

		if len(historyInfo) > 0 {
			for _, info := range historyInfo {
				tempOptions = append(tempOptions, models.CustomTempOption{
					Index: index,
					Key:   "history.subject.label.common-key-value",
					Data: map[string]interface{}{
						"name":  info.Name,
						"value": info.Value,
					},
				})
				index++
			}
		}

		temp := []models.CustomTemp{
			{
				ParKey:              "updateFields",
				ConnectingSymbolKey: "history.subject.label.updateCustomizeConnectingSymbol",
				LastSymbolKey:       "history.subject.label.updateCustomizeLastSymbolKey",
				CustomTempOptions:   tempOptions,
			},
		}

		history := models.History{
			Key:         key,
			OID:         req.ID,
			CustomTemps: temp,
			Data:        map[string]interface{}{},
			Time:        nowTime,
			UID:         me.ID,
			User:        me.Name,
		}
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)
		if req.JoinTime != 0 {
			err = PatchAppDispenseTask(ctx, req.ID.Hex(), req.RoleId.Hex())
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ProjectInfo.ConnectEdc, project.ProjectInfo.PushMode, project.ProjectInfo.EdcSupplier, project.ProjectInfo.PushScenario.UpdateRandomAfterPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectRandomPush(logData, subject.ID, 12, nowTime, "", "", "")
	}
	return nil
}

func fileSiteIds(siteIds []primitive.ObjectID, projectSiteId primitive.ObjectID) bool {
	if siteIds == nil || len(siteIds) == 0 {
		return true
	} else {
		for _, sid := range siteIds {
			if sid == projectSiteId {
				return true
			}
		}
	}
	return false
}

func getLangLabel(ctx context.Context, label, labelEn string) string {
	// 如果 lang 是 "zh"，优先使用 label，否则使用默认值
	// 如果 lang 是 "en"，优先使用 labelEn，否则使用 label
	// 如果两个都为空，则使用默认值
	if locales.Lang(ctx) == "zh" {
		if label != "" {
			return label
		} else {
			return "受试者号" // 默认值
		}

	}
	if locales.Lang(ctx) == "en" {
		if labelEn != "" {
			return labelEn
		}
		if label != "" {
			return label
		}
		return "Subject ID"
	}
	return "受试者号"
}

// EDC分层对比
func layerrdContrast(
	ctx context.Context,
	parameterData map[string]interface{},
	randomList models.RandomList,
	randomForm models.Form,
	httpObtainEdcStatus models.HTTPObtainEdcStatus,
	parameterSubjectReturnResults models.SubjectReturnResults,
	project models.Project,
	cohort models.Cohort,
	screenFlag bool,
	attribute models.Attribute) (models.SubjectReturnResults, error) {

	var subjectReturnResultsData []models.SubjectReturnResultsData
	resultSubjectReturnResults := parameterSubjectReturnResults

	// 受试者号校验
	resultSubjectReturnResults.EdcOpenSubjectNo = true
	resultSubjectReturnResults.SubjectNoIsAlike = true
	if httpObtainEdcStatus.Data.SubjectNo != resultSubjectReturnResults.IrtSubjectNo {
		resultSubjectReturnResults.SubjectNoIsAlike = false
	}
	// 根据语言选择label
	langLabel := getLangLabel(ctx, attribute.AttributeInfo.SubjectReplaceText, attribute.AttributeInfo.SubjectReplaceTextEn)

	subjectReturnResultsData = append(subjectReturnResultsData, models.SubjectReturnResultsData{
		Sign:     resultSubjectReturnResults.SubjectNoIsAlike,
		EdcValue: httpObtainEdcStatus.Data.SubjectNo,
		IrtValue: resultSubjectReturnResults.IrtSubjectNo,
		Label:    langLabel,
	})

	// cohort校验
	if project.ProjectInfo.Type != 1 {
		cohortLabel := "Cohort"
		if project.ProjectInfo.Type == 3 {
			cohortLabel = "Stage"
		}
		resultSubjectReturnResults.CohortIsAlike = true
		for _, data := range httpObtainEdcStatus.Data.SubjectData {
			if data.IrtKey == "cohortName" {
				resultSubjectReturnResults.EdcOpenCohort = true
				if data.EdcValue != cohort.Name {
					resultSubjectReturnResults.CohortIsAlike = false
					resultSubjectReturnResults.EdcCohortName = data.EdcValue
					subjectReturnResultsData = append(subjectReturnResultsData, models.SubjectReturnResultsData{
						Sign:     false,
						EdcValue: data.EdcValue,
						IrtValue: cohort.Name,
						Label:    cohortLabel,
					})
				} else {
					subjectReturnResultsData = append(subjectReturnResultsData, models.SubjectReturnResultsData{
						Sign:     true,
						EdcValue: data.EdcValue,
						IrtValue: cohort.Name,
						Label:    cohortLabel,
					})
				}
				break
			}
		}
	}

	if !screenFlag {
		// 分层校验
		resultSubjectReturnResults.StratificationIsAlike = true
		if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
			stratificationData := []models.SubjectReturnResultsData{}
			irtSign := true
			for _, ft := range randomList.Design.Factors {
				if ft.Status == nil || *ft.Status == 1 {
					var irtValue = ""               // irt的分层因素值
					for _, op := range ft.Options { // 拿到随机系统里面分层因素的label
						if parameterData[ft.Name] != nil && parameterData[ft.Name].(string) == op.Value {
							irtValue = op.Label
							break
						}
					}
					if irtValue != "" {
						irtSign = false
					}

					// 筛选edc的分层因素值
					var edcValue = ""
					openStratificationSign := false
					for _, data := range httpObtainEdcStatus.Data.SubjectData {
						if ft.Name == data.IrtKey {
							edcValue = data.EdcValue
							openStratificationSign = true
							resultSubjectReturnResults.EdcOpenStratification = true
							break
						}
					}
					if openStratificationSign {
						if edcValue != irtValue {
							resultSubjectReturnResults.StratificationIsAlike = false
							stratificationData = append(stratificationData, models.SubjectReturnResultsData{
								Sign:     false,
								EdcValue: edcValue,
								IrtValue: irtValue,
								Label:    ft.Label,
							})
						} else {
							stratificationData = append(stratificationData, models.SubjectReturnResultsData{
								Sign:     true,
								EdcValue: edcValue,
								IrtValue: irtValue,
								Label:    ft.Label,
							})
						}
					}
				}
			}
			// 如果IRT的分层因素全是空。说明是随机前发药项目，此时分层因素没有填写，无需对分层因素进行比较
			if irtSign {
				resultSubjectReturnResults.StratificationIsAlike = true
				stratificationData = []models.SubjectReturnResultsData{}
			}
			subjectReturnResultsData = append(subjectReturnResultsData, stratificationData...)
		}

		// 表单校验
		resultSubjectReturnResults.FormIsAlike = true
		if randomForm.Fields != nil && len(randomForm.Fields) > 0 {
			for _, ff := range randomForm.Fields {
				if (ff.Status == nil || *ff.Status == 1) && (ff.ApplicationType == nil || *ff.ApplicationType == 1 || *ff.ApplicationType == 4) { // 有效或有值
					// 判断具体那个分层EDC没配置
					var irtValue = "" // irt的分层因素值
					if ff.Type == "radio" || ff.Type == "select" {
						if parameterData[ff.Name] != nil {
							for _, op := range ff.Options { // 拿到随机系统里面分层因素的label
								if parameterData[ff.Name] != nil && parameterData[ff.Name].(string) == op.Value {
									irtValue = op.Label
									break
								}
							}
						}
					} else if ff.Type == "checkbox" {
						if parameterData[ff.Name] != nil {
							var checkboxBf bytes.Buffer
							str := parameterData[ff.Name].([]interface{})
							for _, option := range ff.Options {
								for j := 0; j < len(str); j++ {
									if option.Value == str[j].(string) {
										checkboxBf.WriteString(option.Label)
										checkboxBf.WriteString(",")
									}
								}
							}
							irtValue = checkboxBf.String()
						}
					} else if ff.Type == "switch" {
						if parameterData[ff.Name] != nil {
							if parameterData[ff.Name] == true {
								irtValue = "yes"
							} else {
								irtValue = "no"
							}
						}
					} else if ff.Type == "inputNumber" {
						if parameterData[ff.Name] != nil {
							if ff.Type == "inputNumber" && ff.FormatType == "decimalLength" && ff.Length != nil {
								lengthString := strconv.FormatFloat(*ff.Length, 'f', -1, 64)
								if strings.Contains(lengthString, ".") {
									digits, _ := getFractionDigits(*ff.Length)
									str := strconv.FormatFloat(parameterData[ff.Name].(float64), 'f', digits, 64)
									irtValue = str
								} else {
									irtValue = convertor.ToString(parameterData[ff.Name])
								}
							} else {
								irtValue = convertor.ToString(parameterData[ff.Name])
							}
						}
					} else if ff.Type == "datePicker" { // 日期选择器
						dateFormat := "YYYY-MM-DD"
						if ff.DateFormat != "" {
							dateFormat = ff.DateFormat
						}
						if parameterData[ff.Name] != nil {
							irtValue = fmt.Sprint(parameterData[ff.Name])
							if parameterData[ff.Name] != "" {
								parse, err := time.Parse("2006-01-02", irtValue)
								if err != nil {
									return models.SubjectReturnResults{}, errors.WithStack(err)
								}
								irtValue = parse.Format(tools.DateFormatParse(dateFormat))
							}
						}
					} else if ff.Type == "timePicker" {
						timeFormat := "YYYY-MM-DD HH:mm:ss"
						if ff.TimeFormat != "" {
							timeFormat = ff.TimeFormat
						}
						if parameterData[ff.Name] != nil {
							irtValue = fmt.Sprint(parameterData[ff.Name])
							if parameterData[ff.Name] != "" {
								parse, err := time.Parse("2006-01-02 15:04:05", irtValue)
								if err != nil {
									return models.SubjectReturnResults{}, errors.WithStack(err)
								}
								irtValue = parse.Format(tools.DateFormatParse(timeFormat))
							}
						}
					} else {
						if parameterData[ff.Name] != nil {
							irtValue = parameterData[ff.Name].(string)
						}
					}

					// 筛选edc的分层因素/表单的值
					var edcValue = ""
					var isEdcConfigured = false // 默认EDC没配置
					for _, data := range httpObtainEdcStatus.Data.SubjectData {
						if ff.Name == data.IrtKey {
							edcValue = data.EdcValue // edc配置
							isEdcConfigured = true
							resultSubjectReturnResults.EdcOpenForm = true
							if ff.Type == "inputNumber" && ff.FormatType == "decimalLength" && ff.Length != nil && irtValue != "" && edcValue != "" && len(irtValue) != len(edcValue) {
								if strings.Contains(edcValue, ".") {
									var repair = len(irtValue) - len(edcValue)
									// 使用 strings.Repeat 生成 n 个 '0'
									padding := strings.Repeat("0", repair)
									// 将原字符串与补上的0连接起来
									edcValue = edcValue + padding
								} else {
									var repair = len(irtValue) - len(edcValue) - 1
									// 使用 strings.Repeat 生成 n 个 '0'
									padding := strings.Repeat("0", repair)
									// 将原字符串与补上的0连接起来
									edcValue = edcValue + "." + padding
								}

							}
							break
						}
					}
					if isEdcConfigured {
						if edcValue != irtValue {
							resultSubjectReturnResults.FormIsAlike = false
							subjectReturnResultsData = append(subjectReturnResultsData, models.SubjectReturnResultsData{
								Sign:     false,
								EdcValue: edcValue,
								IrtValue: irtValue,
								Label:    ff.Label,
							})
						} else {
							subjectReturnResultsData = append(subjectReturnResultsData, models.SubjectReturnResultsData{
								Sign:     true,
								EdcValue: edcValue,
								IrtValue: irtValue,
								Label:    ff.Label,
							})
						}
					}
				}
			}
		}
	}

	// 随机校验时会传入subject 全部字段，此时 randomNumber 为空
	randomFlag := false
	if randomNumber, ok := parameterData["randomNumber"].(string); ok {
		if randomNumber == "" {
			randomFlag = true
		}
	}

	if !randomFlag {
		// 筛选校验
		if project.ProjectInfo.PushScenario.ScreenPush {
			resultSubjectReturnResults.ScreenIsAlike = true
			screenData := []models.SubjectReturnResultsData{}
			irtSign := true
			labelMap := map[string]string{
				"isScreen":   locales.Tr(ctx, "is_screen"),
				"screenTime": locales.Tr(ctx, "screen_time"),
				"icfTime":    locales.Tr(ctx, "icf_time"),
			}

			for _, key := range []string{"isScreen", "screenTime", "icfTime"} {
				irtValue := ""
				if parameterData[key] != nil {
					if key == "isScreen" {
						if parameterData[key] == true {
							irtValue = "yes"
						} else {
							irtValue = "no"
						}
					} else {
						irtValue = parameterData[key].(string)
					}
				}

				if irtValue != "" {
					irtSign = false
				}

				edcValue := ""
				openScreenSign := false
				for _, data := range httpObtainEdcStatus.Data.SubjectData {
					if key == data.IrtKey {
						edcValue = data.EdcValue
						openScreenSign = true
						resultSubjectReturnResults.EdcOpenScreen = true
						break
					}
				}

				if openScreenSign {
					if edcValue != irtValue {
						resultSubjectReturnResults.ScreenIsAlike = false
						screenData = append(screenData, models.SubjectReturnResultsData{
							Sign:     false,
							EdcValue: edcValue,
							IrtValue: irtValue,
							Label:    labelMap[key],
						})
					} else {
						screenData = append(screenData, models.SubjectReturnResultsData{
							Sign:     true,
							EdcValue: edcValue,
							IrtValue: irtValue,
							Label:    labelMap[key],
						})
					}
				}
			}
			if irtSign {
				resultSubjectReturnResults.ScreenIsAlike = true
				screenData = []models.SubjectReturnResultsData{}
			}
			subjectReturnResultsData = append(subjectReturnResultsData, screenData...)
		}
	}

	resultSubjectReturnResults.SubjectReturnResultsData = subjectReturnResultsData
	return resultSubjectReturnResults, nil
}

// GetNewSubjectPage
func (s *SubjectService) GetNewSubjectPage(ctx *gin.Context, req models.NewSubjectPageReq) (int, error) {
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": req.ProjectID}).Decode(&project)
	if err != nil {
		return 1, errors.WithStack(err)
	}

	match := bson.M{
		"project_id": req.ProjectID,
		"env_id":     req.EnvID,
		"deleted":    bson.M{"$ne": true},
	}
	dispatchMatch := bson.M{
		"project_id": req.ProjectID,
		"env_id":     req.EnvID,
	}
	attributeMatch := bson.M{
		"project_id": req.ProjectID,
		"env_id":     req.EnvID,
	}
	if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		if !req.CohortID.IsZero() {
			match["cohort_id"] = req.CohortID
		}
	} else {
		if !req.CohortID.IsZero() {
			match["cohort_id"] = req.CohortID
			dispatchMatch["cohort_id"] = req.CohortID
			attributeMatch["cohort_id"] = req.CohortID
		}
	}
	// 查询项目属性配置
	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(nil, attributeMatch)
	if err != nil {
		return 1, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return 1, errors.WithStack(err)
	}

	// 随机列表
	randomLists := make([]models.RandomList, 0)
	//forms表单
	forms := make([]models.Form, 0)
	cursor, err = tools.Database.Collection("form").Find(nil, attributeMatch)
	if err != nil {
		return 1, errors.WithStack(err)
	}
	err = cursor.All(nil, &forms)
	if err != nil {
		return 1, errors.WithStack(err)
	}
	cursor, err = tools.Database.Collection("random_list").Find(nil, attributeMatch)
	if err != nil {
		return 1, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return 1, errors.WithStack(err)
	}
	//模糊匹配受试者号 分层 表单
	if req.Keyword != "" {
		//列出所有分层的信息
		type regexFactor struct {
			EnvID        primitive.ObjectID
			CohortID     primitive.ObjectID
			RandomListID primitive.ObjectID
			Label        string
			Name         string
			OptionLabel  string
			OptionValue  string
		}
		regexFactors := make([]regexFactor, 0)
		for _, list := range randomLists {
			for _, factor := range list.Design.Factors {
				for _, option := range factor.Options {
					regexFactors = append(regexFactors, regexFactor{
						EnvID:        list.EnvironmentID,
						CohortID:     list.CohortID,
						RandomListID: list.ID,
						Label:        factor.Label,
						Name:         factor.Name,
						OptionLabel:  option.Label,
						OptionValue:  option.Value,
					})
				}
			}
		}
		regexFactors = slice.Unique(regexFactors)
		//列出所有表单分层的信息
		type regexForm struct {
			EnvID       primitive.ObjectID
			CohortID    primitive.ObjectID
			FormID      primitive.ObjectID
			Label       string
			Name        string
			OptionLabel string
			OptionValue string
		}
		regexForms := make([]regexForm, 0)
		for _, form := range forms {
			for _, field := range form.Fields {
				for _, option := range field.Options {
					regexForms = append(regexForms, regexForm{
						EnvID:       form.EnvironmentID,
						CohortID:    form.CohortID,
						FormID:      form.ID,
						Label:       field.Label,
						Name:        field.Name,
						OptionLabel: option.Label,
						OptionValue: option.Value,
					})
				}
			}
		}
		regexForms = slice.Unique(regexForms)
		regexBsonA := bson.A{}
		regexBsonA = append(regexBsonA, bson.M{"info": bson.M{"$elemMatch": bson.M{"name": "shortname", "value": bson.M{"$regex": req.Keyword, "$options": "i"}}}})
		if len(regexFactors) > 0 {
			factors := slice.Filter(regexFactors, func(index int, item regexFactor) bool {
				return strings.Contains(item.OptionLabel, req.Keyword)
			})
			for _, factor := range factors {
				if factor.CohortID.IsZero() {
					regexBsonA = append(regexBsonA, bson.M{"info": bson.M{"$elemMatch": bson.M{"name": factor.Name, "value": factor.OptionValue}}})
				} else {
					regexBsonA = append(regexBsonA, bson.M{"cohort_id": factor.CohortID, "info": bson.M{"$elemMatch": bson.M{"name": factor.Name, "value": factor.OptionValue}}})
				}

			}
		}
		if len(regexForms) > 0 {
			rForms := slice.Filter(regexForms, func(index int, item regexForm) bool {
				return strings.Contains(item.OptionLabel, req.Keyword)
			})
			for _, form := range rForms {
				if form.CohortID.IsZero() {
					regexBsonA = append(regexBsonA, bson.M{"info": bson.M{"$elemMatch": bson.M{"name": form.Name, "value": form.OptionValue}}})
				} else {
					regexBsonA = append(regexBsonA, bson.M{"cohort_id": form.CohortID, "info": bson.M{"$elemMatch": bson.M{"name": form.Name, "value": form.OptionValue}}})
				}

			}
		}
		match["$or"] = regexBsonA
	}
	if req.SiteIDs != nil && len(req.SiteIDs) > 0 {
		match["project_site_id"] = bson.M{"$in": req.SiteIDs}
	} else {
		service := ProjectSiteService{}
		sites, err := service.UserSites(ctx, project.CustomerID.Hex(), req.ProjectID.Hex(), req.EnvID.Hex(), req.RoleID.Hex())
		if err != nil {
			return 1, err
		}
		if sites != nil && len(sites) > 0 {
			siteIds := slice.Map(sites, func(index int, item map[string]interface{}) primitive.ObjectID {
				return item["id"].(primitive.ObjectID)
			})
			match["project_site_id"] = bson.M{"$in": siteIds}
		} else {
			return 1, nil
		}
	}
	after := bson.M{}
	if req.Status != nil && len(req.Status) > 0 {
		if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.ID == req.EnvID
			})
			cohort, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.LastID == env.Cohorts[0].ID
			})
			isToBeRandom := false
			statusList := make([]int, 0)
			registerScreenStatus := make([]int, 0)
			for _, st := range req.Status {
				if st == 10 {
					isToBeRandom = true
				} else if st == 1 || st == 7 {
					registerScreenStatus = append(registerScreenStatus, st)
				} else {
					statusList = append(statusList, st)
				}
			}
			if isToBeRandom {
				if cohort != nil {
					cohortId := cohort.ID
					after = bson.M{"$or": bson.A{
						bson.M{"status": bson.M{"$in": statusList}},
						bson.M{"cohort_id": cohortId, "status": bson.M{"$in": bson.A{1, 7}}},
						bson.M{"cohort_id": env.Cohorts[0].ID, "status": bson.M{"$in": registerScreenStatus}}}}
				}
			} else {
				after = bson.M{"$or": bson.A{
					bson.M{"status": bson.M{"$in": statusList}},
					bson.M{"cohort_id": env.Cohorts[0].ID, "status": bson.M{"$in": registerScreenStatus}}}}
			}
		} else {

			statusList := make([]int, 0)
			joinStatus := false
			registerScreenStatus := make([]int, 0)
			for _, st := range req.Status {
				if st == 1 || st == 7 {
					registerScreenStatus = append(registerScreenStatus, st)
				} else if st == 11 {
					joinStatus = true
				} else {
					statusList = append(statusList, st)
				}
			}
			matchs := bson.A{}
			if len(statusList) > 0 {
				matchs = append(matchs, bson.M{
					"status": bson.M{"$in": statusList},
				})
			}
			if joinStatus {
				matchs = append(matchs, bson.M{
					"dispensing.status":     bson.M{"$in": bson.A{2, 3}},
					"status":                bson.M{"$in": bson.A{1, 7}},
					"attribute.info.random": false,
				})

				after = bson.M{"$or": bson.A{
					bson.M{
						"status":                bson.M{"$in": req.Status},
						"attribute.info.random": true,
					},
					bson.M{
						"status":                bson.M{"$in": req.Status},
						"attribute.info.random": true,
					},
					bson.M{
						"dispensing.status":     bson.M{"$in": bson.A{2, 3}},
						"status":                bson.M{"$in": bson.A{1, 7}},
						"attribute.info.random": false,
					},
				}}
			}
			if len(registerScreenStatus) > 0 {
				matchs = append(matchs, bson.M{
					"status":                bson.M{"$in": registerScreenStatus},
					"attribute.info.random": false,
					"dispensing.status":     bson.M{"$nin": bson.A{2, 3}},
				})
				matchs = append(matchs, bson.M{
					"status":                bson.M{"$in": registerScreenStatus},
					"attribute.info.random": true,
				})
			}
			after = bson.M{"$or": matchs}
		}
	}
	pipeline := mongo.Pipeline{}
	pipeline = append(pipeline, bson.D{{Key: "$match", Value: match}})
	pipeline = append(pipeline, bson.D{{Key: "$unwind", Value: "$info"}})
	pipeline = append(pipeline, bson.D{{Key: "$match", Value: bson.M{"info.name": "shortname"}}})
	pipeline = append(pipeline, bson.D{{Key: "$group", Value: bson.M{
		"_id":                    "$info.value",
		"id":                     bson.M{"$last": "$_id"},
		"sort_id":                bson.M{"$first": "$_id"}, // 再随机项目按id 排序时使用第一阶段的id
		"random_number":          bson.M{"$last": "$random_number"},
		"random_sequence_number": bson.M{"$last": "$random_sequence_number"},
		"cohort_id":              bson.M{"$last": "$cohort_id"},
		"env_id":                 bson.M{"$last": "$env_id"},
		"status":                 bson.M{"$last": "$status"},
		"ids":                    bson.M{"$push": "$_id"},
	}}})
	pipeline = append(pipeline, bson.D{{"$addFields", bson.M{
		"random_sequence_number_index": bson.M{
			"$cond": bson.M{"if": bson.M{
				"$or": bson.A{
					bson.M{"$not": bson.M{"$ifNull": bson.A{"$random_sequence_number", false}}},
					bson.M{"$eq": bson.A{"$random_sequence_number", ""}},
				}},
				"then": 1, "else": 0}},
		"random_number_index": bson.M{
			"$cond": bson.M{"if": bson.M{
				"$or": bson.A{
					bson.M{"$not": bson.M{"$ifNull": bson.A{"$random_number", false}}},
					bson.M{"$eq": bson.A{"$random_number", ""}},
				}},
				"then": 1, "else": 0}},
	}}})

	pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "id", "foreignField": "subject_id", "as": "dispensing"}}}) //关联发药

	if project.Type == 1 {
		pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "attribute", "localField": "env_id", "foreignField": "env_id", "as": "attribute"}}}) //关联发药
	} else {
		pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "attribute", "localField": "cohort_id", "foreignField": "cohort_id", "as": "attribute"}}}) //关联发药
	}

	pipeline = append(pipeline, bson.D{{Key: "$match", Value: after}})
	if req.SortField != "" {
		if (req.SortField == "random_number" || req.SortField == "random_sequence_number") && req.SortCollation != 0 {
			pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{req.SortField + "_index", req.SortCollation}, {req.SortField, req.SortCollation}}}})
		} else if req.SortField == "shortname" && req.SortCollation != 0 {
			pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{"_id", req.SortCollation}}}})
		}
	} else {
		pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{"sort_id", 1}}}})
	}

	// $project 阶段直接引用从 $group 阶段出来的 _id，即受试者号
	pipeline = append(pipeline, bson.D{{Key: "$project", Value: bson.M{
		"_id": 1, // 上面的group 中 _id 作为 subject_number
	}}})

	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err = tools.Database.Collection("subject").Aggregate(nil, pipeline, opt)
	if err != nil {
		return 1, errors.WithStack(err)
	}
	resps := make([]models.SubjectCountResp, 0)
	err = cursor.All(nil, &resps)
	if err != nil {
		return 1, errors.WithStack(err)
	}
	if len(resps) == 0 {
		return 1, nil
	}

	// 查找受试者号在查询结果中的位置
	var subjectIndex int
	for i, resp := range resps {
		// 查找受试者号是否匹配
		if resp.ID == req.NewSubjectNumber {
			subjectIndex = i
			break
		}
	}

	// 计算该受试者号所在的页码
	pageNumber := (subjectIndex / req.Limit) + 1

	return pageNumber, nil
}

func (s *SubjectService) GetInvalidList(ctx *gin.Context, projectID string, envID string, roleID string, cohortID string) (map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	// 查询项目
	var project models.Project
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目属性配置
	attributeMatch := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(ctx, attributeMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//判断属性配置的盲态，多个cohort如果有一个盲态就算盲态
	isRandomNumber := false
	blind := false
	attribute := models.Attribute{}
	if len(attributes) == 1 {
		attribute = attributes[0]
		isRandomNumber = attribute.AttributeInfo.IsRandomNumber
		blind = attribute.AttributeInfo.Blind
	} else {
		if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			if len(attributes) > 1 {
				for _, attribute := range attributes {
					// 多个属性配置有一个展示随机号就展示随机号列
					if attribute.AttributeInfo.IsRandomNumber {
						isRandomNumber = true
					}
					// 多个属性配置有一个展示盲态就展示揭盲列
					if attribute.AttributeInfo.Blind {
						blind = true
					}
				}
				attribute = attributes[0]
			}
		} else {
			if cohortOID.IsZero() && len(attributes) > 1 {
				isRandomNumberGroup := slice.GroupWith(attributes, func(item models.Attribute) bool {
					return item.AttributeInfo.IsRandomNumber
				})
				if len(maputil.Keys(isRandomNumberGroup)) == 1 {
					randomAttribute := isRandomNumberGroup[maputil.Keys(isRandomNumberGroup)[0]][0]
					isRandomNumber = randomAttribute.AttributeInfo.IsRandomNumber
				}
				blindGroup := slice.GroupWith(attributes, func(item models.Attribute) bool {
					return item.AttributeInfo.Blind
				})
				if len(maputil.Keys(blindGroup)) == 1 {
					blindAttribute := blindGroup[maputil.Keys(blindGroup)[0]][0]
					blind = blindAttribute.AttributeInfo.Blind
				}
				attribute = attributes[0]
			}
		}
	}

	// 随机列表
	randomLists := make([]models.RandomList, 0)
	//forms表单
	forms := make([]models.Form, 0)
	cursor, err = tools.Database.Collection("form").Find(ctx, attributeMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &forms)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor, err = tools.Database.Collection("random_list").Find(ctx, attributeMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &randomLists)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	filter := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
		"deleted":    true,
	}

	// 查询中心
	service := ProjectSiteService{}
	sites, err := service.UserSites(ctx, project.CustomerID.Hex(), projectID, envID, roleID)
	if err != nil {
		return nil, err
	}
	if sites != nil && len(sites) > 0 {
		siteIds := slice.Map(sites, func(index int, item map[string]interface{}) primitive.ObjectID {
			return item["id"].(primitive.ObjectID)
		})
		filter["project_site_id"] = bson.M{"$in": siteIds}
	} else {
		return map[string]interface{}{"total": 0,
			"items":          []models.SubjectView{},
			"fields":         []models.ListField{},
			"blind":          blind,
			"isRandomNumber": isRandomNumber,
			"reRandomForm":   []models.ReRandomInfo{},
		}, nil
	}

	pipeline := mongo.Pipeline{}
	pipeline = append(pipeline, bson.D{{Key: "$match", Value: filter}})
	pipeline = append(pipeline, bson.D{{Key: "$unwind", Value: "$info"}})
	pipeline = append(pipeline, bson.D{{Key: "$match", Value: bson.M{"info.name": "shortname"}}})
	pipeline = append(pipeline, bson.D{{Key: "$group", Value: bson.M{
		"_id":           "$info.value",
		"id":            bson.M{"$last": "$_id"},
		"sort_id":       bson.M{"$first": "$_id"}, // 再随机项目按id 排序时使用第一阶段的id
		"random_number": bson.M{"$last": "$random_number"},
		"cohort_id":     bson.M{"$last": "$cohort_id"},
		"env_id":        bson.M{"$last": "$env_id"},
		"status":        bson.M{"$last": "$status"},
		"ids":           bson.M{"$push": "$_id"},
	}}})

	pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "id", "foreignField": "subject_id", "as": "dispensing"}}}) //关联发药

	if project.Type == 1 {
		pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "attribute", "localField": "env_id", "foreignField": "env_id", "as": "attribute"}}}) //关联发药
	} else {
		pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{"from": "attribute", "localField": "cohort_id", "foreignField": "cohort_id", "as": "attribute"}}}) //关联发药
	}

	pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.D{{"sort_id", -1}}}})

	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err = tools.Database.Collection("subject").Aggregate(ctx, pipeline, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resps := make([]models.SubjectCountResp, 0)
	err = cursor.All(ctx, &resps)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(resps) == 0 {
		return map[string]interface{}{"total": 0,
			"items":          []models.SubjectView{},
			"fields":         []models.ListField{},
			"blind":          blind,
			"isRandomNumber": isRandomNumber,
			"reRandomForm":   []models.ReRandomInfo{},
		}, nil
	}

	subjectIds := make([]primitive.ObjectID, 0)
	for _, resp := range resps {
		for _, id := range resp.IDs {
			subjectIds = append(subjectIds, id)
		}
	}

	var subjectData []models.SubjectView
	opts := &options.FindOptions{}
	opts.Sort = bson.D{{"_id", -1}}
	cursor, err = tools.Database.Collection("subject").Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &subjectData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//查完受试者后再查询中心
	projectSiteIds := slice.Map(subjectData, func(index int, item models.SubjectView) primitive.ObjectID {
		return item.ProjectSiteID
	})
	projectSiteIds = slice.Union(projectSiteIds)
	// 查询中心
	projectSites := make([]models.ProjectSite, 0)
	projectSiteMap := make(map[primitive.ObjectID]models.ProjectSite)
	siteProjectMatch := bson.M{"_id": bson.M{"$in": projectSiteIds}}
	cursor, err = tools.Database.Collection("project_site").Find(nil, siteProjectMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, site := range projectSites {
		projectSiteMap[site.ID] = site
	}

	subNameSubjects := slice.Filter(subjectData, func(index int, item models.SubjectView) bool {
		return item.SubGroupName != ""
	})
	if len(subNameSubjects) > 0 {
		randomListIds := slice.Map(subNameSubjects, func(index int, item models.SubjectView) primitive.ObjectID {
			return item.RandomListID
		})
		replaceSubject := slice.Filter(subNameSubjects, func(index int, item models.SubjectView) bool {
			return item.RandomListID.IsZero()
		})
		beReplaceSubjectViews := make([]models.Subject, 0)
		if len(replaceSubject) > 0 {
			replaceSubjectIds := slice.Map(replaceSubject, func(index int, item models.SubjectView) primitive.ObjectID {
				return item.ID
			})
			cursor, err = tools.Database.Collection("subject").Find(nil, bson.M{"replace_subject_id": bson.M{"$in": replaceSubjectIds}, "deleted": bson.M{"$ne": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &beReplaceSubjectViews)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			beReplaceRandomListIds := slice.Map(beReplaceSubjectViews, func(index int, item models.Subject) primitive.ObjectID {
				return item.RandomListID
			})
			randomListIds = append(randomListIds, beReplaceRandomListIds...)
		}

		randomListIds = slice.Unique(randomListIds)
		replaceRandomLists := make([]models.RandomList, 0)
		//如果前面有做模糊匹配查询过了randomlist就直接用，否则才去查
		if len(randomLists) == 0 {
			cursor, err = tools.Database.Collection("random_list").Find(nil, bson.M{"_id": bson.M{"$in": randomListIds}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &replaceRandomLists)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			replaceRandomLists = slice.Filter(randomLists, func(index int, item models.RandomList) bool {
				return slice.Contain(randomListIds, item.ID)
			})
		}

		//子组别是否需要盲态
		for i, subject := range subjectData {
			if subject.SubGroupName != "" {
				listP, b := slice.Find(replaceRandomLists, func(index int, item models.RandomList) bool {
					return subject.RandomListID == item.ID
				})
				if b {
					list := *listP
					groupP, b2 := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
						return subject.ParGroupName == item.ParName && subject.SubGroupName == item.SubName
					})
					if b2 {
						subjectData[i].SubNameBlind = groupP.Blind
					}
				} else {
					sP, b3 := slice.Find(beReplaceSubjectViews, func(index int, item models.Subject) bool {
						return item.ReplaceSubjectID == subject.ID
					})
					if b3 {
						s := *sP
						replaceListP, b4 := slice.Find(randomLists, func(index int, item models.RandomList) bool {
							return s.RandomListID == item.ID
						})
						if b4 {
							list := *replaceListP
							groupP, b2 := slice.Find(list.Design.Groups, func(index int, item models.RandomListGroup) bool {
								return subject.ParGroupName == item.ParName && subject.SubGroupName == item.SubName
							})
							if b2 {
								subjectData[i].SubNameBlind = groupP.Blind
							}
						}
					}
				}
			}
		}
	}

	// 查询访视周期
	dispatchMatch := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	visitCycles := make([]models.VisitCycle, 0)
	cursor, err = tools.Database.Collection("visit_cycle").Find(nil, dispatchMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询发药记录

	/* 查询优化筛选查到的受试者，不查全部发药信息
	   jira 4376 发药项目，在受试者进行发药后，隐藏对应的删除操作
	*/
	dispatchMatch["subject_id"] = bson.M{"$in": subjectIds}
	var dispensingArray []models.DispensingHistory
	dispensingAllCursor, _ := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{"$match", dispatchMatch}},
		{{Key: "$lookup", Value: bson.M{"from": "history", "localField": "_id", "foreignField": "oid", "as": "history"}}},
		{{"$sort", bson.D{{"subject_id", 1}, {"serial_number", 1}}}},
	})
	err = dispensingAllCursor.All(nil, &dispensingArray)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询是否有分层因素
	randomActiveLists := make([]models.RandomList, 0)
	randomFilter := bson.M{"env_id": envOID, "status": 1}
	cursor, err = tools.Database.Collection("random_list").Find(ctx, randomFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &randomActiveLists)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询该环境关联的所有用户
	userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
	userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(ctx, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userProjectEnvironmentCursor.All(ctx, &userProjectEnvironmentList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var userIDs []primitive.ObjectID
	for _, upel := range userProjectEnvironmentList {
		userIDs = append(userIDs, upel.UserID)
	}

	// 查询user
	var userList []models.User
	userCursor, err := tools.Database.Collection("user").Find(ctx, bson.M{"_id": bson.M{"$in": userIDs}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userCursor.All(ctx, &userList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目角色权限
	projectRolePermissionList := make([]models.ProjectRolePermission, 0)
	projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(ctx, bson.M{"project_id": projectOID, "name": bson.M{"$ne": "Project-Admin"}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = projectRolePermissionCursor.All(ctx, &projectRolePermissionList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询用户关联的角色
	userSiteList := make([]models.UserSite, 0)
	userSiteCursor, err := tools.Database.Collection("user_site").Find(ctx, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userSiteCursor.All(ctx, &userSiteList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 处理数据
	resultData, reRandomResultData, err := handleDataForInvalid(
		ctx,
		project,
		attributes,
		resps,
		subjectData,
		dispensingArray,
		roleID,
		visitCycles,
		projectSiteMap,
		randomLists,
		randomActiveLists,
		forms,
		userProjectEnvironmentList,
		userList,
		projectRolePermissionList,
		userSiteList,
	)
	if err != nil {
		return nil, err
	}

	//过滤未使用的fields
	usedField := make([]models.ListField, 0)
	reRandomUsedInfos := make([]models.ReRandomForm, 0)
	if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		cohortMap := make(map[primitive.ObjectID][]models.SubjectView)
		cohortMap = slice.GroupWith(reRandomResultData, func(item models.SubjectView) primitive.ObjectID {
			return item.CohortID
		})
		env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == envOID
		})
		for _, cohort := range env.Cohorts {
			cohortData := cohortMap[cohort.ID]
			usedField = getUsedInfo(cohortData, forms, randomLists, attribute)
			reRandomUsedInfos = append(reRandomUsedInfos, models.ReRandomForm{
				CohortID:   cohort.ID,
				CohortName: cohort.Name,
				Fields:     usedField,
			})
		}
		if len(reRandomUsedInfos[0].Fields) == 0 {
			reRandomUsedInfos[0].Fields = append(reRandomUsedInfos[0].Fields, reRandomUsedInfos[1].Fields[0])
		}
	} else {
		usedField = getUsedInfo(resultData, forms, randomLists, attribute)
	}

	return map[string]interface{}{
		"items":          resultData,
		"fields":         usedField,
		"blind":          blind,
		"isRandomNumber": isRandomNumber,
		"reRandomForm":   reRandomUsedInfos,
	}, nil
}

// 处理list查询的数据
func handleDataForInvalid(
	ctx *gin.Context,
	project models.Project,
	attributes []models.Attribute,
	subjectResp []models.SubjectCountResp,
	subjectData []models.SubjectView,
	dispensingArray []models.DispensingHistory,
	roleID string,
	visitCycles []models.VisitCycle,
	projectSiteMap map[primitive.ObjectID]models.ProjectSite,
	randomLists []models.RandomList,
	randomActiveLists []models.RandomList,
	forms []models.Form,
	userProjectEnvironmentList []models.UserProjectEnvironment,
	userList []models.User,
	projectRolePermissionList []models.ProjectRolePermission,
	userSiteList []models.UserSite) ([]models.SubjectView, []models.SubjectView, error) {

	//timezone := project.ProjectInfo.TimeZone
	timezone, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
	var resultData []models.SubjectView
	var reRandomResultData []models.SubjectView
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, nil, err
	}
	isBlindedRoomRole, err := tools.IsBlindedRoomRole(roleID)
	if err != nil {
		return nil, nil, err
	}
	if project.ProjectInfo.Type != 3 || tools.InRandomIsolation(project.ProjectInfo.Number) {
		for _, subjectView := range subjectData {
			err := subjectDataToSubjectView(
				ctx,
				project,
				attributes,
				&subjectView,
				visitCycles,
				dispensingArray,
				isBlindedRole,
				isBlindedRoomRole,
				timezone,
				projectSiteMap,
				randomLists,
				randomActiveLists,
				forms,
				userProjectEnvironmentList,
				userList,
				projectRolePermissionList,
				userSiteList,
			)
			if err != nil {
				return nil, nil, err
			}
			resultData = append(resultData, subjectView)
		}
	} else {
		for _, subjectView := range subjectData {
			err := subjectDataToSubjectView(
				ctx,
				project,
				attributes,
				&subjectView,
				visitCycles,
				dispensingArray,
				isBlindedRole,
				isBlindedRoomRole,
				timezone,
				projectSiteMap,
				randomLists,
				randomActiveLists,
				forms,
				userProjectEnvironmentList,
				userList,
				projectRolePermissionList,
				userSiteList,
			)
			if err != nil {
				return nil, nil, err
			}
			reRandomInfo := make([]models.ReRandomInfo, 0)
			reRandomActualInfo := make([]models.ReRandomInfo, 0)
			reRandomJoinTime := make([]models.ReRandomJoinTime, 0)

			joinTime, err := getJoinTime(subjectView.Attribute, subjectView)
			if err != nil {
				return nil, nil, err
			}
			reRandomJoinTime = append(reRandomJoinTime, models.ReRandomJoinTime{
				CohortID: subjectView.CohortID,
				JoinTime: joinTime,
			})

			// 第一阶段数据
			for _, info := range subjectView.Info {
				reRandomInfo = append(reRandomInfo, models.ReRandomInfo{
					CohortID: subjectView.CohortID,
					Name:     info.Name,
					Value:    info.Value,
				})
			}
			for _, info := range subjectView.ActualInfo {
				reRandomActualInfo = append(reRandomActualInfo, models.ReRandomInfo{
					CohortID: subjectView.CohortID,
					Name:     info.Name,
					Value:    info.Value,
				})
			}
			form := models.Form{}
			formP, _ := slice.Find(forms, func(index int, item models.Form) bool {
				return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID
			})
			if formP != nil {
				form = *formP
			}
			//表单
			if form.Fields != nil {
				for _, fm := range form.Fields {
					//这里过滤掉无效并且该受试者没使用过的表单字段
					infoP, b := slice.Find(reRandomInfo, func(index int, item models.ReRandomInfo) bool {
						return item.Name == fm.Name && item.CohortID == subjectView.CohortID
					})
					if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && b && (fm.Status == nil || *fm.Status == 1 || infoP.Value != nil) {
						err = reRandomInfoLabelFormHandler(ctx, fm, reRandomInfo, infoP)
						if err != nil {
							return nil, nil, err
						}
					}
					aInfoP, b := slice.Find(reRandomActualInfo, func(index int, item models.ReRandomInfo) bool {
						return item.Name == fm.Name && item.CohortID == subjectView.CohortID
					})
					if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && b && (fm.Status == nil || *fm.Status == 1 || infoP.Value != nil) {
						err = reRandomInfoLabelFormHandler(ctx, fm, reRandomActualInfo, aInfoP)
						if err != nil {
							return nil, nil, err
						}
					}
				}
			}
			randomList := models.RandomList{}
			//先查是不是已随机，没随机的时候就拿第一个激活的
			if !subjectView.RandomListID.IsZero() {
				randomListP, _ := slice.Find(randomLists, func(index int, item models.RandomList) bool {
					return subjectView.RandomListID == item.ID
				})
				randomList = *randomListP
			} else {
				randomListP, b := slice.Find(randomActiveLists, func(index int, item models.RandomList) bool {
					return item.EnvironmentID == subjectView.EnvironmentID && item.CohortID == subjectView.CohortID && fileSiteIds(item.SiteIds, subjectView.ProjectSiteID)
				})
				if b {
					randomList = *randomListP
				}
			}
			if randomList.Design.Factors != nil {
				for i := 0; i < len(randomList.Design.Factors); i++ {
					ft := randomList.Design.Factors[i]
					//这里过滤掉无效并且该受试者没使用过的分层字段
					infoP, exist := slice.Find(reRandomInfo, func(index int, item models.ReRandomInfo) bool {
						return item.Name == ft.Name && (item.Value != nil || item.Value != "") && item.CohortID == subjectView.CohortID
					})
					status := ft.Status
					if (status == nil || *status == 1) && exist {
						err = reRandomInfoLabelHandler(ctx, ft, reRandomInfo, infoP)
						if err != nil {
							return nil, nil, err
						}
					}
					aInfoP, exist := slice.Find(reRandomActualInfo, func(index int, item models.ReRandomInfo) bool {
						return item.Name == ft.Name && (item.Value != nil || item.Value != "") && item.CohortID == subjectView.CohortID
					})
					if (status == nil || *status == 1) && exist {
						err = reRandomInfoLabelHandler(ctx, ft, reRandomActualInfo, aInfoP)
						if err != nil {
							return nil, nil, err
						}
					}
				}
			}
			reRandomResultData = append(reRandomResultData, subjectView)

			subjectView.ReRandomInfo = reRandomInfo
			subjectView.ReRandomActualInfo = reRandomActualInfo
			subjectView.ReRandomJoinTime = reRandomJoinTime

			resultData = append(resultData, subjectView)
		}
	}
	return resultData, reRandomResultData, nil
}

// SwitchCohort 受试者切换群组
func (s *SubjectService) SwitchCohort(ctx *gin.Context) error {
	var subject map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&subject, binding.JSON)
	ID, _ := primitive.ObjectIDFromHex(subject["id"].(string))
	newCohortID, _ := primitive.ObjectIDFromHex(subject["newCohort"].(string))

	u, _ := ctx.Get("user")
	user := u.(models.User)
	now := time.Duration(time.Now().Unix())

	var project models.Project
	oldCohortName := ""
	newCohortName := ""
	var oldSubject models.Subject
	var historyData []models.History
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		subjectFilter := bson.M{"_id": ID}

		err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&oldSubject)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		//1、受试者状态：当前仅支持状态处于随机前的受试者进行群组切换
		//if oldSubject.Status != 7 || (oldSubject.IsScreen == nil) || (oldSubject.IsScreen != nil && !*oldSubject.IsScreen) {
		//	return nil, tools.BuildServerError(ctx, "subject_switch_cohort_fail1")
		//}
		// 判断受试者是否已随机
		//if oldSubject.RandomNumber != "" && len(oldSubject.RandomNumber) > 0 {
		//	return nil, tools.BuildServerError(ctx, "subject_switch_cohort_fail1")
		//}

		projectFilter := bson.M{"_id": oldSubject.ProjectID}

		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		if project.Environments != nil && len(project.Environments) > 0 {
			for _, environment := range project.Environments {
				if environment.ID == oldSubject.EnvironmentID {
					if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
						for _, cohort := range environment.Cohorts {
							if cohort.ID == oldSubject.CohortID {
								oldCohortName = cohort.Name
							}
							if cohort.ID == newCohortID {
								newCohortName = cohort.Name
							}
						}
					}
				}
			}
		}

		////2、edc对接不能勾选随机前修改推送：当前项目与EDC进行了随机前修改的数据对接，不支持进行群组切换
		//if project.ProjectInfo.PushScenario.UpdateRandomFrontPush {
		//	return nil, tools.BuildServerError(ctx, "subject_switch_cohort_fail2")
		//}

		//dispensingList := make([]models.Dispensing, 0)
		//dsCursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"subject_id": oldSubject.ID})
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}
		//err = dsCursor.All(nil, &dispensingList)
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}
		//issued := false
		//if dispensingList != nil && len(dispensingList) > 0 {
		//	for _, dispensing := range dispensingList {
		//		if dispensing.Status == 2 {
		//			issued = true
		//			break
		//		}
		//	}
		//}
		////3、发药：受试者已产生发药，不支持进行群组切换
		//if issued {
		//	return nil, tools.BuildServerError(ctx, "subject_switch_cohort_fail3")
		//}

		//4、表单分层不一致：切换群组与原群组表单分层配置不同，请确认
		isBool, err := judgeTypeGroupFactorAndForm(ctx, sctx, project, oldSubject.EnvironmentID, oldSubject.CohortID, newCohortID, oldSubject)
		if !isBool {
			return nil, tools.BuildServerError(ctx, "subject_switch_cohort_fail4")
		}

		randomFilter := bson.M{
			"customer_id": oldSubject.CustomerID,
			"env_id":      oldSubject.EnvironmentID,
			"status":      1,
			"cohort_id":   newCohortID,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": oldSubject.ProjectSiteID},
			},
		}

		var randomList models.RandomList
		err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter).Decode(&randomList)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 查询属性配置
		filter := bson.M{"customer_id": oldSubject.CustomerID, "project_id": oldSubject.ProjectID, "env_id": oldSubject.EnvironmentID, "cohort_id": newCohortID}
		var attribute models.Attribute
		err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询访视周期
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 创建发药访视周期（只有发药项目才需要设置访视周期）
		if attribute.AttributeInfo.Dispensing {

			//删除已经存在的发药访视数据
			_, err = tools.Database.Collection("dispensing").DeleteMany(sctx, bson.M{
				"customer_id": oldSubject.CustomerID,
				"env_id":      oldSubject.EnvironmentID,
				"cohort_id":   oldSubject.CohortID,
				"subject_id":  oldSubject.ID,
			})
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if visitCycle.Infos == nil || len(visitCycle.Infos) == 0 {
				return nil, tools.BuildServerError(ctx, "subject_no_visit")
			}
			dispensingVisit := make([]interface{}, len(visitCycle.Infos))
			serialNumber := 100
			var reasons []models.Reason
			for index, visit := range visitCycle.Infos {
				dispensingVisit[index] = models.Dispensing{
					ID:            primitive.NewObjectID(),
					CustomerID:    oldSubject.CustomerID,
					ProjectID:     oldSubject.ProjectID,
					EnvironmentID: oldSubject.EnvironmentID,
					CohortID:      newCohortID,
					SubjectID:     oldSubject.ID,
					VisitInfo: models.VisitInfo{
						VisitCycleInfoID: visit.ID,
						Number:           visit.Number,
						InstanceRepeatNo: "0",
						BlockRepeatNo:    "0",
						Name:             visit.Name,
						Random:           visit.Random,
						Dispensing:       visit.Dispensing,
					},
					SerialNumber: serialNumber,
					VisitSign:    false,
					Status:       1,
					Reasons:      reasons,
				}
				serialNumber += 100
			}

			// 批量添加发药访视计划
			if _, err := tools.Database.Collection("dispensing").InsertMany(sctx, dispensingVisit); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		subjectUpdate := bson.M{"$set": bson.M{
			"cohort_id":               newCohortID,
			"register_random_list_id": randomList.ID,
		}}
		if _, err := tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": oldSubject.ID}, subjectUpdate); err != nil {
			return nil, errors.WithStack(err)
		}

		//添加轨迹
		key := "history.subject.switch-cohort"
		var histories []models.History
		history := models.History{
			Key: key,
			OID: ID,
			Data: map[string]interface{}{
				"label":     attribute.AttributeInfo.SubjectReplaceText,
				"enLabel":   attribute.AttributeInfo.SubjectReplaceTextEn,
				"subjectNo": subject["shortname"],
				"oldCohort": oldCohortName,
				"newCohort": newCohortName},
			Time: now,
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)

		// 查找该受试者的历史纪录里是否有修改操作
		filter = bson.M{"key": bson.M{"$regex": "history.subject"}, "oid": ID}
		opts := options.Find().SetSort(bson.D{{"time", -1}})

		cursor, err := tools.Database.Collection("history").Find(ctx, filter, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(ctx, &historyData)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ProjectInfo.ConnectEdc, project.ProjectInfo.PushMode, project.ProjectInfo.EdcSupplier, true) {
		// 根据受试者的当前的状态及推送场景的勾选情况来判断是否需要推送
		var status = oldSubject.Status
		var pushScenario = project.ProjectInfo.PushScenario
		var pushFlag bool

		// 已登记
		if status == 1 {
			if pushScenario.RegisterPush {
				pushFlag = true
			} else {
				// 如果未勾选登记场景，则查找修改
				if pushScenario.UpdateRandomFrontPush {
					var hasUpdate bool
					updateKeys := []string{ // 修改
						"history.subject.update",
						"history.subject.at-random-update",
						"history.subject.updateSubjectNo",
						"history.subject.label.updateSubjectNo",
						"history.subject.label.update",
						"history.subject.label.at-random-update",
						"history.subject.label.updateCustomize",
						"history.subject.label.updateSignOutTime",
						"history.subject.label.updateScreen",
						"history.subject.label.updateScreenSignOutTime",
						"history.subject.label.updateScreenFail",
						"history.subject.label.updateScreenFailSignOutTime",
					}

					for _, history := range historyData {
						if slice.Contain(updateKeys, history.Key) {
							hasUpdate = true
							break
						}
					}

					if hasUpdate {
						pushFlag = true
					}
				}
			}
		}

		// 已筛选、筛选成功、筛选失败
		if !pushFlag && (status == 2 || status == 7 || status == 8) {
			if pushScenario.RegisterPush || pushScenario.UpdateRandomFrontPush || pushScenario.ScreenPush {
				pushFlag = true
			}
		}

		if pushFlag {
			logData := tools.PrepareLogData(ctx)
			AsyncSubjectRandomPush(logData, ID, 14, now, "", "", oldCohortName)
		}
	}
	return nil

}

// 判断随机类型、治疗组别、分层因素、表单是否一致
func judgeTypeGroupFactorAndForm(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, envID primitive.ObjectID, oldCohortID primitive.ObjectID, newCohortID primitive.ObjectID, oldSubject models.Subject) (bool, error) {

	oldEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": envID, "cohort_id": oldCohortID}
	newEnvCondition := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": envID, "cohort_id": newCohortID}

	var existEnvAttribute models.Attribute
	if err := tools.Database.Collection("attribute").FindOne(nil, oldEnvCondition).Decode(&existEnvAttribute); err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	var newEnvAttribute models.Attribute
	if err := tools.Database.Collection("attribute").FindOne(nil, newEnvCondition).Decode(&newEnvAttribute); err != nil && err != mongo.ErrNoDocuments {
		return false, errors.WithStack(err)
	}

	isExistence := false
	if existEnvAttribute.AttributeInfo.CountryLayered != newEnvAttribute.AttributeInfo.CountryLayered &&
		existEnvAttribute.AttributeInfo.InstituteLayered != newEnvAttribute.AttributeInfo.InstituteLayered &&
		existEnvAttribute.AttributeInfo.RegionLayered != newEnvAttribute.AttributeInfo.RegionLayered {
		isExistence = false
	} else {
		isExistence = true
		if existEnvAttribute.AttributeInfo.RegionLayered == newEnvAttribute.AttributeInfo.RegionLayered {
			existEnvMatch := bson.M{
				"env_id":  envID,
				"deleted": bson.M{"$ne": true},
			}
			existEnvRegionList := make([]models.Region, 0)
			cursor, err := tools.Database.Collection("region").Find(nil, existEnvMatch)
			if err != nil {
				return false, errors.WithStack(err)
			}
			err = cursor.All(nil, &existEnvRegionList)
			if err != nil {
				return false, errors.WithStack(err)
			}
			existEnvNameList := make([]string, 0)
			if existEnvRegionList != nil && len(existEnvRegionList) > 0 {
				for _, region := range existEnvRegionList {
					existEnvNameList = append(existEnvNameList, region.Name)
				}
			}

			newEnvMatch := bson.M{
				"env_id":  envID,
				"deleted": bson.M{"$ne": true},
			}
			newEnvRegionList := make([]models.Region, 0)
			cu, err := tools.Database.Collection("region").Find(nil, newEnvMatch)
			if err != nil {
				return false, errors.WithStack(err)
			}
			err = cu.All(nil, &newEnvRegionList)
			if err != nil {
				return false, errors.WithStack(err)
			}
			newEnvNameList := make([]string, 0)
			if newEnvRegionList != nil && len(newEnvRegionList) > 0 {
				for _, region := range newEnvRegionList {
					newEnvNameList = append(newEnvNameList, region.Name)
				}
			}

			// 对数组进行排序
			sort.Strings(existEnvNameList)
			sort.Strings(newEnvNameList)

			// 比较两个排序后的字符串数组是否相等
			if !reflect.DeepEqual(existEnvNameList, newEnvNameList) {
				//fmt.Println("两个字符串数组不相等")
				isExistence = false
			} else {
				isExistence = true
			}
		}
	}

	if isExistence {

		existEnvRandomDesign := models.RandomDesign{}
		if err := tools.Database.Collection("random_design").FindOne(ctx, oldEnvCondition).Decode(&existEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
			return false, errors.WithStack(err)
		}

		newEnvRandomDesign := models.RandomDesign{}
		if err := tools.Database.Collection("random_design").FindOne(ctx, newEnvCondition).Decode(&newEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
			return false, errors.WithStack(err)
		}

		if existEnvRandomDesign.Info.Type != newEnvRandomDesign.Info.Type {
			return false, nil
		} else {

			existEnvMainGroupList := transformationGroup(existEnvRandomDesign)
			newEnvMainGroupList := transformationGroup(newEnvRandomDesign)

			// 对数组进行排序
			sort.Slice(existEnvMainGroupList, func(i, j int) bool {
				return existEnvMainGroupList[i].Code < existEnvMainGroupList[j].Code
			})
			// 对 sub_group 数组进行排序
			for _, group := range existEnvMainGroupList {
				sort.Slice(group.ViceGroup, func(i, j int) bool {
					return group.ViceGroup[i].Name < group.ViceGroup[j].Name
				})
			}

			sort.Slice(newEnvMainGroupList, func(i, j int) bool {
				return newEnvMainGroupList[i].Code < newEnvMainGroupList[j].Code
			})
			for _, group := range newEnvMainGroupList {
				sort.Slice(group.ViceGroup, func(i, j int) bool {
					return group.ViceGroup[i].Name < group.ViceGroup[j].Name
				})
			}

			// 比较两个排序后的字符串数组是否相等
			if !reflect.DeepEqual(existEnvMainGroupList, newEnvMainGroupList) {
				//fmt.Println("两个字符串数组不相等")
				for _, newMainGroup := range newEnvMainGroupList {
					_, b := slice.Find(existEnvMainGroupList, func(index int, item models.MainGroup) bool {
						return item.Code == newMainGroup.Code && item.Name == newMainGroup.Name &&
							reflect.DeepEqual(item.ViceGroup, newMainGroup.ViceGroup)
					})
					if !b {
						groupList := make([]string, 0)
						if newMainGroup.ViceGroup != nil && len(newMainGroup.ViceGroup) > 0 {
							for _, vg := range newMainGroup.ViceGroup {
								gp := newMainGroup.Name + " " + vg.Name
								groupList = append(groupList, gp)
							}
						} else {
							groupList = append(groupList, newMainGroup.Name)
						}
						condition := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID}
						condition["group"] = bson.M{"$in": groupList}
						if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, condition); count > 0 {
							return false, nil
						}
					}
				}
			} else {
				isExistence = true
			}

			if isExistence {

				//随机列表
				var oldRandomList models.RandomList
				err := tools.Database.Collection("random_list").FindOne(sctx, bson.M{"_id": oldSubject.RegisterRandomListID}).Decode(&oldRandomList)
				if err != nil && err != mongo.ErrNoDocuments {
					return false, errors.WithStack(err)
				}

				randomFilter := bson.M{
					"customer_id": oldSubject.CustomerID,
					"env_id":      oldSubject.EnvironmentID,
					"status":      1,
					"cohort_id":   newCohortID,
					"$or": bson.A{
						bson.M{"site_ids": nil},
						bson.M{"site_ids": oldSubject.ProjectSiteID},
					},
				}

				var newRandomList models.RandomList
				err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter).Decode(&newRandomList)
				if err != nil && err != mongo.ErrNoDocuments {
					return false, errors.WithStack(err)
				}

				oldRandomList.ID = primitive.NilObjectID
				oldRandomList.CustomerID = primitive.NilObjectID
				oldRandomList.ProjectID = primitive.NilObjectID
				oldRandomList.EnvironmentID = primitive.NilObjectID
				oldRandomList.CohortID = primitive.NilObjectID
				if oldRandomList.Design.Groups != nil && len(oldRandomList.Design.Groups) > 0 {
					for i, _ := range oldRandomList.Design.Groups {
						oldRandomList.Design.Groups[i].ID = primitive.NilObjectID
					}
				}
				sort.Slice(oldRandomList.Design.Groups, func(i, j int) bool {
					return oldRandomList.Design.Groups[i].Name < oldRandomList.Design.Groups[j].Name
				})
				if oldRandomList.Design.Factors != nil && len(oldRandomList.Design.Factors) > 0 {
					for i, _ := range oldRandomList.Design.Factors {
						oldRandomList.Design.Factors[i].ID = primitive.NilObjectID
						if oldRandomList.Design.Factors[i].Options != nil && len(oldRandomList.Design.Factors[i].Options) > 0 {
							for j, _ := range oldRandomList.Design.Factors[i].Options {
								oldRandomList.Design.Factors[i].Options[j].IsCopyData = false
							}
							sort.Slice(oldRandomList.Design.Factors[i].Options, func(k, j int) bool {
								return oldRandomList.Design.Factors[i].Options[k].Value < oldRandomList.Design.Factors[i].Options[j].Value
							})
						}
						oldRandomList.Design.Factors[i].IsCopyData = false
					}
				}
				sort.Slice(oldRandomList.Design.Factors, func(i, j int) bool {
					return oldRandomList.Design.Factors[i].Number < oldRandomList.Design.Factors[j].Number
				})
				if oldRandomList.Design.Combination != nil && len(oldRandomList.Design.Combination) > 0 {
					for i, _ := range oldRandomList.Design.Combination {
						oldRandomList.Design.Combination[i].ID = primitive.NilObjectID
					}
				}
				sort.Slice(oldRandomList.Design.Combination, func(i, j int) bool {
					return oldRandomList.Design.Combination[i].Number < oldRandomList.Design.Combination[j].Number
				})
				oldRandomList.Design.BlockNumber = 0
				oldRandomList.Config = models.RandomListConfig{}
				oldRandomList.Version = 0
				oldRandomList.CustomHeaders = []string{}
				oldRandomList.Meta = models.Meta{}

				newRandomList.ID = primitive.NilObjectID
				newRandomList.CustomerID = primitive.NilObjectID
				newRandomList.ProjectID = primitive.NilObjectID
				newRandomList.EnvironmentID = primitive.NilObjectID
				newRandomList.CohortID = primitive.NilObjectID
				if newRandomList.Design.Groups != nil && len(newRandomList.Design.Groups) > 0 {
					for i, _ := range newRandomList.Design.Groups {
						newRandomList.Design.Groups[i].ID = primitive.NilObjectID
					}
				}
				sort.Slice(newRandomList.Design.Groups, func(i, j int) bool {
					return newRandomList.Design.Groups[i].Name < newRandomList.Design.Groups[j].Name
				})
				if newRandomList.Design.Factors != nil && len(newRandomList.Design.Factors) > 0 {
					for i, _ := range newRandomList.Design.Factors {
						newRandomList.Design.Factors[i].ID = primitive.NilObjectID
						if newRandomList.Design.Factors[i].Options != nil && len(newRandomList.Design.Factors[i].Options) > 0 {
							for j, _ := range newRandomList.Design.Factors[i].Options {
								newRandomList.Design.Factors[i].Options[j].IsCopyData = false
							}
							sort.Slice(newRandomList.Design.Factors[i].Options, func(k, j int) bool {
								return newRandomList.Design.Factors[i].Options[k].Value < newRandomList.Design.Factors[i].Options[j].Value
							})
						}
						newRandomList.Design.Factors[i].IsCopyData = false
					}
				}
				sort.Slice(newRandomList.Design.Factors, func(i, j int) bool {
					return newRandomList.Design.Factors[i].Number < newRandomList.Design.Factors[j].Number
				})
				if newRandomList.Design.Combination != nil && len(newRandomList.Design.Combination) > 0 {
					for i, _ := range newRandomList.Design.Combination {
						newRandomList.Design.Combination[i].ID = primitive.NilObjectID
					}
				}
				sort.Slice(newRandomList.Design.Combination, func(i, j int) bool {
					return newRandomList.Design.Combination[i].Number < newRandomList.Design.Combination[j].Number
				})
				newRandomList.Design.BlockNumber = 0
				newRandomList.Config = models.RandomListConfig{}
				newRandomList.Version = 0
				newRandomList.CustomHeaders = []string{}
				newRandomList.Meta = models.Meta{}

				// 比较两个排序后的字符串数组是否相等
				if !reflect.DeepEqual(oldRandomList, newRandomList) {
					return false, nil
				}

				existEnvForm := models.Form{}
				if err = tools.Database.Collection("form").FindOne(ctx, oldEnvCondition).Decode(&existEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
					return false, errors.WithStack(err)
				}

				newEnvForm := models.Form{}
				if err := tools.Database.Collection("form").FindOne(ctx, newEnvCondition).Decode(&newEnvRandomDesign); err != nil && err != mongo.ErrNoDocuments {
					return false, errors.WithStack(err)
				}

				existEnvFactorList := transformationFactors(existEnvRandomDesign)
				newEnvFactorList := transformationFactors(newEnvRandomDesign)

				// 对数组进行排序
				sort.Slice(existEnvFactorList, func(i, j int) bool {
					return existEnvFactorList[i].Number < existEnvFactorList[j].Number
				})
				sort.Slice(newEnvFactorList, func(i, j int) bool {
					return newEnvFactorList[i].Number < newEnvFactorList[j].Number
				})

				existEnvFieldList := transformationFields(existEnvForm)
				newEnvFieldList := transformationFields(newEnvForm)

				// 对数组进行排序
				sort.Slice(existEnvFieldList, func(i, j int) bool {
					return existEnvFieldList[i].Name < existEnvFieldList[j].Name
				})
				sort.Slice(newEnvFieldList, func(i, j int) bool {
					return newEnvFieldList[i].Name < newEnvFieldList[j].Name
				})

				// 比较两个排序后的字符串数组是否相等
				if !reflect.DeepEqual(existEnvFactorList, newEnvFactorList) {
					//fmt.Println("两个字符串数组不相等")
					for _, newEnvFactor := range newEnvFactorList {
						_, b := slice.Find(existEnvFactorList, func(index int, item models.RandomFactor) bool {
							return item.Number == newEnvFactor.Number && item.Name == newEnvFactor.Name &&
								item.Label == newEnvFactor.Label &&
								item.CustomFormulas == newEnvFactor.CustomFormulas && item.Round == newEnvFactor.Round &&
								item.Type == newEnvFactor.Type && item.DateFormat == newEnvFactor.DateFormat &&
								item.Precision == newEnvFactor.Precision && item.IsCalc == newEnvFactor.IsCalc &&
								item.CalcType == newEnvFactor.CalcType && item.Modifiable == newEnvFactor.Modifiable &&
								item.Ratio == newEnvFactor.Ratio &&
								reflect.DeepEqual(item.Options, newEnvFactor.Options)
						})
						if !b {
							filter := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID}
							filter["info"] = bson.M{
								"$elemMatch": bson.M{
									"name": newEnvFactor.Name,
								},
							}
							if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, filter); count > 0 {
								return false, nil
							}
						}
					}

					if !reflect.DeepEqual(existEnvFieldList, newEnvFieldList) {
						for _, newEnvField := range newEnvFieldList {
							_, b := slice.Find(existEnvFieldList, func(index int, item models.Field) bool {
								return item.Name == newEnvField.Name && item.IsCalc == newEnvField.IsCalc &&
									item.CalcType == newEnvField.CalcType && item.Label == newEnvField.Label &&
									item.LabelEn == newEnvField.LabelEn &&
									//item.InputLabel == newEnvField.InputLabel &&
									//item.InputWeightLabel == newEnvField.InputWeightLabel && item.InputHeightLabel == newEnvField.InputHeightLabel &&
									item.Precision == newEnvField.Precision && item.Type == newEnvField.Type &&
									item.Round == newEnvField.Round && item.CustomFormulas == newEnvField.CustomFormulas &&
									item.ApplicationType == newEnvField.ApplicationType && item.Variable == newEnvField.Variable &&
									item.Digit == newEnvField.Digit && item.Accuracy == newEnvField.Accuracy &&
									item.DateFormat == newEnvField.DateFormat && item.FormatType == newEnvField.FormatType &&
									item.TimeFormat == newEnvField.TimeFormat && item.Length == newEnvField.Length &&
									item.Range == newEnvField.Range &&
									reflect.DeepEqual(item.Options, newEnvField.Options)
							})
							if !b {
								filter := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID}
								filter["info"] = bson.M{
									"$elemMatch": bson.M{
										"name": newEnvField.Name,
									},
								}
								if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, filter); count > 0 {
									return false, nil
								}
							}
						}
						return true, nil
					} else {
						return true, nil
					}

				} else {
					if !reflect.DeepEqual(existEnvFieldList, newEnvFieldList) {
						for _, newEnvField := range newEnvFieldList {
							_, b := slice.Find(existEnvFieldList, func(index int, item models.Field) bool {
								return item.Name == newEnvField.Name && item.IsCalc == newEnvField.IsCalc &&
									item.CalcType == newEnvField.CalcType && item.Label == newEnvField.Label &&
									item.LabelEn == newEnvField.LabelEn &&
									//item.InputLabel == newEnvField.InputLabel &&
									//item.InputWeightLabel == newEnvField.InputWeightLabel && item.InputHeightLabel == newEnvField.InputHeightLabel &&
									item.Precision == newEnvField.Precision && item.Type == newEnvField.Type &&
									item.Round == newEnvField.Round && item.CustomFormulas == newEnvField.CustomFormulas &&
									item.ApplicationType == newEnvField.ApplicationType && item.Variable == newEnvField.Variable &&
									item.Digit == newEnvField.Digit && item.Accuracy == newEnvField.Accuracy &&
									item.DateFormat == newEnvField.DateFormat && item.FormatType == newEnvField.FormatType &&
									item.TimeFormat == newEnvField.TimeFormat && item.Length == newEnvField.Length &&
									item.Range == newEnvField.Range &&
									reflect.DeepEqual(item.Options, newEnvField.Options)
							})
							if !b {
								filter := bson.M{"customer_id": newEnvAttribute.CustomerID, "project_id": newEnvAttribute.ProjectID, "env_id": newEnvAttribute.EnvironmentID}
								filter["info"] = bson.M{
									"$elemMatch": bson.M{
										"name": newEnvField.Name,
									},
								}
								if count, _ := tools.Database.Collection("subject").CountDocuments(ctx, filter); count > 0 {
									return false, nil
								}
							}
						}
						return true, nil
					} else {
						return true, nil
					}
				}

			} else {
				return false, nil
			}
		}
	} else {
		return false, nil
	}
}
