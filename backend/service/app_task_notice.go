package service

import (
	"clinflash-irt/database"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"strconv"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type AppTaskNoticeService struct{}

// 查询通知
func (w AppTaskNoticeService) Overview(ctx *gin.Context) (map[string]interface{}, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	filter := bson.M{"user_id": me.ID, "app": true}
	envs := make([]models.UserProjectEnvironment, 0)
	cursor, err := tools.Database.Collection("user_project_environment").Find(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &envs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envIds := slice.Map(envs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.EnvID
	})

	overdueCount := 0       // 逾期通知个数(未读)
	assistantCount := 0     // 项目助手个数(未读)
	visitReminderCount := 0 // 访视提醒个数(未读)

	// 查询任务逾期通知
	var appTaskNoticeOverdue []models.AppTaskNotice
	pipelineOverdue := mongo.Pipeline{
		{{"$match", bson.M{"user_ids.user_id": me.ID, "env_id": bson.M{"$in": envIds}, "notice_type": 0}}},
		{{"$lookup", bson.M{
			"from":         "work_task",
			"localField":   "expire_notice.work_task_id",
			"foreignField": "_id",
			"as":           "work_task",
		}}},
		{{"$match", bson.M{"work_task.info.status": 0}}},
		{{Key: "$sort", Value: bson.D{{"notice_time", -1}}}},
	}
	appTaskNoticeOverdueCursor, err := tools.Database.Collection("app_task_notice").Aggregate(nil, pipelineOverdue)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = appTaskNoticeOverdueCursor.All(nil, &appTaskNoticeOverdue)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	overdueRead := false
	for _, appTaskNoticeOverdue := range appTaskNoticeOverdue {
		overdueRead = false
		for _, atn := range appTaskNoticeOverdue.UserIds {
			if atn.UserID == me.ID {
				overdueRead = atn.Read
				break
			}
		}
		if !overdueRead { // 未读消息
			overdueCount++
		}
	}

	// 查询项目助手
	var appTaskNoticeAssistant []models.AppTaskNotice
	pipelineAssistant := mongo.Pipeline{
		{{"$match", bson.M{"user_ids.user_id": me.ID, "env_id": bson.M{"$in": envIds}, "notice_type": 1}}},
		{{"$lookup", bson.M{
			"from":         "work_task",
			"localField":   "expire_notice.work_task_id",
			"foreignField": "_id",
			"as":           "work_task",
		}}},
		{{"$match", bson.M{"work_task.info.status": 0}}},
		{{Key: "$sort", Value: bson.D{{"notice_time", -1}}}},
	}
	appTaskNoticeAssistantCursor, err := tools.Database.Collection("app_task_notice").Aggregate(nil, pipelineAssistant)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = appTaskNoticeAssistantCursor.All(nil, &appTaskNoticeAssistant)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	assistantRead := false
	for _, appTaskNoticeAssistant := range appTaskNoticeAssistant {
		assistantRead = false
		for _, atn := range appTaskNoticeAssistant.UserIds {
			if atn.UserID == me.ID {
				assistantRead = atn.Read
				break
			}
		}
		if !assistantRead { // 未读消息
			assistantCount++
		}
	}

	// 查询访视提醒
	var appTaskNoticeVisitReminder []models.AppTaskNotice
	pipelineVisitReminder := mongo.Pipeline{
		{{"$match", bson.M{"user_ids.user_id": me.ID, "env_id": bson.M{"$in": envIds}, "notice_type": 2}}},
		{{"$lookup", bson.M{
			"from":         "work_task",
			"localField":   "expire_notice.work_task_id",
			"foreignField": "_id",
			"as":           "work_task",
		}}},
		{{"$match", bson.M{"work_task.info.status": 0}}},
		{{Key: "$sort", Value: bson.D{{"notice_time", -1}}}},
	}
	appTaskNoticeVisitReminderCursor, err := tools.Database.Collection("app_task_notice").Aggregate(nil, pipelineVisitReminder)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = appTaskNoticeVisitReminderCursor.All(nil, &appTaskNoticeVisitReminder)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	visitReminderRead := false
	for _, appTaskNoticeVisitReminder := range appTaskNoticeVisitReminder {
		visitReminderRead = false
		for _, atn := range appTaskNoticeVisitReminder.UserIds {
			if atn.UserID == me.ID {
				visitReminderRead = atn.Read
				break
			}
		}
		if !visitReminderRead { // 未读消息
			visitReminderCount++
		}
	}

	return map[string]interface{}{
		"overdueCount":                 overdueCount,                    // 任务逾期通知(未读个数)
		"overdueIncompleteCount":       len(appTaskNoticeOverdue),       // 任务逾期通知(未完成的个数)
		"overdueAppTaskNotices":        appTaskNoticeOverdue,            // 任务逾期通知(未完成任务状态的数据)
		"assistantCount":               assistantCount,                  // 项目助手通知(未读个数)
		"assistantAppTaskNotices":      appTaskNoticeAssistant,          // 项目助手通知(未完成任务状态的数据)
		"visitReminderCount":           visitReminderCount,              // 访视提醒(未读个数)
		"visitReminderIncompleteCount": len(appTaskNoticeVisitReminder), // 访视提醒(未完成的个数)
		"visitReminderAppTaskNotices":  appTaskNoticeVisitReminder,      // 访视提醒(未完成任务状态的数据)
	}, nil
}

// 查询通知
func (w AppTaskNoticeService) List(ctx *gin.Context) ([]models.WorkTaskView, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	filter := bson.M{"user_id": me.ID, "app": true}
	envs := make([]models.UserProjectEnvironment, 0)
	cursor, err := tools.Database.Collection("user_project_environment").Find(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &envs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envIds := slice.Map(envs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.EnvID
	})
	// 拿到项目ID
	projectIds := slice.Map(envs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.ProjectID
	})
	roleIds := make([]primitive.ObjectID, 0)
	for _, env := range envs {
		roleIds = append(roleIds, env.Roles...)
	}
	roleIds = slice.Unique(roleIds)

	start, _ := strconv.Atoi(ctx.DefaultQuery("start", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "0"))
	noticeType, _ := strconv.Atoi(ctx.DefaultQuery("noticeType", "0"))

	var appTaskNotice []models.AppTaskNotice
	pipeline := mongo.Pipeline{
		{{"$match", bson.M{"user_ids.user_id": me.ID, "env_id": bson.M{"$in": envIds}, "notice_type": int64(noticeType)}}},
		{{"$lookup", bson.M{
			"from":         "work_task",
			"localField":   "expire_notice.work_task_id",
			"foreignField": "_id",
			"as":           "work_task",
		}}},
		{{"$match", bson.M{"work_task.info.status": 0}}},
		{{Key: "$sort", Value: bson.D{{"notice_time", -1}}}},
		{{Key: "$skip", Value: int64(start)}},
		{{Key: "$limit", Value: int64(limit)}},
	}
	appTaskNoticeCursor, err := tools.Database.Collection("app_task_notice").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = appTaskNoticeCursor.All(nil, &appTaskNotice)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目角色权限
	prpFilter := bson.M{"_id": bson.M{"$in": roleIds}}
	projectRolePermissions := make([]models.ProjectRolePermission, 0)
	prpCursor, err := tools.Database.Collection("project_role_permission").Find(nil, prpFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = prpCursor.All(nil, &projectRolePermissions)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询关联中心
	userSiteFilter := bson.M{"user_id": me.ID, "env_id": bson.M{"$in": envIds}}
	userSites := make([]models.UserSite, 0)
	userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, userSiteFilter)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = userSiteCursor.All(nil, &userSites)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询关联仓库
	userDepotFilter := bson.M{"user_id": me.ID, "env_id": bson.M{"$in": envIds}}
	userDepots := make([]models.UserDepot, 0)
	userDepotCursor, err := tools.Database.Collection("user_depot").Find(nil, userDepotFilter)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = userDepotCursor.All(nil, &userDepots)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目
	pFilter := bson.M{"_id": bson.M{"$in": projectIds}}
	projects := make([]models.Project, 0)
	pCursor, err := tools.Database.Collection("project").Find(nil, pFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = pCursor.All(nil, &projects)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var orderIds []primitive.ObjectID
	for _, notice := range appTaskNotice {
		if notice.NoticeType == 0 {
			if notice.ExpireNotice.WorkType == 2 || notice.ExpireNotice.WorkType == 3 || notice.ExpireNotice.WorkType == 6 || notice.ExpireNotice.WorkType == 13 || notice.ExpireNotice.WorkType == 14 || notice.ExpireNotice.WorkType == 15 {
				orderIds = append(orderIds, notice.ExpireNotice.MedicineOrderID)
			}
		} else {
			if notice.ExpireNotice.WorkType == 9 {
				orderIds = append(orderIds, notice.ExpireNotice.MedicineOrderID)
			}
		}
	}
	var medicineOrderList []models.MedicineOrder
	if orderIds != nil && len(orderIds) > 0 {
		medicineOrderCursor, err := tools.Database.Collection("medicine_order").Find(nil, bson.M{"_id": bson.M{"$in": orderIds}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = medicineOrderCursor.All(nil, &medicineOrderList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	var receiveIds []primitive.ObjectID
	for _, ord := range medicineOrderList {
		if ord.Status != 4 && ord.Status != 5 {
			receiveIds = append(receiveIds, ord.ReceiveID)
		}
	}
	// 查询中心
	var projectSiteList []models.ProjectSite
	if receiveIds != nil && len(receiveIds) > 0 {
		projectSiteCursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"_id": bson.M{"$in": receiveIds}})
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		err = projectSiteCursor.All(nil, &projectSiteList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	// 查询项目关联仓库
	var projectStorehouseList []models.ProjectStorehouse
	if receiveIds != nil && len(receiveIds) > 0 {
		projectStorehouseCursor, err := tools.Database.Collection("project_storehouse").Find(nil, bson.M{"_id": bson.M{"$in": receiveIds}})
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		err = projectStorehouseCursor.All(nil, &projectStorehouseList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// 查询任务表
	var taskNoticeIds []primitive.ObjectID
	for _, atn := range appTaskNotice {
		taskNoticeIds = append(taskNoticeIds, atn.ExpireNotice.WorkTaskID)
	}
	var workTaskList []models.WorkTask
	if taskNoticeIds != nil && len(taskNoticeIds) > 0 {
		workTaskCursor, err := tools.Database.Collection("work_task").Find(nil, bson.M{"_id": bson.M{"$in": taskNoticeIds}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = workTaskCursor.All(nil, &workTaskList)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	var filterData []models.WorkTaskView
	for _, notice := range appTaskNotice {
		view := models.AppTaskNotice2View(notice, me.ID, workTaskList)
		if notice.NoticeType == 0 {
			switch notice.ExpireNotice.WorkType {
			case 1: // 扫码入仓
				permissions := []string{"operation.build.medicine.barcode.scan"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					w.getProjectInfo(&view, projects)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 2: // 发送订单确认
				w.getProjectInfo(&view, projects)
				siteIds, deportIds, err := w.getOrderInfo(ctx, &view, medicineOrderList, projectSiteList, projectStorehouseList)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 3: // 研究产品接收
				w.getProjectInfo(&view, projects)
				siteIds, deportIds, err := w.getOrderInfo(ctx, &view, medicineOrderList, projectSiteList, projectStorehouseList)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.receive", "operation.supply.shipment.lose", "operation.supply.shipment.terminated"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 4: // 发药
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getDispensingInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.dispensing"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 5: // 补发药
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getDispensingInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.reissue"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 6: // 订单待运送
				w.getProjectInfo(&view, projects)
				siteIds, deportIds, err := w.getOrderInfo(ctx, &view, medicineOrderList, projectSiteList, projectStorehouseList)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.send", "operation.supply.shipment.lose", "operation.supply.shipment.receive", "operation.supply.shipment.close"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 7: // 创建订单审批
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getSiteInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.approval"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 8: // 紧急揭盲审批
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getSubjectSiteInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.unblinding-approval"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 9: // 访视外发药
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getDispensingInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.out-visit-dispensing"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 10: // pv揭盲
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getSubjectSiteInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.unblinding-pv-approval"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 11: // 发放通知
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getDispensingAppInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.dispensing"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 12: //包装扫码
				permissions := []string{"operation.build.medicine.barcode.scanPackage"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
				if haveAppOperaPermission {
					w.getProjectInfo(&view, projects)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 13: // 发送回收订单待确认任务
				w.getProjectInfo(&view, projects)
				siteIds, deportIds, err := w.getOrderInfo(ctx, &view, medicineOrderList, projectSiteList, projectStorehouseList)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.recovery.cancel", "operation.supply.recovery.determine"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 14: // 发送回收订单待运送
				w.getProjectInfo(&view, projects)
				siteIds, deportIds, err := w.getOrderInfo(ctx, &view, medicineOrderList, projectSiteList, projectStorehouseList)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.recovery.confirm", "operation.supply.recovery.lose", "operation.supply.recovery.receive", "operation.supply.recovery.close"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 15: // 回收订单接收任务
				w.getProjectInfo(&view, projects)
				siteIds, deportIds, err := w.getOrderInfo(ctx, &view, medicineOrderList, projectSiteList, projectStorehouseList)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.recovery.receive", "operation.supply.recovery.lose", "operation.supply.recovery.end"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}

			}

		} else if notice.NoticeType == 1 {
			switch notice.ExpireNotice.WorkType {
			case 9: // 订单超时
				w.getProjectInfo(&view, projects)
				siteIds, deportIds, err := w.getOrderInfo(ctx, &view, medicineOrderList, projectSiteList, projectStorehouseList)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{
						"operation.supply.shipment.send",
						"operation.supply.shipment.lose",
						"operation.supply.shipment.receive",
						"operation.supply.shipment.close",
						"operation.supply.shipment.terminated",
					}
					haveAppOperaPermission := tools.HaveAppOperaPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, permissions)
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b || !haveAppOperaPermission {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 10: // 库房警戒
				w.getProjectInfo(&view, projects)
				depotIds, err := w.getStorehouseInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, []primitive.ObjectID{}, depotIds)
				if haveAppDataPermission {
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			case 11: // 中心警戒
				w.getProjectInfo(&view, projects)
				siteIds, err := w.getProjectSiteInfo(ctx, &view)
				if err != nil {
					return nil, err
				}
				haveAppDataPermission := tools.HaveAppDataPermission(me.ID, notice.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					eP, b := slice.Find(envs, func(index int, item models.UserProjectEnvironment) bool {
						return item.UserID == me.ID && item.EnvID == notice.EnvironmentID
					})
					if b {
						e := *eP
						view.NoPermission = !e.App
					}
					filterData = append(filterData, view)
				}
			}
		} else {
			if notice.ExpireNotice.WorkType == 12 { // 访视提醒
				w.getProjectInfo(&view, projects)
				_, err := w.getVisitReminder(ctx, &view, notice)
				if err != nil {
					return nil, err
				}
				filterData = append(filterData, view)
			}
		}
	}
	return filterData, nil
}

// 修改数据
func (w *AppTaskNoticeService) Update(ctx *gin.Context) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		me, err := tools.Me(ctx)
		var req models.AppTaskNotice
		_ = ctx.ShouldBindBodyWith(&req, binding.JSON)
		filter := bson.M{"notice_type": req.NoticeType, "user_ids.user_id": me.ID}
		setUpdate := bson.M{"$set": bson.M{"user_ids.$.read": true}}
		_, err = tools.Database.Collection("app_task_notice").UpdateMany(nil, filter, setUpdate)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 查询项目信息
func (w *AppTaskNoticeService) getProjectInfo(data *models.WorkTaskView, projects []models.Project) {
	projectID := data.ProjectID
	envID := data.EnvironmentID
	var project models.Project
	for _, p := range projects {
		if p.ID == projectID {
			project = p
		}
	}
	data.ProjectStatus = project.Status
	data.ProjectName = project.Name
	data.ProjectNumber = project.Number
	// 筛选环境
	for _, env := range project.Environments {
		if env.ID == envID {
			data.EnvName = env.Name
			break
		}
	}
}

func (w *AppTaskNoticeService) getOrderInfo(ctx *gin.Context, data *models.WorkTaskView, medicineOrderList []models.MedicineOrder, projectSiteList []models.ProjectSite, projectStorehouseList []models.ProjectStorehouse) ([]primitive.ObjectID, []primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	depotIds := make([]primitive.ObjectID, 0)
	var order models.MedicineOrder
	for _, ord := range medicineOrderList {
		if data.Info.MedicineOrderID == ord.ID {
			order = ord
			break
		}
	}
	if order.Status != 4 && order.Status != 5 {
		data.OrderName = order.OrderNumber
		receiveID := order.ReceiveID
		if !receiveID.IsZero() {
			var projectSite models.ProjectSite
			for _, psl := range projectSiteList {
				if psl.ID == receiveID {
					projectSite = psl
					break
				}
			}
			if !projectSite.ID.IsZero() {
				siteIds = append(siteIds, projectSite.ID)
				data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
			} else {
				var projectStorehouse models.ProjectStorehouse
				for _, psl := range projectStorehouseList {
					if psl.ID == receiveID {
						projectStorehouse = psl
						break
					}
				}
				depotIds = append(depotIds, projectStorehouse.ID)
				var storehouse models.Storehouse
				err := tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
				if err != nil {
					return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
				}
				data.SiteName = storehouse.Number + "-" + storehouse.Name
			}
		} else {
			subjectID := order.SubjectID
			if !subjectID.IsZero() {
				var subject models.Subject
				err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectID}).Decode(&subject)
				if err != nil {
					return []primitive.ObjectID{}, []primitive.ObjectID{}, errors.WithStack(err)
				}
				var projectSite models.ProjectSite
				for _, psl := range projectSiteList {
					if psl.ID == subject.ProjectSiteID {
						projectSite = psl
						break
					}
				}
				if !projectSite.ID.IsZero() {
					siteIds = append(siteIds, projectSite.ID)
				}

				var subjectNo string
				for _, sbjInfo := range subject.Info {
					if sbjInfo.Name == "shortname" {
						subjectNo = sbjInfo.Value.(string)
						break
					}
				}
				data.SiteName = subjectNo
			}
		}
	}
	return siteIds, depotIds, nil
}

func (w *AppTaskNoticeService) getDispensingInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	dispensingId := data.Info.DispensingID
	var dispensing models.Dispensing
	err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingId}).Decode(&dispensing)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	if dispensing.DispensingMedicines != nil {
		data.VisitNumber = dispensing.VisitInfo.Number
		var subject models.Subject
		err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
		if err != nil {
			return []primitive.ObjectID{}, errors.WithStack(err)
		}
		var subjectName string
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				subjectName = info.Value.(string)
				break
			}
		}
		data.SubjectName = subjectName
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return []primitive.ObjectID{}, errors.WithStack(err)
		}
		siteIds = append(siteIds, projectSite.ID)
		data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
	}
	return siteIds, nil
}

func (w *AppTaskNoticeService) getSiteInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	approvalProcessID := data.Info.ApprovalProcessID
	orderTaskFilter := bson.M{"_id": approvalProcessID}
	var orderAddTask models.OrderAddTask
	err := tools.Database.Collection("approval_process").FindOne(ctx, orderTaskFilter).Decode(&orderAddTask)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	projectSiteFilter := bson.M{"_id": orderAddTask.Data.ReceiveID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(ctx, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
	return siteIds, nil
}

func (w *AppTaskNoticeService) getSubjectSiteInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	subjectID := data.Info.SubjectApproval.SubjectID
	if data.Info.WorkType == 10 {
		subjectID = data.Info.SubjectPvApproval.SubjectID
	}
	subjectFilter := bson.M{"_id": subjectID}
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(ctx, subjectFilter).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	projectSiteFilter := bson.M{"_id": subject.ProjectSiteID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(ctx, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
	return siteIds, nil
}

func (w *AppTaskNoticeService) getDispensingAppInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	dispensingId := data.Info.DispensingID
	var dispensing models.Dispensing
	err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingId}).Decode(&dispensing)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	if dispensing.VisitInfo.VisitCycleInfoID != primitive.NilObjectID {
		data.VisitCycleId = dispensing.VisitInfo.VisitCycleInfoID.Hex()
	}

	data.VisitNumber = dispensing.VisitInfo.Number
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	var subjectName string
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectName = info.Value.(string)
			break
		}
	}

	//待发药--插入任务表中
	match := bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID}
	if dispensing.CohortID != primitive.NilObjectID {
		match = bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}
	}
	var visit models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visit)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	filter := bson.M{"subject_id": dispensing.SubjectID}
	var resDispensing []models.ResDispensing
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_order",
			"let": bson.M{
				"dispensing_id": "$_id",
				//"order_number":  "$order",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
			},

			"as": "medicine_order",
		}}},
		{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &resDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	visitCycleMap := map[string]models.VisitCycleInfo{}
	for _, info := range visit.Infos {
		visitCycleMap[info.Number] = info
	}
	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return nil, err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	lastTime := time.Duration(0)
	afterRandom := false
	interval := float64(0)
	var period models.Period

	// TODO 7064 7101
	randomTime := subject.RandomTime
	subjectMap := make(map[string]models.Subject)
	if !visit.BaseCohort.IsZero() && visit.BaseCohort != subject.CohortID {
		subjectMap, err = task.GetBaseCohortSubjectMap(nil, visit.BaseCohort, primitive.NilObjectID, subject.Info[0].Value.(string))
	}
	if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
		randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
	}
	firstTime := time.Duration(0)

	for _, res := range resDispensing {
		if !res.VisitSign {

			if firstTime == 0 && res.DispensingTime != 0 && visitCycleMap[res.VisitInfo.Number].Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(res.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[res.VisitInfo.Number].Interval)).Unix())
				firstTime = res.DispensingTime
			}

			if res.Status == 2 {
				interval = 0
				lastTime = res.DispensingTime
			}
			if res.VisitInfo.Random {
				afterRandom = true
			}
			if res.ID == dispensing.ID {
				period = handlePeriod(afterRandom, visit.VisitType, visitCycleMap[dispensing.VisitInfo.Number], randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
			}
		}
	}
	// 不展示随机号
	if !attribute.AttributeInfo.IsRandomNumber {
		data.RandomNumber = tools.BlindData
	} else {
		data.RandomNumber = subject.RandomNumber
	}
	data.SubjectName = subjectName
	data.SubjectId = dispensing.SubjectID.Hex()
	data.PlanLeftTime = ""
	if len(period.MinPeriod) != 0 {
		data.PlanLeftTime = timestampConversion(period.MinPeriod)
	}
	data.PlanRightTime = ""
	if len(period.MaxPeriod) != 0 {
		data.PlanRightTime = timestampConversion(period.MaxPeriod)
		if data.Info.WorkType == 11 {
			data.Info.Deadline = time.Duration(period.MaximumTime)
		}
	}

	if subject.RandomTime != 0 {
		//timeZone := time.Duration(intTimeZone)
		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)

		duration := hours*time.Hour + minutes*time.Minute
		formattedDate := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006.01.02")
		if data.PlanLeftTime == "" {
			data.PlanLeftTime = formattedDate
		}
		if data.PlanRightTime == "" {
			data.PlanRightTime = formattedDate
			if data.Info.WorkType == 11 {
				data.Info.Deadline = subject.RandomTime
			}
		}
	}

	//if data.PlanRightTime != "" {
	//	data.Info.Deadline = formattedDate
	//}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)

	return siteIds, nil
}

func (w *AppTaskNoticeService) getUnblindingApproval(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	subjectApproval := data.Info.SubjectApproval
	// 查询受试者
	subjectIdFilter := bson.M{"_id": subjectApproval.SubjectID}
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, subjectIdFilter).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	// 查询中心
	projectSiteFilter := bson.M{"_id": subject.ProjectSiteID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = models.GetProjectSiteName(ctx, projectSite)

	// 查询项目属性ID
	attributeFilter := bson.M{
		"customer_id": subject.CustomerID,
		"project_id":  subject.ProjectID,
		"env_id":      subject.EnvironmentID,
	}
	if !subject.CohortID.IsZero() {
		attributeFilter["cohort_id"] = subject.CohortID
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	data.AttributeId = attribute.ID
	return siteIds, nil
}

// 查询库房
func (w *AppTaskNoticeService) getStorehouseInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	depotIds := make([]primitive.ObjectID, 0)
	projectStorehouseId := data.Info.ProjectStorehouseID
	var projectStorehouse models.ProjectStorehouse
	err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": projectStorehouseId}).Decode(&projectStorehouse)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	depotIds = append(depotIds, projectStorehouse.ID)
	var storehouse models.Storehouse
	err = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	data.SiteName = storehouse.Number + "-" + storehouse.Name
	return depotIds, nil
}

// 查询中心
func (w *AppTaskNoticeService) getProjectSiteInfo(ctx *gin.Context, data *models.WorkTaskView) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	projectSiteID := data.Info.ProjectSiteID
	projectSiteFilter := bson.M{"_id": projectSiteID}
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(ctx, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SiteName = projectSite.Number + "-" + models.GetProjectSiteName(ctx, projectSite)
	return siteIds, nil
}

// 查询访视提醒
func (w *AppTaskNoticeService) getVisitReminder(ctx *gin.Context, data *models.WorkTaskView, appTaskNotice models.AppTaskNotice) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	// 查询发药信息
	dispensingId := data.Info.DispensingID
	var dispensing models.Dispensing
	err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingId}).Decode(&dispensing)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}

	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	var subjectName string
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectName = info.Value.(string)
			break
		}
	}

	// 查询中心
	projectSiteFilter := bson.M{"_id": subject.ProjectSiteID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	data.SubjectName = subjectName
	data.SiteName = models.GetProjectSiteName(ctx, projectSite)
	if dispensing.VisitInfo.VisitCycleInfoID != primitive.NilObjectID {
		data.VisitCycleId = dispensing.VisitInfo.VisitCycleInfoID.Hex()
	}
	data.VisitNumber = dispensing.VisitInfo.Number
	data.VisitReminderContent = appTaskNotice.ExpireNotice.VisitReminderContent
	data.VisitReminderContentEn = appTaskNotice.ExpireNotice.VisitReminderContentEn // 访视提醒内容英文
	data.Info.WorkType = 11                                                         // 访视提醒的具体任务是药物发放，app要求改为11方便页面跳转
	data.SubjectId = subject.ID.Hex()
	data.RandomNumber = subject.RandomNumber
	// 查询 PlanLeftTime 和 PlanRightTime
	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return nil, err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	match := bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID}
	if dispensing.CohortID != primitive.NilObjectID {
		match = bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}
	}
	var visit models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visit)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	visitCycleMap := map[string]models.VisitCycleInfo{}
	for _, info := range visit.Infos {
		visitCycleMap[info.Number] = info
	}

	filter := bson.M{"subject_id": dispensing.SubjectID}
	var resDispensing []models.ResDispensing
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_order",
			"let": bson.M{
				"dispensing_id": "$_id",
				//"order_number":  "$order",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
			},

			"as": "medicine_order",
		}}},
		{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &resDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	attribute, err := database.GetAttributeWithEnvCohortID(nil, dispensing.EnvironmentID, dispensing.CohortID)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	lastTime := time.Duration(0)
	afterRandom := false
	interval := float64(0)
	var period models.Period
	isolationProject, baseCohortOID := tools.IsolationProject(models.Project{}, subject.EnvironmentID)
	subjectMap := make(map[string]models.Subject)
	if isolationProject {
		subjectMap, err = task.GetBaseCohortSubjectMap(nil, baseCohortOID, primitive.NilObjectID, subject.Info[0].Value.(string))
	}

	for _, res := range resDispensing {
		if !res.VisitSign {
			if res.Status == 2 {
				interval = 0
				lastTime = res.DispensingTime
			}
			if res.VisitInfo.Random {
				afterRandom = true
			}
			if res.ID == dispensing.ID {
				// TODO 7064
				randomTime := subject.RandomTime
				if isolationProject && subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
					subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
				}
				period = handlePeriod(afterRandom, visit.VisitType, visitCycleMap[dispensing.VisitInfo.Number], randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
			}
		}
	}

	data.PlanLeftTime = ""
	if len(period.MinPeriod) != 0 {
		data.PlanLeftTime = timestampConversion(period.MinPeriod)
	}
	data.PlanRightTime = ""
	if len(period.MaxPeriod) != 0 {
		data.PlanRightTime = timestampConversion(period.MaxPeriod)
	}
	if subject.RandomTime != 0 {
		//timeZone := time.Duration(intTimeZone)
		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)

		duration := hours*time.Hour + minutes*time.Minute
		formattedDate := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006.01.02")
		if data.PlanLeftTime == "" {
			data.PlanLeftTime = formattedDate
		}
		if data.PlanRightTime == "" {
			data.PlanRightTime = formattedDate
		}
	}
	return siteIds, nil
}
