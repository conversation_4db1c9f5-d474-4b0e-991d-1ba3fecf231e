package service

import (
	"clinflash-irt/tools"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"clinflash-irt/models"
)

type BarcodeLabelService struct{}

// AddBarcodeLabel 创建条形码标签
func (s *BarcodeLabelService) AddBarcodeLabel(ctx *gin.Context, req models.BarcodeLabelRequestVo) (bool, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return false, errors.WithStack(err)
	}
	_customerId, _ := primitive.ObjectIDFromHex(req.CustomerID)
	_projectId, _ := primitive.ObjectIDFromHex(req.ProjectID)
	_envId, _ := primitive.ObjectIDFromHex(req.EnvID)
	_cohortId, _ := primitive.ObjectIDFromHex(req.CohortID)
	_correlationId, _ := primitive.ObjectIDFromHex(req.CorrelationID)

	// 验证条形码任务是否存在
	if req.CorrelationName != "" {
		var task models.BarcodeGroup
		taskFilter := bson.M{"_id": _correlationId}
		err = tools.Database.Collection("barcode_group").FindOne(ctx, taskFilter).Decode(&task)
		if err != nil {
			return false, errors.WithStack(err)
		}
	}
	// 生成标签编号
	labelNumber, err := s.generateLabelNumber(ctx, _customerId, _projectId, _envId, _cohortId)
	if err != nil {
		return false, errors.WithStack(err)
	}
	// 创建条形码标签
	now := time.Duration(time.Now().Unix())
	label := &models.BarcodeLabel{
		ID:              primitive.NewObjectID(),
		LabelNumber:     labelNumber,
		CorrelationID:   _correlationId,
		CorrelationName: req.CorrelationName,
		CodeMethod:      req.CodeMethod,
		ProductIds:      req.ProductIds,
		CustomerID:      _customerId,
		ProjectID:       _projectId,
		EnvID:           _envId,
		CohortID:        _cohortId,
		Template:        req.Template,
		Status:          1,
		Meta: models.Meta{
			CreatedAt: now,
			UpdatedAt: now,
			CreatedBy: me.ID,
			UpdatedBy: me.ID,
		},
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		_, err = tools.Database.Collection("barcode_label").InsertOne(sctx, label)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = insertBarcodeLabel(ctx, sctx, models.BarcodeLabel{}, *label, _envId, 1)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//更新研究产品的状态
		updateProductIds := make([]primitive.ObjectID, len(req.ProductIds))
		for index, id := range req.ProductIds {
			productId, _ := primitive.ObjectIDFromHex(id)
			updateProductIds[index] = productId
		}
		medicineFilter := bson.M{"_id": bson.M{"$in": updateProductIds}}
		medicineUpdate := bson.M{
			"$set": bson.M{
				"label_status": 1,
			},
		}
		if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, medicineFilter, medicineUpdate); err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return true, nil

}

// GetBarcodeLabelByID 根据ID获取条形码标签
func (s *BarcodeLabelService) GetBarcodeLabelByID(ctx *gin.Context, id primitive.ObjectID, req models.BarcodeLabelRequestVo) (*models.BarcodeLabel, error) {
	_customerId, _ := primitive.ObjectIDFromHex(req.CustomerID)
	_projectId, _ := primitive.ObjectIDFromHex(req.ProjectID)
	_envId, _ := primitive.ObjectIDFromHex(req.EnvID)
	_cohortId, _ := primitive.ObjectIDFromHex(req.CohortID)
	filter := bson.M{
		"_id":         id,
		"customer_id": _customerId,
		"project_id":  _projectId,
		"env_id":      _envId,
		"cohort_id":   _cohortId,
		"status":      1,
	}
	var barcodeLabel models.BarcodeLabel
	err := tools.Database.Collection("barcode_label").FindOne(ctx, filter).Decode(&barcodeLabel)
	if err != nil {
		return nil, err
	}
	return &barcodeLabel, nil
}

// GetPreviewBarcodeLabel 预览数据
func (s *BarcodeLabelService) GetPreviewBarcodeLabel(ctx *gin.Context, id primitive.ObjectID, req models.BarcodeLabelRequestVo) (*models.BarcodeLabelPreview, error) {
	_customerId, _ := primitive.ObjectIDFromHex(req.CustomerID)
	_projectId, _ := primitive.ObjectIDFromHex(req.ProjectID)
	_envId, _ := primitive.ObjectIDFromHex(req.EnvID)
	_cohortId, _ := primitive.ObjectIDFromHex(req.CohortID)
	filter := bson.M{
		"_id":         id,
		"customer_id": _customerId,
		"project_id":  _projectId,
		"env_id":      _envId,
		"cohort_id":   _cohortId,
		"status":      1,
	}
	var barcodeLabel models.BarcodeLabelPreview
	err := tools.Database.Collection("barcode_label").FindOne(ctx, filter).Decode(&barcodeLabel)
	if err != nil {
		return nil, err
	}
	// 获得关联的产品信息
	var productList []map[string]interface{}
	_productId := slice.Map(barcodeLabel.ProductIds, func(index int, item string) primitive.ObjectID {
		_pid, _ := primitive.ObjectIDFromHex(item)
		return _pid
	})
	project := bson.M{"_id": 1, "name": 1, "status": 1, "batch_number": 1, "expiration_date": 1, "number": 1}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": _productId}}}},
		{{Key: "$project", Value: project}},
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(ctx, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &productList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	barcodeLabel.ProductList = productList
	return &barcodeLabel, nil
}

// SendBarcodeLabel 发送
func (s *BarcodeLabelService) SendBarcodeLabel(ctx *gin.Context, id primitive.ObjectID, req models.BarcodeLabelRequestVo) (bool, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return false, errors.WithStack(err)
	}
	_customerId, _ := primitive.ObjectIDFromHex(req.CustomerID)
	_projectId, _ := primitive.ObjectIDFromHex(req.ProjectID)
	_envId, _ := primitive.ObjectIDFromHex(req.EnvID)
	_cohortId, _ := primitive.ObjectIDFromHex(req.CohortID)
	filter := bson.M{
		"_id":         id,
		"customer_id": _customerId,
		"project_id":  _projectId,
		"env_id":      _envId,
		"cohort_id":   _cohortId,
		"status":      1,
	}
	// 发送
	now := time.Duration(time.Now().Unix())
	update := bson.M{
		"send_meta.send_at": now,
		"send_meta.send_by": me.ID,
		"send_meta.status":  1,
	}
	// 执行更新
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var barcodeLabel models.BarcodeLabel
		err = tools.Database.Collection("barcode_label").FindOne(sctx, filter).Decode(&barcodeLabel)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		_, err = tools.Database.Collection("barcode_label").UpdateOne(sctx, filter, bson.M{"$set": update})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.barcode_label",
			Value: barcodeLabel.LabelNumber,
			Blind: false,
		})

		var OperationLogFieldGroups []models.OperationLogFieldGroup
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.barcode_label",
			TranKey: "operation_log.barcode_label.send_success",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: []interface{}{"1"},
			},
		})
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.barcode_label", _envId, 4, OperationLogFieldGroups, marks, _envId)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return true, nil
}

// UpdateBarcodeLabel 更新条形码标签
func (s *BarcodeLabelService) UpdateBarcodeLabel(ctx *gin.Context, id primitive.ObjectID, req models.BarcodeLabelRequestVo) (bool, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return false, errors.WithStack(err)
	}
	_correlationId, _ := primitive.ObjectIDFromHex(req.CorrelationID)
	// 更新字段
	now := time.Duration(time.Now().Unix())
	update := bson.M{
		"correlation_id":   _correlationId,
		"correlation_name": req.CorrelationName,
		"product_ids":      req.ProductIds,
		"code_method":      req.CodeMethod,
		"template":         req.Template,
		"meta.updated_at":  now,
		"meta.updated_by":  me.ID,
	}
	// 执行更新
	filter := bson.M{"_id": id}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var oldLabel models.BarcodeLabel
		err = tools.Database.Collection("barcode_label").FindOne(sctx, filter).Decode(&oldLabel)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var newLabel models.BarcodeLabel

		err = tools.Database.Collection("barcode_label").FindOneAndUpdate(sctx, filter, bson.M{"$set": update}).Decode(&newLabel)
		if err != nil {
			return nil, errors.WithStack(err)

		}
		_envId, _ := primitive.ObjectIDFromHex(req.EnvID)
		err = insertBarcodeLabel(ctx, sctx, oldLabel, newLabel, _envId, 2)
		if err != nil {
			return nil, errors.WithStack(err)

		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return true, nil
}

// DeleteBarcodeLabel 删除条形码标签
func (s *BarcodeLabelService) DeleteBarcodeLabel(ctx *gin.Context, id primitive.ObjectID, req models.BarcodeLabelRequestVo) (bool, error) {
	me, err := tools.Me(ctx)
	if err != nil {
		return false, errors.WithStack(err)
	}
	now := time.Duration(time.Now().Unix())
	filter := bson.M{"_id": id, "status": 1}
	update := bson.M{
		"status":          2,
		"meta.deleted_at": now,
		"meta.deleted_by": me.ID,
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		_, err = tools.Database.Collection("barcode_label").UpdateOne(sctx, filter, bson.M{"$set": update})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_envId, _ := primitive.ObjectIDFromHex(req.EnvID)
		err = insertBarcodeLabel(ctx, sctx, models.BarcodeLabel{}, models.BarcodeLabel{}, _envId, 3)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return true, nil
}

// GetBarcodeLabelList 获取条形码标签列表
func (s *BarcodeLabelService) GetBarcodeLabelList(ctx *gin.Context, req models.BarcodeLabelListRequestVo) (*models.BarcodeLabelListResponse, error) {
	_customerId, _ := primitive.ObjectIDFromHex(req.CustomerID)
	_projectId, _ := primitive.ObjectIDFromHex(req.ProjectID)
	_envId, _ := primitive.ObjectIDFromHex(req.EnvID)
	_cohortId, _ := primitive.ObjectIDFromHex(req.CohortID)
	_correlationId, _ := primitive.ObjectIDFromHex(req.CorrelationID)
	// 构建查询条件
	filter := bson.M{
		"customer_id": _customerId,
		"project_id":  _projectId,
		"env_id":      _envId,
		"cohort_id":   _cohortId,
		"status":      1,
	}
	// 添加模糊查询条件
	if req.LabelNumber != "" {
		filter["label_number"] = bson.M{"$regex": req.LabelNumber, "$options": "i"}
	}
	if _correlationId != primitive.NilObjectID {
		filter["correlation_id"] = req.CorrelationID
	}
	if req.CorrelationName != "" {
		filter["correlation_name"] = bson.M{"$regex": req.CorrelationName, "$options": "i"}
	}
	// 构建聚合管道
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		// 添加计算字段
		{{Key: "$addFields", Value: bson.M{
			"product_count": bson.M{"$size": bson.M{"$ifNull": []interface{}{"$product_ids", []interface{}{}}}},
		}}},
		{{Key: "$project", Value: bson.M{"template": 0, "product_ids": 0}}},
		{{Key: "$sort", Value: bson.D{{"meta.creatTime", -1}}}},
		{{Key: "$skip", Value: req.Skip}},
		{{Key: "$limit", Value: req.Limit}},
	}
	cursor, err := tools.Database.Collection("barcode_label").Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	var results []models.BarcodeLabelRespVo
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}
	total, err := tools.Database.Collection("barcode_label").CountDocuments(ctx, filter)
	if err != nil {
		return nil, err
	}
	return &models.BarcodeLabelListResponse{
		Data:  results,
		Total: total,
	}, nil
}

// generateLabelNumber 生成L开头的标签编号
func (s *BarcodeLabelService) generateLabelNumber(ctx context.Context, customerID, projectID, envID, cohortID primitive.ObjectID) (string, error) {
	// 查询当前项目下最大的标签编号
	filter := bson.M{
		"customer_id": customerID,
		"project_id":  projectID,
		"env_id":      envID,
		"cohort_id":   cohortID,
		"status":      1,
	}

	opts := options.FindOne().SetSort(bson.D{{Key: "label_number", Value: -1}})
	var lastLabel models.BarcodeLabel
	err := tools.Database.Collection("barcode_label").FindOne(ctx, filter, opts).Decode(&lastLabel)

	var nextNumber = 1
	if err == nil && lastLabel.LabelNumber != "" {
		// 提取数字部分
		numberStr := strings.TrimPrefix(lastLabel.LabelNumber, "L")
		if num, parseErr := strconv.Atoi(numberStr); parseErr == nil {
			nextNumber = num + 1
		}
	}
	return fmt.Sprintf("L%06d", nextNumber), nil
}
