package service

import (
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type CrossCheckService struct{}

func (s *CrossCheckService) Layer(ctx *gin.Context) (map[string]interface{}, error) {
	data, err := task.LayerCheckTask(false)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s *CrossCheckService) ReRandomGroupMatch(ctx *gin.Context) (map[string]interface{}, error) {
	data, err := task.CheckReRandom(false)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// Dispensing ...
func (s *CrossCheckService) Dispensing(ctx *gin.Context, data map[string]interface{}) (interface{}, error) {
	envOID, _ := primitive.ObjectIDFromHex(data["env"].(string))

	var project models.Project
	tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)

	startTime := time.Now().Add(-time.Hour * 24 * 10).Unix()

	endTime := time.Now().Unix()
	if data["time"] != nil && len(data["time"].([]interface{})) == 2 {
		betweenTime := data["time"].([]interface{})
		startTime = int64(int(betweenTime[0].(float64)))
		endTime = int64(int(betweenTime[1].(float64)))
	}

	limit := 20
	skip := 0
	if data["limit"] != nil {
		limit = int(data["limit"].(float64))
	}
	if data["start"] != nil {
		skip = int(data["start"].(float64))
	}
	cohortOID := primitive.NilObjectID
	match := bson.M{"deleted": bson.M{"$ne": true}}
	match["env_id"] = envOID
	if data["cohort"] != nil {
		cohortOID, _ = primitive.ObjectIDFromHex(data["cohort"].(string))
		match["cohort_id"] = cohortOID

	}
	var sitesOIDs []primitive.ObjectID
	if data["site"] != nil {
		for _, site := range data["site"].([]interface{}) {
			siteOID, _ := primitive.ObjectIDFromHex(site.(string))
			sitesOIDs = append(sitesOIDs, siteOID)
		}
	}
	if sitesOIDs != nil {
		match["project_site_id"] = bson.M{"$in": sitesOIDs}
	}

	if data["subject"] != nil && data["subject"] != "" {
		match["info.value"] = bson.M{"$regex": data["subject"]}
	}

	match["random_time"] = bson.M{"$gt": startTime, "$lt": endTime}
	count, errCount, err := dispensingErrorCount(match)
	if err != nil {
		return nil, err
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		{{Key: "$match", Value: bson.M{"dispensing.status": 2}}}, //"dispensing_time":bson.M{"$gt":startTime, "$lt":endTime}

		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		// 关联中心配置表 获取时区
		{{Key: "$lookup", Value: bson.M{
			"from": "sites_config",
			"let": bson.M{
				"name": bson.M{"$first": "$project_site.name"},
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$eq": bson.A{"$cn", "$$name"}}},
							bson.M{"$expr": bson.M{"$eq": bson.A{"$en", "$$name"}}},
						},
					},
				},
			},
			"as": "sites_config",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$sites_config", "preserveNullAndEmptyArrays": true}}},

		//// 关联药物表，匹配项目下的编号
		{{Key: "$lookup", Value: bson.M{
			"from":         "medicine",
			"localField":   "dispensing.dispensing_medicines.medicine_id",
			"foreignField": "_id",
			"as":           "medicine",
		}}},

		{{Key: "$lookup", Value: bson.M{
			"from": "drug_configure",
			"let": bson.M{
				"visit_cycle_info_id": "$dispensing.visit_info.visit_cycle_info_id",
			},

			"pipeline": bson.A{
				bson.M{
					"$unwind": "$configures",
				},
				bson.M{
					"$unwind": "$configures.visit_cycles",
				},
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$configures.visit_cycles", "$$visit_cycle_info_id"}}},
				},
			},
			"as": "drug_configure",
		}}},

		{{Key: "$project", Value: bson.M{
			"id":              "$dispensing._id",
			"site_number":     bson.M{"$first": "$project_site.number"},
			"site_name":       bson.M{"$first": "$project_site.name"},
			"subject":         "$info.value",
			"visit":           "$dispensing.visit_info.name",
			"random_group":    "$group",
			"medicine":        "$dispensing.dispensing_medicines",
			"other_medicine":  "$dispensing.other_dispensing_medicines",
			"serial_number":   "$dispensing.serial_number",
			"medicine_true":   "$medicine",
			"utc":             "$sites_config.timeZone",
			"drug_configure":  "$drug_configure.configures",
			"dispensing_time": "$dispensing.dispensing_time",
			"reissue":         "$dispensing.reissue",
			"visit_sign":      "$dispensing.visit_sign",
		}}},
		////
		{{Key: "$sort", Value: bson.D{{"site_number", 1}, {"subject", 1}, {"serial_number", 1}}}},
		//
		//
		{{Key: "$skip", Value: skip}},
		{{Key: "$limit", Value: limit}},
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var res []models.DispensingCheckMongo
	err = cursor.All(nil, &res)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var dispensingChecks []models.DispensingCheck
	for _, item := range res {

		// 根据配置匹配是否符合组别
		var medicine []string
		var medicineTrue []string
		for _, dispensingMedicine := range item.MedicineTrue {
			medicineTrue = append(medicineTrue, fmt.Sprintf("%s-%s", dispensingMedicine.Number, dispensingMedicine.Name))
		}

		medicineHash := map[string]interface{}{}
		for _, dispensingMedicine := range item.Medicine {
			if medicineHash[dispensingMedicine.Name] == nil {
				medicineHash[dispensingMedicine.Name] = 1
			} else {
				medicineHash[dispensingMedicine.Name] = medicineHash[dispensingMedicine.Name].(int) + 1
			}
			medicine = append(medicine, fmt.Sprintf("%s-%s", dispensingMedicine.Number, dispensingMedicine.Name))
		}
		for _, dispensingMedicine := range item.OtherMedicine {
			medicineHash[dispensingMedicine.Name] = dispensingMedicine.Count
			medicine = append(medicine, fmt.Sprintf("%s(%d)", dispensingMedicine.Name, dispensingMedicine.Count))
			medicineTrue = append(medicineTrue, fmt.Sprintf("%s(%d)", dispensingMedicine.Name, dispensingMedicine.Count))
		}

		configMedicines := make([]map[string]interface{}, 0)
		for _, configure := range item.DrugConfigure {
			configMedicine := map[string]interface{}{}
			length := 0

			for _, value := range configure.Values {
				configMedicine[value.DrugName] = value.DispensingNumber
				configMedicine["group"] = configure.Group
				length += 1
			}
			configMedicine["len"] = length
			configMedicines = append(configMedicines, configMedicine)
		}

		//allMatch := false
		group := ""
		for _, config := range configMedicines {
			matchConfig := true
			length := 0
			for k, v := range config {
				if k == "group" || k == "len" {
					continue
				}
				if medicineHash[k] != v {
					matchConfig = false
					break
				}
				length++
			}
			if config["len"] == length && matchConfig {
				//allMatch = true
				if config["group"] == item.RandomGroup {
					group = config["group"].(string)
					break
				} else {
					if group != "N/A" {
						group = config["group"].(string)
					}
				}

			}
		}

		// 整合数据
		var dispensingCheck models.DispensingCheck

		dispensingCheck.ID = item.ID
		dispensingCheck.Site = item.SiteNumber + "-" + item.SiteName
		dispensingCheck.Subject = item.Subject
		dispensingCheck.RandomGroup = item.RandomGroup
		dispensingCheck.Visit = item.Visit
		dispensingCheck.Medicine = medicine
		dispensingCheck.MedicineGroup = group
		dispensingCheck.MedicineTrue = medicineTrue

		if item.VisitSign {
			dispensingCheck.Type = 1
		}

		if item.Reissue == 1 {
			dispensingCheck.Type = 2
		}

		timeZone, err := tools.GetProjectLocationUtc(project.TimeZoneStr, project.Tz, item.UTC)
		if err != nil {
			return models.RemoteSubjectDispensing{}, errors.WithStack(err)
		}
		//dTime := time.Unix(item.DispensingTime, 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
		dTime := tools.FormatFloatTime(project.TimeZoneStr, project.Tz, item.UTC, item.DispensingTime, "2006-01-02 15:04:05")
		dispensingCheck.DispensingTime = dTime
		dispensingCheck.UTC = timeZone
		medicineCheck := true
		if dispensingCheck.MedicineGroup != "N/A" && dispensingCheck.MedicineGroup != dispensingCheck.RandomGroup {
			medicineCheck = false
		}
		medicineTrueLen := len(medicineTrue)
		medicineLen := 0
		for _, medicineItem := range dispensingCheck.Medicine {
			for _, medicineTrueItem := range dispensingCheck.MedicineTrue {
				if medicineItem == medicineTrueItem {
					medicineLen++
				}
			}
		}
		if medicineTrueLen != medicineLen {
			medicineCheck = false
		}
		dispensingCheck.Check = medicineCheck

		dispensingChecks = append(dispensingChecks, dispensingCheck)

	}

	response := map[string]interface{}{
		"total":      count,
		"errorCount": errCount,
		"item":       dispensingChecks,
	}

	return response, nil
}

func (s *CrossCheckService) ListProjectsSite(ctx *gin.Context) ([]map[string]interface{}, error) {
	var data []map[string]interface{}
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}

	// 如果用户不在全局配置，或者项目配置中 放回空数据
	counts, _ := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "crossCheckAll", "$or": bson.A{bson.M{"data.mail": user.Email}, bson.M{"data.project_mail": user.Email}}})
	if counts == 0 {
		return []map[string]interface{}{}, nil
	}
	// 当前用户为配置上的用户，则返回所有prod环境数据
	count, _ := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "crossCheckAll", "data.mail": user.Email})
	if count == 0 {
		// 查询当前用户下，所有配置 系统监控管理的项目环境
		cursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"user_id": user.ID}}},
			// 关联 role表 匹配 用户配置 IP Officer IRT Designer 的项目环境
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_role_permission",
				"localField":   "roles",
				"foreignField": "_id",
				"as":           "role",
			}}},
			{{Key: "$match", Value: bson.M{"role.permissions": "operation.monitor.edit"}}},
			// 关联项目具体env
			{{Key: "$lookup", Value: bson.M{
				"from": "project",
				"let": bson.M{
					"env_id": "$env_id",
				},
				"pipeline": bson.A{
					bson.M{"$unwind": "$envs"},
					bson.M{
						"$match": bson.M{"envs.name": "PROD"},
					},
					bson.M{
						"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$envs.id", "$$env_id"}}},
					},
				},
				"as": "project",
			}}},
			{{Key: "$unwind", Value: "$project"}},
			{{Key: "$lookup", Value: bson.M{
				"from": "project_site",
				"let": bson.M{
					"env_id": "$env_id",
				},
				"pipeline": bson.A{
					bson.M{
						"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}},
					},
					bson.M{
						"$match": bson.M{"deleted": 2},
					},
					bson.M{"$project": bson.M{
						"_id":    1,
						"number": 1,
						"name":   models.ProjectSiteNameBson(ctx),
					}},
				},
				"as": "project_site",
			}}},
			{{Key: "$project", Value: bson.M{
				"customer_id": 1,
				"env_id":      1,
				"project_id":  1,
				"cohorts":     "$project.envs.cohorts",
				"number":      "$project.info.number",
				"name":        "$project.info.name",
				"site":        "$project_site",
			}}},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		cursor, err := tools.Database.Collection("project").Aggregate(nil, mongo.Pipeline{
			{{Key: "$unwind", Value: "$envs"}},
			{{Key: "$match", Value: bson.M{"envs.name": "PROD"}}},
			{{Key: "$lookup", Value: bson.M{
				"from": "project_site",
				"let": bson.M{
					"env_id": "$envs.id",
				},
				"pipeline": bson.A{
					bson.M{
						"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}},
					},
					bson.M{
						"$match": bson.M{"deleted": 2},
					},
					bson.M{"$project": bson.M{
						"_id":    1,
						"number": 1,
						"name":   models.ProjectSiteNameBson(ctx),
					}},
				},

				"as": "project_site",
			}}},

			{{Key: "$project", Value: bson.M{
				"customer_id": 1,
				"env_id":      "$envs.id",
				"project_id":  "_id",
				"cohorts":     "$envs.cohorts",
				"number":      "$info.number",
				"name":        "$info.name",
				"site":        "$project_site",
			}}},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return data, nil
}
func (s *CrossCheckService) RandomList(ctx *gin.Context, data map[string]interface{}) (map[string]interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(data["customerId"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(data["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(data["envId"].(string))
	cohortOID := primitive.NilObjectID
	if data["cohortId"] != nil {
		cohortOID, _ = primitive.ObjectIDFromHex(data["cohortId"].(string))
	}
	startTime := time.Now().Add(-time.Hour * 24 * 10).Unix()
	endTime := time.Now().Unix()
	if data["time"] != nil && len(data["time"].([]interface{})) == 2 {
		betweenTime := data["time"].([]interface{})
		startTime = int64(int(betweenTime[0].(float64)))
		endTime = int64(int(betweenTime[1].(float64)))
	}
	limit := 20
	skip := 1
	if data["limit"] != nil {
		limit = int(data["limit"].(float64))
	}
	if data["skip"] != nil {
		skip = int(data["skip"].(float64))
	}
	var sitesOIDs []primitive.ObjectID
	if data["site"] != nil && len(data["site"].([]interface{})) != 0 {
		for _, site := range data["site"].([]interface{}) {
			siteOID, _ := primitive.ObjectIDFromHex(site.(string))
			sitesOIDs = append(sitesOIDs, siteOID)
		}
	}
	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID,
		"status": 3, "random_time": bson.M{"$gt": startTime, "$lt": endTime}, "deleted": bson.M{"$ne": true}}
	if sitesOIDs != nil {
		match["project_site_id"] = bson.M{"$in": sitesOIDs}
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		// 关联中心配置表 获取时区
		{{Key: "$lookup", Value: bson.M{
			"from": "sites_config",
			"let": bson.M{
				"name": bson.M{"$first": "$project_site.name"},
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$eq": bson.A{"$cn", "$$name"}}},
							bson.M{"$expr": bson.M{"$eq": bson.A{"$en", "$$name"}}},
						},
					},
				},
			},
			"as": "sites_config",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$sites_config", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "random_number",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "number",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$number"}}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$project", Value: bson.M{
			"id": "$_id",

			"siteNumber":        "$project_site.number",
			"siteName":          models.ProjectSiteNameLookUpBson(ctx),
			"subject":           "$info.value",
			"randomGroup":       "$group",
			"randomNumber":      "$random_number",
			"blindRandomGroup":  "$number.group",
			"blindRandomNumber": "$number.number",
			"factors":           "$number.factors",
			"randomTime":        "$random_time",
			"utc":               "$sites_config.timeZone",
		}}},
		{{Key: "$sort", Value: bson.D{{"id", 1}}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	req := []map[string]interface{}{}
	err = cursor.All(nil, &req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make(map[string]interface{})
	result["total"] = len(req)
	errorCount := 0
	for _, r := range req {
		if r["randomNumber"].(string) != r["blindRandomNumber"].(string) || r["randomGroup"].(string) != r["blindRandomGroup"].(string) {
			errorCount++
		}
	}
	result["errorCount"] = errorCount
	if len(req) > skip*limit {
		result["data"] = req[(skip-1)*limit : skip*limit]
	} else {
		result["data"] = req[(skip-1)*limit:]
	}

	return result, nil
}
func (s *CrossCheckService) DispensingCheck(ctx *gin.Context) (map[string]interface{}, error) {
	data, err := task.CheckDispensing(false)
	if err != nil {
		return nil, err
	}
	return data, err
}

func dispensingErrorCount(match bson.M) (int, int, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		{{Key: "$match", Value: bson.M{"dispensing.status": 2}}}, //"dispensing_time":bson.M{"$gt":startTime, "$lt":endTime}

		//// 关联药物表，匹配项目下的编号
		{{Key: "$lookup", Value: bson.M{
			"from":         "medicine",
			"localField":   "dispensing.dispensing_medicines.medicine_id",
			"foreignField": "_id",
			"as":           "medicine",
		}}},

		{{Key: "$lookup", Value: bson.M{
			"from": "drug_configure",
			"let": bson.M{
				"visit_cycle_info_id": "$dispensing.visit_info.visit_cycle_info_id",
			},

			"pipeline": bson.A{
				bson.M{
					"$unwind": "$configures",
				},
				bson.M{
					"$unwind": "$configures.visit_cycles",
				},
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$configures.visit_cycles", "$$visit_cycle_info_id"}}},
				},
			},
			"as": "drug_configure",
		}}},

		{{Key: "$project", Value: bson.M{
			"id":             "$dispensing._id",
			"visit":          "$dispensing.visit_info.name",
			"random_group":   "$group",
			"medicine":       "$dispensing.dispensing_medicines",
			"other_medicine": "$dispensing.other_dispensing_medicines",
			"serial_number":  "$dispensing.serial_number",
			"medicine_true":  "$medicine",
			"drug_configure": "$drug_configure.configures",
		}}},
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline)
	if err != nil {
		return 0, 0, errors.WithStack(err)
	}
	var res []models.DispensingCheckMongo

	err = cursor.All(nil, &res)
	if err != nil {
		return 0, 0, errors.WithStack(err)
	}
	errCount := 0
	for _, item := range res {

		// 根据配置匹配是否符合组别
		var medicine []string
		var medicineTrue []string
		for _, dispensingMedicine := range item.MedicineTrue {
			medicineTrue = append(medicineTrue, fmt.Sprintf("%s-%s", dispensingMedicine.Number, dispensingMedicine.Name))
		}

		medicineHash := map[string]interface{}{}
		for _, dispensingMedicine := range item.Medicine {
			if medicineHash[dispensingMedicine.Name] == nil {
				medicineHash[dispensingMedicine.Name] = 1
			} else {
				medicineHash[dispensingMedicine.Name] = medicineHash[dispensingMedicine.Name].(int) + 1
			}
			medicine = append(medicine, fmt.Sprintf("%s-%s", dispensingMedicine.Number, dispensingMedicine.Name))
		}
		for _, dispensingMedicine := range item.OtherMedicine {
			medicineHash[dispensingMedicine.Name] = dispensingMedicine.Count
			medicine = append(medicine, fmt.Sprintf("%s(%d)", dispensingMedicine.Name, dispensingMedicine.Count))
			medicineTrue = append(medicineTrue, fmt.Sprintf("%s(%d)", dispensingMedicine.Name, dispensingMedicine.Count))
		}

		configMedicines := make([]map[string]interface{}, 0)
		for _, configure := range item.DrugConfigure {
			configMedicine := map[string]interface{}{}
			length := 0

			for _, value := range configure.Values {
				configMedicine[value.DrugName] = value.DispensingNumber
				configMedicine["group"] = configure.Group
				length += 1
			}
			configMedicine["len"] = length
			configMedicines = append(configMedicines, configMedicine)
		}

		//allMatch := false
		group := ""
		for _, config := range configMedicines {
			matchConfig := true
			length := 0
			for k, v := range config {
				if k == "group" || k == "len" {
					continue
				}
				if medicineHash[k] != v {
					matchConfig = false
					break
				}
				length++
			}
			if config["len"] == length && matchConfig {
				//allMatch = true
				if config["group"] == item.RandomGroup {
					group = config["group"].(string)
					break
				} else {
					if group != "N/A" {
						group = config["group"].(string)
					}
				}

			}
		}

		// 整合数据

		medicineCheck := true
		if group != "N/A" && group != item.RandomGroup {
			medicineCheck = false
		}
		medicineTrueLen := len(medicineTrue)
		medicineLen := 0
		for _, medicineItem := range medicine {
			for _, medicineTrueItem := range medicineTrue {
				if medicineItem == medicineTrueItem {
					medicineLen++
				}
			}
		}
		if medicineTrueLen != medicineLen {
			medicineCheck = false
		}
		if !medicineCheck {
			errCount++
		}
	}
	return len(res), errCount, nil
}

func (s *CrossCheckService) AuthUserCheck(ctx *gin.Context) (map[string]interface{}, error) {
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}
	// 如果用户不在全局配置，或者项目配置中 放回空数据
	counts, err := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "crossCheckAll", "$or": bson.A{bson.M{"data.mail": user.Email}, bson.M{"data.project_mail": user.Email}}})
	if err != nil {
		return map[string]interface{}{"auth": false}, nil
	}
	if counts == 0 {
		return map[string]interface{}{"auth": false}, nil
	}
	return map[string]interface{}{"auth": true}, nil
}
