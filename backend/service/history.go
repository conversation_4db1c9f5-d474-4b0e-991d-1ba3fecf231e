package service

import (
	"bytes"
	"clinflash-irt/data"
	locales "clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"reflect"
	"sort"
	"strings"

	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type HistoryService struct{}

var (
	DispensingOperationKey = []string{
		"history.dispensing.dispensing-other",
		"history.dispensing.dispensing-with-other",
		"history.dispensing.dispensing-with-other-reason",

		"history.dispensing.dispensingVisit-other",
		"history.dispensing.dispensing-other-new",
		"history.dispensing.dispensing-with-other-new",
		"history.dispensing.dispensing-with-other-reason-new",
		"history.dispensing.dispensingVisit-other-new",

		"history.dispensing.dtp-dispensing-other",
		"history.dispensing.dtp-dispensing-with-other",
		"history.dispensing.dtp-dispensing-with-other-reason",
		"history.dispensing.dtp-dispensingVisit-other",
		"history.dispensing.dtp-reissue-other",
		"history.dispensing.dtp-reissue-with-other",

		"history.dispensing.register",
		"history.dispensing.reissue-other",
		"history.dispensing.reissue-with-other",
		"history.dispensing.replace-logistics",
		"history.dispensing.dispensing-logistics",
		"history.dispensing.dispensingVisit-logistics",
		"history.dispensing.reissue-with-logistics",
		"history.dispensing.dispensing-logistics-noRandomNumber",
		"history.dispensing.dispensingVisit-logistics-noRandomNumber",

		"history.dispensing.replace",
		"history.dispensing.retrieval",
		"history.dispensing.retrieval-order",

		"history.dispensing.dispensing-new",
		"history.dispensing.dispensingVisit-new",
		"history.dispensing.dispensingVisit-new-formula",
		"history.dispensing.dispensing-new-formula",
		"history.dispensing.dispensing-other-new-formula",
		"history.dispensing.dispensing-with-other-new-formula",
		"history.dispensing.dispensing-with-other-reason-new-formula",
		"history.dispensing.dispensingVisit-other-new-formula",
		"history.dispensing.dispensing-logistics-formula",
		"history.dispensing.dispensing-logistics-noRandomNumber-formula",
		"history.dispensing.dispensingVisit-logistics-formula",
		"history.dispensing.dispensingVisit-logistics-noRandomNumber-formula",
	}
)

func (s *HistoryService) List(ctx *gin.Context, key string, oid string, start int, limit int, envId string, cohortId string, roleId string) (map[string]interface{}, error) {
	historyList, err := HistoryList(ctx, key, oid, &start, &limit, envId, cohortId, roleId, 0, 0)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return historyList, nil
}

func HistoryList(ctx *gin.Context, key string, oid string, start *int, limit *int, envId string, cohortId string, roleId string, startTime int, endTime int) (map[string]interface{}, error) {
	OID, _ := primitive.ObjectIDFromHex(oid)
	envOId, _ := primitive.ObjectIDFromHex(envId)
	cohortOId, _ := primitive.ObjectIDFromHex(cohortId)

	//var cohorts []models.Cohort
	filter := bson.M{"key": bson.M{"$regex": key}, "oid": OID}

	var project models.Project
	if key == "history.subject" {
		var subject models.Subject
		err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": OID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 筛选受试者号
		var subjectNo interface{}
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				subjectNo = info.Value
			}
		}
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// TODO 在随机
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			// 筛选环境
			env, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.ID == subject.EnvironmentID
			})
			// 循环cohort
			var subjectIds []primitive.ObjectID
			if subject.Deleted == true {
				subjectIds = []primitive.ObjectID{OID} // 再随机项目受试者只可能再第一个阶段被删除
			} else {
				for _, cohort := range env.Cohorts {
					var cohortSbj models.Subject
					subjectFilter := bson.M{
						"customer_id": subject.CustomerID,
						"project_id":  subject.ProjectID,
						"env_id":      subject.EnvironmentID,
						"cohort_id":   cohort.ID,
						"info": bson.M{
							"$elemMatch": bson.M{
								"name":  "shortname",
								"value": subjectNo,
							},
						},
						"deleted": bson.M{"$ne": true},
					}
					err = tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&cohortSbj)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
					if cohortSbj.Info != nil && len(cohortSbj.Info) > 0 {
						subjectIds = append(subjectIds, cohortSbj.ID)
					}
				}
			}

			filter = bson.M{"key": bson.M{"$regex": key}, "oid": bson.M{"$in": subjectIds}}
		}
	}

	//var count int64 = 0
	total, err := tools.Database.Collection("history").CountDocuments(nil, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var historyData []models.History
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}, {Key: "_id", Value: -1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}

	if start == nil || limit == nil {
		pipeline = mongo.Pipeline{
			{{Key: "$match", Value: filter}},
			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}, {Key: "_id", Value: -1}}}},
		}
	}

	if startTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$gt": startTime}}}})
	}
	if endTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$lt": endTime}}}})
	}

	cursor, err := tools.Database.Collection("history").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &historyData)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	userIds := slice.Map(historyData, func(index int, item models.History) primitive.ObjectID {
		return item.UID
	})
	userIds = slice.Filter(userIds, func(index int, item primitive.ObjectID) bool {
		return !item.IsZero()
	})
	userIds = slice.Unique(userIds)
	users := make([]models.User, 0)
	userMap := make(map[primitive.ObjectID]models.User)
	if len(userIds) > 0 {
		cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}}, &options.FindOptions{Projection: bson.M{"_id": 1, "info.unicode": 1}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &users)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, user := range users {
			userMap[user.ID] = user
		}
	}
	for i, history := range historyData {
		user := userMap[history.UID]
		if !user.ID.IsZero() {
			historyData[i].Unicode = user.Unicode
		}
	}
	attributeFilter := bson.M{"env_id": envOId}
	if !cohortOId.IsZero() {
		attributeFilter["cohort_id"] = cohortOId
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, attributeFilter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	// JIRA3036 查询开放药物配置 未编号如果是开放药物不打码，是盲态药物 根据角色判断是否打码
	IsBlindDrugMap, err := tools.AllDrugMap(attribute.EnvironmentID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// isBlindedRole := false
	// if attribute.AttributeInfo.Blind && blindedRole {
	// 	isBlindedRole = true
	// }

	var historyDate = make([]models.HistoryView, 0)
	for _, history := range historyData {

		// 页面请求 有表格数据的 使用已经截取后的字段
		if history.TableInfo != nil {
			history.CustomTemps = *history.ShowCustomTemps
		}
		//TODO 在随机
		var subject models.Subject
		if key == "history.subject" && project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": history.OID}).Decode(&subject)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"cohort_id": subject.CohortID}).Decode(&attribute)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
		}

		// 供应计划 映射
		if key == "history.supply-plan-medicine" {
			switch history.Data["autoSupplySize"] {
			case 1.0:
				history.Data["autoSupplySize"] = locales.Tr(ctx, "medicine.autoSupplySize1")
			case 2.0:
				history.Data["autoSupplySize"] = locales.Tr(ctx, "medicine.autoSupplySize2")
			}
			switch history.Data["supplyMode"] {
			case 1.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode1")
			case 2.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode2")
			case 3.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode3")
			case 4.0:
				history.Data["supplyMode"] = locales.Tr(ctx, "medicine.supplyMode4")
			}
		}
		if key == "history.randomization.config" {
			if history.Data["valueSite"] != nil && history.Data["valueSite"] == "" {
				history.Data["valueSite"] = locales.Tr(ctx, "user.depot")
			}
		}
		if key == "history.randomization" {
			if history.Data["valueSite"] != nil && history.Data["valueSite"] == "" {
				history.Data["valueSite"] = locales.Tr(ctx, "user.depot")
			}
		}
		if strings.Contains(key, "history.subject") {
			if !attribute.AttributeInfo.IsRandomNumber {
				history.Data["randomNumber"] = tools.BlindData
			}
		}
		if history.Key == "history.subject.unblinding" {
			if history.Data["reasonType"] != nil {
				history.Data["reasonType"] = tools.GetUnblindingReasonTran(ctx, int(history.Data["reasonType"].(int32)))
			} else {
				history.Data["reasonType"] = tools.GetUnblindingReasonTran(ctx, 0)

			}
		}
		if history.Key == "history.subject.transfer" ||
			history.Key == "history.subject.at-random-transfer" {
			oldSiteId := history.Data["oldSiteId"].(primitive.ObjectID)
			oldSite := models.ProjectSite{}
			err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": oldSiteId}).Decode(&oldSite)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			oldSiteName := models.GetProjectSiteName(ctx, oldSite)
			newSiteId := history.Data["newSiteId"].(primitive.ObjectID)
			newSite := models.ProjectSite{}
			err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": newSiteId}).Decode(&newSite)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			newSiteName := models.GetProjectSiteName(ctx, newSite)
			history.Data["oldSite"] = fmt.Sprintf("%s-%s", oldSite.Number, oldSiteName)
			history.Data["newSite"] = fmt.Sprintf("%s-%s", newSite.Number, newSiteName)
		}

		if history.Key == "history.randomization.attribute" {
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["segmentLength"] = tools.BlindData
			}
		}
		if history.Key == "history.subject.random" ||
			history.Key == "history.subject.randomNoNumber" ||
			history.Key == "history.subject.randomSub" ||
			history.Key == "history.subject.label.random" ||
			history.Key == "history.subject.label.randomSub" ||
			history.Key == "history.subject.label.randomNoNumber" ||
			history.Key == "history.subject.label.randomNoNumberSub" ||
			history.Key == "history.subject.randomNoNumberSub" ||
			history.Key == "history.subject.label.at-random-random" ||
			history.Key == "history.subject.label.at-random-randomSub" ||
			history.Key == "history.subject.label.at-random-randomNoNumber" ||
			history.Key == "history.subject.label.at-random-randomNoNumberSub" {
			if isBlindedRole && history.Data["subGroup"] != nil && history.Data["subGroup"].(string) != "" {
				var subject models.Subject
				err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": history.OID}).Decode(&subject)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				var randomList models.RandomList
				err = tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": subject.RandomListID}).Decode(&randomList)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				_, b := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
					return item.ParName == history.Data["group"].(string) && item.SubName == history.Data["subGroup"].(string) && item.Blind
				})
				if b {
					history.Data["subGroup"] = tools.BlindData
				}
			}
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["group"] = tools.BlindData
			}
		}
		if history.Key == "history.project.medicine.upload" {
			drugName := history.Data["drugName"]
			isBlindedDrug, _ := tools.IsBlindedDrug(attribute.EnvironmentID, drugName.(string))
			if isBlindedDrug && isBlindedRole {
				history.Data["drugName"] = tools.BlindData
			}
		}
		if history.Key == "history.randomization.config.block.generate" {
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["groupRatio"] = tools.BlindData
				history.Data["numberText"] = tools.BlindData
			}
		}
		if history.Key == "history.randomization.config.group.add" {
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["name"] = tools.BlindData
			}
		}
		if history.Key == "history.randomization.config.group.delete" {
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["name"] = tools.BlindData
			}
		}
		if history.Key == "history.randomization.config.group.edit" {
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["name"] = tools.BlindData
				history.Data["oldName"] = tools.BlindData
			}
		}
		if history.Key == "history.supply-plan-medicine.add" {
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["medicineName"] = tools.BlindData
			}
		}
		if history.Key == "history.supply-plan-medicine.update" {
			if attribute.AttributeInfo.Blind && isBlindedRole {
				history.Data["medicineName"] = tools.BlindData
			}
		}

		// TODO 在随机
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			if history.Key == "history.subject.label.at-random-replaced-new-a" || history.Key == "history.subject.label.at-random-replaced-new-b" {
				cohortIsRandomNumber1 := false
				cohortIsRandomNumber2 := false

				for _, env := range project.Environments {
					if env.ID == subject.EnvironmentID {
						for i, cho := range env.Cohorts {
							var atRandomAttribute models.Attribute
							err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"cohort_id": cho.ID}).Decode(&atRandomAttribute)
							if err != nil && err != mongo.ErrNoDocuments {
								return nil, errors.WithStack(err)
							}
							if i == 0 { // 第一阶段
								cohortIsRandomNumber1 = atRandomAttribute.AttributeInfo.IsRandomNumber
							} else { // 第二阶段
								cohortIsRandomNumber2 = atRandomAttribute.AttributeInfo.IsRandomNumber
							}
						}
						break
					}
				}

				if !cohortIsRandomNumber1 && !cohortIsRandomNumber2 {
					history.Data["beReplaceRandomNumber"] = tools.BlindData
					history.Data["replaceRandomNumber"] = tools.BlindData
					history.Data["beReplaceRandomNumber2"] = tools.BlindData
					history.Data["replaceRandomNumber2"] = tools.BlindData
				} else if !cohortIsRandomNumber1 && cohortIsRandomNumber2 {
					history.Data["beReplaceRandomNumber"] = tools.BlindData
					history.Data["replaceRandomNumber"] = tools.BlindData
				} else if cohortIsRandomNumber1 && !cohortIsRandomNumber2 {
					history.Data["beReplaceRandomNumber2"] = tools.BlindData
					history.Data["replaceRandomNumber2"] = tools.BlindData
				}
			}
		} else {
			if history.Key == "history.subject.replaced-new" || history.Key == "history.subject.label.replaced-new" {
				if !attribute.AttributeInfo.IsRandomNumber {
					history.Data["beReplaceRandomNumber"] = tools.BlindData
					history.Data["replaceRandomNumber"] = tools.BlindData
				}
			}
		}

		if history.Key == "history.medicine.sku-freeze-subject" || history.Key == "history.medicine.sku-used-subject" || history.Key == "history.medicine.sku-use-subject" || history.Key == "history.medicine.sku-in-order-subject" || history.Key == "history.medicine.sku-lost-subject" {
			if history.Data["operation"].(int32) == 4 && visitCycle.SetInfo.IsOpen {
				if ctx.GetHeader("Accept-Language") == "zh" {
					history.Data["operation"] = visitCycle.SetInfo.NameZh
				} else {
					history.Data["operation"] = visitCycle.SetInfo.NameEn
				}
			} else {
				history.Data["operation"] = getOperationType(ctx, history.Data["operation"].(int32))
			}
		}

		if history.Key == "history.dispensing.reissue-with-logistics" ||
			history.Key == "history.dispensing.dispensing-logistics" ||
			history.Key == "history.dispensing.dispensingVisit-logistics" ||
			history.Key == "history.dispensing.dispensingVisit-logistics-formula" ||
			history.Key == "history.dispensing.dispensing-logistics-formula" {
			if !attribute.AttributeInfo.IsRandomNumber || !attribute.AttributeInfo.Random {
				switch history.Key {
				case "history.dispensing.reissue-with-logistics":
					history.Key = "history.dispensing.reissue-with-logistics-noRandomNumber"
				case "history.dispensing.dispensing-logistics":
					history.Key = "history.dispensing.dispensing-logistics-noRandomNumber"
				case "history.dispensing.dispensingVisit-logistics":
					history.Key = "history.dispensing.dispensingVisit-logistics-noRandomNumber"
				case "history.dispensing.dispensing-logistics-formula":
					history.Key = "history.dispensing.dispensing-logistics-noRandomNumber-formula"
				case "history.dispensing.dispensingVisit-logistics-formula":
					history.Key = "history.dispensing.dispensingVisit-logistics-noRandomNumber-formula"
				}
			}
			var medicines []string
			for _, item := range history.Data["medicine"].(primitive.A) {

				if item.(map[string]interface{})["blind"].(bool) {
					if isBlindedRole && IsBlindDrugMap[item.(map[string]interface{})["name"].(string)] {
						medicines = append(medicines, tools.BlindData+"("+item.(map[string]interface{})["number"].(string)+")")
					} else {
						medicines = append(medicines, item.(map[string]interface{})["name"].(string)+"("+item.(map[string]interface{})["number"].(string)+")")
					}
				} else {
					value := ""
					if isBlindedRole && IsBlindDrugMap[item.(map[string]interface{})["name"].(string)] {
						value = tools.BlindData + "/" + convertor.ToString(item.(map[string]interface{})["number"].(int32))
					} else {
						value = item.(map[string]interface{})["name"].(string) + "/" + convertor.ToString(item.(map[string]interface{})["number"].(int32))
					}
					if item.(map[string]interface{})["batch"] != nil {
						value = value + "/" + item.(map[string]interface{})["batch"].(string) + "/" + item.(map[string]interface{})["expireDate"].(string)
						medicines = append(medicines, value)
					} else {
						medicines = append(medicines, value)
					}
				}
			}
			history.Data["medicine"] = medicines
			if history.Data["vendor"] != nil {
				logisName := "其他"
				if locales.Lang(ctx) == "en" {
					logisName = "other"
				}
				if reflect.TypeOf(history.Data["vendor"]) == reflect.TypeOf("0") {
					logis, _ := GetLogisticsCode(ctx, history.Data["vendor"].(string))
					if len(logis.Name) > 0 {
						logisName = logis.Name
					}
					history.Data["vendor"] = logisName
				}
			}
			//history.Data["vendor"] = locales.Tr(ctx, "order_logistics_"+convertor.ToString(history.Data["vendor"]))
			history.Data["sendType"] = locales.Tr(ctx, "history.dispensing.send-type-"+convertor.ToString(history.Data["sendType"]))
		}

		if history.Key == "history.medicine.drugFreeze.allDrugFreeze" ||
			history.Key == "history.medicine.drugFreeze.otherDrugFreeze" {
			//根据OID查询隔离管理数据
			var medicineFreeze models.MedicineFreeze
			err := tools.Database.Collection("medicine_freeze").FindOne(nil, bson.M{"_id": OID}).Decode(&medicineFreeze)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if history.Data["otherMedicines"] != nil {
				var newOtherMedicines []string
				for _, item := range history.Data["otherMedicines"].(primitive.A) {
					newOthers := item.(string)
					others := strings.Split(item.(string), "/")
					if len(others) > 0 {
						var newOthersBytes bytes.Buffer
						isBlindedDrug, _ := tools.IsBlindedDrug(medicineFreeze.EnvironmentID, others[0])
						if isBlindedRole && isBlindedDrug {
							newOthersBytes.WriteString(tools.BlindData + "/")
							for i := 1; i < len(others); i++ {
								newOthersBytes.WriteString(others[i] + "/")
							}
							newOthers = newOthersBytes.String()[0 : len(newOthersBytes.String())-1]
						}
					}
					newOtherMedicines = append(newOtherMedicines, newOthers)
				}
				history.Data["otherMedicines"] = newOtherMedicines
			}
		}

		if history.Key == "history.medicine.drugFreeze.allLost" ||
			history.Key == "history.medicine.drugFreeze.allLost-package" ||
			history.Key == "history.medicine.drugFreeze.all-quarantine-yes" ||
			history.Key == "history.medicine.drugFreeze.all-quarantine-yes-package" ||
			history.Key == "history.medicine.drugFreeze.allRelease" ||
			history.Key == "history.medicine.drugFreeze.allRelease-package" ||
			history.Key == "history.medicine.drugFreeze.all-quarantine-no" ||
			history.Key == "history.medicine.drugFreeze.all-quarantine-no-package" ||
			history.Key == "history.medicine.drugFreeze.all-approval" ||
			history.Key == "history.medicine.drugFreeze.all-approval-package" ||
			history.Key == "history.medicine.drugFreeze.otherLost" ||
			history.Key == "history.medicine.drugFreeze.other-quarantine-yes" ||
			history.Key == "history.medicine.drugFreeze.otherRelease" ||
			history.Key == "history.medicine.drugFreeze.other-quarantine-no" ||
			history.Key == "history.medicine.drugFreeze.other-approval" {
			//根据OID查询隔离管理数据
			var medicineFreeze models.MedicineFreeze
			err := tools.Database.Collection("medicine_freeze").FindOne(nil, bson.M{"_id": OID}).Decode(&medicineFreeze)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if history.Data["otherMedicines"] != nil && history.Data["otherMedicines"].(string) != "" {
				otherMedicines := history.Data["otherMedicines"].(string)
				others := strings.Split(otherMedicines, "/")
				if len(others) > 0 {
					isBlindedDrug, _ := tools.IsBlindedDrug(medicineFreeze.EnvironmentID, others[0])
					if isBlindedRole && isBlindedDrug {
						var newOthers bytes.Buffer
						newOthers.WriteString(tools.BlindData + "/")
						for i := 1; i < len(others); i++ {
							newOthers.WriteString(others[i] + "/")
						}
						history.Data["otherMedicines"] = newOthers.String()[0 : len(newOthers.String())-1]
					}
				}
			}
		}

		if history.Key == "history.medicine.otherFreeze" ||
			history.Key == "history.medicine.otherReceive" ||
			history.Key == "history.medicine.otherShipped" ||
			history.Key == "history.medicine.otherConfirmed" ||
			history.Key == "history.medicine.otherToBeConfirm" ||
			history.Key == "history.medicine.otherLocked" ||
			history.Key == "history.medicine.otherUse" ||
			history.Key == "history.medicine.otherCanUse" ||
			history.Key == "history.medicine.otherLost" ||
			history.Key == "history.medicine.otherRelease" ||
			history.Key == "history.medicine.other-approval" ||
			history.Key == "history.medicine.otherReleaseLost" ||
			history.Key == "history.medicine.other-quarantine-no" {
			//根据OID查询数据
			var medicineOtherKey models.MedicineOtherKey
			err := tools.Database.Collection("medicine_other_key").FindOne(nil, bson.M{"_id": OID}).Decode(&medicineOtherKey)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			otherName := history.Data["name"].(string)
			isBlindedDrug, _ := tools.IsBlindedDrug(medicineOtherKey.EnvironmentID, otherName)
			if isBlindedRole && isBlindedDrug {
				history.Data["name"] = tools.BlindData
			}
		}

		if history.Key == "history.order.logistics" ||
			history.Key == "history.order.logistics—other" ||
			history.Key == "history.order.logistics—other-actualTime-all" ||
			history.Key == "history.order.ogistics-actualTime-all" ||
			history.Key == "history.order.send-with-logistics" ||
			history.Key == "history.order.send-with-logistics-expectedTime" ||
			history.Key == "history.order.send-with-other-logistics" ||
			history.Key == "history.order.logistics-actualTime-all" ||
			history.Key == "history.order.send-with-other-logistics-expectedTime" {
			if history.Data["logistics"] != nil {
				if reflect.TypeOf(history.Data["logistics"]) == reflect.TypeOf("0") {
					logis, _ := GetLogisCode(ctx, history.Data["logistics"].(string))
					history.Data["logistics"] = logis.Name
					history.Data["other"] = history.Data["other"].(string)
				}
			}
			if history.Data["other"] != nil {
				if len(history.Data["other"].(string)) > 0 {
					if locales.Lang(ctx) == "en" {
						history.Data["logistics"] = "other"
					} else {
						history.Data["logistics"] = "其他"
					}
					history.Data["other"] = history.Data["other"].(string)
				}
			}
		}

		if history.Data["other"] != nil {
			if len(history.Data["other"].(string)) > 0 {
				if locales.Lang(ctx) == "en" {
					history.Data["logistics"] = "other"
				} else {
					history.Data["logistics"] = "其他"
				}
			}
		}
		_, inDispensing := slice.Find(DispensingOperationKey, func(index int, item string) bool {
			return item == history.Key
		})
		if inDispensing {
			if isBlindedRole {
				changeOtherMedicineBlind(ctx, &history, IsBlindDrugMap)
			}
			if history.Data["form"] == nil {
				history.Data["form"] = ""
			}
			if strings.Contains(history.Key, "-formula") {
				if (history.Data["weight"] != nil && history.Data["weight"] == "") &&
					(history.Data["date"] != nil && history.Data["date"] == "") &&
					(history.Data["height"] != nil && history.Data["height"] == "") {
					history.Key = strings.Replace(history.Key, "-formula", "", 1)
				}
			}
			if history.Data["formula"] == nil {
				history.Data["formula"] = ""
			}
		}

		if history.Key == "history.medicine.rest-receive" || history.Key == "history.medicine.rest-return" || history.Key == "history.medicine.rest-destroy" ||
			history.Key == "history.medicine.rest-return-retrieve" || history.Key == "history.medicine.rest-destroy-retrieve" {
			if history.Data != nil {
				if history.Data["operation"] != nil {
					history.Data["operation"] = locales.Tr(ctx, history.Data["operation"].(string))
				}
			}
		}
		if len(history.User) > 0 {
			if history.User == "码上放心" && locales.Lang(ctx) == "en" {
				history.User = "CodeTrust Platform"
			}
		}

		if history.Key == "history.order.logistics" {
			if history.Data["logistics"] != nil {
				if history.Data["logistics"] == "" || history.Data["logistics"] == nil {
					continue
				}
			}
		}

		if history.Key == "history.medicine.updateBatch" || history.Key == "history.medicine.updateOtherBatch" || history.Key == "history.dispensing.updateBatch" {
			p := make([]string, 0)
			if history.Data["status"] != nil {
				statuss := history.Data["status"].(primitive.A)
				for _, status := range statuss {
					val := data.GetMedicineStatus(ctx, int(status.(int32)))
					p = append(p, val)
				}
			}
			history.Data["status"] = strings.Join(p, "、")
		}

		if (history.Key == "history.subject.label.screen" ||
			history.Key == "history.subject.label.at-random-screen" ||
			history.Key == "history.subject.label.screenScreenFail" ||
			history.Key == "history.subject.label.updateScreen" ||
			history.Key == "history.subject.label.updateScreenSignOutTime" ||
			history.Key == "history.subject.label.updateScreenFailSignOutTime" ||
			history.Key == "history.subject.label.updateScreenFail") && history.Data["isScreen"] != nil {
			if history.Data["isScreen"].(bool) {
				history.Data["isScreen"] = locales.Tr(ctx, "common.yes")
			} else {
				history.Data["isScreen"] = locales.Tr(ctx, "common.no")
			}
		}

		if strings.Contains(history.Key, "history.dispensing.") || strings.Contains(history.Key, "history.subject.label.") {
			if history.Data["label"] != nil {
				if len(history.Data["label"].(string)) != 0 {
					history.Data["label"] = locales.Tr(ctx, "subject.number")
					if locales.Lang(ctx) == "en" {
						if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
							history.Data["label"] = attribute.AttributeInfo.SubjectReplaceTextEn
						} else {
							if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
								history.Data["label"] = attribute.AttributeInfo.SubjectReplaceText
							}
						}
					} else if locales.Lang(ctx) == "zh" {
						if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
							history.Data["label"] = attribute.AttributeInfo.SubjectReplaceText
						} else {
							if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
								history.Data["label"] = attribute.AttributeInfo.SubjectReplaceTextEn
							}
						}
					}
				}
			}
		}

		//受试者登记
		if history.Key == "history.subject.add" || history.Key == "history.subject.at-random-add" {
			if history.Data["content"] != nil {
				if len(history.Data["content"].(string)) != 0 {
					replacement := GetSubjectReplaceText(ctx, attribute)
					index := strings.Index(history.Data["content"].(string), ":")
					if index != -1 {
						history.Data["content"] = strings.Replace(history.Data["content"].(string), history.Data["content"].(string)[:index], replacement, 1)
					}
				}
			}
		}

		//受试者删除
		if history.Key == "history.subject.delete" {
			if history.Data["content"] != nil {
				if len(history.Data["content"].(string)) != 0 {
					replacement := GetSubjectReplaceText(ctx, attribute)
					index := strings.Index(history.Data["content"].(string), ":")
					if index != -1 {
						history.Data["content"] = strings.Replace(history.Data["content"].(string), history.Data["content"].(string)[:index], replacement, 1)
					}
				}
			}
		}

		if history.Key == "history.medicine.update-batch-expireDate" || history.Key == "history.medicine.other-update-batch-expireDate" {
			history.Data["status"] = getShipmentStatus(ctx, history.Data["status"].(int32))
		}

		//受试者修改/筛选/停用/完成研究/删除
		if history.Key == "history.subject.label.update" ||
			history.Key == "history.subject.label.updateSubjectNo" || history.Key == "history.subject.label.updateSignOutTime" ||
			history.Key == "history.subject.label.updateScreen" || history.Key == "history.subject.label.updateScreenSignOutTime" ||
			history.Key == "history.subject.label.updateScreenFail" || history.Key == "history.subject.label.updateScreenFailSignOutTime" ||
			history.Key == "history.subject.label.screen" || history.Key == "history.subject.label.screenScreenFail" ||
			history.Key == "history.subject.label.signOut" || history.Key == "history.subject.label.signOutReal" ||
			history.Key == "history.subject.label.finish" || history.Key == "history.subject.start-follow-up-visits" ||
			history.Key == "history.subject.label.at-random-screen" || history.Key == "history.subject.label.at-random-random" ||
			history.Key == "history.subject.at-random-transfer" || history.Key == "history.subject.at-random-joinTime" ||
			history.Key == "history.subject.at-random-add" || history.Key == "history.subject.at-random-unblinding-application" ||
			history.Key == "history.subject.at-random-unblinding-application-pv" || history.Key == "history.subject.at-random-unblinding-success" ||
			history.Key == "history.subject.at-random-update" || history.Key == "history.subject.label.at-random-finish" ||
			history.Key == "history.subject.label.at-random-pvUnblinding" || history.Key == "history.subject.label.at-random-randomNoNumber" ||
			history.Key == "history.subject.label.at-random-randomNoNumberSub" || history.Key == "history.subject.label.at-random-randomSub" ||
			history.Key == "history.subject.label.at-random-remark-pvUnblinding" || history.Key == "history.subject.label.at-random-replaced-new-a" ||
			history.Key == "history.subject.label.at-random-signOut" || history.Key == "history.subject.label.updateCustomize" ||
			history.Key == "history.subject.label.at-random-replaced-new-b" || history.Key == "history.dispensing.dispensingCustomer-register" ||
			history.Key == "history.dispensing.dispensingCustomer-replace" || history.Key == "history.dispensing.invalid" ||
			history.Key == "history.dispensing.dispensingCustomer-retrieval" || history.Key == "history.dispensing.resume" ||
			history.Key == "history.subject.label.randomNoNumber" || history.Key == "history.subject.label.replaced-new" ||
			history.Key == "history.subject.label.remark-pvUnblinding" || history.Key == "history.subject.label.pvUnblinding" ||
			history.Key == "history.dispensing.retrieval-order" || history.Key == "history.dispensing.retrieval" ||
			history.Key == "history.dispensing.scanConfrim" || history.Key == "history.dispensing.dispensing-new-formula" ||
			history.Key == "history.dispensing.dispensing-with-other-new-formula" || history.Key == "history.dispensing.dispensing-with-other-reason-new-formula" ||
			history.Key == "history.dispensing.dispensingVisit-new-formula" || history.Key == "history.dispensing.dispensingVisit-other-new-formula" ||
			history.Key == "history.dispensing.dispensing-logistics-formula" || history.Key == "history.dispensing.dispensing-logistics-noRandomNumber-formula" ||
			history.Key == "history.dispensing.dispensingVisit-logistics-formula" || history.Key == "history.dispensing.dispensingVisit-logistics-noRandomNumber-formula" ||
			history.Key == "history.dispensing.replace" || history.Key == "history.dispensing.reissue-with-logistics-noRandomNumber" ||
			history.Key == "history.dispensing.reissue-with-logistics" || history.Key == "history.dispensing.dispensingVisit-logistics-noRandomNumber" ||
			history.Key == "history.dispensing.dispensingVisit-logistics" || history.Key == "history.dispensing.dispensing-logistics-noRandomNumber" ||
			history.Key == "history.dispensing.dispensing-logistics" || history.Key == "history.dispensing.replace-logistics" ||
			history.Key == "history.dispensing.reissue-with-other" || history.Key == "history.dispensing.reissue-other" ||
			history.Key == "history.dispensing.reissue" || history.Key == "history.dispensing.register" ||
			history.Key == "history.dispensing.dtp-reissue-with-other" || history.Key == "history.dispensing.dtp-reissue-other" ||
			history.Key == "history.dispensing.dtp-reissue" || history.Key == "history.dispensing.dtp-dispensingVisit-other" ||
			history.Key == "history.dispensing.dtp-dispensingVisit" || history.Key == "history.dispensing.dtp-dispensing-with-other-reason" ||
			history.Key == "history.dispensing.dtp-dispensing-with-other" || history.Key == "history.dispensing.dtp-dispensing-other" ||
			history.Key == "history.dispensing.dtp-dispensing" || history.Key == "history.dispensing.dispensingVisit-other-new" ||
			history.Key == "history.dispensing.dispensingVisit-new" || history.Key == "history.dispensing.dispensing-with-other-reason-new" ||
			history.Key == "history.dispensing.dispensing-with-other-new" || history.Key == "history.dispensing.dispensing-other-new" ||
			history.Key == "history.dispensing.dispensing-new" || history.Key == "history.dispensing.dispensingVisit-other" ||
			history.Key == "history.dispensing.dispensingVisit" || history.Key == "history.dispensing.dispensing-with-other-reason" ||
			history.Key == "history.dispensing.dispensing-with-other" || history.Key == "history.dispensing.dispensing-other" ||
			history.Key == "history.dispensing.dispensing" || history.Key == "history.dispensing.cancel" || history.Key == "history.subject.switch-cohort" ||
			history.Key == "history.subject.delete" || history.Key == "history.subject.close-follow-up-visits" ||
			history.Key == "history.dispensing.dispensingCustomer-not-attend" || history.Key == "history.dispensing.dispensingCustomer-resume" ||
			history.Key == "history.subject.transfer" {
			if history.Data["label"] != nil {
				replacement := GetSubjectReplaceText(ctx, attribute)
				history.Data["label"] = replacement
			}
		}

		//发药轨迹 单字段拼接轨迹
		if history.Key == "history.dispensing.dispensingCustomer-dispensing" ||
			history.Key == "history.dispensing.dispensingCustomer-dispensingVisit" ||
			history.Key == "history.dispensing.dispensingCustomer-dispensingVisitCustomer" ||
			history.Key == "history.dispensing.dispensingCustomer-unblinding-application" ||
			history.Key == "history.dispensing.dispensingCustomer-reissue" {
			if history.Data["label"] != nil {
				replacement := GetSubjectReplaceText(ctx, attribute)
				history.Data["label"] = replacement
			}
			if !attribute.AttributeInfo.IsRandomNumber || !attribute.AttributeInfo.Random {
				history.CustomTemps[0].CustomTempOptions = slice.Filter(history.CustomTemps[0].CustomTempOptions, func(index int, item models.CustomTempOption) bool {
					return item.Key != "history.dispensing.single.randomNumber"
				})
			}

			// 访视外读取实时配置
			dispensingType := GetVisitDispensingTypeReplaceText(ctx, visitCycle)
			if history.Key == "history.dispensing.dispensingCustomer-dispensingVisitCustomer" {
				history.Data["dispensingType"] = dispensingType
			}
			for i, item := range history.CustomTemps {
				for j, option := range item.CustomTempOptions {
					if option.Key == "history.dispensing.single.reasonDispensingVisitCustomer" {
						history.CustomTemps[i].CustomTempOptions[j].Data["dispensingType"] = dispensingType
					}
				}
			}
		}
		//自定义拼接的轨迹
		colon := locales.Tr(ctx, "history.dispensing.single.colon")
		if history.CustomTemps != nil && len(history.CustomTemps) > 0 {
			for _, temp := range history.CustomTemps {
				err = slice.SortByField(temp.CustomTempOptions, "Index")
				if err != nil {
					return nil, errors.WithStack(err)
				}
				optionContents := make([]string, 0)
				for _, option := range temp.CustomTempOptions {
					if option.Key == "history.subject.label.updateCustomizeIsScreen" {
						if option.Data["isScreen"].(bool) {
							option.Data["isScreen"] = locales.Tr(ctx, "common.yes")
						} else {
							option.Data["isScreen"] = locales.Tr(ctx, "common.no")
						}
					}
					optionContent := ""
					switch option.TransType {
					case models.KeyUnData:
						optionContent = locales.Tr(ctx, option.Key, option.Data)
						if option.Key == "history.subject.label.common-key-value1" {
							replacement := GetSubjectReplaceText(ctx, attribute)
							option.Data["name"] = replacement
							optionContent = locales.Tr(ctx, option.Key, option.Data)
						}
					case models.UnKeyData:
						optionContent = option.Key + colon + locales.Tr(ctx, option.TransData)
					case models.UnKeyUnData:
						optionContent = option.Key + colon + option.TransData
					case models.KeyData:
						optionContent = locales.Tr(ctx, option.Key) + colon + locales.Tr(ctx, option.TransData)
					}
					if option.Key != "history.order.expireDateBatch.ipExpireDateBatch" {
						optionContents = append(optionContents, optionContent)
					}
				}
				customContent := strings.Join(optionContents, locales.Tr(ctx, temp.ConnectingSymbolKey))
				customContent = customContent + locales.Tr(ctx, temp.LastSymbolKey)
				if history.Data == nil {
					history.Data = make(map[string]interface{}) // 根据实际类型调整键值类型
				}
				history.Data[temp.ParKey] = customContent
			}
		}
		content := locales.Tr(ctx, history.Key, history.Data)
		userName := ""
		if history.Unicode == 0 {
			userName = history.User
		} else {
			userName = fmt.Sprintf("%s(%d)", history.User, history.Unicode)
		}
		viewHistory := models.HistoryView{
			OID:     history.OID,
			Content: content,
			Time:    history.Time,
			User:    userName,
		}

		if history.TableInfo != nil {
			for _, item := range history.TableInfo.Title {
				viewHistory.ViewTableInfo.Title = append(viewHistory.ViewTableInfo.Title, locales.Tr(ctx, item))
			}
			for _, col := range history.TableInfo.Value {
				info := map[string]interface{}{}

				for i, item := range col {
					optionContent := ""
					switch item.TransType {
					case models.KeyUnData:
						optionContent = locales.Tr(ctx, item.Key, item.Data)
						if item.Key == "history.subject.label.common-key-value1" {
							replacement := GetSubjectReplaceText(ctx, attribute)
							item.Data["name"] = replacement
							optionContent = locales.Tr(ctx, item.Key, item.Data)
						}
					case models.KeyData:
						optionContent = locales.Tr(ctx, item.TransData)
					}

					info[viewHistory.ViewTableInfo.Title[i]] = optionContent
				}
				viewHistory.ViewTableInfo.Value = append(viewHistory.ViewTableInfo.Value, info)
			}

		}
		historyDate = append(historyDate, viewHistory)

	}
	return map[string]interface{}{"total": total, "items": historyDate}, nil
}

func getShipmentStatus(ctx *gin.Context, operation int32) string {
	switch operation {
	case 0:
		return locales.Tr(ctx, "medicine.status.toBeWarehoused")
	case 1:
		return locales.Tr(ctx, "medicine.status.available")
	case 2:
		return locales.Tr(ctx, "medicine.status.delivered")
	case 3:
		return locales.Tr(ctx, "medicine.status.transit")
	case 4:
		return locales.Tr(ctx, "medicine.status.quarantine")
	case 5:
		return locales.Tr(ctx, "medicine.status.used")
	case 6:
		return locales.Tr(ctx, "medicine.status.lose")
	case 7:
		return locales.Tr(ctx, "medicine.status.expired")
	case 8:
		return locales.Tr(ctx, "medicine.status.receive")
	case 9:
		return locales.Tr(ctx, "medicine.status.return")
	case 10:
		return locales.Tr(ctx, "medicine.status.destroy")
	case 11:
		return locales.Tr(ctx, "medicine.status.InOrder")
	case 12:
		return locales.Tr(ctx, "medicine.status.stockPending")
	case 13:
		return locales.Tr(ctx, "medicine.status.apply")
	case 14:
		return locales.Tr(ctx, "medicine.status.fzn")
	case 15:
		return locales.Tr(ctx, "medicine.status.toBeApproved")
	case 20:
		return locales.Tr(ctx, "medicine.status.locked")
	case 21:
		return locales.Tr(ctx, "medicine.status.dsm")
	case 22:
		return locales.Tr(ctx, "medicine.status.dsh")
	case 23:
		return locales.Tr(ctx, "medicine.status.shsb")
	default:
		return ""
	}
}

func (s *HistoryService) GetKeyList(ctx *gin.Context, key string, oid string) (bool, error) {
	OID, _ := primitive.ObjectIDFromHex(oid)
	collection := tools.Database.Collection("history")
	filter := bson.M{"key": bson.M{"$regex": key}, "oid": OID}

	var data []models.History
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}, {Key: "_id", Value: -1}}}},
	}
	cursor, err := collection.Aggregate(nil, pipeline)
	if err != nil {
		return false, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return false, errors.WithStack(err)
	}

	result := false
	for _, history := range data {
		if history.Key == "history.order.send-new" || history.Key == "history.order.send-with-logistics" {
			result = true
		}
	}

	return result, err
}

func getOperationType(ctx *gin.Context, operation int32) string {
	var (
		OperationType = map[int32]string{
			1: locales.Tr(ctx, "history.medicine.operation.dispensing"),
			2: locales.Tr(ctx, "history.medicine.operation.retrieval"),
			3: locales.Tr(ctx, "history.medicine.operation.replace"),
			4: locales.Tr(ctx, "history.medicine.operation.unscheduled"),
			5: locales.Tr(ctx, "history.medicine.operation.reissue"),
			6: locales.Tr(ctx, "history.medicine.operation.register"),
		}
	)
	return OperationType[operation]
}

func changeOtherMedicineBlind(ctx *gin.Context, history *models.History, blindMedicine map[string]bool) {
	var keys []string
	for key := range blindMedicine {
		keys = append(keys, key)
	}
	sort.Sort(sort.Reverse(sort.StringSlice(keys)))

	for _, key := range keys {
		if !blindMedicine[key] {
			continue
		}

		if history.Data["real_medicine"] != nil {
			if strings.Contains(history.Data["real_medicine"].(string), key) {
				history.Data["real_medicine"] = strings.Replace(history.Data["real_medicine"].(string), key, tools.BlindData, 1)
			}
		}
		if history.Data["medicine"] != nil {
			switch history.Data["medicine"].(type) {
			case string:
				if strings.Contains(history.Data["medicine"].(string), key) {
					history.Data["medicine"] = strings.Replace(history.Data["medicine"].(string), key, tools.BlindData, 1)
				}
			case primitive.A:
				for i, item := range history.Data["medicine"].(primitive.A) {
					_, ok := slice.Find(keys, func(index int, name string) bool {
						if !blindMedicine[name] && strings.Contains(item.(string), name) {
							return true
						}
						return false
					})
					if !ok {
						if strings.Contains(item.(string), key) {
							history.Data["medicine"].(primitive.A)[i] = strings.Replace(item.(string), key, tools.BlindData, 1)
						}
					}

				}
			}

		}

		if history.Data["other_medicine"] != nil {
			for i, item := range history.Data["other_medicine"].(primitive.A) {
				_, ok := slice.Find(keys, func(index int, name string) bool {
					if !blindMedicine[name] && strings.Contains(item.(string), name) {
						return true
					}
					return false
				})
				if !ok {
					if strings.Contains(item.(string), key) {
						history.Data["other_medicine"].(primitive.A)[i] = strings.Replace(item.(string), key, tools.BlindData, 1)
					}
				}
			}
		}

		if history.Data["beReplaceMedicine"] != nil {
			for i, item := range history.Data["beReplaceMedicine"].(primitive.A) {

				_, ok := slice.Find(keys, func(index int, name string) bool {
					if !blindMedicine[name] && strings.Contains(item.(string), name) {
						return true
					}
					return false
				})
				if !ok {
					if strings.Contains(item.(string), key) {
						history.Data["beReplaceMedicine"].(primitive.A)[i] = strings.Replace(item.(string), key, tools.BlindData, 1)
					}
				}

			}
		}
	}
}
