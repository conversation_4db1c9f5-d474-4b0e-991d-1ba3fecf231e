package service

import (
	"clinflash-irt/data"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strconv"
	"strings"
	"time"
)

type ProjectSiteInfo struct {
	ID       primitive.ObjectID `bson:"_id" json:"id"`
	Country  string             `bson:"country" json:"country"`
	Number   string             `bson:"number" json:"number"`
	Name     string             `bson:"name" json:"name"`
	TimeZone string             `bson:"timeZone" json:"timeZone"`
	TZ       string             `bson:"tz" json:"tz"`
	Region   string             `bson:"region" json:"region"`
}

// ExportDispensingReport 导出发药报表
func ExportDispensingReportXX(ctx *gin.Context, projectID string, envID string, cohortIDs []string, templateId string, roleId string, now time.Time) (string, []byte, error) {
	// 1. 初始化基础数据
	exportCtx, err := initExportContext(ctx, projectID, envID, cohortIDs, templateId, roleId, now)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	// 2. 获取报表数据
	datas, err := fetchDispensingData(ctx, exportCtx)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	// 3. 处理用户信息
	if err := processUserInfo(exportCtx, datas); err != nil {
		return "", nil, errors.WithStack(err)
	}

	// 4. 生成Excel报表
	return generateExcelReport(ctx, exportCtx, datas)
}

// ExportContext 导出上下文，包含所有需要的数据和配置
type ExportContext struct {
	Ctx                     *gin.Context
	ProjectID               string
	EnvID                   string
	CohortIDs               []string
	TemplateId              string
	RoleId                  string
	Now                     time.Time
	EnvOID                  primitive.ObjectID
	ProjectOID              primitive.ObjectID
	Subjects                []primitive.ObjectID
	Attributes              []models.Attribute
	VisitCycles             []models.VisitCycle
	Project                 models.Project
	ShowRandomNumber        map[primitive.ObjectID]bool
	ShowRegisterGroup       map[primitive.ObjectID]bool
	SubjectReplaceText      string
	BlindMedicineMap        map[string]bool
	Countries               bson.M
	IsBlindedRole           bool
	RoomShowBlind           bool
	TimeZone                float64
	UserMap                 map[primitive.ObjectID]int32
	Template                *models.CustomTemplate
	Forms                   []map[string]interface{}
	CustomerReportTitles    []models.CustomerReportTitle
	CohortMap               map[primitive.ObjectID]models.Cohort
	OrderInfo               map[primitive.ObjectID]models.LogisticsInfo
	MapLogisticsCompanyCode map[string]string
}
type SubjectBasicInfo struct {
	ID            primitive.ObjectID `bson:"_id" json:"id"`
	CohortID      primitive.ObjectID `bson:"cohort_id" json:"cohortId"`
	ProjectSiteID primitive.ObjectID `bson:"project_site_id" json:"projectSiteId"`
	EnvID         primitive.ObjectID `bson:"env_id" json:"envId"`
	Subject       string             `bson:"subject" json:"subject"`
	Group         string             `bson:"group" json:"group"`
	RegisterGroup string             `bson:"register_group" json:"registerGroup"`
	RandomNumber  string             `bson:"random_number" json:"randomNumber"`
	Room          string             `bson:"room" json:"room"`
	RandomTime    time.Duration      `bson:"random_time" json:"randomTime"`
	JoinTime      string             `bson:"join_time" json:"joinTime"`
}

// initExportContext 初始化导出上下文
func initExportContext(ctx *gin.Context, projectID string, envID string, cohortIDs []string, templateId string, roleId string, now time.Time) (*ExportContext, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	// 创建上下文
	exportCtx := &ExportContext{
		Ctx:               ctx,
		ProjectID:         projectID,
		EnvID:             envID,
		CohortIDs:         cohortIDs,
		TemplateId:        templateId,
		RoleId:            roleId,
		Now:               now,
		EnvOID:            envOID,
		ProjectOID:        projectOID,
		ShowRandomNumber:  make(map[primitive.ObjectID]bool),
		ShowRegisterGroup: make(map[primitive.ObjectID]bool),
	}

	// 解析请求参数获取受试者ID
	var parameter map[string]interface{}
	if err := ctx.ShouldBindBodyWith(&parameter, binding.JSON); err != nil {
		return nil, errors.WithStack(err)
	}

	if subjects, ok := parameter["subject"].([]interface{}); ok {
		exportCtx.Subjects = make([]primitive.ObjectID, 0, len(subjects))
		for _, subject := range subjects {
			if subjectStr, ok := subject.(string); ok {
				subjectOID, _ := primitive.ObjectIDFromHex(subjectStr)
				exportCtx.Subjects = append(exportCtx.Subjects, subjectOID)
			}
		}
	}

	// 获取属性配置
	attributes, err := fetchAttributes(exportCtx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	exportCtx.Attributes = attributes

	// 设置随机号和注册组显示
	for _, attribute := range attributes {
		ok := true
		if len(cohortIDs) > 0 {
			_, ok = slice.Find(cohortIDs, func(index int, cohortID string) bool {
				return cohortID == attribute.CohortID.Hex()
			})
		}

		if attribute.AttributeInfo.IsRandomNumber && ok {
			exportCtx.ShowRandomNumber[attribute.CohortID] = true
		}
		if attribute.AttributeInfo.AllowRegisterGroup && ok {
			exportCtx.ShowRegisterGroup[attribute.CohortID] = true
		}
	}

	// 设置受试者替换文本
	if len(attributes) > 0 {
		exportCtx.SubjectReplaceText = GetSubjectReplaceText(ctx, attributes[0])
	}

	// 获取访视周期
	visitCycles, err := fetchVisitCycles(exportCtx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 按队列过滤访视周期
	if len(cohortIDs) > 0 {
		visitCycles = slice.Filter(visitCycles, func(index int, item models.VisitCycle) bool {
			_, ok := slice.Find(cohortIDs, func(index int, cohortID string) bool {
				return cohortID == item.CohortID.Hex()
			})
			return ok
		})
	}

	exportCtx.VisitCycles = visitCycles

	// 获取项目信息
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	exportCtx.Project = project

	// 获取cohortMap
	envP, ok := slice.Find(exportCtx.Project.Environments, func(index int, environment models.Environment) bool {
		return envOID == environment.ID
	})
	if !ok {
		return nil, errors.WithStack(err)
	}
	env := *envP

	envCohortMap := map[primitive.ObjectID]models.Cohort{}
	for _, cohort := range env.Cohorts {
		envCohortMap[cohort.ID] = cohort
	}
	exportCtx.CohortMap = envCohortMap

	var timeZone float64
	if exportCtx.Project.TimeZoneStr != "" {
		timeZone, _ = strconv.ParseFloat(exportCtx.Project.TimeZoneStr, 64)
	} else {
		timeZone = float64(8)
	}
	exportCtx.TimeZone = timeZone

	// 获取其他配置信息
	// 获取盲态药物映射
	blindMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	exportCtx.BlindMedicineMap = blindMap
	// 获取国家信息
	countries, err := database.GetCountries(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	exportCtx.Countries = countries
	// 检查角色是否盲态
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	exportCtx.IsBlindedRole = isBlindedRole
	exportCtx.RoomShowBlind = isBlindedRole

	// 获取表单和公式配置
	if err := fetchFormsAndFormulas(exportCtx); err != nil {
		return nil, err
	}

	// 国家配置
	var logisticsCompanyCodes []models.LogisticsCompanyCode
	cursor, err := tools.Database.Collection("logistics_company_code").Find(nil, bson.M{})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &logisticsCompanyCodes)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	mapLogisticsCompanyCode := map[string]string{"qita": locales.Tr(ctx, "order_logistics_9")}
	for _, item := range logisticsCompanyCodes {
		mapLogisticsCompanyCode[item.Code] = item.Name
	}
	exportCtx.MapLogisticsCompanyCode = mapLogisticsCompanyCode
	return exportCtx, nil
}

// fetchAttributes 获取属性配置
func fetchAttributes(exportCtx *ExportContext) ([]models.Attribute, error) {
	attributeFilter := bson.M{"env_id": exportCtx.EnvOID}

	cursor, err := tools.Database.Collection("attribute").Find(nil, attributeFilter, &options.FindOptions{
		Sort: bson.D{{"_id", 1}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var attributes []models.Attribute
	if err = cursor.All(nil, &attributes); err != nil {
		return nil, errors.WithStack(err)
	}

	return attributes, nil
}

// fetchVisitCycles 获取访视周期
func fetchVisitCycles(exportCtx *ExportContext) ([]models.VisitCycle, error) {
	attributeFilter := bson.M{"env_id": exportCtx.EnvOID}

	cursor, err := tools.Database.Collection("visit_cycle").Find(nil, attributeFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var visitCycles []models.VisitCycle
	if err = cursor.All(nil, &visitCycles); err != nil {
		return nil, errors.WithStack(err)
	}

	return visitCycles, nil
}

// fetchFormsAndFormulas 获取表单和公式配置
func fetchFormsAndFormulas(exportCtx *ExportContext) error {
	// 获取模板
	if exportCtx.TemplateId != "" {
		templateOID, _ := primitive.ObjectIDFromHex(exportCtx.TemplateId)
		var template models.CustomTemplate
		err := tools.Database.Collection("custom_template").FindOne(nil, bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return errors.WithStack(err)
		}
		exportCtx.Template = &template
	}

	// 获取表单配置
	cohortOIDs := make([]primitive.ObjectID, 0)
	for _, v := range exportCtx.CohortIDs {
		cohortOID, _ := primitive.ObjectIDFromHex(v)
		cohortOIDs = append(cohortOIDs, cohortOID)
	}

	formMatch := bson.M{"env_id": exportCtx.EnvOID, "fields": bson.M{"$ne": nil}}
	if len(exportCtx.CohortIDs) > 0 {
		formMatch["cohort_id"] = bson.M{"$in": cohortOIDs}
	}

	cursor, err := tools.Database.Collection("form").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: formMatch}},
		{{Key: "$unwind", Value: "$fields"}},
		{{Key: "$match", Value: bson.M{"fields.application_type": bson.M{"$in": bson.A{2, 3}}}}},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if err = cursor.All(nil, &exportCtx.Forms); err != nil {
		return errors.WithStack(err)
	}

	// 获取公式配置
	filter := bson.M{"env_id": exportCtx.EnvOID}
	if len(exportCtx.CohortIDs) > 0 {
		filter["cohort_id"] = bson.M{"$in": cohortOIDs}
	}

	cursor, err = tools.Database.Collection("customer_report_title").Find(nil, filter)
	if err != nil {
		return errors.WithStack(err)
	}

	if err = cursor.All(nil, &exportCtx.CustomerReportTitles); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

type SubjectDispensing struct {
	ProjectSiteInfo  `json:",inline" bson:",inline"`
	SubjectBasicInfo `json:",inline" bson:",inline"`
	DispensingInfo   []DispensingInfo `json:"dispensing" bson:"dispensing"`
}

// fetchDispensingData 获取发药数据
func fetchDispensingData(ctx *gin.Context, exportCtx *ExportContext) ([]SubjectDispensing, error) {
	projectSite, err := fetchProjectSites(ctx, exportCtx.EnvOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	subjects, err := fetchSubjects(ctx, exportCtx.Subjects)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	dispensings, err := fetchDispensing(ctx, exportCtx.Subjects)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 获取订单数据
	orderInfo, err := fetchMedicineOrder(ctx, exportCtx.EnvOID)
	exportCtx.OrderInfo = orderInfo
	// 整合数据
	datas := make([]SubjectDispensing, 0, len(subjects))
	for _, subject := range subjects {
		datas = append(datas, SubjectDispensing{
			ProjectSiteInfo:  projectSite[subject.ProjectSiteID],
			SubjectBasicInfo: subject,
			DispensingInfo:   dispensings[subject.ID],
		})
	}

	return datas, nil
}

func fetchMedicineOrder(ctx *gin.Context, envOID primitive.ObjectID) (map[primitive.ObjectID]models.LogisticsInfo, error) {
	type OrderInfo struct {
		ID            primitive.ObjectID   `json:"id" bson:"_id"`
		LogisticsInfo models.LogisticsInfo `json:"logistics_info" bson:"logistics_info"`
	}
	var orders []OrderInfo

	opt := options.FindOptions{
		Projection: bson.M{
			"_id":            1,
			"logistics_info": 1,
		},
	}
	cursor, err := tools.Database.Collection("medicine_order").Find(nil, bson.M{"env_id": envOID, "type": bson.M{"$in": bson.A{5, 6}}}, &opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &orders)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	orderMap := map[primitive.ObjectID]models.LogisticsInfo{}
	for _, order := range orders {
		orderMap[order.ID] = order.LogisticsInfo
	}

	return orderMap, nil
}

// processUserInfo 处理用户信息
func processUserInfo(exportCtx *ExportContext, subjectDispensing []SubjectDispensing) error {
	// 收集用户ID
	userIdSet := make(map[primitive.ObjectID]bool)
	userIds := make([]primitive.ObjectID, 0, 100) // 预分配容量

	for _, subject := range subjectDispensing {
		for _, dispensingInfo := range subject.DispensingInfo {
			for _, h := range dispensingInfo.History {
				if !userIdSet[h.UID] {
					userIds = append(userIds, h.UID)
					userIdSet[h.UID] = true
				}
			}
		}
	}

	// 如果没有用户ID，直接返回
	if len(userIds) == 0 {
		exportCtx.UserMap = make(map[primitive.ObjectID]int32)
		return nil
	}

	// 查询用户信息
	users := make([]models.User, 0, len(userIds))
	cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
	if err != nil {
		return errors.WithStack(err)
	}

	if err = cursor.All(nil, &users); err != nil {
		return errors.WithStack(err)
	}

	// 构建用户映射
	userMap := make(map[primitive.ObjectID]int32, len(users))
	for _, u := range users {
		userMap[u.ID] = u.Unicode
	}

	exportCtx.UserMap = userMap
	return nil
}

func fetchProjectSites(ctx *gin.Context, envOID primitive.ObjectID) (map[primitive.ObjectID]ProjectSiteInfo, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"env_id": envOID}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "region",
			"localField":   "region_id",
			"foreignField": "_id",
			"as":           "region",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"_id":      1,
			"country":  bson.M{"$first": "$country"},
			"number":   "$number",
			"name":     models.ProjectSiteNameLookUpBson(ctx),
			"timeZone": "$time_zone",
			"tz":       "$tz",
			"region":   "$region.name",
		}}},
	}

	var sites []ProjectSiteInfo
	cursor, err := tools.Database.Collection("project_site").Aggregate(ctx, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = cursor.All(ctx, &sites)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := make(map[primitive.ObjectID]ProjectSiteInfo, len(sites))
	for i := range sites {
		result[sites[i].ID] = sites[i]
	}

	return result, nil
}

func fetchSubjects(ctx *gin.Context, subjectIDs []primitive.ObjectID) ([]SubjectBasicInfo, error) {
	if len(subjectIDs) == 0 {
		return nil, nil
	}

	subjectPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": subjectIDs}, "deleted": bson.M{"$ne": true}}}},
		{{Key: "$project", Value: bson.M{
			"_id":             1,
			"cohort_id":       1,
			"project_site_id": 1,
			"env_id":          1,
			"subject":         bson.M{"$first": "$info.value"},
			"group":           1,
			"register_group":  1,
			"random_number":   1,
			"room":            "$room_number",
		}}},
	}

	var subjectData []SubjectBasicInfo
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, subjectPipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return subjectData, nil
}

type DispensingInfo struct {
	ID                           primitive.ObjectID                   `bson:"_id"`
	SubjectID                    primitive.ObjectID                   `bson:"subject_id"`
	SerialNumber                 int                                  `bson:"serial_number"`
	Status                       int                                  `bson:"status"`
	VisitInfo                    models.VisitInfo                     `bson:"visit_info"`
	DispensingTime               time.Duration                        `bson:"dispensing_time"`
	VisitSign                    bool                                 `bson:"visit_sign"`
	Reissue                      int                                  `bson:"reissue"`
	Medicine                     []models.DispensingMedicine          `bson:"dispensing_medicines"`
	OtherMedicines               []models.Medicine                    `bson:"other_dispensing_medicine"`
	ReplaceMedicines             []models.ReplaceMedicines            ` bson:"replace_medicines"`               // 被替换研究产品
	ReplaceOtherMedicines        []models.OtherDispensingMedicineInfo ` bson:"replace_other_medicines"`         // 被替换研究产品
	OtherDispensingMedicines     []models.OtherDispensingMedicine     ` bson:"other_dispensing_medicines"`      // 未编号研究产品
	CancelMedicinesHistory       []models.DispensingMedicine          ` bson:"cancel_medicines_history"`        // 未编号研究产品
	RealDispensingMedicines      []models.DispensingMedicine          ` bson:"real_dispensing_medicines"`       // 实际发放药物
	RealOtherDispensingMedicines []models.OtherDispensingMedicineInfo ` bson:"real_other_dispensing_medicines"` // 实际发放药物
	Medicines                    []models.DispensingMedicineAndOther  `bson:"medicines"`
	Form                         []models.CustomerFormula             `bson:"form"`
	Label                        string                               `bson:"label"`
	Labels                       []string                             `bson:"labels"`
	FormulaInfo                  *models.FormulaInfo                  `bson:"formula_info"`
	DoseInfo                     *models.DoseLevel                    `bson:"dose_info"`
	DoseForm                     *models.FormValue                    `bson:"dose_form"`
	History                      []models.History                     `bson:"history"`
}

func fetchDispensing(ctx *gin.Context, subjectIDs []primitive.ObjectID) (map[primitive.ObjectID][]DispensingInfo, error) {
	dispensingPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"subject_id": bson.M{"$in": subjectIDs}}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "history",
			"localField":   "_id",
			"foreignField": "oid",
			"as":           "history",
		}}},
	}
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, dispensingPipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var dispensings []DispensingInfo
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	dispensingMap := make(map[primitive.ObjectID][]DispensingInfo, len(dispensings))
	for _, dispensing := range dispensings {
		if dispensingMap[dispensing.SubjectID] == nil {
			dispensingMap[dispensing.SubjectID] = []DispensingInfo{}
		}
		dispensingMap[dispensing.SubjectID] = append(dispensingMap[dispensing.SubjectID], dispensing)
	}
	return dispensingMap, nil
}

// generateExcelReport 生成Excel报表
func generateExcelReport(ctx *gin.Context, exportCtx *ExportContext, datas []SubjectDispensing) (string, []byte, error) {

	mapPeriod, formulaBSATitle, formulaBSAKey, err := getPeriodAndBSAKey(ctx, exportCtx, datas)
	if err != nil {
		return "", nil, err
	}

	// 准备报表标题和字段
	title, fieldsArray, fields, fieldCol, doseFieldCol, err := prepareReportFields(ctx, exportCtx, formulaBSATitle)
	if err != nil {
		return "", nil, err
	}

	// 处理报表数据
	content, registerContent, err := processReportData(ctx, exportCtx, datas, mapPeriod, fields, fieldsArray, fieldCol, doseFieldCol, formulaBSAKey)
	if err != nil {
		return "", nil, err
	}

	// 创建Excel文件
	fileName := fmt.Sprintf("%s[%s]DispenseReport_%s.xlsx", exportCtx.Project.Number, "env", exportCtx.Now.Format("20060102150405"))
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")

	// 使用流式写入提高性能
	if err := writeExcelSheet(f, "Sheet1", title, content); err != nil {
		return "", nil, err
	}

	// 写入第二个工作表
	sheetName := locales.Tr(exportCtx.Ctx, "report.attributes.dispensing.sheet.actual")
	f.NewSheet(sheetName)
	if err := writeExcelSheet(f, sheetName, title, registerContent); err != nil {
		return "", nil, err
	}

	// 输出Excel文件
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil
}

// prepareReportFields 准备报表字段
func prepareReportFields(ctx *gin.Context, exportCtx *ExportContext, formulaBSATitle []interface{}) ([]interface{}, []string, map[string]bool, []string, []string, error) {
	// 实现字段准备逻辑...
	title := []interface{}{}
	fieldsArray := []string{}
	fields := map[string]bool{}
	doseFieldCol := []string{}
	fieldCol := []string{}
	subjectText := GetSubjectReplaceText(ctx, exportCtx.Attributes[0])

	// 使用模板或默认字段
	if exportCtx.TemplateId != "" {
		template := exportCtx.Template
		for _, field := range template.Fields {
			// colIdx := fmt.Sprintf("%s", string('A'+col%26))
			// if col > 25 {
			// 	colIdx = "A" + colIdx
			// }
			if field == data.ReportAttributesInfoSubjectNumber {
				title = append(title, subjectText)
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				// fieldCol[field] = colIdx
				// col++
				continue
			}
			if field == data.ReportAttributesRandomNumber && len(exportCtx.ShowRandomNumber) == 0 {
				continue
			}
			if field == data.ReportAttributesDispensingMedicineRealGroup && len(exportCtx.ShowRegisterGroup) == 0 {
				continue
			}

			// 剂量表单
			if field == data.ReportAttributesDispensingDoseFormulas {

				fieldOID := exportCtx.EnvOID
				for _, form := range exportCtx.Forms {
					fields := form["fields"].(map[string]interface{})
					if int(fields["application_type"].(int32)) != 3 {
						continue
					}
					label := fields["label"]
					variable := fields["variable"]
					status := fields["status"].(int32)
					statusStr := ""
					if status == 2 {
						statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
					}
					formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
					title = append(title, formTitle)
					if exportCtx.Project.Type != 1 {
						fieldOID = form["cohort_id"].(primitive.ObjectID)
					} else {
						fieldOID = exportCtx.EnvOID
					}
					doseFieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
					doseFieldCol = append(doseFieldCol, doseFieldColTitle)
					//col++
				}
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				continue
			}
			//公式计算字段
			if field == data.ReportAttributesDispensingUseFormulas {

				if len(exportCtx.CustomerReportTitles) > 0 {
					// 公式发药变量
					title = append(title, formulaBSATitle...)

					fieldOID := exportCtx.EnvOID
					for _, form := range exportCtx.Forms {
						fields := form["fields"].(map[string]interface{})
						if int(fields["application_type"].(int32)) != 2 {
							continue
						}
						label := fields["label"]
						variable := fields["variable"]
						status := fields["status"].(int32)
						statusStr := ""
						if status == 2 {
							statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
						}
						formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
						title = append(title, formTitle)
						if exportCtx.Project.Type != 1 {
							fieldOID = form["cohort_id"].(primitive.ObjectID)
						} else {
							fieldOID = exportCtx.EnvOID
						}
						fieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
						fieldCol = append(fieldCol, fieldColTitle)
						//col++
					}

					for _, customerReportTitle := range exportCtx.CustomerReportTitles {
						for _, useFormula := range customerReportTitle.Title {
							t := fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), useFormula)
							if useFormula == "age" || useFormula == "weight" {
								t = fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), locales.Tr(ctx, "export.dispensing."+useFormula))
							}
							title = append(title, t)
							fieldCol = append(fieldCol, useFormula)
							//fieldCol[useFormula] = useFormula
							//col++
						}
					}

					fieldsArray = append(fieldsArray, field)
					fields[field] = true

				}
				continue
			}

			// 计划外原因
			if field == data.ReportAttributesDispensingOutVisitDispensingReason {
				unscheduledReasonTitle := locales.Tr(ctx, field)
				if len(exportCtx.VisitCycles) > 0 {
					visitCycle := exportCtx.VisitCycles[0]
					if visitCycle.SetInfo.IsOpen {
						if ctx.GetHeader("Accept-Language") == "en" {
							unscheduledReasonTitle = visitCycle.SetInfo.NameEn + " " + locales.Tr(ctx, "operator.reason")

						} else {
							unscheduledReasonTitle = visitCycle.SetInfo.NameZh + locales.Tr(ctx, "operator.reason")
						}
					}
				}
				title = append(title, unscheduledReasonTitle)
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				continue
			}

			//计划外备注
			if field == data.ReportAttributesDispensingOutVisitDispensingRemark {
				unscheduledReasonTitle := locales.Tr(ctx, field)
				if len(exportCtx.VisitCycles) > 0 {
					visitCycle := exportCtx.VisitCycles[0]
					if visitCycle.SetInfo.IsOpen {
						if ctx.GetHeader("Accept-Language") == "en" {
							unscheduledReasonTitle = visitCycle.SetInfo.NameEn + "-" + locales.Tr(ctx, "common.remark")
						} else {
							unscheduledReasonTitle = visitCycle.SetInfo.NameZh + "-" + locales.Tr(ctx, "common.remark")
						}
					}
				}
				title = append(title, unscheduledReasonTitle)
				fieldsArray = append(fieldsArray, field)
				fields[field] = true
				continue

			}

			title = append(title, locales.Tr(ctx, field))
			fieldsArray = append(fieldsArray, field)
			fields[field] = true
		}
	} else {
		double := false
		for _, field := range data.DispenseReport.MultiDefaultFields {

			if field.Type == exportCtx.Project.Type && !double {
				double = true
				for _, defaultField := range field.DefaultFields {
					// 字段有计划时间和超窗时间

					if defaultField.Key == data.ReportAttributesInfoSubjectNumber {
						title = append(title, exportCtx.SubjectReplaceText)
						fieldsArray = append(fieldsArray, defaultField.Key)
						fields[defaultField.Key] = true
						// fieldCol[defaultField.Key] = colIdx
						// col++
						continue
					}
					if defaultField.Key == data.ReportAttributesRandomNumber && len(exportCtx.ShowRandomNumber) == 0 {
						continue
					}

					if defaultField.Key == data.ReportAttributesDispensingMedicineRealGroup && len(exportCtx.ShowRegisterGroup) == 0 {
						continue
					}

					if defaultField.Key == data.ReportAttributesDispensingDoseFormulas {
						// 剂量表单
						fieldOID := exportCtx.EnvOID
						for _, form := range exportCtx.Forms {
							fields := form["fields"].(map[string]interface{})
							if fields["application_type"] != 3 {
								continue
							}
							label := fields["label"]
							variable := fields["variable"]
							status := fields["status"].(int32)
							statusStr := ""
							if status == 2 {
								statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
							}
							formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
							title = append(title, formTitle)
							if exportCtx.Project.Type != 1 {
								fieldOID = form["cohort_id"].(primitive.ObjectID)
							} else {
								fieldOID = exportCtx.EnvOID
							}
							fieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
							fieldCol = append(fieldCol, fieldColTitle)
							//col++
						}
					}

					//公式计算
					if defaultField.Key == data.ReportAttributesDispensingUseFormulas {
						//公式
						if len(exportCtx.CustomerReportTitles) > 0 {
							title = append(title, formulaBSATitle...)
							fieldOID := exportCtx.EnvOID
							for _, form := range exportCtx.Forms {
								fields := form["fields"].(map[string]interface{})
								label := fields["label"]
								variable := fields["variable"]
								status := fields["status"].(int32)
								statusStr := ""
								if status == 2 {
									statusStr = "-" + locales.Tr(ctx, "operation_log.form.status_invalid")
								}
								formTitle := fmt.Sprintf("%s(%s)%s", label, variable, statusStr)
								title = append(title, formTitle)
								if exportCtx.Project.Type != 1 {
									fieldOID = form["cohort_id"].(primitive.ObjectID)
								} else {
									fieldOID = exportCtx.EnvOID
								}
								fieldColTitle := fmt.Sprintf("%s%s", fieldOID.Hex(), variable)
								fieldCol = append(fieldCol, fieldColTitle)
								//fieldCol[fieldColTitle] = fieldColTitle
								//col++
							}

							for _, customerReportTitle := range exportCtx.CustomerReportTitles {
								for _, useFormula := range customerReportTitle.Title {
									t := fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), useFormula)

									if useFormula == "age" || useFormula == "weight" {
										t = fmt.Sprintf("%s(%s)", locales.Tr(ctx, "system_suggest_value"), locales.Tr(ctx, "export.dispensing."+useFormula))
									}
									title = append(title, t)
									fieldCol = append(fieldCol, useFormula)
									//fieldCol[useFormula] = useFormula
									//col++
								}
							}

							fieldsArray = append(fieldsArray, defaultField.Key)
							fields[defaultField.Key] = true

						}
						continue
					}
					//计划外备注
					if defaultField.Key == data.ReportAttributesDispensingOutVisitDispensingRemark {
						unscheduledReasonTitle := locales.Tr(ctx, defaultField.Key)
						if len(exportCtx.VisitCycles) > 0 {
							visitCycle := exportCtx.VisitCycles[0]
							if visitCycle.SetInfo.IsOpen {
								if ctx.GetHeader("Accept-Language") == "en" {
									unscheduledReasonTitle = visitCycle.SetInfo.NameEn + " " + locales.Tr(ctx, "common.remark")
								} else {
									unscheduledReasonTitle = visitCycle.SetInfo.NameZh + locales.Tr(ctx, "common.remark")
								}
							}
						}
						title = append(title, unscheduledReasonTitle)
						fieldsArray = append(fieldsArray, defaultField.Key)
						fields[defaultField.Key] = true
						continue

					}
					// fieldCol[defaultField.Key] = colIdx
					// col++
					title = append(title, locales.Tr(ctx, defaultField.Key))
					fieldsArray = append(fieldsArray, defaultField.Key)
					fields[defaultField.Key] = true
				}
			}
		}
	}

	return title, fieldsArray, fields, fieldCol, doseFieldCol, nil
}

// processReportData 处理报表数据
func processReportData(ctx *gin.Context, exportCtx *ExportContext, subjectDispensings []SubjectDispensing, mapPeriod map[primitive.ObjectID]models.Period, fields map[string]bool, fieldsArray []string, fieldCol []string, doseFieldCol []string, formulaBSAKey []string) ([][]interface{}, [][]interface{}, error) {
	// 实现数据处理逻辑...
	content := make([][]interface{}, 0)
	registerContent := make([][]interface{}, 0)
	for _, subjectInfo := range subjectDispensings {
		roomShow := exportCtx.RoomShowBlind
		attributeP, _ := slice.Find(exportCtx.Attributes, func(index int, data models.Attribute) bool {
			return subjectInfo.CohortID == data.CohortID
		})
		attribute := *attributeP
		if !attribute.AttributeInfo.Blind {
			roomShow = false
		}

		visitCycle := models.VisitCycle{}
		visitCycleP, ok := slice.Find(exportCtx.VisitCycles, func(index int, data models.VisitCycle) bool {
			return subjectInfo.CohortID == data.CohortID
		})
		if ok {
			visitCycle = *visitCycleP
		}

		country := ""
		region := ""
		site := ""
		siteName := ""
		room := ""
		subject := ""
		randomNumber := ""
		visit := ""
		operateTime := ""
		operateTypeStr := ""
		number := ""
		replace := ""
		name := ""
		otherNumber := ""
		realNumber := ""
		registerGroup := ""
		operator := ""
		mark := ""
		unscheduledReason := ""
		reissueReason := ""
		cohort := ""
		batch := ""
		expire := ""
		packageNumber := ""
		label := ""
		doseLevel := ""
		planTime := ""
		outSize := ""
		reissueRemark := ""
		unscheduledRemark := ""
		replaceRemark := ""
		retrieveRemark := ""
		registerRemark := ""
		invalidRemark := ""
		sendType := locales.Tr(ctx, "history.dispensing.send-type-0")
		logistics := ""
		logisticsRemark := ""
		var tmp []interface{}
		operateType := 0 // 操作类型

		for _, dispensingInfo := range subjectInfo.DispensingInfo {
			for _, medicine := range dispensingInfo.Medicines {
				isNumber := true
				if medicine.Count != 0 {
					isNumber = false
				}

				// 国家
				{
					if attribute.AttributeInfo.CountryLayered {
						if subjectInfo.Country != "" {
							country = exportCtx.Countries[subjectInfo.Country].(string)
						}
					}
				}
				// 区域
				{
					if attribute.AttributeInfo.RegionLayered {
						if subjectInfo.Region != "" {
							region = subjectInfo.Region
						}
					}
				}
				// 国家
				{
					if attribute.AttributeInfo.IsRandomNumber {
						randomNumber = subjectInfo.RandomNumber
					}
				}
				site = subjectInfo.ProjectSiteInfo.Number   // 中心编号
				siteName = subjectInfo.ProjectSiteInfo.Name // 中心名称
				// 房间号
				{
					if subjectInfo.Room != "" && !roomShow {
						room = subjectInfo.Room
					}
				}
				subject = subjectInfo.Subject // 受试者

				// 访视名称-访视类型
				{
					if dispensingInfo.VisitSign {
						if dispensingInfo.Reissue == 1 {
							visit = dispensingInfo.VisitInfo.Number + "-" + locales.Tr(ctx, "export.dispensing.reissue")

						} else {
							outPlan := locales.Tr(ctx, "export.dispensing.outVisit")
							if visitCycle.SetInfo.IsOpen {
								if ctx.GetHeader("Accept-Language") == "zh" {
									outPlan = visitCycle.SetInfo.NameZh
								} else {
									outPlan = visitCycle.SetInfo.NameEn
								}
							}
							visit = dispensingInfo.VisitInfo.Number + "-" + outPlan
						}
					} else {
						visit = dispensingInfo.VisitInfo.Number
					}
				}

				// 操作时间
				{
					if dispensingInfo.DispensingTime != 0.0 || medicine.Time != 0 {
						if medicine.Time != 0 {
							//

							if subjectInfo.TZ != "" {
								//siteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
								//var dataTime string
								//dataTime = time.Unix(time.Duration(item["medicines"].(map[string]interface{})["time"].(int64)).Nanoseconds(), 0).UTC().Add(time.Hour * time.Duration(siteTimeZone)).Format("2006-01-02 15:04:05")
								//dataTime = dataTime + "(" + item["timeZone"].(string) + ")"

								timeStr, err := tools.GetLocationUtc(subjectInfo.TZ, int64(medicine.Time))
								if err != nil {
									panic(err)
								}

								operateTime = timeStr
							} else {
								hours := time.Duration(exportCtx.TimeZone)
								minutes := time.Duration((exportCtx.TimeZone - float64(hours)) * 60)
								duration := hours*time.Hour + minutes*time.Minute

								var dataTime string
								dataTime = time.Unix(time.Duration(medicine.Time).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
								strTimeZone := tools.FormatOffsetToZoneString(exportCtx.TimeZone)
								dataTime = dataTime + "(" + strTimeZone + ")"
								operateTime = dataTime
							}

						} else {

							// 旧数据 无药物时间的 使用发药时间

							if subjectInfo.TZ != "" {
								//SiteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
								//var dataTime string
								//dataTime = time.Unix(item["time"].(int64), 0).UTC().Add(time.Hour * time.Duration(SiteTimeZone)).Format("2006-01-02 15:04:05")
								//dataTime = dataTime + "(" + item["timeZone"].(string) + ")"

								timeStr, err := tools.GetLocationUtc(subjectInfo.TZ, int64(dispensingInfo.DispensingTime))
								if err != nil {
									panic(err)
								}

								operateTime = timeStr

							} else {

								hours := time.Duration(exportCtx.TimeZone)
								minutes := time.Duration((exportCtx.TimeZone - float64(hours)) * 60)
								duration := hours*time.Hour + minutes*time.Minute

								var dataTime string
								dataTime = time.Unix(int64(dispensingInfo.DispensingTime), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
								strTimeZone := tools.FormatOffsetToZoneString(exportCtx.TimeZone)
								dataTime = dataTime + "(" + strTimeZone + ")"
								operateTime = dataTime

							}

						}
					}
				}

				// 操作类型
				{
					if exportCtx.Project.ResearchAttribute == 0 {
						// 通用模式
						if !(medicine.Type == 0 || medicine.Type == 8 || medicine.Type == 9) {
							if medicine.Type == 1 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.first")
								operateType = 1
							}
							if medicine.Type == 2.0 || dispensingInfo.Reissue == 1 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue")
								operateType = 2
							}
							if medicine.Type == 3.0 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
								operateType = 3
							}
							// 记录取回数据
							if medicine.Type == 6.0 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.retrieve")
								operateType = 6
							}
							if medicine.Type == 7.0 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.cancel")
								operateType = 7
							}

							if medicine.Type == 4.0 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.register")
								operateType = 4
							}

						} else if medicine.Type == 0 || medicine.Type == 8 || medicine.Type == 9 { // 旧数据处理

							if dispensingInfo.VisitSign {
								if dispensingInfo.Reissue == 1 {
									operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue")
									operateType = 2
								} else {
									operateTypeStr = locales.Tr(ctx, "export.dispensing.first")
									operateType = 1
								}
							} else {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.first")
								operateType = 1
							}
							if (medicine.Type == 8 || medicine.Type == 9) && medicine.BeInfo != nil && medicine.BeInfo.Name != "" {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.register")
								operateType = 4
							}
						}
					} else {
						// DTP 模式
						if !(medicine.Type == 0 || medicine.Type == 8 || medicine.Type == 9) {
							if medicine.Type == 1 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.visit_apply")
							}
							if medicine.Type == 2 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue_apply")
							}
							if medicine.Type == 3 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.replace_apply")
							}
							if medicine.Type == 5 {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.out_visit_apply")
							}
						} else if medicine.Type == 0 || medicine.Type == 8 || medicine.Type == 9 {
							if dispensingInfo.VisitSign {
								if dispensingInfo.Reissue == 1.0 {
									operateTypeStr = locales.Tr(ctx, "export.dispensing.reissue_apply")
								} else {
									operateTypeStr = locales.Tr(ctx, "export.dispensing.out_visit_apply")
								}
							} else {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.visit_apply")
							}
							if (medicine.Type == 8 || medicine.Type == 9) && !medicine.MedicineOtherID.IsZero() {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.register")
								operateType = 4
							}
						}
					}
				}

				// 计划访视时间
				{
					if mapPeriod[dispensingInfo.ID].LineTime != "" && operateType == 1 {
						planTime = mapPeriod[dispensingInfo.ID].LineTime
						if mapPeriod[dispensingInfo.ID].OutSize {
							outSize = locales.Tr(ctx, "common.yes")
						} else {
							outSize = locales.Tr(ctx, "common.no")
						}
					}
				}

				// 研究产品编号
				if medicine.Number != "" {
					number = medicine.Number
				}

				//包装号

				if medicine.PackageNumber != "" {
					packageNumber = medicine.PackageNumber
				}

				// 批次号
				{
					if medicine.Batch != "" {
						batch = medicine.Batch
					}
					if medicine.BatchNumber != "" {
						batch = medicine.BatchNumber
					}
				}

				// 有效期
				{
					if medicine.ExpirationDate != "" {
						expire = medicine.ExpirationDate
					}
					if medicine.ExpireDate != "" {
						expire = medicine.ExpireDate
					}
				}

				// 未编号研究产品数量
				if !isNumber {
					otherNumber = fmt.Sprintf("%d", medicine.Count)
				}

				// 已替换研究产品号
				if isNumber {
					oldNumber := ""
					if len(dispensingInfo.ReplaceMedicines) > 0 {
						for _, replaceMedicine := range dispensingInfo.ReplaceMedicines {
							if replaceMedicine.NewNumber == medicine.Number {
								if medicine.Type != 0 {
									if medicine.Type != 6 && medicine.Type != 7 {
										operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
										operateType = 3
									}
								} else {
									operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
									operateType = 3

								}
								oldNumber = replaceMedicine.Number
							}
						}
					}
					replace = oldNumber
				} else {
					if len(dispensingInfo.ReplaceOtherMedicines) > 0 {
						for _, replaceMedicine := range dispensingInfo.ReplaceOtherMedicines {
							if replaceMedicine.Time == medicine.Time &&
								replaceMedicine.MedicineOtherID == medicine.MedicineOtherID {
								if exportCtx.IsBlindedRole && exportCtx.BlindMedicineMap[replaceMedicine.BeInfo.Name] {
									replaceMedicine.BeInfo.Name = tools.BlindData
								}
								replace = replaceMedicine.BeInfo.Name + "/" + otherNumber + "/" + replaceMedicine.BeInfo.ExpireDate + "/" + replaceMedicine.BeInfo.Batch
								operateTypeStr = locales.Tr(ctx, "export.dispensing.replace")
								break
							}
						}
					}
				}

				// 研究产品名称
				{
					name = medicine.Name
					if !exportCtx.IsBlindedRole || !exportCtx.BlindMedicineMap[name] {
						name = medicine.Name
					} else {
						name = tools.BlindData
					}
				}

				// 发药标签
				{
					if operateType != 3 {
						label = medicine.Label
						if medicine.Label == "" && medicine.Type == 0 && medicine.NewNumber != "" { // 替换药物需查找关联的
							replaceMedicineP, ok := slice.Find(dispensingInfo.Medicine, func(index int, it models.DispensingMedicine) bool {
								return it.MedicineID == medicine.MedicineNewID
							})
							if ok {
								replaceMedicine := *replaceMedicineP
								label = replaceMedicine.Label
							}
						}
					}
				}

				// 实际使用产品
				if isNumber {
					{
						var realMedicines string

						if operateType == 4 {
							realMedicineP, ok := slice.Find(dispensingInfo.CancelMedicinesHistory, func(index int, it models.DispensingMedicine) bool {
								return medicine.RealMedicineID == it.MedicineID
							})
							if ok {
								realMedicine := *realMedicineP
								realMedicines = realMedicine.Number
							}
						}

						realNumber = realMedicines
					}
				} else {
					if medicine.BeInfo != nil && operateType != 3 {
						medicines := medicine
						realName := medicines.Name
						if exportCtx.IsBlindedRole && exportCtx.BlindMedicineMap[realName] {
							realName = tools.BlindData
						}
						realExpireDate := medicines.BeInfo.ExpireDate
						realBatch := medicines.BeInfo.Batch
						realNumber = medicine.BeInfo.Name + "/" + convertor.ToString(medicines.BeInfo.Count) + "/" + realExpireDate + "/" + realBatch
					}
				}

				fieldValue := map[string]string{}
				if len(dispensingInfo.Form) > 0 {
					for _, formInfo := range dispensingInfo.Form {
						key := formInfo.Key
						value := strconv.FormatFloat(formInfo.Value, 'f', -1, 64)

						formP, ok := slice.Find(exportCtx.Forms, func(index int, formInfo map[string]interface{}) bool {
							return formInfo["env_id"] == subjectInfo.EnvID && formInfo["cohort_id"] == subjectInfo.CohortID && formInfo["fields"].(map[string]interface{})["variable"] == key
						})
						if ok {
							form := *formP
							if value != "" {
								if form["fields"].(map[string]interface{})["type"].(string) == "inputNumber" &&
									form["fields"].(map[string]interface{})["formatType"].(string) == "decimalLength" &&
									form["fields"].(map[string]interface{})["length"].(float64) != 0 {
									lengthString := strconv.FormatFloat(form["fields"].(map[string]interface{})["length"].(float64), 'f', -1, 64)
									if strings.Contains(lengthString, ".") {
										digits, _ := getFractionDigits(form["fields"].(map[string]interface{})["length"].(float64))
										str := strconv.FormatFloat(formInfo.Value, 'f', digits, 64)
										value = str
									} else {
										value = strconv.FormatFloat(formInfo.Value, 'f', -1, 64)
									}
								} else {
									value = strconv.FormatFloat(formInfo.Value, 'f', -1, 64)
								}
							}
						}
						OID := exportCtx.EnvOID
						if attribute.CohortID != primitive.NilObjectID {
							OID = attribute.CohortID
						}
						fkey := fmt.Sprintf("%s%s", OID.Hex(), key)
						fieldValue[fkey] = value
					}
				}
				if medicine.DoseInfo != nil && medicine.DoseInfo.Form != nil {
					OID := exportCtx.EnvOID
					if attribute.CohortID != primitive.NilObjectID {
						OID = attribute.CohortID
					}
					fkey := fmt.Sprintf("%s%s", OID.Hex(), medicine.DoseInfo.Form.Key)

					fieldValue[fkey] = medicine.DoseInfo.Form.Value

				}

				// 发药水平
				{

					{
						if medicine.DoseInfo != nil &&
							medicine.DoseInfo.DoseLevelList != nil {
							doseLevel = medicine.DoseInfo.DoseLevelList.Name
						}

						if medicine.DoseInfo != nil {
							doseInfo := medicine.DoseInfo
							doseForm := doseInfo.Form
							key := doseForm.Key
							value := doseForm.Value
							OID := exportCtx.EnvOID
							if attribute.CohortID != primitive.NilObjectID {
								OID = attribute.CohortID
							}
							fkey := fmt.Sprintf("%s%s", OID.Hex(), key)
							formP, ok := slice.Find(exportCtx.Forms, func(index int, formInfo map[string]interface{}) bool {
								return formInfo["env_id"] == subjectInfo.EnvID && formInfo["cohort_id"] == subjectInfo.CohortID && formInfo["fields"].(map[string]interface{})["variable"] == key
							})
							if ok {
								form := *formP
								optionP, _ := slice.Find(form["fields"].(map[string]interface{})["options"].(primitive.A), func(index int, formItem interface{}) bool {
									return formItem.(map[string]interface{})["value"] == value
								})
								option := *optionP
								if doseInfo.DoseLevelList != nil {
									fieldValue[fkey] = locales.Tr(ctx, option.(map[string]interface{})["label"].(string))
								} else {
									fieldValue[fkey] = option.(map[string]interface{})["label"].(string)
								}
							}
						}
					}
				}

				// 实际用药组别
				{
					if len(medicine.RegisterGroup) != 0 {
						for _, groupValue := range medicine.RegisterGroup {
							if subjectInfo.Group != "" && subjectInfo.Group != groupValue {
								registerGroup = strings.Join(medicine.RegisterGroup, " ")
							}

						}
						if exportCtx.IsBlindedRole && attribute.AttributeInfo.Blind && registerGroup != "" {
							registerGroup = tools.BlindData
						}
					}
				}

				for _, history := range dispensingInfo.History {
					keys := []string{"dispensing.dispensing", "dispensing.dtp-dispensing"}
					if operateType == 1 || operateType == 2 {
						keys = []string{"dispensing.dispensing", "dispensing.dtp-dispensing",
							"dispensing.reissue", "dispensing.dtp-reissue", "dispensingCustomer-reissue"}
					} else if operateType == 3 {
						keys = []string{"dispensing.replace", "dispensingCustomer-replace"}
					} else if operateType == 4 {
						keys = []string{"dispensing.register", "dispensingCustomer-register"}
					} else if operateType == 6 {
						keys = []string{"dispensing.retrieval", "dispensingCustomer-retrieval"}
					}
					//if history.Key == "history.dispensing.invalid" && history.Data["remark"] != nil {
					//	invalidRemark = history.Data["remark"].(string)
					//}
					for _, key := range keys {
						if strings.Contains(history.Key, key) {
							unicode := exportCtx.UserMap[history.UID]
							operator = fmt.Sprintf("%s(%d)", history.User, unicode)
							if history.Data["remark"] != nil && operateType == 1 {
								mark = history.Data["remark"].(string)
							}

							if operateType == 1 && history.Data["reason"] != nil {
								unscheduledReason = history.Data["reason"].(string)
							} else if operateType == 2 && history.Data["remark"] != nil {
								reissueReason = history.Data["remark"].(string)
							}

							// 新轨迹
							if operateType == 1 || operateType == 2 || operateType == 3 || operateType == 4 || operateType == 6 {
								if len(history.CustomTemps) > 0 && history.Time == medicine.Time {
									for _, customTemps := range history.CustomTemps {
										for _, customTempOptions := range customTemps.CustomTempOptions {
											if customTempOptions.Key == "history.dispensing.single.remark" {
												if operateType == 1 {
													if dispensingInfo.VisitSign {
														unscheduledRemark = customTempOptions.Data["remark"].(string)
													} else {
														mark = customTempOptions.Data["remark"].(string)
													}
												}
												if operateType == 2 { // 旧数据处理
													reissueRemark = customTempOptions.Data["remark"].(string)
												}
												if operateType == 3 {
													replaceRemark = customTempOptions.Data["remark"].(string)
												}
												if operateType == 4 {
													registerRemark = customTempOptions.Data["remark"].(string)
												}
												if operateType == 6 {
													retrieveRemark = customTempOptions.Data["remark"].(string)
												}
											}
											if customTempOptions.Key == "history.dispensing.single.reasonDispensingVisit" || customTempOptions.Key == "history.dispensing.single.reasonDispensingVisitCustomer" {
												if operateType == 1 {
													unscheduledReason = customTempOptions.Data["reasonDispensingVisit"].(string)
												}
												if operateType == 2 { // 旧数据处理
													reissueReason = customTempOptions.Data["reasonDispensingVisit"].(string)
												}
											}
											if customTempOptions.Key == "history.dispensing.single.reasonReissue" {
												reissueReason = customTempOptions.Data["reasonDispensingVisit"].(string)

											}

											if customTempOptions.Key == "history.dispensing.single.sendType" {
												sendType = locales.Tr(ctx, customTempOptions.TransData)
											}
										}
									}
								}

							}

							if operateType == 3 {

								// 替换操作，需要匹配对应的key + number
								if history.Data["medicine"] != nil {
									for _, medicine := range history.Data["medicine"].(primitive.A) {
										if medicine.(string) == number || medicine.(string) == otherNumber {
											operator = fmt.Sprintf("%s(%d)", history.User, unicode)
											break
										}
									}
								}

							} else if operateType == 4 {
								operator = fmt.Sprintf("%s(%d)", history.User, unicode)

								// TODO登记操作，需要匹配对应的key + number
								medicines := history.Data["medicine"]
								if medicines == number {
									operator = fmt.Sprintf("%s(%d)", history.User, unicode)
									break
								}

							}
						}

					}
				}

				if &medicine.Key != nil {
					if medicine.Key != "history.dispensing.invalid" && medicine.Key != "history.dispensing.resume" && medicine.Key != "" {
						continue
					} else {
						if medicine.Key == "history.dispensing.invalid" || medicine.Key == "history.dispensing.resume" {
							unicode := exportCtx.UserMap[medicine.UID]
							operator = fmt.Sprintf("%s(%d)", medicine.User, unicode)
							if medicine.Data["remark"] != nil {
								invalidRemark = medicine.Data["remark"].(string)
							}
							if medicine.Key == "history.dispensing.invalid" {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.invalid")
							} else if medicine.Key == "history.dispensing.resume" {
								operateTypeStr = locales.Tr(ctx, "export.dispensing.recover")
							}
							outSize = ""
							sendType = ""
						}
					}
				}

				// 发放方式
				{
					if medicine.DTP != 0 {
						sendType = locales.Tr(ctx, "history.dispensing.send-type-"+convertor.ToString(medicine.DTP))
					}
				}

				// 物流
				{
					if medicine.OrderOID != nil {
						logisticsInfo := exportCtx.OrderInfo[*medicine.OrderOID]
						if logisticsInfo.Number != "" {
							logistics = exportCtx.MapLogisticsCompanyCode[logisticsInfo.Logistics] + "/" + logisticsInfo.Number
						}

					}
				}

				// 物流
				{
					if medicine.OrderOID != nil {
						logisticsInfo := exportCtx.OrderInfo[*medicine.OrderOID]
						logisticsRemark = logisticsInfo.Other
					}
				}

				// 过滤字段
				for _, field := range fieldsArray {
					{
						//项目 "report.attributes.project"
						if field == data.ReportAttributesProjectNumber {
							tmp = append(tmp, exportCtx.Project.ProjectInfo.Number)
						}
						if field == data.ReportAttributesProjectName {
							tmp = append(tmp, exportCtx.Project.ProjectInfo.Name)
						}
						//国家
						if field == data.ReportAttributesInfoCountry {
							tmp = append(tmp, country)
						}
						//区域
						if field == data.ReportAttributesInfoRegion {
							tmp = append(tmp, region)
						}
						//中心编号
						if field == data.ReportAttributesInfoSiteNumber {
							tmp = append(tmp, site)
						}
						//中心名称
						if field == data.ReportAttributesInfoSiteName {
							tmp = append(tmp, siteName)

						}
						//房间号
						if field == data.ReportAttributesDispensingRoom {
							tmp = append(tmp, room)

						}

						//群组
						if field == data.ReportAttributesRandomCohort {
							cohort = exportCtx.CohortMap[subjectInfo.CohortID].Name
							tmp = append(tmp, cohort)
						}

						//受试者号
						if field == data.ReportAttributesInfoSubjectNumber {
							tmp = append(tmp, subject)

						}

						//阶段
						if field == data.ReportAttributesRandomStage {
							cohort = exportCtx.CohortMap[subjectInfo.CohortID].Name
							tmp = append(tmp, cohort)
						}

						//随机号
						if field == data.ReportAttributesRandomNumber {
							tmp = append(tmp, randomNumber)

						}
						//访视周期
						if field == data.ReportAttributesDispensingCycleName {
							tmp = append(tmp, visit)

						}
						//发药操作类型
						if field == data.ReportAttributesDispensingType {
							tmp = append(tmp, operateTypeStr)

						}

						//计划访视时间
						if field == data.ReportAttributesDispensingPlanTime {
							tmp = append(tmp, planTime)
						}

						//发药操作时间
						if field == data.ReportAttributesDispensingTime {
							tmp = append(tmp, operateTime)

						}

						//发药操作人
						if field == data.ReportAttributesDispensingOperator {
							tmp = append(tmp, operator)

						}
						//研究产品编号
						if field == data.ReportAttributesDispensingMedicine {
							tmp = append(tmp, number)
						}

						//研究产品名称
						if field == data.ReportAttributesDispensingDrugName {
							tmp = append(tmp, name)
						}

						//发药标签
						if field == data.ReportAttributesDispensingLabel {
							if operateType == 6 { // 取回操作不展示发药标签
								tmp = append(tmp, "")
							} else {
								tmp = append(tmp, label)
							}
						}
						//发药标签
						if field == data.ReportAttributesDispensingDose {
							if operateType == 6 { // 取回操作不展示发药标签
								tmp = append(tmp, "")
							} else {
								tmp = append(tmp, doseLevel)
							}
						}

						//已替换研究产品编号
						if field == data.ReportAttributesDispensingMedicineReplace {
							tmp = append(tmp, replace)

						}
						//实际使用研究产品
						if field == data.ReportAttributesDispensingMedicineReal {
							tmp = append(tmp, realNumber)

						}

						//实际使用研究产品
						if field == data.ReportAttributesDispensingMedicineRealGroup {
							tmp = append(tmp, registerGroup)
						}

						//未编号研究产品数量
						if field == data.ReportAttributesDispensingDrugOtherNumber {
							tmp = append(tmp, otherNumber)

						}

						//发药备注
						if field == data.ReportAttributesDispensingRemark {
							tmp = append(tmp, mark)

						}
						//计划外发药原因
						if field == data.ReportAttributesDispensingOutVisitDispensingReason {
							tmp = append(tmp, unscheduledReason)

						}
						//补发原因
						if field == data.ReportAttributesDispensingReissueReason {
							tmp = append(tmp, reissueReason)

						}
						//批次号
						if field == data.ReportAttributesResearchBatch {
							tmp = append(tmp, batch)

						}
						//有效期
						if field == data.ReportAttributesResearchExpireDate {
							tmp = append(tmp, expire)
						}
						//包装号
						if field == data.ReportAttributesResearchPackageNumber {
							tmp = append(tmp, packageNumber)
						}

						//是否超窗
						if field == data.ReportAttributesDispensingOutSize {
							tmp = append(tmp, outSize)
						}

						// 剂量表单
						if field == data.ReportAttributesDispensingDoseFormulas {
							for _, v := range doseFieldCol {
								if operateType == 1 { // 取回操作不展示发药标签

									if fieldValue[v] == "form.control.type.options.one" || fieldValue[v] == "form.control.type.options.two" || fieldValue[v] == "form.control.type.options.three" {
										fieldValue[v] = locales.Tr(ctx, fieldValue[v])
									}

									tmp = append(tmp, fieldValue[v])
								} else {
									tmp = append(tmp, "")

								}

							}
						}

						//公式计算
						if field == data.ReportAttributesDispensingUseFormulas {

							var formulasKey string
							var formulasValue float64
							if isNumber {
								if medicine.Time != 0 {
									if medicine.UseFormulas != nil {
										useFormulas := medicine.UseFormulas
										formulasKey = useFormulas.Key
										formulasValue = useFormulas.Value
									}
								}
							} else {
								if len(dispensingInfo.OtherDispensingMedicines) > 0 {
									otherMedicines := dispensingInfo.OtherDispensingMedicines
									if len(otherMedicines) > 0 {
										for _, otherMedicine := range otherMedicines {
											if otherMedicine.Name == medicine.Name && otherMedicine.UseFormulas != nil {
												useFormulas := otherMedicine.UseFormulas
												formulasKey = useFormulas.Key
												formulasValue = useFormulas.Value
											}
										}
									}
								}
							}
							for _, key := range formulaBSAKey {
								value := ""
								if dispensingInfo.FormulaInfo != nil {
									formula := dispensingInfo.FormulaInfo

									if formula.Weight != nil && key == "weight" {
										value = convertor.ToString(formula.Weight)
									}
									if formula.Age != nil && key == "age" {
										value = convertor.ToString(formula.Age)
									}
									if formula.Height != nil && key == "height" {
										value = convertor.ToString(formula.Height)
									}
								}
								tmp = append(tmp, value)
							}

							for _, v := range fieldCol {
								value, exists := fieldValue[v]
								if !exists && v == formulasKey {
									value = strconv.FormatFloat(formulasValue, 'f', -1, 64)
								}
								tmp = append(tmp, value)
							}
						}

						//计划外备注
						if field == data.ReportAttributesDispensingOutVisitDispensingRemark {
							tmp = append(tmp, unscheduledRemark)
						}

						//计划外备注
						if field == data.ReportAttributesDispensingMedicineReissueRemark {
							tmp = append(tmp, reissueRemark)
						}

						// 替换备注
						if field == data.ReportAttributesDispensingMedicineReplaceRemark {
							tmp = append(tmp, replaceRemark)
						}
						// 取回备注
						if field == data.ReportAttributesDispensingMedicineRegisterRemark {
							tmp = append(tmp, retrieveRemark)
						}
						// 登记备注
						if field == data.ReportAttributesDispensingMedicineRegisterRemark {
							tmp = append(tmp, registerRemark)
						}

						// 不参加访视-备注
						if field == data.ReportAttributesDispensingMedicineInvalidRemark {
							tmp = append(tmp, invalidRemark)
						}
						// 发放方式
						if field == data.ReportAttributesDispensingMedicineSendType {
							tmp = append(tmp, sendType)
						}
						// 物流
						if field == data.ReportAttributesDispensingMedicineLogisticsInfo {
							tmp = append(tmp, logistics)
						}
						// 物流备注
						if field == data.ReportAttributesDispensingMedicineLogisticsRemark {
							tmp = append(tmp, logisticsRemark)
						}
					}
				}

				if dispensingInfo.DispensingTime != 0 || medicine.Time != 0 {
					content = append(content, tmp)
					if registerGroup != "" {
						registerContent = append(registerContent, tmp)
					}
				}
			}
		}

	}

	// 处理每条数据...

	return content, registerContent, nil
}

// writeExcelSheet 写入Excel工作表
func writeExcelSheet(f *excelize.File, sheetName string, title []interface{}, content [][]interface{}) error {
	streamWriter, err := f.NewStreamWriter(sheetName)
	if err != nil {
		return errors.WithStack(err)
	}

	// 写入标题行
	t := make([]interface{}, len(title))
	for i, item := range title {
		t[i] = excelize.Cell{Value: item}
	}
	if err := streamWriter.SetRow("A1", t); err != nil {
		return errors.WithStack(err)
	}

	// 批量写入数据行
	for i := 1; i <= len(content); i++ {
		r := make([]interface{}, len(content[i-1]))
		for j := 0; j < len(content[i-1]); j++ {
			r[j] = excelize.Cell{Value: content[i-1][j]}
		}
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		if err := streamWriter.SetRow(cell, r); err != nil {
			return errors.WithStack(err)
		}
	}

	return streamWriter.Flush()
}

func getPeriodAndBSAKey(ctx *gin.Context, exportCtx *ExportContext, subjectDispensing []SubjectDispensing) (map[primitive.ObjectID]models.Period, []interface{}, []string, error) {
	var err error

	titlesKey := []string{}
	titlesKeyMap := map[string]bool{}
	titles := []interface{}{}
	mapPeriod := map[primitive.ObjectID]models.Period{}
	// 查询访视配置
	visitCycles := exportCtx.VisitCycles
	// 查询所有中心

	// TODO 7064
	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
			if err != nil {
				return nil, nil, nil, errors.WithStack(err)
			}
		}
	}

	for _, subject := range subjectDispensing {

		intTimeZone := exportCtx.TimeZone

		projectSite := subject.ProjectSiteInfo
		if projectSite.TZ != "" {
			offsetString, err := tools.GetLocationFloat(projectSite.TZ)
			if err != nil {
				return nil, nil, nil, errors.WithStack(err)
			}
			intTimeZone = offsetString
		}

		afterRandom := false
		interval := float64(0)
		lastTime := time.Duration(0)
		visitCycleP, ok := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
			return item.CohortID == subject.CohortID
		})

		if !ok {
			continue
		}

		attributeP, ok := slice.Find(exportCtx.Attributes, func(index int, item models.Attribute) bool {
			return item.CohortID == subject.CohortID
		})
		if !ok {
			continue
		}

		visitCycle := *visitCycleP
		attribute := *attributeP
		randomTime := subject.SubjectBasicInfo.RandomTime
		firstTime := time.Duration(0)
		for _, dispensing := range subject.DispensingInfo {

			if !titlesKeyMap["weight"] && dispensing.FormulaInfo.Height != nil {
				titles = append(titles, locales.Tr(ctx, "export.dispensing.weight"))
				titlesKey = append(titlesKey, "weight")
				titlesKeyMap["weight"] = true
			}
			if dispensing.FormulaInfo.Height != nil {
				titles = append(titles, locales.Tr(ctx, "export.dispensing.height"))
				titlesKey = append(titlesKey, "height")
				titlesKeyMap["height"] = true
			}
			if dispensing.FormulaInfo.Height != nil {
				titles = append(titles, locales.Tr(ctx, "export.dispensing.age"))
				titlesKey = append(titlesKey, "age")
				titlesKeyMap["age"] = true

			}
			if !dispensing.VisitSign {
				visitCycleInfo := models.VisitCycleInfo{}
				visitCycleInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
					return item.Number == dispensing.VisitInfo.Number
				})
				if ok {
					visitCycleInfo = *visitCycleInfoP
				}
				// 计算窗口期最小窗口时间
				// TODO 7064
				if subjectMap[subject.Subject].RandomTime != 0 {
					subject.SubjectBasicInfo.RandomTime = subjectMap[subject.Subject].RandomTime
				}

				if firstTime == 0 && dispensing.DispensingTime != 0 && visitCycleInfo.Interval != nil && randomTime == 0 {
					randomTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfo.Interval)).Unix())
					firstTime = dispensing.DispensingTime
				}
				period := handlePeriod(afterRandom, visitCycle.VisitType, visitCycleInfo, randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
				if dispensing.Status == 2 {
					interval = 0
					lastTime = dispensing.DispensingTime
				}
				if dispensing.VisitInfo.Random {
					afterRandom = true
				}
				mapPeriod[dispensing.ID] = period
			}
		}

	}
	return mapPeriod, titles, titlesKey, nil
}
