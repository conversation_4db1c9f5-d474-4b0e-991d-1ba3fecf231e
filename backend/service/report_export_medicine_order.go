package service

import (
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func ExportShipmentOrdersReport(ctx *gin.Context, projectID string, envID string, roleID string, templateId string, now time.Time, sendIds []string, receiveIds []string) (string, []byte, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)

	var orderData []map[string]interface{}

	// project
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}

	//判断当前登录的角色的分类
	var role models.ProjectRolePermission
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&role)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	user, err := tools.Me(ctx)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	match := bson.M{"user_id": user.ID, "env_id": envOID}
	var instituteIds []primitive.ObjectID
	if role.Scope == "depot" {
		//如果当前角色的分类是depot,查看该用户分配的仓库，
		var userDepots []models.UserDepot
		cursor, err := tools.Database.Collection("user_depot").Find(nil, match)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &userDepots)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, userDepot := range userDepots {
			instituteIds = append(instituteIds, userDepot.DepotID)
		}
	} else if role.Scope == "site" {
		//如果当前角色的分类是site，查看该用户分配的中心
		var userSites []models.UserSite
		cursor, err := tools.Database.Collection("user_site").Find(nil, match)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
		err = cursor.All(nil, &userSites)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, userSite := range userSites {
			instituteIds = append(instituteIds, userSite.SiteID)
		}
	}

	if (role.Scope == "depot" || role.Scope == "site") && len(instituteIds) <= 0 {

	} else {
		baseFilter := bson.M{"env_id": envOID}
		filter := bson.M{"env_id": envOID}

		if len(sendIds) > 0 {
			var sendOIDs []primitive.ObjectID
			for _, id := range sendIds {
				oid, _ := primitive.ObjectIDFromHex(id)
				sendOIDs = append(sendOIDs, oid)
			}
			filter["send_id"] = bson.M{"$in": sendOIDs}
		} else {
			if len(instituteIds) > 0 {
				if role.Scope == "depot" {
					filter["send_id"] = bson.M{"$in": instituteIds}
				}
			}
		}
		if len(receiveIds) > 0 {
			var receiveOIDs []primitive.ObjectID
			for _, id := range receiveIds {
				oid, _ := primitive.ObjectIDFromHex(id)
				receiveOIDs = append(receiveOIDs, oid)
			}
			//filter["receive_id"] = receiveOId
			filter["$or"] = bson.A{
				bson.M{"receive_id": bson.M{"$in": receiveOIDs}},
				bson.M{"subject_id": bson.M{"$in": receiveOIDs}},
			}
		} else {
			if len(instituteIds) > 0 {
				if role.Scope == "site" {
					filter["$or"] = bson.A{
						bson.M{"receive_id": bson.M{"$in": instituteIds}},
						bson.M{"subject.project_site_id": bson.M{"$in": instituteIds}},
					}
					//filter["receive_id"] = bson.M{"$in": instituteIds}
				}
			}
		}

		// var sendOIDs []primitive.ObjectID
		// for _, id := range sendIds {
		// 	oid, _ := primitive.ObjectIDFromHex(id)
		// 	if role.Scope == "depot" || role.Scope == "site" {
		// 		contains := arrays.Contains(instituteIds, oid)
		// 		if contains != -1 {
		// 			sendOIDs = append(sendOIDs, oid)
		// 		}
		// 	} else {
		// 		sendOIDs = append(sendOIDs, oid)
		// 	}
		// }
		// var receiveOIDs []primitive.ObjectID
		// for _, id := range receiveIds {
		// 	oid, _ := primitive.ObjectIDFromHex(id)
		// 	if role.Scope == "depot" || role.Scope == "site" {
		// 		contains := arrays.Contains(instituteIds, oid)
		// 		if contains != -1 {
		// 			receiveOIDs = append(receiveOIDs, oid)
		// 		}
		// 	} else {
		// 		receiveOIDs = append(receiveOIDs, oid)
		// 	}
		// }

		// if len(instituteIds) > 0 {
		// 	if role.Scope == "depot" {
		// 		if len(sendOIDs) > 0 {
		// 			filter["send_id"] = bson.M{"$in": sendOIDs}
		// 		} else {
		// 			filter["send_id"] = bson.M{"$in": instituteIds}
		// 		}
		// 	}
		// 	if role.Scope == "site" {
		// 		if len(receiveOIDs) > 0 {
		// 			filter["$or"] = bson.A{
		// 				bson.M{"receive_id": bson.M{"$in": receiveOIDs}},
		// 				bson.M{"subject_id": bson.M{"$in": receiveOIDs}},
		// 			}
		// 		} else {
		// 			filter["$or"] = bson.A{
		// 				bson.M{"receive_id": bson.M{"$in": instituteIds}},
		// 				bson.M{"subject_id": bson.M{"$in": instituteIds}},
		// 			}
		// 		}
		// 	}
		// }

		// if role.Scope == "study" {
		// 	if len(sendOIDs) > 0 {
		// 		filter["send_id"] = bson.M{"$in": sendOIDs}
		// 	}
		// 	if len(receiveOIDs) > 0 {
		// 		filter["$or"] = bson.A{
		// 			bson.M{"receive_id": bson.M{"$in": receiveOIDs}},
		// 			bson.M{"subject_id": bson.M{"$in": receiveOIDs}},
		// 		}
		// 	}
		// }

		filter["type"] = bson.M{"$in": [4]int{1, 2, 5, 6}}

		pipepine := mongo.Pipeline{
			{{Key: "$match", Value: baseFilter}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "confirm_by", "foreignField": "_id", "as": "confirmUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "close_by", "foreignField": "_id", "as": "closeUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "send_by", "foreignField": "_id", "as": "sendUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "lost_by", "foreignField": "_id", "as": "lostUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "end_by", "foreignField": "_id", "as": "endUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "receive_by", "foreignField": "_id", "as": "receiveUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "canceller_by", "foreignField": "_id", "as": "cancellerUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "meta.created_by", "foreignField": "_id", "as": "meta.createdUser"}}},
			{{Key: "$lookup", Value: bson.M{
				"from": "subject", // 关联的集合名称
				"let": bson.M{
					"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
				},
				"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
					bson.M{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": bson.A{
									bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
									bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
								},
							},
						},
					},
				},
				"as": "subject", // 关联结果的字段名
			}}},
			{{Key: "$unwind", Value: bson.M{"path": "$subject", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: bson.M{"from": "medicine", "localField": "medicines", "foreignField": "_id", "as": "m"}}},
			{{Key: "$unwind", Value: bson.M{"path": "$m", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$sort", Value: bson.D{{"m.expiration_date", 1}, {Key: "m.number", Value: 1}}}},
			{{Key: "$group", Value: bson.M{"_id": "$_id", "order": bson.M{"$first": "$$ROOT"}, "mediciness": bson.M{"$push": "$m"}}}},
			{{Key: "$replaceRoot", Value: bson.M{"newRoot": bson.M{"$mergeObjects": bson.A{bson.M{"mediciness": "$mediciness"}, "$order"}}}}},
			{{Key: "$sort", Value: bson.D{{"sort_index", 1}, {"meta.created_at", -1}}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "history",
				"localField":   "_id",
				"foreignField": "oid",
				"as":           "history",
			}}},
		}
		optTrue := true
		opt := &options.AggregateOptions{
			AllowDiskUse: &optTrue,
		}
		cursor, err := tools.Database.Collection("medicine_order").Aggregate(nil, pipepine, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
		err = cursor.All(nil, &orderData)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
	}
	otherMedicineIds := make([]primitive.ObjectID, 0)
	otherMedicines := make([]models.OtherMedicine, 0)
	otherMedicinesMap := make(map[primitive.ObjectID]models.OtherMedicine)
	for _, d := range orderData {
		if d["other_medicines_new"] != nil && len(d["other_medicines_new"].(primitive.A)) > 0 {
			ids := d["other_medicines_new"].(primitive.A)
			for _, id := range ids {
				oid := id.(primitive.ObjectID)
				otherMedicineIds = append(otherMedicineIds, oid)
			}
		}
	}
	otherMedicineIds = slice.Unique(otherMedicineIds)
	if len(otherMedicineIds) > 0 {
		cursor, err := tools.Database.Collection("medicine_others").Find(nil, bson.M{"_id": bson.M{"$in": otherMedicineIds}})
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &otherMedicines)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, medicine := range otherMedicines {
			otherMedicinesMap[medicine.ID] = medicine
		}
	}

	//包装
	type otherPackageNumber struct {
		ID            primitive.ObjectID `bson:"_id"`
		PackageNumber int                `bson:"package_number"`
		PackageCount  int                `bson:"package_count"`
	}

	otherPackageNumbers := make([]otherPackageNumber, 0)
	packageMatch := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	packagePipepine := mongo.Pipeline{
		{{Key: "$match", Value: packageMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":            1,
				"package_number": 1,
				"package_count":  1,
			},
		}},
	}
	otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = otherPackageAll.All(nil, &otherPackageNumbers)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	otherPackageNumberMap := make(map[string]int, len(otherPackageNumbers))
	for _, number := range otherPackageNumbers {
		packageNumber := strconv.Itoa(number.PackageNumber)
		otherPackageNumberMap[packageNumber] = number.PackageCount
	}
	instituteMatch := bson.M{
		"env_id": envOID,
	}
	//查询该项目下的仓库
	storehouses := make(map[primitive.ObjectID]string)
	var storeHouseData []map[string]interface{}
	storeHousePipeline := mongo.Pipeline{
		{{Key: "$match", Value: instituteMatch}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "storehouse",
		}}},
		{{
			Key: "$project",
			Value: bson.M{
				"value": "$_id",
				"_id":   0,
				"label": bson.M{
					"$arrayElemAt": bson.A{"$storehouse.name", 0},
				},
			},
		}},
	}
	cursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, storeHousePipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &storeHouseData)
	if err != nil {
		return "", nil, errors.WithStack(err)

	}
	for _, storehouse := range storeHouseData {
		storehouses[storehouse["value"].(primitive.ObjectID)] = storehouse["label"].(string)
	}

	//查询该项目下的中心
	sites := make(map[primitive.ObjectID]string)
	var siteData []map[string]interface{}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: instituteMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"value":  "$_id",
				"_id":    0,
				"number": 1,
				"label":  models.ProjectSiteNameBson(ctx),
			},
		}},
	}
	siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = siteCursor.All(nil, &siteData)
	if err != nil {
		return "", nil, errors.WithStack(err)

	}
	for _, site := range siteData {
		sites[site["value"].(primitive.ObjectID)] = site["number"].(string) + "-" + site["label"].(string)
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return "", nil, errors.WithStack(err)

	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	var bytes []byte

	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	_ = streamWriter.SetColWidth(1, 14, 20)

	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	if templateId != "" {
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		var template models.CustomTemplate
		err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, err
		}

		mergeCol := []string{"A", "B"}

		fieldCol := map[string]string{}
		var col = int32(2)

		title := []any{locales.Tr(ctx, "report.attributes.project.number"), locales.Tr(ctx, "report.attributes.project.name")}
		title2 := []any{}
		//订单详情标识
		orderInfoFlag := false
		orderInfoFields := []string{}
		orderInfoBegin := ""

		for _, field := range template.Fields {
			if field == "report.attributes.project.number" || field == "report.attributes.project.name" {
				continue
			}

			if field == "report.attributes.research" || field == "report.attributes.research.packageNumber" || field == "report.attributes.research.packageMethod" ||
				field == "report.attributes.research.batch" || field == "report.attributes.research.expireDate" || field == "report.attributes.research.status" || field == "report.attributes.research.other" {
				orderInfoFields = append(orderInfoFields, field)
				continue
			}

			//c、d、e……
			colIdx := fmt.Sprintf("%s", string('A'+col%26))
			if col > 25 {
				colIdx = "A" + colIdx
			}
			fieldCol[field] = colIdx
			col++

			switch field {
			case "report.attributes.order.number":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_number"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.status":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_status"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.send":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_sendInstitute"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.receive":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_receiveInstitute"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.medicineQuantity":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_count"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.create.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_createUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.create.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_createDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.cancel.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_cancelUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.cancel.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_cancelDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.cancel.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_cancelReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.confirm.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_confirmUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.confirm.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_confirmDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.close.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_closeUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.close.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_closeDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.close.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_closeReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.send.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_sendUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.send.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_sendDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.receive.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_receiveUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.receive.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_receiveDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.expectedArrivalTime":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_expectedArrivalTime"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.actualReceiptTime":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_actualReceiptTime"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.lost.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_lostUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.lost.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_lostDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.lost.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_lostReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.end.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_endUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.end.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_endDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.end.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_endReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.supplier":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_supplier"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.supplier.other":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_supplierOther"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.supplier.number":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_supplierNumber"))
				mergeCol = append(mergeCol, colIdx)
			}
		}

		for _, field := range orderInfoFields {

			//c、d、e……
			colIdx := fmt.Sprintf("%s", string('A'+col%26))
			if col > 25 {
				colIdx = "A" + colIdx
			}
			fieldCol[field] = colIdx
			col++

			switch field {
			case "report.attributes.research":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_medicine"))
			case "report.attributes.research.packageNumber":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_packageNumber"))
			case "report.attributes.research.packageMethod":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_packageMethod"))
			case "report.attributes.research.batch":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_batchNumber"))
			case "report.attributes.research.expireDate":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_expiredDate"))
			case "report.attributes.research.status":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicine_status"))
			case "report.attributes.research.other":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_other"))
			}
		}

		if orderInfoFlag {
			title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
			if orderInfoBegin == "" {
				colIdx := fmt.Sprintf("%s", string('A'+(len(title)-1)%26))
				if col > 25 {
					colIdx = "A" + colIdx
				}
				orderInfoBegin = colIdx
			}
		}

		//设置第一行title
		t := make([]any, len(title))
		for i, item := range title {
			t[i] = excelize.Cell{Value: item}
		}
		_ = streamWriter.SetRow("A1", t)

		axisIndex := 2
		if orderInfoFlag && len(title2) > 0 {
			t2 := make([]any, len(title)+len(title2))
			for i, item := range title2 {
				t2[i+len(title)-1] = excelize.Cell{Value: item}
			}
			//处理订单详情合并
			title1Hcell, _ := excelize.CoordinatesToCellName(len(t), 1)
			title1Vcell, _ := excelize.CoordinatesToCellName(len(t2)-1, 1)
			_ = streamWriter.MergeCell(title1Hcell, title1Vcell)
			_ = streamWriter.SetRow("A2", t2)
			//处理订单字段一二行合并
			for i := 0; i < len(title)-1; i++ {
				hcell, _ := excelize.CoordinatesToCellName(i+1, 1)
				vcell, _ := excelize.CoordinatesToCellName(i+1, 2)
				_ = streamWriter.MergeCell(hcell, vcell)
			}
			axisIndex = 3
		}

		for i := 0; i < len(orderData); i++ {
			//订单类型
			sendId := orderData[i]["send_id"].(primitive.ObjectID)
			sendLabel := ""
			receiveId := orderData[i]["receive_id"].(primitive.ObjectID)
			receiveLabel := ""
			orderType := int(orderData[i]["type"].(int32))

			if orderType == 1 || orderType == 3 || orderType == 5 { //发送发是仓库
				sendLabel = storehouses[sendId]
			} else { //发送发是中心
				sendLabel = sites[sendId]
			}

			if orderType == 1 || orderType == 2 || orderType == 6 { //接收方是中心
				receiveLabel = sites[receiveId]
			} else { //接收方是仓库
				receiveLabel = storehouses[receiveId]
			}

			//未编码研究产品的数量
			otherCount := 0
			if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
				otherCount = len(orderData[i]["other_medicines_new"].(primitive.A))
			}

			max := axisIndex
			_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "A", axisIndex), []any{project.ProjectInfo.Number})
			_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "B", axisIndex), []any{project.ProjectInfo.Name})
			orderInfoFields := []string{}
			for _, field := range template.Fields {
				switch field {
				case "report.attributes.order.number":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{orderData[i]["order_number"]})
				case "report.attributes.order.status":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{data.GetOrderStatus(ctx, int(orderData[i]["status"].(int32)))})
				case "report.attributes.order.send":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{sendLabel})
				case "report.attributes.order.receive":
					if orderData[i]["subject"] != nil {
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{orderData[i]["subject"].(map[string]interface{})["info"].(primitive.A)[0].(map[string]interface{})["value"]})
					} else {
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{receiveLabel})
					}
				case "report.attributes.order.medicineQuantity":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{len(orderData[i]["mediciness"].(primitive.A)) + otherCount})
				case "report.attributes.order.create.by":
					createUserName := "System（Automatic Order）"
					if len(orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)) > 0 {
						createUserName = orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{createUserName})
				case "report.attributes.order.create.time":
					createdAt := orderData[i]["meta"].(map[string]interface{})["created_at"].(int64)
					createdAtStr := time.Unix(createdAt, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{createdAtStr})
				case "report.attributes.order.cancel.by":
					cancellerUserName := ""
					cancellerUser := orderData[i]["cancellerUser"].(primitive.A)
					if len(cancellerUser) > 0 && int(orderData[i]["status"].(int32)) == 5 {
						cancellerUserName = cancellerUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{cancellerUserName})
				case "report.attributes.order.cancel.time":
					cancellerUser := orderData[i]["cancellerUser"].(primitive.A)
					cancellerAtStr := ""
					if len(cancellerUser) > 0 && int(orderData[i]["status"].(int32)) == 5 {
						cancellerAt := orderData[i]["canceller_at"]
						if cancellerAt != nil {
							cancellerAtStr = time.Unix(cancellerAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{cancellerAtStr})
				case "report.attributes.order.cancel.reason":
					var cancelReason interface{}
					if int(orderData[i]["status"].(int32)) == 5 {
						cancelReason = orderData[i]["reason"]
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{cancelReason})
				case "report.attributes.order.confirm.by":
					confirmUser := orderData[i]["confirmUser"].(primitive.A)
					confirmUserName := ""
					if len(confirmUser) > 0 {
						confirmUserName = confirmUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.confrim-new" || history.(map[string]any)["key"].(string) == "history.order.confrim" {
								confirmUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{confirmUserName})
				case "report.attributes.order.confirm.time":
					confirmUser := orderData[i]["confirmUser"].(primitive.A)
					confirmAtStr := ""
					if len(confirmUser) > 0 {
						confirmAt := orderData[i]["confirm_at"]
						if confirmAt != nil {
							confirmAtStr = time.Unix(confirmAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.confrim-new" || history.(map[string]any)["key"].(string) == "history.order.confrim" {
								confirmAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{confirmAtStr})
				case "report.attributes.order.close.by":
					closeUser := orderData[i]["closeUser"].(primitive.A)
					closeUserName := ""
					if len(closeUser) > 0 {
						closeUserName = closeUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.close" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-dtp" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register-lost" ||
								history.(map[string]any)["key"].(string) == "history.order.close-new" {
								closeUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{closeUserName})
				case "report.attributes.order.close.time":
					closeUser := orderData[i]["closeUser"].(primitive.A)
					closeAtStr := ""
					if len(closeUser) > 0 {
						closeAt := orderData[i]["colse_at"]
						if closeAt != nil {
							closeAtStr = time.Unix(closeAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.close" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-dtp" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register-lost" ||
								history.(map[string]any)["key"].(string) == "history.order.close-new" {
								closeAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}

					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{closeAtStr})
				case "report.attributes.order.close.reason":
					val := ""
					for _, history := range orderData[i]["history"].(primitive.A) {
						if history.(map[string]any)["key"].(string) == "history.order.close" ||
							history.(map[string]any)["key"].(string) == "history.order.close-with-dtp" ||
							history.(map[string]any)["key"].(string) == "history.order.close-with-register" ||
							history.(map[string]any)["key"].(string) == "history.order.close-with-register-lost" ||
							history.(map[string]any)["key"].(string) == "history.order.close-new" {
							if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
								val = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.send.by":
					sendUser := orderData[i]["sendUser"].(primitive.A)
					sendUserName := ""
					if len(sendUser) > 0 {
						sendUserName = sendUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.send-new" ||
								history.(map[string]any)["key"].(string) == "history.order.send" {
								sendUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{sendUserName})
				case "report.attributes.order.send.time":
					sendUser := orderData[i]["sendUser"].(primitive.A)
					sendAtStr := ""
					if len(sendUser) > 0 {
						sendAt := orderData[i]["send_at"]
						if sendAt != nil {
							sendAtStr = time.Unix(sendAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.send-new" ||
								history.(map[string]any)["key"].(string) == "history.order.send" {
								sendAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{sendAtStr})
				case "report.attributes.order.receive.by":
					receiveUser := orderData[i]["receiveUser"].(primitive.A)
					receiveUserName := ""
					if len(receiveUser) > 0 {
						receiveUserName = receiveUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{receiveUserName})
				case "report.attributes.order.receive.time":
					receiveUser := orderData[i]["receiveUser"].(primitive.A)
					receiveAtStr := ""
					if len(receiveUser) > 0 {
						receiveAt := orderData[i]["receive_at"]
						if receiveAt != nil {
							receiveAtStr = time.Unix(receiveAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{receiveAtStr})
				case "report.attributes.order.expectedArrivalTime":
					expectedArrivalTime := ""
					if orderData[i]["expected_arrival_time"] != nil {
						expectedArrivalTime = orderData[i]["expected_arrival_time"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{expectedArrivalTime})
				case "report.attributes.order.actualReceiptTime":
					actualReceiptTime := ""
					if orderData[i]["actual_receipt_time"] != nil {
						actualReceiptTime = orderData[i]["actual_receipt_time"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{actualReceiptTime})
				case "report.attributes.order.lost.by":
					lostUser := orderData[i]["lostUser"].(primitive.A)
					lostUserName := ""
					if len(lostUser) > 0 {
						lostUserName = lostUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
								history.(map[string]any)["key"].(string) == "history.order.lost" {
								lostUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{lostUserName})
				case "report.attributes.order.lost.time":
					lostUser := orderData[i]["lostUser"].(primitive.A)
					lostAtStr := ""
					if len(lostUser) > 0 {
						lostAt := orderData[i]["lost_at"]
						if lostAt != nil {
							lostAtStr = time.Unix(lostAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
								history.(map[string]any)["key"].(string) == "history.order.lost" {
								lostAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{lostAtStr})
				case "report.attributes.order.lost.reason":
					val := ""
					for _, history := range orderData[i]["history"].(primitive.A) {
						if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
							history.(map[string]any)["key"].(string) == "history.order.lost" {
							if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
								val = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.end.by":
					endUser := orderData[i]["endUser"].(primitive.A)
					endUserName := ""
					if len(endUser) > 0 {
						endUserName = endUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
								endUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{endUserName})
				case "report.attributes.order.end.time":
					lendUser := orderData[i]["endUser"].(primitive.A)
					endAtStr := ""
					if len(lendUser) > 0 {
						endAt := orderData[i]["end_at"]
						if endAt != nil {
							endAtStr = time.Unix(endAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
								endAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{endAtStr})
				case "report.attributes.order.end.reason":
					val := ""
					for _, history := range orderData[i]["history"].(primitive.A) {
						if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
							if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
								val = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.supplier":
					val := ""
					//if orderData[i]["shipment_info"] != nil && orderData[i]["shipment_info"].(map[string]any)["logistics"] != nil {
					//	code := orderData[i]["shipment_info"].(map[string]any)["logistics"].(int32)
					//	if code > 0 {
					//		val = locales.Tr(ctx, fmt.Sprintf("order_logistics_%d", code))
					//	}
					//}
					if orderData[i]["logistics_info"] != nil && orderData[i]["logistics_info"].(map[string]any)["logistics"] != nil {
						code := orderData[i]["logistics_info"].(map[string]any)["logistics"].(string)
						if len(code) > 0 {
							logis, _ := GetLogisticsCode(ctx, code)
							logisName := "其他"
							if locales.Lang(ctx) == "en" {
								logisName = "other"
							}
							if len(logis.Name) > 0 {
								logisName = logis.Name
							}
							val = logisName
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.supplier.other":
					val := ""
					//if orderData[i]["shipment_info"] != nil && orderData[i]["shipment_info"].(map[string]any)["other"] != nil {
					//	val = orderData[i]["shipment_info"].(map[string]any)["other"].(string)
					//}
					if orderData[i]["logistics_info"] != nil && orderData[i]["logistics_info"].(map[string]any)["other"] != nil {
						val = orderData[i]["logistics_info"].(map[string]any)["other"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.supplier.number":
					val := ""
					//if orderData[i]["shipment_info"] != nil && orderData[i]["shipment_info"].(map[string]any)["number"] != nil {
					//	val = orderData[i]["shipment_info"].(map[string]any)["number"].(string)
					//}
					if orderData[i]["logistics_info"] != nil && orderData[i]["logistics_info"].(map[string]any)["number"] != nil {
						val = orderData[i]["logistics_info"].(map[string]any)["number"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.research":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.packageNumber":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.packageMethod":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.batch":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.expireDate":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.status":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.other":
					orderInfoFields = append(orderInfoFields, field)
				}
			}

			if orderInfoFlag {
				baseAxis := axisIndex
				//订单研究产品的运送方式
				packages := make(map[string]string)
				if orderData[i]["medicines_package"] != nil {
					method := locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
					medicinesPackage := orderData[i]["medicines_package"].(primitive.A)
					for _, medicinePackage := range medicinesPackage {
						medicine := medicinePackage.(map[string]interface{})
						if medicine["package_method"].(bool) {
							method = locales.Tr(ctx, "medicineOrder_download_packageMethodPackage")
						} else {
							method = locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
						}
						packages[medicine["name"].(string)] = method
					}
				}
				if orderData[i]["mediciness"] != nil {
					for j := 0; j < len(orderData[i]["mediciness"].(primitive.A)); j++ {
						orderInfo := []any{}
						medicine := orderData[i]["mediciness"].(primitive.A)[j].(map[string]interface{})
						packageMethod := locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
						for _, field := range orderInfoFields {
							switch field {
							case "report.attributes.research":
								orderInfo = append(orderInfo, medicine["number"])
							case "report.attributes.research.packageNumber":
								orderInfo = append(orderInfo, medicine["package_number"])
							case "report.attributes.research.packageMethod":
								packageMethod = packages[medicine["name"].(string)]
								if packageMethod == "" {
									packageMethod = locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
								}
								orderInfo = append(orderInfo, packageMethod)
							case "report.attributes.research.batch":
								orderInfo = append(orderInfo, medicine["batch_number"])
							case "report.attributes.research.expireDate":
								orderInfo = append(orderInfo, medicine["expiration_date"])
							case "report.attributes.research.status":
								if medicine["status"] != nil {
									orderInfo = append(orderInfo, getMedicineStatusFormat(ctx, medicine["status"].(int32)))
								} else {
									orderInfo = append(orderInfo, "")
								}
							case "report.attributes.research.other":
								orderInfo = append(orderInfo, "")
							}
						}
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", orderInfoBegin, baseAxis), orderInfo)
						baseAxis++
						if baseAxis > max {
							max = baseAxis
						}
					}
				}
				if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
					ids := orderData[i]["other_medicines_new"].(primitive.A)
					oms := make([]models.OtherMedicine, 0)
					for _, id := range ids {
						oid := id.(primitive.ObjectID)
						oms = append(oms, otherMedicinesMap[oid])
					}
					groupMap := groupByMedicineOthers(oms)
					for _, vs := range groupMap {
						orderInfo := []any{}
						packageMethod := locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
						omGroup := vs[0]
						pd := slice.Filter(vs, func(index int, item models.OtherMedicine) bool {
							return item.PackageNumber != ""
						})
						for _, field := range orderInfoFields {
							switch field {
							case "report.attributes.research":
								name := omGroup.Name
								isBlindRole, err := tools.IsBlindedRole(roleID)
								if err != nil {
									return "", nil, errors.WithStack(err)
								}
								if isBlindRole && isBlindDrugMap[name] {
									name = tools.BlindData
								}
								orderInfo = append(orderInfo, name)
							case "report.attributes.research.packageNumber":
								orderInfo = append(orderInfo, "")
							case "report.attributes.research.packageMethod":
								packageMethod = packages[omGroup.Name]
								if packageMethod == "" || len(pd) == 0 {
									packageMethod = locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
								}
								orderInfo = append(orderInfo, packageMethod)
							case "report.attributes.research.batch":
								orderInfo = append(orderInfo, omGroup.BatchNumber)
							case "report.attributes.research.expireDate":
								orderInfo = append(orderInfo, omGroup.ExpirationDate)
							case "report.attributes.research.status":
								orderInfo = append(orderInfo, "")
							case "report.attributes.research.other":
								if len(pd) > 0 {
									pm := slice.GroupWith(pd, func(t models.OtherMedicine) string {
										return t.PackageNumber
									})
									count := fmt.Sprintf("%d(%d)", len(vs), len(pm))
									orderInfo = append(orderInfo, count)
								} else {
									count := fmt.Sprintf("%d", len(vs))
									orderInfo = append(orderInfo, count)
								}
							}
						}
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", orderInfoBegin, baseAxis), orderInfo)
						baseAxis++
						if baseAxis > max {
							max = baseAxis
						}
					}
				}
			}

			if axisIndex == max {
				axisIndex++
			} else {
				for _, colIdx := range mergeCol {
					_ = streamWriter.MergeCell(fmt.Sprintf("%s%d", colIdx, axisIndex), fmt.Sprintf("%s%d", colIdx, max-1))
				}
				axisIndex = max
			}
		}
	} else {
		//设置第一行title
		title := []interface{}{
			locales.Tr(ctx, "report.attributes.project.number"),
			locales.Tr(ctx, "report.attributes.project.name"),
			locales.Tr(ctx, "medicineOrder_download_number"),
			locales.Tr(ctx, "medicineOrder_download_status"),
			locales.Tr(ctx, "medicineOrder_download_sendInstitute"),
			locales.Tr(ctx, "medicineOrder_download_receiveInstitute"),
			locales.Tr(ctx, "medicineOrder_download_count"),
			locales.Tr(ctx, "medicineOrder_download_createUser"),
			locales.Tr(ctx, "medicineOrder_download_createDate"),
			locales.Tr(ctx, "medicineOrder_download_receiveUser"),
			locales.Tr(ctx, "medicineOrder_download_receiveDate"),
			//locales.Tr(ctx, "medicineOrder_download_expectedArrivalTime"),
			locales.Tr(ctx, "medicineOrder_download_actualReceiptTime"),
			locales.Tr(ctx, "medicineOrder_download_closeUser"),
			locales.Tr(ctx, "medicineOrder_download_closeDate"),
			locales.Tr(ctx, "medicineOrder_download_endUser"),
			locales.Tr(ctx, "medicineOrder_download_endDate"),
			locales.Tr(ctx, "medicineOrder_download_lostUser"),
			locales.Tr(ctx, "medicineOrder_download_lostDate"),
			//locales.Tr(ctx, "medicineOrder_download_cancelUser"),
			//locales.Tr(ctx, "medicineOrder_download_cancelDate"),
			locales.Tr(ctx, "medicineOrder_download_orderInfo"),
		}
		t := make([]any, len(title))
		for i, item := range title {
			t[i] = excelize.Cell{Value: item}
		}
		_ = streamWriter.SetRow("A1", t)

		title2 := []any{
			locales.Tr(ctx, "medicineOrder_download_medicine"),
			locales.Tr(ctx, "medicineOrder_download_batchNumber"),
			locales.Tr(ctx, "medicineOrder_download_expiredDate"),
			locales.Tr(ctx, "medicineOrder_download_other"),
		}
		t2 := make([]any, len(title)+len(title2)-1)
		for i, item := range title2 {
			t2[i+len(title)-1] = excelize.Cell{Value: item}
		}

		//处理订单详情合并
		title1Hcell, _ := excelize.CoordinatesToCellName(len(t), 1)
		title1Vcell, _ := excelize.CoordinatesToCellName(len(t2), 1)
		_ = streamWriter.MergeCell(title1Hcell, title1Vcell)

		_ = streamWriter.SetRow("A2", t2)
		//处理订单字段一二行合并
		for i := 0; i < len(title)-1; i++ {
			hcell, _ := excelize.CoordinatesToCellName(i+1, 1)
			vcell, _ := excelize.CoordinatesToCellName(i+1, 2)
			_ = streamWriter.MergeCell(hcell, vcell)
		}

		var axisIndex = 2
		for i := 0; i < len(orderData); i++ {
			//订单类型
			sendId := orderData[i]["send_id"].(primitive.ObjectID)
			sendLabel := ""
			receiveId := orderData[i]["receive_id"].(primitive.ObjectID)
			receiveLabel := ""
			orderType := int(orderData[i]["type"].(int32))

			if orderType == 1 || orderType == 3 || orderType == 5 { //发送发是仓库
				sendLabel = storehouses[sendId]
			} else { //发送发是中心
				sendLabel = sites[sendId]
			}

			if orderType == 1 || orderType == 2 || orderType == 6 { //接收方是中心
				receiveLabel = sites[receiveId]
			} else { //接收方是仓库
				receiveLabel = storehouses[receiveId]
			}
			ipCount := len(orderData[i]["mediciness"].(primitive.A))
			if len(orderData[i]["mediciness"].(primitive.A)) == 0 && orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) == 0 {
				//continue
				orderData[i]["mediciness"] = append(orderData[i]["mediciness"].(primitive.A),
					map[string]interface{}{
						"number":          "",
						"batch_number":    "",
						"expiration_date": "",
					})
			}
			//未编码研究产品的数量
			groupMap := make(map[string][]models.OtherMedicine)
			if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
				ids := orderData[i]["other_medicines_new"].(primitive.A)
				oms := make([]models.OtherMedicine, 0)
				for _, id := range ids {
					oid := id.(primitive.ObjectID)
					oms = append(oms, otherMedicinesMap[oid])
				}
				groupMap = groupByMedicineOthers(oms)
			}
			otherLen := len(groupMap)
			otherCount := 0
			if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
				otherCount = len(orderData[i]["other_medicines_new"].(primitive.A))
			}
			mergeIndex := len(orderData[i]["mediciness"].(primitive.A)) + otherLen + axisIndex

			r := []interface{}{project.ProjectInfo.Number, project.ProjectInfo.Name}
			r = append(r, orderData[i]["order_number"])
			r = append(r, data.GetOrderStatus(ctx, int(orderData[i]["status"].(int32))))
			r = append(r, sendLabel)
			if orderData[i]["subject"] != nil {
				r = append(r, orderData[i]["subject"].(map[string]interface{})["info"].(primitive.A)[0].(map[string]interface{})["value"])
			} else {
				r = append(r, receiveLabel)
			}

			r = append(r, ipCount+otherCount)
			createUserName := "System（Automatic Order）"
			if len(orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)) > 0 {
				createUserName = orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
			}
			r = append(r, createUserName)
			createdAt := orderData[i]["meta"].(map[string]interface{})["created_at"].(int64)
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			createdAtStr := time.Unix(createdAt, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			r = append(r, createdAtStr)

			receiveUser := orderData[i]["receiveUser"].(primitive.A)
			receiveUserName := ""
			receiveAtStr := ""
			if len(receiveUser) > 0 {
				receiveUserName = receiveUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				receiveAt := orderData[i]["receive_at"]
				if receiveAt != nil {
					receiveAtStr = time.Unix(receiveAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			}
			r = append(r, receiveUserName)
			r = append(r, receiveAtStr)

			// expectedArrivalTime := ""
			// if orderData[i]["expected_arrival_time"] != nil {
			// 	expectedArrivalTime = orderData[i]["expected_arrival_time"].(string)
			// }
			// r = append(r, expectedArrivalTime)

			actualReceiptTime := ""
			if orderData[i]["actual_receipt_time"] != nil {
				actualReceiptTime = orderData[i]["actual_receipt_time"].(string)
			}
			r = append(r, actualReceiptTime)

			closeUser := orderData[i]["closeUser"].(primitive.A)
			closeUserName := ""
			closeAtStr := ""
			if len(closeUser) > 0 {
				closeUserName = closeUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				closeAt := orderData[i]["colse_at"]
				if closeAt != nil {
					closeAtStr = time.Unix(closeAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			} else {
				for _, history := range orderData[i]["history"].(primitive.A) {
					if history.(map[string]any)["key"].(string) == "history.order.close" ||
						history.(map[string]any)["key"].(string) == "history.order.close-with-dtp" ||
						history.(map[string]any)["key"].(string) == "history.order.close-with-register" ||
						history.(map[string]any)["key"].(string) == "history.order.close-with-register-lost" ||
						history.(map[string]any)["key"].(string) == "history.order.close-new" {
						closeUserName = history.(map[string]any)["user"].(string)
						closeAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					}
				}
			}
			r = append(r, closeUserName)
			r = append(r, closeAtStr)

			endUser := orderData[i]["endUser"].(primitive.A)
			endUserName := ""
			endAtStr := ""
			if len(endUser) > 0 {
				endUserName = endUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				endAt := orderData[i]["end_at"]
				if endAt != nil {
					endAtStr = time.Unix(endAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			} else {
				for _, history := range orderData[i]["history"].(primitive.A) {
					if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
						endUserName = history.(map[string]any)["user"].(string)
						endAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					}
				}
			}
			r = append(r, endUserName)
			r = append(r, endAtStr)

			lostUser := orderData[i]["lostUser"].(primitive.A)
			lostUserName := ""
			lostAtStr := ""
			if len(lostUser) > 0 {
				lostUserName = lostUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				lostAt := orderData[i]["lost_at"]
				if lostAt != nil {
					lostAtStr = time.Unix(lostAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			} else {
				for _, history := range orderData[i]["history"].(primitive.A) {
					if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
						history.(map[string]any)["key"].(string) == "history.order.lost" {
						lostUserName = history.(map[string]any)["user"].(string)
						lostAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					}
				}
			}
			r = append(r, lostUserName)
			r = append(r, lostAtStr)

			//cancellerUser := orderData[i]["cancellerUser"].(primitive.A)
			//cancellerUserName := ""
			//cancellerAtStr := ""
			//if len(cancellerUser) > 0 && int(orderData[i]["status"].(int32)) == 5 {
			//	cancellerUserName = cancellerUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
			//	cancellerAt := orderData[i]["canceller_at"]
			//	if cancellerAt != nil {
			//		cancellerAtStr = time.Unix(cancellerAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			//	}
			//}
			//r = append(r, cancellerUserName)
			//r = append(r, cancellerAtStr)
			startRow := axisIndex + 1

			writeFirst := false
			for j := 0; j < len(orderData[i]["mediciness"].(primitive.A)); j++ {
				medicine := orderData[i]["mediciness"].(primitive.A)[j].(map[string]interface{})
				rowStart, _ := excelize.CoordinatesToCellName(1, axisIndex+1)
				axisIndex++
				if writeFirst {
					r = make([]interface{}, len(t)-1)
				}
				rm := append(r, medicine["number"])
				rm = append(rm, medicine["batch_number"])
				rm = append(rm, medicine["expiration_date"])
				_ = streamWriter.SetRow(rowStart, rm)
				writeFirst = true
			}
			if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
				for _, vs := range groupMap {
					omGroup := vs[0]
					rowStart, _ := excelize.CoordinatesToCellName(1, axisIndex+1)
					axisIndex++
					if writeFirst {
						r = make([]interface{}, len(t)-1)
					}
					name := omGroup.Name
					isBlindRole, err := tools.IsBlindedRole(roleID)
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					if isBlindRole && isBlindDrugMap[name] {
						name = tools.BlindData
					}
					rm := append(r, name)
					rm = append(rm, omGroup.BatchNumber)
					rm = append(rm, omGroup.ExpirationDate)
					pd := slice.Filter(vs, func(index int, item models.OtherMedicine) bool {
						return item.PackageNumber != ""
					})
					if len(pd) > 0 {
						pm := slice.GroupWith(pd, func(t models.OtherMedicine) string {
							return t.PackageNumber
						})
						count := fmt.Sprintf("%d(%d)", len(vs), len(pm))
						rm = append(rm, count)
					} else {
						rm = append(rm, len(vs))
					}

					_ = streamWriter.SetRow(rowStart, rm)
					writeFirst = true
				}
			}

			for j := 0; j < len(t)-1; j++ {
				hcell, _ := excelize.CoordinatesToCellName(j+1, startRow)
				vcell, _ := excelize.CoordinatesToCellName(j+1, mergeIndex)
				if startRow != mergeIndex {
					_ = streamWriter.MergeCell(hcell, vcell)
				}

			}
		}

	}

	_ = streamWriter.Flush()
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	bytes = buffer.Bytes()

	fileName := fmt.Sprintf("%s[%s]ShipmentOrdersReport_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))

	return fileName, bytes, nil
}

func groupByMedicineOthers(medicines []models.OtherMedicine) map[string][]models.OtherMedicine {
	grouped := make(map[string][]models.OtherMedicine)
	for _, med := range medicines {
		key := fmt.Sprintf("%s_%s_%s", med.Name, med.BatchNumber, med.ExpirationDate)
		grouped[key] = append(grouped[key], med)
	}
	return grouped
}

func ExportReturnOrdersReport(ctx *gin.Context, projectID string, envID string, roleID string, templateId string, now time.Time) (string, []byte, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)

	var orderData []map[string]interface{}

	// project
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}

	//判断当前登录的角色的分类
	var role models.ProjectRolePermission
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&role)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	user, err := tools.Me(ctx)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	match := bson.M{"user_id": user.ID, "env_id": envOID}
	var instituteIds []primitive.ObjectID
	if role.Scope == "depot" {
		//如果当前角色的分类是depot,查看该用户分配的仓库，
		var userDepots []models.UserDepot
		cursor, err := tools.Database.Collection("user_depot").Find(nil, match)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &userDepots)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, userDepot := range userDepots {
			instituteIds = append(instituteIds, userDepot.DepotID)
		}
	} else if role.Scope == "site" {
		//如果当前角色的分类是site，查看该用户分配的中心
		var userSites []models.UserSite
		cursor, err := tools.Database.Collection("user_site").Find(nil, match)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
		err = cursor.All(nil, &userSites)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, userSite := range userSites {
			instituteIds = append(instituteIds, userSite.SiteID)
		}
	}

	if (role.Scope == "depot" || role.Scope == "site") && len(instituteIds) <= 0 {
	} else {
		baseFilter := bson.M{"env_id": envOID}
		filter := bson.M{"env_id": envOID}

		if len(instituteIds) > 0 {
			if role.Scope == "site" {
				filter["send_id"] = bson.M{"$in": instituteIds}
			}
			if role.Scope == "depot" {
				filter["$or"] = bson.A{
					bson.M{"receive_id": bson.M{"$in": instituteIds}},
					bson.M{"subject.project_site_id": bson.M{"$in": instituteIds}},
				}
			}
		}
		filter["type"] = bson.M{"$in": [2]int{3, 4}}

		pipepine := mongo.Pipeline{
			{{Key: "$match", Value: baseFilter}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "confirm_by", "foreignField": "_id", "as": "confirmUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "close_by", "foreignField": "_id", "as": "closeUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "send_by", "foreignField": "_id", "as": "sendUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "lost_by", "foreignField": "_id", "as": "lostUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "end_by", "foreignField": "_id", "as": "endUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "receive_by", "foreignField": "_id", "as": "receiveUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "canceller_by", "foreignField": "_id", "as": "cancellerUser"}}},
			{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "meta.created_by", "foreignField": "_id", "as": "meta.createdUser"}}},
			{{Key: "$lookup", Value: bson.M{
				"from": "subject", // 关联的集合名称
				"let": bson.M{
					"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
				},
				"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
					bson.M{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": bson.A{
									bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
									bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
								},
							},
						},
					},
				},
				"as": "subject", // 关联结果的字段名
			}}},
			{{Key: "$unwind", Value: bson.M{"path": "$subject", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$match", Value: filter}},
			{{Key: "$lookup", Value: bson.M{"from": "medicine", "localField": "medicines", "foreignField": "_id", "as": "m"}}},
			{{Key: "$unwind", Value: bson.M{"path": "$m", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$sort", Value: bson.D{{"m.expiration_date", 1}, {Key: "m.number", Value: 1}}}},
			{{Key: "$group", Value: bson.M{"_id": "$_id", "order": bson.M{"$first": "$$ROOT"}, "mediciness": bson.M{"$push": "$m"}}}},
			{{Key: "$replaceRoot", Value: bson.M{"newRoot": bson.M{"$mergeObjects": bson.A{bson.M{"mediciness": "$mediciness"}, "$order"}}}}},
			{{Key: "$sort", Value: bson.D{{"sort_index", 1}, {"meta.created_at", -1}}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "history",
				"localField":   "_id",
				"foreignField": "oid",
				"as":           "history",
			}}},
		}
		optTrue := true
		opt := &options.AggregateOptions{
			AllowDiskUse: &optTrue,
		}
		cursor, err := tools.Database.Collection("medicine_order").Aggregate(nil, pipepine, opt)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
		err = cursor.All(nil, &orderData)
		if err != nil {
			return "", nil, errors.WithStack(err)

		}
	}
	otherMedicineIds := make([]primitive.ObjectID, 0)
	otherMedicines := make([]models.OtherMedicine, 0)
	otherMedicinesMap := make(map[primitive.ObjectID]models.OtherMedicine)
	for _, d := range orderData {
		if d["other_medicines_new"] != nil && len(d["other_medicines_new"].(primitive.A)) > 0 {
			ids := d["other_medicines_new"].(primitive.A)
			for _, id := range ids {
				oid := id.(primitive.ObjectID)
				otherMedicineIds = append(otherMedicineIds, oid)
			}
		}
	}
	otherMedicineIds = slice.Unique(otherMedicineIds)
	if len(otherMedicineIds) > 0 {
		cursor, err := tools.Database.Collection("medicine_others").Find(nil, bson.M{"_id": bson.M{"$in": otherMedicineIds}})
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &otherMedicines)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, medicine := range otherMedicines {
			otherMedicinesMap[medicine.ID] = medicine
		}
	}
	//包装
	type otherPackageNumber struct {
		ID            primitive.ObjectID `bson:"_id"`
		PackageNumber int                `bson:"package_number"`
		PackageCount  int                `bson:"package_count"`
	}

	otherPackageNumbers := make([]otherPackageNumber, 0)
	packageMatch := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	packagePipepine := mongo.Pipeline{
		{{Key: "$match", Value: packageMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":            1,
				"package_number": 1,
				"package_count":  1,
			},
		}},
	}
	otherPackageAll, err := tools.Database.Collection("other_package_number").Aggregate(nil, packagePipepine)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = otherPackageAll.All(nil, &otherPackageNumbers)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	otherPackageNumberMap := make(map[string]int, len(otherPackageNumbers))
	for _, number := range otherPackageNumbers {
		packageNumber := strconv.Itoa(number.PackageNumber)
		otherPackageNumberMap[packageNumber] = number.PackageCount
	}

	instituteMatch := bson.M{
		"env_id": envOID,
	}
	//查询该项目下的仓库
	storehouses := make(map[primitive.ObjectID]string)
	var storeHouseData []map[string]interface{}
	storeHousePipeline := mongo.Pipeline{
		{{Key: "$match", Value: instituteMatch}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "storehouse",
		}}},
		{{
			Key: "$project",
			Value: bson.M{
				"value": "$_id",
				"_id":   0,
				"label": bson.M{
					"$arrayElemAt": bson.A{"$storehouse.name", 0},
				},
			},
		}},
	}
	cursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, storeHousePipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &storeHouseData)
	if err != nil {
		return "", nil, errors.WithStack(err)

	}
	for _, storehouse := range storeHouseData {
		storehouses[storehouse["value"].(primitive.ObjectID)] = storehouse["label"].(string)
	}

	//查询该项目下的中心
	sites := make(map[primitive.ObjectID]string)
	var siteData []map[string]interface{}
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: instituteMatch}},
		{{
			Key: "$project",
			Value: bson.M{
				"value":  "$_id",
				"_id":    0,
				"number": 1,
				"label":  models.ProjectSiteNameBson(ctx),
			},
		}},
	}
	siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, sitePipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = siteCursor.All(nil, &siteData)
	if err != nil {
		return "", nil, errors.WithStack(err)

	}
	for _, site := range siteData {
		sites[site["value"].(primitive.ObjectID)] = site["number"].(string) + "-" + site["label"].(string)
	}

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err = tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return "", nil, errors.WithStack(err)

	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}

	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	var bytes []byte

	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	_ = streamWriter.SetColWidth(1, 14, 20)

	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	if templateId != "" {
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		var template models.CustomTemplate
		err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, err
		}

		mergeCol := []string{"A", "B"}
		fieldCol := map[string]string{}
		var col = int32(2)

		title := []any{locales.Tr(ctx, "report.attributes.project.number"), locales.Tr(ctx, "report.attributes.project.name")}
		title2 := []any{}
		//订单详情标识
		orderInfoFlag := false
		orderInfoFields := []string{}
		orderInfoBegin := ""

		for _, field := range template.Fields {
			if field == "report.attributes.project.number" || field == "report.attributes.project.name" {
				continue
			}

			if field == "report.attributes.research" || field == "report.attributes.research.packageNumber" || field == "report.attributes.research.packageMethod" ||
				field == "report.attributes.research.batch" || field == "report.attributes.research.expireDate" || field == "report.attributes.research.status" || field == "report.attributes.research.other" {
				orderInfoFields = append(orderInfoFields, field)
				continue
			}

			//c、d、e……
			colIdx := fmt.Sprintf("%s", string('A'+col%26))
			if col > 25 {
				colIdx = "A" + colIdx
			}
			fieldCol[field] = colIdx
			col++

			switch field {
			case "report.attributes.order.number":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_number"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.status":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_status"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.send":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_sendInstitute"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.receive":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_receiveInstitute"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.medicineQuantity":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_count"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.create.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_createUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.create.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_createDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.cancel.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_cancelUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.cancel.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_cancelDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.cancel.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_cancelReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.confirm.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_confirmUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.confirm.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_confirmDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.close.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_closeUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.close.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_closeDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.close.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_closeReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.send.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_sendUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.send.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_sendDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.receive.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_receiveUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.receive.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_receiveDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.expectedArrivalTime":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_expectedArrivalTime"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.actualReceiptTime":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_actualReceiptTime"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.lost.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_lostUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.lost.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_lostDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.lost.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_lostReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.end.by":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_endUser"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.end.time":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_endDate"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.end.reason":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_endReason"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.supplier":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_supplier"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.supplier.other":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_supplierOther"))
				mergeCol = append(mergeCol, colIdx)
			case "report.attributes.order.supplier.number":
				title = append(title, locales.Tr(ctx, "medicineOrder_download_supplierNumber"))
				mergeCol = append(mergeCol, colIdx)
			}
		}

		for _, field := range orderInfoFields {

			//c、d、e……
			colIdx := fmt.Sprintf("%s", string('A'+col%26))
			if col > 25 {
				colIdx = "A" + colIdx
			}
			fieldCol[field] = colIdx
			col++

			switch field {
			case "report.attributes.research":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_medicine"))
			case "report.attributes.research.packageNumber":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_packageNumber"))
			case "report.attributes.research.packageMethod":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_packageMethod"))
			case "report.attributes.research.batch":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_batchNumber"))
			case "report.attributes.research.expireDate":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_expiredDate"))
			case "report.attributes.research.status":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicine_status"))
			case "report.attributes.research.other":
				if !orderInfoFlag {
					orderInfoFlag = true
					// if orderInfoBegin == "" {
					// 	orderInfoBegin = colIdx
					// }
					//title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
				}
				title2 = append(title2, locales.Tr(ctx, "medicineOrder_download_other"))
			}
		}

		if orderInfoFlag {
			title = append(title, locales.Tr(ctx, "medicineOrder_download_orderInfo"))
			if orderInfoBegin == "" {
				colIdx := fmt.Sprintf("%s", string('A'+(len(title)-1)%26))
				if col > 25 {
					colIdx = "A" + colIdx
				}
				orderInfoBegin = colIdx
			}
		}

		//设置第一行title
		t := make([]any, len(title))
		for i, item := range title {
			t[i] = excelize.Cell{Value: item}
		}
		_ = streamWriter.SetRow("A1", t)
		axisIndex := 2
		if orderInfoFlag && len(title2) > 0 {
			t2 := make([]any, len(title)+len(title2))
			for i, item := range title2 {
				t2[i+len(title)-1] = excelize.Cell{Value: item}
			}
			//处理订单详情合并
			title1Hcell, _ := excelize.CoordinatesToCellName(len(t), 1)
			title1Vcell, _ := excelize.CoordinatesToCellName(len(t2)-1, 1)
			_ = streamWriter.MergeCell(title1Hcell, title1Vcell)
			_ = streamWriter.SetRow("A2", t2)
			//处理订单字段一二行合并
			for i := 0; i < len(title)-1; i++ {
				hcell, _ := excelize.CoordinatesToCellName(i+1, 1)
				vcell, _ := excelize.CoordinatesToCellName(i+1, 2)
				_ = streamWriter.MergeCell(hcell, vcell)
			}
			axisIndex = 3
		}

		for i := 0; i < len(orderData); i++ {
			//订单类型
			sendId := orderData[i]["send_id"].(primitive.ObjectID)
			sendLabel := ""
			receiveId := orderData[i]["receive_id"].(primitive.ObjectID)
			receiveLabel := ""
			orderType := int(orderData[i]["type"].(int32))

			if orderType == 1 || orderType == 3 || orderType == 5 { //发送发是仓库
				sendLabel = storehouses[sendId]
			} else { //发送发是中心
				sendLabel = sites[sendId]
			}

			if orderType == 1 || orderType == 2 || orderType == 6 { //接收方是中心
				receiveLabel = sites[receiveId]
			} else { //接收方是仓库
				receiveLabel = storehouses[receiveId]
			}

			//未编码研究产品的数量
			//otherLen := 0
			otherCount := 0
			if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
				otherCount = len(orderData[i]["other_medicines_new"].(primitive.A))
			}
			max := axisIndex
			_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "A", axisIndex), []any{project.ProjectInfo.Number})
			_ = streamWriter.SetRow(fmt.Sprintf("%s%d", "B", axisIndex), []any{project.ProjectInfo.Name})

			orderInfoFields := []string{}
			for _, field := range template.Fields {
				switch field {
				case "report.attributes.order.number":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{orderData[i]["order_number"]})
				case "report.attributes.order.status":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{data.GetOrderStatus(ctx, int(orderData[i]["status"].(int32)))})
				case "report.attributes.order.send":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{sendLabel})
				case "report.attributes.order.receive":
					if orderData[i]["subject"] != nil {
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{orderData[i]["subject"].(map[string]interface{})["info"].(primitive.A)[0].(map[string]interface{})["value"]})
					} else {
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{receiveLabel})
					}
				case "report.attributes.order.medicineQuantity":
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{len(orderData[i]["mediciness"].(primitive.A)) + otherCount})
				case "report.attributes.order.create.by":
					var createUserName string
					if len(orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)) > 0 {
						createUserName = orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{createUserName})
				case "report.attributes.order.create.time":
					createdAt := orderData[i]["meta"].(map[string]interface{})["created_at"].(int64)
					createdAtStr := time.Unix(createdAt, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{createdAtStr})
				case "report.attributes.order.cancel.by":
					cancellerUser := orderData[i]["cancellerUser"].(primitive.A)
					cancellerUserName := ""
					if len(cancellerUser) > 0 && int(orderData[i]["status"].(int32)) == 5 {
						cancellerUserName = cancellerUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{cancellerUserName})
				case "report.attributes.order.cancel.time":
					cancellerUser := orderData[i]["cancellerUser"].(primitive.A)
					cancellerAtStr := ""
					if len(cancellerUser) > 0 && int(orderData[i]["status"].(int32)) == 5 {
						cancellerAt := orderData[i]["canceller_at"]
						if cancellerAt != nil {
							cancellerAtStr = time.Unix(cancellerAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{cancellerAtStr})
				case "report.attributes.order.cancel.reason":
					var cancelReason interface{}
					if int(orderData[i]["status"].(int32)) == 5 {
						cancelReason = orderData[i]["reason"]
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{cancelReason})
				case "report.attributes.order.confirm.by":
					confirmUser := orderData[i]["confirmUser"].(primitive.A)
					confirmUserName := ""
					if len(confirmUser) > 0 {
						confirmUserName = confirmUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.confrim-new" || history.(map[string]any)["key"].(string) == "history.order.confrim" {
								confirmUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{confirmUserName})
				case "report.attributes.order.confirm.time":
					confirmUser := orderData[i]["confirmUser"].(primitive.A)
					confirmAtStr := ""
					if len(confirmUser) > 0 {
						confirmAt := orderData[i]["confirm_at"]
						if confirmAt != nil {
							confirmAtStr = time.Unix(confirmAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.confrim-new" || history.(map[string]any)["key"].(string) == "history.order.confrim" {
								confirmAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{confirmAtStr})
				case "report.attributes.order.close.by":
					closeUser := orderData[i]["closeUser"].(primitive.A)
					closeUserName := ""
					if len(closeUser) > 0 {
						closeUserName = closeUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.close" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-dtp" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register-lost" ||
								history.(map[string]any)["key"].(string) == "history.order.close-new" {
								closeUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{closeUserName})
				case "report.attributes.order.close.time":
					closeUser := orderData[i]["closeUser"].(primitive.A)
					closeAtStr := ""
					if len(closeUser) > 0 {
						closeAt := orderData[i]["colse_at"]
						if closeAt != nil {
							closeAtStr = time.Unix(closeAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.close" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-dtp" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register" ||
								history.(map[string]any)["key"].(string) == "history.order.close-with-register-lost" ||
								history.(map[string]any)["key"].(string) == "history.order.close-new" {
								closeAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}

					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{closeAtStr})
				case "report.attributes.order.close.reason":
					val := ""
					for _, history := range orderData[i]["history"].(primitive.A) {
						if history.(map[string]any)["key"].(string) == "history.order.close" ||
							history.(map[string]any)["key"].(string) == "history.order.close-with-dtp" ||
							history.(map[string]any)["key"].(string) == "history.order.close-with-register" ||
							history.(map[string]any)["key"].(string) == "history.order.close-with-register-lost" ||
							history.(map[string]any)["key"].(string) == "history.order.close-new" {
							if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
								val = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.send.by":
					sendUser := orderData[i]["sendUser"].(primitive.A)
					sendUserName := ""
					if len(sendUser) > 0 {
						sendUserName = sendUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.send-new" ||
								history.(map[string]any)["key"].(string) == "history.order.send" {
								sendUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{sendUserName})
				case "report.attributes.order.send.time":
					sendUser := orderData[i]["sendUser"].(primitive.A)
					sendAtStr := ""
					if len(sendUser) > 0 {
						sendAt := orderData[i]["send_at"]
						if sendAt != nil {
							sendAtStr = time.Unix(sendAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.send-new" ||
								history.(map[string]any)["key"].(string) == "history.order.send" {
								sendAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{sendAtStr})
				case "report.attributes.order.receive.by":
					receiveUser := orderData[i]["receiveUser"].(primitive.A)
					receiveUserName := ""
					if len(receiveUser) > 0 {
						receiveUserName = receiveUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{receiveUserName})
				case "report.attributes.order.receive.time":
					receiveUser := orderData[i]["receiveUser"].(primitive.A)
					receiveAtStr := ""
					if len(receiveUser) > 0 {
						receiveAt := orderData[i]["receive_at"]
						if receiveAt != nil {
							receiveAtStr = time.Unix(receiveAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{receiveAtStr})
				case "report.attributes.order.expectedArrivalTime":
					expectedArrivalTime := ""
					if orderData[i]["expected_arrival_time"] != nil {
						expectedArrivalTime = orderData[i]["expected_arrival_time"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{expectedArrivalTime})
				case "report.attributes.order.actualReceiptTime":
					actualReceiptTime := ""
					if orderData[i]["actual_receipt_time"] != nil {
						actualReceiptTime = orderData[i]["actual_receipt_time"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{actualReceiptTime})
				case "report.attributes.order.lost.by":
					lostUser := orderData[i]["lostUser"].(primitive.A)
					lostUserName := ""
					if len(lostUser) > 0 {
						lostUserName = lostUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
								history.(map[string]any)["key"].(string) == "history.order.lost" {
								lostUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{lostUserName})
				case "report.attributes.order.lost.time":
					lostUser := orderData[i]["lostUser"].(primitive.A)
					lostAtStr := ""
					if len(lostUser) > 0 {
						lostAt := orderData[i]["lost_at"]
						if lostAt != nil {
							lostAtStr = time.Unix(lostAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
								history.(map[string]any)["key"].(string) == "history.order.lost" {
								lostAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{lostAtStr})
				case "report.attributes.order.lost.reason":
					val := ""
					for _, history := range orderData[i]["history"].(primitive.A) {
						if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
							history.(map[string]any)["key"].(string) == "history.order.lost" {
							if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
								val = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.end.by":
					endUser := orderData[i]["endUser"].(primitive.A)
					endUserName := ""
					if len(endUser) > 0 {
						endUserName = endUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
								endUserName = history.(map[string]any)["user"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{endUserName})
				case "report.attributes.order.end.time":
					lendUser := orderData[i]["endUser"].(primitive.A)
					endAtStr := ""
					if len(lendUser) > 0 {
						endAt := orderData[i]["end_at"]
						if endAt != nil {
							endAtStr = time.Unix(endAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						}
					} else {
						for _, history := range orderData[i]["history"].(primitive.A) {
							if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
								endAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{endAtStr})
				case "report.attributes.order.end.reason":
					val := ""
					for _, history := range orderData[i]["history"].(primitive.A) {
						if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
							if history.(map[string]any)["data"].(map[string]any)["reason"] != nil {
								val = history.(map[string]any)["data"].(map[string]any)["reason"].(string)
							}
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.supplier":
					val := ""
					//if orderData[i]["shipment_info"] != nil && orderData[i]["shipment_info"].(map[string]any)["logistics"] != nil {
					//	code := orderData[i]["shipment_info"].(map[string]any)["logistics"].(int32)
					//	if code > 0 {
					//		val = locales.Tr(ctx, fmt.Sprintf("order_logistics_%d", code))
					//	}
					//}
					if orderData[i]["logistics_info"] != nil && orderData[i]["logistics_info"].(map[string]any)["logistics"] != nil {
						code := orderData[i]["logistics_info"].(map[string]any)["logistics"].(string)
						if len(code) > 0 {
							logis, _ := GetLogisticsCode(ctx, code)
							logisName := "其他"
							if locales.Lang(ctx) == "en" {
								logisName = "other"
							}
							if len(logis.Name) > 0 {
								logisName = logis.Name
							}
							val = logisName
						}
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.supplier.other":
					val := ""
					//if orderData[i]["shipment_info"] != nil && orderData[i]["shipment_info"].(map[string]any)["other"] != nil {
					//	val = orderData[i]["shipment_info"].(map[string]any)["other"].(string)
					//}
					if orderData[i]["logistics_info"] != nil && orderData[i]["logistics_info"].(map[string]any)["other"] != nil {
						val = orderData[i]["logistics_info"].(map[string]any)["other"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.order.supplier.number":
					val := ""
					//if orderData[i]["shipment_info"] != nil && orderData[i]["shipment_info"].(map[string]any)["number"] != nil {
					//	val = orderData[i]["shipment_info"].(map[string]any)["number"].(string)
					//}
					if orderData[i]["logistics_info"] != nil && orderData[i]["logistics_info"].(map[string]any)["number"] != nil {
						val = orderData[i]["logistics_info"].(map[string]any)["number"].(string)
					}
					_ = streamWriter.SetRow(fmt.Sprintf("%s%d", fieldCol[field], axisIndex), []any{val})
				case "report.attributes.research":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.packageNumber":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.packageMethod":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.batch":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.expireDate":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.status":
					orderInfoFields = append(orderInfoFields, field)
				case "report.attributes.research.other":
					orderInfoFields = append(orderInfoFields, field)
				}
			}

			if orderInfoFlag {
				baseAxis := axisIndex
				//订单研究产品的运送方式
				packages := make(map[string]string)
				if orderData[i]["medicines_package"] != nil {
					method := "单品"
					medicinesPackage := orderData[i]["medicines_package"].(primitive.A)
					for _, medicinePackage := range medicinesPackage {
						medicine := medicinePackage.(map[string]interface{})
						if medicine["package_method"].(bool) {
							method = locales.Tr(ctx, "medicineOrder_download_packageMethodPackage")
						} else {
							method = locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
						}
						packages[medicine["name"].(string)] = method
					}
				}
				if orderData[i]["mediciness"] != nil {
					for j := 0; j < len(orderData[i]["mediciness"].(primitive.A)); j++ {
						orderInfo := []any{}
						medicine := orderData[i]["mediciness"].(primitive.A)[j].(map[string]interface{})
						packageMethod := locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
						for _, field := range orderInfoFields {
							switch field {
							case "report.attributes.research":
								orderInfo = append(orderInfo, medicine["number"])
							case "report.attributes.research.packageNumber":
								orderInfo = append(orderInfo, medicine["package_number"])
							case "report.attributes.research.packageMethod":
								packageMethod = packages[medicine["name"].(string)]
								if packageMethod == "" {
									packageMethod = locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
								}
								orderInfo = append(orderInfo, packageMethod)
							case "report.attributes.research.batch":
								orderInfo = append(orderInfo, medicine["batch_number"])
							case "report.attributes.research.expireDate":
								orderInfo = append(orderInfo, medicine["expiration_date"])
							case "report.attributes.research.status":
								if medicine["status"] != nil {
									orderInfo = append(orderInfo, getMedicineStatusFormat(ctx, medicine["status"].(int32)))
								} else {
									orderInfo = append(orderInfo, "")
								}
							case "report.attributes.research.other":
								orderInfo = append(orderInfo, "")
							}
						}
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", orderInfoBegin, baseAxis), orderInfo)
						baseAxis++
						if baseAxis > max {
							max = baseAxis
						}
					}
				}
				if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
					ids := orderData[i]["other_medicines_new"].(primitive.A)
					oms := make([]models.OtherMedicine, 0)
					for _, id := range ids {
						oid := id.(primitive.ObjectID)
						oms = append(oms, otherMedicinesMap[oid])
					}
					groupMap := groupByMedicineOthers(oms)
					for _, vs := range groupMap {
						orderInfo := []any{}
						packageMethod := locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
						omGroup := vs[0]
						pd := slice.Filter(vs, func(index int, item models.OtherMedicine) bool {
							return item.PackageNumber != ""
						})
						for _, field := range orderInfoFields {
							switch field {
							case "report.attributes.research":
								name := omGroup.Name
								isBlindRole, err := tools.IsBlindedRole(roleID)
								if err != nil {
									return "", nil, errors.WithStack(err)
								}
								if isBlindRole && isBlindDrugMap[name] {
									name = tools.BlindData
								}
								orderInfo = append(orderInfo, name)
							case "report.attributes.research.packageNumber":
								orderInfo = append(orderInfo, "")
							case "report.attributes.research.packageMethod":
								packageMethod = packages[omGroup.Name]
								if packageMethod == "" || len(pd) == 0 {
									packageMethod = locales.Tr(ctx, "medicineOrder_download_packageMethodSingle")
								}
								orderInfo = append(orderInfo, packageMethod)
							case "report.attributes.research.batch":
								orderInfo = append(orderInfo, omGroup.BatchNumber)
							case "report.attributes.research.expireDate":
								orderInfo = append(orderInfo, omGroup.ExpirationDate)
							case "report.attributes.research.status":
								orderInfo = append(orderInfo, "")
							case "report.attributes.research.other":
								if len(pd) > 0 {
									pm := slice.GroupWith(pd, func(t models.OtherMedicine) string {
										return t.PackageNumber
									})
									count := fmt.Sprintf("%d(%d)", len(vs), len(pm))
									orderInfo = append(orderInfo, count)
								} else {
									count := fmt.Sprintf("%d", len(vs))
									orderInfo = append(orderInfo, count)
								}
							}
						}
						_ = streamWriter.SetRow(fmt.Sprintf("%s%d", orderInfoBegin, baseAxis), orderInfo)
						baseAxis++
						if baseAxis > max {
							max = baseAxis
						}
					}
				}
			}

			if axisIndex == max {
				axisIndex++
			} else {
				for _, colIdx := range mergeCol {
					_ = streamWriter.MergeCell(fmt.Sprintf("%s%d", colIdx, axisIndex), fmt.Sprintf("%s%d", colIdx, max-1))
				}
				axisIndex = max
			}
		}

	} else {

		//设置第一行title
		title := []interface{}{
			locales.Tr(ctx, "report.attributes.project.number"),
			locales.Tr(ctx, "report.attributes.project.name"),
			locales.Tr(ctx, "medicineOrder_download_number"),
			locales.Tr(ctx, "medicineOrder_download_status"),
			locales.Tr(ctx, "medicineOrder_download_sendInstitute"),
			locales.Tr(ctx, "medicineOrder_download_receiveInstitute"),
			locales.Tr(ctx, "medicineOrder_download_count"),
			locales.Tr(ctx, "medicineOrder_download_createUser"),
			locales.Tr(ctx, "medicineOrder_download_createDate"),
			locales.Tr(ctx, "medicineOrder_download_receiveUser"),
			locales.Tr(ctx, "medicineOrder_download_receiveDate"),
			locales.Tr(ctx, "medicineOrder_download_actualReceiptTime"),
			locales.Tr(ctx, "medicineOrder_download_closeUser"),
			locales.Tr(ctx, "medicineOrder_download_closeDate"),
			locales.Tr(ctx, "medicineOrder_download_endUser"),
			locales.Tr(ctx, "medicineOrder_download_endDate"),
			locales.Tr(ctx, "medicineOrder_download_lostUser"),
			locales.Tr(ctx, "medicineOrder_download_lostDate"),
			//locales.Tr(ctx, "medicineOrder_download_cancelUser"),
			//locales.Tr(ctx, "medicineOrder_download_cancelDate"),
			locales.Tr(ctx, "medicineOrder_download_orderInfo"),
		}
		t := make([]any, len(title))
		for i, item := range title {
			t[i] = excelize.Cell{Value: item}
		}

		title2 := []any{
			locales.Tr(ctx, "medicineOrder_download_medicine"),
			locales.Tr(ctx, "medicineOrder_download_batchNumber"),
			locales.Tr(ctx, "medicineOrder_download_expiredDate"),
			locales.Tr(ctx, "medicineOrder_download_other"),
		}
		t2 := make([]any, len(title)+len(title2))
		for i, item := range title2 {
			t2[i+len(title)-1] = excelize.Cell{Value: item}
		}

		//处理订单详情合并
		title1Hcell, _ := excelize.CoordinatesToCellName(len(t), 1)
		title1Vcell, _ := excelize.CoordinatesToCellName(len(t2)-1, 1)
		_ = streamWriter.MergeCell(title1Hcell, title1Vcell)

		_ = streamWriter.SetRow("A1", t)
		_ = streamWriter.SetRow("A2", t2)
		//处理订单字段一二行合并
		for i := 0; i < len(title)-1; i++ {
			hcell, _ := excelize.CoordinatesToCellName(i+1, 1)
			vcell, _ := excelize.CoordinatesToCellName(i+1, 2)
			_ = streamWriter.MergeCell(hcell, vcell)
		}

		// _ = streamWriter.SetRow("A3", []any{project.ProjectInfo.Number})
		// _ = streamWriter.SetRow("B3", []any{project.ProjectInfo.Name})
		var axisIndex = 2
		for i := 0; i < len(orderData); i++ {
			//订单类型
			sendId := orderData[i]["send_id"].(primitive.ObjectID)
			sendLabel := ""
			receiveId := orderData[i]["receive_id"].(primitive.ObjectID)
			receiveLabel := ""
			orderType := int(orderData[i]["type"].(int32))

			if orderType == 1 || orderType == 3 || orderType == 5 { //发送发是仓库
				sendLabel = storehouses[sendId]
			} else { //发送发是中心
				sendLabel = sites[sendId]
			}

			if orderType == 1 || orderType == 2 || orderType == 6 { //接收方是中心
				receiveLabel = sites[receiveId]
			} else { //接收方是仓库
				receiveLabel = storehouses[receiveId]
			}

			//未编码研究产品的数量
			otherLen := 0
			otherCount := 0
			if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
				otherCount = len(orderData[i]["other_medicines_new"].(primitive.A))
			}
			mergeIndex := len(orderData[i]["mediciness"].(primitive.A)) + otherLen + axisIndex

			r := []interface{}{project.ProjectInfo.Number, project.ProjectInfo.Name}
			r = append(r, orderData[i]["order_number"])
			r = append(r, data.GetOrderStatus(ctx, int(orderData[i]["status"].(int32))))
			r = append(r, sendLabel)
			if orderData[i]["subject"] != nil {
				r = append(r, orderData[i]["subject"].(map[string]interface{})["info"].(primitive.A)[0].(map[string]interface{})["value"])
			} else {
				r = append(r, receiveLabel)
			}
			r = append(r, len(orderData[i]["mediciness"].(primitive.A))+otherCount)
			var createUserName string
			if len(orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)) > 0 {
				createUserName = orderData[i]["meta"].(map[string]interface{})["createdUser"].(primitive.A)[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
			}
			r = append(r, createUserName)
			createdAt := orderData[i]["meta"].(map[string]interface{})["created_at"].(int64)
			createdAtStr := time.Unix(createdAt, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			r = append(r, createdAtStr)

			receiveUser := orderData[i]["receiveUser"].(primitive.A)
			receiveUserName := ""
			receiveAtStr := ""
			if len(receiveUser) > 0 {
				receiveUserName = receiveUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				receiveAt := orderData[i]["receive_at"]
				if receiveAt != nil {
					receiveAtStr = time.Unix(receiveAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			}
			r = append(r, receiveUserName)
			r = append(r, receiveAtStr)

			actualReceiptTime := ""
			if orderData[i]["actual_receipt_time"] != nil {
				actualReceiptTime = orderData[i]["actual_receipt_time"].(string)
			}
			r = append(r, actualReceiptTime)

			closeUser := orderData[i]["closeUser"].(primitive.A)
			closeUserName := ""
			closeAtStr := ""
			if len(closeUser) > 0 {
				closeUserName = closeUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				closeAt := orderData[i]["colse_at"]
				if closeAt != nil {
					closeAtStr = time.Unix(closeAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			} else {
				for _, history := range orderData[i]["history"].(primitive.A) {
					if history.(map[string]any)["key"].(string) == "history.order.close" ||
						history.(map[string]any)["key"].(string) == "history.order.close-new" {
						closeUserName = history.(map[string]any)["user"].(string)
						closeAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					}
				}
			}
			r = append(r, closeUserName)
			r = append(r, closeAtStr)

			endUser := orderData[i]["endUser"].(primitive.A)
			endUserName := ""
			endAtStr := ""
			if len(endUser) > 0 {
				endUserName = endUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				endAt := orderData[i]["end_at"]
				if endAt != nil {
					endAtStr = time.Unix(endAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			} else {
				for _, history := range orderData[i]["history"].(primitive.A) {
					if history.(map[string]any)["key"].(string) == "history.order.cancel-dtp" {
						endUserName = history.(map[string]any)["user"].(string)
						endAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					}
				}
			}
			r = append(r, endUserName)
			r = append(r, endAtStr)

			lostUser := orderData[i]["lostUser"].(primitive.A)
			lostUserName := ""
			lostAtStr := ""
			if len(lostUser) > 0 {
				lostUserName = lostUser[0].(map[string]interface{})["info"].(map[string]interface{})["name"].(string)
				lostAt := orderData[i]["lost_at"]
				if lostAt != nil {
					lostAtStr = time.Unix(lostAt.(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				}
			} else {
				for _, history := range orderData[i]["history"].(primitive.A) {
					if history.(map[string]any)["key"].(string) == "history.order.lost-new" ||
						history.(map[string]any)["key"].(string) == "history.order.lost" {
						lostUserName = history.(map[string]any)["user"].(string)
						lostAtStr = time.Unix(history.(map[string]any)["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					}
				}
			}
			r = append(r, lostUserName)
			r = append(r, lostAtStr)

			startRow := axisIndex + 1
			if len(orderData[i]["mediciness"].(primitive.A)) == 0 && orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) == 0 {
				continue
			}

			writeFirst := false
			for j := 0; j < len(orderData[i]["mediciness"].(primitive.A)); j++ {
				medicine := orderData[i]["mediciness"].(primitive.A)[j].(map[string]interface{})
				rowStart, _ := excelize.CoordinatesToCellName(1, axisIndex+1)
				axisIndex++
				if writeFirst {
					r = make([]interface{}, len(t)-1)
				}

				rm := append(r, medicine["number"])
				rm = append(rm, medicine["batch_number"])
				rm = append(rm, medicine["expiration_date"])
				_ = streamWriter.SetRow(rowStart, rm)
				writeFirst = true
			}

			if orderData[i]["other_medicines_new"] != nil && len(orderData[i]["other_medicines_new"].(primitive.A)) > 0 {
				ids := orderData[i]["other_medicines_new"].(primitive.A)
				oms := make([]models.OtherMedicine, 0)
				for _, id := range ids {
					oid := id.(primitive.ObjectID)
					oms = append(oms, otherMedicinesMap[oid])
				}
				groupMap := groupByMedicineOthers(oms)
				for _, vs := range groupMap {
					omGroup := vs[0]
					rowStart, _ := excelize.CoordinatesToCellName(1, axisIndex+1)
					axisIndex++
					if writeFirst {
						r = make([]interface{}, len(t)-1)
					}
					name := omGroup.Name
					isBlindRole, err := tools.IsBlindedRole(roleID)
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					if isBlindRole && isBlindDrugMap[name] {
						name = tools.BlindData
					}
					rm := append(r, name)
					rm = append(rm, omGroup.BatchNumber)
					rm = append(rm, omGroup.ExpirationDate)
					pd := slice.Filter(vs, func(index int, item models.OtherMedicine) bool {
						return item.PackageNumber != ""
					})
					if len(pd) > 0 {
						pm := slice.GroupWith(pd, func(t models.OtherMedicine) string {
							return t.PackageNumber
						})
						count := fmt.Sprintf("%d(%d)", len(vs), len(pm))
						rm = append(rm, count)
					} else {
						rm = append(rm, len(vs))
					}
					_ = streamWriter.SetRow(rowStart, rm)
					writeFirst = true
				}
			}

			for j := 0; j < len(t)-1; j++ {
				hcell, _ := excelize.CoordinatesToCellName(j+1, startRow)
				vcell, _ := excelize.CoordinatesToCellName(j+1, mergeIndex)
				if startRow != mergeIndex {
					_ = streamWriter.MergeCell(hcell, vcell)
				}

			}
		}
		// _ = streamWriter.MergeCell("A3", fmt.Sprintf("%s%d", "A", axisIndex))
		// _ = streamWriter.MergeCell("B3", fmt.Sprintf("%s%d", "B", axisIndex))
	}

	_ = streamWriter.Flush()
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	bytes = buffer.Bytes()

	fileName := fmt.Sprintf("%s[%s]ReturnOrdersReport_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))

	return fileName, bytes, nil
}

func getMedicineStatusFormat(ctx context.Context, status int32) string {
	switch status {
	case 0:
		return locales.Tr(ctx, "medicine.status.toBeWarehoused")
	case 1:
		return locales.Tr(ctx, "medicine.status.available")
	case 2:
		return locales.Tr(ctx, "medicine.status.delivered")
	case 3:
		return locales.Tr(ctx, "medicine.status.sending")
	case 4:
		return locales.Tr(ctx, "medicine.status.quarantine")
	case 5:
		return locales.Tr(ctx, "medicine.status.used")
	case 6:
		return locales.Tr(ctx, "medicine.status.lose")
	case 7:
		return locales.Tr(ctx, "medicine.status.expired")
	case 8:
		return locales.Tr(ctx, "medicine.status.receive")
	case 9:
		return locales.Tr(ctx, "medicine.status.return")
	case 10:
		return locales.Tr(ctx, "medicine.status.destroy")
	case 11:
		return locales.Tr(ctx, "medicine.status.toBeConfirmed")
	case 12:
		return locales.Tr(ctx, "medicine.status.stockPending")
	case 13:
		return locales.Tr(ctx, "medicine.status.apply")
	case 14:
		return locales.Tr(ctx, "medicine.status.frozen")
	case 20:
		return locales.Tr(ctx, "medicine.status.locked")
	}
	return ""
}
