package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strings"
	"time"
)

const (
	OutSizeNotCompleted = iota + 1 // 1 超窗未完成
	InProgress                     // 2 进行中
	OutSizeCompleted               // 3 超窗已完成
	Prepare                        // 4 未开始
	CompletedOnSchedule            // 5 按期已完成
)

// SubjectVisitService struct
type SubjectVisitService struct {
}

func (s *SubjectVisitService) GetSubjectVisit(ctx *gin.Context, envID string, siteID string, roleID string, types string) (interface{}, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	me, err := tools.Me(ctx)
	role, err := tools.GetRole(roleID)
	// 获取cohort下或者环境下所有的访视配置
	var visitCycles []models.VisitCycle
	visitCycleMap := map[string]models.VisitCycle{}
	cursor, err := tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	subjectVisitSummary := models.SubjectVisitSummary{}
	subjectVisitHomeSummary := models.SubjectVisitHomeSummary{}
	// TODO 时区修改
	//me.Timezone
	intTimeZone := float64(8)

	// 将visitCycle放在map里面  不用再循环里面循环取对应的值
	for _, cycle := range visitCycles {
		visitCycleMap[cycle.CohortID.Hex()] = cycle
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	match := bson.M{"env_id": envOID, "deleted": bson.M{"$ne": true}}
	if siteID != "" {
		siteOID, _ := primitive.ObjectIDFromHex(siteID)
		match["project_site_id"] = siteOID
	}

	if role.Scope != "study" && siteID == "" {
		// 查询角色关联中心
		// 查询关联中心
		userSiteFilter := bson.M{"user_id": me.ID, "env_id": envOID}
		userSites := make([]models.UserSite, 0)
		userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, userSiteFilter)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		err = userSiteCursor.All(nil, &userSites)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIDs := make([]primitive.ObjectID, 0)
		for _, userSite := range userSites {
			siteIDs = append(siteIDs, userSite.SiteID)
		}
		match["project_site_id"] = bson.M{"$in": siteIDs}
	}
	var subjectDispensing []models.SubjectDispensing
	var sites []models.ProjectSite
	cursor, err = tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &sites)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor, err = tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{"$lookup", bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var attributes []models.Attribute
	cursor, err = tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	date := map[string][]models.SubjectVisit{}

	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycleMap {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")

		}
	}

	for _, subject := range subjectDispensing {

		randomTime := subject.RandomTime
		if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
			randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
		}

		attributeP, _ := slice.Find(attributes, func(index int, item models.Attribute) bool {
			return subject.CohortID == item.CohortID
		})
		attribute := *attributeP

		//  筛选是否加入计算的受试者  比如揭盲后不发药、 完成后不计算
		blindSubject := checkCountSubject(attribute, subject)
		err = slice.SortByField(subject.Dispensing, "SerialNumber")
		if err != nil {
			return nil, err
		}
		visitCycle := visitCycleMap[subject.CohortID.Hex()]

		lastTime := time.Duration(0)
		afterRandom := false
		interval := float64(0)

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute

		today := time.Now().UTC().Add(duration).Format("2006-01-02")
		site := ""
		siteP, ok := slice.Find(sites, func(index int, item models.ProjectSite) bool {
			return item.ID == subject.ProjectSiteID
		})
		if ok {
			siteInfo := *siteP
			site = siteInfo.Number + "-" + siteInfo.Name
		}
		firstTime := time.Duration(0)
		for _, resDispensing := range subject.Dispensing {

			if !blindSubject {
				continue
			}
			visitInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
				return resDispensing.VisitInfo.Number == item.Number
			})
			if !ok {
				continue
			}
			visitInfo := *visitInfoP
			dispensingDate := ""
			dispensingId := resDispensing.ID.Hex()

			if firstTime == 0 && resDispensing.DispensingTime != 0 && visitInfo.Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitInfo.Interval)).Unix())
				firstTime = resDispensing.DispensingTime
			}

			if !resDispensing.VisitSign {

				period := handlePeriod(
					afterRandom, visitCycle.VisitType, visitInfo, randomTime, lastTime, resDispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
				if resDispensing.Status == 2 {
					interval = 0
					lastTime = resDispensing.DispensingTime
					dispensingDate = time.Unix(int64(resDispensing.DispensingTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
				}
				if period.LineTime != "" {
					//if 未发药、未随机 || 发放访视 已随机 未发药
					//if 最小窗口期 < 今天
					//	超窗未完成
					//if 最大窗口期 > 今天
					//	未开始
					//if 今天 in 窗口期
					//进行中
					//if 已发药已随机
					//if 发药时间不在窗口期内
					//	超窗已完成
					//if 发药时间在窗口期内
					//	按期已完成
					status := -1
					actualDate := period.MinPeriod
					if (visitInfo.Dispensing && resDispensing.Status == 1) || visitInfo.Random && subject.Group == "" { //未发药、未随机
						actualDate, status = getPeriodStatus(&subjectVisitSummary, &subjectVisitHomeSummary, period, today, false)
					}
					if (visitInfo.Dispensing && resDispensing.Status == 2 || resDispensing.Status == 3) ||
						(!visitInfo.Dispensing && visitInfo.Random && subject.Group != "") { //已发药、 仅随机访视已随机

						formulaDate := dispensingDate
						if resDispensing.Status == 3 {
							formulaDate = time.Unix(int64(resDispensing.DispensingTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
						}

						actualDate, status = getPeriodStatus(&subjectVisitSummary, &subjectVisitHomeSummary, period, formulaDate, true)
					}
					date[actualDate] = append(date[actualDate], models.SubjectVisit{
						Name:         subject.Info[0].Value.(string),
						Period:       period,
						Status:       status,
						VisitNumber:  visitInfo.Number,
						Site:         site,
						ActualDate:   dispensingDate,
						DispensingId: dispensingId,
					})

				} else if visitCycle.VisitType == 0 && resDispensing.VisitInfo.Random && subject.Group != "" && resDispensing.VisitInfo.Dispensing { //随机访视  并且随机过
					formulaDate := "-"
					status := -1
					randomTime := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
					period.LineTime = randomTime
					period.MinPeriod = randomTime
					period.MaxPeriod = randomTime

					if resDispensing.Status == 1 {
						_, status = getPeriodStatus(&subjectVisitSummary, &subjectVisitHomeSummary, period, today, false)
					}
					if resDispensing.Status == 2 {
						formulaDate = time.Unix(int64(resDispensing.DispensingTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
						_, status = getPeriodStatus(&subjectVisitSummary, &subjectVisitHomeSummary, period, formulaDate, true)
					}
					if resDispensing.Status == 3 {
						formulaDate = time.Unix(int64(resDispensing.DispensingTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
						_, status = getPeriodStatus(&subjectVisitSummary, &subjectVisitHomeSummary, period, formulaDate, true)
						formulaDate = "-"
					}

					//period.MinPeriod = ""
					//period.MaxPeriod = ""
					date[randomTime] = append(date[randomTime], models.SubjectVisit{
						Name:         subject.Info[0].Value.(string),
						Period:       period,
						Status:       status,
						VisitNumber:  visitInfo.Number,
						Site:         site,
						ActualDate:   formulaDate,
						DispensingId: dispensingId,
					})
				}
			}
			if resDispensing.VisitInfo.Random {
				afterRandom = true
			}
		}
	}

	if types == "1" {
		return subjectVisitHomeSummary, nil
	}

	returnData := map[string][]models.SubjectVisit{}
	// 使用for range遍历Map并打印每个key-value对
	for key, value := range date {
		lastMonth, e := LastMonth(types)
		if e != nil {
			return nil, e
		}
		nextMonth, e := NextMonth(types)
		if e != nil {
			return nil, e
		}
		if strings.Contains(key, lastMonth) || strings.Contains(key, types) || strings.Contains(key, nextMonth) {
			returnData[key] = value
		}
	}

	return map[string]interface{}{
		"subjectVisitSummary": subjectVisitSummary,
		"dateInfo":            returnData,
	}, nil
}

// 上月
func LastMonth(dateStr string) (string, error) {
	// 解析日期字符串
	t, err := time.Parse("2006-01", dateStr)
	if err != nil {
		return "", err
	}

	// 计算2024年1月和3月的日期
	lastMonth := t.AddDate(0, -1, 0) // 减去一个月

	return lastMonth.Format("2006-01"), nil
}

// 下月
func NextMonth(dateStr string) (string, error) {
	// 解析日期字符串
	t, err := time.Parse("2006-01", dateStr)
	if err != nil {
		return "", err
	}

	// 计算2024年1月和3月的日期
	nextMonth := t.AddDate(0, 1, 0) // 加上一个月
	return nextMonth.Format("2006-01"), nil
}

func checkCountSubject(attribute models.Attribute, subject models.SubjectDispensing) bool {

	if subject.Status == 4 || subject.Status == 5 || subject.Status == 8 {
		return false
	}

	if attribute.AttributeInfo.UnBlindingRestrictions && subject.Status == 6 { //开启紧急揭盲不继续发药
		return false
	}
	if attribute.AttributeInfo.PvUnBlindingRestrictions && subject.PvUnblindingStatus == 1 { //开启紧急揭盲包括pv揭盲不继续发药
		return false
	}

	return true
}

func getPeriodStatus(subjectVisitSummary *models.SubjectVisitSummary, subjectVisitHomeSummary *models.SubjectVisitHomeSummary, period models.Period, date string, isDispensing bool) (string, int) {

	if period.MinPeriod > date {
		if isDispensing {
			subjectVisitSummary.OutSizeCompleted++
			subjectVisitHomeSummary.OutSizeCompleted++
			return period.MinPeriod, OutSizeCompleted
		} else {
			subjectVisitSummary.Prepare++
			return period.MinPeriod, Prepare
		}
	}
	if period.MaxPeriod < date {
		if isDispensing {
			subjectVisitSummary.OutSizeCompleted++
			subjectVisitHomeSummary.OutSizeCompleted++
			return period.MaxPeriod, OutSizeCompleted
		} else {
			subjectVisitHomeSummary.OutSizeNotCompleted++
			subjectVisitSummary.OutSizeNotCompleted++
			return period.MaxPeriod, OutSizeNotCompleted
		}
	}
	if period.MinPeriod <= date && period.MaxPeriod >= date {
		if isDispensing {
			subjectVisitSummary.CompletedOnSchedule++
			return date, CompletedOnSchedule
		} else {
			subjectVisitSummary.InProgress++
			return date, InProgress
		}
	}
	return "", -1
}

func (s *SubjectVisitService) PushNotice(ctx *gin.Context, req models.PushVisitReq) error {
	user, err := tools.Me(ctx)
	dispensingOID, _ := primitive.ObjectIDFromHex(req.DispensingID)

	var dispensing models.Dispensing
	err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingOID}).Decode(&dispensing)
	if err != nil {
		return errors.WithStack(err)
	}
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	if err != nil {
		return errors.WithStack(err)
	}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return errors.WithStack(err)
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": dispensing.ProjectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	// 更新通知表
	now := time.Now().UTC()
	nowStr := now.Format("2006-01-02 15:04:05")

	//timeZone, _ := strconv.ParseFloat(strings.Replace(projectSite.TimeZone, "UTC", "", 1), 64)
	strTimeZone, err := tools.GetSiteTimeZoneInfo(projectSite)
	if err != nil {
		return errors.WithStack(err)
	}
	timeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	// 查询是否在任务内 存在 不需要写入数据

	roleIDs := make([]primitive.ObjectID, 0)

	userIDs := []primitive.ObjectID{}
	UserIdsRead := []models.UserIds{}
	for _, users := range req.RoleUser {

		if users.Users != nil && len(users.Users) > 0 {
			userEnvFilter := bson.M{"roles": bson.M{"$elemMatch": bson.M{"$eq": users.RoleID}}, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "unbind": bson.M{"$ne": true}}
			userEnvs := make([]models.UserProjectEnvironment, 0)
			userEnvCursor, err := tools.Database.Collection("user_project_environment").Find(nil, userEnvFilter)
			if err != nil {
				return errors.WithStack(err)
			}
			err = userEnvCursor.All(nil, &userEnvs)
			if err != nil {
				return errors.WithStack(err)
			}

			if userEnvs != nil && len(userEnvs) > 0 && users.Users != nil && len(users.Users) > 0 && len(userEnvs) == len(users.Users) {
				roleIDs = append(roleIDs, users.RoleID)
			}
		}

		for _, id := range users.Users {
			index := arrays.Contains(userIDs, id)
			if index == -1 {
				userIDs = append(userIDs, id)
				UserIdsRead = append(UserIdsRead, models.UserIds{UserID: id})
			}
		}
	}
	day := ""

	if req.Template == "a" || req.Template == "b" {
		periodMinTime, _ := time.Parse("2006-01-02", req.PeriodMin)
		duration := periodMinTime.Sub(now)
		between := int(duration.Hours() / 24)

		if between < 0 {
			return tools.BuildServerError(ctx, "subject_visit_err")
		}

		day = convertor.ToString(between)
	}
	contentEN := task.SwitchNoticeContent("en-US", req.Template, day)
	contentZH := task.SwitchNoticeContent("zh-CN", req.Template, day)

	notice := models.VisitNotice{
		ID:            primitive.NewObjectID(),
		CustomerID:    dispensing.CustomerID,
		ProjectID:     dispensing.ProjectID,
		EnvID:         dispensing.EnvironmentID,
		CohortID:      dispensing.CohortID,
		ProjectSiteID: subject.ProjectSiteID, // test
		SubjectID:     dispensing.SubjectID,
		DispensingID:  dispensing.ID,
		Status:        0,
		Content: models.VisitNoticeContent{
			Day:           day,
			Template:      req.Template,
			ProjectNumber: project.Number,
			SiteNumber:    projectSite.Number,
			SubjectNumber: subject.Info[0].Value.(string),
			VisitNumber:   dispensing.VisitInfo.Name,
		},
		PushType:      req.Type,
		TimeZoneFloat: timeZone, // 受试者时区
		BeSendTime:    nowStr,
		NoticeUser:    userIDs,
		UserID:        user.ID,
		PhoneUser:     req.PhoneUser,
		AllRoleIDs:    roleIDs,
	}
	// 写入通知列表
	appTaskNotice := models.AppTaskNotice{
		ID:            primitive.NewObjectID(),
		CustomerID:    notice.CustomerID,
		ProjectID:     notice.ProjectID,
		EnvironmentID: notice.EnvID,
		NoticeType:    2,
		NoticeTime:    time.Duration(time.Now().Unix()),
		UserIds:       UserIdsRead,
		ExpireNotice: models.ExpireNotice{
			DispensingID:           notice.DispensingID,
			VisitReminderContent:   contentZH,
			VisitReminderContentEn: contentEN,
		},
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		if req.Type == 1 {
			var task models.WorkTask
			err = tools.Database.Collection("work_task").FindOne(sctx, bson.M{"info.dispensing_id": notice.DispensingID, "info.work_type": 11}).Decode(&task)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			if task.ID.IsZero() {
				workTask := models.WorkTask{
					ID:            primitive.NewObjectID(),
					CustomerID:    subject.CustomerID,
					ProjectID:     subject.ProjectID,
					EnvironmentID: subject.EnvironmentID,
					CohortID:      subject.CohortID,
					UserIDs:       userIDs,
					Info: models.WorkTaskInfo{
						WorkType:    11,
						Status:      0,
						CreatedTime: time.Duration(time.Now().Unix()),
						Deadline:    time.Duration(time.Now().AddDate(0, 0, 1).Unix()), //TODO
						MedicineIDs: []primitive.ObjectID{},
						//MedicineOrderID: orderId,
						DispensingID: dispensing.ID,
					},
					Deleted: false,
				}
				_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		_, err = tools.Database.Collection("visit_notice").InsertOne(sctx, notice)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		_, err = tools.Database.Collection("app_task_notice").InsertOne(sctx, appTaskNotice)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}

	err = tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (s *SubjectVisitService) GetNoticeHistory(ctx *gin.Context, envID string, dispensingID string) ([]models.VisitNoticeHistory, error) {
	dispensingOID, _ := primitive.ObjectIDFromHex(dispensingID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	visitNoticeHistoryList := make([]models.VisitNoticeHistory, 0)

	visitNoticeList := make([]models.VisitNotice, 0)
	filter := bson.M{
		"dispensing_id": dispensingOID,
		"env_id":        envOID,
		"send_time": bson.M{
			"$ne": 0,
		},
	}
	findOptions := options.Find()
	findOptions.SetSort(bson.D{{"send_time", -1}}) // 指定按 send_time 降序排列

	cus, err := tools.Database.Collection("visit_notice").Find(ctx, filter, findOptions)
	if err != nil {
		return visitNoticeHistoryList, err
	}
	err = cus.All(nil, &visitNoticeList)
	if err != nil {
		return visitNoticeHistoryList, err
	}

	for _, visitNotice := range visitNoticeList {
		var visitNoticeHistory models.VisitNoticeHistory
		visitNoticeHistory.ID = visitNotice.ID
		visitNoticeHistory.Status = visitNotice.Status
		visitNoticeHistory.Content = visitNotice.Content
		visitNoticeHistory.TimeZone = visitNotice.TimeZone
		visitNoticeHistory.TimeZoneFloat = visitNotice.TimeZoneFloat
		visitNoticeHistory.PushTime = visitNotice.SendTime
		//timestamp, e := Timestamp(visitNotice.BeSendTime)
		//if e != nil {
		//	return nil, errors.WithStack(e)
		//}
		//visitNoticeHistory.PushTime = timestamp
		//推送人
		if !visitNotice.UserID.IsZero() {
			var user models.User
			err = tools.Database.Collection("user").FindOne(ctx, bson.M{"_id": visitNotice.UserID}).Decode(&user)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			userName := ""
			if user.Unicode == 0 {
				userName = user.Name
			} else {
				userName = fmt.Sprintf("%s(%d)", user.Name, user.Unicode)
			}
			visitNoticeHistory.PushPeople = userName
		} else {
			visitNoticeHistory.PushPeople = locales.Tr(ctx, "calendar.button.site.visit.matter.notice.history.people.system")
		}

		visitNoticeHistory.PushType = visitNotice.PushType

		objectList := make([]string, 0)
		if visitNotice.NoticeUser != nil && len(visitNotice.NoticeUser) > 0 {
			for _, noticeUserId := range visitNotice.NoticeUser {
				var u models.User
				err = tools.Database.Collection("user").FindOne(ctx, bson.M{"_id": noticeUserId}).Decode(&u)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				objectList = append(objectList, u.Email)
			}
		}

		if visitNotice.PhoneUser != nil && len(visitNotice.PhoneUser) > 0 {
			for _, phone := range visitNotice.PhoneUser {
				objectList = append(objectList, phone)
			}
		}
		// 使用 strings.Join 将数组转换为逗号分隔的字符串
		objectString := strings.Join(objectList, ",")
		visitNoticeHistory.Object = objectString

		visitNoticeHistoryList = append(visitNoticeHistoryList, visitNoticeHistory)
	}

	return visitNoticeHistoryList, nil
}

func Timestamp(dateString string) (int64, error) {
	layout := "2006-01-02 15:04:05"

	//t, err := time.Parse(layout, dateString)
	t, err := time.ParseInLocation(layout, dateString, time.UTC)
	if err != nil {
		return 0, err
	}

	timestamp := t.Unix()

	return timestamp, nil
}

func TimestampDay(dateString string) (int64, error) {
	layout := "2006-01-02"

	//t, err := time.Parse(layout, dateString)
	t, err := time.ParseInLocation(layout, dateString, time.UTC)
	if err != nil {
		return 0, err
	}

	timestamp := t.Unix()

	return timestamp, nil
}
