package service

import (
	"bytes"
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/multilingual"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	datastructure "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

func ExportRandomizationSimulationReportPDF(ctx *gin.Context, projectID string, envID string, cohortID string, roleId string, simulateRandomId string, now time.Time) (string, []byte, error) {
	// 获取配置数据
	//customerID, _ := primitive.ObjectIDFromHex(ctx.Query("customerId"))
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	simulateRandomOID, _ := primitive.ObjectIDFromHex(simulateRandomId)

	match := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	var attribute models.Attribute
	var randomDesign models.RandomDesign

	var project models.Project

	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match["cohort_id"] = cohortOID
	}
	err := tools.Database.Collection("attribute").FindOne(ctx, match).Decode(&attribute)
	if err != nil {
		return "", nil, err
	}
	err = tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return "", nil, err
	}
	err = tools.Database.Collection("random_design").FindOne(ctx, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, err
	}

	filter := bson.M{"_id": simulateRandomOID, "project_id": projectOID, "env_id": envOID}
	if !cohortOID.IsZero() {
		filter = bson.M{"_id": simulateRandomOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var simulateRandoms []models.SimulateRandom
	cursor, err := tools.Database.Collection("simulate_random").Find(ctx, filter)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &simulateRandoms)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	// 获取激活随机列表配置
	match["status"] = 1
	var randomList []map[string]interface{}
	randomListCursor, _ := tools.Database.Collection("random_list").Find(nil, match)
	err = randomListCursor.All(nil, &randomList)
	if err != nil {
		return "", nil, err
	}

	user, err := tools.Me(ctx)
	if err != nil {
		return "", nil, err
	}
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return "", nil, err
	}
	hour := time.Duration(timeZone)
	minute := time.Duration((timeZone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	date := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")
	env := ""
	cohort := ""
	for _, environment := range project.Environments {
		if environment.ID == envOID {
			env = environment.Name
		}
		for _, item := range environment.Cohorts {
			if item.ID.Hex() == cohortID {
				cohort = item.Name
			}
		}
	}
	//TODO
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return "", nil, err
	}
	groups := models.RandomDesignToGroups(randomDesign, isBlindedRole, attribute)
	var factor []string
	for _, randomFactor := range randomDesign.Info.Factors {
		status := randomFactor.Status
		if status == nil || *status == 1 {
			var tmp []string
			for _, option := range randomFactor.Options {
				tmp = append(tmp, option.Label)
			}
			temp := fmt.Sprintf("%s(%s)", randomFactor.Label, strings.Join(tmp, "、"))
			factor = append(factor, temp)
		}
	}

	var randomizationSimulationsData []map[string]interface{}
	for _, random := range simulateRandoms {
		data := map[string]interface{}{}
		data["id"] = random.ID
		data["name"] = random.SimulateRandomInfo.Name
		data["siteQuantity"] = random.SimulateRandomInfo.SiteQuantity
		data["runQuantity"] = random.SimulateRandomInfo.RunQuantity
		data["subjectQuantity"] = random.SimulateRandomInfo.SubjectQuantity

		randomService := SimulateRandomService{}
		groupsData, _ := randomService.GetGroups(ctx, random.ID.Hex())
		data["groupsData"] = groupsData
		layeredOverviewData, _ := randomService.GetLayeredOverview(ctx, random.ID.Hex())
		data["layeredOverviewData"] = layeredOverviewData
		siteOverviewData, _ := randomService.GetSiteOverview(ctx, random.ID.Hex())
		data["siteOverviewData"] = siteOverviewData
		siteDetailDataList := map[string]interface{}{}
		for i := 0; i < random.SimulateRandomInfo.RunQuantity; i++ {
			siteDetailData, _ := randomService.GetSiteDetail(ctx, random.ID.Hex(), i+1)
			siteDetailDataList[strconv.Itoa(i)] = siteDetailData
		}
		data["siteDetailDataList"] = siteDetailDataList

		randomizationSimulationsData = append(randomizationSimulationsData, data)
	}

	accuracy := "ones"
	types := ""
	if attribute.AttributeInfo.Accuracy != 1 {
		accuracy = "twos"
	}
	if randomDesign.Info.Type == 2 {
		types = locales.Tr(ctx, "randomization.type.twos")

	} else {
	}
	if randomDesign.Info.Type == 1 {
		types = locales.Tr(ctx, "randomization.type.ones")

	}

	data := map[string]interface{}{
		"sponsor":          project.Sponsor,
		"projectName":      project.Name,
		"projectNumber":    project.Number,
		"env":              env,
		"cohort":           cohort,
		"createDate":       date,
		"createBy":         user.Name,
		"random":           attribute.AttributeInfo.Random,
		"blind":            attribute.AttributeInfo.Blind,
		"dispensing":       attribute.AttributeInfo.Dispensing,
		"instituteLayered": attribute.AttributeInfo.InstituteLayered,
		"countryLayered":   attribute.AttributeInfo.CountryLayered,
		"regionLayered":    attribute.AttributeInfo.RegionLayered,
		"prefix":           attribute.AttributeInfo.Prefix,
		//"sitePrefix":         attribute.AttributeInfo.SitePrefix,
		"SubjectReplaceText":           attribute.AttributeInfo.SubjectReplaceText,
		"digit":                        attribute.AttributeInfo.Digit,
		"accuracy":                     locales.Tr(ctx, fmt.Sprintf("randomization.accuracy.%s", accuracy)),
		"isFreeze":                     attribute.AttributeInfo.IsFreeze,
		"isRandom":                     attribute.AttributeInfo.IsRandom,
		"type":                         types,
		"group":                        strings.Join(groups, ","),
		"random_list":                  randomList,
		"factor":                       factor,
		"randomizationSimulationsData": randomizationSimulationsData,
	}

	pdf, err := tools.RandomizationSimulationToPdf(ctx, data)
	if err != nil {
		return "", nil, err

	}
	pdfName := url.PathEscape(fmt.Sprintf("%s_RandomizationSimulationReport", project.Number))
	err = pdf.WritePdf(pdfName)
	if err != nil {
		return "", nil, err
	}
	defer os.Remove(pdfName)

	bytes, err := os.ReadFile(pdfName)
	if err != nil {
		return "", nil, err
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]RandomizationSimulationReport_%s.pdf", project.Number, envCode, now.Format("20060102150405"))

	return fileName, bytes, nil
}

func ExportRandomizationStatisticsExport(ctx *gin.Context, projectId string, envId string, cohortIds []string, roleId string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortOIds := make([]primitive.ObjectID, 0)
	if len(cohortIds) > 0 {
		cohortOIds = slice.Map(cohortIds, func(index int, item string) primitive.ObjectID {
			oid, _ := primitive.ObjectIDFromHex(item)
			return oid
		})
	}
	type statistics struct {
		ID            primitive.ObjectID `json:"id" bson:"id"`
		CohortID      primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
		Factors       []models.Factors   `json:"factors" bson:"factors"`
		ProjectSiteId primitive.ObjectID `json:"projectSiteId" bson:"project_site_id"`
		RandomTime    time.Duration      `json:"randomTime" bson:"random_time"`
		RandomListId  primitive.ObjectID `json:"randomListId" bson:"random_list_id"`
		Info          []models.Info      `json:"info" bson:"info"`
	}
	statisticsData := make([]statistics, 0)
	match := bson.M{"group": bson.M{"$ne": ""}, "env_id": envOID, "status": bson.M{"$in": []int{3, 4, 6, 9}}}
	if len(cohortOIds) > 0 {
		match["cohort_id"] = bson.M{"$in": cohortOIds}
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "random_number",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "random_number",
		}}},
		{{"$unwind", bson.M{"path": "$random_number", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"_id":             0,
			"id":              "$_id",
			"cohort_id":       "$cohort_id",
			"factors":         "$random_number.factors",
			"project_site_id": 1,
			"random_time":     1,
			"random_list_id":  1,
			"info":            1,
		}}},
	})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &statisticsData)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	var randomDesigns []models.RandomDesign
	cursor, err = tools.Database.Collection("random_design").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomDesigns)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	randomDesignsMap := make(map[primitive.ObjectID]models.RandomDesign)
	for _, design := range randomDesigns {
		randomDesignsMap[design.CohortID] = design
	}

	//replaceStatistics := slice.Filter(statisticsData, func(index int, item statistics) bool {
	//	return item.RandomListId.IsZero()
	//})
	//replaceSubjectIds := slice.Map(replaceStatistics, func(index int, item statistics) primitive.ObjectID {
	//	return item.ID
	//})
	////查找被替换的subject的randomList
	//type replace struct {
	//	ID      primitive.ObjectID    `json:"id" bson:"id"`
	//	Factors []models.RandomFactor `json:"factors" bson:"factors"`
	//	Country []string              `json:"country" bson:"country"`
	//}
	//replaceSubjects := make([]replace, 0)
	//cursor, err = tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
	//	{{Key: "$match", Value: bson.M{"replace_subject_id": bson.M{"$in": replaceSubjectIds}}}},
	//	{{Key: "$lookup", Value: bson.M{
	//		"from":         "random_list",
	//		"localField":   "random_list_id",
	//		"foreignField": "_id",
	//		"as":           "random_list",
	//	}}},
	//	{{Key: "$unwind", Value: "$random_list"}},
	//	{{Key: "$lookup", Value: bson.M{
	//		"from":         "project_site",
	//		"localField":   "project_site_id",
	//		"foreignField": "_id",
	//		"as":           "project_site",
	//	}}},
	//	{{Key: "$unwind", Value: "$project_site"}},
	//	{{Key: "$project", Value: bson.M{
	//		"id":      "$replace_subject_id",
	//		"factors": "$random_list.design.factors",
	//		"country": "$project_site.country",
	//	}}},
	//})
	//if err != nil {
	//	return "", nil, errors.WithStack(err)
	//}
	//err = cursor.All(nil, &replaceSubjects)
	//if err != nil {
	//	return "", nil, errors.WithStack(err)
	//}
	//if len(replaceSubjects) > 0 {
	//	replaceMap := make(map[primitive.ObjectID]replace)
	//	for _, subject := range replaceSubjects {
	//		replaceMap[subject.ID] = subject
	//	}
	for i := 0; i < len(statisticsData); i++ {
		if statisticsData[i].RandomListId.IsZero() {
			replaceFactors := randomDesignsMap[statisticsData[i].CohortID].Info.Factors
			factorInfo := slice.Filter(statisticsData[i].Info, func(index int, item models.Info) bool {
				return strings.Contains(item.Name, "factor")
			})
			factors := make([]models.Factors, 0)
			for _, info := range factorInfo {
				if replaceFactors != nil && len(replaceFactors) > 0 && strings.Contains(info.Name, "factor") && !strings.Contains(info.Name, "Weight") && !strings.Contains(info.Name, "Birthday") && !strings.Contains(info.Name, "Height") {
					randomFactorP, _ := slice.Find(replaceFactors, func(index int, item models.RandomFactor) bool {
						return item.Name == info.Name
					})
					rf := *randomFactorP
					text := ""
					for _, o := range rf.Options {
						if info.Value != nil && o.Value == info.Value.(string) {
							text = o.Label
						}
					}
					if info.Value != nil {
						factors = append(factors, models.Factors{
							Name:  info.Name,
							Label: rf.Label,
							Value: info.Value.(string),
							Text:  text,
						})
					}
				}
			}
			statisticsData[i].Factors = factors
		}
	}
	//}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return "", nil, err
	}
	role, err := tools.GetRole(roleId)
	if err != nil {
		return "", nil, err
	}
	if role.Scope == "depot" {
		statisticsData = make([]statistics, 0)
	} else if role.Scope == "site" {
		sites, err := tools.GetRoleSite(ctx, envId)
		if err != nil {
			return "", nil, err
		}
		statisticsData = slice.Filter(statisticsData, func(index int, item statistics) bool {
			return slice.Contain(sites, item.ProjectSiteId)
		})
	}
	//sheet1
	type factor struct {
		ID     primitive.ObjectID `json:"id" bson:"id"`
		Factor models.Factors     `json:"factors" bson:"factors"`
	}
	factors := make([]factor, 0)
	for _, s := range statisticsData {
		for _, f := range s.Factors {
			_, ok := slice.Find(s.Info, func(index int, info models.Info) bool {
				return info.Name == f.Name
			})
			if ok {
				factors = append(factors, factor{
					ID:     s.ID,
					Factor: f,
				})
			}
		}
	}

	factorMap := make(map[string][]factor)
	factors = slice.Filter(factors, func(index int, item factor) bool {
		return item.Factor.Text != ""
	})
	factorMap = slice.GroupWith(factors, func(t factor) string {
		return t.Factor.Label + "-" + t.Factor.Text
	})
	type respItem struct {
		Name  string `json:"name"`
		Value int    `json:"value"`
	}
	respArray := make([]respItem, 0)
	for k, v := range factorMap {
		respArray = append(respArray, respItem{
			Name:  k,
			Value: len(v),
		})
	}
	err = slice.SortByField(respArray, "Name")
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	sheet1Title := []interface{}{
		locales.TrStash(ctx, "report.attributes.project.number"),
		locales.TrStash(ctx, "report.attributes.project.name"),
	}
	sheet1ContentItem := []interface{}{
		project.Number,
		project.Name,
	}
	//分层因素枚举
	for _, r := range respArray {
		sheet1Title = append(sheet1Title, r.Name)
		sheet1ContentItem = append(sheet1ContentItem, r.Value)
	}
	if project.Type == 2 || project.Type == 3 {
		envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == envOID
		})
		env := *envP
		cohortNames := make([]string, 0)
		if len(cohortOIds) > 0 {
			for _, cohort := range env.Cohorts {
				if slice.Contain(cohortOIds, cohort.ID) {
					cohortNames = append(cohortNames, cohort.Name)
				}
			}
		} else {
			for _, cohort := range env.Cohorts {
				cohortNames = append(cohortNames, cohort.Name)
			}
		}
		cohortName := strings.Join(cohortNames, "&")
		if project.Type == 2 {
			sheet1Title = append(sheet1Title, locales.Tr(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			sheet1Title = append(sheet1Title, locales.Tr(ctx, "report.attributes.random.stage"))
		}
		sheet1ContentItem = append(sheet1ContentItem, cohortName)
	}

	sheet1Content := [][]interface{}{sheet1ContentItem}

	//sheet2
	type randomTimeStrItem struct {
		ID         primitive.ObjectID `json:"id"`
		RandomTime string             `json:"randomTime"`
	}
	monthArray := slice.Map(statisticsData, func(index int, item statistics) randomTimeStrItem {
		randomTime := time.Unix(item.RandomTime.Nanoseconds(), 0).UTC().Format("2006.01")
		return randomTimeStrItem{
			ID:         item.ID,
			RandomTime: randomTime,
		}
	})
	weekArray := slice.Map(statisticsData, func(index int, item statistics) randomTimeStrItem {
		randomTime := tools.GetOfWeek(time.Unix(item.RandomTime.Nanoseconds(), 0).UTC(), "2006.01.02", time.Monday) +
			"~" +
			tools.GetOfWeek(time.Unix(item.RandomTime.Nanoseconds(), 0).UTC(), "2006.01.02", time.Sunday)

		return randomTimeStrItem{
			ID:         item.ID,
			RandomTime: randomTime,
		}
	})
	monthMap := make(map[string][]randomTimeStrItem)
	weekMap := make(map[string][]randomTimeStrItem)
	monthMap = slice.GroupWith(monthArray, func(t randomTimeStrItem) string {
		return t.RandomTime
	})
	weekMap = slice.GroupWith(weekArray, func(t randomTimeStrItem) string {
		return t.RandomTime
	})
	currentMonth := make([]respItem, 0)
	cumulativeMonth := make([]respItem, 0)
	currentWeek := make([]respItem, 0)
	cumulativeWeek := make([]respItem, 0)
	for k, v := range monthMap {
		currentMonth = append(currentMonth, respItem{
			Name:  k,
			Value: len(v),
		})
	}
	err = slice.SortByField(currentMonth, "Name")
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	//每月
	for i, c := range currentMonth {
		if i == 0 {
			cumulativeMonth = append(cumulativeMonth, respItem{
				Name:  c.Name,
				Value: c.Value,
			})
		} else {
			cumulativeMonth = append(cumulativeMonth, respItem{
				Name:  c.Name,
				Value: c.Value + cumulativeMonth[i-1].Value,
			})
		}
	}
	//每周
	for k, v := range weekMap {
		currentWeek = append(currentWeek, respItem{
			Name:  k,
			Value: len(v),
		})
	}
	err = slice.SortByField(currentWeek, "Name")
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	for i, c := range currentWeek {
		if i == 0 {
			cumulativeWeek = append(cumulativeWeek, respItem{
				Name:  c.Name,
				Value: c.Value,
			})
		} else {
			cumulativeWeek = append(cumulativeWeek, respItem{
				Name:  c.Name,
				Value: c.Value + cumulativeWeek[i-1].Value,
			})
		}
	}
	sheet2Title := []interface{}{
		locales.Tr(ctx, "report.attributes.project.number"),
		locales.Tr(ctx, "report.attributes.project.name"),
	}
	sheet2ContentItem := []interface{}{
		project.Number,
		project.Name,
	}
	for i, w := range currentMonth {
		sheet2Title = append(sheet2Title, w.Name)
		sheet2ContentItem = append(sheet2ContentItem, fmt.Sprintf("%d/%d", w.Value, cumulativeMonth[i].Value))
	}
	for i, w := range currentWeek {
		sheet2Title = append(sheet2Title, w.Name)
		sheet2ContentItem = append(sheet2ContentItem, fmt.Sprintf("%d/%d", w.Value, cumulativeWeek[i].Value))
	}
	if project.Type == 2 || project.Type == 3 {
		envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == envOID
		})
		env := *envP
		cohortNames := make([]string, 0)
		if len(cohortOIds) > 0 {
			for _, cohort := range env.Cohorts {
				if slice.Contain(cohortOIds, cohort.ID) {
					cohortNames = append(cohortNames, cohort.Name)
				}
			}
		} else {
			for _, cohort := range env.Cohorts {
				cohortNames = append(cohortNames, cohort.Name)
			}
		}
		cohortName := strings.Join(cohortNames, "&")
		if project.Type == 2 {
			sheet2Title = append(sheet2Title, locales.Tr(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			sheet2Title = append(sheet2Title, locales.Tr(ctx, "report.attributes.random.stage"))
		}
		sheet2ContentItem = append(sheet2ContentItem, cohortName)
	}
	sheet2Content := [][]interface{}{sheet2ContentItem}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", multilingual.TrBatch(ctx, sheet1Title), sheet1Content)
	f.NewSheet("Sheet2")
	tools.ExportSheet(f, "Sheet2", sheet2Title, sheet2Content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	fileName := fmt.Sprintf("%s[%s]RandomazationStatisticsExport_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))
	return fileName, buffer.Bytes(), nil
}

func ExportRandomizationSimulationResult(ctx *gin.Context, projectID string, envID string, cohortIds, parameterSimulateRandomIds []interface{}, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	var cohortOIDs []primitive.ObjectID
	for _, cohortId := range cohortIds {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortId.(string))
		cohortOIDs = append(cohortOIDs, cohortOID)
	}
	var simulateRandomOIDs []primitive.ObjectID

	for _, simulateRandomId := range parameterSimulateRandomIds {
		simulateRandomOID, _ := primitive.ObjectIDFromHex(simulateRandomId.(string))
		simulateRandomOIDs = append(simulateRandomOIDs, simulateRandomOID)
	}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP
	cohorts := env.Cohorts
	if len(cohortOIDs) > 0 {
		cohorts = slice.Filter(cohorts, func(index int, item models.Cohort) bool {
			return slice.Contain(cohortOIDs, item.ID)
		})
	}
	filter := bson.M{"env_id": envOID, "_id": bson.M{"$in": simulateRandomOIDs}}
	if len(cohortIds) > 0 {
		filter = bson.M{"env_id": envOID, "cohort_id": bson.M{"$in": cohortOIDs}, "_id": bson.M{"$in": simulateRandomOIDs}}
	}
	simulateRandoms := make([]models.SimulateRandom, 0)
	cursor, err := tools.Database.Collection("simulate_random").Find(nil, filter)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &simulateRandoms)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	simulateRandomIds := slice.Map(simulateRandoms, func(index int, item models.SimulateRandom) primitive.ObjectID {
		return item.ID
	})
	simulateSubjects := make([]models.SimulateSubject, 0)
	cursor, err = tools.Database.Collection("simulate_subject").Find(nil, bson.M{"simulate_random_id": bson.M{"$in": simulateRandomIds}, "done": true})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &simulateSubjects)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	numbers := slice.Map(simulateSubjects, func(index int, item models.SimulateSubject) string {
		return item.RandomNumber
	})
	randomListIds := slice.Map(simulateSubjects, func(index int, item models.SimulateSubject) primitive.ObjectID {
		return item.RandomListID
	})
	randomListIds = slice.Unique(randomListIds)
	randomNumbers := make([]models.RandomNumber, 0)
	cursor, err = tools.Database.Collection("random_number").Find(nil, bson.M{"number": bson.M{"$in": numbers}, "status": 1, "random_list_id": bson.M{"$in": randomListIds}})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomNumbers)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	randomLists := make([]models.RandomList, 0)
	cursor, err = tools.Database.Collection("random_list").Find(nil, bson.M{"_id": bson.M{"$in": randomListIds}})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	haveSubGroup := false
	_, haveSubGroup = slice.Find(randomLists, func(index int, item models.RandomList) bool {
		_, b := slice.Find(item.Design.Groups, func(index int, i models.RandomListGroup) bool {
			return i.SubName != ""
		})
		return b
	})
	needBlock := false
	_, b := slice.Find(randomLists, func(index int, item models.RandomList) bool {
		return item.Design.Type == 1
	})
	if b {
		needBlock = true
	}

	countryCount := slice.Count(simulateSubjects, func(index int, item models.SimulateSubject) bool {
		return item.CountryName != ""
	})
	regionCount := slice.Count(simulateSubjects, func(index int, item models.SimulateSubject) bool {
		return item.RegionName != ""
	})
	title := []interface{}{
		locales.Tr(ctx, "report.attributes.project.number"),
		locales.Tr(ctx, "report.attributes.project.name"),
		locales.Tr(ctx, "report.simulate.random.name"),
		locales.Tr(ctx, "simulated.random.list.runCount"),
	}
	if countryCount > 0 {
		title = append(title, locales.Tr(ctx, "report.attributes.info.site.country"))
	}
	if regionCount > 0 {
		title = append(title, locales.Tr(ctx, "report.attributes.info.site.region"))
	}
	title = append(title, locales.Tr(ctx, "report.attributes.info.site.name"))

	match := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match, &options.FindOneOptions{
		Sort: bson.D{{"_id", 1}},
	}).Decode(&attribute)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	if project.Type == 2 {
		title = append(title, locales.Tr(ctx, "report.attributes.random.cohort"))
	}
	if project.Type == 3 {
		title = append(title, locales.Tr(ctx, "report.attributes.random.stage"))
	}
	if attribute.AttributeInfo.SubjectReplaceText != "" {
		title = append(title, attribute.AttributeInfo.SubjectReplaceText)
	} else {
		title = append(title, locales.Tr(ctx, "export.subject.number"))
	}

	subjectMap := slice.GroupWith(simulateSubjects, func(t models.SimulateSubject) primitive.ObjectID {
		return t.SimulateRandomID
	})
	content := make([][]interface{}, 0)
	if project.Type == 1 || project.Type == 2 {
		if needBlock {
			title = append(title, locales.Tr(ctx, "report.simulate.random.block"))
		}
		title = append(title, locales.Tr(ctx, "report.attributes.random.number"))
		title = append(title, locales.Tr(ctx, "report.attributes.random.config.code"))
		title = append(title, locales.Tr(ctx, "report.simulate.random.group"))
		if haveSubGroup {
			title = append(title, locales.Tr(ctx, "report.simulate.random.subGroup"))
		}
		randomFactors := getEffectiveFlatFactor(randomLists)
		values := getGroupValueGroups(randomLists)
		if len(randomFactors) > 0 {
			for _, factor := range randomFactors {
				title = append(title, fmt.Sprintf("%s(%s)", locales.Tr(ctx, "simulated.random.list.factor"), factor))
			}
		}
		if len(values) > 0 {
			title = append(title, locales.Tr(ctx, "plan_number"))
			for _, v := range values {
				title = append(title, fmt.Sprintf("%s%s", "G", v))
			}
		}

		for _, simulateRandom := range simulateRandoms {
			subjects := subjectMap[simulateRandom.ID]
			for _, subject := range subjects {
				randomNumberP, randomNumberOK := slice.Find(randomNumbers, func(index int, number models.RandomNumber) bool {
					return subject.RandomNumber == number.Number
				})
				if randomNumberOK {
					randomNumber := *randomNumberP
					randomListP, randomListOK := slice.Find(randomLists, func(index int, list models.RandomList) bool {
						return randomNumber.RandomListID == list.ID
					})
					if randomListOK {
						randomList := *randomListP
						groups := randomList.Design.Groups
						gruopP, gruopOK := slice.Find(groups, func(index int, group models.RandomListGroup) bool {
							return group.Name == subject.GroupName
						})
						if gruopOK {
							group := *gruopP
							contentItem := make([]interface{}, 0)
							contentItem = append(contentItem, project.Number)
							contentItem = append(contentItem, project.Name)
							contentItem = append(contentItem, simulateRandom.SimulateRandomInfo.Name)
							contentItem = append(contentItem, subject.RunCount)
							if countryCount > 0 {
								contentItem = append(contentItem, subject.CountryName)
							}
							if regionCount > 0 {
								contentItem = append(contentItem, subject.RegionName)
							}
							contentItem = append(contentItem, subject.SiteName)
							if project.Type == 2 {
								cohort := ""
								for _, c := range cohorts {
									if c.ID == randomList.CohortID {
										cohort = c.Name
										break
									}
								}
								contentItem = append(contentItem, cohort)
							}
							contentItem = append(contentItem, subject.Name)
							if needBlock {
								if randomList.Design.Type == 1 {
									contentItem = append(contentItem, randomNumber.Block)
								} else {
									contentItem = append(contentItem, "")
								}
							}
							contentItem = append(contentItem, subject.RandomNumber)
							contentItem = append(contentItem, group.Code)
							if subject.SubName != "" {
								contentItem = append(contentItem, subject.ParName)
							} else {
								contentItem = append(contentItem, subject.GroupName)
							}
							if haveSubGroup {
								contentItem = append(contentItem, subject.SubName)
							}
							if len(randomFactors) > 0 {
								factorFields := []models.Field{}
								if randomListP.Design.Factors != nil {
									for i := 0; i < len(randomList.Design.Factors); i++ {
										if randomList.Design.Factors[i].Status == nil || *randomList.Design.Factors[i].Status == 1 {
											factorFields = append(factorFields, models.Field{
												Name:           randomList.Design.Factors[i].Name,
												Label:          randomList.Design.Factors[i].Label,
												Type:           randomList.Design.Factors[i].Type,
												Options:        randomList.Design.Factors[i].Options,
												Modifiable:     true, // 标记可修改
												Stratification: true, // 标记是分层因素
												Status:         randomList.Design.Factors[i].Status,
											})
										}
									}
								}
								for _, factor := range randomFactors {
									label := ""
									field, b := slice.Find(factorFields, func(index int, item models.Field) bool {
										return item.Label == factor
									})
									if b {
										info, b2 := slice.Find(subject.Info, func(index int, item models.Info) bool {
											return item.Name == field.Name
										})
										if b2 {
											option, b3 := slice.Find(field.Options, func(index int, item models.Option) bool {
												return item.Value == info.Value
											})
											if b3 {
												label = option.Label
											}
										}
									}
									contentItem = append(contentItem, label)
								}
							}
							if len(values) > 0 {
								if randomList.Design.Type == 2 {
									contentItem = append(contentItem, subject.PlanNumber)
								} else {
									contentItem = append(contentItem, "")
								}
								for _, v := range values {
									value := ""
									for _, groupValue := range subject.GroupValue {
										if groupValue.Group == v {
											value = fmt.Sprintf("%f", groupValue.Value)
											break
										}
									}
									contentItem = append(contentItem, value)
								}
							}

							content = append(content, contentItem)
						}
					}
				}
			}
		}
	} else if project.Type == 3 {
		cohortColumn := make([]int, 0)
		for _, cohort := range cohorts {
			column := 0
			title = append(title, fmt.Sprintf("%s%s", cohort.Name, locales.Tr(ctx, "report.simulate.random.block")))
			title = append(title, fmt.Sprintf("%s%s", cohort.Name, locales.Tr(ctx, "report.attributes.random.number")))
			title = append(title, fmt.Sprintf("%s%s", cohort.Name, locales.Tr(ctx, "report.attributes.random.config.code")))
			title = append(title, fmt.Sprintf("%s%s", cohort.Name, locales.Tr(ctx, "report.simulate.random.group")))
			if haveSubGroup {
				title = append(title, fmt.Sprintf("%s%s", cohort.Name, locales.Tr(ctx, "report.simulate.random.subGroup")))
			}
			column = column + 4
			cohortLists := slice.Filter(randomLists, func(index int, item models.RandomList) bool {
				return item.CohortID == cohort.ID
			})
			randomFactors := getEffectiveRandomFactor(cohortLists)
			values := getGroupValueGroups(cohortLists)
			if len(randomFactors) > 0 {
				for _, factor := range randomFactors {
					title = append(title, fmt.Sprintf("%s%s(%s)", cohort.Name, locales.Tr(ctx, "simulated.random.list.factor"), factor.Label))
				}
				column = column + len(randomFactors)
			}
			if len(values) > 0 {
				title = append(title, fmt.Sprintf("%s%s", cohort.Name, locales.Tr(ctx, "plan_number")))
				for _, v := range values {
					title = append(title, fmt.Sprintf("%s%s%s", cohort.Name, "G", v))
				}
				column = column + 1 + len(values)
			}
			cohortColumn = append(cohortColumn, column)
		}
		for ci, cohort := range cohorts {
			cohortLists := slice.Filter(randomLists, func(index int, item models.RandomList) bool {
				return item.CohortID == cohort.ID
			})
			randomFactors := getEffectiveFlatFactor(cohortLists)
			values := getGroupValueGroups(cohortLists)
			cohortRandoms := slice.Filter(simulateRandoms, func(index int, item models.SimulateRandom) bool {
				return item.CohortID == cohort.ID
			})
			sum := func(_ int, v1, v2 int) int {
				return v1 + v2
			}
			for _, simulateRandom := range cohortRandoms {
				subjects := subjectMap[simulateRandom.ID]
				for _, subject := range subjects {
					randomNumberP, randomNumberOK := slice.Find(randomNumbers, func(index int, number models.RandomNumber) bool {
						return subject.RandomNumber == number.Number
					})
					if randomNumberOK {
						randomNumber := *randomNumberP
						randomListP, randomListOK := slice.Find(randomLists, func(index int, list models.RandomList) bool {
							return randomNumber.RandomListID == list.ID
						})
						if randomListOK {
							randomList := *randomListP

							groups := randomList.Design.Groups
							gruopP, gruopOK := slice.Find(groups, func(index int, group models.RandomListGroup) bool {
								return group.Name == subject.GroupName
							})
							if gruopOK {
								group := *gruopP
								contentItem := make([]interface{}, 0)
								contentItem = append(contentItem, project.Number)
								contentItem = append(contentItem, project.Name)
								contentItem = append(contentItem, simulateRandom.SimulateRandomInfo.Name)
								contentItem = append(contentItem, subject.RunCount)
								if countryCount > 0 {
									contentItem = append(contentItem, subject.CountryName)
								}
								if regionCount > 0 {
									contentItem = append(contentItem, subject.RegionName)
								}
								contentItem = append(contentItem, subject.SiteName)
								contentItem = append(contentItem, cohort.Name)
								contentItem = append(contentItem, subject.Name)
								if ci != 0 {
									for i := 0; i < slice.Reduce(cohortColumn[:ci], sum, 0); i++ {
										contentItem = append(contentItem, "")
									}
								}
								if randomList.Design.Type == 1 {
									if randomNumber.Block != 0 {
										contentItem = append(contentItem, randomNumber.Block)
									} else {
										contentItem = append(contentItem, "")
									}
								} else {
									contentItem = append(contentItem, "")
								}
								contentItem = append(contentItem, subject.RandomNumber)
								contentItem = append(contentItem, group.Code)
								parName := subject.GroupName
								if subject.ParName != "" {
									parName = subject.ParName
								}
								contentItem = append(contentItem, parName)
								if haveSubGroup {
									contentItem = append(contentItem, subject.SubName)
								}
								if len(randomFactors) > 0 {
									factorFields := []models.Field{}
									if randomListP.Design.Factors != nil {
										for i := 0; i < len(randomList.Design.Factors); i++ {
											if randomList.Design.Factors[i].Status == nil || *randomList.Design.Factors[i].Status == 1 {
												factorFields = append(factorFields, models.Field{
													Name:           randomList.Design.Factors[i].Name,
													Label:          randomList.Design.Factors[i].Label,
													Type:           randomList.Design.Factors[i].Type,
													Options:        randomList.Design.Factors[i].Options,
													Modifiable:     true, // 标记可修改
													Stratification: true, // 标记是分层因素
													Status:         randomList.Design.Factors[i].Status,
												})
											}
										}
									}
									for _, factor := range randomFactors {
										label := ""
										field, b := slice.Find(factorFields, func(index int, item models.Field) bool {
											return item.Label == factor
										})
										if b {
											info, b2 := slice.Find(subject.Info, func(index int, item models.Info) bool {
												return item.Name == field.Name
											})
											if b2 {
												option, b3 := slice.Find(field.Options, func(index int, item models.Option) bool {
													return item.Value == info.Value
												})
												if b3 {
													label = option.Label
												}
											}
										}
										contentItem = append(contentItem, label)
									}
								}
								if len(values) > 0 {
									if randomList.Design.Type == 2 {
										contentItem = append(contentItem, subject.PlanNumber)
									} else {
										contentItem = append(contentItem, "")
									}
									for _, v := range values {
										value := ""
										for _, groupValue := range subject.GroupValue {
											if groupValue.Group == v {
												value = fmt.Sprintf("%f", groupValue.Value)
												break
											}
										}
										contentItem = append(contentItem, value)
									}
								}
								if ci != len(cohorts)-1 {
									reduce := slice.Reduce(cohortColumn[ci+1:], sum, 0)
									for i := 0; i < reduce; i++ {
										contentItem = append(contentItem, "")
									}
								}
								content = append(content, contentItem)
							}
						}
					}
				}
			}
		}
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	fileName := fmt.Sprintf("%s[%s]RandomizationSimulationResult_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))
	return fileName, buffer.Bytes(), nil
}

func ExportSimulateRandomReportPdf(ctx *gin.Context, projectID string, envID string, cohortID string, roleId string, simulateRandomId string, now time.Time) (models.SimulateRandomReport, error) {

	var pdfData models.SimulateRandomReport

	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	simulateRandomOID, _ := primitive.ObjectIDFromHex(simulateRandomId)

	condition := bson.M{"project_id": projectOID, "env_id": envOID}
	oid := envOID
	if cohortOID != primitive.NilObjectID {
		oid = cohortOID
		condition["cohort_id"] = cohortOID
	}

	var attribute models.Attribute
	if err := tools.Database.Collection("attribute").FindOne(nil, condition).Decode(&attribute); err != nil {
		return pdfData, errors.WithStack(err)
	}

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return pdfData, err
	}

	user, err := tools.Me(ctx)
	if err != nil {
		return pdfData, err
	}
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return pdfData, errors.WithStack(err)
	}

	operationLogFilter := bson.M{"module": "operation_log.module.simulate_random", "oid": oid, "operatorId": simulateRandomOID, "type": 4}
	operationLogList := make([]models.OperationLog, 0)
	opt := &options.FindOptions{
		Sort: bson.D{{"_id", -1}},
	}
	cursor, err := tools.Database.Collection("operation_log").Find(nil, operationLogFilter, opt)
	if err != nil {
		return pdfData, errors.WithStack(err)
	}
	err = cursor.All(nil, &operationLogList)
	if err != nil {
		return pdfData, errors.WithStack(err)
	}

	hour := time.Duration(timeZone)
	minute := time.Duration((timeZone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	runningDate := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")
	if operationLogList != nil && len(operationLogList) > 0 {
		runningDate = time.Unix(operationLogList[0].Time.Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	}

	generationDate := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")

	envName := ""
	cohortName := ""
	if project.Environments != nil && len(project.Environments) > 0 {
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				envName = environment.Name
				if cohortOID != primitive.NilObjectID {
					if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
						for _, cohort := range environment.Cohorts {
							if cohortOID == cohort.ID {
								cohortName = cohort.Name
							}
						}
					}
				}
			}
		}
	}

	userName := ""
	if user.Unicode == 0 {
		userName = user.Name
	} else {
		userName = fmt.Sprintf("%s(%d)", user.Name, user.Unicode)
	}

	pdfData = models.SimulateRandomReport{
		Sponsor:              project.Sponsor,
		ProjectNumber:        project.Number,
		SimulateRandomReport: locales.Tr(ctx, "export.random_config.simulation_pdf"),
		Env:                  envName,
		Cohort:               cohortName,
		RunningTime:          locales.Tr(ctx, "export.random_config.runningTime"),
		RunningDate:          runningDate,
		GenerationTime:       locales.Tr(ctx, "export.random_config.generationTime"),
		GenerationDate:       generationDate,
		Generator:            locales.Tr(ctx, "export.random_config.createBy"),
		CreateBy:             userName,
		Directory:            locales.Tr(ctx, "export.random_config.directory"),
		Summary:              locales.Tr(ctx, "export.random_config.summary"),
		SummaryDetails:       locales.Tr(ctx, "export.random_config.report_pdf"),
	}

	simulateRandomFilter := bson.M{"_id": simulateRandomOID}
	var simulateRandom models.SimulateRandom
	err = tools.Database.Collection("simulate_random").FindOne(ctx, simulateRandomFilter).Decode(&simulateRandom)
	if err != nil {
		return pdfData, errors.WithStack(err)
	}
	var randomDesign models.RandomDesign
	filter := bson.M{"env_id": envOID}
	if !cohortOID.IsZero() {
		filter["cohort_id"] = cohortOID
	}
	if err := tools.Database.Collection("random_design").FindOne(ctx, filter).Decode(&randomDesign); err != nil {
		return pdfData, errors.WithStack(err)
	}

	var configureParameterDetail models.ConfigureParameterDetail
	configureParameterDetail.Title = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail")

	randomListNameList := make([]string, 0)
	configureParameterInfoList := make([]models.ConfigureParameterInfo, 0)
	if simulateRandom.SimulateRandomInfo.RandomListIds != nil && len(simulateRandom.SimulateRandomInfo.RandomListIds) > 0 {
		for _, randomListId := range simulateRandom.SimulateRandomInfo.RandomListIds {
			randomListOId, _ := primitive.ObjectIDFromHex(randomListId)
			randomListFilter := bson.M{"_id": randomListOId}
			var randomList models.RandomList
			err = tools.Database.Collection("random_list").FindOne(ctx, randomListFilter).Decode(&randomList)
			if err != nil {
				return pdfData, errors.WithStack(err)
			}

			randomListNameList = append(randomListNameList, randomList.Name)
			var configureParameterInfo models.ConfigureParameterInfo
			configureParameterInfo.Type = strconv.Itoa(randomList.Design.Type)
			configureParameterInfo.RandomTypeLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.randomType")
			if randomList.Design.Type == 1 {
				configureParameterInfo.RandomType = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.randomType.blockRandomization")
			} else if randomList.Design.Type == 2 {
				configureParameterInfo.RandomType = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.randomType.minimizedRandomization")
			}
			configureParameterInfo.RandomTableNameLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.randomizationListName")
			configureParameterInfo.RandomTableName = randomList.Name
			configureParameterInfo.BiasProbabilityLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.biasProbability")
			if randomList.Design.Type == 1 {
				configureParameterInfo.BiasProbability = ""
			} else if randomList.Design.Type == 2 {
				configureParameterInfo.BiasProbability = strconv.FormatFloat(randomList.Config.Probability, 'f', -1, 64)
			}
			configureParameterInfo.RandomIdNumberLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.randomCount")
			randomNumberMatch := bson.M{
				"random_list_id": randomList.ID,
			}
			randomNumberCursor, _ := tools.Database.Collection("random_number").CountDocuments(nil, randomNumberMatch)
			configureParameterInfo.RandomIdNumber = strconv.FormatInt(randomNumberCursor, 10)

			//组别配置
			var groupConfigurationDetail models.GroupConfigurationDetail
			groupConfigurationDetail.GroupConfigurationLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.groupConfiguration")
			groupConfigurationDetail.GroupNameLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.groupConfiguration.groupName")
			groupConfigurationDetail.SubGroupNameLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.groupConfiguration.subGroupName")
			groupConfigurationDetail.GroupProportionLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.groupConfiguration.groupProportion")
			groupDetailList := make([]models.GroupDetail, 0)
			if randomList.Design.Groups != nil && len(randomList.Design.Groups) > 0 {
				for _, group := range randomList.Design.Groups {
					var groupDetail models.GroupDetail
					groupDetail.GroupName = group.ParName
					groupDetail.SubGroupName = group.SubName
					groupDetail.GroupProportion = strconv.Itoa(group.Ratio)
					groupDetailList = append(groupDetailList, groupDetail)
				}
			}
			groupConfigurationDetail.GroupDetailList = groupDetailList
			configureParameterInfo.GroupConfigurationDetail = groupConfigurationDetail

			//分层因素
			var factorConfigurationDetail models.FactorConfigurationDetail
			factorConfigurationDetail.FactorConfigurationLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.stratificationFactor")
			factorConfigurationDetail.FactorNameLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.stratificationFactor.factorName")
			factorConfigurationDetail.OptionLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.stratificationFactor.option")
			factorConfigurationDetail.WeightRatioLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.stratificationFactor.weightRatio")
			factorDetailList := make([]models.FactorDetail, 0)
			if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
				for _, factor := range randomList.Design.Factors {
					var factorDetail models.FactorDetail
					factorDetail.FactorName = factor.Label
					if randomList.Design.Type == 1 {
						factorDetail.WeightRatio = ""
					} else if randomList.Design.Type == 2 {
						factorDetail.WeightRatio = strconv.Itoa(factor.Ratio)
					}
					if factor.Options != nil && len(factor.Options) > 0 {
						for _, option := range factor.Options {
							factorDetail.Option = option.Label
							factorDetailList = append(factorDetailList, factorDetail)
						}
					}
				}
			}
			factorConfigurationDetail.FactorDetailList = factorDetailList
			configureParameterInfo.FactorConfigurationDetail = factorConfigurationDetail

			configureParameterInfo.IsCountryLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isCountry")
			if attribute.AttributeInfo.CountryLayered {
				configureParameterInfo.IsCountry = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isCountry.true")
			} else {
				configureParameterInfo.IsCountry = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isCountry.false")
			}
			configureParameterInfo.IsSiteLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isSite")
			if attribute.AttributeInfo.InstituteLayered {
				configureParameterInfo.IsSite = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isSite.true")
			} else {
				configureParameterInfo.IsSite = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isSite.false")
			}
			configureParameterInfo.IsRegionLabel = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isRegion")
			if attribute.AttributeInfo.RegionLayered {
				configureParameterInfo.IsRegion = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isRegion.true")
			} else {
				configureParameterInfo.IsRegion = locales.Tr(ctx, "simulateRandomReport.configureParameterDetail.isRegion.false")
			}

			configureParameterInfoList = append(configureParameterInfoList, configureParameterInfo)
		}
	}
	configureParameterDetail.ConfigureParameterInfoList = configureParameterInfoList
	pdfData.ConfigureParameterDetail = configureParameterDetail

	var simulateRandomDetail models.SimulateRandomDetail
	simulateRandomDetail.Title = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail")
	simulateRandomDetail.ProjectLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.project")
	simulateRandomDetail.SerialLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.serial")
	simulateRandomDetail.SiteLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.site")
	simulateRandomDetail.RegionLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.region")
	simulateRandomDetail.CountryLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.country")
	simulateRandomDetail.FactorLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.factor")
	simulateRandomDetail.CombinationFactorLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.combinationFactor")
	simulateRandomDetail.SubjectCountMinLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.subjectCountMin")
	simulateRandomDetail.TotalLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.total")
	simulateRandomDetail.RunCountLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.runCount")
	simulateRandomDetail.PeopleCountLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.peopleCount")
	simulateRandomDetail.UnbalancedLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.label.unbalanced")

	var simulateRandomParameterDetail models.SimulateRandomParameterDetail
	simulateRandomParameterDetail.Title = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter")
	simulateRandomParameterDetail.SimulateRandomNameLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.simulateRandomName")
	simulateRandomParameterDetail.SimulateRandomName = simulateRandom.SimulateRandomInfo.Name
	simulateRandomParameterDetail.RandomListNameLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.randomListName")
	simulateRandomParameterDetail.RandomListName = strings.Join(randomListNameList, ",")
	simulateRandomParameterDetail.SiteNumberLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.siteNumber")
	simulateRandomParameterDetail.SiteNumber = strconv.Itoa(simulateRandom.SimulateRandomInfo.SiteQuantity)
	simulateRandomParameterDetail.RegionNumberLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.regionNumber")
	simulateRandomParameterDetail.RegionNumber = strconv.Itoa(simulateRandom.SimulateRandomInfo.RegionQuantity)
	simulateRandomParameterDetail.CountryNumberLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.countryNumber")
	simulateRandomParameterDetail.CountryNumber = strconv.Itoa(simulateRandom.SimulateRandomInfo.CountryQuantity)
	simulateRandomParameterDetail.RunNumberLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.runNumber")
	simulateRandomParameterDetail.RunNumber = strconv.Itoa(simulateRandom.SimulateRandomInfo.RunQuantity)
	simulateRandomParameterDetail.SubjectQuantityLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.subjectQuantity")
	simulateRandomParameterDetail.SubjectQuantity = strconv.Itoa(simulateRandom.SimulateRandomInfo.SubjectQuantity)
	simulateRandomParameterDetail.FactorRatioLabel = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.parameter.factorRatio")

	ratios := make([]string, 0)
	factors := randomDesign.Info.Factors
	for _, factorRatios := range simulateRandom.SimulateRandomInfo.FactorRatio {
		for _, ratio := range factorRatios {
			for _, factor := range factors {
				for _, option := range factor.Options {
					if ratio.FactorName == factor.Name && ratio.OptionValue == option.Value {
						ratioValue := "-"
						if ratio.Ratio != nil {
							ratioValue = fmt.Sprintf("%d", *ratio.Ratio)
						}
						value := fmt.Sprintf("%s:%s/%s", factor.Label, option.Label, ratioValue)
						ratios = append(ratios, value)
					}
				}
			}
		}
	}
	simulateRandomParameterDetail.FactorRatio = ratios
	simulateRandomDetail.SimulateRandomParameterDetail = simulateRandomParameterDetail

	views, err := GetSimulateRandomList(ctx, project.CustomerID.Hex(), projectID, envID)
	if err != nil {
		return pdfData, errors.WithStack(err)
	}

	simulationRandomView := models.SimulationRandomView{}
	if views != nil && len(views) > 0 {
		for _, view := range views {
			if view.ID == simulateRandomOID {
				simulationRandomView = view
			}
		}
	}

	var overviewDetail models.OverviewDetail
	overviewDetail.Title = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.overviewDetail")

	var avgSDOverviewDetail models.AvgSDOverviewDetail
	avgSDOverviewDetail.Title = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.overviewDetail.averageStandardDeviation")
	avgSDOverviewDetail.AvgSDOverview = simulationRandomView.SimulationRandomResult.AvgSDOverview
	overviewDetail.AvgSDOverviewDetail = avgSDOverviewDetail

	var minOverviewDetail models.MinOverviewDetail
	minOverviewDetail.Title = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.overviewDetail.min")
	minOverviewDetail.MinOverview = simulationRandomView.SimulationRandomResult.MinOverview
	overviewDetail.MinOverviewDetail = minOverviewDetail

	var unbalancedRunCountOverviewDetail models.UnbalancedRunCountOverviewDetail
	unbalancedRunCountOverviewDetail.Title = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.overviewDetail.numberOfRunningImbalances")
	unbalancedRunCountOverviewDetail.UnbalancedRunCountOverview = simulationRandomView.SimulationRandomResult.UnbalancedRunCountOverview
	overviewDetail.UnbalancedRunCountOverviewDetail = unbalancedRunCountOverviewDetail

	overviewDetail.Groups = simulationRandomView.SimulationRandomResult.Groups

	simulateRandomDetail.OverviewDetail = overviewDetail

	var detailedResults models.DetailedResults
	detailedResults.Title = locales.Tr(ctx, "simulateRandomReport.simulateRandomDetail.detailedResults")
	detailedResults.UnbalancedDetails = simulationRandomView.SimulationRandomResult.UnbalancedDetails
	simulateRandomDetail.DetailedResults = detailedResults

	pdfData.SimulateRandomDetail = simulateRandomDetail

	//bytes, err := tools.ExportHtmlPdf(ctx, pdfData)
	//if err != nil {
	//	return "", nil, err
	//}
	//
	//envCode := getEnvName(project, envOID)
	//fileName := fmt.Sprintf("%s[%s]ConfigureReport_%s.pdf", project.Number, envCode, now.Format("20060102150405"))
	return pdfData, nil
}

func handleFormData(item models.ExportSubjectNumber, field models.Field, fieldValue string) (string, error) {
	for _, info := range item.Subject {
		if info.Value == nil {
			continue
		}
		if field.Name == info.Name {
			if field.Type == "radio" || field.Type == "select" {
				for _, option := range field.Options {
					if info.Value == option.Value {
						fieldValue = option.Label
					}
				}
			} else if field.Type == "checkbox" {
				var options []string
				for _, option := range field.Options {
					if info.Value != nil {
						for _, check := range info.Value.(primitive.A) {
							if check == option.Value {
								options = append(options, option.Label)
							}
						}
					}

				}
				fieldValue = strings.Join(options, ",")
			} else if field.Type == "switch" {
				if info.Value == nil {
					fieldValue = ""
				} else {
					if info.Value.(bool) {
						fieldValue = "yes"
					} else {
						fieldValue = "no"
					}
				}

			} else if field.Type == "datePicker" {
				value := info.Value.(string)
				if value == "" {
					continue
				}
				parse, err := time.Parse("2006-01-02", value)
				if err != nil {
					return "", errors.WithStack(err)
				}
				fieldValue = parse.Format(tools.DateFormatParse(field.DateFormat))
			} else if field.Type == "timePicker" {
				value := info.Value.(string)
				if value == "" {
					continue
				}
				parse, err := time.Parse("2006-01-02 15:04:05", value)
				if err != nil {
					return "", errors.WithStack(err)
				}
				fieldValue = parse.Format(tools.DateFormatParse(field.TimeFormat))
			} else {
				fieldValue = convertor.ToString(info.Value)
			}
		}
	}
	return fieldValue, nil
}
func handleFormCalcData(item models.ExportSubjectNumber, field models.Field, fieldValue string) (string, error) {
	for _, info := range item.ActualInfo {
		if info.Value == nil {
			continue
		}
		if field.Name == info.Name {
			if field.Type == "radio" || field.Type == "select" {
				for _, option := range field.Options {
					if info.Value == option.Value {
						fieldValue = option.Label
					}
				}
			} else if field.Type == "checkbox" {
				var options []string
				for _, option := range field.Options {
					if info.Value != nil {
						for _, check := range info.Value.(primitive.A) {
							if check == option.Value {
								options = append(options, option.Label)
							}
						}
					}

				}
				fieldValue = strings.Join(options, ",")
			} else if field.Type == "switch" {
				if info.Value == nil {
					fieldValue = ""
				} else {
					if info.Value.(bool) {
						fieldValue = "yes"
					} else {
						fieldValue = "no"
					}
				}

			} else if field.Type == "datePicker" {
				value := info.Value.(string)
				if value == "" {
					continue
				}
				parse, err := time.Parse("2006-01-02", value)
				if err != nil {
					return "", errors.WithStack(err)
				}
				fieldValue = parse.Format(tools.DateFormatParse(field.DateFormat))
			} else if field.Type == "timePicker" {
				value := info.Value.(string)
				if value == "" {
					continue
				}
				parse, err := time.Parse("2006-01-02 15:04:05", value)
				if err != nil {
					return "", errors.WithStack(err)
				}
				fieldValue = parse.Format(tools.DateFormatParse(field.TimeFormat))
			} else {
				fieldValue = convertor.ToString(info.Value)
			}
		}
	}
	return fieldValue, nil
}

type randomFactor struct {
	Name     string          `json:"name" bson:"name"`
	Label    string          `json:"label" bson:"label"`
	Type     string          `json:"type" bson:"type"`
	Options  []models.Option `json:"options" bson:"options"`
	Status   *int            `json:"status" bson:"status"`
	IsCalc   bool            `json:"isCalc" bson:"is_calc"`     //是否分层计算
	CalcType *int            `json:"calcType" bson:"calc_type"` //计算公式类型 0年龄 1BMI
}

// ExportReportTypeSourceRandomizationList 随机盲底
func ExportReportTypeSourceRandomizationList(ctx *gin.Context, projectID string, envID string, cohortIDs []interface{}, randomListIDs []interface{}, templateId string, now time.Time) (string, []byte, error) {
	randomListOID := slice.Map(randomListIDs, func(index int, item interface{}) primitive.ObjectID {
		OID, _ := primitive.ObjectIDFromHex(item.(string))
		return OID
	})
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	selectCohort := map[primitive.ObjectID]bool{}
	var cohortOIDs []primitive.ObjectID
	for _, cohortId := range cohortIDs {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortId.(string))
		cohortOIDs = append(cohortOIDs, cohortOID)
		selectCohort[cohortOID] = true
	}
	var project models.Project

	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	//countries, err := database.GetCountries(ctx)
	statusKey := []string{
		locales.Tr(ctx, "randomNumber.export.status.unused"),
		locales.Tr(ctx, "randomNumber.export.status.used"),
		locales.Tr(ctx, "randomNumber.export.status.invalid"),
		locales.Tr(ctx, "randomNumber.export.status.invalid"),
		locales.Tr(ctx, "randomNumber.export.status.unavailable"),
	}
	envInfoP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	envInfo := *envInfoP

	var attributes []models.Attribute
	cursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	timeZone, err := tools.GetTimeZone(projectOID)
	// 获取已选随机表配置（分层）
	var randomLists []models.RandomList
	cursor, err = tools.Database.Collection("random_list").Find(nil, bson.M{"_id": bson.M{"$in": randomListOID}})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	haveSubGroup := false
	_, haveSubGroup = slice.Find(randomLists, func(index int, item models.RandomList) bool {
		_, b := slice.Find(item.Design.Groups, func(index int, i models.RandomListGroup) bool {
			return i.SubName != ""
		})
		return b
	})

	_, showRegisterGroup := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.AllowRegisterGroup
	})

	//获取randomList所在的所有cohort
	cohortMap := map[primitive.ObjectID]string{}
	if project.Type != 1 {
		slice.Map(envInfo.Cohorts, func(index int, item models.Cohort) string {
			cohortMap[item.ID] = item.Name
			return ""
		})
	}

	// 查找所有随机号
	var randomNumber []models.ExportNumber

	// 配置表头
	_, commonHasBlock := slice.Find(randomLists, func(index int, data models.RandomList) bool {
		return data.Design.Type == 1
	})
	randomFactors := getRandomFactor(randomLists)
	groupValue := getGroupValueGroups(randomLists)
	title := []interface{}{}
	title = append(title, locales.Tr(ctx, data.ReportAttributesProjectNumber))
	title = append(title, locales.Tr(ctx, data.ReportAttributesProjectName))
	title = append(title, locales.Tr(ctx, data.ReportAttributesInfoSiteNumber))
	title = append(title, locales.Tr(ctx, data.ReportAttributesInfoSiteName))
	if project.Type == 2 {
		title = append(title, locales.Tr(ctx, data.ReportAttributesRandomCohort))
	}

	if locales.Lang(ctx) == "zh" {
		if attributes[0].AttributeInfo.SubjectReplaceText != "" {
			title = append(title, attributes[0].AttributeInfo.SubjectReplaceText)
		} else if attributes[0].AttributeInfo.SubjectReplaceTextEn != "" {
			title = append(title, attributes[0].AttributeInfo.SubjectReplaceTextEn)
		} else {
			title = append(title, locales.Tr(ctx, data.ReportAttributesInfoSubjectNumber))
		}
	} else {
		if attributes[0].AttributeInfo.SubjectReplaceTextEn != "" {
			title = append(title, attributes[0].AttributeInfo.SubjectReplaceTextEn)
		} else if attributes[0].AttributeInfo.SubjectReplaceText != "" {
			title = append(title, attributes[0].AttributeInfo.SubjectReplaceText)
		} else {
			title = append(title, locales.Tr(ctx, data.ReportAttributesInfoSubjectNumber))
		}
	}
	title = append(title, locales.Tr(ctx, data.ReportAttributesInfoStatus))
	if project.Type == 1 || project.Type == 2 {
		title = append(title, locales.Tr(ctx, data.ReportAttributesRandomTime))
		// 存在区组随机 列表
		if commonHasBlock {
			title = append(title, locales.Tr(ctx, data.ReportAttributesRandomBlock)) // 区组
		}
		title = append(title, locales.Tr(ctx, data.ReportAttributesRandomNumber)) // 随机号
		title = append(title, locales.Tr(ctx, data.ReportAttributesRandomGroup))  // 组别
		if haveSubGroup {
			title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubGroup)) // 子组别
		}
		title = append(title, locales.Tr(ctx, data.ReportAttributesRandomConfigCode)) // 组别名称
		if showRegisterGroup {
			title = append(title, locales.Tr(ctx, data.ReportAttributesDispensingMedicineRealGroup)) // 实际登记组别
		}

		if len(groupValue) > 0 {
			title = append(title, locales.Tr(ctx, data.ReportAttributesRandomPlanNumber)) // 计划随机数
			for _, item := range groupValue {
				title = append(title, "G"+item) // GA（偏倚数）
			}
		}
		// 遍历随机列表所有分层
		for _, factor := range randomFactors {
			title = append(title, factor.Label)
		}
	} else { // 再随机项目
		for _, cohort := range envInfo.Cohorts {
			if len(selectCohort) > 0 && selectCohort[cohort.ID] == false {
				continue
			}
			randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
				return data.CohortID == cohort.ID
			})
			groupValue := getGroupValueGroups(randomListCohort)
			randomFactors := getRandomFactor(randomListCohort)

			// 随机时间
			title = append(title, cohort.Name+locales.Tr(ctx, data.ReportAttributesRandomTime))

			// 存在区组随机 列表
			{
				_, ok := slice.Find(randomListCohort, func(index int, data models.RandomList) bool {
					return data.Design.Type == 1
				})
				if ok {
					title = append(title, cohort.Name+locales.Tr(ctx, data.ReportAttributesRandomBlock))
				}
			}
			// 随机号
			title = append(title, cohort.Name+locales.Tr(ctx, data.ReportAttributesRandomNumber))
			// 随机组别
			title = append(title, cohort.Name+locales.Tr(ctx, data.ReportAttributesRandomConfigCode))
			// 组别名称
			title = append(title, cohort.Name+locales.Tr(ctx, data.ReportAttributesRandomGroup))
			if haveSubGroup {
				title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubGroup)) // 子组别
			}

			if showRegisterGroup {
				title = append(title, cohort.Name+locales.Tr(ctx, data.ReportAttributesDispensingMedicineRealGroup)) // 实际登记组别
			}

			// 存在最小化随机列表
			{
				_, ok := slice.Find(randomListCohort, func(index int, data models.RandomList) bool {
					return data.Design.Type == 2
				})
				if ok {
					title = append(title, cohort.Name+locales.Tr(ctx, data.ReportAttributesRandomPlanNumber)) // 计划随机数
					for _, item := range groupValue {
						title = append(title, cohort.Name+"G"+item) // GA（偏倚数）
					}
				}
			}
			// 分层
			{
				for _, factor := range randomFactors {
					title = append(title, cohort.Name+factor.Label)
				}
			}
		}
	}
	title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubjectReplaceTime))
	subjectReplaceText := ""
	if locales.Lang(ctx) == "zh" {
		if len(attributes[0].AttributeInfo.SubjectReplaceText) != 0 {
			subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceText
		} else {
			if len(attributes[0].AttributeInfo.SubjectReplaceTextEn) != 0 {
				subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceTextEn
			}
		}
	} else if locales.Lang(ctx) == "en" {
		if len(attributes[0].AttributeInfo.SubjectReplaceTextEn) != 0 {
			subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceTextEn
		} else {
			if len(attributes[0].AttributeInfo.SubjectReplaceText) != 0 {
				subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceText
			}
		}
	}
	if subjectReplaceText != "" {
		title = append(title, locales.Tr(ctx, "report.attributes.random.subject.number.replace.substitute")+subjectReplaceText)
	} else {
		title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubjectNumberReplace))
	}
	title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubjectReplaceNumber))
	title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubjectReplaceStatus))
	title = append(title, locales.Tr(ctx, "report.random.list.name"))

	//处理自定义表头
	custHeaders := make([]string, 0)
	for _, list := range randomLists {
		if list.CustomHeaders != nil && len(list.CustomHeaders) > 0 {
			for _, header := range list.CustomHeaders {
				custHeaders = append(custHeaders, header)
			}
		}
	}
	custHeaders = slice.Union(custHeaders)
	for _, header := range custHeaders {
		title = append(title, header)
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"random_list_id": bson.M{"$in": randomListOID}}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "subject",
			"localField":   "subject_id",
			"foreignField": "_id",
			"as":           "subject",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "random_list",
			"localField":   "random_list_id",
			"foreignField": "_id",
			"as":           "random_list",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "history",
			"localField":   "subject_id",
			"foreignField": "oid",
			"as":           "history",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "subject",
			"localField":   "replace_subject_id",
			"foreignField": "_id",
			"as":           "replace_subject_lookup",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$subject", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$replace_subject_lookup", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$project_site", "preserveNullAndEmptyArrays": true}}},

		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
		{{Key: "$project", Value: bson.M{
			"_id":                    0,
			"cohort_id":              1,
			"block":                  1,
			"number":                 1,
			"group":                  1,
			"par_name":               1,
			"sub_name":               1,
			"factors":                1,
			"subject":                bson.M{"$first": "$subject.info.value"},
			"random_time":            "$subject.random_time",
			"status":                 1,
			"site_number":            "$project_site.number",
			"time_zone":              "$project_site.time_zone",
			"tz":                     "$project_site.tz",
			"site":                   models.ProjectSiteNameLookUpBson(ctx),
			"replace_subject":        1,
			"replace_subject_status": "$replace_subject_lookup.status",
			"replace_number":         bson.M{"$ifNull": bson.A{"$replace_number", ""}},
			"country":                bson.M{"$first": "$project_site.country"},
			"group_value":            1,
			"plan_number":            1,
			"random_list_id":         1,
			"history":                1,
			"custom":                 1,
			"register_group":         "$subject.register_group",
		}}},
	}
	t := true
	cursor, err = tools.Database.Collection("random_number").Aggregate(nil, pipeline, &options.AggregateOptions{AllowDiskUse: &t})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomNumber)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var content [][]interface{}

	for _, number := range randomNumber {
		itemTime := timeZone
		if number.Tz != "" {
			zone, err := tools.GetLocationFloat(number.Tz)
			if err != nil {
				return "", nil, err
			}
			itemTime = zone
		}
		hours := time.Duration(itemTime)
		minutes := time.Duration((itemTime - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		var tmp []interface{}
		tmp = append(tmp, project.Number)
		tmp = append(tmp, project.Name)
		tmp = append(tmp, number.SiteNumber)
		tmp = append(tmp, number.Site)
		if project.Type == 2 {
			tmp = append(tmp, cohortMap[number.CohortID])
		}
		tmp = append(tmp, number.Subject)
		tmp = append(tmp, statusKey[number.Status-1])
		// 组别代码
		code := ""
		randomListP, _ := slice.Find(randomLists, func(index int, item models.RandomList) bool {
			return number.RandomListID == item.ID
		})
		// 随机时间
		randomTime := ""
		if number.RandomTime != 0 {
			randomTime = time.Unix(int64(number.RandomTime), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			strTimeZone := tools.FormatOffsetToZoneStringUtc(itemTime)
			randomTime = randomTime + "(" + strTimeZone + ")"

		}
		randomList := *randomListP
		groupCodeP, _ := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
			return number.Group == item.Name
		})
		if groupCodeP != nil {
			groupCode := *groupCodeP
			code = groupCode.Code
		}
		if project.Type == 3 {
			for _, cohort := range envInfo.Cohorts {
				if len(selectCohort) > 0 && selectCohort[cohort.ID] == false {
					continue
				}
				randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
					return data.CohortID == cohort.ID
				})
				groupValue := getGroupValueGroups(randomListCohort)
				randomFactors := getRandomFactor(randomListCohort)
				// 存在区组随机表
				_, ok := slice.Find(randomListCohort, func(index int, data models.RandomList) bool {
					return data.Design.Type == 1
				})
				if number.CohortID != cohort.ID {
					length := 5
					if ok {
						length++
					}
					if len(randomFactors) > 0 {
						length = length + len(randomFactors)
					}
					if len(groupValue) > 0 {
						length = length + len(groupValue) + 1
					}
					for i := 0; i < length; i++ {
						tmp = append(tmp, "")
					}
				} else {
					// 随机时间
					tmp = append(tmp, randomTime)
					// 区组
					if ok {
						if number.Block != 0 {
							tmp = append(tmp, number.Block)
						} else {
							tmp = append(tmp, "")
						}
					}
					tmp = append(tmp, number.Number)
					tmp = append(tmp, code)
					if haveSubGroup {
						if number.SubName != "" {
							tmp = append(tmp, number.ParName)
							tmp = append(tmp, number.SubName)
						} else {
							tmp = append(tmp, number.Group)
							tmp = append(tmp, "")
						}
					} else {
						tmp = append(tmp, number.Group)
					}

					if showRegisterGroup {
						tmp = append(tmp, number.RegisterGroup)
					}

					// 计划随机数
					if len(groupValue) > 0 {
						if number.PlanNumber != nil {
							tmp = append(tmp, *number.PlanNumber)

						} else {
							tmp = append(tmp, "")

						}
						if len(number.GroupValue) > 0 {
							for _, v := range groupValue {
								vP, b := slice.Find(number.GroupValue, func(index int, item models.GroupValue) bool {
									return item.Group == v
								})
								if b {
									tmp = append(tmp, vP.Value)
								} else {
									tmp = append(tmp, "")
								}
							}
						} else {
							for range groupValue {
								tmp = append(tmp, "")
							}
						}
					}
					if len(randomFactors) > 0 { // 分层
						for _, factor := range randomFactors {
							factorP, ok := slice.Find(number.Factors, func(index int, item models.Factors) bool {
								return item.Name == factor.Name
							})
							if ok {
								factorc := *factorP
								tmp = append(tmp, factorc.Text)
							} else {
								tmp = append(tmp, "")
							}
						}
					}
				}
			}
		} else {
			tmp = append(tmp, randomTime)
			if commonHasBlock {
				if number.Block != 0 {
					tmp = append(tmp, number.Block)
				} else {
					tmp = append(tmp, "")
				}
			}
			tmp = append(tmp, number.Number)
			if haveSubGroup {
				if number.SubName != "" {
					tmp = append(tmp, number.ParName)
					tmp = append(tmp, number.SubName)
				} else {
					tmp = append(tmp, number.Group)
					tmp = append(tmp, "")
				}
			} else {
				tmp = append(tmp, number.Group)
			}

			////组别代码
			tmp = append(tmp, code)
			if showRegisterGroup {
				tmp = append(tmp, number.RegisterGroup)
			}

			// 计划随机数 GA GB
			if len(groupValue) > 0 {
				if *number.PlanNumber != 0 {
					tmp = append(tmp, *number.PlanNumber)
				} else {
					tmp = append(tmp, "")
				}
				if len(number.GroupValue) > 0 {
					for _, v := range groupValue {
						vP, b := slice.Find(number.GroupValue, func(index int, item models.GroupValue) bool {
							return item.Group == v
						})
						if b {
							tmp = append(tmp, vP.Value)
						} else {
							tmp = append(tmp, "")
						}
					}
				} else {
					for range groupValue {
						tmp = append(tmp, "")
					}
				}
			}
			if len(randomFactors) > 0 { // 分层
				for _, factor := range randomFactors {
					factorP, ok := slice.Find(number.Factors, func(index int, item models.Factors) bool {
						return item.Name == factor.Name
					})
					if ok {
						factorc := *factorP
						tmp = append(tmp, factorc.Text)
					} else {
						tmp = append(tmp, "")
					}
				}
			}

		}
		{
			replaceTime := ""
			//替换时间
			for _, history := range number.History {
				if strings.Contains(history.Key, "replace") && history.OID != primitive.NilObjectID {
					replaceTime = time.Unix(int64(history.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					strTimeZone := tools.FormatOffsetToZoneStringUtc(itemTime)
					randomTime = replaceTime + "(" + strTimeZone + ")"
					break
				}
			}
			tmp = append(tmp, replaceTime) //TODO替换时间
		}

		tmp = append(tmp, number.ReplaceSubject)
		tmp = append(tmp, number.ReplaceNumber)
		replaceStatus := ""
		if number.ReplaceSubjectStatus != nil {
			replaceStatus = statusItem(ctx, int32(*number.ReplaceSubjectStatus))
		}
		tmp = append(tmp, replaceStatus)   //替换受试者状态
		tmp = append(tmp, randomList.Name) //随机表名称
		for _, header := range custHeaders {
			v := ""
			if number.Custom != nil && len(number.Custom) > 0 {
				value, ok := number.Custom[header]
				if ok {
					v = value
				}
			}
			tmp = append(tmp, v)
		}
		content = append(content, tmp)
	}

	fileName := fmt.Sprintf("%s[%s]SourceRandomizationList_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))
	f := excelize.NewFile()
	sheetName := locales.Tr(ctx, "report.random.list")
	f.NewSheet(sheetName)
	tools.ExportSheet(f, sheetName, title, content)
	f.DeleteSheet("sheet1")
	siteIds := make([]primitive.ObjectID, 0)
	for _, list := range randomLists {
		if list.SiteIds != nil && len(list.SiteIds) > 0 {
			siteIds = append(siteIds, list.SiteIds...)
		}
	}
	siteIds = slice.Unique(siteIds)
	sites := make([]models.ProjectSite, 0)
	if len(siteIds) > 0 {
		cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"_id": bson.M{"$in": siteIds}})
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &sites)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
	}

	//随机表配置
	for _, list := range randomLists {
		sheet := f.NewSheet(list.Name)
		f.SetActiveSheet(sheet)
		//标题样式
		titleStyle, _ := f.NewStyle(&excelize.Style{
			Border: []excelize.Border{
				{
					Type:  "left",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "top",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "bottom",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "right",
					Color: "000000",
					Style: 1,
				}},
			Fill: excelize.Fill{
				Type:    "pattern",
				Color:   []string{"CCCCCC"},
				Pattern: 1, // Solid pattern
			},
			Font: &excelize.Font{
				Bold: true,
				Size: 16,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
			},
		})

		//key样式
		keyStyle, _ := f.NewStyle(&excelize.Style{
			Border: []excelize.Border{
				{
					Type:  "left",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "top",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "bottom",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "right",
					Color: "000000",
					Style: 1,
				}},
			Fill: excelize.Fill{
				Type:    "pattern",
				Color:   []string{"CCCCCC"},
				Pattern: 1, // Solid pattern
			},
			Font: &excelize.Font{
				Bold: true,
				Size: 11,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
			},
		})

		//value样式
		valueStyle, _ := f.NewStyle(&excelize.Style{
			Border: []excelize.Border{
				{
					Type:  "left",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "top",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "bottom",
					Color: "000000",
					Style: 1,
				},
				{
					Type:  "right",
					Color: "000000",
					Style: 1,
				}},
			Font: &excelize.Font{
				Size: 11,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
				WrapText:   true,
			},
		})

		_ = f.SetColWidth(list.Name, "A", "A", 35)
		_ = f.SetColWidth(list.Name, "B", "B", 15)
		_ = f.SetColWidth(list.Name, "C", "C", 12)
		_ = f.SetColWidth(list.Name, "D", "D", 22)
		_ = f.SetColWidth(list.Name, "E", "E", 20)
		row := 1
		titleFirst, _ := excelize.CoordinatesToCellName(1, row)
		titleLast, _ := excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, titleFirst, locales.Tr(ctx, "report.random.list.attribute"))
		_ = f.MergeCell(list.Name, titleFirst, titleLast)
		_ = f.SetCellStyle(list.Name, titleFirst, titleLast, titleStyle)
		_ = f.SetRowHeight(list.Name, 1, 38)
		row++

		tf, _ := excelize.CoordinatesToCellName(1, row)
		vf, _ := excelize.CoordinatesToCellName(2, row)
		vl, _ := excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "subjectReport.sponsor"))
		_ = f.SetCellStr(list.Name, vf, project.Sponsor)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.attributes.project.number"))
		_ = f.SetCellStr(list.Name, vf, project.Number)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "subjectReport.projectName"))
		_ = f.SetCellStr(list.Name, vf, project.Name)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.project.env"))
		_ = f.SetCellStr(list.Name, vf, envInfoP.Name)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		randomType := locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType.region")
		if list.Design.Type == 2 {
			randomType = locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType.min")
		}
		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType"))
		_ = f.SetCellStr(list.Name, vf, randomType)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.name"))
		_ = f.SetCellStr(list.Name, vf, list.Name)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		allSite := locales.Tr(ctx, "report.random.list.all.site")
		if list.SiteIds != nil && len(list.SiteIds) > 0 {
			var siteNameBuffer bytes.Buffer
			for _, id := range list.SiteIds {
				site, ok := slice.Find(sites, func(index int, item models.ProjectSite) bool {
					return item.ID == id
				})
				if ok {
					name := models.GetProjectSiteName(ctx, *site)
					siteNameBuffer.WriteString(name)
					siteNameBuffer.WriteString("\n")
				}
			}
			siteName := siteNameBuffer.String()
			allSite = strings.TrimSuffix(siteName, "\n")
		}
		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.applicable.site"))
		_ = f.SetCellStr(list.Name, vf, allSite)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		status := locales.Tr(ctx, "report.random.list.status.open")
		if list.Status == 2 {
			status = locales.Tr(ctx, "report.random.list.status.close")
		} else if list.Status == 3 {
			status = locales.Tr(ctx, "report.random.list.status.invalidate")
		}
		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.status"))
		_ = f.SetCellStr(list.Name, vf, status)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		createTime := tools.FormatFloatTime(project.TimeZoneStr, project.Tz, "", int64(list.Meta.CreatedAt), "2006-01-02 15:04:05")
		//createTime := time.Unix(int64(list.Meta.CreatedAt), 0).UTC().Add(time.Hour * time.Duration(project.TimeZone)).Format("2006-01-02 15:04:05")
		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.create.time"))
		_ = f.SetCellStr(list.Name, vf, createTime)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		var user models.User
		err := tools.Database.Collection("user").FindOne(nil, bson.M{"_id": list.Meta.CreatedBy}).Decode(&user)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.create.by"))
		_ = f.SetCellStr(list.Name, vf, user.Name)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++
		needNull := false
		if list.Config.NumberLength == 0 {
			needNull = true
		}
		if list.Design.Type == 1 && !needNull {
			//区组规则
			tf, _ = excelize.CoordinatesToCellName(1, row)
			vf, _ = excelize.CoordinatesToCellName(2, row)
			vl, _ = excelize.CoordinatesToCellName(5, row)
			blockRule := locales.Tr(ctx, "report.random.list.block.rule.order")
			if list.Config.BlocksRule == 1 {
				blockRule = locales.Tr(ctx, "report.random.list.block.rule.reverse")
			}
			_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.block.rule"))
			_ = f.SetCellStr(list.Name, vf, blockRule)
			_ = f.MergeCell(list.Name, vf, vl)
			_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
			_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++
		}

		if !needNull {
			numberRule := locales.Tr(ctx, "report.random.list.block.rule.order")
			if list.Config.Rule == 1 {
				numberRule = locales.Tr(ctx, "report.random.list.block.rule.reverse")
			}
			tf, _ = excelize.CoordinatesToCellName(1, row)
			vf, _ = excelize.CoordinatesToCellName(2, row)
			vl, _ = excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.random.number.rule"))
			_ = f.SetCellStr(list.Name, vf, numberRule)
			_ = f.MergeCell(list.Name, vf, vl)
			_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
			_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++
		}

		tf, _ = excelize.CoordinatesToCellName(1, row)
		vf, _ = excelize.CoordinatesToCellName(2, row)
		vl, _ = excelize.CoordinatesToCellName(5, row)
		count := slice.Count(randomNumber, func(index int, item models.ExportNumber) bool {
			return item.RandomListID == list.ID
		})
		_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.random.number.count"))
		_ = f.SetCellValue(list.Name, vf, count)
		_ = f.MergeCell(list.Name, vf, vl)
		_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
		_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++

		if !needNull {
			tf, _ = excelize.CoordinatesToCellName(1, row)
			vf, _ = excelize.CoordinatesToCellName(2, row)
			vl, _ = excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.initial.number"))
			if needNull {
				_ = f.SetCellValue(list.Name, vf, "")
			} else {
				_ = f.SetCellValue(list.Name, vf, list.Config.InitialValue)
			}
			_ = f.MergeCell(list.Name, vf, vl)
			_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
			_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++

			if list.Config.Rule == 1 {
				tf, _ = excelize.CoordinatesToCellName(1, row)
				vf, _ = excelize.CoordinatesToCellName(2, row)
				vl, _ = excelize.CoordinatesToCellName(5, row)
				_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.end.number"))
				if needNull {
					_ = f.SetCellValue(list.Name, vf, "")
				} else {
					_ = f.SetCellValue(list.Name, vf, list.Config.EndValue)
				}
				_ = f.MergeCell(list.Name, vf, vl)
				_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
				_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
				_ = f.SetRowHeight(list.Name, row, 20)
				row++
			}

			//号码长度
			tf, _ = excelize.CoordinatesToCellName(1, row)
			vf, _ = excelize.CoordinatesToCellName(2, row)
			vl, _ = excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.number.length"))
			if needNull {
				_ = f.SetCellValue(list.Name, vf, "")
			} else {
				_ = f.SetCellValue(list.Name, vf, list.Config.NumberLength)
			}
			_ = f.MergeCell(list.Name, vf, vl)
			_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
			_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++

			//号码前缀
			tf, _ = excelize.CoordinatesToCellName(1, row)
			vf, _ = excelize.CoordinatesToCellName(2, row)
			vl, _ = excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.prefix"))
			_ = f.SetCellValue(list.Name, vf, list.Config.Prefix)
			_ = f.MergeCell(list.Name, vf, vl)
			_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
			_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++

			//随机种子
			tf, _ = excelize.CoordinatesToCellName(1, row)
			vf, _ = excelize.CoordinatesToCellName(2, row)
			vl, _ = excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.seed"))
			if needNull {
				_ = f.SetCellValue(list.Name, vf, "")
			} else {
				_ = f.SetCellValue(list.Name, vf, list.Config.Seed)
			}
			_ = f.MergeCell(list.Name, vf, vl)
			_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
			_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++
		}

		if list.Design.Type == 2 {
			tf, _ = excelize.CoordinatesToCellName(1, row)
			vf, _ = excelize.CoordinatesToCellName(2, row)
			vl, _ = excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, tf, locales.Tr(ctx, "report.random.list.probability"))
			_ = f.SetCellValue(list.Name, vf, list.Config.Probability)
			_ = f.MergeCell(list.Name, vf, vl)
			_ = f.SetCellStyle(list.Name, tf, tf, keyStyle)
			_ = f.SetCellStyle(list.Name, vf, vl, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++
		}
		if list.Design.Type == 1 && !needNull {
			blockConfig, _ := excelize.CoordinatesToCellName(1, row)
			blockLen := len(list.Config.Blocks)
			blockConfigLast, _ := excelize.CoordinatesToCellName(1, row+blockLen)
			//区组配置
			_ = f.SetCellStr(list.Name, blockConfig, locales.Tr(ctx, "report.random.list.block.configuration"))
			_ = f.MergeCell(list.Name, blockConfig, blockConfigLast)
			_ = f.SetCellStyle(list.Name, blockConfig, blockConfigLast, keyStyle)
			//区组长度
			blockLength, _ := excelize.CoordinatesToCellName(2, row)
			blockLengthLast, _ := excelize.CoordinatesToCellName(3, row)
			_ = f.SetCellStr(list.Name, blockLength, locales.Tr(ctx, "report.random.list.block.length"))
			_ = f.MergeCell(list.Name, blockLength, blockLengthLast)
			_ = f.SetCellStyle(list.Name, blockLength, blockLengthLast, keyStyle)
			//区组数
			blockNumber, _ := excelize.CoordinatesToCellName(4, row)
			blockNumberLast, _ := excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, blockNumber, locales.Tr(ctx, "report.random.list.block.number"))
			_ = f.MergeCell(list.Name, blockNumber, blockNumberLast)
			_ = f.SetCellStyle(list.Name, blockNumber, blockNumberLast, keyStyle)
			_ = f.SetRowHeight(list.Name, row, 18)
			for _, block := range list.Config.Blocks {
				row++
				bL, _ := excelize.CoordinatesToCellName(2, row)
				bLLast, _ := excelize.CoordinatesToCellName(3, row)
				_ = f.SetCellInt(list.Name, bL, block.BlockLength)
				_ = f.MergeCell(list.Name, bL, bLLast)
				_ = f.SetCellStyle(list.Name, bL, bLLast, valueStyle)
				bN, _ := excelize.CoordinatesToCellName(4, row)
				bNLast, _ := excelize.CoordinatesToCellName(5, row)
				_ = f.SetCellInt(list.Name, bN, block.BlockNumber)
				_ = f.MergeCell(list.Name, bN, bNLast)
				_ = f.SetCellStyle(list.Name, bN, bNLast, valueStyle)
				_ = f.SetRowHeight(list.Name, row, 15)
			}
			row++
		}

		if list.Design.Type == 1 && needNull {
			//区组配置大小值
			blockConfig, _ := excelize.CoordinatesToCellName(1, row)
			blockConfigValue, _ := excelize.CoordinatesToCellName(2, row)
			blockConfigLast, _ := excelize.CoordinatesToCellName(5, row)
			value := strings.Join(list.Config.BlockSizes, ",")
			//区组配置
			_ = f.SetCellStr(list.Name, blockConfig, locales.Tr(ctx, "report.random.list.block.configuration"))
			//区组大小值
			_ = f.SetCellStr(list.Name, blockConfigValue, value)
			_ = f.MergeCell(list.Name, blockConfigValue, blockConfigLast)
			_ = f.SetCellStyle(list.Name, blockConfig, blockConfig, keyStyle)
			_ = f.SetCellStyle(list.Name, blockConfigValue, blockConfigLast, valueStyle)
			_ = f.SetRowHeight(list.Name, row, 20)
			row++
		}
		if needNull {
			//组别配置
			groupConfig, _ := excelize.CoordinatesToCellName(1, row)
			groupLen := len(list.Design.Groups)
			groupConfigLast, _ := excelize.CoordinatesToCellName(1, row+groupLen)
			_ = f.SetCellStr(list.Name, groupConfig, locales.Tr(ctx, "report.random.list.group.config"))
			_ = f.MergeCell(list.Name, groupConfig, groupConfigLast)
			_ = f.SetCellStyle(list.Name, groupConfig, groupConfigLast, keyStyle)
			//组别代码
			groupCode, _ := excelize.CoordinatesToCellName(2, row)
			_ = f.SetCellStr(list.Name, groupCode, locales.Tr(ctx, "report.attributes.random.config.code"))
			_ = f.SetCellStyle(list.Name, groupCode, groupCode, keyStyle)
			//组别名称
			groupName, _ := excelize.CoordinatesToCellName(3, row)
			groupNameLast, _ := excelize.CoordinatesToCellName(4, row)
			_ = f.SetCellStr(list.Name, groupName, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.group"))
			_ = f.SetCellStyle(list.Name, groupName, groupNameLast, keyStyle)
			_ = f.MergeCell(list.Name, groupName, groupNameLast)
			//子组别
			subGroup, _ := excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, subGroup, locales.Tr(ctx, "report.attributes.random.sub.group"))
			_ = f.SetCellStyle(list.Name, subGroup, subGroup, keyStyle)
			for _, group := range list.Design.Groups {
				row++
				gc, _ := excelize.CoordinatesToCellName(2, row)
				_ = f.SetCellStr(list.Name, gc, group.Code)
				_ = f.SetCellStyle(list.Name, gc, gc, valueStyle)
				gn, _ := excelize.CoordinatesToCellName(3, row)
				gnLast, _ := excelize.CoordinatesToCellName(4, row)
				parName := group.Name
				if group.SubName != "" {
					parName = group.ParName
				}
				_ = f.SetCellStr(list.Name, gn, parName)
				_ = f.SetCellStyle(list.Name, gn, gnLast, valueStyle)
				_ = f.MergeCell(list.Name, gn, gnLast)
				gs, _ := excelize.CoordinatesToCellName(5, row)
				_ = f.SetCellStr(list.Name, gs, group.SubName)
				_ = f.SetCellStyle(list.Name, gs, gs, valueStyle)
			}
			row++
		} else {
			//组别配置
			groupConfig, _ := excelize.CoordinatesToCellName(1, row)
			groupLen := len(list.Design.Groups)
			groupConfigLast, _ := excelize.CoordinatesToCellName(1, row+groupLen)
			_ = f.SetCellStr(list.Name, groupConfig, locales.Tr(ctx, "report.random.list.group.config"))
			_ = f.MergeCell(list.Name, groupConfig, groupConfigLast)
			_ = f.SetCellStyle(list.Name, groupConfig, groupConfigLast, keyStyle)
			//组别代码
			groupCode, _ := excelize.CoordinatesToCellName(2, row)
			_ = f.SetCellStr(list.Name, groupCode, locales.Tr(ctx, "report.attributes.random.config.code"))
			_ = f.SetCellStyle(list.Name, groupCode, groupCode, keyStyle)
			//组别名称
			groupName, _ := excelize.CoordinatesToCellName(3, row)
			_ = f.SetCellStr(list.Name, groupName, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.group"))
			_ = f.SetCellStyle(list.Name, groupName, groupName, keyStyle)
			//子组别
			subGroup, _ := excelize.CoordinatesToCellName(4, row)
			_ = f.SetCellStr(list.Name, subGroup, locales.Tr(ctx, "report.attributes.random.sub.group"))
			_ = f.SetCellStyle(list.Name, subGroup, subGroup, keyStyle)
			//组别比例
			groupRatio, _ := excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, groupRatio, locales.Tr(ctx, "report.random.list.group.ratio"))
			_ = f.SetCellStyle(list.Name, groupRatio, groupRatio, keyStyle)
			_ = f.SetRowHeight(list.Name, row, 18)
			for _, group := range list.Design.Groups {
				row++
				gc, _ := excelize.CoordinatesToCellName(2, row)
				_ = f.SetCellStr(list.Name, gc, group.Code)
				_ = f.SetCellStyle(list.Name, gc, gc, valueStyle)
				gn, _ := excelize.CoordinatesToCellName(3, row)
				parName := group.Name
				if group.SubName != "" {
					parName = group.ParName
				}
				_ = f.SetCellStr(list.Name, gn, parName)
				_ = f.SetCellStyle(list.Name, gn, gn, valueStyle)
				gs, _ := excelize.CoordinatesToCellName(4, row)
				_ = f.SetCellStr(list.Name, gs, group.SubName)
				_ = f.SetCellStyle(list.Name, gs, gs, valueStyle)
				gr, _ := excelize.CoordinatesToCellName(5, row)
				_ = f.SetCellInt(list.Name, gr, group.Ratio)
				_ = f.SetCellStyle(list.Name, gr, gr, valueStyle)
				_ = f.SetRowHeight(list.Name, row, 15)
			}
			row++
		}

		if list.Design.Factors == nil || len(list.Design.Factors) == 0 {
			factors := make([]models.RandomFactor, 0)
			ops := make([]models.Option, 0)
			ops = append(ops, models.Option{Label: "", Value: ""})
			factor := models.RandomFactor{
				Options: ops,
			}
			factors = append(factors, factor)
			list.Design.Factors = factors
		}
		//分层因素
		factorConfig, _ := excelize.CoordinatesToCellName(1, row)
		factorCount := 0
		for _, factor := range list.Design.Factors {
			factorCount = factorCount + len(factor.Options)
		}
		factorConfigLast, _ := excelize.CoordinatesToCellName(1, row+factorCount)
		_ = f.SetCellStr(list.Name, factorConfig, locales.Tr(ctx, "report.attributes.random.factor"))
		_ = f.MergeCell(list.Name, factorConfig, factorConfigLast)
		_ = f.SetCellStyle(list.Name, factorConfig, factorConfigLast, keyStyle)
		//分层因素编号
		factorCode, _ := excelize.CoordinatesToCellName(2, row)
		_ = f.SetCellStr(list.Name, factorCode, locales.Tr(ctx, "report.random.list.factor.code"))
		_ = f.SetCellStyle(list.Name, factorCode, factorCode, keyStyle)
		//分层因素名称
		factorName, _ := excelize.CoordinatesToCellName(3, row)
		_ = f.SetCellStr(list.Name, factorName, locales.Tr(ctx, "report.random.list.factor.name"))
		_ = f.SetCellStyle(list.Name, factorName, factorName, keyStyle)
		//分层因素权重比
		if list.Design.Type == 2 {
			factorRatio, _ := excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, factorRatio, locales.Tr(ctx, "report.random.list.factor.ratio"))
			_ = f.SetCellStyle(list.Name, factorRatio, factorRatio, keyStyle)
		}
		//选项值
		if list.Design.Type == 1 {
			option, _ := excelize.CoordinatesToCellName(4, row)
			optionLast, _ := excelize.CoordinatesToCellName(5, row)
			_ = f.SetCellStr(list.Name, option, locales.Tr(ctx, "report.random.list.factor.option"))
			_ = f.MergeCell(list.Name, option, optionLast)
			_ = f.SetCellStyle(list.Name, option, optionLast, keyStyle)
			_ = f.SetRowHeight(list.Name, row, 18)
			for _, factor := range list.Design.Factors {
				row++
				//分层因素编号值
				fc, _ := excelize.CoordinatesToCellName(2, row)
				fcLast, _ := excelize.CoordinatesToCellName(2, row+len(factor.Options)-1)
				_ = f.SetCellStr(list.Name, fc, factor.Number)
				_ = f.MergeCell(list.Name, fc, fcLast)
				_ = f.SetCellStyle(list.Name, fc, fcLast, valueStyle)
				//分层因素名称值
				fn, _ := excelize.CoordinatesToCellName(3, row)
				fnLast, _ := excelize.CoordinatesToCellName(3, row+len(factor.Options)-1)
				_ = f.SetCellStr(list.Name, fn, factor.Label)
				_ = f.MergeCell(list.Name, fn, fnLast)
				_ = f.SetCellStyle(list.Name, fn, fnLast, valueStyle)
				for i, o := range factor.Options {
					fo, _ := excelize.CoordinatesToCellName(4, row)
					foLast, _ := excelize.CoordinatesToCellName(5, row)
					_ = f.SetCellStr(list.Name, fo, o.Label)
					_ = f.MergeCell(list.Name, fo, foLast)
					_ = f.SetCellStyle(list.Name, fo, foLast, valueStyle)
					if i != len(factor.Options)-1 {
						row++
					}
					_ = f.SetRowHeight(list.Name, row, 15)
				}
			}
		} else if list.Design.Type == 2 {
			option, _ := excelize.CoordinatesToCellName(4, row)
			_ = f.SetCellStr(list.Name, option, locales.Tr(ctx, "report.random.list.factor.option"))
			_ = f.SetCellStyle(list.Name, option, option, keyStyle)
			_ = f.SetRowHeight(list.Name, row, 18)
			for _, factor := range list.Design.Factors {
				row++
				//分层因素编号值
				fc, _ := excelize.CoordinatesToCellName(2, row)
				fcLast, _ := excelize.CoordinatesToCellName(2, row+len(factor.Options)-1)
				_ = f.SetCellStr(list.Name, fc, factor.Number)
				_ = f.MergeCell(list.Name, fc, fcLast)
				_ = f.SetCellStyle(list.Name, fc, fcLast, valueStyle)
				//分层因素名称值
				fn, _ := excelize.CoordinatesToCellName(3, row)
				fnLast, _ := excelize.CoordinatesToCellName(3, row+len(factor.Options)-1)
				_ = f.SetCellStr(list.Name, fn, factor.Label)
				_ = f.MergeCell(list.Name, fn, fnLast)
				_ = f.SetCellStyle(list.Name, fn, fnLast, valueStyle)
				//分层因素权重比
				fr, _ := excelize.CoordinatesToCellName(5, row)
				frLast, _ := excelize.CoordinatesToCellName(5, row+len(factor.Options)-1)
				_ = f.SetCellInt(list.Name, fr, factor.Ratio)
				_ = f.MergeCell(list.Name, fr, frLast)
				_ = f.SetCellStyle(list.Name, fr, frLast, valueStyle)
				//选项值
				for i, o := range factor.Options {
					fo, _ := excelize.CoordinatesToCellName(4, row)
					_ = f.SetCellStr(list.Name, fo, o.Label)
					_ = f.SetCellStyle(list.Name, fo, fo, valueStyle)
					if i != len(factor.Options)-1 {
						row++
					}
					_ = f.SetRowHeight(list.Name, row, 15)
				}
			}
		}
		row++

		attributeP, _ := slice.Find(attributes, func(index int, item models.Attribute) bool {
			return item.EnvironmentID == list.EnvironmentID && item.CohortID == list.CohortID
		})
		//国家是否作为分层因素
		countryFactor, _ := excelize.CoordinatesToCellName(1, row)
		_ = f.SetCellStr(list.Name, countryFactor, locales.Tr(ctx, "report.random.list.factor.country"))
		countryValue, _ := excelize.CoordinatesToCellName(2, row)
		countryValueLast, _ := excelize.CoordinatesToCellName(5, row)
		country := locales.Tr(ctx, "report.random.list.factor.no")
		if attributeP != nil && attributeP.AttributeInfo.CountryLayered {
			country = locales.Tr(ctx, "report.random.list.factor.yes")
		}
		_ = f.SetCellStr(list.Name, countryValue, country)
		_ = f.MergeCell(list.Name, countryValue, countryValueLast)
		_ = f.SetCellStyle(list.Name, countryFactor, countryFactor, keyStyle)
		_ = f.SetCellStyle(list.Name, countryValue, countryValueLast, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++
		//中心是否作为分层因素
		siteFactor, _ := excelize.CoordinatesToCellName(1, row)
		_ = f.SetCellStr(list.Name, siteFactor, locales.Tr(ctx, "report.random.list.factor.site"))
		siteValue, _ := excelize.CoordinatesToCellName(2, row)
		siteValueLast, _ := excelize.CoordinatesToCellName(5, row)
		site := locales.Tr(ctx, "report.random.list.factor.no")
		if attributeP != nil && attributeP.AttributeInfo.InstituteLayered {
			site = locales.Tr(ctx, "report.random.list.factor.yes")
		}
		_ = f.SetCellStr(list.Name, siteValue, site)
		_ = f.MergeCell(list.Name, siteValue, siteValueLast)
		_ = f.SetCellStyle(list.Name, siteFactor, siteFactor, keyStyle)
		_ = f.SetCellStyle(list.Name, siteValue, siteValueLast, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++
		//区域是否作为分层因素
		regionFactor, _ := excelize.CoordinatesToCellName(1, row)
		_ = f.SetCellStr(list.Name, regionFactor, locales.Tr(ctx, "report.random.list.factor.region"))
		regionValue, _ := excelize.CoordinatesToCellName(2, row)
		regionValueLast, _ := excelize.CoordinatesToCellName(5, row)
		region := locales.Tr(ctx, "report.random.list.factor.no")
		if attributeP != nil && attributeP.AttributeInfo.RegionLayered {
			region = locales.Tr(ctx, "report.random.list.factor.yes")
		}
		_ = f.SetCellStr(list.Name, regionValue, region)
		_ = f.MergeCell(list.Name, regionValue, regionValueLast)
		_ = f.SetCellStyle(list.Name, regionFactor, regionFactor, keyStyle)
		_ = f.SetCellStyle(list.Name, regionValue, regionValueLast, valueStyle)
		_ = f.SetRowHeight(list.Name, row, 20)
		row++
	}
	f.SetActiveSheet(0)
	buffer, err := f.WriteToBuffer()
	return fileName, buffer.Bytes(), nil

}

func getGroupValueGroups(randomList []models.RandomList) []string {
	//最小化随机组别set
	groupValueRandomList := make([]models.RandomList, 0)
	groupValueRandomList = slice.Filter(randomList, func(index int, item models.RandomList) bool {
		return item.Design.Type == 2
	})
	groupSet := datastructure.NewSet[string]()
	for _, group := range groupValueRandomList {
		for _, g := range group.Design.Groups {
			groupSet.Add(g.Name)
		}
	}
	values := groupSet.Values()
	return values
}

func getFlatFactor(randomList []models.RandomList) []string {
	labels := make([]string, 0)
	for _, list := range randomList {
		if list.Design.Factors != nil && len(list.Design.Factors) > 0 {
			for _, factor := range list.Design.Factors {
				labels = append(labels, factor.Label)
			}
		}
	}
	return slice.Unique(labels)
}
func getEffectiveFlatFactor(randomList []models.RandomList) []string {
	labels := make([]string, 0)
	for _, list := range randomList {
		if list.Design.Factors != nil && len(list.Design.Factors) > 0 {
			for _, factor := range list.Design.Factors {
				if factor.Status == nil || *factor.Status == 1 {
					labels = append(labels, factor.Label)
				}
			}
		}
	}
	return slice.Unique(labels)
}

// 平展分层因素
func getRandomFactor(randomList []models.RandomList) []randomFactor {
	factorsRandomList := make([]models.RandomList, 0)
	factorsRandomList = slice.Filter(randomList, func(index int, item models.RandomList) bool {
		return item.Design.Factors != nil && len(item.Design.Factors) > 0
	})
	randomFactorArrays := slice.Map(factorsRandomList, func(index int, item models.RandomList) []randomFactor {
		rfs := make([]randomFactor, len(item.Design.Factors))
		rfs = slice.Map(item.Design.Factors, func(index int, f models.RandomFactor) randomFactor {
			return randomFactor{
				Name:     f.Name,
				Label:    f.Label,
				Options:  f.Options,
				Type:     f.Type,
				IsCalc:   f.IsCalc,
				CalcType: f.CalcType,
			}
		})
		return rfs
	})
	randomFactors := make([]randomFactor, 0)
	randomFactors = slice.FlattenDeep(randomFactorArrays).([]randomFactor)
	randomFactors = slice.Unique(randomFactors)
	return randomFactors
}

func getEffectiveRandomFactor(randomList []models.RandomList) []randomFactor {
	factorsRandomList := make([]models.RandomList, 0)
	factorsRandomList = slice.Filter(randomList, func(index int, item models.RandomList) bool {
		return item.Design.Factors != nil && len(item.Design.Factors) > 0
	})
	randomFactorArrays := slice.Map(factorsRandomList, func(index int, item models.RandomList) []randomFactor {
		rfs := make([]randomFactor, 0)
		for _, f := range item.Design.Factors {
			if f.Status == nil || *f.Status == 1 {
				rfs = append(rfs, randomFactor{
					Name:     f.Name,
					Label:    f.Label,
					Options:  f.Options,
					Type:     f.Type,
					IsCalc:   f.IsCalc,
					CalcType: f.CalcType,
				})
			}
		}
		return rfs
	})
	randomFactors := make([]randomFactor, 0)
	randomFactors = slice.FlattenDeep(randomFactorArrays).([]randomFactor)
	randomFactors = slice.Unique(randomFactors)
	return randomFactors
}

func getFormulaForm(ctx *gin.Context, envOID primitive.ObjectID, cohortIDs []primitive.ObjectID) ([]interface{}, []string, error) {
	titles := []interface{}{}
	titlesKey := []string{}
	weightMatch := bson.M{
		"env_id":              envOID,
		"formula_info.weight": bson.M{"$ne": nil},
	}
	if len(cohortIDs) > 0 {
		weightMatch["cohort_id"] = bson.M{"$in": cohortIDs}
	}
	count, err := tools.Database.Collection("dispensing").CountDocuments(nil, weightMatch)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	if count > 0 {
		titles = append(titles, locales.Tr(ctx, "export.dispensing.weight"))
		titlesKey = append(titlesKey, "weight")
	}
	heightMatch := bson.M{
		"env_id":              envOID,
		"formula_info.height": bson.M{"$ne": nil},
	}
	if len(cohortIDs) > 0 {
		heightMatch["cohort_id"] = bson.M{"$in": cohortIDs}
	}
	count, err = tools.Database.Collection("dispensing").CountDocuments(nil, heightMatch)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	if count > 0 {
		titles = append(titles, locales.Tr(ctx, "export.dispensing.height"))
		titlesKey = append(titlesKey, "height")
	}
	ageMatch := bson.M{
		"env_id":           envOID,
		"formula_info.age": bson.M{"$ne": nil},
	}
	if len(cohortIDs) > 0 {
		ageMatch["cohort_id"] = bson.M{"$in": cohortIDs}
	}
	count, err = tools.Database.Collection("dispensing").CountDocuments(nil, ageMatch)
	if err != nil {
		return nil, nil, errors.WithStack(err)

	}
	if count > 0 {
		titles = append(titles, locales.Tr(ctx, "export.dispensing.age"))
		titlesKey = append(titlesKey, "age")
	}
	return titles, titlesKey, nil
}
