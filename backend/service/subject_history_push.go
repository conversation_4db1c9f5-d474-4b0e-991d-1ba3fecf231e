package service

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"context"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
)

/*
	推送统计中的历史数据推送
*/

var pushTypeToTrail = map[int][]string{
	1: { // 登记
		"history.subject.add",
		"history.subject.at-random-add",
	},
	2: { // 修改
		"history.subject.update",
		"history.subject.at-random-update",
		"history.subject.updateSubjectNo",
		"history.subject.label.updateSubjectNo",
		"history.subject.label.update",
		"history.subject.label.at-random-update",
		"history.subject.label.updateCustomize",
		"history.subject.label.updateSignOutTime",
		"history.subject.label.updateScreen",
		"history.subject.label.updateScreenSignOutTime",
		"history.subject.label.updateScreenFail",
		"history.subject.label.updateScreenFailSignOutTime",
	},
	3: { // 随机 | 替换
		"history.subject.random",
		"history.subject.randomSub",
		"history.subject.randomNoNumber",
		"history.subject.randomNoNumberSub",
		"history.subject.at-random-random",
		"history.subject.label.random",
		"history.subject.label.at-random-random",
		"history.subject.label.randomSub",
		"history.subject.label.at-random-randomSub",
		"history.subject.label.randomNoNumber",
		"history.subject.label.at-random-randomNoNumber",
		"history.subject.label.randomNoNumberSub",
		"history.subject.label.at-random-randomNoNumberSub",
		"history.subject.replaced-new",
		"history.subject.replaced",
		"history.subject.label.replaced-new",
		"history.subject.label.at-random-replaced-new-a",
		"history.subject.label.at-random-replaced-new-b",
	},
	12: { // 编辑实际分层
		"history.subject.label.update2Customize",
	},
	13: { // 筛选
		"history.subject.label.screen",
		"history.subject.label.at-random-screen",
		"history.subject.label.screenScreenFail",
	},
}

func (s *SubjectService) CheckPushHistory(ctx *gin.Context, req models.CheckPushHistoryReq) (models.CheckPushHistoryResp, error) {
	// 返回数据
	var checkPushHistoryResp models.CheckPushHistoryResp

	var project models.Project
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": req.ProjectID}).Decode(&project)
	if err != nil {
		return checkPushHistoryResp, errors.WithStack(err)
	}

	// 获取待推送的受试者
	subjects, err := getPushHistorySubjects(ctx, project, req.EnvID, req.RoleID, req.CohortID)
	if err != nil {
		return checkPushHistoryResp, err
	}

	// 检查实际包含的推送场景
	var actualPushScenario models.PushScenario

	//if project.PushScenario.RegisterPush {
	//	actualPushScenario.RegisterPush = true
	//}

	for _, subject := range subjects {
		historyData, err := getSubjectTrail(ctx, subject.Subject)
		if err != nil {
			return checkPushHistoryResp, err
		}
		trailKeys := slice.Map(historyData, func(index int, item models.History) string {
			return item.Key
		})
		trailKeys = slice.Unique(trailKeys)

		// 检查随机前后修改状态
		//hasBeforeUpdate, hasAfterUpdate, _ := checkRandomUpdateStatus(historyData)

		// 登记
		if project.PushScenario.RegisterPush && !actualPushScenario.RegisterPush {
			if slice.Contain(subject.ToBePushedTypes, 1) {
				actualPushScenario.RegisterPush = true
			}
		}

		// 随机前修改
		if project.PushScenario.UpdateRandomFrontPush && !actualPushScenario.UpdateRandomFrontPush {
			if subject.HasBeforeUpdate {
				actualPushScenario.UpdateRandomFrontPush = true
			}
		}
		// 随机后修改
		if project.PushScenario.UpdateRandomAfterPush && !actualPushScenario.UpdateRandomAfterPush {
			if subject.HasAfterUpdate {
				actualPushScenario.UpdateRandomAfterPush = true
			}
		}
		// 随机
		if project.PushScenario.RandomPush && !actualPushScenario.RandomPush {
			if slice.Contain(subject.ToBePushedTypes, 3) {
				actualPushScenario.RandomPush = true
			}
		}
		// 筛选
		if project.PushScenario.ScreenPush && !actualPushScenario.ScreenPush {
			if slice.Contain(subject.ToBePushedTypes, 13) {
				actualPushScenario.ScreenPush = true
			}
		}
		// 发药
		if project.PushScenario.DispensingPush && !actualPushScenario.DispensingPush {
			if subject.HasDispensingToBePush {
				actualPushScenario.DispensingPush = true
			}

		}

		// 只有 project.PushScenario 对应的值为 true，才需要检查是否满足条件
		if (!project.PushScenario.RegisterPush || actualPushScenario.RegisterPush) &&
			(!project.PushScenario.UpdateRandomFrontPush || actualPushScenario.UpdateRandomFrontPush) &&
			(!project.PushScenario.UpdateRandomAfterPush || actualPushScenario.UpdateRandomAfterPush) &&
			(!project.PushScenario.RandomPush || actualPushScenario.RandomPush) &&
			(!project.PushScenario.ScreenPush || actualPushScenario.ScreenPush) &&
			(!project.PushScenario.DispensingPush || actualPushScenario.DispensingPush) {
			break
		}
	}

	checkPushHistoryResp.SubjectCount = int64(len(subjects))
	checkPushHistoryResp.PushScenario = actualPushScenario

	return checkPushHistoryResp, nil
}

func (s *SubjectService) PushHistory(ctx *gin.Context, req models.CheckPushHistoryReq) error {
	var project models.Project
	err := tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": req.ProjectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	subjects, err := getPushHistorySubjects(ctx, project, req.EnvID, req.RoleID, req.CohortID)
	if err != nil {
		return err
	}

	//遍历受试者，构造推送数据
	for _, subject := range subjects {
		nowDuration := time.Duration(time.Now().Unix())

		if len(subject.ToBePushedTypes) > 0 {
			SubjectRandomHistoryPush(ctx, subject.ID, subject.ToBePushedTypes, nowDuration)
		}

		// 构造受试者的发药数据并推送
		if project.PushScenario.DispensingPush {
			for _, dispensing := range subject.DispensingData {
				// 检查该发药是否被推送过
				isPushed := false
				for _, edcPush := range subject.EdcPushData {
					if edcPush.OID == dispensing.ID {
						isPushed = true
						break
					}
				}
				if isPushed {
					// 推送过就不推了
					continue
				}

				// 每个发药都只有一个 sourceType
				// 获取sourceType
				sourceType := 4
				if dispensing.VisitSign {
					if dispensing.Reissue == 1 {
						sourceType = 7
					} else {
						sourceType = 5
					}
				}
				SubjectDispensingHistoryPush(ctx, dispensing.ID, sourceType, nowDuration)
			}
		}
	}

	return nil
}

func checkRandomUpdateStatus(historyData []models.History, lastUpdatePushTime int64) (hasRandomBeforeUpdate, hasRandomAfterUpdate bool, updateTypes map[int]bool) {
	var (
		hasAnyUpdate             bool // 是否存在任何修改操作
		hasUpdateBeforeAnyRandom bool // 是否存在随机前的修改
		hasUpdateAfterAnyRandom  bool // 是否存在随机后的修改
		hasAnyRandom             bool // 是否存在任何随机操作
	)
	updateTypes = make(map[int]bool) // 记录出现的修改类型

	// 遍历历史记录，检查操作顺序
	for _, item := range historyData {
		key := item.Key
		itemTime := int64(item.Time) // 获取历史记录的时间

		// 检查是否是修改操作 (sourceType=2 或 12)
		if isTrailContainsKey([]string{key}, 2) {
			// 只有时间大于 lastUpdatePushTime 的修改才被记录
			if itemTime > lastUpdatePushTime {
				hasAnyUpdate = true
				updateTypes[2] = true
				if !hasAnyRandom {
					// 当前随机操作尚未出现，所以这是随机前的修改
					hasUpdateBeforeAnyRandom = true
				} else {
					// 当前随机操作已出现，所以这是随机后的修改
					hasUpdateAfterAnyRandom = true
				}
			}
		} else if isTrailContainsKey([]string{key}, 12) {
			// 只有时间大于 lastUpdatePushTime 的修改才被记录
			if itemTime > lastUpdatePushTime {
				hasAnyUpdate = true
				updateTypes[12] = true
				if !hasAnyRandom {
					// 当前随机操作尚未出现，所以这是随机前的修改
					hasUpdateBeforeAnyRandom = true
				} else {
					// 当前随机操作已出现，所以这是随机后的修改
					hasUpdateAfterAnyRandom = true
				}
			}
		}

		// 检查是否是随机操作
		if isTrailContainsKey([]string{key}, 3) {
			hasAnyRandom = true
		}
	}

	// 随机前修改的判断：
	// 1. 必须有修改
	// 2. 且（没有随机 或 有随机前的修改）
	hasRandomBeforeUpdate = hasAnyUpdate && (!hasAnyRandom || hasUpdateBeforeAnyRandom)

	// 随机后修改的判断：
	// 1. 必须有随机
	// 2. 且必须有随机后的修改
	hasRandomAfterUpdate = hasAnyRandom && hasUpdateAfterAnyRandom

	return
}

// 不包含发药
func getSourceTypeList(ctx context.Context, subject models.HistoryPushSubject, project models.Project) ([]int, bool, bool, error) {
	// historyModifyPushed 推送纪录中出现过历史修改或历史实际分层编辑，且该纪录是在勾选修改推送场景之后
	pushScenario := project.PushScenario
	var sourceTypeList []int

	historyData, err := getSubjectTrail(ctx, subject.Subject)
	if err != nil {
		return nil, false, false, err
	}

	// 获取到推送纪录中最后一次的时间
	var lastUpdatePushTime int64
	for _, pd := range subject.EdcPushData {
		if pd.Source == 1 && (pd.SourceType == 2 || pd.SourceType == 12 || slice.Contain(pd.SourceTypeList, 2) || slice.Contain(pd.SourceTypeList, 12)) {
			if int64(pd.SendTime) > lastUpdatePushTime {
				lastUpdatePushTime = int64(pd.SendTime)
			}
		}
	}

	// 检查修改操作和对应的操作时间戳
	//hasBetweenUpdate := false
	//hasAfterCheckedUpdate := false
	//if pushScenario.UpdateRandomFrontPush || pushScenario.UpdateRandomAfterPush {
	//	for _, history := range historyData {
	//		if slice.Contain(pushTypeToTrail[2], history.Key) || slice.Contain(pushTypeToTrail[12], history.Key) {
	//			// 取消勾选和再次勾选之间有修改，但是勾选之后没有修改
	//			ht := int64(history.Time)
	//			if ht > uncheckUpdateTimestamp && ht < checkedUpdateTimestamp {
	//				hasBetweenUpdate = true
	//			}
	//			if ht > checkedUpdateTimestamp {
	//				hasAfterCheckedUpdate = true
	//			}
	//		}
	//
	//	}
	//}
	//
	//needPushUpdate := hasBetweenUpdate && !hasAfterCheckedUpdate

	keys := slice.Map(historyData, func(index int, item models.History) string {
		return item.Key
	})

	// sourceType 0.未知 1.登记 2.修改 3.随机 4.发药 5.访视外发药 6.药物替换 7.药物补发 8.药物撤销 9.药物取回 10.实际用药 11.受试者替换 12.编辑实际分层 13.筛选

	// 登记
	if pushScenario.RegisterPush {
		sourceTypeList = append(sourceTypeList, 1)
	}

	// 检查随机前后修改状态,只检查最后一次修改推送之后的修改历史纪录
	hasBeforeUpdate, hasAfterUpdate, updateTypes := checkRandomUpdateStatus(historyData, lastUpdatePushTime)

	// 随机前修改
	if pushScenario.UpdateRandomFrontPush && hasBeforeUpdate {
		for t := range updateTypes {
			sourceTypeList = append(sourceTypeList, t) // 添加实际出现的修改类型 2 | 12
		}
	}
	// 随机后修改
	if pushScenario.UpdateRandomAfterPush && hasAfterUpdate {
		for t := range updateTypes {
			sourceTypeList = append(sourceTypeList, t) // 添加实际出现的修改类型 2 | 12
		}
	}
	// 随机
	if pushScenario.RandomPush {
		if isTrailContainsKey(keys, 3) {
			sourceTypeList = append(sourceTypeList, 3)
		}
	}
	// 筛选
	if pushScenario.ScreenPush {
		if isTrailContainsKey(keys, 13) {
			sourceTypeList = append(sourceTypeList, 13)
		}
	}

	sourceTypeList = slice.Unique(sourceTypeList)

	return sourceTypeList, hasBeforeUpdate, hasAfterUpdate, nil
}

func getSubjectTrail(ctx context.Context, subject models.Subject) ([]models.History, error) {
	filter := bson.M{"key": bson.M{"$regex": "history.subject"}, "oid": subject.ID}
	opts := options.Find().SetSort(bson.D{{"time", 1}}) // 历史纪录从前往后排
	var historyData []models.History
	cursor, err := tools.Database.Collection("history").Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &historyData)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return historyData, nil
}

func isTrailContainsKey(keys []string, sourceType int) bool {
	flag := false
	for _, key := range keys {
		if slice.Contain(pushTypeToTrail[sourceType], key) {
			flag = true
			break
		}
	}

	return flag
}

// 获取待历史数据推送的受试者列表
func getPushHistorySubjects(ctx *gin.Context, project models.Project, envId primitive.ObjectID, roleId primitive.ObjectID, cohortId primitive.ObjectID) ([]models.HistoryPushSubject, error) {
	pushScenario := project.PushScenario

	dispensingPushFlag := pushScenario.DispensingPush
	onlyDispensingPush := !pushScenario.RegisterPush &&
		!pushScenario.UpdateRandomFrontPush &&
		!pushScenario.UpdateRandomAfterPush &&
		!pushScenario.RandomPush &&
		!pushScenario.ScreenPush &&
		pushScenario.DispensingPush

	// 根据推送场景筛选对应状态的受试者，（按照最大化原则） 用来粗筛
	statusList := buildStatusList(pushScenario)

	//// 根据推送场景，查找存在该场景的 ，// todo 修改和发药要做特殊处理
	//checkSourceTypeList := buildSourceTypeList(pushScenario)
	//
	//// 查询所有推送过的纪录
	//pushFilter := bson.M{
	//	"project_id":  project.ID,
	//	"env_id":      envId,
	//	"customer_id": project.CustomerID,
	//	"cohort_id":   cohortId,
	//}
	//
	//pipeline := []bson.M{
	//	{"$match": pushFilter},
	//
	//	{"$project": bson.M{
	//		"oid":         1,
	//		"source":      1,
	//		"source_type": 1,
	//		"source_type_list": bson.M{
	//			"$ifNull": []interface{}{"$source_type_list", bson.A{}}, // 如果为nil，转为空数组
	//		},
	//		"subject_no": "$content.subject_no",
	//	}},
	//
	//	// 初步筛选
	//	{"$match": bson.M{
	//		"$or": []bson.M{
	//			// 筛选source_type命中目标的情况
	//			{"source_type": bson.M{"$in": checkSourceTypeList}},
	//
	//			// 筛选source_type_list包含目标的情况（对数组字段的特殊处理）
	//			{"source_type_list": bson.M{"$elemMatch": bson.M{"$in": checkSourceTypeList}}},
	//		},
	//	}},
	//
	//	{"$group": bson.M{
	//		"_id":               "$oid",
	//		"source_types":      bson.M{"$addToSet": "$source_type"},  // int[]
	//		"source_type_lists": bson.M{"$push": "$source_type_list"}, // int[][]
	//		"source":            bson.M{"$first": "$source"},
	//		"subject_no":        bson.M{"$first": "$subject_no"},
	//	}},
	//	{"$project": bson.M{
	//		"oid":        "$_id",
	//		"_id":        0,
	//		"subject_no": 1,
	//		"source":     1,
	//		"merged_source_types": bson.M{
	//			"$setUnion": bson.A{
	//				"$source_types", // 自动转为[int]
	//				bson.M{
	//					"$reduce": bson.M{ // 展开所有数组
	//						"input":        "$source_type_lists",
	//						"initialValue": bson.A{},
	//						"in":           bson.M{"$concatArrays": bson.A{"$$value", "$$this"}},
	//					},
	//				},
	//			},
	//		},
	//	}},
	//
	//	{
	//		"$match": bson.M{
	//			"$expr": bson.M{
	//				"$setIsSubset": bson.A{
	//					checkSourceTypeList,    // 目标数组
	//					"$merged_source_types", // 被检查的字段
	//				},
	//			},
	//		},
	//	},
	//
	//	//{"$match": bson.M{"merged_source_types": "000001-482"}},
	//}
	//
	//var pushData []models.GroupPush
	//cursor, err := tools.Database.Collection("edc_push").Aggregate(ctx, pipeline)
	//if err != nil {
	//	return nil, errors.WithStack(err)
	//}
	//err = cursor.All(nil, &pushData)
	//if err != nil {
	//	return nil, errors.WithStack(err)
	//}
	//
	//pushedSubjectOidList := make([]primitive.ObjectID, 0)
	//dispensingOIDList := make([]primitive.ObjectID, 0)
	//for _, push := range pushData {
	//	// 如果是发药推送，则 oid 是发药的 id,需要再查出对应的受试者id
	//	// source 来源 1.随机 2.发药
	//	if push.Source == 2 {
	//		dispensingOIDList = append(dispensingOIDList, push.OID)
	//	} else {
	//		pushedSubjectOidList = append(pushedSubjectOidList, push.OID)
	//	}
	//}
	//
	//if len(dispensingOIDList) > 0 {
	//	dispensingOIDList = slice.Unique(dispensingOIDList)
	//	dispensingData, err := GetDispensingByIDs(ctx, dispensingOIDList)
	//	if err != nil {
	//		return nil, err
	//	}
	//	for _, dispensing := range dispensingData {
	//		pushedSubjectOidList = append(pushedSubjectOidList, dispensing.SubjectID)
	//	}
	//}
	//
	//pushedSubjectOidList = slice.Unique(pushedSubjectOidList)

	matchCondition := bson.M{
		"project_id": project.ID,
		"env_id":     envId,
		"cohort_id":  cohortId,
		"status":     bson.M{"$in": statusList}, // 过滤 status 在 statusList 中的文档
	}

	//if len(pushedSubjectOidList) > 0 {
	//	matchCondition["_id"] = bson.M{"$nin": pushedSubjectOidList}
	//}

	service := ProjectSiteService{}
	sites, err := service.UserSites(ctx, project.CustomerID.Hex(), project.ID.Hex(), envId.Hex(), roleId.Hex())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if sites != nil && len(sites) > 0 {
		siteIds := slice.Map(sites, func(index int, item map[string]interface{}) primitive.ObjectID {
			return item["id"].(primitive.ObjectID)
		})
		matchCondition["project_site_id"] = bson.M{"$in": siteIds}
	} else {
		return nil, nil
	}

	pipeline := []bson.M{
		{
			"$match": matchCondition, // 合并后的匹配条件
		},
		{
			"$lookup": bson.M{
				"from": "edc_push",
				"let": bson.M{
					"subject_id_str": bson.M{"$toString": "$_id"}, // 将 _id 转为字符串
					"subject_no": bson.M{
						"$arrayElemAt": []interface{}{"$info.value", 0},
					}, // 受试者号
				},
				"pipeline": []bson.M{
					{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": []bson.M{
									{"$eq": []interface{}{"$project_id", project.ID}},
									{"$eq": []interface{}{"$env_id", envId}},
									{"$eq": []interface{}{"$cohort_id", cohortId}},
									{"$or": []bson.M{
										{"$eq": []interface{}{"$content.irt_subject_id", "$$subject_id_str"}},
										{"$eq": []interface{}{"$content.subject_no", "$$subject_no"}},
									}},
								},
							},
						},
					},
					{
						"$sort": bson.M{"send_time": -1}, // 按照 send_time 降序排序
					},
				},
				"as": "edc_push_data",
			},
		},
	}

	if dispensingPushFlag {
		pipeline = append(pipeline,
			bson.M{
				"$lookup": bson.M{
					"from": "dispensing",
					"let": bson.M{
						"subject_id": "$_id", // 将 subject 集合的 _id 赋值给变量 subject_id
					},
					"pipeline": []bson.M{
						{
							"$match": bson.M{
								"$expr": bson.M{
									"$and": []bson.M{
										{"$eq": []interface{}{"$subject_id", "$$subject_id"}}, // 关联条件
										{"$eq": []interface{}{"$status", 2}},                  // 状态为已发药
									},
								},
							},
						},
					},
					"as": "dispensing_data", // 关联结果的字段名
				},
			},
		)

		// 如果只有发药推送，则过滤发药纪录不为空的受试者
		if onlyDispensingPush {
			pipeline = append(pipeline,
				bson.M{
					"$match": bson.M{
						"dispensing_data": bson.M{"$ne": []bson.M{}}, // 过滤 dispensing_data 非空的文档
					},
				},
				// 添加字段检查 source=2 的数据
				bson.M{
					"$addFields": bson.M{
						"source_2_edc_push_data": bson.M{
							"$filter": bson.M{
								"input": "$edc_push_data",
								"as":    "item",
								"cond":  bson.M{"$eq": []interface{}{"$$item.source", 2}},
							},
						},
					},
				},

				// 检查 dispensing_data._id 是否全部存在于 source=2 的 edc_push_data 中
				bson.M{
					"$addFields": bson.M{
						"all_dispensing_in_edc_push": bson.M{
							"$eq": []interface{}{
								bson.M{"$size": "$dispensing_data._id"}, // dispensing_data 的 _id 数量
								bson.M{"$size": bson.M{
									"$setIntersection": []interface{}{
										"$dispensing_data._id",        // dispensing_data 的 _id 列表
										"$source_2_edc_push_data.oid", // edc_push_data 的 oid 列表
									},
								}},
							},
						},
					},
				},
				// 过滤 all_dispensing_in_edc_push 为 false 的数据
				bson.M{
					"$match": bson.M{
						"all_dispensing_in_edc_push": false,
					},
				},
			)
		}

	}

	var subjectData []models.HistoryPushSubject
	cursor, err := tools.Database.Collection("subject").Aggregate(ctx, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 细筛
	var retSubjectData []models.HistoryPushSubject
	if onlyDispensingPush {
		retSubjectData = subjectData
	} else {
		//uncheckUpdateTimestamp, checkedUpdateTimestamp, err := getUpdatePushCheckedTime(ctx, project)
		//if err != nil {
		//	return nil, err
		//}

		var projectOncePushType []int
		if pushScenario.RegisterPush {
			projectOncePushType = append(projectOncePushType, 1)
		}
		if pushScenario.ScreenPush {
			projectOncePushType = append(projectOncePushType, 13)
		}
		if pushScenario.RandomPush {
			projectOncePushType = append(projectOncePushType, 3)
		}

		for _, subject := range subjectData {
			subjectOncePushedTypes := getSubjectPushedDetail(subject, projectOncePushType)

			sourceTypeList, hasBeforeUpdate, hasAfterUpdate, err := getSourceTypeList(ctx, subject, project)
			if err != nil {
				return nil, err
			}

			// 移除已经推送过的单次推送内容，登记，筛选，随机
			var retSourceTypeList []int
			for _, st := range sourceTypeList {
				if !slice.Contain(subjectOncePushedTypes, st) {
					retSourceTypeList = append(retSourceTypeList, st)
				}
			}

			if len(retSourceTypeList) > 0 {
				subject.ToBePushedTypes = retSourceTypeList
				subject.HasBeforeUpdate = hasBeforeUpdate
				subject.HasAfterUpdate = hasAfterUpdate
			}
			if pushScenario.DispensingPush {
				// 检查是否有待推送的发药
				hasDispensingToBePush := false
				for _, dispensing := range subject.DispensingData {
					// 检查该发药是否被推送过
					isPushed := false
					for _, edcPush := range subject.EdcPushData {
						if edcPush.OID == dispensing.ID {
							isPushed = true
							break
						}
					}
					if isPushed {
						// 推送过就不推了
						continue
					} else {
						hasDispensingToBePush = true
						break
					}
				}

				if hasDispensingToBePush {
					subject.HasDispensingToBePush = true
				}

			}
			if len(retSourceTypeList) > 0 || subject.HasDispensingToBePush {
				retSubjectData = append(retSubjectData, subject)
			}
		}
	}

	return retSubjectData, nil
}

// 根据推送场景，构造出需要筛选的受试者的状态列表
func buildStatusList(pushScenario models.PushScenario) []int {
	// 根据推送场景筛选对应状态的受试者，（按照最大化原则）
	// 受试者状态(1已登记/2已筛选/3已随机/4已退出/5已替换<页面上展示的是已退出>/6已紧急揭盲/7筛选成功/8筛选失败/9完成研究)
	statusMap := map[string][]int{
		"registerPush":          {1, 2, 3, 6, 7, 8, 9},
		"updateRandomFrontPush": {1, 2, 3, 6, 7, 8, 9},
		"updateRandomAfterPush": {3, 6, 9},
		"randomPush":            {3, 6, 9},
		"screenPush":            {2, 3, 6, 7, 8, 9},
		"dispensingPush":        {1, 2, 3, 6, 7, 9},
	}
	var statusList []int
	if pushScenario.RegisterPush {
		statusList = append(statusList, statusMap["registerPush"]...)
	}
	if pushScenario.UpdateRandomFrontPush {
		statusList = append(statusList, statusMap["updateRandomFrontPush"]...)
	}
	if pushScenario.UpdateRandomAfterPush {
		statusList = append(statusList, statusMap["updateRandomAfterPush"]...)
	}
	if pushScenario.RandomPush {
		statusList = append(statusList, statusMap["randomPush"]...)
	}
	if pushScenario.ScreenPush {
		statusList = append(statusList, statusMap["screenPush"]...)
	}
	if pushScenario.DispensingPush {
		statusList = append(statusList, statusMap["dispensingPush"]...)
	}
	return slice.Unique(statusList)
}

// 根据推送场景，构造出需要筛选的推送来源数组
func buildSourceTypeList(pushScenario models.PushScenario) []int {
	// 来源具体细分 0.未知 1.登记 2.修改 3.随机 4.发药 5.访视外发药 6.药物替换 7.药物补发 8.药物撤销 9.药物取回 10.实际用药 11.受试者替换 12.编辑实际分层 13.筛选
	var sourceTypeList []int
	if pushScenario.RegisterPush {
		sourceTypeList = append(sourceTypeList, 1)
	}
	if pushScenario.RandomPush {
		sourceTypeList = append(sourceTypeList, 3)
	}
	if pushScenario.ScreenPush {
		sourceTypeList = append(sourceTypeList, 13)
	}

	// todo 修改(2,12)和发药特殊处理

	if pushScenario.DispensingPush {
		// 发药推送这里不做过滤
		// 后面要查受试者的所有有效的发药纪录，然后对比该受试者的发药推送纪录，只推没有推送过的
	}
	return slice.Unique(sourceTypeList)
}

// 获取最新一次修改的取消勾选和重新勾选时间
func getUpdatePushCheckedTime(ctx context.Context, project models.Project) (int64, int64, error) {
	var uncheckedTime, recheckedTime int64

	if project.PushScenario.UpdateRandomFrontPush || project.PushScenario.UpdateRandomAfterPush {
		match := bson.M{
			"oid":    project.ID,
			"module": "operation_log.module.project_docking",
		}

		// 查找重新勾选的时间
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{
				Key: "$match",
				Value: bson.M{
					"$or": []bson.M{
						{
							"fields": bson.M{
								"$elemMatch": bson.M{
									"tran_key":  "operation_log.project.updateRandomFrontPush",
									"new.value": true,
									"old.value": false,
								},
							},
						},
						{
							"fields": bson.M{
								"$elemMatch": bson.M{
									"tran_key":  "operation_log.project.updateRandomAfterPush",
									"new.value": true,
									"old.value": false,
								},
							},
						},
					},
				},
			}},
			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
		}

		// 查找取消勾选的时间
		pipeline1 := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{
				Key: "$match",
				Value: bson.M{
					"$or": []bson.M{
						{
							"fields": bson.M{
								"$elemMatch": bson.M{
									"tran_key":  "operation_log.project.updateRandomFrontPush",
									"new.value": false,
									//"old.value": true, // 有可能没开启过，初始值就是false
								},
							},
						},
						{
							"fields": bson.M{
								"$elemMatch": bson.M{
									"tran_key":  "operation_log.project.updateRandomAfterPush",
									"new.value": false,
									//"old.value": true, // 有可能没开启过，初始值就是false
								},
							},
						},
					},
				},
			}},
			{{Key: "$sort", Value: bson.D{{Key: "time", Value: -1}}}},
		}

		// 重新勾选
		var operationLogList []models.OperationLog
		cursor, err := tools.Database.Collection("operation_log").Aggregate(ctx, pipeline)
		if err != nil {
			return 0, 0, errors.WithStack(err)
		}
		err = cursor.All(nil, &operationLogList)
		if err != nil {
			return 0, 0, errors.WithStack(err)
		}

		if len(operationLogList) > 0 {
			recheckedTime = int64(operationLogList[0].Time)
		}

		// 取消勾选
		var operationLogList1 []models.OperationLog
		cursor, err = tools.Database.Collection("operation_log").Aggregate(ctx, pipeline1)
		if err != nil {
			return 0, 0, errors.WithStack(err)
		}
		err = cursor.All(nil, &operationLogList1)
		if err != nil {
			return 0, 0, errors.WithStack(err)
		}

		if len(operationLogList1) > 0 {
			uncheckedTime = int64(operationLogList1[0].Time)
		}
	}

	return uncheckedTime, recheckedTime, nil
}

func getSubjectPushedDetail(subject models.HistoryPushSubject, projectOncePushType []int) []int {
	//historyUpdatePushed := false
	var subjectPushSourceTypes []int
	for _, edcPush := range subject.EdcPushData {
		//// 检查受试者是否历史推送过，并且推送纪录中最新的一次历史修改或历史实际分层是否大于勾选修改配置的时间，
		//// 无论是随机前修改还是随机后修改的勾选时间，只要小于历史修改推送的时间，就代表修改之后历史推送过，就不用再推
		//if (pushScenario.UpdateRandomFrontPush || pushScenario.UpdateRandomAfterPush) && (slice.Contain(edcPush.SourceTypeList, 2) || slice.Contain(edcPush.SourceTypeList, 12)) {
		//	if int64(edcPush.SendTime) > updateCheckTimestamp {
		//		historyUpdatePushed = true
		//	}
		//}
		// 收集所有推送内容
		edcPushTypes := append(edcPush.SourceTypeList, edcPush.SourceType)
		subjectPushSourceTypes = append(subjectPushSourceTypes, edcPushTypes...)
	}
	subjectPushSourceTypes = slice.Unique(subjectPushSourceTypes)
	subjectOncePushedTypes := slice.Intersection(subjectPushSourceTypes, projectOncePushType) // 受试者推送过的单次推送类型

	return subjectOncePushedTypes
}
