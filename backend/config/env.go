package config

import (
	"os"
)

var (
	PORT string

	URI          string
	DB           string
	CLOUD        string
	CLOUD_KEY    string
	CLOUD_SECRET string
	CRM_URL      string
	BucketName   string

	TASK string

	Learn   string
	LearnON string

	CatalentURL  string
	CatalentPort string
	CatalentUser string
	CatalentPwd  string

	DMP_GRPC_URL string
	DMP_KEY      string
	DMP_SECRET   string

	LOG_LEVEL        string
	LOG_OUTPUT       string
	LOG_GRAYLOG_ADDR string

	IRT_URL                     string
	IRT_UNBLINDING_SMS          string
	IRT_MEDICINE_UNBLINDING_SMS string

	CTTQ_SECRET string
)

func Init() {
	PORT = getDefault("PORT", "8080")
	CLOUD_KEY = getDefault("CLOUD_KEY", "irt")
	URI = getDefault("URI", "mongodb://app:<EMAIL>:3717,dds-bp17703931a951942228-pub.mongodb.rds.aliyuncs.com:3717")

	// DEV 环境
	//DB = getDefault("DB", "clinflash-irt-dev")
	//CLOUD = getDefault("CLOUD", "https://cloud-dev.clinflash.com")
	//CLOUD_SECRET = getDefault("CLOUD_SECRET", "8e7e606d0531a150d676019187a5fc68b140919fe345214bd1296de98e93ebc4")

	// Test环境
	DB = getDefault("DB", "clinflash-irt-test")
	CLOUD = getDefault("CLOUD", "https://cloud-test.clinflash.com")
	CLOUD_SECRET = getDefault("CLOUD_SECRET", "7ef8a68ab6f5e64e7b14b77fc4ef5cb78811ed494a28e7b36de6d23fd2f099de")

	// Uat环境
	//DB = getDefault("DB", "clinflash-irt-uat")
	//CLOUD = getDefault("CLOUD", "https://cloud-uat.clinflash.com")
	//CLOUD_SECRET = getDefault("CLOUD_SECRET", "66fd7e30c3637b5019d5e3e46d334f8fbb86c6cbf6cb5fed2e2fdee128882754")

	BucketName = getDefault("BUCKET_NAME", "fs")

	TASK = getDefault("TASK", "OFF")

	Learn = getDefault("LEARN", "https://learn-test.clinflash.com")
	LearnON = getDefault("LEARNON", "")

	//Catalent
	CatalentURL = getDefault("CatalentURL", "catalent.ftpstream.com")
	CatalentPort = getDefault("CatalentPort", "22")
	CatalentUser = getDefault("CatalentUser", "cps_clinflash_qual")
	CatalentPwd = getDefault("CatalentPwd", "CuC9ow0eQ+9r")

	// DMP
	//DMP_GRPC_URL = getDefault("DMP_GRPC_URL", "dmp-grpc.clinflash.com:9090")
	DMP_GRPC_URL = getDefault("DMP_GRPC_URL", "172.16.10.201:20011")
	DMP_KEY = getDefault("DMP_KEY", "irt")
	DMP_SECRET = getDefault("DMP_SECRET", "8e7e606d0531a150d676019187a5fc68b140919fe345214bd1296de98e93ebc4")

	LOG_LEVEL = getDefault("LOG_LEVEL", "info")
	// Set the logger output, if the value is `graylog`, logs will be sent to Graylog server over UDP.
	LOG_OUTPUT = getDefault("LOG_OUTPUT", "")
	LOG_GRAYLOG_ADDR = getDefault("LOG_GRAYLOG_ADDR", "***********:12201")

	IRT_UNBLINDING_SMS = "SMS_465126337"
	IRT_MEDICINE_UNBLINDING_SMS = "SMS_487335548"
	//IRT_UNBLINDING_SMS = "SMS_244960151"   目前线上

	//WKHTMLTOPDF_PATH = "/bin/wkhtmltopdf"

	switch DB {
	case "clinflash-irt-dev":
		IRT_URL = "http://localhost:3000"
	case "clinflash-irt-test":
		IRT_URL = "https://irt-test.clinflash.net"
	case "clinflash-irt-uat":
		IRT_URL = "https://irt-uat.clinflash.net"
	case "clinflash-irt-pre":
		IRT_URL = "https://irt-pre.clinflash.com"
	case "clinflash-irt":
		IRT_URL = "https://irt.clinflash.com"
	}

	switch DB {
	case "clinflash-irt-dev":
		CRM_URL = "https://crm-dev.clinflash.com"
	case "clinflash-irt-test":
		CRM_URL = "https://crm-test.clinflash.com"
	case "clinflash-irt-uat":
		CRM_URL = "https://crm-uat.clinflash.com"
	case "clinflash-irt-pre":
		CRM_URL = "https://crm-pre.clinflash.com"
	case "clinflash-irt":
		CRM_URL = "https://crm.clinflash.com"
	}

	switch DB {
	case "clinflash-irt-dev":
		CTTQ_SECRET = "gL8sL9aX0nB4hA2pU9yK2aS7cD9aJ3dA2dM2"
	case "clinflash-irt-test":
		CTTQ_SECRET = "tQ3hD3qF9dK1lQ0jB0sH8pD2eU5yY4dK8zN4"
	case "clinflash-irt-uat":
		CTTQ_SECRET = "jF7eG6qT5uJ5bQ6xH6vU0pK3rN1nB6dG6iT0"
	case "clinflash-irt-pre":
		CTTQ_SECRET = "mD4vD5cE0wM2cQ1iR6gE6oN3bM2mA2eF1zL9"
	case "clinflash-irt":
		CTTQ_SECRET = "wV0cE3cF2bR0iL8yB4wI9vF9aR2dS6vN2qT7"
	}
}

func getDefault(key string, def string) string {
	value := os.Getenv(key)
	if value == "" {
		os.Setenv(key, def)
		value = def
	}
	return value
}
