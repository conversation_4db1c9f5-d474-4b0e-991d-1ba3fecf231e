package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson"
	"testing"
)

func UpdateOperationOrderDTP(t *testing.T) {

	keyValue := map[string]interface{}{
		"operation.supply.shipment.close": bson.A{
			"operation.supply.shipment.close-dtp",
		},
		"operation.supply.shipment.confirm": bson.A{
			"operation.supply.shipment.confirm-dtp",
		},
		"operation.supply.shipment.cancel": bson.A{
			"operation.supply.shipment.cancel-dtp",
		},
		"operation.supply.shipment.terminated": bson.A{
			"operation.supply.shipment.terminated-dtp",
		},
	}

	for key, value := range keyValue {

		// 模板清洗
		//projectID, _ := primitive.ObjectIDFromHex("64dc2d36de9209a4e22d3cb9")
		roleIDs := bson.A{}
		var rolePermission []models.RolePermission
		cursor, err := DB.Collection("role_permission").Find(nil, bson.M{
			//"project_id": projectID,
			"permissions": key})
		if err != nil {
			panic(err)
		}
		err = cursor.All(nil, &rolePermission)
		if err != nil {
			panic(err)

		}
		for _, permission := range rolePermission {
			roleIDs = append(roleIDs, permission.ID)
		}
		if len(roleIDs) > 0 {
			_, err = DB.Collection("role_permission").UpdateMany(nil,
				bson.M{"_id": bson.M{"$in": roleIDs}},
				bson.M{
					"$addToSet": bson.M{
						"permissions": bson.M{
							"$each": value,
						},
					},
				},
			)
			if err != nil {
				panic(err)
			}
		}

		IDs := bson.A{}
		//projectID, _ := primitive.ObjectIDFromHex("64dc2d36de9209a4e22d3cb9")
		var projectRolePermission []models.ProjectRolePermission
		cursor, err = DB.Collection("project_role_permission").Find(nil, bson.M{
			//"project_id": projectID,
			"permissions": key})
		if err != nil {
			panic(err)
		}
		err = cursor.All(nil, &projectRolePermission)
		if err != nil {
			panic(err)

		}
		for _, permission := range projectRolePermission {
			IDs = append(IDs, permission.ID)
		}
		if len(IDs) > 0 {
			_, err = DB.Collection("project_role_permission").UpdateMany(nil,
				bson.M{"_id": bson.M{"$in": IDs}},
				bson.M{
					"$addToSet": bson.M{
						"permissions": bson.M{
							"$each": value,
						},
					},
				},
			)
			if err != nil {
				panic(err)
			}
		}
	}
}
