package test

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type HistoryWithID struct {
	ID   primitive.ObjectID     `json:"id" bson:"_id"`
	Key  string                 `json:"key" bson:"key"`
	OID  primitive.ObjectID     `json:"oid" bson:"oid"`
	Data map[string]interface{} `json:"data" bson:"data"`
	Time time.Duration          `json:"time" bson:"time"`
	UID  primitive.ObjectID     `json:"uid" bson:"uid"`
	User string                 `json:"user" bson:"user"`
}

//func initUnblindingReasonConfig(t *testing.T) {
//	//揭盲原因配置
//	config := []models.UnblindingReasonConfig{
//		{Reason: "SAE", AllowRemark: false},
//		{Reason: "妊娠", AllowRemark: false},
//		{Reason: "政策要求", AllowRemark: false},
//		{Reason: "其他", AllowRemark: true},
//	}
//	_, err := DB.Collection("attribute").UpdateMany(nil, bson.M{}, bson.M{"$set": bson.M{"info.unblinding_reason_config": config}})
//	if err != nil {
//		panic(err)
//	}
//	subjects := make([]models.Subject, 0)
//	cursor, err := DB.Collection("subject").Find(nil, bson.M{"$or": bson.A{
//		bson.M{"status": 6},
//		bson.M{"pv_unblinding_status": 1},
//		bson.M{"urgent_unblinding_approvals.number": bson.M{"$exists": true}},
//	}})
//	if err != nil {
//		panic(err)
//	}
//	err = cursor.All(nil, &subjects)
//	if err != nil {
//		panic(err)
//	}
//	//受试者
//	var wg sync.WaitGroup
//	wg.Add(len(subjects))
//	for _, subject := range subjects {
//		go func(s models.Subject) {
//			defer wg.Done()
//			if s.Status == 6 {
//				urgentUnblindingReasonStr := type2Str(s.UrgentUnblindingReasonType)
//				_, err = DB.Collection("subject").UpdateOne(nil, bson.M{"_id": s.ID}, bson.M{"$set": bson.M{
//					"urgent_unblinding_reason_str": urgentUnblindingReasonStr,
//				}})
//				if err != nil {
//					panic(err)
//				}
//			}
//			if s.PvUnblindingStatus == 1 {
//				pvUnblindingReasonStr := type2Str(s.PvUnblindingReasonType)
//				_, err = DB.Collection("subject").UpdateOne(nil, bson.M{"_id": s.ID}, bson.M{"$set": bson.M{
//					"pv_unblinding_reason_str": pvUnblindingReasonStr,
//				}})
//				if err != nil {
//					panic(err)
//				}
//			}
//
//			if s.UrgentUnblindingApprovals != nil && len(s.UrgentUnblindingApprovals) > 0 {
//				for _, approval := range s.UrgentUnblindingApprovals {
//					reasonStr := type2Str(approval.Reason)
//					opts := &options.UpdateOptions{
//						ArrayFilters: &options.ArrayFilters{
//							Filters: bson.A{
//								bson.M{"data.number": approval.Number},
//							},
//						},
//					}
//					_, err = DB.Collection("subject").UpdateOne(nil, bson.M{"_id": s.ID}, bson.M{"$set": bson.M{
//						"urgent_unblinding_approvals.$[data].reason_str": reasonStr,
//					}}, opts)
//					if err != nil {
//						panic(err)
//					}
//				}
//			}
//
//		}(subject)
//	}
//	wg.Wait()
//
//	//轨迹 紧急揭盲
//	histories := make([]HistoryWithID, 0)
//	cursor, err = DB.Collection("history").Find(nil, bson.M{"key": "history.subject.unblinding"})
//	if err != nil {
//		panic(err)
//	}
//	err = cursor.All(nil, &histories)
//	if err != nil {
//		panic(err)
//	}
//	wg.Add(len(histories))
//	for _, history := range histories {
//		go func(h HistoryWithID) {
//			defer wg.Done()
//			reasonStr := ""
//			if h.Data["reasonType"] == nil {
//				reasonStr = "其他"
//			} else {
//				reasonStr = type2Str(int(h.Data["reasonType"].(int32)))
//			}
//
//			if err != nil {
//				panic(err)
//			}
//			_, err := DB.Collection("history").UpdateOne(nil, bson.M{"_id": h.ID}, bson.M{"$set": bson.M{"data.reasonStr": reasonStr}})
//			if err != nil {
//				panic(err)
//			}
//		}(history)
//	}
//	wg.Wait()
//
//	//轨迹 pv揭盲
//	pvHistories := make([]HistoryWithID, 0)
//	cursor, err = DB.Collection("history").Find(nil, bson.M{"key": "history.subject.pvUnblinding", "data.remark": nil})
//	if err != nil {
//		panic(err)
//	}
//	err = cursor.All(nil, &pvHistories)
//	if err != nil {
//		panic(err)
//	}
//	wg.Add(len(pvHistories))
//	for _, history := range pvHistories {
//		go func(h HistoryWithID) {
//			defer wg.Done()
//			_, err := DB.Collection("history").UpdateOne(nil, bson.M{"_id": h.ID}, bson.M{"$set": bson.M{
//				"key":         "history.subject.remark-pvUnblinding",
//				"data.reason": "其他",
//				"data.remark": h.Data["reason"],
//			}})
//			if err != nil {
//				panic(err)
//			}
//		}(history)
//	}
//	wg.Wait()
//
//	//轨迹 pv揭盲remark
//	pvRemarkHistories := make([]HistoryWithID, 0)
//	cursor, err = DB.Collection("history").Find(nil, bson.M{"key": "history.subject.remark-pvUnblinding", "data.reason": "Other"})
//	if err != nil {
//		panic(err)
//	}
//	err = cursor.All(nil, &pvRemarkHistories)
//	if err != nil {
//		panic(err)
//	}
//	wg.Add(len(pvRemarkHistories))
//	for _, history := range pvRemarkHistories {
//		go func(h HistoryWithID) {
//			defer wg.Done()
//			_, err := DB.Collection("history").UpdateOne(nil, bson.M{"_id": h.ID}, bson.M{"$set": bson.M{
//				"data.reason": "其他",
//			}})
//			if err != nil {
//				panic(err)
//			}
//		}(history)
//	}
//	wg.Wait()
//}

func type2Str(t int) string {
	if t == 0 {
		return "其他"
	} else if t == 1 {
		return "SAE"
	} else if t == 2 {
		return "妊娠"
	} else if t == 3 {
		return "政策要求"
	}
	return ""
}
