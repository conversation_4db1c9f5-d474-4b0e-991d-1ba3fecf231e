package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strconv"
	"time"
)

func CheckDispensing(needMail bool) (map[string]interface{}, error) {
	defer tools.DeferReturn("CheckDispensing")

	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname", "deleted": bson.M{"$ne": true}}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "project",
			"let": bson.M{
				"env_id": "$env_id",
			},
			"pipeline": bson.A{

				bson.M{"$unwind": "$envs"},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$envs.id", "$$env_id"}}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$envs.name", "PROD"}}}},
			},
			"as": "project",
		}}},
		{{Key: "$unwind", Value: "$project"}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		{{Key: "$match", Value: bson.M{"dispensing.status": 2}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "drug_configure",
			"localField":   "dispensing.visit_info.visit_cycle_info_id",
			"foreignField": "configures.visit_cycles",
			"as":           "drug_configure",
		}}},
		{{Key: "$unwind", Value: "$drug_configure"}},
		{{Key: "$project", Value: bson.M{
			"subject":         "$info.value",
			"group":           "$group",
			"projectNumber":   "$project.info.number",
			"projectName":     "$project.info.name",
			"projectType":     "$project.info.type",
			"projectTimeZone": "$project.info.timeZoneStr",
			"env":             "$project.envs.name",
			"subjectCohort":   "$cohort_id",
			"cohort": bson.M{
				"$first": bson.M{
					"$filter": bson.M{
						"input": "$project.envs.cohorts",
						"as":    "item",
						"cond": bson.M{
							"$eq": bson.A{"$$item.id", "$cohort_id"},
						},
					}},
			},
			"reissue":        "$dispensing.reissue",
			"visitSign":      "$dispensing.visit_sign",
			"visitName":      "$dispensing.visit_info.name",
			"medicines":      "$dispensing.dispensing_medicines.name",
			"otherMedicines": "$dispensing.other_dispensing_medicines",
			"dispensingTime": "$dispensing.dispensing_time",
			"configures": bson.M{
				"$filter": bson.M{
					"input": "$drug_configure.configures",
					"as":    "item",
					"cond": bson.M{
						"$and": bson.A{
							bson.M{
								"$in": bson.A{"$dispensing.visit_info.visit_cycle_info_id", "$$item.visit_cycles"},
							},
							bson.M{
								"$or": bson.A{bson.M{
									"$eq": bson.A{"$$item.group", "$group"},
								}, bson.M{
									"$eq": bson.A{"$$item.group", "N/A"},
								}},
							},
						},
					},
				},
			},
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	ss := make([]string, 0)
	for _, d := range data {
		var timeZone string
		if d["projectTimeZone"] != nil {
			timeZone = d["projectTimeZone"].(string)
		} else {
			timeZone = "8"
		}
		timeZoneFloat, _ := strconv.ParseFloat(timeZone, 64)
		hours := time.Duration(timeZoneFloat)
		minutes := time.Duration((timeZoneFloat - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		dispensingTime := ""
		if d["dispensingTime"] != nil {
			dispensingTime = time.Unix(time.Duration(d["dispensingTime"].(int64)).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
		}
		subjectCohort := d["subjectCohort"].(primitive.ObjectID)
		cohort := ""
		if !subjectCohort.IsZero() && d["cohort"] != nil {
			cohort = d["cohort"].(map[string]interface{})["name"].(string)
		}
		cohortName := "cohort/阶段"
		medicines := bson.A{}
		if d["medicines"] != nil {
			medicines = d["medicines"].(primitive.A)
		}
		otherMedicines := bson.A{}
		if d["otherMedicines"] != nil {
			otherMedicines = d["otherMedicines"].(primitive.A)
		}
		medicinesMap := groupSum(medicines, otherMedicines)
		configures := d["configures"].(primitive.A)
		configs := make([]map[string]int32, 0)
		for _, configure := range configures {
			config := make(map[string]int32)
			values := configure.(map[string]interface{})["values"].(primitive.A)
			for _, value := range values {
				config[value.(map[string]interface{})["drugname"].(string)] = value.(map[string]interface{})["dispensing_number"].(int32)
				configs = append(configs, config)
			}
		}
		allMatch := false
		for _, config := range configs {
			match := true
			for k, v := range config {
				if medicinesMap[k] != v {
					match = false
					break
				}
			}
			if match {
				allMatch = true
				break
			}
		}
		visitName := d["visitName"].(string)
		if d["visitSign"].(bool) && d["reissue"] != nil && d["reissue"].(int32) == 1 {
			visitName = fmt.Sprintf("%s(%s)", d["visitName"], "补发")
		} else if d["visitSign"].(bool) {
			visitName = fmt.Sprintf("%s(%s)", d["visitName"], "访视外")
		}
		if !allMatch {
			s := fmt.Sprintf("projectNumber[%s] projectName[%s] env[%s] %s[%s] 受试者[%s] 访视[%s] 发药时间[%s] 该次发药与配置不匹配,请检查是否进行了补发、取回，若不是请检查是否数据异常",
				d["projectNumber"].(string), d["projectName"].(string), d["env"].(string), cohortName, cohort, d["subject"], visitName, dispensingTime)
			ss = append(ss, s)
		}

	}
	if len(ss) > 0 && needMail {
		var settingConfig map[string]interface{}
		tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": "corssCheck"}).Decode(&settingConfig)
		var mailArr []string
		for _, item := range settingConfig["data"].(map[string]interface{})["mail"].(primitive.A) {
			mailArr = append(mailArr, item.(string))
		}
		mails := models.Mail{
			ID:           primitive.NewObjectID(),
			Subject:      "cross.check.error",
			ContentData:  map[string]interface{}{"results": ss},
			To:           mailArr,
			Lang:         "en-US",
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         "layer_check_en-US_new.html",
		}
		_, err = tools.Database.Collection("mail").InsertOne(nil, mails)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	result := map[string]interface{}{}
	result["info"] = ss
	return result, nil
}

func groupSum(medicines primitive.A, otherMedicines primitive.A) map[string]int32 {
	result := make(map[string]int32)
	for _, m := range medicines {
		s := m.(string)
		if result[s] == 0 {
			result[s] = 1
		} else {
			result[s] = result[s] + 1
		}
	}
	for _, o := range otherMedicines {
		m := o.(map[string]interface{})
		name := m["name"].(string)
		count := m["count"].(int32)
		if result[name] == 0 {
			result[name] = count
		} else {
			result[name] = result[name] + count
		}
	}
	return result
}
