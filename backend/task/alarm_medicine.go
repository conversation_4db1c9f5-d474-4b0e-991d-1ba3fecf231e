package task

import (
	"clinflash-irt/catalent"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"clinflash-irt/wms"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// TaskAlarmMedicine ...
func TaskAlarmMedicine(orderCheck int) error {
	err := AlarmMedicine(orderCheck, []string{})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// AlarmMedicine ...
func AlarmMedicine(orderCheck int, checkEnv []string, env ...string) error {
	ctx := context.Background()
	//sctx.StartTransaction()
	//// 过滤匹配获取PRO项目
	//var mails []models.Mail
	match := bson.M{"envs.name": "PROD", "status": bson.M{"$ne": 2}, "info.order_check": orderCheck, "info.research_attribute": bson.M{"$ne": 1}}
	if len(env) > 0 {
		// 手动触发，当前环境核查。
		envOID, _ := primitive.ObjectIDFromHex(env[0])
		match = bson.M{"envs.id": envOID}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "attribute",
			"localField":   "envs.id",
			"foreignField": "env_id",
			"as":           "attribute",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$attribute", "preserveNullAndEmptyArrays": true}}},
		//{{Key: "$unwind", Value: "$attribute"}},
		{{Key: "$project", Value: bson.M{
			"_id":           0,
			"id":            "$_id",
			"envId":         "$envs.id",
			"customerId":    "$customer_id",
			"projectName":   "$info.name",
			"projectNumber": "$info.number",
			"timeZone":      "$info.timeZoneStr",
			"envName":       "$envs.name",
			"is_freeze":     "$attribute.info.is_freeze",
			//"attribute":      "$attribute",
		}}},
	}
	cursor, err := tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	var projects []map[string]interface{}
	err = cursor.All(nil, &projects)
	if err != nil {
		return errors.WithStack(err)
	}
	envs := map[string]interface{}{}
	// 循环项目里的中心
	for _, project := range projects {
		timeZone, _ := strconv.ParseFloat(project["timeZone"].(string), 64)
		hours := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		now := time.Now().UTC().Add(duration).Format("2006-01-02")
		ctx = context.WithValue(ctx, "now", now)

		envStr := project["envId"].(primitive.ObjectID).Hex()
		if _, ok := envs[envStr]; ok {
			// 同个环境下 则跳过
			continue
		} else {
			envs[envStr] = true
		}
		// 判断项目属性中，隔离的是否作为运单的一部分
		status := []int{1, 2, 3}
		if project["is_freeze"] != nil && project["is_freeze"].(bool) {
			status = append(status, 4)
		}
		// 获取该中心的供应计划研究产品配置
		match := bson.M{"project_id": project["id"], "env_id": project["envId"], "deleted": 2, "supply_plan_id": bson.M{"$ne": primitive.NilObjectID}}

		pipeline = mongo.Pipeline{
			{{Key: "$match", Value: match}},
			// 关联订单
			{{Key: "$lookup", Value: bson.M{
				"from":         "medicine_order",
				"localField":   "_id",
				"foreignField": "receive_id",
				"as":           "medicine_order",
			}}},
			// 过滤未创建订单的中心  即订单数量 == 0  jira 1916
			{{"$match", bson.M{"medicine_order.status": bson.M{"$in": bson.A{1, 2, 3, 4, 5, 8, 9}}}}},
			// 联表查询 供应计划研究产品配置
			{{Key: "$lookup", Value: bson.M{
				"from":         "supply_plan_medicine",
				"localField":   "supply_plan_id",
				"foreignField": "supply_plan_id",
				"as":           "plan",
			}}},

			//  根据查询到的订单 联表 查询研究产品（待配送 运送或者可用）

			{{Key: "$unwind", Value: "$plan"}},
			//
			{{Key: "$match", Value: bson.M{"$or": bson.A{bson.M{"plan.info.warning": bson.M{"$ne": 0}}, bson.M{"plan.info.auto_supply_size": 3}}}}},

			{{Key: "$lookup", Value: bson.M{
				"from": "medicine",
				"let": bson.M{
					"site_id":          "$_id",
					"name":             "$plan.info.medicine_name",
					"not_counted_date": "$plan.info.not_counted_date",
					"un_provide_date":  "$plan.info.un_provide_date",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}, "status": bson.M{"$in": status}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$name", "$$name"}}}}},
					// 过滤 不计入+不发放日期
					{{"$project", bson.M{
						"name":            1,
						"expiration_date": 1,
						"un_push_date": bson.M{
							"$dateToString": bson.M{
								"date": bson.M{
									"$add": bson.A{
										bson.M{"$toDate": now},
										bson.M{"$multiply": bson.A{86400000, bson.M{"$add": bson.A{bson.M{"$ifNull": bson.A{"$$not_counted_date", 0}}, bson.M{"$ifNull": bson.A{"$$un_provide_date", 0}}}}}},
									},
								},
								"format": "%Y-%m-%d",
							},
						},
					}}},
					//{{"$match", bson.M{"$expr": bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}}}}},

					{{"$match", bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}}},
							bson.M{"expiration_date": ""},
							bson.M{"expiration_date": nil},
						},
					}}},

					{{Key: "$group", Value: bson.M{"_id": "$name", "count": bson.M{"$sum": 1}}}},
				},
				"as": "medicine",
			}}},
			{{Key: "$unwind", Value: bson.M{"path": "$medicine", "preserveNullAndEmptyArrays": true}}},

			// 联表查询未编号研究产品
			{{Key: "$lookup", Value: bson.M{
				"from": "medicine_others",
				"let": bson.M{
					"site_id":          "$_id",
					"name":             "$plan.info.medicine_name",
					"not_counted_date": "$plan.info.not_counted_date",
					"un_provide_date":  "$plan.info.un_provide_date",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}, "status": bson.M{"$in": status}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$name", "$$name"}}}}},
					// 过滤 不计入 + 不发放日期
					{{"$project", bson.M{
						"name":            1,
						"expiration_date": 1,
						"un_push_date": bson.M{
							"$dateToString": bson.M{
								"date": bson.M{
									"$add": bson.A{
										bson.M{"$toDate": now},
										bson.M{"$multiply": bson.A{86400000, bson.M{"$add": bson.A{bson.M{"$ifNull": bson.A{"$$not_counted_date", 0}}, bson.M{"$ifNull": bson.A{"$$un_provide_date", 0}}}}}},
									},
								},
								"format": "%Y-%m-%d",
							},
						},
					}}},
					//{{"$match", bson.M{"$expr": bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}}}}},

					{{"$match", bson.M{
						"$or": bson.A{
							bson.M{"$expr": bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}}},
							bson.M{"expiration_date": ""},
							bson.M{"expiration_date": nil},
						},
					}}},

					{{Key: "$group", Value: bson.M{"_id": "$name", "count": bson.M{"$sum": 1}}}},
				},
				"as": "medicine_other_institute",
			}}},
			{{Key: "$unwind", Value: bson.M{"path": "$medicine_other_institute", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$unwind", Value: bson.M{"path": "$medicine_other", "preserveNullAndEmptyArrays": true}}},

			//// 查询低于警戒值的记录
			{{Key: "$match", Value: bson.M{
				"$or": bson.A{ // 编号研究产品名称 相同， 小于警戒值
					bson.M{"$and": bson.A{
						bson.M{"$expr": bson.M{"$eq": bson.A{"$plan.info.medicine_name", "$medicine._id"}}},
						bson.M{"$expr": bson.M{"$gt": bson.A{"$plan.info.warning", "$medicine.count"}}},
					}},
					bson.M{"$and": bson.A{ // 未编号研究产品名称 相同， 小于警戒值
						bson.M{"$expr": bson.M{"$eq": bson.A{"$plan.info.medicine_name", "$medicine_other_institute._id"}}},
						bson.M{"$expr": bson.M{"$gt": bson.A{"$plan.info.warning", "$medicine_other_institute.count"}}},
					}},
					bson.M{"medicine_other_institute": bson.M{"$exists": 0}, "medicine": bson.M{"$exists": 0}},
					// 配了自动预测
					bson.M{"plan.info.auto_supply_size": 3},
				}}}},
			{{Key: "$unwind", Value: "$storehouse_id"}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_storehouse",
				"localField":   "storehouse_id",
				"foreignField": "_id",
				"as":           "project_storehouse",
			}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "storehouse",
				"localField":   "project_storehouse.storehouse_id",
				"foreignField": "_id",
				"as":           "storehouse",
			}}},
			{{Key: "$project",
				Value: bson.M{
					"_id":            0,
					"site_id":        "$_id",
					"customer_id":    1,
					"project_id":     1,
					"env_id":         1,
					"storehouse_id":  1,
					"active":         1,
					"storehouseName": "$storehouse.name",
					"count":          bson.M{"$ifNull": bson.A{"$medicine.count", 0}},
					"other":          bson.M{"$ifNull": bson.A{"$medicine_other_institute.count", 0}}, //中心未编码数量
					"sendNumber":     "$storehouse.number",
					"sendName":       "$storehouse.name",
					"siteNumber":     "$number",
					"siteName":       models.ProjectSiteNameZhBson(),
					"siteNameEn":     models.ProjectSiteNameEnBson(),
					"contacts":       "$contacts",
					"phone":          "$phone",
					"address":        "$address",
					"info":           "$plan.info",
					"supply_plan_id": "$plan.supply_plan_id",
					"email":          "$email",
					"contact_group":  "$contact_group",
					"forecast_min":   "$plan.info.forecast_min",
					"name":           "$plan.info.medicine_name",
					"forecast_max":   "$plan.info.forecast_max",
				},
			}},
		}
		appTaskNotices, _ := getAppTaskNotice(project["envId"].(primitive.ObjectID))
		var orderIds []primitive.ObjectID

		var title_zh string
		var title_en string
		var alertBody_zh string
		var alertBody_en string
		var registrationId_zh []string
		var registrationId_en []string
		var sJson []byte

		callback := func(sctx mongo.SessionContext) (interface{}, error) {
			var err error
			defer func() {
				if err := recover(); err != nil {
					trace := tools.PrintStackTrace(err)
					tools.SaveErrorLog("TaskAlarmMedicine-"+project["projectNumber"].(string), errors.New(trace))
				}

			}()
			// 过滤匹配获取PRO项目
			var mails []models.Mail
			cursor, err = tools.Database.Collection("project_site").Aggregate(nil, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var projectSitePlan []map[string]interface{}
			err = cursor.All(nil, &projectSitePlan)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			orderNumber, err := getOrderNumber(project["id"].(primitive.ObjectID))
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var siteUse []primitive.ObjectID
			var siteMailUse []primitive.ObjectID
			var siteMailTypeUse []primitive.ObjectID // 控制一个中心自动订单创建失败只发一份

			otherNameMap, err := getOtherMedicineName(project["envId"].(primitive.ObjectID))
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 获取未编号药物
			// app任务通知
			var appTaskNotice []interface{}
			for _, item := range projectSitePlan {
				// 自动发药
				// 全研究产品补充 供应计划相同过滤   (中心相同）
				needOrder := true

				// 全研究产品 单研究产品  各中心 只跑一次 生成一个订单或者药物警戒通知
				for _, site := range siteUse {
					if item["site_id"] == site {
						needOrder = false
						break
					}
				}
				siteUse = append(siteUse, item["site_id"].(primitive.ObjectID))

				if int(item["active"].(int32)) == 2 { // 是否发药  调整为中心管理 激活控制

					if needOrder {
						var siteOrStoreIDs = []primitive.ObjectID{item["site_id"].(primitive.ObjectID), item["storehouse_id"].(primitive.ObjectID)}
						userMail, err := tools.GetRoleUsersMailWithRole(project["id"].(primitive.ObjectID), project["envId"].(primitive.ObjectID), "notice.medicine.order", siteOrStoreIDs...)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						orderId, err := autoMedicineOrder(ctx, sctx, item, project, otherNameMap, &mails, userMail, orderNumber, &siteMailTypeUse)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						if !orderId.IsZero() {
							orderIds = append(orderIds, orderId)
						}
						orderNumbers, _ := strconv.Atoi(orderNumber)
						orderNumber = strconv.Itoa(orderNumbers + 1)
					}

				} else { // 非自动发药 需要邮件通知提醒
					needMail := true
					for _, id := range siteMailUse {
						if item["site_id"] == id {
							needMail = false
						}
					}
					if needMail {

						// 查询是否开启研究产品库存
						//
						documents, err := tools.Database.Collection("notice_config").CountDocuments(nil, bson.M{
							"env_id": project["envId"].(primitive.ObjectID),
							"state":  "order.no_automatic_title",
						})
						if err != nil {
							return nil, errors.WithStack(err)
						}
						if documents == 0 {
							continue
						}

						userMail, err := tools.GetRoleUsersMail(project["id"].(primitive.ObjectID), project["envId"].(primitive.ObjectID), "notice.medicine.alarm", item["site_id"].(primitive.ObjectID))
						if err != nil {
							return nil, errors.WithStack(err)
						}

						// if  < 警戒值 直接触发邮件
						// if  > 警戒值 需要判断自动预测够不够
						alarm := false
						if (item["other"] != nil || item["info"].(map[string]interface{})["warning"].(int32) > item["other"].(int32)) ||
							(item["count"] != nil && item["info"].(map[string]interface{})["warning"].(int32) > item["count"].(int32)) {
							alarm = true
						} else {
							_, min, err := generationForecast(sctx, item)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							if min > int(item["other"].(int32)) {
								alarm = true
							}
						}

						if !alarm {
							continue
						}
						siteMailUse = append(siteMailUse, item["site_id"].(primitive.ObjectID))

						data := bson.M{
							"customer_id":     project["customerId"],
							"project_site_id": item["site_id"],
							"project_id":      project["id"],
							"env_id":          project["envId"],
							"projectNumber":   project["projectNumber"],
							"projectName":     project["projectName"],
							"envName":         project["envName"],
							"siteNumber":      item["siteNumber"],
							"siteName":        item["siteName"],
							"siteNameEn":      item["siteNameEn"]}
						mailBodyContet, err := tools.MailBodyContent(nil, project["envId"].(primitive.ObjectID), "notice.medicine.alarm")
						for key, v := range mailBodyContet {
							data[key] = v
						}
						if err != nil {
							return nil, errors.WithStack(err)
						}

						// 查询该环境下分配中心的用户
						CID := project["customerId"].(primitive.ObjectID)
						PID := project["id"].(primitive.ObjectID)
						EID := project["envId"].(primitive.ObjectID)
						SID := item["site_id"].(primitive.ObjectID)
						siteName := item["siteName"]
						var uIds []models.UserIds
						var us []primitive.ObjectID
						userProjectEnvironments := make([]models.UserProjectEnvironment, 0)
						upeCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"project_id": PID, "env_id": EID, "app": true})
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = upeCursor.All(nil, &userProjectEnvironments)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						projectRolePermissions := make([]models.ProjectRolePermission, 0)
						prpCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": PID})
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = prpCursor.All(nil, &projectRolePermissions)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						userSites := make([]models.UserSite, 0)
						userSitesCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"project_id": PID, "env_id": EID, "site_id": SID})
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = userSitesCursor.All(nil, &userSites)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						for _, upe := range userProjectEnvironments {
							for _, ur := range upe.Roles {
								for _, prp := range projectRolePermissions {
									if ur == prp.ID {
										bl := true
										//去重逻辑
										for _, u := range uIds {
											if u.UserID == upe.UserID {
												bl = false
											}
										}
										if prp.Scope == "study" && bl {
											uIds = append(uIds, models.UserIds{
												UserID: upe.UserID,
												Read:   false,
											})
											us = append(us, upe.UserID)
										} else if prp.Scope == "site" && bl {
											for _, ust := range userSites {
												if ust.UserID == upe.UserID {
													uIds = append(uIds, models.UserIds{
														UserID: upe.UserID,
														Read:   false,
													})
													us = append(us, upe.UserID)
													break
												}
											}
										}
										break
									}
								}
							}
						}

						atn := slice.Filter(appTaskNotices, func(index int, item models.AppTaskNotice) bool {
							return item.ExpireNotice.ProjectSiteID == SID
						})
						if (uIds != nil && len(uIds) > 0) && (atn == nil || len(atn) == 0) {
							appTaskNotice = append(appTaskNotice, models.AppTaskNotice{
								ID:            primitive.NewObjectID(),
								CustomerID:    CID,
								ProjectID:     PID,
								EnvironmentID: EID,
								NoticeType:    1,
								NoticeTime:    time.Duration(time.Now().Unix()),
								ExpireNotice: models.ExpireNotice{
									WorkType:      11,
									ProjectSiteID: SID,
								},
								UserIds: uIds,
							})

							// 极光推送
							wtv := models.WorkTaskView{}
							wtv.NoticeType = 1
							wtv.WorkNoticeType = 11

							var wk models.WorkTask
							//wk.Info.WorkType = 12
							wk.ProjectID = PID
							wk.EnvironmentID = EID
							//registrationId = append(registrationId, "101d85590841177bedb")
							userRegistrationIdAppLanguages, err := getUserRegistrationId(33, us, wk, primitive.NilObjectID, SID)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							projectInfo, _, _ := getAuroraPushProjectInfo(PID, EID)
							title_zh = locales.TrWithLang("zh-CN", "app_site_alert_notification_title")
							title_en = locales.TrWithLang("en-US", "app_site_alert_notification_title")

							alertBody_zh = "[" + siteName.(string) + "]" + locales.TrWithLang("zh-CN", "app_site_alert_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"
							alertBody_en = "[" + siteName.(string) + "]" + locales.TrWithLang("en-US", "app_site_alert_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"

							//转JSON
							sJson, err = json.Marshal(wtv)
							if err != nil {
								return nil, errors.WithStack(err)
							}

							registrationId_zh = []string{}
							registrationId_en = []string{}
							for _, ural := range userRegistrationIdAppLanguages {
								if ural.AppLanguage == "" || ural.AppLanguage == "zh-CN" {
									registrationId_zh = append(registrationId_zh, ural.RegistrationId)
								} else {
									registrationId_en = append(registrationId_en, ural.RegistrationId)
								}
							}

							// 中文推送
							//if registrationId_zh != nil && len(registrationId_zh) > 0 {
							//	err = tools.DataAssembly(registrationId_zh, title_zh, alertBody_zh, string(sJson))
							//	if err != nil {
							//		return nil, errors.WithStack(err)
							//	}
							//}
							//
							//// 英文推送
							//if registrationId_en != nil && len(registrationId_en) > 0 {
							//	err = tools.DataAssembly(registrationId_en, title_en, alertBody_en, string(sJson))
							//	if err != nil {
							//		return nil, errors.WithStack(err)
							//	}
							//}
						}

						var noticeConfig models.NoticeConfig
						err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": project["envId"].(primitive.ObjectID), "key": "notice.basic.settings"}).Decode(&noticeConfig)
						if err != nil && err != mongo.ErrNoDocuments {
							return nil, errors.WithStack(err)
						}
						langList := make([]string, 0)
						html := "order_no_automatic_new.html"
						if noticeConfig.Automatic != 0 {
							if noticeConfig.Automatic == 1 {
								langList = append(langList, "zh")
								html = "order_no_automatic_new_zh.html"
							} else if noticeConfig.Automatic == 2 {
								langList = append(langList, "en")
								html = "order_no_automatic_new_en.html"
							} else if noticeConfig.Automatic == 3 {
								langList = append(langList, "zh")
								langList = append(langList, "en")
								html = "order_no_automatic_new.html"
							}
						} else {
							langList = append(langList, "zh")
							langList = append(langList, "en")
						}

						for _, mail := range userMail {
							var toUserMail []string
							toUserMail = append(toUserMail, mail)
							mails = append(mails, models.Mail{
								ID:          primitive.NewObjectID(),
								Subject:     "order.no_automatic_title",
								SubjectData: data,
								//Content:        "order.no_automatic_dual",
								ContentData:  data,
								To:           toUserMail,
								Lang:         "en-US",
								LangList:     langList,
								Status:       0,
								CreatedTime:  time.Duration(time.Now().Unix()),
								ExpectedTime: time.Duration(time.Now().Unix()),
								SendTime:     time.Duration(time.Now().Unix()),
								HTML:         html,
							})
						}

					}
				}

			}

			// 添加app任务通知
			if appTaskNotice != nil && len(appTaskNotice) > 0 {
				_, err = tools.Database.Collection("app_task_notice").InsertMany(sctx, appTaskNotice)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			var docs []interface{}
			var mailEnvs []interface{}
			if orderCheck == 2 {
				// 过滤当天已发送的邮件
				filterMails, err := filterMail(sctx, mails)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, mail := range filterMails {
					if len(mail.To) > 0 {
						docs = append(docs, mail)
					}
				}
			} else {
				for _, mail := range mails {
					if len(mail.To) > 0 {
						docs = append(docs, mail)
					}
				}
			}
			if len(docs) > 0 {
				_, err := tools.Database.Collection("mail").InsertMany(sctx, docs)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if len(mails) > 0 {
					for _, m := range docs {
						mail := m.(models.Mail)
						mailEnvs = append(mailEnvs, models.MailEnv{
							ID:         primitive.NewObjectID(),
							MailID:     mail.ID,
							CustomerID: project["customerId"].(primitive.ObjectID),
							ProjectID:  project["id"].(primitive.ObjectID),
							EnvID:      project["envId"].(primitive.ObjectID),
						})
					}
					_, err := tools.Database.Collection("mail_env").InsertMany(sctx, mailEnvs)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
			return nil, errors.WithStack(err)
		}
		err = func() error {
			cctx := context.Background()
			session, err := tools.MongoClient.StartSession()
			if err != nil {
				tools.SaveErrorLog("TaskAlarmMedicine-"+project["projectNumber"].(string), err)
				return errors.WithStack(err)
			}
			defer session.EndSession(cctx)
			_, err = session.WithTransaction(cctx, callback)
			if err != nil {
				tools.SaveErrorLog("TaskAlarmMedicine-"+project["projectNumber"].(string), err)
				return errors.WithStack(err)
			}
			return nil
		}()
		if err == nil {
			for _, id := range orderIds {
				err := catalent.SendToCatalent(id.Hex())
				if err != nil {
					tools.SaveErrorLog(fmt.Sprintf("SendToCatalent-%s", id), err)
				}
				var order models.MedicineOrder
				if err := tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": id}).Decode(&order); err != nil {
					tools.SaveErrorLog(fmt.Sprintf("SendToWMS-%s", id), err)
				} else {
					connectedErr := wms.ConnectedWms(nil, nil, order)

					if connectedErr != nil {
						tools.SaveErrorLog(fmt.Sprintf("SendToWMS-%s", id), connectedErr)
					}
				}
			}
			//
			// 中文推送
			if registrationId_zh != nil && len(registrationId_zh) > 0 {
				err = tools.DataAssembly(registrationId_zh, title_zh, alertBody_zh, string(sJson))
				if err != nil {
					tools.SaveErrorLog("TaskAlarmMedicinePushApp-"+project["projectNumber"].(string), err)
				}
			}

			// 英文推送
			if registrationId_en != nil && len(registrationId_en) > 0 {
				err = tools.DataAssembly(registrationId_en, title_en, alertBody_en, string(sJson))
				if err != nil {
					tools.SaveErrorLog("TaskAlarmMedicinePushApp-"+project["projectNumber"].(string), err)
				}
			}

		}
	}
	return nil
}

func autoMedicineOrder(ctx context.Context, sctx mongo.SessionContext, info map[string]interface{}, project map[string]interface{}, otherNameMap map[string]bool, mail *[]models.Mail, userMail []tools.RoleTypeEmail, orderNumber string, needMails *[]primitive.ObjectID) (primitive.ObjectID, error) {
	//projectOID, _ := info["project_id"].(primitive.ObjectID)
	//envOID, _ := info["env_id"].(primitive.ObjectID)
	//customerOID := info["customer_id"].(primitive.ObjectID)
	//storeOID := info["storehouse_id"].(primitive.ObjectID)
	//siteOID := info["site_id"].(primitive.ObjectID)
	//mode := int(info["info"].(map[string]interface{})["auto_supply_size"].(int32))
	supplyMode := int(info["info"].(map[string]interface{})["supply_mode"].(int32))
	//count := 0
	// ************************************************************ 全研究产品补充 ************************************************************
	orderId := primitive.NilObjectID
	var err error
	if supplyMode == 1.0 || supplyMode == 3.0 {
		// 全研究产品补充
		orderId, err = AllSupplyMode(ctx, sctx, info, project, otherNameMap, mail, userMail, orderNumber)
		if err != nil {
			return orderId, err
		}
	} else {
		orderId, err = SingeSupplyModeNew(ctx, sctx, info, project, otherNameMap, mail, userMail, orderNumber)
		if err != nil {
			return orderId, err
		}
	}
	return orderId, nil
}

func getOrderNumber(OID primitive.ObjectID) (string, error) {
	var orderNumber string
	var data []models.MedicineOrder
	pipepine := mongo.Pipeline{
		{{Key: "$sort", Value: bson.D{{"order_number", -1}}}},
		{{Key: "$limit", Value: 1}},
	}
	cursor, err := tools.Database.Collection("medicine_order").Aggregate(nil, pipepine)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return "", errors.WithStack(err)
	}
	timeZone, err := tools.GetTimeZone(OID)
	if err != nil {
		return "", errors.WithStack(err)
	}
	ho := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(ho)) * 60)
	duration := ho*time.Hour + minutes*time.Minute
	now := time.Now().UTC().Add(duration).Format("20060102")

	if data != nil {
		maxOrderNumber := data[0].OrderNumber
		if maxOrderNumber[:8] == now {
			maxNumber, _ := strconv.Atoi(maxOrderNumber[9:])
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%04s", number)
			orderNumber = now + formatNumber
		} else {
			orderNumber = now + "0001"
		}
	} else {
		orderNumber = now + "0001"
	}

	return orderNumber, nil
}

// AllSupplyMode ..全研究产品补充
func AllSupplyMode(ctx context.Context, sctx mongo.SessionContext, info map[string]interface{}, project map[string]interface{}, otherNameMap map[string]bool, mail *[]models.Mail, userMail []tools.RoleTypeEmail, orderNumber string) (orderId primitive.ObjectID, err error) {
	status := []int{1, 2, 3}
	supplyMode := int(info["info"].(map[string]interface{})["supply_mode"].(int32))
	if project["is_freeze"] != nil && project["is_freeze"].(bool) {
		status = append(status, 4)
	}
	data, err := getAllSupplyPlan(ctx, sctx, info, project)

	if err != nil {
		return [12]byte{}, errors.WithStack(err)
	}
	pageMedicineIDs := []primitive.ObjectID{}
	pageOtherMedicineIDs := []primitive.ObjectID{}
	create := true
	//var otherMedicine []models.OtherMedicineCount
	var otherDrugData []models.OtherMedicine
	timeZone, err := tools.GetTimeZone(project["id"].(primitive.ObjectID))
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}
	otherMedicineID := []primitive.ObjectID{}
	medicineID := []primitive.ObjectID{}
	medicineData := []map[string]interface{}{}
	blindMedicineData := []map[string]interface{}{}

	medicineName := []map[string]interface{}{}

	needOrder := 0
	medicinesPackage := []models.MedicinePackage{}
	packageIsOpen, packageAllCount, packageConfigs, mixPackageConfig, _, _ := tools.IsOpenPackage(info["env_id"].(primitive.ObjectID))
	drugPackageCount := map[string]models.DrugPackageCount{}
	for _, item := range data {
		isPageNumber, ok := packageConfigs[item["name"].(string)]
		needPageCount := packageIsOpen && ok && isPageNumber != 0
		hours, _ := time.ParseDuration(fmt.Sprintf("%sh", strconv.Itoa(int(item["un_distribution_date"].(int32))*24)))

		ho := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(ho)) * 60)
		duration := ho*time.Hour + minutes*time.Minute
		date := time.Now().Add(hours).UTC().Add(duration).Format("2006-01-02")

		site := 0
		other := false
		// 其他研究产品
		if otherNameMap[item["name"].(string)] {
			other = true
			if item["medicine_other_institute"] != nil {
				site = int(item["medicine_other_institute"].(map[string]interface{})["count"].(int32))
			}
		} else {
			// 编号研究产品
			medicineName = append(medicineName, map[string]interface{}{
				"name":                 item["name"],
				"un_distribution_date": item["un_distribution_date"],
				"not_counted_date":     0,
			})
			if item["medicine"] != nil {
				site = int(item["medicine"].(map[string]interface{})["count"].(int32))
			}

		}

		var meCount int
		var forecaseCount int
		_, buffer := slice.Find(item["auto_supply_size"].(primitive.A), func(index int, item interface{}) bool {
			return int(item.(int32)) == 1
		})
		_, supply := slice.Find(item["auto_supply_size"].(primitive.A), func(index int, item interface{}) bool {
			return int(item.(int32)) == 2
		})
		_, forecast := slice.Find(item["auto_supply_size"].(primitive.A), func(index int, item interface{}) bool {
			return int(item.(int32)) == 3
		})
		if buffer { // 最大缓冲 - 中心存量
			meCount = int(item["buffer"].(int32)) - site
		}
		if supply { //在供应量
			meCount = int(item["second_supply"].(int32))
		}
		max := 0
		min := 0

		if forecast { //最大预测量
			max, min, err = generationForecast(sctx, item)
			if err != nil {
				return [12]byte{}, errors.WithStack(err)
			}
			forecaseCount = max - site
		}

		if int(item["warning"].(int32)) > site && min <= site { // 警戒条件达到  预测没到
			// 继续使用meCount 警戒值算出来的最大缓冲 再供应
		}

		if int(item["warning"].(int32)) <= site && min > site { // 预测条件达到  预测没到
			//
			meCount = forecaseCount
		}

		if int(item["warning"].(int32)) > site && min > site { // 预测条件达到  预测条件达到
			if forecaseCount > meCount { //使用最大值
				meCount = forecaseCount
			}
		}

		if min <= site && int(item["warning"].(int32)) <= site { // 预测没到 警戒没到 都跳过
			needOrder++
			// 使用最大量
			if forecaseCount > meCount { //使用最大值
				meCount = forecaseCount
			}
		}

		if !buffer && !supply && forecast && forecaseCount > 0 { // 只有预测的情况  使用预测的值
			meCount = forecaseCount
		}

		if meCount > 0 {
			{
				// 生成订单
				// 未编号研究产品
				if other {
					// 更新库存的数量
					queryFilter := bson.M{
						"project_id":    item["project_id"],
						"env_id":        item["env_id"],
						"storehouse_id": item["storehouse_id"],
						"name":          item["name"].(string),
						//"expiration_date": bson.M{"$gt": date},
						"$or": []bson.M{
							bson.M{"expiration_date": bson.M{"$gt": date}},
							bson.M{"expiration_date": nil},
							bson.M{"expiration_date": ""},
						},
						"status": 1,
					}
					if needPageCount {
						drugPackageCount[item["name"].(string)] = models.DrugPackageCount{
							Count:              int(math.Ceil(float64(meCount) / float64(isPageNumber))),
							UnDistributionDate: date,
							Other:              true,
						}
					} else {
						otherMedicinePipeline := mongo.Pipeline{
							{{Key: "$match", Value: queryFilter}},
							models.MedicineQueryProject,
							models.MedicineSort,
							{{Key: "$limit", Value: meCount}},
						}

						cursor, err := tools.Database.Collection("medicine_others").Aggregate(sctx, otherMedicinePipeline)
						if err != nil {
							return primitive.NilObjectID, errors.WithStack(err)
						}

						err = cursor.All(nil, &otherDrugData)
						if err != nil {
							return primitive.NilObjectID, errors.WithStack(err)
						}
						if meCount != len(otherDrugData) {
							create = false
						}
						for _, medicine := range otherDrugData {
							otherMedicineID = append(otherMedicineID, medicine.ID)
						}
						if !create {
							break
						}
					}
				} else {
					// 编号研究产品
					create, err = getStoreNumberMedicine(sctx, &medicineID, item, date, drugPackageCount, needPageCount, meCount, isPageNumber)
					if err != nil {
						return [12]byte{}, err
					}
					if !create {
						break
					}
				}

			}
		}
	}

	if needOrder == len(data) { // 所有药物都没有到警戒值 自动预测
		return primitive.NilObjectID, nil
	}

	if create && len(drugPackageCount) > 0 {
		for _, item := range data {
			maxCount := drugPackageCount[item["name"].(string)].Count
			unDistributionDate := drugPackageCount[item["name"].(string)].UnDistributionDate
			drugNames := mixPackageConfig[item["name"].(string)]
			if len(drugNames) == 0 {
				continue
			}
			for _, v := range drugNames {
				if maxCount < drugPackageCount[v].Count {
					maxCount = drugPackageCount[v].Count
				}
				if unDistributionDate != "" && unDistributionDate < drugPackageCount[v].UnDistributionDate {
					unDistributionDate = drugPackageCount[v].UnDistributionDate
				}
			}
			drugPackageCount[item["name"].(string)] = models.DrugPackageCount{
				Count:              maxCount,
				UnDistributionDate: unDistributionDate,
				Other:              otherNameMap[item["name"].(string)],
			}
		}
		createNumber := create
		createOtherNumber := create
		pageMedicineIDs, createNumber, err = GetPageMedicine(sctx, nil, project["envId"].(primitive.ObjectID), info["storehouse_id"].(primitive.ObjectID), &medicinesPackage, drugPackageCount, packageAllCount, mixPackageConfig)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		pageOtherMedicineIDs, createOtherNumber, err = GetPageOtherMedicine(sctx, project["envId"].(primitive.ObjectID), info["storehouse_id"].(primitive.ObjectID), &medicinesPackage, drugPackageCount, packageAllCount, mixPackageConfig)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		medicineID = append(medicineID, pageMedicineIDs...)
		otherMedicineID = append(otherMedicineID, pageOtherMedicineIDs...)
		if createNumber && createOtherNumber {
			create = true
		}
	}

	if len(otherMedicineID) == 0 && len(medicineID) == 0 && create {
		err = AlarmOrderMail(project, info, userMail, mail)
		if err != nil {
			return [12]byte{}, errors.WithStack(err)
		}
		return [12]byte{}, nil
	}

	// 存在库存不足， 通知否则生成订单
	if !create {
		// 查询通知配置 是否需要邮件通知
		state, err := tools.MailCustomContentState(project["envId"].(primitive.ObjectID), "order.automatic_error_title")
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		if state {
			contentData := bson.M{
				"project_id":      project["id"],
				"env_id":          project["envId"],
				"project_site_id": info["site_id"],
				"projectNumber":   project["projectNumber"],
				"orderNumber":     orderNumber,
				"projectName":     project["projectName"],
				"envName":         project["envName"],
				"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
				"destinationEn":   fmt.Sprintf("%s-%s", info["siteNumber"], info["siteNameEn"]),
				"start":           fmt.Sprintf("%s", info["sendName"]),
			}
			mailBodyContet, err := tools.MailBodyContent(nil, project["envId"].(primitive.ObjectID), "notice.medicine.order")
			for key, v := range mailBodyContet {
				contentData[key] = v
			}
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			var noticeConfig models.NoticeConfig
			err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": project["envId"].(primitive.ObjectID), "key": "notice.basic.settings"}).Decode(&noticeConfig)
			if err != nil && err != mongo.ErrNoDocuments {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			langList := make([]string, 0)
			html := "order_automatic_error_new.html"
			if noticeConfig.Automatic != 0 {
				if noticeConfig.Automatic == 1 {
					langList = append(langList, "zh")
					html = "order_automatic_error_new_zh.html"
				} else if noticeConfig.Automatic == 2 {
					langList = append(langList, "en")
					html = "order_automatic_error_new_en.html"
				} else if noticeConfig.Automatic == 3 {
					langList = append(langList, "zh")
					langList = append(langList, "en")
					html = "order_automatic_error_new.html"
				}
			} else {
				langList = append(langList, "zh")
				langList = append(langList, "en")
			}
			for _, userEmail := range userMail {
				var toUserMail []string
				toUserMail = append(toUserMail, userEmail.Email)
				*mail = append(*mail, models.Mail{
					ID:      primitive.NewObjectID(),
					Subject: "order.automatic_error_title",
					SubjectData: bson.M{
						"project_id":      project["id"],
						"customer_id":     project["customerId"],
						"env_id":          project["envId"],
						"project_site_id": info["site_id"],
						"projectNumber":   project["projectNumber"],
						"orderNumber":     orderNumber,
						"projectName":     project["projectName"],
						"envName":         project["envName"],
						"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
						"destinationEn":   fmt.Sprintf("%s-%s", info["siteNumber"], info["siteNameEn"]),
					},
					//Content:      "order.automatic_error_dual",
					ContentData:  contentData,
					To:           toUserMail,
					Lang:         "en-US",
					LangList:     langList,
					Status:       0,
					CreatedTime:  time.Duration(time.Now().Unix()),
					ExpectedTime: time.Duration(time.Now().Unix()),
					SendTime:     time.Duration(time.Now().Unix()),
					HTML:         html,
				})
			}
		}
	} else {
		if supplyMode == 3.0 {
			create, err = addOneRandomMedicine(sctx, data[0]["env_id"].(primitive.ObjectID), &medicineID, medicineName, data[0]["storehouse_id"].(primitive.ObjectID), packageConfigs, packageIsOpen, &medicinesPackage, drugPackageCount, packageAllCount, mixPackageConfig)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}

			if !create {
				// 查询通知配置 是否需要邮件通知
				state, err := tools.MailCustomContentState(project["envId"].(primitive.ObjectID), "order.automatic_error_title")
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				if state {
					contentData := bson.M{
						"project_id":      project["id"],
						"env_id":          project["envId"],
						"project_site_id": info["site_id"],
						"projectNumber":   project["projectNumber"],
						"orderNumber":     orderNumber,
						"projectName":     project["projectName"],
						"envName":         project["envName"],
						"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
						"start":           fmt.Sprintf("%s", info["sendName"]),
					}

					mailBodyContet, err := tools.MailBodyContent(nil, project["envId"].(primitive.ObjectID), "notice.medicine.order")
					for key, v := range mailBodyContet {
						contentData[key] = v
					}
					if err != nil {
						return primitive.NilObjectID, errors.WithStack(err)
					}
					var noticeConfig models.NoticeConfig
					err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": project["envId"].(primitive.ObjectID), "key": "notice.basic.settings"}).Decode(&noticeConfig)
					if err != nil && err != mongo.ErrNoDocuments {
						return primitive.NilObjectID, errors.WithStack(err)
					}
					langList := make([]string, 0)
					html := "order_automatic_error_new.html"
					if noticeConfig.Automatic != 0 {
						if noticeConfig.Automatic == 1 {
							langList = append(langList, "zh")
							html = "order_automatic_error_new_zh.html"
						} else if noticeConfig.Automatic == 2 {
							langList = append(langList, "en")
							html = "order_automatic_error_new_en.html"
						} else if noticeConfig.Automatic == 3 {
							langList = append(langList, "zh")
							langList = append(langList, "en")
							html = "order_automatic_error_new.html"
						}
					} else {
						langList = append(langList, "zh")
						langList = append(langList, "en")
					}
					for _, userEmail := range userMail {
						var toUserMail []string
						toUserMail = append(toUserMail, userEmail.Email)
						*mail = append(*mail, models.Mail{
							ID:      primitive.NewObjectID(),
							Subject: "order.automatic_error_title",
							SubjectData: bson.M{
								"project_id":      project["id"],
								"customer_id":     project["customerId"],
								"env_id":          project["envId"],
								"project_site_id": info["site_id"],
								"projectNumber":   project["projectNumber"],
								"orderNumber":     orderNumber,
								"projectName":     project["projectName"],
								"envName":         project["envName"],
								"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
							},
							//Content: "order.automatic_error_dual",
							ContentData:  contentData,
							To:           toUserMail,
							Lang:         "en-US",
							LangList:     langList,
							Status:       0,
							CreatedTime:  time.Duration(time.Now().Unix()),
							ExpectedTime: time.Duration(time.Now().Unix()),
							SendTime:     time.Duration(time.Now().Unix()),
							HTML:         html,
						})
					}
				}

				return primitive.NilObjectID, nil
			}

		}
		medicineData, blindMedicineData, err = getMedicineDataForMail(medicineID, otherMedicineID, project["envId"].(primitive.ObjectID), packageIsOpen)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}

		//更新研究产品状态(创建)

		// 药物数量为0的情况
		if len(otherMedicineID) > 0 || len(medicineID) > 0 {
			contacts := ""
			phone := ""
			email := ""
			address := ""
			if info["contact_group"] != nil {
				contactsGroup := info["contact_group"].(primitive.A)
				for _, v := range contactsGroup {
					contact := v.(map[string]interface{})
					if contact["isdefault"] != nil && contact["isdefault"].(int32) == 1 {
						if contact["contacts"] != nil {
							contacts = contact["contacts"].(string)
						}
						if contact["phone"] != nil {
							phone = contact["phone"].(string)
						}
						if contact["email"] != nil {
							email = contact["email"].(string)
						}
						if contact["address"] != nil {
							address = contact["address"].(string)
						}

					}
				}
			} else {
				if info["contacts"] != nil {
					contacts = info["contacts"].(string)
				}
				if info["phone"] != nil {
					phone = info["phone"].(string)
				}
				if info["email"] != nil {
					email = info["email"].(string)
				}
				if info["address"] != nil {
					address = info["address"].(string)
				}
			}
			order := models.MedicineOrder{
				ID:                primitive.NewObjectID(),
				CustomerID:        data[0]["customer_id"].(primitive.ObjectID),
				ProjectID:         data[0]["project_id"].(primitive.ObjectID),
				EnvironmentID:     data[0]["env_id"].(primitive.ObjectID),
				SendID:            data[0]["storehouse_id"].(primitive.ObjectID),
				ReceiveID:         data[0]["site_id"].(primitive.ObjectID),
				MedicinesPackage:  medicinesPackage,
				Status:            1,
				SortIndex:         10,
				Mode:              1,
				Medicines:         medicineID,
				OtherMedicinesNew: otherMedicineID,
				//OtherMedicines:   otherMedicine,
				OrderNumber: orderNumber,
				Contacts:    contacts,
				Phone:       phone,
				Email:       email,
				Address:     address,
				Type:        1,
				Meta: models.Meta{
					CreatedAt: time.Duration(time.Now().Unix()),
				},
			}

			// 插入订单
			result, err := tools.Database.Collection("medicine_order").InsertOne(sctx, order)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			// 插入订单轨迹
			err = insertOrderHistory(nil, sctx, order)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			//创建待运送的订单任务
			permissions := []string{"operation.supply.shipment.send", "operation.supply.shipment.lose", "operation.supply.shipment.receive", "operation.supply.shipment.close"}
			var siteOrStoreIDs = []primitive.ObjectID{order.SendID, order.ReceiveID}
			userIds, err := tools.GetPermissionUserIds(sctx, permissions, order.ProjectID, order.EnvironmentID, siteOrStoreIDs...)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			if len(userIds) > 0 {
				createWorkTask := models.WorkTask{
					ID:            primitive.NewObjectID(),
					CustomerID:    order.CustomerID,
					ProjectID:     order.ProjectID,
					EnvironmentID: order.EnvironmentID,
					//CohortID:      order.CohortID,
					UserIDs: userIds,
					Info: models.WorkTaskInfo{
						WorkType:        6,
						Status:          0,
						CreatedTime:     time.Duration(time.Now().Unix()),
						Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
						MedicineIDs:     []primitive.ObjectID{},
						MedicineOrderID: order.ID,
						DispensingID:    primitive.NilObjectID,
					},
				}
				_, err = tools.Database.Collection("work_task").InsertOne(sctx, createWorkTask)
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
			}

			orderId = order.ID
			medicineFilter := bson.M{"_id": bson.M{"$in": medicineID}}
			update := bson.M{
				"$set": bson.M{
					"status":        2,
					"order_id":      result.InsertedID,
					"site_id":       data[0]["site_id"],
					"storehouse_id": nil,
				},
			}

			_, err = tools.Database.Collection("medicine").UpdateMany(sctx, medicineFilter, update)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			otherMedicineFilter := bson.M{"_id": bson.M{"$in": otherMedicineID}}
			_, err = tools.Database.Collection("medicine_others").UpdateMany(sctx, otherMedicineFilter, update)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			// 插入邮件
			// 查询通知配置 是否需要邮件通知
			state, err := tools.MailCustomContentState(project["envId"].(primitive.ObjectID), "order.automatic_success_title")
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}

			if state {
				contentData := bson.M{"projectNumber": project["projectNumber"],
					"projectName":   project["projectName"],
					"envName":       project["envName"],
					"destination":   fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
					"start":         fmt.Sprintf("%s", info["sendName"]),
					"orderNumber":   orderNumber,
					"contacts":      contacts,
					"phone":         phone,
					"email":         email,
					"address":       address,
					"sendCompany":   info["storehouseName"],
					"results":       medicineData,
					"packageIsOpen": packageIsOpen,

					//"other":       otherMedicine
				}

				blindContentData := bson.M{"projectNumber": project["projectNumber"],
					"projectName":   project["projectName"],
					"envName":       project["envName"],
					"destination":   fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
					"start":         fmt.Sprintf("%s", info["sendName"]),
					"orderNumber":   orderNumber,
					"contacts":      contacts,
					"phone":         phone,
					"email":         email,
					"address":       address,
					"sendCompany":   info["storehouseName"],
					"results":       blindMedicineData,
					"packageIsOpen": packageIsOpen,

					//"other":       otherMedicine
				}
				mailBodyContet, err := tools.MailBodyContent(nil, order.EnvironmentID, "notice.medicine.order")
				for key, v := range mailBodyContet {
					contentData[key] = v
					blindContentData[key] = v
				}
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				bodyContentKeys, _, err := tools.MailCustomContent(&gin.Context{}, project["envId"].(primitive.ObjectID), "notice.medicine.order", contentData, nil)
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				//userMail := tools.GetRoleUsersMail(project["id"].(primitive.ObjectID), "notice.medicine.alarm", siteOID)
				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": project["envId"].(primitive.ObjectID), "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return primitive.NilObjectID, errors.WithStack(err)
				}

				langList := make([]string, 0)
				html := "medicine_order_new_zh_en.html"
				if noticeConfig.Automatic != 0 {
					if noticeConfig.Automatic == 1 {
						langList = append(langList, "zh")
						html = "medicine_order_new_zh.html"
					} else if noticeConfig.Automatic == 2 {
						langList = append(langList, "en")
						html = "medicine_order_new_en.html"
					} else if noticeConfig.Automatic == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						html = "medicine_order_new_zh_en.html"
					}
				} else {
					langList = append(langList, "zh")
					langList = append(langList, "en")
				}
				for _, userEmail := range userMail {
					sendContendData := contentData
					if userEmail.IsBlind {
						sendContendData = blindContentData
					}
					var toUserMail []string
					toUserMail = append(toUserMail, userEmail.Email)
					*mail = append(*mail, models.Mail{
						ID:      primitive.NewObjectID(),
						Subject: "order.automatic_success_title",
						SubjectData: bson.M{
							"customer_id":     project["customerId"],
							"project_id":      project["id"],
							"env_id":          project["envId"],
							"project_site_id": info["site_id"],
							"orderNumber":     orderNumber,
							"projectNumber":   project["projectNumber"],
							"projectName":     project["projectName"],
							"envName":         project["envName"],
							"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
							"destinationEn":   fmt.Sprintf("%s-%s", info["siteNumber"], info["siteNameEn"]),
							//
							//"siteNumber":    info["siteNumber"],
							//"siteName":      info["siteName"],
						},
						Content:        "order.automatic_success",
						ContentData:    sendContendData,
						BodyContentKey: bodyContentKeys,
						To:             toUserMail,
						Lang:           "en-US",
						LangList:       langList,
						Status:         0,
						CreatedTime:    time.Duration(time.Now().Unix()),
						ExpectedTime:   time.Duration(time.Now().Unix()),
						SendTime:       time.Duration(time.Now().Unix()),
						HTML:           html,
					})
				}
			}
		}
	}

	return orderId, nil
}

// addOneRandomMedicine ...
func addOneRandomMedicine(sctx mongo.SessionContext, envOID primitive.ObjectID, medicineID *[]primitive.ObjectID, medicineName []map[string]interface{}, storehouseID primitive.ObjectID, packageDrugNames map[string]int, packageIsOpen bool, medicinesPackage *[]models.MedicinePackage, drugPackageCount map[string]models.DrugPackageCount, packageAllCount map[string]int, mixPackageConfig map[string][]string) (bool, error) {
	// +1 随机号研究产品
	create := true
	type MedicinePage struct {
		Medicine         []models.Medicine
		IsPage           bool
		PageCount        int
		MixPackageConfig []string
	}
	var avlMedicine []MedicinePage
	findOneOpts := &options.FindOneOptions{
		Sort: bson.D{{"expiration_date", 1}, {"number", 1}},
	}
	//envOID := data[0]["env_id"]
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return false, errors.WithStack(err)
	}
	for _, item := range medicineName {
		isPageNumber, ok := packageDrugNames[item["name"].(string)]
		needPageCount := packageIsOpen && ok && isPageNumber != 0
		var medicine models.Medicine
		hours, _ := time.ParseDuration(fmt.Sprintf("%sh", strconv.Itoa(int(item["un_distribution_date"].(int32))*24)))
		timeZone, err := tools.GetTimeZone(project.ID)
		if err != nil {
			return false, errors.WithStack(err)
		}
		ho := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(ho)) * 60)
		duration := ho*time.Hour + minutes*time.Minute
		date := time.Now().Add(hours).UTC().Add(duration).Format("2006-01-02")
		match := bson.M{
			"status": 1,
			"env_id": envOID,
			"name":   item["name"],
			//"expiration_date": bson.M{"$gt": date},
			"$or": []bson.M{
				bson.M{"expiration_date": bson.M{"$gt": date}},
				bson.M{"expiration_date": nil},
				bson.M{"expiration_date": ""},
			},
			"storehouse_id": storehouseID,
		}
		if len(*medicineID) > 0 {
			match["_id"] = bson.M{"$nin": *medicineID}
		}
		if needPageCount {
			tmpDrugPackageCount := map[string]models.DrugPackageCount{}
			for key := range drugPackageCount {
				tmpDrugPackageCount[key] = models.DrugPackageCount{
					Count:              1, // 一个包装
					UnDistributionDate: drugPackageCount[key].UnDistributionDate,
				}
			}
			medicines := []primitive.ObjectID{}
			if create {
				medicines, create, err = GetPageMedicine(sctx, *medicineID, envOID, storehouseID, medicinesPackage, tmpDrugPackageCount, packageAllCount, mixPackageConfig)
				if err != nil {
					return false, errors.WithStack(err)
				}
			}

			if len(medicines) > 0 {
				tmpMedicine := []models.Medicine{}
				for _, tmpMedicineID := range medicines {
					tmpMedicine = append(tmpMedicine, models.Medicine{ID: tmpMedicineID})
				}
				avlMedicine = append(avlMedicine, MedicinePage{
					Medicine:         tmpMedicine,
					IsPage:           true,
					MixPackageConfig: mixPackageConfig[item["name"].(string)],
					PageCount:        packageAllCount[item["name"].(string)],
				})
			}
		} else {
			err = tools.Database.Collection("medicine").FindOne(sctx, match, findOneOpts).Decode(&medicine)
			if err != nil && err != mongo.ErrNoDocuments {
				return false, errors.WithStack(err)
			}
			if medicine.ID != primitive.NilObjectID {
				var medicines []models.Medicine
				medicines = append(medicines, medicine)
				avlMedicine = append(avlMedicine, MedicinePage{
					Medicine: medicines,
					IsPage:   false,
				})
			}
		}

	}
	// +1随机号数量不足
	if len(avlMedicine) == 0 {
		create = false
	} else {
		random := rand.Intn(len(avlMedicine))
		oneMedicine := avlMedicine[random]

		if oneMedicine.IsPage {
			for _, name := range oneMedicine.MixPackageConfig {
				_, ok := slice.Find(*medicinesPackage, func(index int, item models.MedicinePackage) bool {
					return item.Name == name
				})
				if !ok {
					*medicinesPackage = append(*medicinesPackage, models.MedicinePackage{
						Name:          name,
						PackageNumber: oneMedicine.PageCount,
						PackageMethod: true,
					})
				}
			}

		}
		for _, medicine := range oneMedicine.Medicine {
			*medicineID = append(*medicineID, medicine.ID)
		}
	}
	return create, nil
}

func SingeSupplyModeNew(ctx context.Context, sctx mongo.SessionContext, info map[string]interface{}, project map[string]interface{}, otherNameMap map[string]bool, mail *[]models.Mail, userMail []tools.RoleTypeEmail, orderNumber string) (orderId primitive.ObjectID, err error) {
	projectOID, _ := info["project_id"].(primitive.ObjectID)
	envOID, _ := info["env_id"].(primitive.ObjectID)
	customerOID := info["customer_id"].(primitive.ObjectID)
	storeOID := info["storehouse_id"].(primitive.ObjectID)
	siteOID := info["site_id"].(primitive.ObjectID)

	// 项目
	projectFilter := bson.M{"_id": projectOID}
	var querProject models.Project
	err = tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&querProject)
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}

	// ************************************************************ 单研究产品补充 ************************************************************
	var drugDataID []primitive.ObjectID
	var otherDrugDataID []primitive.ObjectID
	//var otherMedicinesCount []models.OtherMedicineCount
	medicinesPackage := []models.MedicinePackage{}
	data, err := getAllSupplyPlan(ctx, sctx, info, project)
	if err != nil {
		return [12]byte{}, errors.WithStack(err)
	}
	needFail := false
	packageIsOpen, packageAllCount, packageConfigs, mixPackageConfig, _, _ := tools.IsOpenPackage(info["env_id"].(primitive.ObjectID))
	drugPackageCount := map[string]models.DrugPackageCount{}
	for _, item := range data {
		isPageNumber, ok := packageConfigs[item["name"].(string)]
		needPageCount := packageIsOpen && ok && isPageNumber != 0
		//mode := int(item["auto_supply_size"].(int32))
		_, buffer := slice.Find(item["auto_supply_size"].(primitive.A), func(index int, item interface{}) bool {

			return int(item.(int32)) == 1
		})
		_, supply := slice.Find(item["auto_supply_size"].(primitive.A), func(index int, item interface{}) bool {
			return int(item.(int32)) == 2
		})
		_, forecast := slice.Find(item["auto_supply_size"].(primitive.A), func(index int, item interface{}) bool {
			return int(item.(int32)) == 3
		})
		needCount := -1
		site := 0
		if otherNameMap[item["name"].(string)] { // 是未编号药
			if item["medicine_other_institute"] != nil {
				site = int(item["medicine_other_institute"].(map[string]interface{})["count"].(int32))
			}
			if int32(site) < item["warning"].(int32) { //小于警戒值
				if buffer {
					needCount = int(item["buffer"].(int32) - int32(site))
				}
				if supply {
					needCount = int(item["second_supply"].(int32))
				}
			}
		} else {
			medicineCount := 0
			if item["medicine"] != nil {
				site = int(item["medicine"].(map[string]interface{})["count"].(int32))
				medicineCount = int(item["medicine"].(map[string]interface{})["count"].(int32))
			}
			if medicineCount < int(item["warning"].(int32)) { //小于警戒值
				if buffer {
					needCount = int(item["buffer"].(int32)) - medicineCount
				}
				if supply {
					needCount = int(item["second_supply"].(int32))
				}
			}
		}

		if forecast { //最大预测量
			max, min, err := generationForecast(sctx, item)
			if err != nil {
				return [12]byte{}, errors.WithStack(err)
			}
			if min > site && max-site > needCount {
				needCount = max - site
			}

			if min <= site && int(item["warning"].(int32)) <= site {
				continue
			}
		}
		// 库存小于警戒值 && 需要的数量 > 0
		if needCount > 0 {
			// 过期时间   （当前时间 + 不配送时间）
			hours, _ := time.ParseDuration(fmt.Sprintf("%sh", strconv.Itoa(int(item["un_distribution_date"].(int32))*24)))
			timeZone, err := tools.GetTimeZone(projectOID)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			hour := time.Duration(timeZone)
			minute := time.Duration((timeZone - float64(hour)) * 60)
			duration := hour*time.Hour + minute*time.Minute
			date := time.Now().Add(hours).UTC().Add(duration).Format("2006-01-02")
			if otherNameMap[item["name"].(string)] {
				var medicineOther []models.OtherMedicine
				drugMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID,
					"name":          item["name"],
					"storehouse_id": item["storehouse_id"],
					//"expiration_date": bson.M{"$gt": date},
					"$or": []bson.M{
						bson.M{"expiration_date": bson.M{"$gt": date}},
						bson.M{"expiration_date": nil},
						bson.M{"expiration_date": ""},
					},
					"status": 1,
				}
				if needPageCount {
					drugPackageCount[item["name"].(string)] = models.DrugPackageCount{
						Count:              int(math.Ceil(float64(needCount) / float64(isPageNumber))),
						UnDistributionDate: date,
						Other:              true,
					}
				} else {

					counts := int64(needCount)
					cur, err := tools.Database.Collection("medicine_others").Find(sctx, drugMatch, &options.FindOptions{Sort: bson.M{"_id": 1}, Limit: &counts})
					if err != nil {
						return [12]byte{}, errors.WithStack(err)
					}
					err = cur.All(nil, &medicineOther)
					if err != nil {
						return [12]byte{}, errors.WithStack(err)
					}
					if len(medicineOther) == needCount {
						for _, medicine := range medicineOther {
							otherDrugDataID = append(otherDrugDataID, medicine.ID)
						}
						_, ok := slice.Find(medicinesPackage, func(index int, data models.MedicinePackage) bool {
							return data.Name == item["name"].(string)
						})
						if !ok {
							medicinesPackage = append(medicinesPackage, models.MedicinePackage{
								Name:          item["name"].(string),
								PackageMethod: needPageCount,
								//PackageNumber: packageDrugNames[item["name"].(string)],
							})
						}
					} else {
						needFail = true
					}
				}
				// // 分批次获取未编号数量
				// var medicineOtherInstitutes []models.MedicineOtherInstitute
				// drugMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID,
				// 	"info.name":     item["name"],
				// 	"storehouse_id": item["storehouse_id"],
				// 	//"info.expire_date": bson.M{"$gt": date},
				// 	"$or": []bson.M{
				// 		bson.M{"info.expire_date": bson.M{"$gt": date}},
				// 		bson.M{"info.expire_date": nil},
				// 		bson.M{"info.expire_date": ""},
				// 	},
				// 	"info.count": bson.M{"$gt": 0},
				// }
				// cur, err := tools.Database.Collection("medicine_other_institute").Find(sctx, drugMatch, &options.FindOptions{Sort: bson.M{"info.expire_date": 1}})
				// if err != nil {
				// 	return [12]byte{}, errors.WithStack(err)
				// }
				// err = cur.All(nil, &medicineOtherInstitutes)
				// if err != nil {
				// 	return [12]byte{}, errors.WithStack(err)
				// }
				// generateDrug := 0
				// for _, institute := range medicineOtherInstitutes {
				// 	if generateDrug < needCount {
				// 		// 一个批次足够
				// 		if institute.Info.Count > needCount {
				// 			// 更新库房数据
				// 			currentCount := needCount - generateDrug // 当前的数量 = 总数-已获取
				// 			generateDrug = generateDrug + currentCount
				// 			err = generateOtherMedicine(sctx, item, institute, currentCount, &otherMedicinesCount)
				// 			if err != nil {
				// 				return [12]byte{}, errors.WithStack(err)
				// 			}
				// 			break
				// 		} else { // 多批次获取
				// 			currentCount := needCount - generateDrug
				// 			if institute.Info.Count > 0 {
				// 				if institute.Info.Count > currentCount {
				// 					generateDrug = generateDrug + currentCount
				// 					err = generateOtherMedicine(sctx, item, institute, currentCount, &otherMedicinesCount)
				// 					if err != nil {
				// 						return [12]byte{}, errors.WithStack(err)
				// 					}
				// 					break
				// 				} else {
				// 					generateDrug = generateDrug + institute.Info.Count
				// 					err = generateOtherMedicine(sctx, item, institute, institute.Info.Count, &otherMedicinesCount)
				// 					if err != nil {
				// 						return [12]byte{}, errors.WithStack(err)
				// 					}
				// 				}

				// 			}
				// 		}
				// 	}
				// }

				// // 按批次获取 直到数量相等
				// if generateDrug == needCount {
				// 	// 未编号数量足够
				// } else {
				// 	needFail = true
				// }
			} else {
				create, err := getStoreNumberMedicine(sctx, &drugDataID, item, date, drugPackageCount, needPageCount, needCount, isPageNumber)
				if err != nil {
					return primitive.NilObjectID, errors.WithStack(err)
				}
				if !create {
					needFail = true
				}
			}

		}
	}

	create := true

	if !needFail && len(drugPackageCount) > 0 {
		pageMedicineIDs := []primitive.ObjectID{}
		pageOtherMedicineIDs := []primitive.ObjectID{}
		for _, item := range data {
			maxCount := drugPackageCount[item["name"].(string)].Count
			unDistributionDate := drugPackageCount[item["name"].(string)].UnDistributionDate
			durgNames := mixPackageConfig[item["name"].(string)]
			if len(durgNames) == 0 {
				continue
			}
			for _, v := range durgNames {
				if maxCount < drugPackageCount[v].Count {
					maxCount = drugPackageCount[v].Count
				}
				if unDistributionDate < drugPackageCount[v].UnDistributionDate {
					unDistributionDate = drugPackageCount[v].UnDistributionDate
				}
			}
			drugPackageCount[item["name"].(string)] = models.DrugPackageCount{
				Count:              maxCount,
				UnDistributionDate: unDistributionDate,
				Other:              otherNameMap[item["name"].(string)],
			}
		}
		createNumber := needFail
		createOtherNumber := needFail
		pageMedicineIDs, createNumber, err = GetPageMedicine(sctx, nil, project["envId"].(primitive.ObjectID), info["storehouse_id"].(primitive.ObjectID), &medicinesPackage, drugPackageCount, packageAllCount, mixPackageConfig)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		pageOtherMedicineIDs, createOtherNumber, err = GetPageOtherMedicine(sctx, project["envId"].(primitive.ObjectID), info["storehouse_id"].(primitive.ObjectID), &medicinesPackage, drugPackageCount, packageAllCount, mixPackageConfig)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		if !(createNumber && createOtherNumber) {
			needFail = true
		}
		drugDataID = append(drugDataID, pageMedicineIDs...)
		otherDrugDataID = append(otherDrugDataID, pageOtherMedicineIDs...)
	}

	if len(drugDataID) == 0 && len(otherDrugDataID) == 0 && !needFail {
		// TODO 自动订单警戒
		err = AlarmOrderMail(project, info, userMail, mail)
		if err != nil {
			return [12]byte{}, errors.WithStack(err)
		}
		return [12]byte{}, nil

	}
	// 单研究产品补充 + 1个随机研究产品号
	supplyMode := int(info["info"].(map[string]interface{})["supply_mode"].(int32))

	if supplyMode == 4.0 && (len(drugDataID) > 0 || len(otherDrugDataID) > 0) {
		// 查询当前供应计划研究产品配置列表，排除 未编号研究产品名称
		//envOID := info["env_id"]
		var supplyPlanMedicine []map[string]interface{}
		cursor, _ := tools.Database.Collection("supply_plan_medicine").Aggregate(sctx, mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"supply_plan_id": info["supply_plan_id"]}}},
			{{Key: "$lookup", Value: bson.M{
				"from": "medicine_other",
				"let": bson.M{
					"env_id":        "$env_id",
					"medicine_name": "$info.medicine_name",
				},
				"pipeline": bson.A{
					bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}},
					bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$info.name", "$$medicine_name"}}}},
				},
				"as": "medicine_other",
			}}},
			{{Key: "$match", Value: bson.M{"medicine_other": bson.M{"$size": 0}}}},
			{{Key: "$project", Value: bson.M{
				"name":                 "$info.medicine_name",
				"un_distribution_date": "$info.un_distribution_date",
			}}},
		})
		err := cursor.All(nil, &supplyPlanMedicine)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		create, err = addOneRandomMedicine(sctx, info["env_id"].(primitive.ObjectID), &drugDataID, supplyPlanMedicine, info["storehouse_id"].(primitive.ObjectID), packageConfigs, packageIsOpen, &medicinesPackage, drugPackageCount, packageAllCount, mixPackageConfig)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
	}
	// 查询通知配置 是否需要邮件通知
	state, err := tools.MailCustomContentState(project["envId"].(primitive.ObjectID), "order.automatic_error_title")
	if err != nil {
		return primitive.NilObjectID, errors.WithStack(err)
	}
	if (!create || needFail) && state {
		subjectData := bson.M{
			"customer_id":     project["customerId"],
			"project_id":      project["id"],
			"env_id":          project["envId"],
			"project_site_id": info["site_id"],
			"projectNumber":   project["projectNumber"],
			"orderNumber":     orderNumber,
			"projectName":     project["projectName"],
			"envName":         project["envName"],
			"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
		}
		var noticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": project["envId"].(primitive.ObjectID), "key": "notice.basic.settings"}).Decode(&noticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return primitive.NilObjectID, errors.WithStack(err)
		}

		langList := make([]string, 0)
		if noticeConfig.Automatic != 0 {
			if noticeConfig.Automatic == 1 {
				langList = append(langList, "zh")
			} else if noticeConfig.Automatic == 2 {
				langList = append(langList, "en")
			} else if noticeConfig.Automatic == 3 {
				langList = append(langList, "zh")
				langList = append(langList, "en")
			}
		} else {
			langList = append(langList, "zh")
			langList = append(langList, "en")
		}
		for _, userEmail := range userMail {
			contentData := bson.M{
				"project_id":      project["id"],
				"env_id":          project["envId"],
				"project_site_id": info["site_id"],
				"projectNumber":   project["projectNumber"],
				"orderNumber":     orderNumber,
				"projectName":     project["projectName"],
				"envName":         project["envName"],
				"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
				"start":           fmt.Sprintf("%s", info["sendName"]),
			}
			mailBodyContet, err := tools.MailBodyContent(nil, project["envId"].(primitive.ObjectID), "notice.medicine.order")
			for key, v := range mailBodyContet {
				contentData[key] = v
			}
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			var toUserMail []string
			toUserMail = append(toUserMail, userEmail.Email)
			*mail = append(*mail, models.Mail{
				ID:           primitive.NewObjectID(),
				Subject:      "order.automatic_error_title",
				SubjectData:  subjectData,
				Content:      "order.automatic_error_dual",
				ContentData:  contentData,
				To:           toUserMail,
				Lang:         "en-US",
				LangList:     langList,
				Status:       0,
				CreatedTime:  time.Duration(time.Now().Unix()),
				ExpectedTime: time.Duration(time.Now().Unix()),
				SendTime:     time.Duration(time.Now().Unix()),
			})
		}
	}

	// 创建订单

	// 其中一个存在 则生成订单
	if (len(drugDataID) > 0 || len(otherDrugDataID) > 0) && create {
		contacts := ""
		phone := ""
		email := ""
		address := ""
		if info["contact_group"] != nil {
			contactsGroup := info["contact_group"].(primitive.A)
			for _, v := range contactsGroup {
				contact := v.(map[string]interface{})
				if contact["isdefault"] != nil && contact["isdefault"].(int32) == 1 {
					if contact["contacts"] != nil {
						contacts = contact["contacts"].(string)
					}
					if contact["phone"] != nil {
						phone = contact["phone"].(string)
					}
					if contact["email"] != nil {
						email = contact["email"].(string)
					}
					if contact["address"] != nil {
						address = contact["address"].(string)
					}
				}
			}
		} else {
			if info["contacts"] != nil {
				contacts = info["contacts"].(string)
			}
			if info["phone"] != nil {
				phone = info["phone"].(string)
			}
			if info["email"] != nil {
				email = info["email"].(string)
			}
			if info["address"] != nil {
				address = info["address"].(string)
			}
		}
		order := models.MedicineOrder{
			ID:                primitive.NewObjectID(),
			CustomerID:        customerOID,
			ProjectID:         projectOID,
			EnvironmentID:     envOID,
			SendID:            storeOID,
			ReceiveID:         siteOID,
			Status:            1,
			SortIndex:         10,
			Mode:              1,
			Medicines:         drugDataID,
			OtherMedicinesNew: otherDrugDataID,
			//OtherMedicines:   otherMedicinesCount,
			MedicinesPackage: medicinesPackage,
			OrderNumber:      orderNumber,
			Contacts:         contacts,
			Phone:            phone,
			Address:          address,
			Email:            email,
			Type:             1,
			Meta: models.Meta{
				CreatedAt: time.Duration(time.Now().Unix()),
				UpdatedAt: time.Duration(time.Now().Unix()),
			},
		}
		result, err := tools.Database.Collection("medicine_order").InsertOne(sctx, order)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		//插入轨迹
		err = insertOrderHistory(nil, sctx, order)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		//创建待运送的订单任务
		permissions := []string{"operation.supply.shipment.send", "operation.supply.shipment.lose", "operation.supply.shipment.receive", "operation.supply.shipment.close"}
		var siteOrStoreIDs = []primitive.ObjectID{order.ReceiveID}
		userIds, err := tools.GetPermissionUserIds(sctx, permissions, order.ProjectID, order.EnvironmentID, siteOrStoreIDs...)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		if len(userIds) > 0 {
			createWorkTask := models.WorkTask{
				ID:            primitive.NewObjectID(),
				CustomerID:    order.CustomerID,
				ProjectID:     order.ProjectID,
				EnvironmentID: order.EnvironmentID,
				//CohortID:      order.CohortID,
				UserIDs: userIds,
				Info: models.WorkTaskInfo{
					WorkType:        6,
					Status:          0,
					CreatedTime:     time.Duration(time.Now().Unix()),
					Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
					MedicineIDs:     []primitive.ObjectID{},
					MedicineOrderID: order.ID,
					DispensingID:    primitive.NilObjectID,
				},
			}
			_, err = tools.Database.Collection("work_task").InsertOne(sctx, createWorkTask)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
		}
		orderId = order.ID
		//更新研究产品状态(待发送)
		medicineFilter := bson.M{"_id": bson.M{"$in": drugDataID}}
		medicineOtherFilter := bson.M{"_id": bson.M{"$in": otherDrugDataID}}
		update := bson.M{
			"$set": bson.M{
				"status":        2,
				"order_id":      result.InsertedID,
				"site_id":       siteOID,
				"storehouse_id": nil,
			},
		}
		if len(drugDataID) > 0 {
			if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, medicineFilter, update); err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
		}

		if len(otherDrugDataID) > 0 {
			if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, medicineOtherFilter, update); err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
		}
		medicineData, blindMedicineData, err := getMedicineDataForMail(drugDataID, otherDrugDataID, envOID, packageIsOpen)
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		// 查询通知配置 是否需要邮件通知
		state, err := tools.MailCustomContentState(project["envId"].(primitive.ObjectID), "order.automatic_success_title")
		if err != nil {
			return primitive.NilObjectID, errors.WithStack(err)
		}
		if state {
			contentData := bson.M{"projectNumber": project["projectNumber"],
				"projectName":   project["projectName"],
				"envName":       project["envName"],
				"destination":   fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
				"start":         fmt.Sprintf("%s", info["sendName"]),
				"orderNumber":   orderNumber,
				"contacts":      contacts,
				"phone":         phone,
				"email":         email,
				"address":       address,
				"sendCompany":   info["storehouseName"],
				"results":       medicineData,
				"packageIsOpen": packageIsOpen,
			}
			blindContentData := bson.M{"projectNumber": project["projectNumber"],
				"projectName":   project["projectName"],
				"envName":       project["envName"],
				"destination":   fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
				"start":         fmt.Sprintf("%s", info["sendName"]),
				"orderNumber":   orderNumber,
				"contacts":      contacts,
				"phone":         phone,
				"email":         email,
				"address":       address,
				"sendCompany":   info["storehouseName"],
				"results":       blindMedicineData,
				"packageIsOpen": packageIsOpen,
			}

			mailBodyContet, err := tools.MailBodyContent(nil, envOID, "notice.medicine.order")
			for key, v := range mailBodyContet {
				contentData[key] = v
				blindContentData[key] = v
			}
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			bodyContentKeys, _, err := tools.MailCustomContent(&gin.Context{}, project["envId"].(primitive.ObjectID), "notice.medicine.order", contentData, nil)
			if err != nil {
				return primitive.NilObjectID, errors.WithStack(err)
			}
			//userMail := tools.GetRoleUsersMail(project["id"].(primitive.ObjectID), "notice.medicine.alarm", siteOID)
			var noticeConfig models.NoticeConfig
			err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": project["envId"].(primitive.ObjectID), "key": "notice.basic.settings"}).Decode(&noticeConfig)
			if err != nil && err != mongo.ErrNoDocuments {
				return primitive.NilObjectID, errors.WithStack(err)
			}

			langList := make([]string, 0)
			html := "medicine_order_new_zh_en.html"
			if noticeConfig.Automatic != 0 {
				if noticeConfig.Automatic == 1 {
					langList = append(langList, "zh")
					html = "medicine_order_new_zh.html"
				} else if noticeConfig.Automatic == 2 {
					langList = append(langList, "en")
					html = "medicine_order_new_en.html"
				} else if noticeConfig.Automatic == 3 {
					langList = append(langList, "zh")
					langList = append(langList, "en")
					html = "medicine_order_new_zh_en.html"
				}
			} else {
				langList = append(langList, "zh")
				langList = append(langList, "en")
			}
			for _, userEmail := range userMail {
				sendContentData := contentData
				if userEmail.IsBlind {
					sendContentData = blindContentData
				}
				var toUserMail []string
				toUserMail = append(toUserMail, userEmail.Email)
				*mail = append(*mail, models.Mail{
					ID:      primitive.NewObjectID(),
					Subject: "order.automatic_success_title",
					SubjectData: bson.M{
						"customer_id":     project["customerId"],
						"project_id":      project["id"],
						"env_id":          project["envId"],
						"project_site_id": info["site_id"],
						"orderNumber":     orderNumber,
						"projectNumber":   project["projectNumber"],
						"projectName":     project["projectName"],
						"envName":         project["envName"],
						"destination":     fmt.Sprintf("%s-%s", info["siteNumber"], info["siteName"]),
						//
						//"siteNumber":    info["siteNumber"],
						//"siteName":      info["siteName"],
					},
					Content:        "order.automatic_success",
					ContentData:    sendContentData,
					To:             toUserMail,
					Lang:           "en-US",
					LangList:       langList,
					Status:         0,
					CreatedTime:    time.Duration(time.Now().Unix()),
					ExpectedTime:   time.Duration(time.Now().Unix()),
					SendTime:       time.Duration(time.Now().Unix()),
					HTML:           html,
					BodyContentKey: bodyContentKeys,
				})
			}
		}

	}
	return orderId, nil
}

func getMedicineDataForMail(medicines []primitive.ObjectID, otherMedicines []primitive.ObjectID, envOID primitive.ObjectID, packageIsOpen bool) ([]map[string]interface{}, []map[string]interface{}, error) {
	var medicineData []map[string]interface{}
	var blindMedicineData []map[string]interface{}
	var medicineRes []map[string]interface{}
	var otherMedicineRes []map[string]interface{}
	if len(medicines) > 0 {
		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": medicines}}}},
			{{Key: "$group", Value: bson.M{
				"_id":    bson.M{"batch_number": "$batch_number", "expiration_date": "$expiration_date", "package_number": bson.M{"$ifNull": bson.A{"$package_number", ""}}},
				"number": bson.M{"$addToSet": "$number"},
			}}},
			{{Key: "$project", Value: bson.M{
				"batch_number":    "$_id.batch_number",
				"expiration_date": "$_id.expiration_date",
				"package_number":  "$_id.package_number",
				"number":          1,
			}}},
			{{Key: "$sort", Value: bson.D{{"expiration_date", 1}, {Key: "batch_number", Value: 1}}}},
			{{Key: "$group", Value: bson.M{
				"_id": bson.M{"batch_number": "$batch_number", "expiration_date": "$expiration_date"},
				"detail": bson.M{"$push": bson.M{
					"number":         "$number",
					"package_number": "$package_number",
				}},
			}}},
		})
		if err != nil {
			return []map[string]interface{}{}, []map[string]interface{}{}, errors.WithStack(err)
		}

		err = cursor.All(nil, &medicineRes)
		if err != nil {
			return []map[string]interface{}{}, []map[string]interface{}{}, errors.WithStack(err)
		}
	}

	for _, data := range medicineRes {

		var details []map[string]interface{}
		dataId := data["_id"].(map[string]interface{})
		for _, detail := range data["detail"].(primitive.A) {
			medicine := detail.(map[string]interface{})
			var medicines []string
			for _, item := range medicine["number"].(primitive.A) {
				medicines = append(medicines, item.(string))
			}
			sort.Sort(sort.StringSlice(medicines))
			details = append(details, map[string]interface{}{
				"number":        strings.Join(medicines, ","),
				"count":         len(medicines),
				"packageNumber": medicine["package_number"],
			})
		}
		resultData := map[string]interface{}{
			"batch":         dataId["batch_number"],
			"expiryDate":    dataId["expiration_date"],
			"rowsSpan":      len(data["detail"].(primitive.A)),
			"packageIsOpen": packageIsOpen,
			"details":       details,
		}
		medicineData = append(medicineData, resultData)
		blindMedicineData = append(blindMedicineData, resultData)

		//var medicines []string
		//for _, item := range medicine["number"].(primitive.A) {
		//	medicines = append(medicines, item.(string))
		//}
		//sort.Sort(sort.StringSlice(medicines))
		//medicineData = append(medicineData, map[string]interface{}{
		//	"number":     strings.Join(medicines, ","),
		//	"batch":      medicine["batch_number"],
		//	"count":      len(medicines),
		//	"expiryDate": medicine["expiration_date"],
		//})
		//blindMedicineData = append(blindMedicineData, map[string]interface{}{
		//	"number":     strings.Join(medicines, ","),
		//	"batch":      medicine["batch_number"],
		//	"count":      len(medicines),
		//	"expiryDate": medicine["expiration_date"],
		//})
	}

	if len(otherMedicines) > 0 {
		cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": otherMedicines}}}},
			{{Key: "$group", Value: bson.M{
				"_id":      bson.M{"name": "$name", "batch_number": "$batch_number", "expiration_date": "$expiration_date"},
				"useCount": bson.M{"$sum": 1},
			}}},
			{{Key: "$project", Value: bson.M{
				"batch_number":    "$_id.batch_number",
				"expiration_date": "$_id.expiration_date",
				//"package_number":  "$_id.package_number",
				"name":     "$_id.name",
				"useCount": 1,
			}}},
			{{Key: "$sort", Value: bson.D{{"expiration_date", 1}, {Key: "batch_number", Value: 1}}}},
		})
		if err != nil {
			return []map[string]interface{}{}, []map[string]interface{}{}, errors.WithStack(err)
		}

		err = cursor.All(nil, &otherMedicineRes)
		if err != nil {
			return []map[string]interface{}{}, []map[string]interface{}{}, errors.WithStack(err)
		}
	}

	for _, otherData := range otherMedicineRes {

		var details []map[string]interface{}
		details = append(details, map[string]interface{}{
			"number":        otherData["name"],
			"count":         otherData["useCount"],
			"packageNumber": nil,
		})
		medicineData = append(medicineData, map[string]interface{}{
			"number":        otherData["name"],
			"batch":         otherData["batch_number"],
			"expiryDate":    otherData["expiration_date"],
			"packageIsOpen": packageIsOpen,
			"rowsSpan":      1,
			"details":       details,
		})
		name := otherData["name"].(string)
		isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
		if isBlindedDrug {
			name = tools.BlindData
		}
		var blindDetails []map[string]interface{}
		blindDetails = append(blindDetails, map[string]interface{}{
			"number":        name,
			"count":         otherData["useCount"],
			"packageNumber": nil,
		})
		blindMedicineData = append(blindMedicineData, map[string]interface{}{
			"number":        name,
			"batch":         otherData["batch_number"],
			"expiryDate":    otherData["expiration_date"],
			"packageIsOpen": packageIsOpen,
			"rowsSpan":      1,
			"details":       blindDetails,
		})
	}

	// for _, otherMedicine := range otherMedicines {
	// 	var details []map[string]interface{}
	// 	details = append(details, map[string]interface{}{
	// 		"number":        otherMedicine.Name,
	// 		"count":         otherMedicine.UseCount,
	// 		"packageNumber": nil,
	// 	})
	// 	medicineData = append(medicineData, map[string]interface{}{
	// 		"number":        otherMedicine.Name,
	// 		"batch":         otherMedicine.Batch,
	// 		"expiryDate":    otherMedicine.ExpireDate,
	// 		"packageIsOpen": packageIsOpen,
	// 		"rowsSpan":      1,
	// 		"details":       details,
	// 	})
	// 	name := otherMedicine.Name
	// 	isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
	// 	if isBlindedDrug {
	// 		name = tools.BlindData
	// 	}
	// 	var blindDetails []map[string]interface{}
	// 	blindDetails = append(blindDetails, map[string]interface{}{
	// 		"number":        name,
	// 		"count":         otherMedicine.UseCount,
	// 		"packageNumber": nil,
	// 	})
	// 	blindMedicineData = append(blindMedicineData, map[string]interface{}{
	// 		"number":        name,
	// 		"batch":         otherMedicine.Batch,
	// 		"expiryDate":    otherMedicine.ExpireDate,
	// 		"packageIsOpen": packageIsOpen,
	// 		"rowsSpan":      1,
	// 		"details":       blindDetails,
	// 	})
	// }
	return medicineData, blindMedicineData, nil
}

func filterMail(sctx context.Context, mail []models.Mail) ([]models.Mail, error) {

	// 查询  大于等于当天0点 该项目中心 相同类型的 记录
	//currentDate := time.Now().UTC().Add(time.Hour * time.Duration(tools.GetTimeZone(projectOID))).Format("2006-01-02")
	//timeStr := time.Now().Format("2006-01-02")

	//t, _ := time.ParseInLocation("2006-01-02", currentDate, time.Local)
	//beginTimeNum := t.Unix()
	//for _, item := range mail {
	//	var alarmMedicineRecords models.AlarmMedicineRecords
	//	tools.Database.Collection("alarm_medicine_records").FindOne(nil, bson.M{""})
	//}

	var alarmMedicineRecords []models.AlarmMedicineRecords
	cursor, _ := tools.Database.Collection("alarm_medicine_records").Find(nil, bson.M{"created_time": bson.M{"$gte": time.Now().Add(-time.Hour * 24).Unix()}})
	err := cursor.All(nil, &alarmMedicineRecords)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(alarmMedicineRecords) == 0 {
		var doc []interface{}
		for _, item := range mail {
			if item.Subject != "order.automatic_success_title" {
				doc = append(doc, models.AlarmMedicineRecords{
					ID:            primitive.NewObjectID(),
					LastMailID:    item.ID,
					EnvID:         item.SubjectData["env_id"].(primitive.ObjectID),
					ProjectSiteID: item.SubjectData["project_site_id"].(primitive.ObjectID),
					Subject:       item.Subject,
					CreatedTime:   time.Duration(time.Now().Unix()),
				})
			}

		}

		if len(doc) > 0 {
			_, err := tools.Database.Collection("alarm_medicine_records").InsertMany(sctx, doc)
			if err != nil {
				return []models.Mail{}, errors.WithStack(err)

			}
		}
		return mail, nil
	}
	var MailArray []models.Mail
	var doc []interface{}
	for _, item := range mail {
		sign := true
		for _, record := range alarmMedicineRecords {
			if item.Subject != "order.automatic_success_title" && record.EnvID == item.SubjectData["env_id"] && record.ProjectSiteID == item.SubjectData["project_site_id"] && record.Subject == item.Subject {
				//sign = false
				timeZone, err := tools.GetTimeZone(item.SubjectData["project_id"].(primitive.ObjectID))
				if err != nil {
					return nil, errors.WithStack(err)
				}
				hour := time.Duration(timeZone)
				minute := time.Duration((timeZone - float64(hour)) * 60)
				duration := hour*time.Hour + minute*time.Minute
				recordDate := time.Unix(int64(record.CreatedTime), 0).UTC().Add(duration).Format("2006-01-02")
				date := time.Now().UTC().Add(duration).Format("2006-01-02")
				if recordDate == date {
					sign = false
				}
			}
		}
		if sign && item.Subject != "order.automatic_success_title" {
			doc = append(doc, models.AlarmMedicineRecords{
				ID:            primitive.NewObjectID(),
				EnvID:         item.SubjectData["env_id"].(primitive.ObjectID),
				LastMailID:    item.ID,
				ProjectSiteID: item.SubjectData["project_site_id"].(primitive.ObjectID),
				Subject:       item.Subject,
				CreatedTime:   time.Duration(time.Now().Unix()),
			})
			MailArray = append(MailArray, item)
		}
		if item.Subject == "order.automatic_success_title" {
			MailArray = append(MailArray, item)
		}
	}
	if len(doc) > 0 {
		_, err := tools.Database.Collection("alarm_medicine_records").InsertMany(sctx, doc)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return MailArray, nil
}

func getAppTaskNotice(envID primitive.ObjectID) ([]models.AppTaskNotice, error) {
	var appTaskNotices []models.AppTaskNotice
	now := time.Now()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	cursor, _ := tools.Database.Collection("app_task_notice").Find(nil, bson.M{
		"notice_time":             bson.M{"$gte": startOfDay.Unix()},
		"env_id":                  envID,
		"notice_type":             1,
		"expire_notice.work_type": 11})

	err := cursor.All(nil, &appTaskNotices)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	return appTaskNotices, nil
}

func getAllSupplyPlan(ctx context.Context, sctx context.Context, info map[string]interface{}, project map[string]interface{}) ([]map[string]interface{}, error) {
	status := []int{1, 2, 3}
	if project["is_freeze"] != nil && project["is_freeze"].(bool) {
		status = append(status, 4)
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"supply_plan_id": info["supply_plan_id"].(primitive.ObjectID)}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "supply_plan_id",
			"foreignField": "supply_plan_id",
			"as":           "project_site",
		}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$match", Value: bson.M{"project_site._id": info["site_id"]}}},
		{{Key: "$unwind", Value: "$project_site.storehouse_id"}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine",
			"let": bson.M{
				"site_id":          "$project_site._id",
				"medicine_name":    "$info.medicine_name",
				"un_provide_date":  "$info.un_provide_date",
				"not_counted_date": "$info.not_counted_date",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}},
				},
				bson.M{"$match": bson.M{"status": bson.M{"$in": status}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$name", "$$medicine_name"}}}},

				// 过滤 不发放 + 不计入天数的
				bson.M{"$project": bson.M{
					"_id":  0,
					"name": 1,
					"un_push_date": bson.M{
						"$dateToString": bson.M{
							"date": bson.M{
								"$add": bson.A{
									bson.M{"$toDate": ctx.Value("now")},
									bson.M{"$multiply": bson.A{86400000, bson.M{"$add": bson.A{bson.M{"$ifNull": bson.A{"$$not_counted_date", 0}}, bson.M{"$ifNull": bson.A{"$$un_provide_date", 0}}}}}},
								},
							},
							"format": "%Y-%m-%d",
						},
					},

					"expiration_date": 1,
				}},

				//bson.M{"$match": bson.M{"$expr": bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}}}},
				bson.M{"$match": bson.M{
					"$expr": bson.M{
						"$or": bson.A{
							bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}},
							bson.M{"$eq": bson.A{"$expiration_date", nil}},
							bson.M{"$eq": bson.A{"$expiration_date", ""}},
						},
					},
				}},
				bson.M{"$group": bson.M{"_id": "$site_id", "count": bson.M{"$sum": 1}}},
			},
			"as": "medicine",
		}}},

		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_others",
			"let": bson.M{
				"site_id":          "$project_site._id",
				"medicine_name":    "$info.medicine_name",
				"un_provide_date":  "$info.un_provide_date",
				"not_counted_date": "$info.not_counted_date",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$site_id", "$$site_id"}}},
				},
				bson.M{"$match": bson.M{"status": bson.M{"$in": status}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$name", "$$medicine_name"}}}},

				// 过滤 不发放 + 不计入天数的
				bson.M{"$project": bson.M{
					"_id":  0,
					"name": 1,
					"un_push_date": bson.M{
						"$dateToString": bson.M{
							"date": bson.M{
								"$add": bson.A{
									bson.M{"$toDate": ctx.Value("now")},
									bson.M{"$multiply": bson.A{86400000, bson.M{"$add": bson.A{bson.M{"$ifNull": bson.A{"$$not_counted_date", 0}}, bson.M{"$ifNull": bson.A{"$$un_provide_date", 0}}}}}},
								},
							},
							"format": "%Y-%m-%d",
						},
					},

					"expiration_date": 1,
				}},

				//bson.M{"$match": bson.M{"$expr": bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}}}},
				bson.M{"$match": bson.M{
					"$expr": bson.M{
						"$or": bson.A{
							bson.M{"$gt": bson.A{"$expiration_date", "$un_push_date"}},
							bson.M{"$eq": bson.A{"$expiration_date", nil}},
							bson.M{"$eq": bson.A{"$expiration_date", ""}},
						},
					},
				}},
				bson.M{"$group": bson.M{"_id": "$site_id", "count": bson.M{"$sum": 1}}},
			},
			"as": "medicine_other_institute",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$medicine", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$medicine_other_institute", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"buffer":                   "$info.buffer",
			"warning":                  "$info.warning",
			"auto_supply_size":         "$info.auto_supply_size",
			"forecast_min":             "$info.forecast_min",
			"forecast_max":             "$info.forecast_max",
			"second_supply":            "$info.second_supply",
			"name":                     "$info.medicine_name",
			"un_distribution_date":     "$info.un_distribution_date",
			"not_counted_date":         "$info.not_counted_date",
			"medicine":                 1,
			"medicine_other_institute": 1,
			"customer_id":              1,
			"project_id":               1,
			"env_id":                   1,
			"storehouse_id":            "$project_site.storehouse_id",
			"site_id":                  "$project_site._id",
		}}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("supply_plan_medicine").Aggregate(sctx, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return data, nil
}

// generationForecast
func generationForecast(sctx mongo.SessionContext, medicine map[string]interface{}) (int, int, error) {
	maxSupply := 0
	minForecacst := 0
	minDay := medicine["forecast_min"].(int32)
	maxDay := medicine["forecast_max"].(int32)

	// 查询有环境 或者cohort下的访视计划、 受试者

	// 查询未来最低预测的量，预测最高使用的量
	var attributes []models.Attribute
	cursor, err := tools.Database.Collection("attribute").Find(sctx, bson.M{"env_id": medicine["env_id"]})
	if err != nil {
		return 0, 0, err
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return 0, 0, errors.WithStack(err)
	}
	/*
		计算单个受试者下 未来最低预测的量，预测最高使用的量 既有多少个访视需要发药
		for cohort
			if visit_type == baseline
				计算单个受试者下 未来最低预测的量，预测最高使用的量 既有多少个访视需要发药
				根据配置查询这个药要发到哪个访视 筛选 这个药配在哪几个访视上
			else
				获取每个受试者的发药数据获取最新的一条发过药的访视
	*/

	// 7064
	subjectMap := make(map[string]models.Subject)

	var visitCycles []models.VisitCycle

	cursor, err = tools.Database.Collection("visit_cycle").Find(sctx, bson.M{"env_id": medicine["env_id"].(primitive.ObjectID)})
	if err != nil {
		return 0, 0, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return 0, 0, errors.WithStack(err)
	}
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")

		}
	}
	for _, attribute := range attributes {

		// 是否需要计算揭盲的

		// 是否需要计算pv揭盲

		var subjectDispensing []models.SubjectDispensing
		//var subjectDispensingmap []map[string]interface{}

		var drugConfigure models.DrugConfigure
		visitCycleP, _ := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
			return item.CohortID == attribute.CohortID
		})
		visitCycle := *visitCycleP
		// 查询药物在各个访视最大发药量
		err = tools.Database.Collection("drug_configure").FindOne(sctx, bson.M{"env_id": attribute.EnvironmentID, "cohort_id": attribute.CohortID}).Decode(&drugConfigure)
		if err != nil && err != mongo.ErrNoDocuments {
			return 0, 0, errors.WithStack(err)
		}

		// 获取每个访视在各个组别中发的药
		type VisitDispensingNumber struct {
			visitNumber      string
			DispensingNumber int
		}
		// 组别-访视-最大发药量、组别
		medicineCount, _ := getGroupAndVisitGroup(drugConfigure, medicine)

		subjectDispensing, err = GetSubjectDispensing(sctx, attribute, medicine["site_id"].(primitive.ObjectID))
		if err != nil {
			return 0, 0, errors.WithStack(err)
		}

		if visitCycle.VisitType == 0 {
			for _, subject := range subjectDispensing {
				// 以随机为基线
				if subject.Group == "" {
					subject.Group = "N/A"
				}
				if attribute.AttributeInfo.Random && subject.RegisterGroup != "" {
					subject.Group = subject.RegisterGroup
				}
				// TODO 7064
				if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
					subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
				}

				firstTime := time.Duration(0)
				for _, resDispensing := range subject.Dispensing {
					visitCycleInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
						return item.Number == resDispensing.VisitInfo.Number
					})
					if ok {
						visitCycleInfo := *visitCycleInfoP
						if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleInfo.Interval != nil && subject.RandomTime == 0 {
							firstTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfo.Interval)).Unix())
							subject.RandomTime = firstTime
							break
						}
					}

				}

				countForecastBaseLine(drugConfigure.Configures, medicine["name"].(string), medicineCount, subject, subject.RandomTime, visitCycle, int(minDay), int(maxDay), &minForecacst, &maxSupply, attribute, subject.JoinTime)
			}
		} else {
			for _, dispensing := range subjectDispensing {
				// 获取最新的一次发药时间
				err := slice.SortByField(dispensing.Dispensing, "DispensingTime", "desc")
				if err != nil {
					return 0, 0, errors.WithStack(err)
				}
				if dispensing.Dispensing[0].DispensingTime == 0 { // 没发过药
					continue
				}
				if dispensing.Group == "" {
					dispensing.Group = "N/A"
				}
				countForecast(medicineCount, dispensing, dispensing.Dispensing[0].DispensingTime, visitCycle, int(minDay), int(maxDay), &minForecacst, &maxSupply)

			}
		}

	}

	return maxSupply, minForecacst, errors.WithStack(err)
}

func countForecast(medicineCount map[string]map[string]int, dispensing models.SubjectDispensing, dispensingTime time.Duration, visitCycle models.VisitCycle, minDay int, maxDay int, minForecacst *int, maxSupply *int) {
	betweenHours := getHoursBetween(time.Now().Unix(), int64(dispensingTime))

	totalMin := betweenHours + (minDay * 24)
	totalMax := betweenHours + (maxDay * 24)
	baseInt := float64(0)
	for _, info := range visitCycle.Infos {
		// 当前受试者是否有这个访视，是否发过药
		_, ok := slice.Find(dispensing.Dispensing, func(index int, item models.Dispensing) bool {
			return info.Number == item.VisitInfo.Number && item.Status == 1
		})
		if ok {
			Unit, Interval, PeriodMin := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)

			addTime := tools.ConvertTime(Unit, Interval, PeriodMin) + baseInt
			// 非小时制的转成小时
			if addTime <= float64(totalMin) { // 访视在最小窗口期内
				*minForecacst += medicineCount[dispensing.Group][info.ID.Hex()]
			}
			if addTime <= float64(totalMax) { // 访视在最大窗口期内
				*maxSupply += medicineCount[dispensing.Group][info.ID.Hex()]
			}

			baseInt = baseInt + tools.ConvertTime(Unit, Interval, 0)
		}
	}
}

func countForecastBaseLine(configure []models.DrugConfigureInfo, medicineName string, medicineCount map[string]map[string]int, dispensing models.SubjectDispensing, dispensingTime time.Duration, visitCycle models.VisitCycle, minDay int, maxDay int, minForecacst *int, maxSupply *int, attribute models.Attribute, joinTime string) {
	if dispensingTime == 0 {
		return
	}
	betweenHours := getHoursBetween(time.Now().Unix(), int64(dispensingTime))

	if !attribute.AttributeInfo.Random && joinTime != "" {
		parse, _ := time.Parse("2006-01-02", joinTime)
		betweenHours = getHoursBetween(time.Now().Unix(), parse.Unix())
	}

	var w *float64
	var h *float64
	var age *string

	_ = slice.SortByField(dispensing.Dispensing, "DispensingTime")
	for _, d := range dispensing.Dispensing {
		if d.FormulaInfo.Weight != nil {
			w = d.FormulaInfo.Weight
		}
		if d.FormulaInfo.Height != nil {
			h = d.FormulaInfo.Height
		}
		if d.FormulaInfo.Height != nil {
			age = d.FormulaInfo.Age
		}
	}

	totalMin := betweenHours + (minDay * 24)
	totalMax := betweenHours + (maxDay * 24)
	for _, info := range visitCycle.Infos {
		// 当前受试者是否有这个访视，是否发过药
		_, ok := slice.Find(dispensing.Dispensing, func(index int, item models.Dispensing) bool {
			return info.Number == item.VisitInfo.Number && item.Status == 1
		})
		if ok {
			if info.Interval == nil && dispensing.Group == "N/A" {
				continue
			}
			// 非小时制的转成小时
			Unit, Interval, PeriodMin := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)
			converTimes := tools.ConvertTime(Unit, Interval, PeriodMin)
			if !info.Random && converTimes == 0 {
				continue
			}
			if converTimes <= float64(totalMin) { // 访视在最小窗口期内
				*minForecacst += medicineCount[dispensing.Group][info.ID.Hex()]
				if medicineCount[dispensing.Group][info.ID.Hex()] == 0 { // dispensingNumber == 0 则是公式计算
					forest, _ := formulaForest(configure, dispensing.Group, medicineName, info.ID, age, w, h)
					*minForecacst += forest
				}
			}
			if converTimes <= float64(totalMax) { // 访视在最大窗口期内
				*maxSupply += medicineCount[dispensing.Group][info.ID.Hex()]
				if medicineCount[dispensing.Group][info.ID.Hex()] == 0 { // dispensingNumber == 0 则是公式计算
					forest, _ := formulaForest(configure, dispensing.Group, medicineName, info.ID, age, w, h)
					*maxSupply += forest
				}
			}
		}
	}
}
func ReturnUnitIntervalPeriod(unit *string, interval *int, period *float64) (string, int, float64) {
	unitData := "d"
	intervalData := 0
	periodData := float64(0)
	if unit != nil {
		unitData = *unit
	}
	if interval != nil {
		intervalData = *interval
	}
	if period != nil {
		periodData = *period
	}
	return unitData, intervalData, periodData
}

func getGroupAndVisitGroup(drugConfigure models.DrugConfigure, medicine map[string]interface{}) (map[string]map[string]int, []string) {
	// 查询组别-访视-最大发药量
	medicineCount := map[string]map[string]int{}
	// 过滤访视
	for _, configure := range drugConfigure.Configures {

		for _, value := range configure.Values {
			if value.DrugName == medicine["name"].(string) {
				for _, cycle := range configure.VisitCycles {
					if configure.OpenSetting == 3 {
						medicineCount[configure.Group] = map[string]int{cycle.Hex(): 0}
						continue
					}
					if medicineCount[configure.Group] != nil {
						if medicineCount[configure.Group][cycle.Hex()] < value.DispensingNumber {
							medicineCount[configure.Group][cycle.Hex()] = value.DispensingNumber //组别-访视-最大发药量
						}
					} else {
						medicineCount[configure.Group] = map[string]int{cycle.Hex(): value.DispensingNumber}
					}
				}
			}
		}
	}
	groups := []string{}
	for key := range medicineCount {
		if key != "N/A" {
			groups = append(groups, key)
		}
	}
	return medicineCount, groups
}

func getHoursBetween(timestamp1, timestamp2 int64) int {
	t1 := time.Unix(timestamp1, 0)
	t2 := time.Unix(timestamp2, 0)

	duration := t1.Sub(t2)
	hours := int(duration.Hours())
	return hours
}

func returnPageMedicine(sctx mongo.SessionContext, match bson.M, meCount *int, isPageNumber int) ([]models.Medicine, error) {
	var drugData []map[string]interface{}
	var medicines []models.Medicine
	pageCount := int(math.Ceil(float64(*meCount) / float64(isPageNumber)))
	count := pageCount * isPageNumber
	*meCount = count
	medicinePipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		models.MedicineProject,
		{{Key: "$group", Value: bson.M{"_id": bson.M{"name": "$name", "package_number": "$package_number", "expiration_date": "$expiration_date"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$addToSet": bson.M{"_id": "$_id", "serial_number": "$serial_number"}}}}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", isPageNumber}}}}},
		{{"$unwind", "$ids"}},
		{{Key: "$sort", Value: bson.D{{"_id.expiration_date", 1}, {"ids.serial_number", 1}}}},
		{{Key: "$limit", Value: count}},
	}

	cursor, err := tools.Database.Collection("medicine").Aggregate(sctx, medicinePipeline)
	if err != nil {
		return medicines, errors.WithStack(err)
	}
	err = cursor.All(nil, &drugData)
	if err != nil {
		return medicines, errors.WithStack(err)
	}
	for _, drug := range drugData {
		var medicine models.Medicine
		item := drug["ids"].(map[string]interface{})
		medicine.ID = item["_id"].(primitive.ObjectID)
		medicine.Name = drug["_id"].(map[string]interface{})["name"].(string)
		medicines = append(medicines, medicine)
	}
	return medicines, nil
}

func AlarmOrderMail(project, info map[string]interface{}, userMail []tools.RoleTypeEmail, mail *[]models.Mail) error {
	state, err := tools.MailCustomContentState(project["envId"].(primitive.ObjectID), "order.automatic_alarm_title")
	if err != nil {
		return errors.WithStack(err)
	}
	if state {
		for _, userEmail := range userMail {
			var noticeConfig models.NoticeConfig
			err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": project["envId"].(primitive.ObjectID), "key": "notice.basic.settings"}).Decode(&noticeConfig)
			if err != nil && err != mongo.ErrNoDocuments {
				return errors.WithStack(err)
			}
			langList := make([]string, 0)
			html := "medicine_alarm.html"
			if noticeConfig.Automatic != 0 {
				if noticeConfig.Automatic == 1 {
					langList = append(langList, "zh")
					html = "medicine_alarm_zh.html"
				} else if noticeConfig.Automatic == 2 {
					langList = append(langList, "en")
					html = "medicine_alarm_en.html"
				} else if noticeConfig.Automatic == 3 {
					langList = append(langList, "zh")
					langList = append(langList, "en")
					html = "medicine_alarm.html"
				}
			} else {
				langList = append(langList, "zh")
				langList = append(langList, "en")
			}
			subjectData := bson.M{
				"projectNumber":   project["projectNumber"],
				"envName":         project["envName"],
				"customer_id":     project["customerId"],
				"project_id":      project["id"],
				"env_id":          project["envId"],
				"project_site_id": info["site_id"],
			}
			contentData := bson.M{
				"projectNumber": project["projectNumber"],
				"projectName":   project["projectName"],
				"envName":       project["envName"],
				"siteNumber":    info["siteNumber"],
				"siteName":      info["siteName"],
			}
			mailBodyContet, err := tools.MailBodyContent(nil, project["envId"].(primitive.ObjectID), "notice.medicine.order")
			for key, v := range mailBodyContet {
				contentData[key] = v
			}
			if err != nil {
				return errors.WithStack(err)
			}
			var toUserMail []string
			toUserMail = append(toUserMail, userEmail.Email)
			*mail = append(*mail, models.Mail{
				ID:           primitive.NewObjectID(),
				Subject:      "order.automatic_alarm_title",
				SubjectData:  subjectData,
				ContentData:  contentData,
				To:           toUserMail,
				Lang:         "en-US",
				LangList:     langList,
				Status:       0,
				CreatedTime:  time.Duration(time.Now().Unix()),
				ExpectedTime: time.Duration(time.Now().Unix()),
				SendTime:     time.Duration(time.Now().Unix()),
				HTML:         html,
			})
		}

	}
	return nil
}

func insertOrderHistory(ctx *gin.Context, sctx mongo.SessionContext, order models.MedicineOrder) error {
	//
	//for _, medicineIDs := range order.OtherMedicinesNew {
	//
	//}
	//for _, medicineIDs := range order.Medicines {
	//
	//}

	_, err := tools.Database.Collection("history").InsertOne(sctx, models.History{
		Key:  "history.order.confrim-new",
		OID:  order.ID,
		Time: time.Duration(time.Now().Unix()),
		Data: map[string]interface{}{"orderNumber": order.OrderNumber},
		User: "System（Automatic Order）",
		UID:  primitive.NilObjectID,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func formulaForest(drugConfigures []models.DrugConfigureInfo, group, name string, cycleID primitive.ObjectID, age *string, weight, height *float64) (int, error) {
	// 过滤访视
	count := 0
	for _, configure := range drugConfigures {
		for _, value := range configure.Values {
			if value.DrugName == name && configure.Group == group {
				for _, cycle := range configure.VisitCycles {
					if cycle == cycleID { // 组别 - 访视 - 药物名称 匹配上
						// 公式计算
						countTemp, err := formulaCount(configure, value, weight, height, age)
						if err != nil {
							return 0, errors.WithStack(err)
						}
						if count < countTemp {
							count = countTemp
						}
					}
				}
			}
		}
	}
	return count, nil

}

func formulaCount(configure models.DrugConfigureInfo, value models.DrugValue, weight *float64, height *float64, age *string) (int, error) {
	_, number, err := tools.Formula(nil, configure, value, age, weight, height)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return number, nil
}

func GetPageMedicine(sctx mongo.SessionContext, filterUsedID []primitive.ObjectID, envID, storehouseID primitive.ObjectID, medicinePackage *[]models.MedicinePackage, drugPackageCount map[string]models.DrugPackageCount, packageAllCount map[string]int, mixPackageConfig map[string][]string) ([]primitive.ObjectID, bool, error) {

	pageMedicine := []primitive.ObjectID{}

	generateName := map[string]struct{}{}

Loop:
	for name, value := range drugPackageCount {
		if value.Other {
			continue
		}
		mixPackage, _ := mixPackageConfig[name]
		for _, key := range mixPackage {
			if _, ok := generateName[key]; ok {
				continue Loop
			}
			generateName[key] = struct{}{}
		}
		drugData := []map[string]interface{}{}
		packageFilter := bson.M{
			"env_id":         envID,
			"name":           bson.M{"$in": mixPackage},
			"status":         1,
			"package_number": bson.M{"$nin": [2]interface{}{nil, ""}},
			"storehouse_id":  storehouseID,
			//"expiration_date": bson.M{"$gt": value.UnDistributionDate},
			"$or": []bson.M{
				bson.M{"expiration_date": bson.M{"$gt": value.UnDistributionDate}},
				bson.M{"expiration_date": nil},
				bson.M{"expiration_date": ""},
			},
		}
		if len(filterUsedID) > 0 {
			packageFilter["_id"] = bson.M{"$nin": filterUsedID}
		}
		pipepine := mongo.Pipeline{
			{{Key: "$match", Value: packageFilter}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number", "expiration_date": "$expiration_date", "package_serial_number": "$package_serial_number"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$addToSet": "$_id"}}}},
			{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageAllCount[name]}}}}},
			{{Key: "$sort", Value: bson.D{{"_id.expiration_date", 1}, {"_id.package_serial_number", 1}}}},
			{{Key: "$limit", Value: value.Count}},
		}
		cursor, err := tools.Database.Collection("medicine").Aggregate(sctx, pipepine)
		if err != nil {
			return nil, false, errors.WithStack(err)
		}
		err = cursor.All(sctx, &drugData)
		if err != nil {
			return nil, false, errors.WithStack(err)
		}
		if len(drugData) < value.Count {
			return nil, false, err
		}

		for _, drug := range drugData {
			drugs := drug["ids"].(primitive.A)
			for _, id := range drugs {
				pageMedicine = append(pageMedicine, id.(primitive.ObjectID))
			}
		}
		for _, tmpName := range mixPackage {
			_, ok := slice.Find(*medicinePackage, func(index int, item models.MedicinePackage) bool {
				return item.Name == tmpName
			})
			if !ok {
				*medicinePackage = append(*medicinePackage, models.MedicinePackage{
					Name:          tmpName,
					PackageMethod: true,
					PackageNumber: packageAllCount[name],
				})
			}
		}

	}
	return pageMedicine, true, nil

}

func GetPageOtherMedicine(sctx mongo.SessionContext, envID, storehouseID primitive.ObjectID, medicinePackage *[]models.MedicinePackage, drugPackageCount map[string]models.DrugPackageCount, packageAllCount map[string]int, mixPackageConfig map[string][]string) ([]primitive.ObjectID, bool, error) {

	pageMedicine := []primitive.ObjectID{}

	generateName := map[string]struct{}{}

Loop:
	for name, value := range drugPackageCount {
		if !value.Other {
			continue
		}
		mixPackage, _ := mixPackageConfig[name]
		for _, key := range mixPackage {
			if _, ok := generateName[key]; ok {
				continue Loop
			}
			generateName[key] = struct{}{}
		}
		drugData := []map[string]interface{}{}
		packageFilter := bson.M{
			"env_id":         envID,
			"name":           bson.M{"$in": mixPackage},
			"status":         1,
			"package_number": bson.M{"$nin": [2]interface{}{nil, ""}},
			"storehouse_id":  storehouseID,
			//"expiration_date": bson.M{"$gt": value.UnDistributionDate},
			"$or": []bson.M{
				bson.M{"expiration_date": bson.M{"$gt": value.UnDistributionDate}},
				bson.M{"expiration_date": nil},
				bson.M{"expiration_date": ""},
			},
		}

		pipepine := mongo.Pipeline{
			{{Key: "$match", Value: packageFilter}},
			{{Key: "$group", Value: bson.M{"_id": bson.M{"package_number": "$package_number", "expiration_date": "$expiration_date", "package_serial_number": "$package_serial_number"}, "availableCount": bson.M{"$sum": 1}, "ids": bson.M{"$addToSet": "$_id"}}}},
			{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$availableCount", packageAllCount[name]}}}}},
			{{Key: "$sort", Value: bson.D{{"_id.expiration_date", 1}, {"_id.package_serial_number", 1}}}},
			{{Key: "$limit", Value: value.Count}},
		}
		cursor, err := tools.Database.Collection("medicine_others").Aggregate(sctx, pipepine)
		if err != nil {
			return nil, false, errors.WithStack(err)
		}
		err = cursor.All(sctx, &drugData)
		if err != nil {
			return nil, false, errors.WithStack(err)
		}
		if len(drugData) < value.Count {
			return nil, false, err
		}

		for _, drug := range drugData {
			drugs := drug["ids"].(primitive.A)
			for _, id := range drugs {
				pageMedicine = append(pageMedicine, id.(primitive.ObjectID))
			}
		}
		for _, tmpName := range mixPackage {
			_, ok := slice.Find(*medicinePackage, func(index int, item models.MedicinePackage) bool {
				return item.Name == tmpName
			})
			if !ok {
				*medicinePackage = append(*medicinePackage, models.MedicinePackage{
					Name:          tmpName,
					PackageMethod: true,
					PackageNumber: packageAllCount[name],
				})
			}
		}

	}
	return pageMedicine, true, nil

}

func getOtherMedicineName(envOID primitive.ObjectID) (map[string]bool, error) {
	nameMap := map[string]bool{}
	var drugConfigure []models.DrugConfigure
	cursor, err := tools.Database.Collection("drug_configure").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nameMap, err
	}
	err = cursor.All(nil, &drugConfigure)
	if err != nil {
		return nameMap, errors.WithStack(err)
	}
	for _, item := range drugConfigure {
		for _, config := range item.Configures {
			for _, value := range config.Values {
				if value.IsOther {
					nameMap[value.DrugName] = true
				}
			}
		}
	}
	return nameMap, nil
}

func getStoreNumberMedicine(sctx mongo.SessionContext, medicineID *[]primitive.ObjectID, item map[string]interface{}, date string, drugPackageCount map[string]models.DrugPackageCount, needPageCount bool, meCount, isPageNumber int) (bool, error) {
	create := true
	var drugData []models.Medicine
	queryFilter := bson.M{
		"project_id":    item["project_id"],
		"env_id":        item["env_id"],
		"storehouse_id": item["storehouse_id"],
		"name":          item["name"].(string),
		//"expiration_date": bson.M{"$gt": date},
		"$or": []bson.M{
			bson.M{"expiration_date": bson.M{"$gt": date}},
			bson.M{"expiration_date": nil},
			bson.M{"expiration_date": ""},
		},
		"status": 1,
	}
	if needPageCount {
		drugPackageCount[item["name"].(string)] = models.DrugPackageCount{
			Count:              int(math.Ceil(float64(meCount) / float64(isPageNumber))),
			UnDistributionDate: date,
		}
	} else {
		medicinePipeline := mongo.Pipeline{
			{{Key: "$match", Value: queryFilter}},
			models.MedicineQueryProject,
			models.MedicineSort,
			{{Key: "$limit", Value: meCount}},
		}

		cursor, err := tools.Database.Collection("medicine").Aggregate(sctx, medicinePipeline)
		if err != nil {
			return false, errors.WithStack(err)
		}

		err = cursor.All(nil, &drugData)
		if err != nil {
			return false, errors.WithStack(err)
		}
		if meCount != len(drugData) {
			create = false
		}
		if create {
			for _, medicine := range drugData {
				*medicineID = append(*medicineID, medicine.ID)
			}
		}

	}
	return create, nil
}
