package task

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// OrderTimeOutReminder ..

type Storehouse struct {
	models.ProjectStorehouse ` bson:",inline"`
	Storehouse               models.Storehouse `bson:"storehouse"`
}
type Project struct {
	ProjectInfo  models.ProjectInfo `json:"info" bson:"info"`
	Environments models.Environment `json:"envs" bson:"envs"`
}
type dataStruct struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	OrderNumber   string             `json:"orderNumber" bson:"order_number"`
	SendID        primitive.ObjectID `json:"sendId" bson:"send_id"`
	ReceiveID     primitive.ObjectID `json:"receiveId" bson:"receive_id"`
	Type          int                `json:"type" bson:"type"`     //1:仓库->中心、2:中心->中心、3:仓库->仓库、4:中心->仓库    5 仓库 ->受试者    6 中心-> 受试者
	Status        int                `json:"status" bson:"status"` //1:已确认、2:已运送、3:已接收、4:已丢失、5:已取消 6:待确认 7:已申请  8:已终止 9:已关闭
	// Project           Project              `bson:"project"`
	// ProjectSite       []models.ProjectSite `bson:"project_site"`
	// ProjectStorehouse []Storehouse         `bson:"project_storehouse"`
	//Subject     models.Subject     `bson:"subject"`
	//Dispensing  models.Dispensing  `bson:"dispensing"`
	UpdatedAt    time.Duration      `bson:"updated_at"`
	CreatedAt    time.Duration      `bson:"created_at"`
	TimeoutDays  int32              `bson:"timeout_days"`
	SendDays     int32              `bson:"send_days"`
	History      models.History     `bson:"history"`
	SubjectID    primitive.ObjectID `json:"subjectId" bson:"subject_id"`
	DispensingID primitive.ObjectID `json:"dispensingId" bson:"dispensing_id"`
}

func OrderTimeOutReminder() error {
	defer tools.DeferReturn("OrderTimeOutReminder")
	hours, _ := time.ParseDuration("23h59m")

	yesDate := time.Now().Add(-hours).Unix()
	nowTime := time.Now()
	now := nowTime.Unix()
	//date := nowTime.UTC().Format("15:04")

	pipeline := mongo.Pipeline{
		// 查询超过24小时未发送通知，状态为1、2的订单
		{{Key: "$match", Value: bson.M{"status": bson.M{"$in": bson.A{1, 2}},
			"$or": bson.A{
				bson.M{"last_reminder_date": bson.M{"$lt": yesDate}},
				bson.M{"last_reminder_date": bson.M{"$exists": false}},
			},
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "notice_config",
			"let": bson.M{
				"env_id": "$env_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}, "key": "notice.order.timeout",
						"$or": bson.A{
							bson.M{"timeout_days": bson.M{"$gt": 0}},
							bson.M{"send_days": bson.M{"$gt": 0}}},
					},
				},
				bson.M{"$project": bson.M{
					"timeout_days": bson.M{"$multiply": bson.A{"$timeout_days", 24, 3600}},
					"send_days":    bson.M{"$multiply": bson.A{"$send_days", 24, 3600}},
				}},
			},
			"as": "notice_config",
		}}},
		{{Key: "$match", Value: bson.M{"notice_config.0": bson.M{"$exists": true}}}},
		{{Key: "$project", Value: bson.M{
			"_id":           1,
			"customer_id":   1,
			"project_id":    1,
			"env_id":        1,
			"send_id":       1,
			"receive_id":    1,
			"order_number":  1,
			"type":          1,
			"subject_id":    1,
			"updated_at":    "$meta.updated_at",
			"created_at":    "$meta.created_at",
			"status":        1,
			"notice_config": bson.M{"$first": "$notice_config"},
		}}},
		{{Key: "$project", Value: bson.M{
			"_id":          1,
			"customer_id":  1,
			"project_id":   1,
			"env_id":       1,
			"send_id":      1,
			"receive_id":   1,
			"order_number": 1,
			"type":         1,
			"subject_id":   1,
			"updated_at":   1,
			"created_at":   1,
			"status":       1,
			"timeout_days": bson.M{"$ifNull": bson.A{"$notice_config.timeout_days", 0}},
			"send_days":    bson.M{"$ifNull": bson.A{"$notice_config.send_days", 0}},
		}}},
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		cursor, err := tools.Database.Collection("medicine_order").Aggregate(sctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var data []dataStruct
		//var datas []map[string]interface{}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//过滤配置了过期天数的数据，并且按照环境分组
		envGroup := make(map[primitive.ObjectID][]dataStruct)
		for _, medicineOrder := range data {
			if medicineOrder.Status == 1 && medicineOrder.TimeoutDays == 0 { // 确认超时时间没有配置 确认订单不发超时订单
				continue
			}
			group, exists := envGroup[medicineOrder.EnvironmentID]
			if !exists {
				group = []dataStruct{medicineOrder}
			} else {
				group = append(group, medicineOrder)
			}
			envGroup[medicineOrder.EnvironmentID] = group
		}

		var updateID []primitive.ObjectID
		// app任务通知
		var appTaskNotice []interface{}

		projectMap := make(map[primitive.ObjectID]models.Project)
		for envID, envGroupData := range envGroup {
			var env models.Environment
			//查询环境下的项目信息、，环境信息、仓库、中心、受试者信息、发药信息
			projectID := envGroupData[0].ProjectID
			var project models.Project
			project, exists := projectMap[projectID]
			if !exists {
				_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectID}).Decode(&project)
				projectMap[projectID] = project
			}

			for _, environment := range project.Environments {
				if environment.ID == envID {
					env = environment
					break
				}
			}

			match := bson.M{
				"project_id": projectID,
				"env_id":     envID,
			}
			var projectSites []models.ProjectSite
			cursor, err := tools.Database.Collection("project_site").Find(nil, match)
			if err != nil {
				return nil, errors.WithStack(err)

			}
			if err := cursor.All(nil, &projectSites); err != nil {
				return nil, errors.WithStack(err)
			}

			var projectStorehouses []Storehouse
			cursor, err = tools.Database.Collection("project_storehouse").Aggregate(nil, mongo.Pipeline{
				{{"$match", match}},
				{{"$lookup", bson.M{"from": "storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "storehouse"}}},
				{{"$unwind", "$storehouse"}},
			})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &projectStorehouses)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			for _, item := range envGroupData {

				//baseTime := item.CreatedAt
				//
				//// 计算超时时间点
				//var timeOutDay int64
				//if item.Status == 1 {
				//	timeOutDay = time.Unix(int64(item.CreatedAt), 0).Add(time.Duration(item.TimeoutDays)).Unix()
				//}
				//if item.SendDays != 0 {
				//	baseTime = item.UpdatedAt
				//	timeOutDay = time.Unix(int64(item.UpdatedAt), 0).Add(time.Duration(item.SendDays)).Unix()
				//} else {
				//	timeOutDay = time.Unix(int64(item.CreatedAt), 0).Add(time.Duration(item.TimeoutDays)).Unix()
				//}
				//
				baseTime := item.CreatedAt

				if item.UpdatedAt != 0 { // 确认时间 || 运送时间
					baseTime = item.UpdatedAt
				}
				// 计算超时时间点
				var timeOutDay int64
				if item.Status == 1 {
					timeOutDay = int64(baseTime) + int64(item.TimeoutDays)
				}
				if item.Status == 2 {
					if item.SendDays != 0 { //运送起时间配置    使用运送时间计算
						timeOutDay = int64(baseTime) + int64(item.SendDays)
					} else { // 运送起时间没配置    使用确认时间计算
						//
						history := models.History{}
						err := tools.Database.Collection("history").FindOne(nil, bson.M{"oid": item.ID,
							"key": "history.order.confrim-new",
						}).Decode(&history)
						if err != nil && err != mongo.ErrNoDocuments {
							return nil, errors.WithStack(err)
						}
						if history.Time == 0 { //自动订单运送  没有确认轨迹    使用创建时间
							baseTime = item.CreatedAt
							timeOutDay = int64(item.CreatedAt) + int64(item.TimeoutDays)
						} else { //订单运送 有确认轨迹 使用轨迹时间
							baseTime = history.Time
							timeOutDay = int64(history.Time) + int64(item.TimeoutDays)
						}
					}
				}

				// 判断是否在超时时间范围内
				if now < timeOutDay {
					continue
				}

				orderDate := time.Unix(int64(baseTime), 0).UTC().Format("15:04")
				format, _ := time.ParseDuration("5m")
				updated_at := nowTime.UTC().Format("15:04")
				updatedAddOne := nowTime.UTC().Add(format).Format("15:04")
				// 判断订单是在这个时间区间内过期
				if !(updated_at <= orderDate && updatedAddOne >= orderDate) {
					continue
				}
				//项目动态
				{
					typeTran := "project_dynamics_type_overtime"
					if item.Type == 3 || item.Type == 4 {
						typeTran = "project_dynamics_type_overtime_recovery"
					}
					dynamics := models.ProjectDynamics{
						ID:          primitive.NewObjectID(),
						Operator:    primitive.NilObjectID,
						OID:         item.EnvironmentID,
						Time:        time.Duration(now),
						SceneTran:   "project_dynamics_scene_order",
						TypeTran:    typeTran,
						ContentTran: "project_dynamics_content_overtime",
						ContentData: map[string]interface{}{
							"orderNumber": item.OrderNumber,
						},
					}
					_, err := tools.Database.Collection("project_dynamics").InsertOne(nil, dynamics)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
				updateID = append(updateID, item.ID)

				var siteOrStoreIDs = []primitive.ObjectID{item.SendID, item.ReceiveID}
				userMails, err := tools.GetRoleUsersMail(item.ProjectID, item.EnvironmentID, "notice.order.timeout", siteOrStoreIDs...)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//difference := time.Unix(now, 0).Sub(time.Unix(timeOutDay, 0))
				// 查询权限的用户
				//permissions := []string{
				//	"operation.supply.shipment.send",
				//	"operation.supply.shipment.lose",
				//	"operation.supply.shipment.receive",
				//	"operation.supply.shipment.close",
				//	"operation.supply.shipment.terminated",
				//}
				//var siteOrStoreID = []primitive.ObjectID{item.ReceiveID}
				//userIds, err := tools.GetPermissionUserIds(sctx, permissions, item.ProjectID, item.EnvironmentID, siteOrStoreID...)
				//if err != nil {
				//	return nil, errors.WithStack(err)
				//}

				workTask := models.WorkTask{}
				err = tools.Database.Collection("work_task").FindOne(sctx, bson.M{
					"info.medicine_order_id": item.ID,
					"info.work_type":         bson.M{"$in": []int{3, 6, 14, 15}},
					"info.status":            0,
				}).Decode(&workTask)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}

				if workTask.UserIDs != nil && len(workTask.UserIDs) > 0 {
					//过滤掉app已关闭的用户
					filter := bson.M{"user_id": bson.M{"$in": workTask.UserIDs}, "app": true, "project_id": workTask.ProjectID, "env_id": workTask.EnvironmentID}
					userEnvs := make([]models.UserProjectEnvironment, 0)
					cursor, err := tools.Database.Collection("user_project_environment").Find(nil, filter)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &userEnvs)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					var uIds []models.UserIds
					var us []primitive.ObjectID
					for _, user := range userEnvs {
						uIds = append(uIds, models.UserIds{
							UserID: user.UserID,
							Read:   false,
						})
						us = append(us, user.UserID)
					}

					if !workTask.ID.IsZero() && len(us) > 0 {
						timeoutDays := int64((time.Unix(now, 0).Sub(time.Unix(timeOutDay, 0))).Hours()/24) + 1 // +1表示不满一天就按一天算
						appTaskNotice = append(appTaskNotice, models.AppTaskNotice{
							ID:            primitive.NewObjectID(),
							CustomerID:    item.CustomerID,
							ProjectID:     item.ProjectID,
							EnvironmentID: item.EnvironmentID,
							NoticeType:    1,
							NoticeTime:    time.Duration(time.Now().Unix()),
							ExpireNotice: models.ExpireNotice{
								WorkTaskID:      workTask.ID,
								WorkType:        9,
								MedicineOrderID: item.ID,
								// 加上3600秒是因为 timeOutDay->baseTime->item.UpdatedAt日期并不是按定时任务的执行时间去更新的。系统执行逻辑过程中多少会有延迟。给3600秒是防止item.UpdatedAt延迟几秒导致超时时间少计算一天。
								TimeoutDays: int64((time.Unix(now+3600, 0).Sub(time.Unix(timeOutDay, 0))).Hours()/24) + 1, // +1表示不满一天就按一天算
							},
							UserIds: uIds,
						})

						// 极光推送
						wtv := models.WorkTaskView{}
						wtv.ID = workTask.ID
						wtv.CustomerID = workTask.CustomerID
						wtv.ProjectID = workTask.ProjectID
						wtv.EnvironmentID = workTask.EnvironmentID
						wtv.CohortID = workTask.CohortID
						wtv.Info.FinishTime = workTask.Info.FinishTime
						wtv.Info.Status = workTask.Info.Status
						wtv.NoticeType = 1
						wtv.WorkNoticeType = 9
						wtv.Info.WorkType = workTask.Info.WorkType

						var wk models.WorkTask
						wk.ProjectID = workTask.ProjectID
						wk.EnvironmentID = item.EnvironmentID
						wk.Info.MedicineOrderID = item.ID
						userRegistrationIdAppLanguages, err := getUserRegistrationId(31, us, wk, primitive.NilObjectID, primitive.NilObjectID)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						//projectInfo, project, _ := getAuroraPushProjectInfo(item.ProjectID, item.EnvironmentID)
						_, _, orderInfo, err := getAuroraPushOrderInfo(item.ID, project)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						day := strconv.FormatInt(timeoutDays, 10)

						title_zh := locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_title")
						title_en := locales.TrWithLang("en-US", "app_shipment_timeout_notification_title")

						alertBody_zh := locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_content_1") + item.OrderNumber + locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_content_2") + day + locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_content_day") + " | " + project.ProjectInfo.Number + "[" + env.Name + "]" + " | " + orderInfo["siteName"].(string)
						alertBody_en := ""

						dayNum, err := strconv.Atoi(day)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						if dayNum <= 1 {
							alertBody_en = locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_1") + item.OrderNumber + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_2") + day + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_day") + " | " + project.ProjectInfo.Number + "[" + env.Name + "]" + " | " + orderInfo["siteName"].(string)
						} else {
							alertBody_en = locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_1") + item.OrderNumber + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_2") + day + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_days") + " | " + project.ProjectInfo.Number + "[" + env.Name + "]" + " | " + orderInfo["siteName"].(string)
						}

						//转JSON
						sJson, err := json.Marshal(wtv)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						var registrationId_zh []string
						var registrationId_en []string
						for _, ural := range userRegistrationIdAppLanguages {
							if ural.AppLanguage == "" || ural.AppLanguage == "zh-CN" {
								registrationId_zh = append(registrationId_zh, ural.RegistrationId)
							} else {
								registrationId_en = append(registrationId_en, ural.RegistrationId)
							}
						}

						// 中文推送
						if registrationId_zh != nil && len(registrationId_zh) > 0 {
							err = tools.DataAssembly(registrationId_zh, title_zh, alertBody_zh, string(sJson))
							if err != nil {
								tools.SaveErrorLog("OrderTimeOutReminder-"+project.ProjectInfo.Number, err)
							}
						}

						// 英文推送
						if registrationId_en != nil && len(registrationId_en) > 0 {
							err = tools.DataAssembly(registrationId_en, title_en, alertBody_en, string(sJson))
							if err != nil {
								tools.SaveErrorLog("OrderTimeOutReminder-"+project.ProjectInfo.Number, err)
							}
						}
					}
				}
				if len(userMails) == 0 {
					continue
				}
				mails := []interface{}{}
				subject := "order.over_title_site"
				//content := "order.overtime_site_dual"
				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": item.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				langList := make([]string, 0)
				htmlName := "order_overtime_new.html"
				if noticeConfig.Automatic != 0 {
					if noticeConfig.Automatic == 1 {
						langList = append(langList, "zh")
						htmlName = "order_overtime_new_zh.html"
					} else if noticeConfig.Automatic == 2 {
						langList = append(langList, "en")
						htmlName = "order_overtime_new_en.html"
					} else if noticeConfig.Automatic == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						htmlName = "order_overtime_new.html"
					}
				} else {
					langList = append(langList, "zh")
					langList = append(langList, "en")
				}
				statusItem := "Confirmed"
				statusItemZH := "已确认"
				orderStatus := item.Status
				if orderStatus == 2 {
					statusItem = "In Delivery"
					statusItemZH = "已运送"
				}
				date, err := time.Parse("20060102", item.OrderNumber[0:8])
				dateStr := date.Format("2006-01-02")
				//timeZone := fmt.Sprintf("(UTC%+d)", project.ProjectInfo.TimeZone)
				timeZone, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
				generateDate := dateStr + "(" + timeZone + ")"
				sendName := getOriginationName(item.SendID, projectStorehouses, projectSites, "zh")
				sendNameEn := getOriginationName(item.SendID, projectStorehouses, projectSites, "en")
				receiveName := ""
				receiveNameEn := ""
				contentData := bson.M{
					"projectNumber": project.ProjectInfo.Number,
					"projectName":   project.ProjectInfo.Name,
					"envName":       env.Name,
					"orderNumber":   item.OrderNumber,
					"origination":   sendName,
					"originationEn": sendNameEn,
					"destination":   receiveName,
					"destinationEn": receiveNameEn,
					"statusItem":    statusItem,
					"statusItemZH":  statusItemZH,
					"generateDate":  generateDate,
				}
				subjectData := bson.M{
					"projectNumber": project.ProjectInfo.Number,
					"projectName":   project.ProjectInfo.Name,
					"envName":       env.Name,
					"orderNumber":   item.OrderNumber,
					"destination":   receiveName,
					"destinationEn": receiveNameEn,
				}
				if item.Type == 5 || item.Type == 6 {
					var subject models.Subject
					err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": item.SubjectID}).Decode(&subject)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					var dispensing models.Dispensing
					err := tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": item.DispensingID}).Decode(&dispensing)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
					for _, info := range subject.Info {
						receiveName = info.Value.(string)
						receiveNameEn = info.Value.(string)
						subjectData["destination"] = receiveName + " " + dispensing.VisitInfo.Name
						contentData["destination"] = receiveName
						subjectData["destinationEn"] = receiveNameEn + " " + dispensing.VisitInfo.Name
						contentData["destinationEn"] = receiveNameEn
						break
					}
				} else {
					receiveName = getOriginationName(item.ReceiveID, projectStorehouses, projectSites, "zh")
					receiveNameEn = getOriginationName(item.ReceiveID, projectStorehouses, projectSites, "en")
					subjectData["destination"] = receiveName
					contentData["destination"] = receiveName
					subjectData["destinationEn"] = receiveNameEn
					contentData["destinationEn"] = receiveNameEn

				}

				mailBodyContet, err := tools.MailBodyContent(nil, envID, "notice.order.timeout")
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for key, v := range mailBodyContet {
					contentData[key] = v
				}
				bodyContentKeys, _, err := tools.MailCustomContent(&gin.Context{}, envID, "notice.order.timeout", contentData, nil)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				for _, userMail := range userMails {
					var toUserMail []string
					toUserMail = append(toUserMail, userMail)
					mail := models.Mail{
						ID:          primitive.NewObjectID(),
						Subject:     subject,
						SubjectData: subjectData,
						//Content:      content,
						ContentData:    contentData,
						To:             toUserMail,
						Lang:           "en-US",
						LangList:       langList,
						Status:         0,
						CreatedTime:    time.Duration(time.Now().Unix()),
						ExpectedTime:   time.Duration(time.Now().Unix()),
						SendTime:       time.Duration(time.Now().Unix()),
						HTML:           htmlName,
						BodyContentKey: bodyContentKeys,
					}
					mails = append(mails, mail)
				}
				_, err = tools.Database.Collection("mail").InsertMany(sctx, mails)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		if len(updateID) > 0 {
			_, err = tools.Database.Collection("medicine_order").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": updateID}}, bson.M{"$set": bson.M{"last_reminder_date": now}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		// 添加app任务通知
		if appTaskNotice != nil && len(appTaskNotice) > 0 {
			_, err = tools.Database.Collection("app_task_notice").InsertMany(sctx, appTaskNotice)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// func OrderTimeOutReminderNew() error {
// 	defer tools.DeferReturn("OrderTimeOutReminder")
// 	hours, _ := time.ParseDuration("23h59m")

// 	yesDate := time.Now().Add(-hours).Unix()
// 	nowTime := time.Now()
// 	now := nowTime.Unix()
// 	date := nowTime.UTC().Format("15:04")

// 	pipeline := mongo.Pipeline{
// 		// 查询超过24小时未发送通知，状态为1、2的订单
// 		{{"$match", bson.M{"status": bson.M{"$in": bson.A{1, 2}},
// 			"$or": bson.A{
// 				bson.M{"last_reminder_date": bson.M{"$lt": yesDate}},
// 				bson.M{"last_reminder_date": bson.M{"$exists": false}},
// 			},
// 		}}},
// 		{{Key: "$lookup", Value: bson.M{
// 			"from": "notice_config",
// 			"let": bson.M{
// 				"env_id": "$env_id",
// 			},
// 			"pipeline": bson.A{
// 				bson.M{
// 					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}, "key": "notice.order.timeout",
// 						"$or": bson.A{
// 							bson.M{"timeout_days": bson.M{"$gt": 0}},
// 							bson.M{"send_days": bson.M{"$gt": 0}}},
// 					},
// 				},
// 				bson.M{"$project": bson.M{
// 					"timeout_days": bson.M{"$multiply": bson.A{"$timeout_days", 24, 3600}},
// 					"send_days":    bson.M{"$multiply": bson.A{"$send_days", 24, 3600}},
// 				}},
// 			},
// 			"as": "notice_config",
// 		}}},
// 		{{"$unwind", "$notice_config"}},
// 		// 创建时间 + 超时时间 < 当前时间
// 		{{Key: "$lookup", Value: bson.M{
// 			"from": "project",
// 			"let": bson.M{
// 				"project_id": "$project_id",
// 				"env_id":     "$env_id",
// 			},
// 			"pipeline": bson.A{
// 				bson.M{
// 					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$project_id"}}},
// 				},
// 				bson.M{"$unwind": "$envs"},
// 				bson.M{
// 					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$envs.id", "$$env_id"}}},
// 				},
// 			},
// 			"as": "project",
// 		}}},
// 		{{Key: "$lookup", Value: bson.M{
// 			"from": "project_site",
// 			"let": bson.M{
// 				"send_id":    "$send_id",
// 				"receive_id": "$receive_id",
// 			},
// 			"pipeline": bson.A{
// 				bson.M{
// 					"$match": bson.M{"$or": bson.A{
// 						bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$receive_id"}}},
// 						bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$send_id"}}},
// 					}},
// 				},
// 			},
// 			"as": "project_site",
// 		}}},
// 		{{Key: "$lookup", Value: bson.M{
// 			"from": "project_storehouse",
// 			"let": bson.M{
// 				"send_id":    "$send_id",
// 				"receive_id": "$receive_id",
// 			},
// 			"pipeline": bson.A{
// 				bson.M{
// 					"$match": bson.M{"$or": bson.A{
// 						bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$receive_id"}}},
// 						bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$send_id"}}},
// 					}},
// 				},
// 				bson.M{"$lookup": bson.M{"from": "storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "storehouse"}},
// 				bson.M{"$unwind": "$storehouse"},
// 			},
// 			"as": "project_storehouse",
// 		}}},
// 		{{Key: "$lookup", Value: bson.M{
// 			"from": "subject", // 关联的集合名称
// 			"let": bson.M{
// 				"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
// 			},
// 			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
// 				bson.M{
// 					"$match": bson.M{
// 						"$expr": bson.M{
// 							"$and": bson.A{
// 								bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
// 								bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
// 							},
// 						},
// 					},
// 				},
// 			},
// 			"as": "subject", // 关联结果的字段名
// 		}}},
// 		{{Key: "$lookup", Value: bson.M{"from": "dispensing", "localField": "dispensing_id", "foreignField": "_id", "as": "dispensing"}}},
// 		{{Key: "$unwind", Value: bson.M{"path": "$subject", "preserveNullAndEmptyArrays": true}}},
// 		{{Key: "$unwind", Value: bson.M{"path": "$dispensing", "preserveNullAndEmptyArrays": true}}},

// 		{{Key: "$lookup", Value: bson.M{
// 			"from": "history",
// 			"let": bson.M{
// 				"id": "$_id",
// 			},
// 			"pipeline": bson.A{
// 				bson.M{
// 					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$oid", "$$id"}}, "key": "history.order.confrim-new"},
// 				},
// 			},
// 			"as": "history",
// 		}}},
// 		{{Key: "$unwind", Value: bson.M{"path": "$history", "preserveNullAndEmptyArrays": true}}},

// 		{{Key: "$project", Value: bson.M{
// 			"_id":                1,
// 			"customer_id":        1,
// 			"project_id":         1,
// 			"env_id":             1,
// 			"send_id":            1,
// 			"receive_id":         1,
// 			"order_number":       1,
// 			"type":               1,
// 			"status":             1,
// 			"project":            bson.M{"$first": "$project"},
// 			"project_site":       1,
// 			"project_storehouse": 1,
// 			"subject":            1,
// 			"dispensing":         1,
// 			"history":            1,
// 			"updated_at":         "$meta.updated_at",
// 			"created_at":         "$meta.created_at",
// 			"timeout_days":       bson.M{"$ifNull": bson.A{"$notice_config.timeout_days", 0}},
// 			"send_days":          bson.M{"$ifNull": bson.A{"$notice_config.send_days", 0}},
// 		}}},
// 	}
// 	callback := func(sctx mongo.SessionContext) (interface{}, error) {
// 		cursor, err := tools.Database.Collection("medicine_order").Aggregate(sctx, pipeline)
// 		if err != nil {
// 			return nil, errors.WithStack(err)
// 		}
// 		var data []dataStruct
// 		//var datas []map[string]interface{}
// 		err = cursor.All(nil, &data)
// 		if err != nil {
// 			return nil, errors.WithStack(err)
// 		}
// 		var updateID []primitive.ObjectID
// 		// app任务通知
// 		var appTaskNotice []interface{}
// 		for _, item := range data {
// 			if item.Status == 1 && item.TimeoutDays == 0 { // 确认超时时间没有配置 确认订单不发超时订单
// 				continue
// 			}
// 			//baseTime := item.CreatedAt
// 			//
// 			//// 计算超时时间点
// 			//var timeOutDay int64
// 			//if item.Status == 1 {
// 			//	timeOutDay = time.Unix(int64(item.CreatedAt), 0).Add(time.Duration(item.TimeoutDays)).Unix()
// 			//}
// 			//if item.SendDays != 0 {
// 			//	baseTime = item.UpdatedAt
// 			//	timeOutDay = time.Unix(int64(item.UpdatedAt), 0).Add(time.Duration(item.SendDays)).Unix()
// 			//} else {
// 			//	timeOutDay = time.Unix(int64(item.CreatedAt), 0).Add(time.Duration(item.TimeoutDays)).Unix()
// 			//}
// 			//
// 			baseTime := item.CreatedAt

// 			if item.UpdatedAt != 0 { // 确认时间 || 运送时间
// 				baseTime = item.UpdatedAt
// 			}
// 			// 计算超时时间点
// 			var timeOutDay int64
// 			if item.Status == 1 {
// 				timeOutDay = int64(baseTime) + int64(item.TimeoutDays)
// 			}
// 			if item.Status == 2 {
// 				if item.SendDays != 0 { //运送起时间配置    使用运送时间计算
// 					timeOutDay = int64(baseTime) + int64(item.SendDays)
// 				} else { // 运送起时间没配置    使用确认时间计算
// 					if item.History.Time == 0 { //自动订单运送  没有确认轨迹    使用创建时间
// 						baseTime = item.CreatedAt
// 						timeOutDay = int64(item.CreatedAt) + int64(item.TimeoutDays)
// 					} else { //订单运送 有确认轨迹 使用轨迹时间
// 						baseTime = item.History.Time
// 						timeOutDay = int64(item.History.Time) + int64(item.TimeoutDays)

// 					}

// 				}
// 			}

// 			// 判断是否在超时时间范围内
// 			if now < timeOutDay {
// 				continue
// 			}

// 			format, _ := time.ParseDuration("1m")
// 			updated_at := time.Unix(int64(baseTime), 0).UTC().Format("15:04")
// 			updatedAddOne := time.Unix(int64(baseTime), 0).UTC().Add(format).Format("15:04")
// 			// 判断订单是在这个时间区间内过期 +- 一分钟
// 			if updated_at != date && updatedAddOne != date {
// 				continue
// 			}
// 			//项目动态
// 			{
// 				typeTran := "project_dynamics_type_overtime"
// 				if item.Type == 3 || item.Type == 4 {
// 					typeTran = "project_dynamics_type_overtime_recovery"
// 				}
// 				dynamics := models.ProjectDynamics{
// 					ID:          primitive.NewObjectID(),
// 					Operator:    primitive.NilObjectID,
// 					OID:         item.EnvironmentID,
// 					Time:        time.Duration(now),
// 					SceneTran:   "project_dynamics_scene_order",
// 					TypeTran:    typeTran,
// 					ContentTran: "project_dynamics_content_overtime",
// 					ContentData: map[string]interface{}{
// 						"orderNumber": item.OrderNumber,
// 					},
// 				}
// 				_, err := tools.Database.Collection("project_dynamics").InsertOne(nil, dynamics)
// 				if err != nil {
// 					return nil, errors.WithStack(err)
// 				}
// 			}
// 			updateID = append(updateID, item.ID)

// 			var siteOrStoreIDs = []primitive.ObjectID{item.SendID, item.ReceiveID}
// 			userMails, err := tools.GetRoleUsersMail(item.ProjectID, item.EnvironmentID, "notice.order.timeout", siteOrStoreIDs...)
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}

// 			//difference := time.Unix(now, 0).Sub(time.Unix(timeOutDay, 0))
// 			// 查询权限的用户
// 			//permissions := []string{
// 			//	"operation.supply.shipment.send",
// 			//	"operation.supply.shipment.lose",
// 			//	"operation.supply.shipment.receive",
// 			//	"operation.supply.shipment.close",
// 			//	"operation.supply.shipment.terminated",
// 			//}
// 			//var siteOrStoreID = []primitive.ObjectID{item.ReceiveID}
// 			//userIds, err := tools.GetPermissionUserIds(sctx, permissions, item.ProjectID, item.EnvironmentID, siteOrStoreID...)
// 			//if err != nil {
// 			//	return nil, errors.WithStack(err)
// 			//}

// 			workTask := models.WorkTask{}
// 			err = tools.Database.Collection("work_task").FindOne(sctx, bson.M{
// 				"info.medicine_order_id": item.ID,
// 				"info.work_type":         bson.M{"$in": []int{3, 6, 14, 15}},
// 				"info.status":            0,
// 			}).Decode(&workTask)
// 			if err != nil && err != mongo.ErrNoDocuments {
// 				return nil, errors.WithStack(err)
// 			}

// 			if workTask.UserIDs != nil && len(workTask.UserIDs) > 0 {
// 				//过滤掉app已关闭的用户
// 				filter := bson.M{"user_id": bson.M{"$in": workTask.UserIDs}, "app": true, "project_id": workTask.ProjectID, "env_id": workTask.EnvironmentID}
// 				userEnvs := make([]models.UserProjectEnvironment, 0)
// 				cursor, err := tools.Database.Collection("user_project_environment").Find(nil, filter)
// 				if err != nil {
// 					return nil, errors.WithStack(err)
// 				}
// 				err = cursor.All(nil, &userEnvs)
// 				if err != nil {
// 					return nil, errors.WithStack(err)
// 				}
// 				var uIds []models.UserIds
// 				var us []primitive.ObjectID
// 				for _, user := range userEnvs {
// 					uIds = append(uIds, models.UserIds{
// 						UserID: user.UserID,
// 						Read:   false,
// 					})
// 					us = append(us, user.UserID)
// 				}

// 				if !workTask.ID.IsZero() && len(us) > 0 {
// 					timeoutDays := int64((time.Unix(now, 0).Sub(time.Unix(timeOutDay, 0))).Hours()/24) + 1 // +1表示不满一天就按一天算
// 					appTaskNotice = append(appTaskNotice, models.AppTaskNotice{
// 						ID:            primitive.NewObjectID(),
// 						CustomerID:    item.CustomerID,
// 						ProjectID:     item.ProjectID,
// 						EnvironmentID: item.EnvironmentID,
// 						NoticeType:    1,
// 						NoticeTime:    time.Duration(time.Now().Unix()),
// 						ExpireNotice: models.ExpireNotice{
// 							WorkTaskID:      workTask.ID,
// 							WorkType:        9,
// 							MedicineOrderID: item.ID,
// 							// 加上3600秒是因为 timeOutDay->baseTime->item.UpdatedAt日期并不是按定时任务的执行时间去更新的。系统执行逻辑过程中多少会有延迟。给3600秒是防止item.UpdatedAt延迟几秒导致超时时间少计算一天。
// 							TimeoutDays: int64((time.Unix(now+3600, 0).Sub(time.Unix(timeOutDay, 0))).Hours()/24) + 1, // +1表示不满一天就按一天算
// 						},
// 						UserIds: uIds,
// 					})

// 					// 极光推送
// 					wtv := models.WorkTaskView{}
// 					wtv.ID = workTask.ID
// 					wtv.CustomerID = workTask.CustomerID
// 					wtv.ProjectID = workTask.ProjectID
// 					wtv.EnvironmentID = workTask.EnvironmentID
// 					wtv.CohortID = workTask.CohortID
// 					wtv.Info.FinishTime = workTask.Info.FinishTime
// 					wtv.Info.Status = workTask.Info.Status
// 					wtv.NoticeType = 1
// 					wtv.WorkNoticeType = 9
// 					wtv.Info.WorkType = workTask.Info.WorkType

// 					var wk models.WorkTask
// 					wk.ProjectID = workTask.ProjectID
// 					wk.EnvironmentID = item.EnvironmentID
// 					wk.Info.MedicineOrderID = item.ID
// 					userRegistrationIdAppLanguages, err := getUserRegistrationId(31, us, wk, primitive.NilObjectID, primitive.NilObjectID)
// 					if err != nil {
// 						return nil, errors.WithStack(err)
// 					}

// 					projectInfo, project, _ := getAuroraPushProjectInfo(item.ProjectID, item.EnvironmentID)
// 					_, _, orderInfo, err := getAuroraPushOrderInfo(item.ID, project)
// 					if err != nil {
// 						return nil, errors.WithStack(err)
// 					}
// 					day := strconv.FormatInt(timeoutDays, 10)

// 					title_zh := locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_title")
// 					title_en := locales.TrWithLang("en-US", "app_shipment_timeout_notification_title")

// 					alertBody_zh := locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_content_1") + item.OrderNumber + locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_content_2") + day + locales.TrWithLang("zh-CN", "app_shipment_timeout_notification_content_day") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
// 					alertBody_en := ""

// 					dayNum, err := strconv.Atoi(day)
// 					if err != nil {
// 						return nil, errors.WithStack(err)
// 					}

// 					if dayNum <= 1 {
// 						alertBody_en = locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_1") + item.OrderNumber + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_2") + day + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_day") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
// 					} else {
// 						alertBody_en = locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_1") + item.OrderNumber + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_2") + day + locales.TrWithLang("en-US", "app_shipment_timeout_notification_content_days") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
// 					}

// 					//转JSON
// 					sJson, err := json.Marshal(wtv)
// 					if err != nil {
// 						return nil, errors.WithStack(err)
// 					}

// 					var registrationId_zh []string
// 					var registrationId_en []string
// 					for _, ural := range userRegistrationIdAppLanguages {
// 						if ural.AppLanguage == "" || ural.AppLanguage == "zh-CN" {
// 							registrationId_zh = append(registrationId_zh, ural.RegistrationId)
// 						} else {
// 							registrationId_en = append(registrationId_en, ural.RegistrationId)
// 						}
// 					}

// 					// 中文推送
// 					if registrationId_zh != nil && len(registrationId_zh) > 0 {
// 						err = tools.DataAssembly(registrationId_zh, title_zh, alertBody_zh, string(sJson))
// 						if err != nil {
// 							return nil, errors.WithStack(err)
// 						}
// 					}

// 					// 英文推送
// 					if registrationId_en != nil && len(registrationId_en) > 0 {
// 						err = tools.DataAssembly(registrationId_en, title_en, alertBody_en, string(sJson))
// 						if err != nil {
// 							return nil, errors.WithStack(err)
// 						}
// 					}
// 				}
// 			}
// 			if len(userMails) == 0 {
// 				continue
// 			}
// 			mails := []interface{}{}
// 			subject := "order.over_title_site"
// 			//content := "order.overtime_site_dual"
// 			var noticeConfig models.NoticeConfig
// 			err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": item.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
// 			if err != nil && err != mongo.ErrNoDocuments {
// 				return nil, errors.WithStack(err)
// 			}
// 			langList := make([]string, 0)
// 			htmlName := "order_overtime_new.html"
// 			if noticeConfig.Automatic != 0 {
// 				if noticeConfig.Automatic == 1 {
// 					langList = append(langList, "zh")
// 					htmlName = "order_overtime_new_zh.html"
// 				} else if noticeConfig.Automatic == 2 {
// 					langList = append(langList, "en")
// 					htmlName = "order_overtime_new_en.html"
// 				} else if noticeConfig.Automatic == 3 {
// 					langList = append(langList, "zh")
// 					langList = append(langList, "en")
// 					htmlName = "order_overtime_new.html"
// 				}
// 			} else {
// 				langList = append(langList, "zh")
// 				langList = append(langList, "en")
// 			}
// 			statusItem := "Confirmed"
// 			statusItemZH := "已确认"
// 			orderStatus := item.Status
// 			if orderStatus == 2 {
// 				statusItem = "In Delivery"
// 				statusItemZH = "已运送"
// 			}
// 			date, err := time.Parse("20060102", item.OrderNumber[0:8])
// 			dateStr := date.Format("2006-01-02")
// 			timeZone := fmt.Sprintf("(UTC%+d)", item.Project.ProjectInfo.TimeZone)
// 			generateDate := dateStr + timeZone
// 			sendName := getOriginationName(item.SendID, item.ProjectStorehouse, item.ProjectSite)
// 			receiveName := ""
// 			contentData := bson.M{
// 				"projectNumber": item.Project.ProjectInfo.Number,
// 				"projectName":   item.Project.ProjectInfo.Name,
// 				"envName":       item.Project.Environments.Name,
// 				"orderNumber":   item.OrderNumber,
// 				"origination":   sendName,
// 				"destination":   receiveName,
// 				"statusItem":    statusItem,
// 				"statusItemZH":  statusItemZH,
// 				"generateDate":  generateDate,
// 			}
// 			subjectData := bson.M{
// 				"projectNumber": item.Project.ProjectInfo.Number,
// 				"projectName":   item.Project.ProjectInfo.Name,
// 				"envName":       item.Project.Environments.Name,
// 				"orderNumber":   item.OrderNumber,
// 				"destination":   receiveName,
// 			}
// 			if item.Type == 5 || item.Type == 6 {
// 				for _, info := range item.Subject.Info {
// 					receiveName = info.Value.(string)
// 					subjectData["destination"] = receiveName + " " + item.Dispensing.VisitInfo.Name
// 					contentData["destination"] = receiveName
// 					break
// 				}
// 			} else {
// 				receiveName = getOriginationName(item.ReceiveID, item.ProjectStorehouse, item.ProjectSite)
// 				subjectData["destination"] = receiveName
// 				contentData["destination"] = receiveName

// 			}

// 			mailBodyContet, err := tools.MailBodyContent(nil, item.Project.Environments.ID, "notice.order.timeout")
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}
// 			for key, v := range mailBodyContet {
// 				contentData[key] = v
// 			}
// 			bodyContentKeys, _, err := tools.MailCustomContent(&gin.Context{}, item.Project.Environments.ID, "notice.order.timeout", contentData, nil)
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}

// 			for _, userMail := range userMails {
// 				var toUserMail []string
// 				toUserMail = append(toUserMail, userMail)
// 				mail := models.Mail{
// 					ID:          primitive.NewObjectID(),
// 					Subject:     subject,
// 					SubjectData: subjectData,
// 					//Content:      content,
// 					ContentData:    contentData,
// 					To:             toUserMail,
// 					Lang:           "en-US",
// 					LangList:       langList,
// 					Status:         0,
// 					CreatedTime:    time.Duration(time.Now().Unix()),
// 					ExpectedTime:   time.Duration(time.Now().Unix()),
// 					SendTime:       time.Duration(time.Now().Unix()),
// 					HTML:           htmlName,
// 					BodyContentKey: bodyContentKeys,
// 				}
// 				mails = append(mails, mail)
// 			}
// 			_, err = tools.Database.Collection("mail").InsertMany(sctx, mails)
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}
// 		}
// 		if len(updateID) > 0 {
// 			_, err = tools.Database.Collection("medicine_order").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": updateID}}, bson.M{"$set": bson.M{"last_reminder_date": now}})
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}
// 		}
// 		// 添加app任务通知
// 		if appTaskNotice != nil && len(appTaskNotice) > 0 {
// 			_, err = tools.Database.Collection("app_task_notice").InsertMany(sctx, appTaskNotice)
// 			if err != nil {
// 				return nil, errors.WithStack(err)
// 			}
// 		}

// 		return nil, nil
// 	}
// 	err := tools.Transaction(callback)
// 	if err != nil {
// 		return errors.WithStack(err)
// 	}
// 	return nil
// }

func getOriginationName(ID primitive.ObjectID, storehouse []Storehouse, projectSite []models.ProjectSite, lang string) string {
	for _, item := range storehouse {
		if ID == item.ID {
			return item.Storehouse.Name
		}
	}
	for _, site := range projectSite {
		if ID == site.ID {
			if site.ShortName != "" {
				return site.Number + " " + site.ShortName
			} else {
				if lang == "zh" {
					return site.Number + " " + site.Name
				} else if lang == "en" {
					if site.NameEn != "" {
						return site.Number + " " + site.NameEn
					} else {
						return site.Number + " " + site.Name
					}

				}
			}
		}
	}
	return ""
}

// 查询固定权限的用户
//func GetPermissionUserId(sctx mongo.SessionContext, permission []string, projectID primitive.ObjectID, envID primitive.ObjectID, siteOrStorehouseID primitive.ObjectID) ([]primitive.ObjectID, error) {
//	// 返回数据
//	var userIds []primitive.ObjectID
//	// 查询项目权限角色
//	projectRoles := make([]models.ProjectRolePermission, 0)
//	cursor, err := tools.Database.Collection("project_role_permission").Find(sctx, bson.M{"project_id": projectID, "permissions": bson.M{"$in": permission}})
//	if err != nil {
//		return nil, errors.WithStack(err)
//	}
//	err = cursor.All(sctx, &projectRoles)
//	if err != nil {
//		return nil, errors.WithStack(err)
//	}
//	// 查询有该角色的用户
//	var roleIds []primitive.ObjectID
//	for _, pr := range projectRoles {
//		roleIds = append(roleIds, pr.ID)
//	}
//	if roleIds != nil && len(roleIds) > 0 {
//		userProjectEnvironments := make([]models.UserProjectEnvironment, 0)
//		cursor, err = tools.Database.Collection("user_project_environment").Find(sctx, bson.M{"project_id": projectID, "env_id": envID, "roles": bson.M{"$in": roleIds}, "app": true})
//		if err != nil {
//			return nil, errors.WithStack(err)
//		}
//		err = cursor.All(sctx, &userProjectEnvironments)
//		if err != nil {
//			return nil, errors.WithStack(err)
//		}
//		for _, upe := range userProjectEnvironments {
//			if !siteOrStorehouseID.IsZero() {
//				var projectSite models.ProjectSite
//				err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": siteOrStorehouseID}).Decode(&projectSite)
//				if err != nil && err != mongo.ErrNoDocuments {
//					return nil, errors.WithStack(err)
//				}
//				if !projectSite.ID.IsZero() {
//					// 查询是否给当前用户分配了中心
//					match := bson.M{"project_id": projectID, "env_id": envID, "user_id": upe.UserID, "site_id": siteOrStorehouseID}
//					count, err := tools.Database.Collection("user_site").CountDocuments(sctx, match)
//					if err != nil {
//						return nil, errors.WithStack(err)
//					}
//
//					// 有中心权限才追加进去
//					if count > 0 {
//						userIds = append(userIds, upe.UserID)
//					}
//				} else {
//					// 查询是否给当前用户分配了仓库
//					match := bson.M{"project_id": projectID, "env_id": envID, "user_id": upe.UserID, "depot_id": siteOrStorehouseID}
//					count, err := tools.Database.Collection("user_depot").CountDocuments(sctx, match)
//					if err != nil {
//						return nil, errors.WithStack(err)
//					}
//
//					// 有仓库权限才追加进去
//					if count > 0 {
//						userIds = append(userIds, upe.UserID)
//					}
//				}
//			} else {
//				userIds = append(userIds, upe.UserID)
//			}
//		}
//	}
//	return userIds, nil
//}
