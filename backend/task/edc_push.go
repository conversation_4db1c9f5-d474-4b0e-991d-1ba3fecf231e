package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strings"
	"time"
)

// 定时任务间隔执行(retry-重试次数，taskType任务类型 0间隔执行，1凌晨定时任务执行)
func SendEdcPush(retry int, taskType int) error {
	defer tools.DeferReturn("SendEdcPush")
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询失败的数据
		var edcPushAll []models.EdcPush
		// 条件
		match := bson.M{
			"status": 0,
			"retry":  retry,
		}

		if taskType == 1 {
			now := time.Now()
			yesterday := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, time.Local)
			match = bson.M{
				"status":      2,
				"create_time": bson.M{"$gte": yesterday.Unix()},
			}
		}

		cursor, err := tools.Database.Collection("edc_push").Find(sctx, match)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if err = cursor.All(sctx, &edcPushAll); err != nil {
			return nil, errors.WithStack(err)
		}

		for _, edcPush := range edcPushAll {
			var project models.Project
			var edcPushData models.EdcPush
			var update = bson.M{}
			if taskType == 1 {
				retry = edcPush.Retry
			}
			if edcPush.Source == 1 { // 随机
				// 查询受试者信息
				subjectFilter := bson.M{"_id": edcPush.OID}
				var findSubject models.Subject
				err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				if findSubject.ID == primitive.NilObjectID {
					continue
				}

				// 查询项目
				projectFilter := bson.M{"_id": findSubject.ProjectID}
				err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 查询属性
				var attribute models.Attribute
				match := bson.M{
					"project_id":  findSubject.ProjectID,
					"env_id":      findSubject.EnvironmentID,
					"customer_id": findSubject.CustomerID,
				}
				if findSubject.CohortID != primitive.NilObjectID {
					match = bson.M{
						"project_id":  findSubject.ProjectID,
						"env_id":      findSubject.EnvironmentID,
						"customer_id": findSubject.CustomerID,
						"cohort_id":   findSubject.CohortID,
					}
				}
				err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 查询中心
				var projectSite models.ProjectSite
				err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": findSubject.ProjectSiteID}).Decode(&projectSite)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 随机信息组装
				edcPushData = RandomDataAssembly(edcPush, attribute, projectSite)
			} else { // 发药
				// 查询发药信息
				dispensingFilter := bson.M{"_id": edcPush.OID}
				var findDispensing models.Dispensing
				err = tools.Database.Collection("dispensing").FindOne(sctx, dispensingFilter).Decode(&findDispensing)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 查询项目
				projectFilter := bson.M{"_id": findDispensing.ProjectID}
				err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 查询受试者信息
				subjectFilter := bson.M{"_id": findDispensing.SubjectID}
				var findSubject models.Subject
				err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 查询属性
				var attribute models.Attribute
				match := bson.M{
					"project_id":  findSubject.ProjectID,
					"env_id":      findSubject.EnvironmentID,
					"customer_id": findSubject.CustomerID,
				}
				if findSubject.CohortID != primitive.NilObjectID {
					match = bson.M{
						"project_id":  findSubject.ProjectID,
						"env_id":      findSubject.EnvironmentID,
						"customer_id": findSubject.CustomerID,
						"cohort_id":   findSubject.CohortID,
					}
				}
				err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 查询中心
				var projectSite models.ProjectSite
				err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": findSubject.ProjectSiteID}).Decode(&projectSite)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				// 发药数据组装
				edcPushData = DispensingDataAssembly(edcPush, attribute, projectSite)
			}

			edcPushData.SendTime = time.Duration(time.Now().Unix()) // 推送时间

			edcPushData.Content.EdcPushLogId = primitive.NewObjectID().Hex() // 记录推送日志ID
			status := edcPush.Status                                         // 状态
			count := retry + 1                                               // 次数
			var result string
			// v2.11 作废
			//var httpRspBody models.HTTPRspBody
			// TODO v2.11新增
			var httpRspBodyNew models.HTTPRspBodyNew
			var err error

			// 组装成功开始推送
			if project.ProjectInfo.EdcSupplier == 1 {
				// edc对接
				// v2.11作废
				//res, rspBody, e := tools.HttpPost(project.EdcUrl, edcPushData.Content) // 调用推送接口
				//TODO v2.11新增
				res, rspBody, e := tools.HttpPostNew(project.EdcUrl, edcPushData.Content) // 调用推送接口
				result = res
				httpRspBodyNew = rspBody
				err = e
			} else if project.ProjectInfo.EdcSupplier == 2 {
				//rave edc对接
				res, rspBody, e := tools.HttpPostRave(project, edcPush) // 调用推送接口
				result = res
				//TODO v2.11新增
				httpRspBodyNew.Status = rspBody.Status
				httpRspBodyNew.Success = rspBody.Success
				httpRspBodyNew.Code = rspBody.Code
				httpRspBodyNew.Error = rspBody.Error
				httpRspBodyNew.Message = rspBody.Message
				// v2.11作废
				//httpRspBodyNew = rspBody
				err = e
			}

			edcPushLogStatus := 0               // TODO v2.11新增
			if httpRspBodyNew.Success == true { // 成功 直接改状态为EDC处理中
				//status = 4			 // 处理中 v2.11作废
				if project.ProjectInfo.EdcSupplier == 2 {
					status = 1
					edcPushLogStatus = 1
				} else {
					if httpRspBodyNew.Data.Status == "1" { // 成功
						status = 1
						edcPushLogStatus = 1
						edcPush.EdcFields = httpRspBodyNew.Data.SaveField
						edcPush.EecErrorCode = 200 // 成功了edc没有返回errorCode,只能IRT这边自己去定义
					} else if httpRspBodyNew.Data.Status == "2" { // 失败
						status = 2           // 失败
						edcPushLogStatus = 5 // 入库失败
						edcPush.EdcFields = httpRspBodyNew.Data.SaveField
						edcPush.EecErrorCode = httpRspBodyNew.Data.ErrorCode
					} else { // 没有任何被处理的内容
						status = 5           // 不处理
						edcPushLogStatus = 6 // 不处理
					}
				}
				edcPushData.Result = result
				update = bson.M{
					"$set": bson.M{
						"project_site_id": edcPushData.ProjectSiteID,
						"status":          status,               // 修改状态为处理中
						"content":         edcPushData.Content,  // 重新放入数据
						"retry":           count,                // 修改重试次数
						"send_time":       edcPushData.SendTime, // 发送时间
						"result":          edcPushData.Result,   // 返回结果
						"http_rsp_body":   httpRspBodyNew,       // edc返回body
						"edc_fields":      edcPush.EdcFields,
						"edc_error_code":  edcPush.EecErrorCode,
					},
				}
			} else { // 失败
				if err != nil {
					edcPushData.Result = err
				} else {
					edcPushData.Result = result
				}

				if retry >= 3 { //最后一次还是失败，就直接把推送状态改为失败
					status = 2
					edcPushLogStatus = 2
				}

				// 添加推送记录信息
				update = bson.M{
					"$set": bson.M{
						"project_site_id": edcPushData.ProjectSiteID,
						"status":          status,               // 修改状态
						"content":         edcPushData.Content,  // 重新放入数据
						"retry":           count,                // 修改重试次数
						"send_time":       edcPushData.SendTime, // 发送时间
						"result":          edcPushData.Result,   // 返回结果
						"http_rsp_body":   httpRspBodyNew,       // edc返回body
					},
				}
			}

			// 修改推送记录信息StudyEventOid
			_, err = tools.Database.Collection("edc_push").UpdateOne(sctx, bson.M{"_id": edcPush.ID}, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 修改推送日志(把之前推送记录里面的推送中改为推送失败)
			edcPushLogUpdate := bson.M{
				"$set": bson.M{
					"status": 2, // 状态改为推送失败
				},
			}
			_, err = tools.Database.Collection("edc_push_log").UpdateMany(sctx, bson.M{"edc_push_id": edcPush.ID, "status": 0}, edcPushLogUpdate)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 添加推送日志信息
			err = AddEdcPushLog(sctx, edcPush, edcPushLogStatus, count, edcPushData, httpRspBodyNew)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 随机数据组装
func RandomDataAssembly(ep models.EdcPush, attribute models.Attribute, projectSite models.ProjectSite) models.EdcPush {
	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串
	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}

	var content = models.Content{
		Env:              ep.Content.Env,
		Project:          ep.Content.Project,
		Sign:             md5Str,
		Site:             projectSite.Number,
		IrtSubjectID:     ep.Content.IrtSubjectID,
		ReplaceSubjectID: ep.Content.ReplaceSubjectID,
		ReplaceSubjectNo: ep.Content.ReplaceSubjectNo,
		EdcPushId:        ep.Content.EdcPushId,
		SubjectNo:        ep.Content.SubjectNo,
		SubjectNoPrefix:  subjectNoPrefix,
		Timestamp:        timestamp,
		Type:             "",
		//AsyncFlag:    ep.Content.AsyncFlag,
		Remarks:     ep.Content.Remarks,
		Cohort:      ep.Content.Cohort,
		SubjectData: ep.Content.SubjectData,
		OldCohort:   ep.Content.OldCohort,
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:            ep.ID,
		CustomerID:    ep.CustomerID,
		ProjectID:     ep.ProjectID,
		EnvironmentID: ep.EnvironmentID,
		CohortID:      ep.CohortID,
		ProjectSiteID: projectSite.ID,
		OID:           ep.OID,
		Content:       content,
	}
	return edcPush
}

// 发药数据组装
func DispensingDataAssembly(ep models.EdcPush, attribute models.Attribute, projectSite models.ProjectSite) models.EdcPush {
	timestamp := time.Now().UnixMilli()
	md5Str := GetMD5Hash(timestamp) // 签名串

	subjectNoPrefix := ""
	// 追加受试者号前缀
	if attribute.AttributeInfo.Prefix && attribute.AttributeInfo.PrefixExpression != "" {
		subjectNoPrefix = strings.ReplaceAll(attribute.AttributeInfo.PrefixExpression, "{siteNO}", projectSite.Number)
	}
	var content = models.Content{
		Env:             ep.Content.Env,
		Project:         ep.Content.Project,
		Sign:            md5Str,
		Site:            projectSite.Number,
		IrtSubjectID:    ep.Content.IrtSubjectID,
		EdcPushId:       ep.Content.EdcPushId,
		SubjectNo:       ep.Content.SubjectNo,
		SubjectNoPrefix: subjectNoPrefix,
		Timestamp:       timestamp,
		Type:            ep.Content.Type,
		//AsyncFlag:    ep.Content.AsyncFlag,
		Remarks:     ep.Content.Remarks,
		Cohort:      ep.Content.Cohort,
		SubjectData: ep.Content.SubjectData,
	}

	// 数据组装
	var edcPush = models.EdcPush{
		ID:            ep.ID,
		CustomerID:    ep.CustomerID,
		ProjectID:     ep.ProjectID,
		EnvironmentID: ep.EnvironmentID,
		CohortID:      ep.CohortID,
		ProjectSiteID: projectSite.ID,
		OID:           ep.OID,
		Content:       content,
	}
	return edcPush
}

// 添加推送记录信息
func AddEdcPushLog(sctx mongo.SessionContext, edcPush models.EdcPush, edcPushStatus int, count int, edcPushData models.EdcPush, httpRspBodyNew models.HTTPRspBodyNew) error {
	id, _ := primitive.ObjectIDFromHex(edcPushData.Content.EdcPushLogId)
	var edcPushLog = models.EdcPushLog{
		ID:            id,
		CustomerID:    edcPush.CustomerID,
		ProjectID:     edcPush.ProjectID,
		EnvironmentID: edcPush.EnvironmentID,
		CohortID:      edcPush.CohortID,
		EdcPushID:     edcPush.ID,
		ProjectSiteID: edcPushData.ProjectSiteID,
		OID:           edcPush.OID,
		Content:       edcPushData.Content,
		PushMode:      edcPush.PushMode,
		Source:        edcPush.Source,
		SourceType:    edcPush.SourceType,
		SerialNumber:  edcPush.SerialNumber,
		Status:        edcPushStatus,
		Retry:         count,
		SendTime:      edcPushData.SendTime,
		Result:        edcPushData.Result,
		HttpRspBody:   httpRspBodyNew,
		EecErrorCode:  edcPush.EecErrorCode,
	}
	_, err := tools.Database.Collection("edc_push_log").InsertOne(sctx, edcPushLog)
	if err != nil {
		return err
	}
	return nil
}

// 获取签名串
func GetMD5Hash(time int64) string {
	hash := md5.Sum([]byte(fmt.Sprint("ClinflashIRT", time)))
	return hex.EncodeToString(hash[:])
}

// EdcPushTaskExecutor 定义EDC任务执行接口(非重试任务)
type EdcPushTaskExecutor interface {
	SubjectRandomPush(
		subjectId primitive.ObjectID,
		sourceType int,
		now time.Duration,
		replaceSubjectId string,
		replaceSubjectNo string,
		oldCohort string,
	) error

	SubjectDispensingPush(
		dispensingId primitive.ObjectID,
		sourceType int,
		now time.Duration,
	) error
}
