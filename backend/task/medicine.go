package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// MedicineExpiredTask 研究产品过有效期，自动失效
func MedicineExpiredTask() error {
	defer tools.DeferReturn("MedicineExpiredTask")

	type ProjectTimeZone struct {
		ID       primitive.ObjectID `bson:"_id"`
		TimeZone string             `bson:"timeZone"`
	}
	var projects []ProjectTimeZone
	// 查询
	cursor, err := tools.Database.Collection("project").Find(nil, bson.M{"status": bson.M{"$ne": 2}}, &options.FindOptions{
		Projection: bson.M{
			"_id":      1,
			"timeZone": bson.M{"$ifNull": bson.A{"$info.timeZoneStr", "8"}},
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(nil, &projects); err != nil {
		return errors.WithStack(err)
	}

	projectIDs := []primitive.ObjectID{}
	projectTimeZoneMap := map[primitive.ObjectID]float64{}
	for _, project := range projects {
		projectIDs = append(projectIDs, project.ID)
		timeZone, _ := strconv.ParseFloat(project.TimeZone, 64)
		projectTimeZoneMap[project.ID] = timeZone
	}

	//过期的研究产品
	var freezeData []models.Medicine
	afterOneDate := time.Now().UTC().AddDate(0, 0, 1).Format("2006-01-02")
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"project_id":      bson.M{"$in": projectIDs},
			"status":          1,
			"expiration_date": bson.M{"$lte": afterOneDate},
		}}},
		{{Key: "$match", Value: bson.M{
			"$expr": bson.M{
				"$and": []bson.M{
					{"$ne": []interface{}{"$expiration_date", nil}},
					{"$ne": []interface{}{"$expiration_date", ""}},
				},
			},
		}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"id":              "$_id",
					"customer_id":     1,
					"project_id":      1,
					"env_id":          1,
					"name":            1,
					"number":          1,
					"expiration_date": 1,
				},
			},
		},
	}
	var data []map[string]interface{}

	cursor, err = tools.Database.Collection("medicine").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(nil, &data); err != nil {
		return errors.WithStack(err)
	}
	envOIDs := []string{}

	for _, medicine := range data {
		timeZone := projectTimeZoneMap[medicine["project_id"].(primitive.ObjectID)]
		hours := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		currDate := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")
		expirationDate := medicine["expiration_date"].(string)
		if currDate >= expirationDate {
			freezeData = append(freezeData, models.Medicine{
				ID:     medicine["id"].(primitive.ObjectID),
				Number: medicine["number"].(string),
			})
			envOIDs = append(envOIDs, medicine["env_id"].(primitive.ObjectID).Hex())
		}
	}
	//研究产品轨迹
	var histories = make([]interface{}, len(freezeData))

	var medicineIds = make([]primitive.ObjectID, len(freezeData))
	for index, medicine := range freezeData {
		medicineIds[index] = medicine.ID
		//轨迹内容
		history := models.History{
			Key:  "history.medicine.expired",
			OID:  medicine.ID,
			Data: bson.M{"packNumber": medicine.Number},
			Time: time.Duration(time.Now().Unix()),
			User: "System（Automatic Calculate）",
		}
		histories[index] = history
	}

	//更新已过期的研究产品
	update := bson.M{
		"$set": bson.M{
			"status": 7,
		},
	}
	if _, err := tools.Database.Collection("medicine").UpdateMany(nil, bson.M{"_id": bson.M{"$in": medicineIds}}, update); err != nil {
		return errors.WithStack(err)
	}

	//研究产品操作轨迹
	if len(histories) > 0 {
		_, err = tools.Database.Collection("history").InsertMany(nil, histories)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	// 未研究产品自动过期
	err = OtherMedicineExpired(projectIDs, projectTimeZoneMap)
	if err != nil {
		return errors.WithStack(err)
	}

	// 实时订单核查

	//envOIDs = slice.Unique(envOIDs)
	//
	//err = AlarmMedicine(2, envOIDs)
	//if err != nil {
	//	return errors.WithStack(err)
	//}
	return nil
}

// ReminderMedicinesValidDays 研究产品有效期天数提醒
func ReminderMedicinesValidDays() error {
	//查询项目属性为“发药”的项目
	defer tools.DeferReturn("ReminderMedicinesValidDays")

	//仓库
	storehousePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}, "deleted": 2}}},
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"info.dispensing": true}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "let": bson.M{"env_id": "$env_id"}, "pipeline": storehousePipeline, "as": "storehouses"}}},
		{{Key: "$unwind", Value: "$storehouses"}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: "$project"}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":            0,
					"id":             "$_id",
					"customer_id":    1,
					"project_id":     1,
					"env_id":         1,
					"storehouseIds":  "$storehouses._id",
					"medicine_infos": "$storehouses.medicine_infos",
					"projectStatus":  "$project.status",
				},
			},
		},
		{{Key: "$unwind", Value: "$medicine_infos"}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$gt": bson.A{"$medicine_infos.validity_reminder", 0}}, "projectStatus": bson.M{"$ne": 2}}}},
	}
	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("attribute").Aggregate(nil, pipeline)

	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(nil, &data); err != nil {
		return errors.WithStack(err)
	}

	//中心
	sitePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"info.dispensing": true}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "let": bson.M{"env_id": "$env_id"}, "pipeline": storehousePipeline, "as": "site"}}},
		{{Key: "$unwind", Value: "$site"}},
		{{Key: "$lookup", Value: bson.M{"from": "supply_plan_medicine", "localField": "site.supply_plan_id", "foreignField": "supply_plan_id", "as": "supply_plan_medicine"}}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":                  0,
					"id":                   "$_id",
					"customer_id":          1,
					"project_id":           1,
					"env_id":               1,
					"site":                 "$site._id",
					"supply_plan_medicine": "$supply_plan_medicine.info",
					"project":              bson.M{"$first": "$project"},
				},
			},
		},
		{{Key: "$match", Value: bson.M{"supply_plan_medicine": bson.M{"$exists": true}}}},
		{{Key: "$unwind", Value: "$supply_plan_medicine"}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"id":                1,
					"project_id":        1,
					"env_id":            1,
					"site":              1,
					"medicine":          "$supply_plan_medicine.medicine_name",
					"validity_reminder": "$supply_plan_medicine.validity_reminder",
					"projectStatus":     "$project.status",
				},
			},
		},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$gt": bson.A{"$validity_reminder", 0}}, "projectStatus": bson.M{"$ne": 2}}}},
	}

	var siteData []map[string]interface{}
	siteCursor, err := tools.Database.Collection("attribute").Aggregate(nil, sitePipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := siteCursor.All(nil, &siteData); err != nil {
		return errors.WithStack(err)
	}

	//项目环境下仓库
	//把每个仓库下需要提醒的研究产品，放到map中，用于发邮件的时候，每个用户分配的仓库不同
	storehouseMedicinesMap := make(map[primitive.ObjectID][]map[string]interface{})
	siteMedicinesMap := make(map[primitive.ObjectID][]map[string]interface{})
	needEnvs := make(map[primitive.ObjectID]primitive.ObjectID)
	envIDStore := map[string]bool{}
	for _, envData := range data {
		var projectID = envData["project_id"].(primitive.ObjectID)
		var envID = envData["env_id"].(primitive.ObjectID)

		//查询仓库研究产品有效期提醒天数的配置
		if infos, ok := envData["medicine_infos"].(map[string]interface{}); ok {
			envStr := envData["env_id"].(primitive.ObjectID).String() + envData["storehouseIds"].(primitive.ObjectID).String() + infos["medicine_name"].(string)
			if _, ok := envIDStore[envStr]; ok {
				continue
			} else {
				envIDStore[envStr] = true
			}
			//根据项目环境和仓库查找需要提醒的研究产品
			storehouseIDS := envData["storehouseIds"].(primitive.ObjectID)
			medicineName := infos["medicine_name"].(string)
			validityReminder := int(infos["validity_reminder"].(int32))
			date := 1
			if validityReminder > 5 {
				date = validityReminder - 4
			}
			timeZone, err := tools.GetTimeZone(projectID)
			if err != nil {
				return errors.WithStack(err)
			}
			hour := time.Duration(timeZone)
			minute := time.Duration((timeZone - float64(hour)) * 60)
			duration := hour*time.Hour + minute*time.Minute
			dateStart := time.Now().AddDate(0, 0, date).UTC().Add(duration).Format("2006-01-02")
			dateEnd := time.Now().AddDate(0, 0, validityReminder).UTC().Add(duration).Format("2006-01-02")
			medicineFilter := bson.M{"$and": bson.A{
				bson.M{"expiration_date": bson.M{"$ne": nil}},
				bson.M{"expiration_date": bson.M{"$ne": ""}},
				bson.M{"expiration_date": bson.M{"$gte": dateStart}},
				bson.M{"expiration_date": bson.M{"$lte": dateEnd}},
				bson.M{"env_id": envID, "storehouse_id": storehouseIDS, "name": medicineName, "status": 1},
			}}

			medicinePipeline := mongo.Pipeline{
				{{Key: "$match", Value: medicineFilter}},
				{{Key: "$lookup", Value: bson.M{"from": "project_storehouse", "localField": "storehouse_id", "foreignField": "_id", "as": "projectStorehouse"}}},
				{
					{
						Key: "$project",
						Value: bson.M{
							"_id":               0,
							"id":                "$_id",
							"name":              1,
							"number":            1,
							"expiration_date":   1,
							"storehouse_id":     1,
							"env_id":            1,
							"projectStorehouse": bson.M{"$first": "$projectStorehouse"},
							"batch_number":      1,
						},
					},
				},
				{
					{
						Key: "$project",
						Value: bson.M{
							"id":            1,
							"name":          1,
							"number":        1,
							"expiryDate":    "$expiration_date",
							"storehouse_id": 1,
							"env_id":        1,
							"institute_id":  "$projectStorehouse.storehouse_id",
							"batch_number":  1,
						},
					},
				},
				{{Key: "$lookup", Value: bson.M{"from": "storehouse", "localField": "institute_id", "foreignField": "_id", "as": "storehouse"}}},
				{
					{
						Key: "$project",
						Value: bson.M{
							"id":            1,
							"name":          1,
							"number":        1,
							"expiryDate":    1,
							"storehouse_id": 1,
							"env_id":        1,
							"storehouse":    bson.M{"$first": "$storehouse"},
							"batch_number":  1,
						},
					},
				},
				{
					{
						Key: "$project",
						Value: bson.M{
							"name":            1,
							"number":          1,
							"expiryDate":      1,
							"storehouse_id":   1,
							"env_id":          1,
							"instituteName":   "$storehouse.name",
							"instituteNameEn": "$storehouse.name",
							"instituteNumber": "-",
							"batch_number":    1,
						},
					},
				},
			}

			cursor, err = tools.Database.Collection("medicine").Aggregate(nil, medicinePipeline)
			if err != nil {
				return errors.WithStack(err)
			}
			//cursor, err := tools.Database.Collection("medicine").Find(nil, medicineFilter)
			var reminderMedicines []map[string]interface{}

			if err = cursor.All(nil, &reminderMedicines); err != nil {
				return errors.WithStack(err)
			}

			if len(reminderMedicines) > 0 {
				if _, ok := needEnvs[reminderMedicines[0]["env_id"].(primitive.ObjectID)]; ok {
				} else {
					needEnvs[reminderMedicines[0]["env_id"].(primitive.ObjectID)] = reminderMedicines[0]["env_id"].(primitive.ObjectID)
				}
			}

			//按照仓库ID存放研究产品
			for _, medicine := range reminderMedicines {
				storehouseID := medicine["storehouse_id"].(primitive.ObjectID)
				if _, ok := storehouseMedicinesMap[storehouseID]; ok {
					existMedicine := storehouseMedicinesMap[storehouseID]
					existMedicine = append(existMedicine, medicine)
					storehouseMedicinesMap[storehouseID] = existMedicine
				} else {
					storehouseMedicinesMap[storehouseID] = []map[string]interface{}{medicine}
				}
			}
		}
	}
	envIDSite := map[string]bool{}
	for _, siteData := range siteData {
		envStr := siteData["env_id"].(primitive.ObjectID).String() + siteData["site"].(primitive.ObjectID).String() + siteData["medicine"].(string)
		if _, ok := envIDSite[envStr]; ok {
			continue
		} else {
			envIDSite[envStr] = true
		}
		// 项目
		var projectID = siteData["project_id"].(primitive.ObjectID)
		var envID = siteData["env_id"].(primitive.ObjectID)
		siteID := siteData["site"].(primitive.ObjectID)
		medicineName := siteData["medicine"].(string)
		validityReminder := int(siteData["validity_reminder"].(int32))
		date := 1
		if validityReminder > 5 {
			date = validityReminder - 4
		}
		timeZone, err := tools.GetTimeZone(projectID)
		if err != nil {
			return errors.WithStack(err)
		}
		hour := time.Duration(timeZone)
		minute := time.Duration((timeZone - float64(hour)) * 60)
		duration := hour*time.Hour + minute*time.Minute
		dateStart := time.Now().AddDate(0, 0, date).UTC().Add(duration).Format("2006-01-02")
		dateEnd := time.Now().AddDate(0, 0, validityReminder).UTC().Add(duration).Format("2006-01-02")
		medicineFilter := bson.M{"$and": bson.A{
			bson.M{"expiration_date": bson.M{"$ne": nil}},
			bson.M{"expiration_date": bson.M{"$ne": ""}},
			bson.M{"expiration_date": bson.M{"$gte": dateStart}},
			bson.M{"expiration_date": bson.M{"$lte": dateEnd}},
			bson.M{"env_id": envID, "site_id": siteID, "name": medicineName, "status": 1},
		}}

		medicinePipeline := mongo.Pipeline{
			{{Key: "$match", Value: medicineFilter}},
			{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
			{{Key: "$unwind", Value: "$project_site"}},
			{
				{
					Key: "$project",
					Value: bson.M{
						"name":            1,
						"number":          1,
						"expiryDate":      "$expiration_date",
						"site_id":         1,
						"env_id":          1,
						"instituteName":   models.ProjectSiteNameZhLookUpBson(),
						"instituteNameEn": models.ProjectSiteNameEnLookUpBson(),
						"instituteNumber": "$project_site.number",
						"batch_number":    1,
					},
				},
			},
		}

		cursor, err := tools.Database.Collection("medicine").Aggregate(nil, medicinePipeline)

		//cursor, err := tools.Database.Collection("medicine").Find(nil, medicineFilter)
		var reminderMedicines []map[string]interface{}
		if err != nil {
			return errors.WithStack(err)
		}
		if err := cursor.All(nil, &reminderMedicines); err != nil {
			return errors.WithStack(err)
		}

		if len(reminderMedicines) > 0 {
			if _, ok := needEnvs[reminderMedicines[0]["env_id"].(primitive.ObjectID)]; ok {
			} else {
				needEnvs[reminderMedicines[0]["env_id"].(primitive.ObjectID)] = reminderMedicines[0]["env_id"].(primitive.ObjectID)
			}
		}

		//按照中心ID存放研究产品
		if _, ok := siteMedicinesMap[siteID]; ok {
			existMedicine := siteMedicinesMap[siteID]
			existMedicine = append(existMedicine, reminderMedicines...)
			siteMedicinesMap[siteID] = existMedicine
		} else {
			siteMedicinesMap[siteID] = append(siteMedicinesMap[siteID], reminderMedicines...)
		}
	}

	projectPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$project_id"}}}}},
	}

	var needsEnvs []primitive.ObjectID
	for _, v := range needEnvs {
		needsEnvs = append(needsEnvs, v)
	}

	if len(needEnvs) <= 0 {
		return nil
	}

	envPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"info.dispensing": true, "env_id": bson.M{"$in": needsEnvs}}}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "let": bson.M{"project_id": "$project_id"}, "pipeline": projectPipeline, "as": "project"}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":         0,
					"id":          "$_id",
					"customer_id": 1,
					"project_id":  1,
					"env_id":      1,
					"project":     bson.M{"$first": "$project"},
				},
			},
		},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":           0,
					"id":            "$_id",
					"customer_id":   1,
					"project_id":    1,
					"env_id":        1,
					"projectNumber": "$project.info.number",
					"projectName":   "$project.info.name",
					"env": bson.M{
						"$filter": bson.M{
							"input": "$project.envs",
							"as":    "item",
							"cond":  bson.M{"$eq": bson.A{"$$item.id", "$env_id"}},
						},
					},
				},
			},
		},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":           0,
					"id":            "$_id",
					"customer_id":   1,
					"project_id":    1,
					"env_id":        1,
					"projectNumber": 1,
					"projectName":   1,
					"env":           bson.M{"$first": "$env"},
				},
			},
		},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":           0,
					"id":            "$_id",
					"customer_id":   1,
					"project_id":    1,
					"env_id":        1,
					"projectNumber": 1,
					"projectName":   1,
					"envName":       "$env.name",
				},
			},
		},
	}
	var envData []map[string]interface{}
	envCursor, err := tools.Database.Collection("attribute").Aggregate(nil, envPipeline)
	if err != nil {
		return errors.WithStack(err)

	}
	if err := envCursor.All(nil, &envData); err != nil {
		return errors.WithStack(err)
	}
	// 过滤cohort 在随机 重复的env
	envIDMails := map[string]bool{}
	for _, env := range envData {
		envStr := env["env_id"].(primitive.ObjectID).String()
		if _, ok := envIDMails[envStr]; ok {
			continue
		} else {
			envIDMails[envStr] = true
		}
		//查询该项目环境下的用户
		customerID := env["customer_id"].(primitive.ObjectID)
		projectID := env["project_id"].(primitive.ObjectID)
		envID := env["env_id"].(primitive.ObjectID)

		//查询发送的邮箱
		// TODO 通用 研究产品有效期通知邮件 模版修改
		sendEmail, err := tools.GetRoleUsersMail(projectID, envID, "notice.medicine.reminder")
		if err != nil {
			return errors.WithStack(err)
		}
		//sendEmail = []string{"<EMAIL>"}
		mails := []interface{}{}
		mailEnvs := []interface{}{}
		for _, send := range sendEmail {

			//查询该用户有哪些角色，如果有study角色，就是所有的数据
			userRolePipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"env_id": envID}}},
				{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "user_id", "foreignField": "_id", "as": "user"}}},
				{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "roles", "foreignField": "_id", "as": "roles"}}},
				{{Key: "$match", Value: bson.M{"roles.scope": "study", "user.0.info.email": send}}},
			}
			var userRolesData []map[string]interface{}
			userRoleCursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, userRolePipeline)
			if err != nil {
				return errors.WithStack(err)
			}
			if err := userRoleCursor.All(nil, &userRolesData); err != nil {
				return errors.WithStack(err)
			}

			var results [][]interface{}
			if len(userRolesData) > 0 { //存在study角色
				//查询该项目下的仓库
				var storeHouseData []models.ProjectStorehouse
				storehouseMatch := bson.M{
					"env_id":  envID,
					"deleted": 2,
				}

				cursor, err := tools.Database.Collection("project_storehouse").Find(nil, storehouseMatch)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &storeHouseData)
				if err != nil {
					return errors.WithStack(err)
				}
				for _, depot := range storeHouseData {
					if _, ok := storehouseMedicinesMap[depot.ID]; ok {
						var resultsItem []interface{}
						storehouseMedicines := storehouseMedicinesMap[depot.ID]
						for _, m := range storehouseMedicines {
							resultsItem = append(resultsItem, m)
						}
						if len(resultsItem) > 0 {
							results = append(results, resultsItem)
						}
					}
				}
				//查询该项目下的中心
				var siteData []models.ProjectSite
				siteMatch := bson.M{
					"env_id":  envID,
					"deleted": 2,
				}

				siteCursor, err := tools.Database.Collection("project_site").Find(nil, siteMatch)
				if err != nil {
					return errors.WithStack(err)
				}
				err = siteCursor.All(nil, &siteData)
				if err != nil {
					return errors.WithStack(err)
				}
				for _, site := range siteData {
					if _, ok := siteMedicinesMap[site.ID]; ok {
						var resultsItem []interface{}
						siteMedicines := siteMedicinesMap[site.ID]
						for _, m := range siteMedicines {
							resultsItem = append(resultsItem, m)
						}
						if len(resultsItem) > 0 {
							results = append(results, resultsItem)
						}
					}
				}
			} else {

				//查询该项目下分配的仓库
				var storeHouseData []models.UserDepot
				storehouseMatch := mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"env_id": envID}}},
					{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "user_id", "foreignField": "_id", "as": "user"}}},
					{{Key: "$match", Value: bson.M{"user.0.info.email": send}}},
				}

				cursor, err := tools.Database.Collection("user_depot").Aggregate(nil, storehouseMatch)
				if err != nil {
					return errors.WithStack(err)
				}
				err = cursor.All(nil, &storeHouseData)
				if err != nil {
					return errors.WithStack(err)

				}
				for _, depot := range storeHouseData {
					if _, ok := storehouseMedicinesMap[depot.DepotID]; ok {
						var resultsItem []interface{}
						storehouseMedicines := storehouseMedicinesMap[depot.DepotID]
						for _, m := range storehouseMedicines {
							resultsItem = append(resultsItem, m)
						}
						if len(resultsItem) > 0 {
							results = append(results, resultsItem)
						}
					}
				}
				//查询该项目下分配的中心
				var siteData []models.UserSite
				siteMatch := mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"env_id": envID}}},
					{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "user_id", "foreignField": "_id", "as": "user"}}},
					{{Key: "$match", Value: bson.M{"user.0.info.email": send}}},
				}

				siteCursor, err := tools.Database.Collection("user_site").Aggregate(nil, siteMatch)
				if err != nil {
					return errors.WithStack(err)
				}
				err = siteCursor.All(nil, &siteData)
				if err != nil {
					return errors.WithStack(err)
				}
				for _, site := range siteData {
					if _, ok := siteMedicinesMap[site.ID]; ok {
						var resultsItem []interface{}
						siteMedicines := siteMedicinesMap[site.ID]
						for _, m := range siteMedicines {
							resultsItem = append(resultsItem, m)
						}
						if len(resultsItem) > 0 {
							results = append(results, resultsItem)
						}
					}
				}

			}

			if len(results) <= 0 {
				continue
			}

			for _, result := range results {
				//判断该用户分配了哪些仓库和中心
				floor := func(item interface{}) string {
					return item.(map[string]interface{})["batch_number"].(string) + item.(map[string]interface{})["expiryDate"].(string)
				}
				res := slice.GroupWith(result, floor)
				medicineItems := []map[string]interface{}{}
				for _, item := range res {
					medicineItem := map[string]interface{}{}
					var medicineNumber []string
					for _, value := range item {
						medicineNumber = append(medicineNumber, value.(map[string]interface{})["number"].(string))
					}
					sort.Sort(sort.StringSlice(medicineNumber))
					medicineItem["batchNumber"] = item[0].(map[string]interface{})["batch_number"]
					medicineItem["number"] = strings.Join(medicineNumber, ",")
					medicineItem["count"] = len(medicineNumber)
					medicineItem["expiryDate"] = item[0].(map[string]interface{})["expiryDate"]
					medicineItems = append(medicineItems, medicineItem)
				}
				subjectData := bson.M{
					"projectNumber":   env["projectNumber"],
					"projectName":     env["projectName"],
					"envName":         env["envName"],
					"instituteNumber": result[0].(map[string]interface{})["instituteNumber"],
					"instituteName":   result[0].(map[string]interface{})["instituteName"],
					"instituteNameEn": result[0].(map[string]interface{})["instituteNameEn"],
				}
				contentData := bson.M{
					"projectNumber":   env["projectNumber"],
					"projectName":     env["projectName"],
					"envName":         env["envName"],
					"instituteNumber": result[0].(map[string]interface{})["instituteNumber"],
					"instituteName":   result[0].(map[string]interface{})["instituteName"],
					"instituteNameEn": result[0].(map[string]interface{})["instituteNameEn"],
					"results":         medicineItems,
				}

				mailBodyContet, err := tools.MailBodyContent(nil, envID, "notice.medicine.reminder")
				for key, v := range mailBodyContet {
					contentData[key] = v
				}
				if err != nil {
					return errors.WithStack(err)
				}

				var noticeConfig models.NoticeConfig
				err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
				if err != nil && err != mongo.ErrNoDocuments {
					return errors.WithStack(err)
				}
				langList := make([]string, 0)
				html := "medicine_expire_new.html"
				if noticeConfig.Automatic != 0 {
					if noticeConfig.Automatic == 1 {
						langList = append(langList, "zh")
						html = "medicine_expire_new_zh.html"
					} else if noticeConfig.Automatic == 2 {
						langList = append(langList, "en")
						html = "medicine_expire_new_en.html"
					} else if noticeConfig.Automatic == 3 {
						langList = append(langList, "zh")
						langList = append(langList, "en")
						html = "medicine_expire_new.html"
					}
				} else {
					langList = append(langList, "zh")
					langList = append(langList, "en")
				}

				//插入邮件数据
				mail := models.Mail{
					ID:           primitive.NewObjectID(),
					Subject:      "medicine.expire_title",
					SubjectData:  subjectData,
					Content:      "medicine.expire",
					ContentData:  contentData,
					To:           []string{send},
					Lang:         "en-US",
					LangList:     langList,
					Status:       0,
					CreatedTime:  time.Duration(time.Now().Unix()),
					ExpectedTime: time.Duration(time.Now().Unix()),
					SendTime:     time.Duration(time.Now().Unix()),
					HTML:         html,
				}
				mails = append(mails, mail)
				mailEnvs = append(mailEnvs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     mail.ID,
					CustomerID: customerID,
					ProjectID:  projectID,
					EnvID:      envID,
				})
			}

		}
		//把邮件插入数据库中
		if len(mails) > 0 {
			if _, err := tools.Database.Collection("mail").InsertMany(nil, mails); err != nil {
				return errors.WithStack(err)
			}
		}
		if len(mailEnvs) > 0 {
			if _, err := tools.Database.Collection("mail_env").InsertMany(nil, mailEnvs); err != nil {
				return errors.WithStack(err)
			}
		}
	}
	return nil
}

func OtherMedicineExpired(projectIDs []primitive.ObjectID, projectTimeZoneMap map[primitive.ObjectID]float64) error {
	//过期的研究产品
	afterOneDate := time.Now().UTC().AddDate(0, 0, 1).Format("2006-01-02")
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"project_id":      bson.M{"$in": projectIDs},
			"status":          1,
			"expiration_date": bson.M{"$lte": afterOneDate},
		}}},
		{{Key: "$match", Value: bson.M{
			"$expr": bson.M{
				"$and": []bson.M{
					{"$ne": []interface{}{"$expiration_date", nil}},
					{"$ne": []interface{}{"$expiration_date", ""}},
				},
			},
		}}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"customer_id": "$customer_id", "project_id": "$project_id", "env_id": "$env_id", "storehouse_id": "$storehouse_id", "site_id": "$site_id", "name": "$name", "expiration_date": "$expiration_date", "batch_number": "$batch_number"},
			"count": bson.M{"$sum": 1}, "ids": bson.M{"$push": "$_id"}}}},
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":             0,
					"customer_id":     "$_id.customer_id",
					"project_id":      "$_id.project_id",
					"env_id":          "$_id.env_id",
					"storehouse_id":   "$_id.storehouse_id",
					"site_id":         "$_id.site_id",
					"name":            "$_id.name",
					"batch_number":    "$_id.batch_number",
					"expiration_date": "$_id.expiration_date",
					"count":           1,
					"ids":             1,
				},
			},
		},
	}
	var data []map[string]interface{}
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err := tools.Database.Collection("medicine_others").Aggregate(nil, pipeline, opt)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := cursor.All(nil, &data); err != nil {
		return errors.WithStack(err)
	}
	envOIDs := []string{}

	//研究产品轨迹
	var histories = make([]interface{}, 0)

	var medicineIds = make([]primitive.ObjectID, 0)

	for _, medicine := range data {
		timeZone := projectTimeZoneMap[medicine["project_id"].(primitive.ObjectID)]
		hours := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		currDate := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")
		expirationDate := medicine["expiration_date"].(string)
		if currDate >= expirationDate {
			name := medicine["name"].(string)
			expirationDate := medicine["expiration_date"].(string)
			batchNumber := medicine["batch_number"].(string)
			count := medicine["count"].(int32)
			envOIDs = append(envOIDs, medicine["env_id"].(primitive.ObjectID).Hex())
			ids := medicine["ids"].(primitive.A)
			for _, otherId := range ids {
				otherOID := otherId.(primitive.ObjectID)
				medicineIds = append(medicineIds, otherOID)
			}
			//medicineIds[index] = medicine.ID
			otherMedicine := models.OtherMedicineFreeze{
				CustomerID:    medicine["customer_id"].(primitive.ObjectID),
				ProjectID:     medicine["project_id"].(primitive.ObjectID),
				EnvironmentID: medicine["env_id"].(primitive.ObjectID),
			}
			if medicine["storehouse_id"] != nil && medicine["storehouse_id"].(primitive.ObjectID) != primitive.NilObjectID {
				//otherMedicine.InstituteID = medicine["storehouse_id"].(primitive.ObjectID)
				otherMedicine.InstituteType = 2
			}
			if medicine["site_id"] != nil && medicine["site_id"].(primitive.ObjectID) != primitive.NilObjectID {
				//otherMedicine.InstituteID = medicine["site_id"].(primitive.ObjectID)
				otherMedicine.InstituteType = 1
			}
			otherMedicineInfo := models.FreezeOtherMedicines{
				Name:         name,
				ExpireDate:   expirationDate,
				Batch:        batchNumber,
				Count:        int(count),
				SiteID:       medicine["site_id"].(primitive.ObjectID),
				StorehouseID: medicine["storehouse_id"].(primitive.ObjectID),
			}
			otherMedicineKey, _ := tools.GetOtherMedicineKey(otherMedicine, otherMedicineInfo)
			if otherMedicineKey.ID != primitive.NilObjectID {
				//轨迹内容
				history := models.History{
					Key:  "history.medicine.otherExpired",
					OID:  otherMedicineKey.ID,
					Time: time.Duration(time.Now().Unix()),
					User: "System（Automatic Calculate）",
					Data: bson.M{"name": name, "batch": batchNumber, "expireDate": expirationDate, "count": count},
				}
				histories = append(histories, history)
			}
		}
	}

	//更新已过期的研究产品
	update := bson.M{
		"$set": bson.M{
			"status": 7,
		},
	}
	if _, err := tools.Database.Collection("medicine_others").UpdateMany(nil, bson.M{"_id": bson.M{"$in": medicineIds}}, update); err != nil {
		return errors.WithStack(err)
	}

	//研究产品操作轨迹
	if len(histories) > 0 {
		_, err = tools.Database.Collection("history").InsertMany(nil, histories)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}
