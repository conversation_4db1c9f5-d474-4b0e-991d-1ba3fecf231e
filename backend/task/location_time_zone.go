package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strconv"
	"time"
)

/*
夏令时\冬令时计算
中心0点触发定时任务  每小时跑一次定时任务

*/

// LocationTimeZoneTask ...
func LocationTimeZoneTask() error {

	//查询更新time_zone
	timeZoneList := make([]models.TimeZone, 0)
	cursor, err := tools.Database.Collection("time_zone").Find(nil, bson.M{})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &timeZoneList)
	if err != nil {
		return errors.WithStack(err)
	}

	if timeZoneList != nil && len(timeZoneList) > 0 {
		for _, timeZone := range timeZoneList {
			if len(timeZone.Tz) > 0 {
				location, err := tools.GetLocationTimeZone(timeZone.Tz)
				if err != nil {
					return errors.WithStack(err)
				}

				if location != timeZone.TimeZone {
					lc, err := tools.GetUTCOffsetString(timeZone.Tz)
					if err != nil {
						return errors.WithStack(err)
					}
					//更新project_site中心时区time_zone和tz字段
					if _, err := tools.Database.Collection("project_site").UpdateMany(nil, bson.M{"tz": timeZone.Tz}, bson.M{"$set": bson.M{"time_zone": lc}}); err != nil {
						return errors.WithStack(err)
					}

					//更新visit_notice
					offset, err := strconv.ParseFloat(location, 64)
					if err != nil {
						return errors.WithStack(err)
					}
					if offset > 0 {
						location = "+" + location
					}

					visitNoticeList := make([]models.VisitNotice, 0)
					visitNoticeCursor, err := tools.Database.Collection("visit_notice").Find(nil, bson.M{"tz": timeZone.Tz})
					if err != nil {
						return errors.WithStack(err)
					}
					err = visitNoticeCursor.All(nil, &visitNoticeList)
					if err != nil {
						return errors.WithStack(err)
					}

					if visitNoticeList != nil && len(visitNoticeList) > 0 {
						for _, visitNotice := range visitNoticeList {

							if visitNotice.TimeZoneFloat != offset {
								err = UpdateNotice(nil, 3, visitNotice.EnvID, primitive.NilObjectID, visitNotice.ProjectSiteID, primitive.NilObjectID)
								if err != nil {
									panic(err)
								}
							}

							//recordTime, err := stringTime(visitNotice.BeSendTime)
							//if err != nil {
							//	return errors.WithStack(err)
							//}
							//timeStr := time.Unix(recordTime.Unix(), 0).In(time.FixedZone(fmt.Sprintf("UTC%s", location), offset*60*60)).Format("2006-01-02 15:04:05")
							//if _, err := tools.Database.Collection("visit_notice").UpdateOne(nil, bson.M{"_id": visitNotice.ID}, bson.M{"$set": bson.M{"time_zone": offset, "record_time": timeStr}}); err != nil {
							//	return errors.WithStack(err)
							//}
						}
					}
				}

			}
		}
	}

	return nil
}

func stringTime(str string) (time.Time, error) {
	layout := "2006-01-02 15:04:05"

	t, err := time.Parse(layout, str)
	if err != nil {
		fmt.Println("解析时间出错:", err)
		var emptyTime time.Time
		return emptyTime, errors.WithStack(err)
	}

	fmt.Println("转换后的时间为:", t)
	return t, nil
}
