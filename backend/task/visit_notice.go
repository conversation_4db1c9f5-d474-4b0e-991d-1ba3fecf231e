package task

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"math"
	"time"
)

/*

if 开启访视通知
	0点查询 所有受试者
		写入visit notice 需要通知的访视
		根据配置写入通知
	改访视配置、通知配置、随机、发药(发药、全部取回、撤销)、改中心配置(涉及换时区)触发新的计算规则
	if 改通知配置、改访视配置
		删除之前的通知(如果实时通知刚好查询更新到、涉及事务)
		根据配置写入通知
	if 改中心、涉及到换时区
		删除之前修改中心受试者的通知
		根据新的配置写入新的通知
	if受试者发药、随机
		对未触发通知的受试者重新计算根
		据新的配置写入新的通知

for 受试者
	for 单个受试者发药数据
	读取配置
		if 当天 < 最小窗口期 && 当天 + N天 > 最小窗口期
			通知
*/

func VisitNoticeTask() error {
	var projectNotices []models.ProjectNotice
	cursor, err := tools.Database.Collection("project_notice").Find(nil, bson.M{
		"open": true,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectNotices)
	if err != nil {
		return errors.WithStack(err)
	}

	for _, projectNotice := range projectNotices {
		if len(projectNotice.NoticeRule) == 0 || len(projectNotice.RoleUser) == 0 {
			continue
		}
		callback := func(sctx mongo.SessionContext) (interface{}, error) {
			err = getSubjectInfo(sctx, projectNotice, bson.M{"env_id": projectNotice.EnvID}, false)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			return nil, nil
		}
		err := tools.Transaction(callback)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	}
	return nil
}

func getSubjectInfo(sctx mongo.SessionContext, projectNotice models.ProjectNotice, match bson.M, update bool) error {
	var visitCycles []models.VisitCycle
	cursor, err := tools.Database.Collection("visit_cycle").Find(nil, bson.M{
		"env_id": projectNotice.EnvID,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return errors.WithStack(err)
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectNotice.ProjectID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	var projectSites []models.ProjectSite
	cursor, err = tools.Database.Collection("project_site").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"env_id": projectNotice.EnvID}}},
	})
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return errors.WithStack(err)
	}
	var subjectDispensings []models.SubjectDispensing
	if match["subject_id"] != nil {
		match["_id"] = match["subject_id"]
		delete(match, "subject_id")
	}
	match["deleted"] = bson.M{"$ne": true}
	cursor, err = tools.Database.Collection("subject").Aggregate(nil, mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
	})
	err = cursor.All(nil, &subjectDispensings)
	if err != nil {
		return errors.WithStack(err)
	}

	visitNotices := []interface{}{}

	// 角色关联中心
	// TODO 7064
	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
		}
	}

	for _, subject := range subjectDispensings {
		afterRandom := false
		interval := float64(0)
		lastTime := time.Duration(0)
		visitCycle := models.VisitCycle{}
		projectSite := models.ProjectSite{}
		projectSitesP, ok := slice.Find(projectSites, func(index int, item models.ProjectSite) bool {
			return subject.ProjectSiteID == item.ID
		})
		if ok {
			projectSite = *projectSitesP
		}
		visitCycleP, ok := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
			return subject.CohortID == item.CohortID
		})
		if ok {
			visitCycle = *visitCycleP
		}
		//intTimeZone := project.TimeZone
		intTimeZone, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
		if projectSite.TimeZone != "" {
			strTimeZone, _ := tools.GetSiteTimeZoneInfo(projectSite)
			intTimeZone, _ = tools.ParseTimezoneOffset(strTimeZone)
			//intTimeZone, _ = strconv.ParseFloat(strings.Replace(projectSite.TimeZone, "UTC", "", 1), 64)
		}
		firstTime := time.Duration(0)
		for _, dispensing := range subject.Dispensing {
			if !dispensing.VisitSign {
				visitCycleInfo := models.VisitCycleInfo{}
				visitCycleInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
					return item.Number == dispensing.VisitInfo.Number
				})
				if ok {
					visitCycleInfo = *visitCycleInfoP
				}
				// 计算窗口期最小窗口时间
				// TODO 7064
				if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
					subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
				}
				if firstTime == 0 && dispensing.DispensingTime != 0 && visitCycleInfo.Interval != nil && subject.RandomTime == 0 {
					subject.RandomTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfo.Interval)).Unix())
					firstTime = dispensing.DispensingTime
				}
				period := tools.HandleVisitDate(afterRandom, visitCycle.VisitType, visitCycleInfo, subject.RandomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval)
				if dispensing.Status == 2 {
					interval = 0
					lastTime = dispensing.DispensingTime
				}
				if dispensing.VisitInfo.Random {
					afterRandom = true
				}
				// 在范围内写入通知
				err = formulaDateNotice(project, subject, dispensing, projectSite, period.MinPeriod, projectNotice, intTimeZone, &visitNotices, update)
				if err != nil {
					return errors.WithStack(err)
				}
			}
		}
	}

	// 写入通知
	if len(visitNotices) > 0 {
		err = insertVisitNotice(sctx, visitNotices)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return err
}

func insertVisitNotice(sctx mongo.SessionContext, visitNotices []interface{}) error {
	_, err := tools.Database.Collection("visit_notice").InsertMany(sctx, visitNotices)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func formulaDateNotice(project models.Project, subject models.SubjectDispensing, dispensing models.Dispensing, projectSite models.ProjectSite, periodMin string, notice models.ProjectNotice, intTimeZone float64, visitNotices *[]interface{}, update bool) error {
	/*
		if 当前日期 加入时区计算  当前27号04点  访视27号 02点  utc+12	当天 	 超期
		if 当前日期 加入时区计算  当前26号04点  访视27号 02点  utc+12	提前一天	 生成通知
		if 当前日期 加入时区计算  当前25号04点  访视27号 02点  utc+12	提前二天	 生成通知

		if 当前日期 加入时区计算  当前27号00点  访视27号 12点  utc+8	当天 	 生成通知 生成通知 27号12点通知
		if 当前日期 加入时区计算  当前26号00点  访视27号 12点  utc+8	提前一天	 生成通知 生成通知 26号12点通知
		if 当前日期 加入时区计算  当前25号00点  访视27号 12点  utc+8	提前二天	 未达到

		if 当前日期 加入时区计算  当前26号16点  访视26号 12点  utc-8	当天		  超期
		if 当前日期 加入时区计算  当前25号16点  访视26号 12点  utc-8	提前一天	  生成通知 26号12点通知
		if 当前日期 加入时区计算  当前24号16点  访视26号 12点  utc-8	提前二天   生成通知 25号12点通知



		if 当前日期 加入时区计算  当前26号0点  访视26号 0点  utc+8	当天		 生成通知（边界问题） 01分跑完定时任务
		if 当前日期 加入时区计算  当前25号0点  访视26号 0点  utc+8	提前一天	 生成通知
		if 当前日期 加入时区计算  当前24号0点  访视26号 0点  utc+8	提前二天   未达到 （边界问题） 01分跑完定时任务

		if 当前日期 加入时区计算  当前26号1点  访视26号 01点00分  utc+9	当天		 生成通知（边界问题） 01分跑完定时任务
		if 当前日期 加入时区计算  当前25号1点  访视26号 01点00分  utc+9	提前一天	 生成通知
		if 当前日期 加入时区计算  当前24号1点  访视26号 01点00分  utc+9	提前二天   未达到 （边界问题） 01分跑完定时任务

		if 当前日期 加入时区计算  当前26号1点  访视26号 00点59分  utc+9	当天		 生成通知（边界问题） 01分跑完定时任务
		if 当前日期 加入时区计算  当前25号1点  访视26号 00点59分  utc+9	提前一天	 生成通知
		if 当前日期 加入时区计算  当前24号1点  访视26号 00点59分  utc+9	提前二天   生成通知

		if 当前日期 < 最小窗口期
			if 	当前时间点 < 配置时间点
				当前日期 + N天 >= 访视推送日期
			else
				当前日期 +(N + 1) >= 推送日期
	*/

	// rule 按天数最大的排序  最大的取到 推出循环

	if periodMin == "" {
		return nil
	}
	if dispensing.Status != 1 {
		return nil
	}

	hours := time.Duration(intTimeZone)
	minutes := time.Duration((intTimeZone - float64(hours)) * 60)

	dt := hours*time.Hour + minutes*time.Minute

	now := time.Now().UTC().Add(dt).Truncate(time.Hour)

	if update {
		now = time.Now().UTC().Add(dt)
	}
	hourTime := now.Format("2006-01-02 15:04")
	send := false

	err := slice.SortByField(notice.NoticeRule, "Date", "desc")
	if err != nil {
		return errors.WithStack(err)
	}

	periodMinTime, _ := time.Parse("2006-01-02", periodMin)
	duration := periodMinTime.Sub(now)
	betweenF := math.Ceil(duration.Hours() / 24)
	between := int(betweenF)

	sendDateTime := ""
	template := ""
	day := ""
	for _, rule := range notice.NoticeRule {
		// 整点时间
		if rule.Date < between {
			continue
		}
		Nday := now.AddDate(0, 0, rule.Date).Format("2006-01-02")
		addNday := now.AddDate(0, 0, rule.Date+1).Format("2006-01-02")
		nextDate := now.AddDate(0, 0, 1).Format("2006-01-02")
		if between > 0 { // 当前日期 < 最小窗口期
			if hourTime[11:] < rule.Time[:5] { // 当前时间点 < 配置时间点
				if Nday >= hourTime[:10] {
					sendDateTime = hourTime[:10] + " " + rule.Time // 当天发送
					send = true
					template = rule.Template
					day = convertor.ToString(between)
					//switchNoticeTemplate(rule.Template, convertor.ToString(between))
					break
				}
			} else {
				if addNday >= hourTime[:10] {
					sendDateTime = nextDate + " " + rule.Time // +一天发送
					send = true
					template = rule.Template
					day = convertor.ToString(between)
					//switchNoticeTemplate(rule.Template, convertor.ToString(between))
					break
				}
			}
		}

	}
	userIDs := []primitive.ObjectID{}
	for _, users := range notice.RoleUser {
		for _, user := range users.Users {
			userIDs = append(userIDs, user)
		}
	}
	userIDs = slice.Unique(userIDs)

	// 修改配置后 触发的时间在0点之后 则由定时任务完成
	nextDay := now.AddDate(0, 0, 1)
	nextDayMidnight := nextDay.Format("2006-01-02")
	if sendDateTime != "" && sendDateTime[:10] == nextDayMidnight && update {
		return nil
	}
	if send {
		// 时区换算
		t, err := time.Parse("2006-01-02 15:04", sendDateTime[:16])
		if err != nil {
			return errors.WithStack(err)
		}
		sendDateTime = t.UTC().Add(-dt).Format("2006-01-02 15:04")
		*visitNotices = append(*visitNotices, models.VisitNotice{
			ID:            primitive.NewObjectID(),
			CustomerID:    dispensing.CustomerID,
			ProjectID:     dispensing.ProjectID,
			EnvID:         dispensing.EnvironmentID,
			CohortID:      dispensing.CohortID,
			ProjectSiteID: projectSite.ID, // test
			SubjectID:     dispensing.SubjectID,
			DispensingID:  dispensing.ID,
			Status:        0,
			Content: models.VisitNoticeContent{
				Template:      template,
				Day:           day,
				ProjectNumber: project.Number,
				SiteNumber:    projectSite.Number,
				SubjectNumber: subject.Info[0].Value.(string),
				VisitNumber:   dispensing.VisitInfo.Number,
			},
			TimeZoneFloat: intTimeZone, // 受试者时区
			BeSendTime:    sendDateTime,
			Tz:            projectSite.Tz, // 受试者时区
			NoticeUser:    userIDs,
			PushType:      1,
		})
	}

	return nil
}

func switchNoticeTemplate(value string, config map[string]interface{}) string {
	switch value {
	case "a":
		return config["data"].(map[string]interface{})["a"].(string)
	case "b":
		return config["data"].(map[string]interface{})["b"].(string)
	case "c":
		return config["data"].(map[string]interface{})["c"].(string)
	case "d":
		return config["data"].(map[string]interface{})["d"].(string)
	}
	return ""
}

func SwitchNoticeContent(lang, value, date string) string {
	switch value {
	case "a":
		return locales.TrWithLang(lang, "app_visit_reminder_content_"+value, map[string]interface{}{"days": date})
	case "b":
		return locales.TrWithLang(lang, "app_visit_reminder_content_"+value)
	case "c":
		return locales.TrWithLang(lang, "app_visit_reminder_content_"+value)
	case "d":
		return locales.TrWithLang(lang, "app_visit_reminder_content_"+value)
	}
	return ""
}

func SendVisitNoticeTask() error {
	now := time.Now().UTC()
	fiveMinutesAgo := now.Add(-5 * time.Minute)
	nowStr := now.Format("2006-01-02 15:04")
	fiveMinutesAgoStr := fiveMinutesAgo.Format("2006-01-02 15:04")
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var settingConfig map[string]interface{}
		err := tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": "irt.visit.sms"}).Decode(&settingConfig)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var visitNotice []models.VisitNotice
		var appTaskNotice []interface{}
		cursor, err := tools.Database.Collection("visit_notice").Find(sctx, bson.M{
			"record_time": bson.M{"$gte": fiveMinutesAgoStr, "$lte": nowStr},
			"status":      0,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &visitNotice)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 推送通知
		for _, notice := range visitNotice {
			user, err := getUserPhone(sctx, notice, notice.PushType)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var sendErr error
			status := 1
			errString := ""
			if notice.PushType == 1 { // app通知
				var userRegistrationIdAppLanguages []models.UserRegistrationIdAppLanguage
				userIDs := []models.UserIds{}
				slice.ForEach(user, func(index int, item models.User) {
					userRegistrationIdAppLanguages = append(userRegistrationIdAppLanguages, models.UserRegistrationIdAppLanguage{
						RegistrationId: item.RegistrationId,
						AppLanguage:    item.AppLanguage,
					})
					userIDs = append(userIDs, models.UserIds{UserID: item.ID})
				})

				// 查询app任务里面是否有当前通知的发药任务
				var task models.WorkTask
				err = tools.Database.Collection("work_task").FindOne(sctx, bson.M{"info.dispensing_id": notice.DispensingID, "info.work_type": 11}).Decode(&task)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				contentEN := SwitchNoticeContent("en-US", notice.Content.Template, notice.Content.Day)
				contentZH := SwitchNoticeContent("zh-CN", notice.Content.Template, notice.Content.Day)
				if !task.ID.IsZero() {
					var dispensing models.Dispensing
					err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": task.Info.DispensingID}).Decode(&dispensing)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					if len(userRegistrationIdAppLanguages) > 0 {

						// 极光推送
						title_zh := locales.TrWithLang("zh-CN", "app_visit_reminder_title")
						title_en := locales.TrWithLang("en-US", "app_visit_reminder_title")
						wtv := models.WorkTaskView{}
						wtv.ID = task.ID
						wtv.CustomerID = task.CustomerID
						wtv.ProjectID = task.ProjectID
						wtv.EnvironmentID = task.EnvironmentID
						wtv.CohortID = task.CohortID
						wtv.Info.FinishTime = task.Info.FinishTime
						wtv.Info.Status = task.Info.Status
						wtv.SubjectId = dispensing.SubjectID.Hex()
						if dispensing.VisitInfo.VisitCycleInfoID != primitive.NilObjectID {
							wtv.VisitCycleId = dispensing.VisitInfo.VisitCycleInfoID.Hex()
						}
						wtv.NoticeType = 2     // 0:任务逾期通知、1:项目助手、2:访视提醒
						wtv.Info.WorkType = 11 // 访视提醒

						// 查询 PlanLeftTime 和 PlanRightTime
						var subject models.Subject
						err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						if strTimeZone == "" {
							zone, err := tools.GetTimeZone(subject.ProjectID)
							if err != nil {
								return nil, err
							}
							//strTimeZone = fmt.Sprintf("UTC%+d", zone)
							strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
						}
						//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
						intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

						match := bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID}
						if dispensing.CohortID != primitive.NilObjectID {
							match = bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}
						}
						var visit models.VisitCycle
						err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visit)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						attribute, err := database.GetAttributeWithEnvCohortID(sctx, dispensing.EnvironmentID, dispensing.CohortID)
						visitCycleMap := map[string]models.VisitCycleInfo{}
						for _, info := range visit.Infos {
							visitCycleMap[info.Number] = info
						}

						// TODO 7064
						isolationProject, baseCohortOID := tools.IsolationProject(models.Project{}, dispensing.EnvironmentID)
						subjectMap := make(map[string]models.Subject)
						if isolationProject {
							subjectMap, err = GetBaseCohortSubjectMap(nil, baseCohortOID, primitive.NilObjectID, subject.Info[0].Value.(string))
						}

						filter := bson.M{"subject_id": dispensing.SubjectID}
						var resDispensing []models.ResDispensing
						cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
							{{Key: "$match", Value: filter}},
							{{Key: "$lookup", Value: bson.M{
								"from": "medicine_order",
								"let": bson.M{
									"dispensing_id": "$_id",
									//"order_number":  "$order",
								},
								"pipeline": bson.A{
									bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
								},

								"as": "medicine_order",
							}}},
							{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
						})
						if err != nil {
							return nil, errors.WithStack(err)
						}
						err = cursor.All(nil, &resDispensing)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						lastTime := time.Duration(0)
						afterRandom := false
						interval := float64(0)
						var period models.Period
						for _, res := range resDispensing {
							if !res.VisitSign {
								if res.Status == 2 {
									interval = 0
									lastTime = res.DispensingTime
								}
								if res.VisitInfo.Random {
									afterRandom = true
								}
								if res.ID == dispensing.ID {

									// TODO 7064
									randomTime := subject.RandomTime
									if isolationProject && subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
										randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
									}
									period = tools.HandlePeriod(afterRandom, visit.VisitType, visitCycleMap[dispensing.VisitInfo.Number], randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
								}
							}
						}
						wtv.PlanLeftTime = ""
						if len(period.MinPeriod) != 0 {
							wtv.PlanLeftTime = timestampConversion(period.MinPeriod)
						}
						wtv.PlanRightTime = ""
						if len(period.MaxPeriod) != 0 {
							wtv.PlanRightTime = timestampConversion(period.MaxPeriod)
						}
						if subject.RandomTime != 0 {
							//timeZone := time.Duration(intTimeZone)
							hours := time.Duration(intTimeZone)
							minutes := time.Duration((intTimeZone - float64(hours)) * 60)
							duration := hours*time.Hour + minutes*time.Minute
							formattedDate := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006.01.02")
							if wtv.PlanLeftTime == "" {
								wtv.PlanLeftTime = formattedDate
							}
							if wtv.PlanRightTime == "" {
								wtv.PlanRightTime = formattedDate
							}
						}
						wtv.RandomNumber = subject.RandomNumber

						//转JSON
						sJson, err := json.Marshal(wtv)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						var registrationId_zh []string
						var registrationId_en []string
						for _, ural := range userRegistrationIdAppLanguages {
							if ural.AppLanguage == "" || ural.AppLanguage == "zh-CN" {
								registrationId_zh = append(registrationId_zh, ural.RegistrationId)
							} else {
								registrationId_en = append(registrationId_en, ural.RegistrationId)
							}
						}

						// 中文推送
						if registrationId_zh != nil && len(registrationId_zh) > 0 {
							//alertBody := contentZH
							sendErr = tools.DataAssembly(registrationId_zh, title_zh, contentZH, string(sJson))
						}
						// 英文推送
						if registrationId_en != nil && len(registrationId_en) > 0 {
							//alertBody := contentEN
							sendErr = tools.DataAssembly(registrationId_en, title_en, contentEN, string(sJson))
						}
					}
					userOIDs := []models.UserIds{}
					for _, id := range notice.NoticeUser {
						userOIDs = append(userOIDs, models.UserIds{UserID: id})
					}

					appTaskNotice = append(appTaskNotice, models.AppTaskNotice{
						ID:            primitive.NewObjectID(),
						CustomerID:    notice.CustomerID,
						ProjectID:     notice.ProjectID,
						EnvironmentID: notice.EnvID,
						NoticeType:    2,
						NoticeTime:    time.Duration(time.Now().Unix()),
						UserIds:       userOIDs,
						ExpireNotice: models.ExpireNotice{
							WorkTaskID:             task.ID,
							WorkType:               12,
							DispensingID:           notice.DispensingID,
							VisitReminderContent:   contentZH,
							VisitReminderContentEn: contentEN,
						},
					})

				} else {
					status = 4
					errString = "旧数据无work_task"
				}

			} else {
				sendIDorPhone := []string{}
				slice.ForEach(user, func(index int, item models.User) {
					sendIDorPhone = append(sendIDorPhone, item.Phone)
				})
				sendIDorPhone = append(sendIDorPhone, notice.PhoneUser...)
				slice.Unique(sendIDorPhone)

				type SMSContent struct {
					Day           string `json:"days,omitempty"`
					ProjectNumber string `json:"projectNumber"`
					SiteNumber    string `json:"siteNumber"`
					SubjectNumber string `json:"subjectNumber"`
					VisitNumber   string `json:"visitNumber"`
				}
				smsMap := SMSContent{
					ProjectNumber: notice.Content.ProjectNumber,
					SiteNumber:    notice.Content.SiteNumber,
					SubjectNumber: notice.Content.SubjectNumber,
					VisitNumber:   notice.Content.VisitNumber,
				}
				if notice.Content.Template == "a" {
					smsMap.Day = notice.Content.Day
				}
				if len(sendIDorPhone) > 0 {
					paramJsonStr, err := convertor.ToJson(smsMap)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					sms := switchNoticeTemplate(notice.Content.Template, settingConfig)
					sendErr = tools.SendVisitNotice(paramJsonStr, sendIDorPhone, sms)
				}
			}
			// 更新状态
			if sendErr != nil {
				status = 2
				errString = errors.WithStack(sendErr).Error()
			}
			update := bson.M{"$set": bson.M{"status": status, "error": errString}}
			if status != 4 {
				update = bson.M{"$set": bson.M{"status": status, "error": errString, "send_time": time.Duration(time.Now().Unix())}}
			}
			_, err = tools.Database.Collection("visit_notice").UpdateOne(sctx,
				bson.M{"_id": notice.ID},
				update,
			)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		if len(appTaskNotice) > 0 {
			_, err = tools.Database.Collection("app_task_notice").InsertMany(sctx, appTaskNotice)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 过滤 获取手机号
func getUserPhone(sctx mongo.SessionContext, visitNotice models.VisitNotice, pushType int) ([]models.User, error) {
	var userIDs []primitive.ObjectID
	if len(visitNotice.NoticeUser) == 0 {
		return nil, nil
	}
	for _, users := range visitNotice.NoticeUser {
		userIDs = append(userIDs, users)
	}
	var user []models.User
	var err error
	match := bson.M{}
	if pushType == 2 {
		// 过滤手机号为空的
		match = bson.M{"_id": bson.M{"$in": userIDs},
			"info.phone": bson.M{"$ne": ""},
		}
	} else {
		// 过滤绑定极光id为空
		match = bson.M{"_id": bson.M{"$in": userIDs},
			"$and": bson.A{
				bson.M{"registration_id": bson.M{"$exists": 1}},
				bson.M{"storehouse_id": bson.M{"$ne": ""}},
			},
		}
	}
	user, err = FilterSiteOrDeptUser(match, visitNotice.EnvID, visitNotice.ProjectSiteID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return user, nil
}

func FilterSiteOrDeptUser(match bson.M, envOID, SiteOID primitive.ObjectID) ([]models.User, error) {
	pipeline := mongo.Pipeline{
		{{"$match", match}},
		{{"$lookup", bson.M{
			"from": "user_project_environment",
			"let": bson.M{
				"id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"env_id": envOID,
					},
				},
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$id"}}},
				},
			},
			"as": "user_project_environment",
		}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_role_permission", "localField": "user_project_environment.roles", "foreignField": "_id", "as": "project_role_permission"}}},

		{{"$lookup", bson.M{
			"from": "user_site",
			"let": bson.M{
				"user_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{
					"$match": bson.M{
						"site_id": SiteOID,
					},
				},
				bson.M{
					"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$$user_id", "$user_id"}}},
				},
			},
			"as": "user_site",
		}}},
		{{"$match", bson.M{
			"$or": bson.A{
				bson.M{"project_role_permission.scope": "study"},
				bson.M{"user_site": bson.M{"$not": bson.M{"$size": 0}}},
			},
		}}},
	}
	cursor, err := tools.Database.Collection("user").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var users []models.User
	err = cursor.All(nil, &users)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return users, nil
}

func UpdateNotice(sctx mongo.SessionContext, types int, envOID, cohortOID, ProjectSiteOID, SubjectOID primitive.ObjectID) error {
	match := bson.M{}
	switch types {
	case 1:
		match = bson.M{"env_id": envOID}
		fmt.Println("修改项目通知配置 修改环境下所有通知") // 修改通知
	case 2:
		match = bson.M{"env_id": envOID, "cohort_id": cohortOID}

		fmt.Println("修改项目访视配置 修改cohort下所有受试者通知")
	case 3:
		match = bson.M{"env_id": envOID, "project_site_id": ProjectSiteOID}

		fmt.Println("修改中心配置 修改中心下受试者数据") // 修改通知时间  超过当前时间就取消
	case 4:
		match = bson.M{"env_id": envOID, "cohort_id": cohortOID, "project_site_id": ProjectSiteOID, "subject_id": SubjectOID}
	}
	err := UpdateVisitNotice(sctx, match)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func UpdateVisitNotice(sctx mongo.SessionContext, match bson.M) error {
	var notice models.ProjectNotice
	err := tools.Database.Collection("project_notice").FindOne(sctx, bson.M{"env_id": match["env_id"]}).Decode(&notice)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	match["status"] = 0
	_, err = tools.Database.Collection("visit_notice").UpdateMany(sctx,
		match,
		bson.M{"$set": bson.M{"status": 3}},
	)
	if err != nil {
		return errors.WithStack(err)
	}
	delete(match, "status")

	if !notice.Open {
		return nil
	}

	err = getSubjectInfo(sctx, notice, match, true)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
