package task

import (
	"clinflash-irt/catalent"
	"clinflash-irt/config"
	"clinflash-irt/tools"
	"strings"

	"github.com/robfig/cron/v3"
	log "github.com/sirupsen/logrus"
)

// Init ..
func Init() {

	c := cron.New()

	if !strings.Contains(config.CLOUD, "https://cloud-pre.clinflash.com") {

		//每30秒执行一次邮件发送的定时任务
		c.AddFunc("@every 20s", func() {
			err := tools.SendEmail(0, 0, false)
			if err != nil {
				log.Printf("%+v\n", err)
				tools.SaveErrorLog("SendEmail", err)
			}
		})

		// 每2分钟执行一次，邮件第一次重试的定时任务
		c.AddFunc("@every 2m", func() {
			err := tools.SendEmail(2, 1, false)
			if err != nil {
				log.Printf("%+v\n", err)
				tools.SaveErrorLog("SendEmail", err)
			}
		})
		// 每5分钟执行一次，邮件第二次重试的定时任务
		c.AddFunc("@every 5m", func() {
			err := tools.SendEmail(2, 2, true)
			if err != nil {
				log.Printf("%+v\n", err)
				tools.SaveErrorLog("SendEmail", err)
			}
		})

	}

	// 监听catalent sftp
	c.AddFunc("@every 600s", func() {
		err := catalent.ReadFtpFile()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("ReadFtpFile", err)
		}
	})
	//// 发送catalent ftp
	c.AddFunc("@every 600s", func() {
		err := catalent.SendToSFTP()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SendToSFTP", err)
		}
	})

	//// 再随机项目一二阶段组别匹配check - 北京时间08:00
	////c.AddFunc("0 0 * * *", func() {
	//c.AddFunc("0 0 * * *", func() {
	//	_, err := CheckReRandom(true)
	//	if err != nil {
	//		log.Printf("%+v\n", err)
	//		tools.SaveErrorLog("CheckReRandom", err)
	//	}
	//})
	//// 发药检查 - 北京时间08:00
	////c.AddFunc("0 0 * * *", func() {
	//c.AddFunc("0 0 * * *", func() {
	//	_, err := CheckDispensing(true)
	//	if err != nil {
	//		log.Printf("%+v\n", err)
	//		tools.SaveErrorLog("CheckDispensing", err)
	//	}
	//})
	//
	//c.AddFunc("0 0 * * *", func() {
	//	_, err := LayerCheckTask(true)
	//	if err != nil {
	//		log.Printf("%+v\n", err)
	//		tools.SaveErrorLog("LayerCheckTask", err)
	//	}
	//})

	// app任务通知 - 北京时间09:00
	c.AddFunc("0 1 * * *", func() {
		err := TaskExpireDetection()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("TaskExpireDetection", err)
		}
	})

	// 订单超时提醒 - 北京时间00:00
	c.AddFunc("@every 5m", func() {
		err := OrderTimeOutReminder()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("OrderTimeOutReminder", err)
		}
	})

	// 库房低库存提醒 - 北京时间01:00
	c.AddFunc("0 17 * * *", func() {
		err := AlarmStorehouse()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("AlarmStorehouse", err)
		}
	})

	// 研究产品有效期自动失效定时任务 - 每5分钟执行一次   实时过期的药物
	c.AddFunc("*/5 * * * *", func() {
		err := MedicineExpiredTask()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("MedicineExpiredTask", err)
		}
	})

	// 研究产品有效期提醒 - 北京时间03:00
	c.AddFunc("0 19 * * *", func() {
		err := ReminderMedicinesValidDays()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("ReminderMedicinesValidDays", err)
		}
	})

	// 研究产品警戒 - 北京时间08:00   jira 7549 每分钟跑一次
	c.AddFunc("@every 1m", func() {
		err := TaskAlarmMedicineNew(1)
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("TaskAlarmMedicine", err)
		}
	})

	c.AddFunc("30 0 * * *", func() {
		err := ForecastMedicineTask()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("ForecastMedicineTask", err)
		}
	})

	c.AddFunc("30 19 * * *", func() {
		err := UnProvideDateTask()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("UnProvideDateTask", err)
		}
	})

	// 每30秒执行一次失败信息推送(IRT推送EDC数据)
	c.AddFunc("@every 30s", func() {
		err := SendEdcPush(0, 0)
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SendEdcPush", err)
		}
	})

	// 每2分钟执行一次失败信息推送(IRT推送EDC数据)
	c.AddFunc("@every 2m", func() {
		err := SendEdcPush(1, 0)
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SendEdcPush", err)
		}
	})

	// 每10分钟执行一次失败信息推送(IRT推送EDC数据)
	c.AddFunc("@every 10m", func() {
		err := SendEdcPush(2, 0)
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SendEdcPush", err)
		}
	})

	// 每1小时执行一次失败信息推送(IRT推送EDC数据)
	c.AddFunc("@every 60m", func() {
		err := SendEdcPush(3, 0)
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SendEdcPush", err)
		}
	})

	// 每天1：00定时任务，对于当天失败（每日新增）的任务数据点，集中推送一次，更新状态(IRT推送EDC数据)
	c.AddFunc("0 17 * * *", func() {
		err := SendEdcPush(0, 1)
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SendEdcPush", err)
		}
	})

	// 删除过期的报表 TODO 放开了30天记录的限制，重复文件MD5后面优化
	//c.AddFunc("@daily", func() {
	//	err := DeleteExpiredReport()
	//	if err != nil {
	//		log.Printf("%+v\n", err)
	//		tools.SaveErrorLog("DeleteExpiredReport", err)
	//	}
	//})

	// 同步cloud的用户状态以及删除状态信息
	c.AddFunc("0 2 * * *", func() {
		err := SynchronizeUserStatus()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SynchronizeUserStatus", err)
		}
	})

	//每1分钟执行一次访视通知推送
	c.AddFunc("@every 1m", func() {
		err := SendVisitNoticeTask()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SendEdcPush", err)
		}
	})

	//每天0点 执行 受试者访视通知
	c.AddFunc("0 16 * * *", func() {
		err := VisitNoticeTask()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("VisitNoticeTask", err)
		}
	})

	// 每1小时执行一次失败信息推送(IRT推送EDC数据)
	c.AddFunc("0 * * * *", func() {
		err := VisitDynamicsTask()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("VisitDynamicsTask", err)
		}
	})

	// 每1小时执行一次夏令时/冬令时转换
	c.AddFunc("0 * * * *", func() {
		err := LocationTimeZoneTask()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("VisitDynamicsTask", err)
		}
	})

	// 同步mail集合数据到另一张集合MailHistoryRecord
	c.AddFunc("0 20 * * *", func() {
		err := SynchronizeMails()
		if err != nil {
			log.Printf("%+v\n", err)
			tools.SaveErrorLog("SynchronizeMails", err)
		}
	})

	c.Start()
}
