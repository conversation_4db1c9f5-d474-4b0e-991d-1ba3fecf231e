package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

/***
跑批获取所有进行中的项目的PROD环境

获取环境ENV下的所有绑定供应计划 有效的中心

查询环境ENV下的供应计划

查询不发放配置




for env

	查询环境下药物属性

	Group env环境下 药物名称+日期
	中心
		关联供应计划
			每个药的不发放时间
				达到不发放天数研究产品数量
				中心可发放研究产品数量



*/

type MedicineEnvName struct {
	SiteID         primitive.ObjectID `bson:"site_id"`
	Name           string             `bson:"name"`
	ExpirationDate string             `bson:"expiration_date"`
	Count          int64              `bson:"count"`
}

func UnProvideDateTask() error {

	// 跑批获取所有进行中的项目的PROD环境
	//match := bson.M{"envs.name": "UAT", "status": bson.M{"$ne": 2}, "info.number": "test0416"}
	match := bson.M{"envs.name": "PROD", "status": bson.M{"$ne": 2}}
	now := time.Now()
	pipe := mongo.Pipeline{
		{{Key: "$unwind", Value: "$envs"}},
		{{"$match", match}},
		{{"$project", bson.M{
			"info.timeZone":    bson.M{"$ifNull": bson.A{"$info.timeZone", 8}},
			"info.timeZoneStr": bson.M{"$ifNull": bson.A{"$info.timeZoneStr", "8"}},
			"info.number":      1,
			"info.name":        1,
			"envs.id":          1,
			"envs.name":        1,
		}}},
	}
	var projects []ProjectAlarm
	cursor, err := tools.Database.Collection("project").Aggregate(nil, pipe)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return errors.WithStack(err)
	}

	// 获取环境下所有的中心
	envIDs := slice.Map(projects, func(index int, item ProjectAlarm) primitive.ObjectID {
		return item.Environments.ID
	})

	var projectSites []models.ProjectSite

	cursor, err = tools.Database.Collection("project_site").Find(nil, bson.M{"deleted": 2, "env_id": bson.M{"$in": envIDs}, "supply_plan_id": bson.M{"$ne": primitive.NilObjectID}})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
		return errors.WithStack(err)
	}

	var supplyPlan []models.SupplyPlanMedicine

	cursor, err = tools.Database.Collection("supply_plan_medicine").Find(nil, bson.M{"env_id": bson.M{"$in": envIDs}})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &supplyPlan)
	if err != nil {
		return errors.WithStack(err)
	}
	for _, item := range projects {
		err = siteProjectSite(item, projectSites, supplyPlan, now)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func siteProjectSite(project ProjectAlarm, projectSites []models.ProjectSite, supplyPlan []models.SupplyPlanMedicine, now time.Time) error {
	var medicineEnvNames []MedicineEnvName
	var otherMedicineEnvNames []MedicineEnvName
	envOID := project.Environments.ID
	projectSitesEnv := slice.Filter(projectSites, func(index int, item models.ProjectSite) bool {
		return item.EnvironmentID == envOID
	})
	projectSiteOIDs := slice.Map(projectSitesEnv, func(index int, item models.ProjectSite) primitive.ObjectID {
		return item.ID
	})
	cursor, err := tools.Database.Collection("medicine").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"site_id": bson.M{"$in": projectSiteOIDs}, "status": 1}}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"name":            "$name",
				"expiration_date": "$expiration_date",
				"site_id":         "$site_id"},
			"count": bson.M{"$sum": 1}}}},
		{{"$project", bson.M{
			"name":            "$_id.name",
			"expiration_date": "$_id.expiration_date",
			"site_id":         "$_id.site_id",
			"count":           1,
		}}},
	})
	if err != nil {
		return err
	}

	err = cursor.All(nil, &medicineEnvNames)
	if err != nil {
		return err
	}

	cursor, err = tools.Database.Collection("medicine_others").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"site_id": bson.M{"$in": projectSiteOIDs}, "status": 1}}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"name":            "$name",
				"expiration_date": "$expiration_date",
				"site_id":         "$site_id"},
			"count": bson.M{"$sum": 1}}}},
		{{"$project", bson.M{
			"name":            "$_id.name",
			"expiration_date": "$_id.expiration_date",
			"site_id":         "$_id.site_id",
			"count":           1,
		}}},
	})
	if err != nil {
		return err
	}
	err = cursor.All(nil, &otherMedicineEnvNames)
	if err != nil {
		return err
	}
	medicineEnvNames = append(medicineEnvNames, otherMedicineEnvNames...)
	IsBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return errors.WithStack(err)
	}

	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	mailBodyContet, err := tools.MailBodyContent(nil, envOID, "notice.medicine.isolation")

	langList := make([]string, 0)
	html := "medicine_un_provide_date_zh_en.html"
	if noticeConfig.Automatic != 0 {
		if noticeConfig.Automatic == 1 {
			langList = append(langList, "zh")
			html = "medicine_un_provide_date_zh.html"
		} else if noticeConfig.Automatic == 2 {
			langList = append(langList, "en")
			html = "medicine_un_provide_date_en.html"
		} else if noticeConfig.Automatic == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
			html = "medicine_un_provide_date_zh_en.html"
		}
	} else {
		langList = append(langList, "zh")
		langList = append(langList, "en")
	}

	for _, projectSite := range projectSites {
		plans := slice.Filter(supplyPlan, func(index int, item models.SupplyPlanMedicine) bool {
			return item.SupplyPlanID == projectSite.SupplyPlanID
		})
		mailList := []bson.M{}
		// TODO  查询中心下的研究产品
		for _, plan := range plans {
			if plan.Medicine.UnProvideDate == 0 {
				continue
			}
			count := int64(0)
			useCount := int64(0)
			// 逻辑计算
			lastDate := now.Add(time.Hour * 24 * time.Duration(plan.Medicine.UnProvideDate)).Format("2006-01-02")
			firstDate := now.Format("2006-01-02")
			if plan.Medicine.UnProvideDate > 5 {
				firstDate = now.Add(time.Hour * 24 * time.Duration(plan.Medicine.UnProvideDate-5)).Format("2006-01-02")
			}

			medicineEnvName := slice.Filter(medicineEnvNames, func(index int, item MedicineEnvName) bool {
				return item.SiteID == projectSite.ID && plan.Medicine.MedicineName == item.Name && item.ExpirationDate >= firstDate && item.ExpirationDate <= lastDate
			})
			for _, item := range medicineEnvName {
				count = count + item.Count
			}
			useMedicineEnvName := slice.Filter(medicineEnvNames, func(index int, item MedicineEnvName) bool {
				return item.SiteID == projectSite.ID && plan.Medicine.MedicineName == item.Name && item.ExpirationDate > lastDate
			})
			for _, item := range useMedicineEnvName {
				useCount = useCount + item.Count
			}
			if count > 0 {
				tmpList := bson.M{
					"name":          plan.Medicine.MedicineName,
					"unProvideDate": convertor.ToString(plan.Medicine.UnProvideDate),
					"count":         convertor.ToString(count),
					"useCount":      convertor.ToString(useCount)}
				mailList = append(mailList, tmpList)
			}
		}

		if len(mailList) > 0 {
			userMail, err := tools.GetRoleUsersMailWithRole(projectSite.ProjectID, projectSite.EnvironmentID, "notice.medicine.un_provide_date", projectSite.ID)
			if err != nil {
				return errors.WithStack(err)
			}

			blindResult := []bson.M{}
			for _, item := range mailList {

				_, ok := slice.Find(blindResult, func(index int, info bson.M) bool {
					return info["name"] == tools.BlindData && item["unProvideDate"] == info["unProvideDate"]
				})

				if IsBlindDrugMap[item["name"].(string)] {
					if ok {
						// 在原来的基础上加上数量
						for i, info := range blindResult {

							if info["name"] == tools.BlindData && item["unProvideDate"] == info["unProvideDate"] {
								baseCount, _ := convertor.ToInt(blindResult[i]["count"])
								addCount, _ := convertor.ToInt(item["count"])
								blindResult[i]["count"] = convertor.ToString(baseCount + addCount)
								baseUseCount, _ := convertor.ToInt(blindResult[i]["useCount"])
								addUseCount, _ := convertor.ToInt(item["useCount"])
								blindResult[i]["useCount"] = convertor.ToString(baseUseCount + addUseCount)
							}

						}

					} else {

						//append一条数据
						dataInfo := bson.M{}
						for key, value := range item {
							if key == "name" {
								dataInfo["name"] = tools.BlindData
							} else {
								dataInfo[key] = value
							}
						}
						blindResult = append(blindResult, dataInfo)
					}

				} else {
					blindResult = append(blindResult, item)
				}
			}

			mails := make([]interface{}, 0)
			for _, item := range userMail {

				sendContentData := bson.M{
					"projectNumber": project.ProjectInfo.Number,
					"projectName":   project.ProjectInfo.Name,
					"envName":       project.Environments.Name,
					"siteNumber":    projectSite.Number,
					"siteNameZh":    tools.GetProjectSiteLangName(projectSite, "zh"),
					"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
					"results":       mailList,
				}
				if item.IsBlind {
					sendContentData["results"] = blindResult
				}

				for key, v := range mailBodyContet {
					sendContentData[key] = v
				}

				mails = append(mails, models.Mail{
					ID:      primitive.NewObjectID(),
					Subject: "medicine.un_provide_date.title",
					SubjectData: bson.M{
						"projectNumber": project.ProjectInfo.Number,
						"projectName":   project.ProjectInfo.Name,
						"envName":       project.Environments.Name,
						"siteNumber":    projectSite.Number,
						"siteNameZh":    tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
					},
					ContentData:  sendContentData,
					To:           []string{item.Email},
					Lang:         "en-US",
					LangList:     langList,
					Status:       0,
					CreatedTime:  time.Duration(time.Now().Unix()),
					ExpectedTime: time.Duration(time.Now().Unix()),
					SendTime:     time.Duration(time.Now().Unix()),
					HTML:         html,
				})

			}
			if len(mails) > 0 {
				_, err = tools.Database.Collection("mail").InsertMany(nil, mails)
				if err != nil {
					return errors.WithStack(err)
				}
			}

		}

	}
	return nil
}
