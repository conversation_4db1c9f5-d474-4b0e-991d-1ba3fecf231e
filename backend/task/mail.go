package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"log"
	"math"
)

// 背景：mail集合数据过多，将已经发送成功的数据同步到另一张集合MailHistoryRecord里面
// SynchronizeMails .. 同步mail集合数据到另一张集合MailHistoryRecord
func SynchronizeMails() error {
	defer tools.DeferReturn("SynchronizeMails")
	// 迁移所有status=1的邮件
	err := SafeMigrateMails(bson.M{"status": 1})
	if err != nil {
		log.Printf("迁移失败: %v", err)
	}

	return nil
}

func SafeMigrateMails(filter bson.M) error {

	if count, _ := tools.Database.Collection("mail").CountDocuments(nil, filter); count > 0 {

		const batchSize = 1000 // 每批次插入的数量
		numBatches := int(math.Ceil(float64(count) / batchSize))

		for i := 0; i < numBatches; i++ {
			callback := func(sctx mongo.SessionContext) (interface{}, error) {
				findOptions := options.Find()
				findOptions.SetLimit(1000)
				mailList := make([]models.Mail, 0)
				mailCursor, err := tools.Database.Collection("mail").Find(nil, filter, findOptions)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = mailCursor.All(nil, &mailList)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if mailList != nil && len(mailList) > 0 {
					var docs []interface{}
					idsToDelete := make([]primitive.ObjectID, 0)
					for _, mail := range mailList {
						docs = append(docs, mail)
						idsToDelete = append(idsToDelete, mail.ID)
					}
					if _, err := tools.Database.Collection("mail_history_record").InsertMany(nil, docs); err != nil {
						return nil, errors.WithStack(err)
					}

					// 3. 删除原文档
					_, err := tools.Database.Collection("mail").DeleteMany(nil, bson.M{"_id": bson.M{"$in": idsToDelete}})
					if err != nil {
						return nil, errors.WithStack(err)
					}

				}

				return nil, nil
			}

			err := tools.Transaction(callback)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func insertDocumentsInBatches(documents []interface{}) error {
	const batchSize = 1000 // 每批次插入的数量
	totalDocs := len(documents)
	numBatches := int(math.Ceil(float64(totalDocs) / batchSize))

	for i := 0; i < numBatches; i++ {
		start := i * batchSize
		end := start + batchSize
		if end > totalDocs {
			end = totalDocs
		}
		batch := documents[start:end]
		_, err := tools.Database.Collection("mail_history_record").InsertMany(nil, batch)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}
