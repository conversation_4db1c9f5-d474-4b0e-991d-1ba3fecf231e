package task

import (
	"clinflash-irt/database"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

/*
超窗访视
中心0点触发定时任务  每小时跑一次定时任务

	查询访视配置配置了间隔时间的配置
	根据当前时间查询当前时区的中心
		查询中心的所有受试者所有访视
			for 访视 :
				当前时区时间2006-01-02减去一天 == 最大窗口期
					写入项目动态
*/

// VisitDynamicsTask ...
func VisitDynamicsTask() error {
	// 查询配置了时间间隔的环境
	var visitCycles []models.VisitCycle
	cursor, err := tools.Database.Collection("visit_cycle").Find(nil, bson.M{"infos": bson.M{"$elemMatch": bson.M{"interval": bson.M{"$ne": nil}}}})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return errors.WithStack(err)
	}
	now := time.Duration(time.Now().Unix())

	// 查询配置的所有的项目数据

	projectOID := slice.Map(visitCycles, func(index int, item models.VisitCycle) primitive.ObjectID {
		return item.ProjectID
	})
	var projects []models.Project
	cursor, err = tools.Database.Collection("project").Find(nil, bson.M{"_id": bson.M{"$in": projectOID}})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		return errors.WithStack(err)
	}
	for _, cycle := range visitCycles {
		dynamics := []interface{}{}
		projectP, ok := slice.Find(projects, func(index int, item models.Project) bool {
			return item.ID == cycle.ProjectID
		})
		if !ok {
			continue
		}
		project := *projectP

		_, ok = slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.Name == "PROD" && cycle.EnvironmentID == item.ID
		})

		if !ok {
			continue
		}

		projectSites, useTimeZone, err := currentSite(project, cycle.EnvironmentID)
		if err != nil {
			return errors.WithStack(err)
		}

		subjectMap := make(map[string]models.Subject)
		for _, value := range visitCycles {
			if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
				subjectMap, err = GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
			}
		}

		if len(projectSites) > 0 {
			// 查询中心所有受试者
			siteOIDs := slice.Map(projectSites, func(index int, item models.ProjectSite) primitive.ObjectID {
				return item.ID
			})

			subjectDispensings, err := getSubjectDispensingManySite(nil, cycle.EnvironmentID, cycle.CohortID, siteOIDs)
			if err != nil {
				return errors.WithStack(err)
			}
			hour := time.Duration(useTimeZone)
			minute := time.Duration((useTimeZone - float64(hour)) * 60)
			duration := hour*time.Hour + minute*time.Minute
			yesterday := time.Unix(now.Nanoseconds(), 0).UTC().Add(duration).AddDate(0, 0, -1).Format("2006-01-02")

			for _, subjectDispensing := range subjectDispensings {
				projectSiteP, ok := slice.Find(projectSites, func(index int, item models.ProjectSite) bool {
					return subjectDispensing.ProjectSiteID == item.ID
				})
				if ok {
					projectSite := *projectSiteP

					// TODO 6074

					subjectInsertDynamics(subjectDispensing, cycle, useTimeZone, yesterday, projectSite, &dynamics, now, subjectMap)
				}

			}

		}
		if len(dynamics) > 0 {
			//fmt.Println(len(dynamics))
			_, err = tools.Database.Collection("project_dynamics").InsertMany(nil, dynamics)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}

	return nil
}

func currentSite(project models.Project, envOID primitive.ObjectID) ([]models.ProjectSite, float64, error) {
	useTimeZone := float64(0)
	var sites []models.ProjectSite
	cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, useTimeZone, errors.WithStack(err)
	}
	err = cursor.All(nil, &sites)
	if err != nil {
		return nil, useTimeZone, errors.WithStack(err)
	}
	selectSite := []models.ProjectSite{}
	for _, site := range sites {
		timeZone, _ := tools.GetProjectLocation(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
		//timeZone := project.TimeZone
		if site.TimeZone != "" {
			strTimeZone, err := tools.GetSiteTimeZoneInfo(site)
			if err != nil {
				return nil, useTimeZone, errors.WithStack(err)
			}
			timeZone, _ = tools.ParseTimezoneOffset(strTimeZone)
			//timeZone, _ = strconv.ParseFloat(strings.Replace(site.TimeZone, "UTC", "", 1), 64)
		}
		hours := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute

		now := time.Now().UTC().Add(duration).Truncate(time.Hour).Format("2006-01-02 15:04")
		if now[11:] == "00:00" {
			useTimeZone = timeZone
			selectSite = append(selectSite, site)
		}
	}
	return selectSite, useTimeZone, nil
}

func subjectInsertDynamics(subject models.SubjectDispensing, visitCycle models.VisitCycle, intTimeZone float64, yesterday string, site models.ProjectSite, data *[]interface{}, now time.Duration, subjectMap map[string]models.Subject) {
	afterRandom := false
	interval := float64(0)
	lastTime := time.Duration(0)
	firstTime := time.Duration(0)
	for _, dispensing := range subject.Dispensing {
		if !dispensing.VisitSign {
			visitCycleInfo := models.VisitCycleInfo{}
			visitCycleInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
				return item.Number == dispensing.VisitInfo.Number
			})
			if ok {
				visitCycleInfo = *visitCycleInfoP
			}

			// TODO 7064
			if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
				subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
			}
			if firstTime == 0 && dispensing.DispensingTime != 0 && visitCycleInfo.Interval != nil && subject.RandomTime == 0 {
				subject.RandomTime = time.Duration(time.Unix(int64(dispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfo.Interval)).Unix())
				firstTime = dispensing.DispensingTime
			}
			// 计算窗口期最小窗口时间
			period := tools.HandleVisitDate(afterRandom, visitCycle.VisitType, visitCycleInfo, subject.RandomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval)
			if dispensing.Status == 2 {
				interval = 0
				lastTime = dispensing.DispensingTime
			}
			if dispensing.VisitInfo.Random {
				afterRandom = true
			}
			if period.MaxPeriod == yesterday && dispensing.Status == 1 { // 刚好是昨天超窗 写入项目动态
				*data = append(*data,
					models.ProjectDynamics{
						ID:          primitive.NewObjectID(),
						Operator:    primitive.NilObjectID,
						OID:         visitCycle.EnvironmentID,
						Time:        now,
						SceneTran:   "project_dynamics_scene_visit",
						TypeTran:    "project_dynamics_type_visit",
						ContentTran: "project_dynamics_content_visit",
						ContentData: map[string]interface{}{
							"siteId":      site.ID,
							"siteName":    site.Name,
							"siteName_en": site.NameEn,
							"subject":     subject.Info[0].Value,
							"visit":       dispensing.VisitInfo.Name,
						},
					},
				)
			}
		}
	}
}

func getSubjectDispensingManySite(sctx mongo.SessionContext, envID, cohortID primitive.ObjectID, siteOIDs []primitive.ObjectID) ([]models.SubjectDispensing, error) {
	attribute, err := database.GetAttributeWithEnvCohortID(nil, envID, cohortID)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	match := bson.M{"env_id": attribute.EnvironmentID, "cohort_id": attribute.CohortID, "project_site_id": bson.M{"$in": siteOIDs}}
	// 排除退出 替换的受试者
	match["status"] = bson.M{"$in": bson.A{3, 6, 9}}

	// 是否需要计算揭盲的
	if attribute.AttributeInfo.UnBlindingRestrictions {
		match["urgent_unblinding_status"] = 0
	}
	// 是否需要计算pv揭盲
	if attribute.AttributeInfo.PvUnBlindingRestrictions {
		match["pv_unblinding_status"] = 0
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
		{{"$match", match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing",
			"let":  bson.M{"subject_id": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
				bson.M{"$sort": bson.D{{"serial_number", 1}}},
			},
			"as": "dispensing",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var subjectDispensing []models.SubjectDispensing
	err = cursor.All(nil, &subjectDispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return subjectDispensing, nil
}
