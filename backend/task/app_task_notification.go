package task

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// 任务过期检测
func TaskExpireDetection() error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 查询过期的数据（过期时间大于昨天0时，小于今天0时）
		now := time.Now()
		// 昨天0时
		yesterday := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, time.Local)
		// 今天0时
		today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

		// 查询条件
		match := bson.M{"info.deadline": bson.M{"$gte": yesterday.Unix(), "$lt": today.Unix()}, "info.status": 0, "deleted": bson.M{"$ne": true}}
		var workTask []models.WorkTask
		cursor, err := tools.Database.Collection("work_task").Find(nil, match)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &workTask)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var appTaskNotice []interface{}
		// 查询工作任务
		for _, wt := range workTask {
			//过滤掉app已关闭的用户
			filter := bson.M{"user_id": bson.M{"$in": wt.UserIDs}, "app": true, "project_id": wt.ProjectID, "env_id": wt.EnvironmentID}
			userEnvs := make([]models.UserProjectEnvironment, 0)
			cursor, err := tools.Database.Collection("user_project_environment").Find(nil, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &userEnvs)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var userIds []models.UserIds
			var us []primitive.ObjectID
			if wt.UserIDs != nil && len(wt.UserIDs) > 0 {
				for _, user := range userEnvs {
					userIds = append(userIds, models.UserIds{
						UserID: user.UserID,
						Read:   false,
					})
					us = append(us, user.UserID)
				}
				appTaskNotice = append(appTaskNotice, models.AppTaskNotice{
					ID:            primitive.NewObjectID(),
					CustomerID:    wt.CustomerID,
					ProjectID:     wt.ProjectID,
					EnvironmentID: wt.EnvironmentID,
					CohortID:      wt.CohortID,
					NoticeType:    0,
					NoticeTime:    time.Duration(time.Now().Unix()),
					ExpireNotice: models.ExpireNotice{
						WorkTaskID:        wt.ID,
						WorkType:          wt.Info.WorkType,
						MedicineOrderID:   wt.Info.MedicineOrderID,
						DispensingID:      wt.Info.DispensingID,
						ApprovalProcessID: wt.Info.ApprovalProcessID,
						SubjectApproval:   wt.Info.SubjectApproval,
						SubjectPvApproval: wt.Info.SubjectPvApproval,
					},
					UserIds: userIds,
				})

				// 极光推送
				title_zh := ""
				alertBody_zh := ""
				title_en := ""
				alertBody_en := ""
				wtv := models.WorkTaskView{}
				wtv.ID = wt.ID
				wtv.CustomerID = wt.CustomerID
				wtv.ProjectID = wt.ProjectID
				wtv.EnvironmentID = wt.EnvironmentID
				wtv.CohortID = wt.CohortID
				wtv.Info.FinishTime = wt.Info.FinishTime
				wtv.Info.Status = wt.Info.Status
				wtv.NoticeType = 0
				wtv.Info.WorkType = wt.Info.WorkType

				userRegistrationIdAppLanguages, err := getUserRegistrationId(wt.Info.WorkType, us, wt, primitive.NilObjectID, primitive.NilObjectID)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				projectInfo, project, _ := getAuroraPushProjectInfo(wt.ProjectID, wt.EnvironmentID)
				if wt.Info.WorkType == 1 { // 扫码入仓
					title_zh = locales.TrWithLang("zh-CN", "app_scan_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_scan_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"

					title_en = locales.TrWithLang("en-US", "app_scan_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_scan_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"
				} else if wt.Info.WorkType == 2 { // 发送订单确认
					_, _, orderInfo, err := getAuroraPushOrderInfo(wt.Info.MedicineOrderID, project)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					title_zh = locales.TrWithLang("zh-CN", "app_shipment_confirmed_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_shipment_confirmed_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_shipment_confirmed_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_shipment_confirmed_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)

				} else if wt.Info.WorkType == 3 { // 研究产品接收
					_, _, orderInfo, err := getAuroraPushOrderInfo(wt.Info.MedicineOrderID, project)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_shipment_received_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_shipment_received_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_shipment_received_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_shipment_received_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
				} else if wt.Info.WorkType == 4 { // 发药
					_, dispensingInfo, err := getAuroraPushDispensingInfo(wt.Info.DispensingID)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_dispensation_confirmed_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_dispensation_confirmed_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_dispensation_confirmed_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_dispensation_confirmed_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)
				} else if wt.Info.WorkType == 5 { // 补发药
					_, dispensingInfo, err := getAuroraPushDispensingInfo(wt.Info.DispensingID)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_re_dispensation_confirmed_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_re_dispensation_confirmed_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_re_dispensation_confirmed_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_re_dispensation_confirmed_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)
				} else if wt.Info.WorkType == 6 { // 订单待运送
					_, _, orderInfo, err := getAuroraPushOrderInfo(wt.Info.MedicineOrderID, project)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_shipment_delivered_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_shipment_delivered_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_shipment_delivered_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_shipment_delivered_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
				} else if wt.Info.WorkType == 7 { // 创建订单审批
					_, siteInfo, err := getAuroraPushSiteInfo(wt.Info.ApprovalProcessID)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_shipment_delivered_application_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_shipment_delivered_application_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + siteInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_shipment_delivered_application_title")
					alertBody_en = locales.TrWithLang("en-US", "app_shipment_delivered_application_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + siteInfo["siteName"].(string)
				} else if wt.Info.WorkType == 8 { // 紧急揭盲审批
					_, subjectSiteInfo, err := getAuroraPushSubjectSiteInfo(wt.Info.SubjectApproval.SubjectID)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_unblinding_urgent_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_unblinding_urgent_notification_content") + "-" + wt.Info.SubjectApproval.Number + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + subjectSiteInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_unblinding_urgent_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_unblinding_urgent_notification_content") + "-" + wt.Info.SubjectApproval.Number + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + subjectSiteInfo["siteName"].(string)
				} else if wt.Info.WorkType == 9 { // 访视外发药
					_, dispensingInfo, err := getAuroraPushDispensingInfo(wt.Info.DispensingID)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_unschedualed_dispensation_confirmation_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_unschedualed_dispensation_confirmation_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_unschedualed_dispensation_confirmation_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_unschedualed_dispensation_confirmation_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)
				} else if wt.Info.WorkType == 10 { // pv揭盲
					_, subjectSiteInfo, err := getAuroraPushSubjectSiteInfo(wt.Info.SubjectPvApproval.SubjectID)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_unblinding_pv_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_unblinding_pv_notification_content") + "-" + wt.Info.SubjectPvApproval.Number + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + subjectSiteInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_unblinding_pv_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_unblinding_pv_notification_content") + "-" + wt.Info.SubjectPvApproval.Number + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + subjectSiteInfo["siteName"].(string)
				} else if wt.Info.WorkType == 11 { // 药物发放
					_, dispensingInfo, planLeftTime, planRightTime, err := getDispensingDataInfo(wt)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					title_zh = locales.TrWithLang("zh-CN", "app_ip_dispensation_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_ip_dispensation_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_ip_dispensation_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_ip_dispensation_notification_content") + "-" + dispensingInfo["subjectName"].(string) + "-" + dispensingInfo["visitNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + dispensingInfo["siteName"].(string)

					wtv.SubjectId = dispensingInfo["subjectId"].(string)
					wtv.VisitNumber = dispensingInfo["visitNumber"].(string)
					wtv.VisitCycleId = dispensingInfo["visitCycleId"].(string)

					condition := bson.M{"customer_id": wtv.CustomerID, "project_id": wtv.ProjectID, "env_id": wtv.EnvironmentID}
					if wtv.CohortID != primitive.NilObjectID {
						condition = bson.M{"customer_id": wtv.CustomerID, "project_id": wtv.ProjectID, "env_id": wtv.EnvironmentID, "cohort_id": wtv.CohortID}
					}
					var attribute models.Attribute
					err = tools.Database.Collection("attribute").FindOne(nil, condition).Decode(&attribute)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					// 不展示随机号
					if !attribute.AttributeInfo.IsRandomNumber {
						wtv.RandomNumber = tools.BlindData
					} else {
						wtv.RandomNumber = dispensingInfo["randomNumber"].(string)
					}
					wtv.PlanLeftTime = planLeftTime
					wtv.PlanRightTime = planRightTime
				} else if wt.Info.WorkType == 12 { // 包装扫码
					title_zh = locales.TrWithLang("zh-CN", "app_scan_package_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_scan_package_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"

					title_en = locales.TrWithLang("en-US", "app_scan_package_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_scan_package_notification_content") + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]"

				} else if wt.Info.WorkType == 13 { //13回收订单待确认
					_, _, orderInfo, err := getAuroraPushOrderInfo(wt.Info.MedicineOrderID, project)
					if err != nil {
						return nil, errors.WithStack(err)
					}

					title_zh = locales.TrWithLang("zh-CN", "app_recovery_shipment_confirmed_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_recovery_shipment_confirmed_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_recovery_shipment_confirmed_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_recovery_shipment_confirmed_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
				} else if wt.Info.WorkType == 14 { //14回收订单待运送
					_, _, orderInfo, err := getAuroraPushOrderInfo(wt.Info.MedicineOrderID, project)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_recovery_shipment_delivered_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_recovery_shipment_delivered_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_recovery_shipment_delivered_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_recovery_shipment_delivered_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
				} else if wt.Info.WorkType == 15 { // 15回收订单待接收
					_, _, orderInfo, err := getAuroraPushOrderInfo(wt.Info.MedicineOrderID, project)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					title_zh = locales.TrWithLang("zh-CN", "app_recovery_shipment_received_notification_title")
					alertBody_zh = locales.TrWithLang("zh-CN", "app_recovery_shipment_received_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)

					title_en = locales.TrWithLang("en-US", "app_recovery_shipment_received_notification_title")
					alertBody_en = locales.TrWithLang("en-US", "app_recovery_shipment_received_notification_content") + "-" + orderInfo["orderNumber"].(string) + " | " + projectInfo["projectNumber"].(string) + "[" + projectInfo["envName"].(string) + "]" + " | " + orderInfo["siteName"].(string)
				}

				//转JSON
				sJson, err := json.Marshal(wtv)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				var registrationId_zh []string
				var registrationId_en []string
				for _, ural := range userRegistrationIdAppLanguages {
					if ural.AppLanguage == "" || ural.AppLanguage == "zh-CN" {
						registrationId_zh = append(registrationId_zh, ural.RegistrationId)
					} else {
						registrationId_en = append(registrationId_en, ural.RegistrationId)
					}
				}

				// 中文推送
				if registrationId_zh != nil && len(registrationId_zh) > 0 {
					err = tools.DataAssembly(registrationId_zh, title_zh, alertBody_zh, string(sJson))
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}

				// 英文推送
				if registrationId_en != nil && len(registrationId_en) > 0 {
					err = tools.DataAssembly(registrationId_en, title_en, alertBody_en, string(sJson))
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		}
		if appTaskNotice != nil && len(appTaskNotice) > 0 {
			_, err = tools.Database.Collection("app_task_notice").InsertMany(sctx, appTaskNotice)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 项目详情
func getAuroraPushProjectInfo(projectID primitive.ObjectID, envID primitive.ObjectID) (map[string]interface{}, models.Project, error) {
	projectName := ""
	projectNumber := ""
	envName := ""

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectID}).Decode(&project)
	if err != nil {
		return nil, project, errors.WithStack(err)
	}

	projectName = project.Name
	projectNumber = project.Number
	// 筛选环境
	for _, env := range project.Environments {
		if env.ID == envID {
			envName = env.Name
			break
		}
	}
	return map[string]interface{}{"projectName": projectName, "projectNumber": projectNumber, "envName": envName}, project, nil
}

// 订单详情
func getAuroraPushOrderInfo(orderId primitive.ObjectID, project models.Project) ([]primitive.ObjectID, []primitive.ObjectID, map[string]interface{}, error) {
	siteIds := make([]primitive.ObjectID, 0)
	depotIds := make([]primitive.ObjectID, 0)

	orderNumber := ""
	siteName := ""

	var order models.MedicineOrder
	err := tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": orderId}).Decode(&order)
	if err != nil {
		return []primitive.ObjectID{}, []primitive.ObjectID{}, nil, err
	}

	if order.Status != 4 && order.Status != 5 {
		if order.Type == 5 || order.Type == 6 { //目的地是受试者
			var subject models.Subject
			err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": order.SubjectID}).Decode(&subject)
			if err != nil {
				return []primitive.ObjectID{}, []primitive.ObjectID{}, nil, err
			}
			for _, info := range subject.Info {
				if info.Name == "shortname" {
					siteName = fmt.Sprintf("%v", info.Value)
				}
			}
		} else {
			orderNumber = order.OrderNumber
			receiveID := order.ReceiveID
			if !receiveID.IsZero() {
				var projectSite models.ProjectSite
				err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": receiveID}).Decode(&projectSite)
				if err != nil && err != mongo.ErrNoDocuments {
					return []primitive.ObjectID{}, []primitive.ObjectID{}, nil, err
				}
				if !projectSite.ID.IsZero() {
					siteIds = append(siteIds, projectSite.ID)
					siteName = projectSite.Name
				} else {
					var projectStorehouse models.ProjectStorehouse
					err = tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": receiveID}).Decode(&projectStorehouse)
					if err != nil {
						return []primitive.ObjectID{}, []primitive.ObjectID{}, nil, err
					}
					depotIds = append(depotIds, projectStorehouse.ID)
					var storehouse models.Storehouse
					err = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
					if err != nil {
						return []primitive.ObjectID{}, []primitive.ObjectID{}, nil, err
					}
					siteName = storehouse.Name
				}
			}
		}
	}
	return siteIds, depotIds, map[string]interface{}{"orderNumber": orderNumber, "siteName": siteName}, nil
}

// 发药详情
func getAuroraPushDispensingInfo(dispensingId primitive.ObjectID) ([]primitive.ObjectID, map[string]interface{}, error) {
	siteIds := make([]primitive.ObjectID, 0)
	var dispensing models.Dispensing
	err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingId}).Decode(&dispensing)
	if err != nil {
		return []primitive.ObjectID{}, nil, err
	}

	visitNumber := ""
	subjectName := ""
	siteName := ""

	if dispensing.DispensingMedicines != nil {
		visitNumber = dispensing.VisitInfo.Number
		var subject models.Subject
		err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
		if err != nil {
			return []primitive.ObjectID{}, nil, err
		}
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				subjectName = info.Value.(string)
				break
			}
		}
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return []primitive.ObjectID{}, nil, err
		}
		siteIds = append(siteIds, projectSite.ID)
		siteName = projectSite.ShortName
	}
	return siteIds, map[string]interface{}{"visitNumber": visitNumber, "subjectName": subjectName, "siteName": siteName}, nil
}

// 药物发放详情
func getDispensingDataInfo(workTask models.WorkTask) ([]primitive.ObjectID, map[string]interface{}, string, string, error) {
	siteIds := make([]primitive.ObjectID, 0)
	var dispensing models.Dispensing
	err := tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": workTask.Info.DispensingID}).Decode(&dispensing)
	if err != nil {
		return []primitive.ObjectID{}, nil, "", "", err
	}

	visitNumber := ""
	subjectName := ""
	siteName := ""
	visitCycleId := ""

	visitNumber = dispensing.VisitInfo.Number
	if dispensing.VisitInfo.VisitCycleInfoID != primitive.NilObjectID {
		visitCycleId = dispensing.VisitInfo.VisitCycleInfoID.Hex()
	}
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, nil, "", "", err
	}
	for _, info := range subject.Info {
		if info.Name == "shortname" {
			subjectName = info.Value.(string)
			break
		}
	}

	attribute, err := database.GetAttributeWithEnvCohortID(nil, dispensing.EnvironmentID, dispensing.CohortID)
	if err != nil {
		return []primitive.ObjectID{}, nil, "", "", err
	}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, nil, "", "", err
	}

	//待发药--插入任务表中
	match := bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID}
	if dispensing.CohortID != primitive.NilObjectID {
		match = bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}
	}
	var visit models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visit)
	if err != nil {
		return []primitive.ObjectID{}, nil, "", "", err
	}

	filter := bson.M{"subject_id": dispensing.SubjectID}
	var resDispensing []models.ResDispensing
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_order",
			"let": bson.M{
				"dispensing_id": "$_id",
				//"order_number":  "$order",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
			},

			"as": "medicine_order",
		}}},
		{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return nil, nil, "", "", errors.WithStack(err)
	}
	err = cursor.All(nil, &resDispensing)
	if err != nil {
		return nil, nil, "", "", errors.WithStack(err)
	}

	visitCycleMap := map[string]models.VisitCycleInfo{}
	for _, info := range visit.Infos {
		visitCycleMap[info.Number] = info
	}
	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return nil, nil, "", "", errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return nil, nil, "", "", err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	lastTime := time.Duration(0)
	afterRandom := false
	interval := float64(0)

	var period models.Period

	// TODO 7064
	subjectMap := make(map[string]models.Subject)
	if !visit.BaseCohort.IsZero() && visit.CohortID != visit.BaseCohort {
		subjectMap, err = GetBaseCohortSubjectMap(nil, visit.BaseCohort, primitive.NilObjectID, subject.Info[0].Value.(string))

	}

	firstTime := time.Duration(0)
	for _, res := range resDispensing {
		if !res.VisitSign {
			if res.Status == 2 {
				interval = 0
				lastTime = res.DispensingTime
			}
			if res.VisitInfo.Random {
				afterRandom = true
			}
			if res.ID == dispensing.ID {
				//
				// TODO 7064
				randomTime := subject.RandomTime
				if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
					subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
				}
				if firstTime == 0 && res.DispensingTime != 0 && visitCycleMap[res.VisitInfo.Number].Interval != nil && randomTime == 0 {
					randomTime = time.Duration(time.Unix(int64(res.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[res.VisitInfo.Number].Interval)).Unix())
					firstTime = res.DispensingTime
				}
				period = tools.HandlePeriod(afterRandom, visit.VisitType, visitCycleMap[dispensing.VisitInfo.Number], randomTime, lastTime, dispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
			}
		}
	}
	planLeftTime := ""
	if len(period.MinPeriod) != 0 {
		planLeftTime = timestampConversion(period.MinPeriod)
	}
	planRightTime := ""
	if len(period.MaxPeriod) != 0 {
		planRightTime = timestampConversion(period.MaxPeriod)
	}

	if subject.RandomTime != 0 {
		//timeZone := time.Duration(intTimeZone)
		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		formattedDate := time.Unix(int64(subject.RandomTime), 0).UTC().Add(duration).UTC().Format("2006.01.02")
		if planLeftTime == "" {
			planLeftTime = formattedDate
		}
		if planRightTime == "" {
			planRightTime = formattedDate
		}
	}

	//planLeftTime := time.Duration(time.Unix(int64(workTask.Info.CreatedTime), 0).AddDate(0, 0, periodMin).Unix())
	//planRightTime := time.Duration(time.Unix(int64(workTask.Info.CreatedTime), 0).AddDate(0, 0, periodMax).Unix())

	siteIds = append(siteIds, projectSite.ID)
	siteName = projectSite.Name
	return siteIds, map[string]interface{}{
		"visitNumber":  visitNumber,
		"subjectName":  subjectName,
		"siteName":     siteName,
		"subjectId":    dispensing.SubjectID.Hex(),
		"randomNumber": subject.RandomNumber,
		"visitCycleId": visitCycleId}, planLeftTime, planRightTime, nil
}

func timestampConversion(dateStr string) string {
	timestamp := strings.Replace(dateStr, "-", ".", -1) // 第三个参数表示全局替换（-1）或仅替换首次出现的位置（0）
	return timestamp
}

// 研究中心订单申请 中心查询
func getAuroraPushSiteInfo(approvalProcessId primitive.ObjectID) ([]primitive.ObjectID, map[string]interface{}, error) {
	siteIds := make([]primitive.ObjectID, 0)
	orderTaskFilter := bson.M{"_id": approvalProcessId}
	var orderAddTask models.OrderAddTask
	err := tools.Database.Collection("approval_process").FindOne(nil, orderTaskFilter).Decode(&orderAddTask)
	if err != nil {
		return []primitive.ObjectID{}, nil, err
	}
	projectSiteFilter := bson.M{"_id": orderAddTask.Data.ReceiveID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, nil, err
	}
	siteIds = append(siteIds, projectSite.ID)
	siteName := projectSite.ShortName

	return siteIds, map[string]interface{}{"siteName": siteName}, nil
}

// 揭盲审批中心查询
func getAuroraPushSubjectSiteInfo(subjectID primitive.ObjectID) ([]primitive.ObjectID, map[string]interface{}, error) {
	siteIds := make([]primitive.ObjectID, 0)
	subjectFilter := bson.M{"_id": subjectID}
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, subjectFilter).Decode(&subject)
	if err != nil {
		return []primitive.ObjectID{}, nil, err
	}
	projectSiteFilter := bson.M{"_id": subject.ProjectSiteID}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, nil, err
	}
	siteIds = append(siteIds, projectSite.ID)
	siteName := projectSite.Name

	return siteIds, map[string]interface{}{"siteName": siteName}, nil
}

// 查询库房
func getAuroraPushStorehouseInfo(projectStorehouseId primitive.ObjectID) ([]primitive.ObjectID, map[string]interface{}, error) {
	depotIds := make([]primitive.ObjectID, 0)
	var projectStorehouse models.ProjectStorehouse
	err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": projectStorehouseId}).Decode(&projectStorehouse)
	if err != nil {
		return []primitive.ObjectID{}, nil, err
	}
	depotIds = append(depotIds, projectStorehouse.ID)
	var storehouse models.Storehouse
	err = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
	if err != nil {
		return []primitive.ObjectID{}, nil, err
	}
	storehouseName := storehouse.Name
	return depotIds, map[string]interface{}{"storehouseName": storehouseName}, nil
}

// 查询中心
func getAuroraPushProjectSiteInfo(projectSiteID primitive.ObjectID) ([]primitive.ObjectID, error) {
	siteIds := make([]primitive.ObjectID, 0)
	projectSiteFilter := bson.M{"_id": projectSiteID}
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(nil, projectSiteFilter).Decode(&projectSite)
	if err != nil {
		return []primitive.ObjectID{}, errors.WithStack(err)
	}
	siteIds = append(siteIds, projectSite.ID)
	//data.SiteName = models.GetProjectSiteName(ctx, projectSite)
	return siteIds, nil
}

// 查询registrationId  1扫码入仓 2发送订单确认 3研究产品接收 4发药确认 5补发药确认 6订单待运送 7创建订单审批 8紧急揭盲审批 9访视外发药 10pv揭盲审批 11发放通知 12包装扫码        31订单超时 32库房警戒 33中心警戒 15访视提醒
func getUserRegistrationId(wkType int, userIDs []primitive.ObjectID, workTask models.WorkTask, projectStorehouseId primitive.ObjectID, projectSiteId primitive.ObjectID) ([]models.UserRegistrationIdAppLanguage, error) {
	var userRegistrationIdAppLanguage []models.UserRegistrationIdAppLanguage
	//查询用户
	uFilter := bson.M{"_id": bson.M{"$in": userIDs}}
	users := make([]models.User, 0)
	uCursor, err := tools.Database.Collection("user").Find(nil, uFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = uCursor.All(nil, &users)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询用户绑定的项目
	upeFilter := bson.M{"user_id": bson.M{"$in": userIDs}, "app": true}
	envs := make([]models.UserProjectEnvironment, 0)
	upeCursor, err := tools.Database.Collection("user_project_environment").Find(nil, upeFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = upeCursor.All(nil, &envs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	envIds := slice.Map(envs, func(index int, item models.UserProjectEnvironment) primitive.ObjectID {
		return item.EnvID
	})

	// 关联中心
	siteFilter := bson.M{"env_id": bson.M{"$in": envIds}}
	sites := make([]models.UserSite, 0)
	siteCursor, err := tools.Database.Collection("user_site").Find(nil, siteFilter)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = siteCursor.All(nil, &sites)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 关联仓库
	depotFilter := bson.M{"env_id": bson.M{"$in": envIds}}
	depots := make([]models.UserDepot, 0)
	depotCursor, err := tools.Database.Collection("user_depot").Find(nil, depotFilter)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	err = depotCursor.All(nil, &depots)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 角色ID
	roleIds := make([]primitive.ObjectID, 0)
	for _, env := range envs {
		roleIds = append(roleIds, env.Roles...)
	}
	roleIds = slice.Unique(roleIds)

	// 查询项目角色权限
	prpFilter := bson.M{"_id": bson.M{"$in": roleIds}}
	projectRolePermissions := make([]models.ProjectRolePermission, 0)
	prpCursor, err := tools.Database.Collection("project_role_permission").Find(nil, prpFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = prpCursor.All(nil, &projectRolePermissions)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	siteIds := make([]primitive.ObjectID, 0)
	deportIds := make([]primitive.ObjectID, 0)

	switch wkType {
	case 2: //发送订单确认
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds, deportIds, _, err = getAuroraPushOrderInfo(workTask.Info.MedicineOrderID, project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 3: //研究产品接收
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds, deportIds, _, err = getAuroraPushOrderInfo(workTask.Info.MedicineOrderID, project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 4: //发药确认
		siteIds, _, err = getAuroraPushDispensingInfo(workTask.Info.DispensingID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 5: //补发药确认
		siteIds, _, err = getAuroraPushDispensingInfo(workTask.Info.DispensingID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 6: //订单待运送
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds, deportIds, _, err = getAuroraPushOrderInfo(workTask.Info.MedicineOrderID, project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 7: //创建订单审批
		siteIds, _, err = getAuroraPushSiteInfo(workTask.Info.ApprovalProcessID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 8: //紧急揭盲审批
		siteIds, _, err = getAuroraPushSubjectSiteInfo(workTask.Info.SubjectApproval.SubjectID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 9: //访视外发药
		siteIds, _, err = getAuroraPushDispensingInfo(workTask.Info.DispensingID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 10: //pv揭盲审批
		siteIds, _, err = getAuroraPushSubjectSiteInfo(workTask.Info.SubjectPvApproval.SubjectID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 11: //发放通知
		siteIds, _, err = getAuroraPushDispensingInfo(workTask.Info.DispensingID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 13: //发送回收订单待确认任务
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds, deportIds, _, err = getAuroraPushOrderInfo(workTask.Info.MedicineOrderID, project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 14: //研究产品接收发送回收订单待运送
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds, deportIds, _, err = getAuroraPushOrderInfo(workTask.Info.MedicineOrderID, project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 15: //回收订单接收任务
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds, deportIds, _, err = getAuroraPushOrderInfo(workTask.Info.MedicineOrderID, project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 31: //订单超时
		var project models.Project
		err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": workTask.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		siteIds, deportIds, _, err = getAuroraPushOrderInfo(workTask.Info.MedicineOrderID, project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 32: //库房警戒
		deportIds, _, err = getAuroraPushStorehouseInfo(projectStorehouseId)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	case 33: //中心警戒
		siteIds, err = getAuroraPushProjectSiteInfo(projectSiteId)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// 循环用户
	for _, u := range users {
		if u.RegistrationId != "" {
			// 筛选用户关联环境
			userEnvs := make([]models.UserProjectEnvironment, 0)
			for _, env := range envs {
				if env.UserID == u.ID {
					userEnvs = append(userEnvs, env)
				}
			}

			// 用户角色
			userRoleIds := make([]primitive.ObjectID, 0)
			for _, env := range userEnvs {
				userRoleIds = append(userRoleIds, env.Roles...)
			}
			userRoleIds = slice.Unique(userRoleIds)

			// 筛选项目权限
			userProjectRolePermissions := make([]models.ProjectRolePermission, 0)
			for _, up := range userRoleIds {
				for _, prp := range projectRolePermissions {
					if up == prp.ID {
						userProjectRolePermissions = append(userProjectRolePermissions, prp)
					}
				}
			}

			// 筛选用户关联的中心
			userSites := make([]models.UserSite, 0)
			for _, site := range sites {
				if site.UserID == u.ID {
					userSites = append(userSites, site)
				}
			}

			// 筛选用户关联的仓库
			userDepots := make([]models.UserDepot, 0)
			for _, depot := range depots {
				if depot.UserID == u.ID {
					userDepots = append(userDepots, depot)
				}
			}

			switch wkType {
			case 1: // 扫码入仓
				permissions := []string{"operation.build.medicine.barcode.scan"}
				haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, userEnvs, userProjectRolePermissions, permissions)
				if haveAppOperaPermission {
					userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
						RegistrationId: u.RegistrationId,
						AppLanguage:    u.AppLanguage,
					})
				}
			case 2: //发送订单确认
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, userEnvs, userProjectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, userEnvs, userProjectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 3: //研究产品接收
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.receive", "operation.supply.shipment.lose", "operation.supply.shipment.terminated"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 4: //发药确认
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.dispensing"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 5: //补发药确认
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.reissue"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 6: //订单待运送
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.send", "operation.supply.shipment.lose", "operation.supply.shipment.receive", "operation.supply.shipment.close"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 7: //创建订单审批
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.supply.shipment.approval"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 8: //紧急揭盲审批
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.unblinding-approval"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 9: //访视外发药
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.out-visit-dispensing"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 10: //pv揭盲审批
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.unblinding-pv-approval"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 11: //发放通知
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					permissions := []string{"operation.subject.medicine.dispensing"}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 12: //包装扫码
				permissions := []string{"operation.build.medicine.barcode.scanPackage "}
				haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, userEnvs, userProjectRolePermissions, permissions)
				if haveAppOperaPermission {
					userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
						RegistrationId: u.RegistrationId,
						AppLanguage:    u.AppLanguage,
					})
				}
			case 31: //订单超时
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, deportIds)
				if haveAppDataPermission {
					permissions := []string{
						"operation.supply.shipment.send",
						"operation.supply.shipment.lose",
						"operation.supply.shipment.receive",
						"operation.supply.shipment.close",
						"operation.supply.shipment.terminated",
					}
					haveAppOperaPermission := tools.HaveAppOperaPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, permissions)
					if haveAppOperaPermission {
						userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
							RegistrationId: u.RegistrationId,
							AppLanguage:    u.AppLanguage,
						})
					}
				}
			case 32: //库房警戒
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, []primitive.ObjectID{}, deportIds)
				if haveAppDataPermission {
					userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
						RegistrationId: u.RegistrationId,
						AppLanguage:    u.AppLanguage,
					})
				}
			case 33: //中心警戒
				haveAppDataPermission := tools.HaveAppDataPermission(u.ID, workTask.EnvironmentID, envs, projectRolePermissions, userSites, userDepots, siteIds, []primitive.ObjectID{})
				if haveAppDataPermission {
					userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
						RegistrationId: u.RegistrationId,
						AppLanguage:    u.AppLanguage,
					})
				}
			case 15: //访视提醒
				userRegistrationIdAppLanguage = append(userRegistrationIdAppLanguage, models.UserRegistrationIdAppLanguage{
					RegistrationId: u.RegistrationId,
					AppLanguage:    u.AppLanguage,
				})
			}
		}
	}
	return userRegistrationIdAppLanguage, nil
}
