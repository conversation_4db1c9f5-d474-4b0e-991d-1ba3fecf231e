package logging

import (
	"clinflash-irt/config"

	graylog "github.com/gemnasium/logrus-graylog-hook/v3"
	"github.com/sirupsen/logrus"
)

type nullWriter struct{}

func (w *nullWriter) Write(p []byte) (n int, err error) {
	return len(p), nil
}

func Init() {
	level, err := logrus.ParseLevel(config.LOG_LEVEL)
	if err != nil {
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.JSONFormatter{PrettyPrint: true})
	logrus.SetReportCaller(true)
	logrus.SetOutput(&nullWriter{})
	if config.LOG_OUTPUT == "graylog" {
		logrus.AddHook(graylog.NewAsyncGraylogHook(config.LOG_GRAYLOG_ADDR, map[string]interface{}{"tag": "clinflash-irt", "env": config.DB}))
	}
}
