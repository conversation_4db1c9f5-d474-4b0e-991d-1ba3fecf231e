package data

import "clinflash-irt/models"

var (
	RolePools = []models.RolePool{
		{Key: 0, Type: 2, Name: "Biostatistician [blinded]"},
		{Key: 1, Type: 1, Name: "Biostatistician [unblinded]"},
		{Key: 2, Type: 2, Name: "CRA [blinded]"},
		{Key: 3, Type: 1, Name: "CRA [unblinded]"},
		{Key: 4, Type: 3, Name: "CRC [blinded, can see room number temporarily]"},
		{Key: 5, Type: 2, Name: "CRC [blinded]"},
		{Key: 6, Type: 1, Name: "CRC [unblinded]"},
		{Key: 32, Type: 2, Name: "DM [blinded]"},
		{Key: 7, Type: 1, Name: "DM [unblinded]"},
		{Key: 8, Type: 1, Name: "IP Officer"},
		{Key: 9, Type: 1, Name: "IRT Designer"},
		{Key: 10, Type: 2, Name: "PI [blinded,can unblind]"},
		{Key: 11, Type: 2, Name: "PI [blinded]"},
		{Key: 12, Type: 1, Name: "PI [unblinded]"},
		{Key: 13, Type: 2, Name: "Pharmacist [blinded]"},
		{Key: 31, Type: 1, Name: "Pharmacist [unblinded]"},
		{Key: 14, Type: 2, Name: "Project Manager [blinded]"},
		{Key: 15, Type: 1, Name: "Project Manager [unblinded]"},
		{Key: 16, Type: 2, Name: "Safety Monitor [blinded,can unblind]"},
		{Key: 17, Type: 1, Name: "Safety Monitor [unblinded]"},
		{Key: 18, Type: 2, Name: "Shipper Manager [blinded]"},
		{Key: 19, Type: 1, Name: "Shipper Manager [unblinded]"},
		{Key: 20, Type: 2, Name: "Sub-I [blinded]"},
		{Key: 21, Type: 1, Name: "Sub-I [unblinded]"},
		{Key: 22, Type: 2, Name: "Supply Manager [blinded]"},
		{Key: 23, Type: 1, Name: "Supply Manager [unblinded]"},
		{Key: 24, Type: 4, Name: "Customer-Admin"},
		{Key: 25, Type: 5, Name: "Sys-Admin"},
		{Key: 26, Type: 6, Name: "Project-Admin"},
		{Key: 27, Type: 2, Name: "Read-Only[blinded]"},
		{Key: 28, Type: 1, Name: "Read-Only[unblinded]"},
		{Key: 29, Type: 2, Name: "Sponsor[blinded]"},
		{Key: 30, Type: 1, Name: "Sponsor[unblinded]"},
		{Key: 33, Type: 2, Name: "Reportor[blinded]"},
		{Key: 34, Type: 1, Name: "Reportor[unblinded]"},
	}
	RolePoolMap map[string]models.RolePool
)

var (
	RoleNoticeInit = map[string][]string{
		"notice.basic.settings":           {},
		"notice.subject.add":              {},
		"notice.subject.random":           {"CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Pharmacist [blinded]", "PI [blinded]", "Sub-I [blinded]"},
		"notice.subject.signOut":          {"IP Officer"},
		"notice.subject.replace":          {"IP Officer"},
		"notice.subject.update":           {"IP Officer"},
		"notice.subject.dispensing":       {"CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Pharmacist [blinded]", "PI [blinded]", "Sub-I [blinded]"},
		"notice.subject.alarm":            {"Biostatistician [unblinded]", "CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Project Manager [blinded]", "Pharmacist [blinded]", "PI [blinded]", "Sub-I [blinded]"},
		"notice.subject.unblinding":       {"Biostatistician [unblinded]", "CRA [blinded]", "IP Officer", "PI [blinded,can unblind]", "Project Manager [blinded]", "Safety Monitor [blinded,can unblind]", "PI [blinded]", "Sub-I [blinded]"},
		"notice.medicine.isolation":       {"CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Pharmacist [blinded]", "PI [blinded]", "Sub-I [blinded]"},
		"notice.medicine.order":           {"CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Pharmacist [blinded]", "PI [blinded]", "Project Manager [blinded]", "Shipper Manager [blinded]", "Sub-I [blinded]"},
		"notice.medicine.reminder":        {"CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Pharmacist [blinded]", "PI [blinded]", "Project Manager [blinded]", "Shipper Manager [blinded]", "Sub-I [blinded]"},
		"notice.medicine.alarm":           {"CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Pharmacist [blinded]", "PI [blinded]", "Project Manager [blinded]", "Shipper Manager [blinded]", "Sub-I [blinded]"},
		"notice.storehouse.alarm":         {},
		"notice.order.timeout":            {"CRA [blinded]", "CRC [blinded]", "IP Officer", "PI [blinded,can unblind]", "Pharmacist [blinded]", "PI [blinded]", "Project Manager [blinded]", "Shipper Manager [blinded]", "Sub-I [blinded]"},
		"notice.subject.alert.threshold":  {"IP Officer", "IRT Designer", "Biostatistician [unblinded]"},
		"notice.subject.screen":           {},
		"notice.medicine.un_provide_date": {"IP Officer"},
	}
)

func init() {
	rolePoolMap := make(map[string]models.RolePool)
	for _, role := range RolePools {
		rolePoolMap[role.Name] = role
	}
	RolePoolMap = rolePoolMap
}
