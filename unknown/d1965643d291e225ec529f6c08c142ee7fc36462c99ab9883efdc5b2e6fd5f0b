package tools

import (
	"fmt"
	"github.com/pkg/errors"
	"math"
	"strconv"
	"strings"
	"time"
)

// GetUTCOffsetString 支持半时区，例如 UTC+08:45
func GetUTCOffsetString(location string) (string, error) {
	if location == "" {
		return "", nil
	}

	loc, err := time.LoadLocation(location)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 获取该时区当前时间
	now := time.Now().In(loc)

	// 计算与UTC的偏移量（秒）
	_, offset := now.Zone()

	hours := offset / 3600          // 将秒转换为小时
	minutes := (offset % 3600) / 60 // 剩余的秒数转换为分钟

	// 确定符号并处理负数情况
	sign := "+"
	if hours < 0 {
		sign = "-"
	}
	hoursAbs := abs(hours)     // 绝对值用于显示
	minutesAbs := abs(minutes) // 绝对值用于显示

	// 格式化输出
	timeStr := fmt.Sprintf("UTC%s%02d:%02d", sign, hoursAbs, minutesAbs)
	return timeStr, nil
}

func GetLocationTimeZone(location string) (string, error) {
	loc, err := time.LoadLocation(location)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 获取该时区当前时间
	now := time.Now().In(loc)

	// 计算与UTC的偏移量（秒）
	_, offset := now.Zone()

	hours := offset / 3600          // 将秒转换为小时
	minutes := (offset % 3600) / 60 // 剩余的秒数转换为分钟

	// 确定符号并处理负数情况
	sign := ""
	if hours < 0 {
		sign = "-"
	}
	hoursAbs := abs(hours)     // 绝对值用于显示
	minutesAbs := abs(minutes) // 绝对值用于显示

	// 格式化输出
	timeStr := fmt.Sprintf("%s%02d:%02d", sign, hoursAbs, minutesAbs)
	return timeStr, nil
}

// 计算时区的时间并且拼上(时区)
func GetLocationUtc(location string, date int64) (string, error) {
	loc, err := time.LoadLocation(location)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 获取该时区相对于UTC的偏移量（秒）
	_, offset := time.Unix(date, 0).In(loc).Zone()

	// 计算小时和分钟偏移
	hours := offset / 3600
	minutes := (offset % 3600) / 60

	// 确定符号并处理负数情况
	sign := "+"
	if hours < 0 {
		sign = "-"
	}
	hoursAbs := abs(hours)     // 绝对值用于显示
	minutesAbs := abs(minutes) // 绝对值用于显示

	// 构建时区字符串，例如 "UTC+2:30"
	timezoneStr := fmt.Sprintf("UTC%s%02d:%02d", sign, hoursAbs, minutesAbs)

	// 使用 FixedZone 创建一个包含完整偏移信息的时区
	fixedLoc := time.FixedZone(timezoneStr, offset)

	// 格式化时间字符串
	timeStr := time.Unix(date, 0).In(fixedLoc).Format("2006-01-02 15:04:05(MST)")

	return timeStr, nil
}

// 计算时区的时间
func GetLocation(location string, date int64) (string, error) {
	loc, err := time.LoadLocation(location)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 获取该时区相对于UTC的偏移量（秒）
	_, offset := time.Unix(date, 0).In(loc).Zone()

	// 计算小时和分钟偏移
	hours := offset / 3600
	minutes := (offset % 3600) / 60

	// 确定符号并处理负数情况
	sign := "+"
	if hours < 0 {
		sign = "-"
	}
	hoursAbs := abs(hours)     // 绝对值用于显示
	minutesAbs := abs(minutes) // 绝对值用于显示

	// 构建时区字符串，例如 "UTC+2:30"
	timezoneStr := fmt.Sprintf("UTC%s%02d:%02d", sign, hoursAbs, minutesAbs)

	// 使用 FixedZone 创建一个包含完整偏移信息的时区
	fixedLoc := time.FixedZone(timezoneStr, offset)

	// 格式化时间字符串
	timeStr := time.Unix(date, 0).In(fixedLoc).Format("2006-01-02 15:04:05")

	return timeStr, nil
}

// 计算传入时间的时区 支持半时区，例如 UTC+08:45
func GetLocationTZ(location string, date int64) (string, error) {
	loc, err := time.LoadLocation(location)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 获取该时区相对于UTC的偏移量
	_, offset := time.Unix(date, 0).In(loc).Zone()

	hours := offset / 3600          // 将秒转换为小时
	minutes := (offset % 3600) / 60 // 剩余的秒数转换为分钟

	// 确定符号并处理负数情况
	sign := "+"
	if hours < 0 {
		sign = "-"
	}
	hoursAbs := abs(hours)     // 绝对值用于显示
	minutesAbs := abs(minutes) // 绝对值用于显示

	// 格式化输出
	timeStr := fmt.Sprintf("UTC%s%02d:%02d", sign, hoursAbs, minutesAbs)
	return timeStr, nil
}

// FormatOffsetToZoneString 将浮点数的时区偏移转换为半时区格式
func FormatOffsetToZoneString(offset float64) string {
	hours := int(math.Floor(math.Abs(offset)))                           // 获取绝对值的小时部分
	minutes := int(math.Round((math.Abs(offset) - float64(hours)) * 60)) // 计算分钟部分并四舍五入

	// 确定符号
	sign := "+"
	if offset < 0 {
		sign = "-"
	}

	// 如果分钟部分是60，需要调整小时和分钟
	if minutes == 60 {
		hours += 1
		minutes = 0
	}

	return fmt.Sprintf("%s%02d:%02d", sign, hours, minutes)
}

// FormatOffsetToZoneStringUtc 将浮点数的时区偏移转换为半时区格式
func FormatOffsetToZoneStringUtc(offset float64) string {
	hours := int(math.Floor(math.Abs(offset)))                           // 获取绝对值的小时部分
	minutes := int(math.Round((math.Abs(offset) - float64(hours)) * 60)) // 计算分钟部分并四舍五入

	// 确定符号
	sign := "+"
	if offset < 0 {
		sign = "-"
	}

	// 如果分钟部分是60，需要调整小时和分钟
	if minutes == 60 {
		hours += 1
		minutes = 0
	}

	return fmt.Sprintf("UTC%s%02d:%02d", sign, hours, minutes)
}

// 辅助函数：计算绝对值
func abs(a int) int {
	if a < 0 {
		return -a
	}
	return a
}

// GetLocationString 将"Pacific/Marquesas"转为-9.5
func GetLocationFloat(location string) (float64, error) {
	// 加载 Marquesas 时区
	loc, err := time.LoadLocation(location)
	if err != nil {
		return 0, nil
	}
	// 获取当前时间在该时区的时间
	now := time.Now().In(loc)

	// 获取时区信息
	_, offset := now.Zone()

	// 将秒转换为小时（包括小数部分）
	offsetHours := float64(offset) / 3600

	return offsetHours, nil
}

// 计算时区的时间 "-8.75"
func GetProjectLocation(timeZone string, tz string, utc string) (float64, error) {
	timeFloat := float64(0)
	if len(tz) != 0 {
		timeStr, err := GetLocationFloat(tz)
		if err != nil {
			return timeFloat, errors.WithStack(err)
		}
		timeFloat = timeStr
	} else if len(timeZone) != 0 {
		// 尝试将字符串转换为 float64
		floatValue, err := strconv.ParseFloat(timeZone, 64)
		if err != nil {
			return timeFloat, errors.WithStack(err)
		}
		timeFloat = floatValue
	} else if len(utc) != 0 {
		// 将字符串转换为 float64
		f, err := strconv.ParseFloat(strings.Replace(utc, "UTC", "", 1), 64)
		if err != nil {
			return timeFloat, errors.WithStack(err)
		}
		timeFloat = f
	}
	return timeFloat, nil
}

// 计算时区的时间 "-8.75"
func GetProjectLocationUtc(timeZone string, tz string, utc string) (string, error) {
	timeStr := ""
	if len(tz) != 0 {
		str, err := GetUTCOffsetString(tz)
		if err != nil {
			return "", nil
		}
		timeStr = str
	} else if len(timeZone) != 0 {
		// 尝试将字符串转换为 float64
		floatValue, err := strconv.ParseFloat(timeZone, 64)
		if err != nil {
			return timeStr, nil
		}
		timeStr = FormatOffsetToZoneStringUtc(floatValue)
	} else if len(utc) != 0 {
		f, err := strconv.ParseFloat(strings.Replace(utc, "UTC", "", 1), 64)
		if err != nil {
			return timeStr, errors.WithStack(err)
		}
		timeStr = fmt.Sprintf("UTC%+d", f)
	}

	return timeStr, nil
}

// 将-8.75和传入的时间戳转为对应时区的时间
func FormatFloatTime(timeZone string, tz string, utc string, data int64, t string) string {

	// 时区偏移量 -8.75 小时
	f, _ := GetProjectLocation(timeZone, tz, utc)

	// 假设这是你的原始时间（Unix 时间戳）
	dispensingTime := data // 示例时间戳

	// 将小数小时转换为 Duration
	// 1. 计算小时部分
	hours := int(f)
	// 2. 计算分钟部分（0.75 * 60 = 45 分钟）
	minutes := int((f - float64(hours)) * 60)
	// 3. 组合成 Duration
	offsetDuration := time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute

	// 应用时区偏移
	dTime := time.Unix(dispensingTime, 0).UTC().Add(offsetDuration).Format(t)

	return dTime
}

// 将-8.75时区的时间
func FormatFloatTimeHour(timeZone string, tz string, utc string) string {
	now := time.Now().UTC() // 当前 UTC 时间

	// 时区偏移量 -8.75 小时
	f, _ := GetProjectLocation(timeZone, tz, utc)

	// 处理半时区
	hours := int(f)
	minutes := (f - float64(hours)) * 60
	duration := time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute

	// 计算并格式化时间
	changeTime := now.Add(duration).Format("15:04")
	fmt.Printf("UTC时间: %v\n", now.Format("15:04"))
	fmt.Printf("转换后时间（时区 %.2f）: %v\n", timeZone, changeTime)

	return changeTime
}

// ParseTimezoneOffset 将 UTC±HH:mm 格式转换成 float64 类型的小时数
func ParseTimezoneOffset(offsetStr string) (float64, error) {
	// 去掉前缀 "UTC"
	s := strings.TrimPrefix(offsetStr, "UTC")

	// 提取符号
	sign := 1.0
	if strings.HasPrefix(s, "-") {
		sign = -1.0
		s = s[1:]
	} else if strings.HasPrefix(s, "+") {
		s = s[1:]
	}

	// 分割小时和分钟
	parts := strings.Split(s, ":")
	if len(parts) != 2 {
		return 0, fmt.Errorf("invalid format")
	}

	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, err
	}

	minutes, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, err
	}

	// 检查是否是合法的分钟（通常是 00、15、30、45）
	if minutes%15 != 0 || minutes >= 60 {
		return 0, fmt.Errorf("invalid minutes: %d", minutes)
	}

	// 合成浮点数结果
	return sign * (float64(hours) + float64(minutes)/60.0), nil
}
