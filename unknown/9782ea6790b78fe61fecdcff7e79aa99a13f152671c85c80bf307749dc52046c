import { get, patch, post } from "utils/http";

export const getOrderMedicines = (params: object = {}) => get(`/order`, { params });
export const getOrderList = (params: object = {}) => get(`/order/list`, { params });
export const getOrderSubject = (params: object = {}) => get(`/order/subject`, { params });
export const getDtpOrderList = (params: object = {}) => get(`/order/dtp/list`, { params });
export const downloadOrder = (params: object = {}) => get(`/order/download`, { params });
export const downloadDtpOrder = (params: object = {}) => get(`/order/dtp/download`, { params });
export const updateOrderStatus = (data: object = {}) => patch(`/order`, { data });
export const alarmMedicine = (data: object = {}) => post(`/order/alarm-medicine`, { data });
export const addOrder = (data: object = {}) => post(`/order`, { data });
export const confirmOrder = (data: object = {}) => patch(`/order/confirm`, { data });
export const recoverConfirmOrder = (data: object = {}) => patch(`/order/recover-confirm`, { data });
export const addRecoveryOrder = (data: object = {}) => post(`/order/add-recovery-order`, { data });
export const updateRecoveryOrder = (data: object = {}) => patch(`/order/update-recovery-order`, { data });
export const receiveOrder = (data: object = {}) => patch(`/order/receive`, { data });
export const receiveRecoveryOrder = (data: object = {}) => patch(`/order/receive-recovery`, { data });
export const changeOrderMedicines = (data: object = {}) => patch(`/order/change-medicines`, { data });
export const getChangeOrderRecord = (params: Object = {}) => get("/order/change-records", { params })
export const getLogistics = (params: object = {}) => get(`/logistics`, { params });
export const getLogisticsCompanyCode = (params: object = {}) => get(`/logistics/company-code`, { params });
export const getCompanyCode = (params: object = {}) => get(`/logistics/company/code`, { params });


export const updateExpirationSingle = (data: object = {}) => post(`/order/update-expiration-single`, { data });

export const updateExpirationBatch = (data: object = {}) => post(`/order/update-expiration-batch`, { data });
