package tools

import (
	"clinflash-irt/models"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strconv"
	"time"
)

func GetTimeZone(projectOID primitive.ObjectID) (float64, error) {
	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err := Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	return timeZone, nil
}

func GetSiteTimeZone(projectSiteOID primitive.ObjectID) (string, error) {
	var projectSite models.ProjectSite
	err := Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteOID}).Decode(&projectSite)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", errors.WithStack(err)
	}
	timeZone := projectSite.TimeZone
	if projectSite.Tz != "" {
		offsetString, e := GetUTCOffsetString(projectSite.Tz)
		if e != nil {
			return "", errors.WithStack(e)
		}
		timeZone = offsetString
	}
	return timeZone, nil
}

func GetSiteTimeZoneInfo(projectSite models.ProjectSite) (string, error) {
	timeZone := projectSite.TimeZone
	if projectSite.Tz != "" {
		offsetString, e := GetUTCOffsetString(projectSite.Tz)
		if e != nil {
			return "", errors.WithStack(e)
		}
		timeZone = offsetString
	}
	return timeZone, nil
}

func GetTimeZoneTime(utc time.Time, projectSite models.ProjectSite, project models.Project) (string, error) {
	shTime := ""
	if projectSite.TimeZone != "" {
		strTimeZone, _ := GetSiteTimeZoneInfo(projectSite)
		timeZone, _ := ParseTimezoneOffset(strTimeZone)

		hours := time.Duration(timeZone)
		minutes := time.Duration((timeZone - float64(hours)) * 60)

		duration := hours*time.Hour + minutes*time.Minute

		//timeZone, _ := strconv.Atoi(strings.Replace(projectSite.TimeZone, "UTC", "", 1))
		shTime = utc.Add(duration).Format("2006-01-02 15:04:05")
		shTime = shTime + "(" + projectSite.TimeZone + ")"

	} else {
		timeZone, err := GetTimeZone(project.ID)
		if err != nil {
			return "", err
		}
		shTime = utc.Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
		var showTimeZone string
		if timeZone > 0 {
			showTimeZone = "+" + strconv.FormatInt(int64(timeZone), 10)
		} else {
			showTimeZone = strconv.FormatInt(int64(timeZone), 10)
		}
		shTime = shTime + " (UTC" + showTimeZone + ")"
	}
	return shTime, nil
}
