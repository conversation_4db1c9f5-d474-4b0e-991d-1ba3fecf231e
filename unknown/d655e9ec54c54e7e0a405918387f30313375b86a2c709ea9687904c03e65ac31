package tools

import (
	"clinflash-irt/models"
	"time"
)

func HandleVisitDate(afterRandom bool, visitType int, info models.VisitCycleInfo, baseTime time.Duration, lastTime time.Duration, currentTime time.Duration, timeZoneInt float64, interval *float64) models.Period {
	var period models.Period
	if info.Interval != nil {
		return period
	}
	Unit, Interval, PeriodMin := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)
	_, _, PeriodMax := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMax)
	converTimes := ConvertTime(Unit, Interval, 0)
	periodMin := ConvertTime(Unit, Interval, PeriodMin)
	periodMax := ConvertTime(Unit, Interval, PeriodMax)
	if (visitType == 0 && baseTime != 0) || (visitType == 1 && lastTime != 0) {
		// 计算最小和最大时间
		var minTime, maxTime, lineTime time.Time
		//timeZone := time.Duration(timeZoneInt)

		hours := time.Duration(timeZoneInt)
		minutes := time.Duration((timeZoneInt - float64(hours)) * 60)

		duration := hours*time.Hour + minutes*time.Minute
		if visitType == 0 { //基准日期
			lineTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(converTimes)).Add(duration)
			minTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(periodMin)).UTC().Add(duration)
			maxTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(periodMax)).Add(duration)
		} else { //上一次发药日期
			lineTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(converTimes)).Add(duration)
			minTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(periodMin)).Add(duration)
			maxTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(periodMax)).Add(duration)
		}

		// 判断是否超出时间范围
		//if (currentTime < time.Duration(minTime.Unix()) || currentTime > time.Duration(maxTime.Unix())) && currentTime != 0 {
		//	period.OutSize = true
		//}
		// 格式化时间
		period.MinPeriod = minTime.UTC().Format("2006-01-02")
		period.MaxPeriod = maxTime.UTC().Format("2006-01-02")
		period.LineTime = lineTime.UTC().Format("2006-01-02")

		current := time.Unix(int64(currentTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
		if (current < period.MinPeriod || current > period.MaxPeriod) && currentTime != 0 {
			period.OutSize = true
		}
	}

	// 间隔时间计算
	*interval = *interval + ConvertTime(Unit, Interval, 0)

	return period
}

func HandlePeriod(afterRandom bool, visitType int, info models.VisitCycleInfo, baseTime time.Duration, lastTime time.Duration, currentTime time.Duration, timeZoneInt float64, interval *float64, attribute models.Attribute, joinTime string) models.Period {
	var period models.Period
	if info.Interval == nil {
		return period
	}
	Unit, Interval, PeriodMin := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)
	_, _, PeriodMax := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMax)
	converTimes := ConvertTime(Unit, Interval, 0)
	periodMin := ConvertTime(Unit, Interval, PeriodMin)
	periodMax := ConvertTime(Unit, Interval, PeriodMax)
	if (visitType == 0 && (baseTime != 0 || joinTime != "")) || (visitType == 1 && lastTime != 0) {
		// 计算最小和最大时间
		var minTime, maxTime, lineTime time.Time
		//timeZone := time.Duration(timeZoneInt)
		hours := time.Duration(timeZoneInt)
		minutes := time.Duration((timeZoneInt - float64(hours)) * 60)
		duration := hours*time.Hour + minutes*time.Minute
		if visitType == 0 { //基准日期
			// TODO 仅发药并且配置了入组时间
			if !attribute.AttributeInfo.Random && joinTime != "" {
				parse, err := time.Parse("2006-01-02", joinTime)
				if err != nil {
					return period
				}
				lineTime = parse.Add(time.Hour * time.Duration(converTimes))
				minTime = parse.Add(time.Hour * time.Duration(periodMin))
				maxTime = parse.Add(time.Hour * time.Duration(periodMax))
			} else if baseTime != 0 {
				lineTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(converTimes)).Add(duration)
				minTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(periodMin)).Add(duration)
				maxTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(periodMax)).Add(duration)
			}

		} else { //上一次发药日期
			lineTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(converTimes)).Add(duration)
			minTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(periodMin)).Add(duration)
			maxTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(periodMax)).Add(duration)
		}

		// 判断是否超出时间范围
		//if (currentTime < time.Duration(minTime.Unix()) || currentTime > time.Duration(maxTime.Unix())) && currentTime != 0 {
		//	period.OutSize = true
		//}
		// 格式化时间
		period.MinPeriod = minTime.UTC().Format("2006-01-02")
		period.MaxPeriod = maxTime.UTC().Format("2006-01-02")
		period.LineTime = lineTime.UTC().Format("2006-01-02")
		period.MaximumTime = maxTime.UTC().Unix()

		current := time.Unix(int64(currentTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
		if (current < period.MinPeriod || current > period.MaxPeriod) && currentTime != 0 {
			period.OutSize = true
			period.OutSizeWindow = true
		}

		if currentTime == 0 {
			now := time.Now().UTC().Add(duration).UTC().Format("2006-01-02")
			if now > period.MaxPeriod {
				period.OutSize = true
			}
			if now < period.MinPeriod || now > period.MaxPeriod {
				period.OutSizeWindow = true
			}
		}
	}

	// 间隔时间计算
	*interval = *interval + ConvertTime(Unit, Interval, 0)

	return period
}

func HandleDate(visitType int, info models.VisitCycleInfo, baseTime time.Duration, lastTime time.Duration, timeZoneInt float64, interval *float64, attribute models.Attribute, joinTime string) string {
	var lineTimeStr string
	Unit, Interval, _ := ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)
	converTimes := ConvertTime(Unit, Interval, 0)
	if (visitType == 0 && (baseTime != 0 || joinTime != "")) || (visitType == 1 && lastTime != 0) {
		// 计算最小和最大时间
		var lineTime time.Time
		//timeZone := time.Duration(timeZoneInt)
		if visitType == 0 { //基准日期
			if baseTime != 0 {
				// 根据 baseTime 创建 time.Time 对象
				t := time.Unix(int64(baseTime), 0)

				// 添加 converTimes 小时
				t = t.Add(time.Hour * time.Duration(converTimes))

				// 计算 timeZone 的小时和分钟部分
				hours := int(timeZoneInt)                           // 获取小时部分
				minutes := int((timeZoneInt - float64(hours)) * 60) // 计算分钟部分

				// 构造完整的 Duration 包括小时和分钟
				duration := time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute
				lineTime = t.Add(duration)
				//lineTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(converTimes)).Add(time.Hour * timeZone)
			}
			if !attribute.AttributeInfo.Random && joinTime != "" {
				parse, _ := time.Parse("2006-01-02", joinTime)
				lineTime = parse.Add(time.Hour * time.Duration(converTimes))

			}
		} else { //上一次发药日期
			hours := time.Duration(timeZoneInt)
			minutes := time.Duration((timeZoneInt - float64(hours)) * 60)

			duration := hours*time.Hour + minutes*time.Minute

			lineTime = time.Unix(int64(lastTime), 0).
				Add(time.Hour * time.Duration(*interval)).
				Add(time.Hour * time.Duration(converTimes)).
				Add(duration)

			//lineTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(converTimes)).Add(time.Hour * timeZone)
		}
		// 格式化时间
		lineTimeStr = lineTime.UTC().Format("2006-01-02")
	}
	// 间隔时间计算
	*interval = *interval + ConvertTime(Unit, Interval, 0)

	return lineTimeStr
}
