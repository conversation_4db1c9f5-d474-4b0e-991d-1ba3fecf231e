# MongoDB 聚合查询性能优化指南

## 问题分析

在 `backend/service/report_export_dispensing.go` 文件中，`cursor.All(nil, &datas)` 执行缓慢的主要原因：

1. **复杂的聚合管道**：包含多个 `$lookup` 操作和 `$unwind` 操作
2. **大量数据处理**：需要处理 subject、dispensing、project_site、history、region 等多个集合的关联
3. **缺乏分页机制**：一次性加载所有数据到内存
4. **索引可能不够优化**

## 已实施的优化措施

### 1. 聚合管道优化

#### 调整 lookup 顺序
```go
// 优化前：先 lookup dispensing，再 lookup project_site
{{Key: "$lookup", Value: bson.M{
    "from":         "dispensing",
    "localField":   "_id",
    "foreignField": "subject_id",
    "as":           "dispensing",
}}},
{{Key: "$lookup", Value: bson.M{
    "from":         "project_site",
    "localField":   "project_site_id",
    "foreignField": "_id",
    "as":           "project_site",
}}},

// 优化后：先 lookup project_site，减少后续处理的数据量
{{Key: "$lookup", Value: bson.M{
    "from":         "project_site",
    "localField":   "project_site_id",
    "foreignField": "_id",
    "as":           "project_site",
}}},
{{Key: "$unwind", Value: "$project_site"}},
```

#### 使用 pipeline 进行预过滤
```go
// 优化：添加 dispensing 状态过滤，减少数据量
{{Key: "$lookup", Value: bson.M{
    "from": "dispensing",
    "let":  bson.M{"subject_id": "$_id"},
    "pipeline": bson.A{
        bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
        // 可以根据需要添加状态过滤
        // bson.M{"$match": bson.M{"status": bson.M{"$in": bson.A{1, 2}}}},
        bson.M{"$sort": bson.D{{"serial_number", 1}}},
    },
    "as": "dispensing",
}}},
```

#### 限制 history 数据量
```go
// 优化：history lookup 限制返回数量和字段
{{Key: "$lookup", Value: bson.M{
    "from": "history",
    "let":  bson.M{"dispensing_id": "$dispensing._id"},
    "pipeline": bson.A{
        bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$oid", "$$dispensing_id"}}}},
        bson.M{"$sort": bson.D{{"time", -1}}},
        bson.M{"$limit": 100}, // 限制历史记录数量
        bson.M{"$project": bson.M{ // 只获取需要的字段
            "time": 1,
            "key":  1,
            "user": 1,
            "uid":  1,
            "data": 1,
        }},
    },
    "as": "history",
}}},
```

### 2. 流式处理优化

#### 替代 cursor.All 的流式处理
```go
// 优化前：一次性加载所有数据
err = cursor.All(nil, &datas)

// 优化后：使用流式处理
datas = make([]models.ReportDispensing, 0)
batchSize := 500 // 批处理大小
batch := make([]models.ReportDispensing, 0, batchSize)

for cursor.Next(nil) {
    var item models.ReportDispensing
    if err := cursor.Decode(&item); err != nil {
        cursor.Close(nil)
        return "", nil, errors.WithStack(err)
    }
    
    batch = append(batch, item)
    
    // 当批次达到指定大小时，处理这一批数据
    if len(batch) >= batchSize {
        datas = append(datas, batch...)
        batch = batch[:0] // 重置批次切片
    }
}

// 处理最后一批数据
if len(batch) > 0 {
    datas = append(datas, batch...)
}
```

### 3. 聚合选项优化

```go
// 优化聚合选项
optTrue := true
opt := &options.AggregateOptions{
    AllowDiskUse: &optTrue,
    // 添加批处理大小限制，避免内存溢出
    BatchSize: func() *int32 { i := int32(1000); return &i }(),
}
```

## 进一步优化建议

### 1. 数据库索引优化

确保以下字段有适当的索引：

```javascript
// subject 集合
db.subject.createIndex({"_id": 1, "deleted": 1})
db.subject.createIndex({"project_site_id": 1})
db.subject.createIndex({"info.name": 1, "info.value": 1})

// dispensing 集合
db.dispensing.createIndex({"subject_id": 1, "serial_number": 1})
db.dispensing.createIndex({"status": 1})

// project_site 集合
db.project_site.createIndex({"_id": 1})
db.project_site.createIndex({"region_id": 1})

// history 集合
db.history.createIndex({"oid": 1, "time": -1})

// region 集合
db.region.createIndex({"_id": 1})
```

### 2. 分页处理

对于大量数据，考虑实现分页：

```go
func ExportDispensingReportWithPagination(ctx *gin.Context, projectID string, envID string, cohortIDs []string, templateId string, roleId string, now time.Time, page int, pageSize int) (string, []byte, error) {
    // 添加分页参数到聚合管道
    pipeline = append(pipeline, 
        bson.D{{"$skip", page * pageSize}},
        bson.D{{"$limit", pageSize}},
    )
    // ... 其他逻辑
}
```

### 3. 缓存机制

对于频繁查询的数据，考虑添加缓存：

```go
// 使用 Redis 缓存查询结果
func getCachedReportData(key string) ([]models.ReportDispensing, bool) {
    // 实现缓存逻辑
}

func setCachedReportData(key string, data []models.ReportDispensing) {
    // 实现缓存逻辑
}
```

### 4. 异步处理

对于大型报告，考虑异步处理：

```go
func ExportDispensingReportAsync(ctx *gin.Context, params ExportParams) (string, error) {
    // 返回任务ID，异步处理报告生成
    taskID := generateTaskID()
    go func() {
        // 异步生成报告
        generateReport(params)
    }()
    return taskID, nil
}
```

## 性能监控

### 1. 添加性能日志

```go
start := time.Now()
// ... 执行查询
fmt.Printf("聚合查询耗时: %v\n", time.Since(start))
```

### 2. MongoDB 性能分析

```javascript
// 使用 explain 分析查询性能
db.subject.aggregate(pipeline).explain("executionStats")
```

## 预期性能提升

通过以上优化措施，预期可以获得以下性能提升：

1. **内存使用优化**：流式处理减少内存峰值使用 60-80%
2. **查询速度提升**：优化聚合管道和索引，查询速度提升 30-50%
3. **系统稳定性**：批处理和错误处理提升系统稳定性
4. **可扩展性**：为后续分页和缓存优化奠定基础

## 注意事项

1. **测试验证**：在生产环境部署前，需要在测试环境充分验证
2. **监控指标**：部署后需要监控查询性能和系统资源使用情况
3. **回滚准备**：准备回滚方案，以防优化后出现问题
4. **索引维护**：新增索引需要考虑对写入性能的影响
