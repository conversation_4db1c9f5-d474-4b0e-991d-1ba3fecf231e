import React from "react";
import {Row, Tooltip} from "antd";
import {FormattedMessage} from "../pages/common/multilingual/component";

export const nilObjectId = "000000000000000000000000"
export const blindData = "**********"

// 系统默认语言
export const defaultLanguageOptions = [
    {label: "中文", value: "zh"},
    {label: "English", value: "en"},
]

export const getDefaultLocale = (lang) => {
    if (!lang) return 'en'
    return !!defaultLanguageOptions.find(it => it.value === lang) ? lang : 'en'
}

export const projectTypes = [
    { key: 1, value: <FormattedMessage id="projects.types.first" /> },
    { key: 2, value: <FormattedMessage id="projects.types.second" /> },
    { key: 3, value: <FormattedMessage id="projects.types.third" /> }
];

//1:库房 2：中心 3：订单
export const batchManagementTypes = [
    { key: 2, value: <FormattedMessage id="common.depot" /> },
    { key: 1, value: <FormattedMessage id="common.site" /> },
    { key: 3, value: <FormattedMessage id="batchMag.type.order" /> }
];


export const projectResearch = [
    { key: 0, value: <FormattedMessage id="projects.research.site" /> },
    { key: 1, value: <FormattedMessage id="projects.research.dtp" /> },
]

export const projectUtc = [
    { value: 0, label: 'UTC' },
    { value: 1, label: 'UTC+1' },
    { value: 2, label: 'UTC+2' },
    { value: 3, label: 'UTC+3' },
    { value: 4, label: 'UTC+4' },
    { value: 5, label: 'UTC+5' },
    { value: 6, label: 'UTC+6' },
    { value: 7, label: 'UTC+7' },
    { value: 8, label: 'UTC+8' },
    { value: 9, label: 'UTC+9' },
    { value: 10, label: 'UTC+10' },
    { value: 11, label: 'UTC+11' },
    { value: 12, label: 'UTC+12' },
    { value: -1, label: 'UTC-1' },
    { value: -2, label: 'UTC-2' },
    { value: -3, label: 'UTC-3' },
    { value: -4, label: 'UTC-4' },
    { value: -5, label: 'UTC-5' },
    { value: -6, label: 'UTC-6' },
    { value: -7, label: 'UTC-7' },
    { value: -8, label: 'UTC-8' },
    { value: -9, label: 'UTC-9' },
    { value: -10, label: 'UTC-10' },
    { value: -11, label: 'UTC-11' },
    { value: -12, label: 'UTC-12' }
];


export const EdcSupplier = [
    { value: 1, label: 'Clinflash  EDC' },
    { value: 2, label: 'Medidata Rave  EDC' },
];

export const enviroments = [
    { key: 1, value: 'Pro' },
    { key: 2, value: 'Dev' },
    { key: 3, value: 'Test' }
];


export const controlTypes = [
    { value: 'input', label: <FormattedMessage id="form.control.type.input" /> },
    { value: 'inputNumber', label: <FormattedMessage id="form.control.type.inputNumber" /> },
    { value: 'textArea', label: <FormattedMessage id="form.control.type.textArea" /> },
    { value: 'select', label: <FormattedMessage id="form.control.type.select" /> },
    { value: 'checkbox', label: <FormattedMessage id="form.control.type.checkbox" /> },
    { value: 'radio', label: <FormattedMessage id="form.control.type.radio" /> },
    { value: 'switch', label: <FormattedMessage id="form.control.type.switch" /> },
    { value: 'datePicker', label: <FormattedMessage id="form.control.type.date" /> },
    { value: 'timePicker', label: <FormattedMessage id="form.control.type.dateTime" /> }
];


export const medicineStatusColors = ["grey", "geekblue", "orange", "cyan", "red", "purple", "yellow", "black", "grey", "grey", "grey", "grey", "grey", "grey", "magenta", "blue", "grey", "grey", "grey", "grey", "purple", "brown", "coral", "cyan"];

export const medicineStatus = [
    { value: 0, label: <FormattedMessage id="medicine.status.toBeWarehoused" /> },
    { value: 1, label: <FormattedMessage id="medicine.status.available" /> },
    { value: 2, label: <FormattedMessage id="medicine.status.delivered" /> },
    { value: 3, label: <FormattedMessage id="medicine.status.transit" /> },
    { value: 4, label: <FormattedMessage id="medicine.status.quarantine" /> },
    { value: 5, label: <FormattedMessage id="medicine.status.used" /> },
    { value: 6, label: <FormattedMessage id="medicine.status.lose" /> },
    { value: 7, label: <FormattedMessage id="medicine.status.expired" /> },
    { value: 8, label: <FormattedMessage id="medicine.status.receive" /> },
    { value: 9, label: <FormattedMessage id="medicine.status.return" /> },
    { value: 10, label: <FormattedMessage id="medicine.status.destroy" /> },
    { value: 11, label: <FormattedMessage id="medicine.status.InOrder" /> },
    //{value: 12, label: <FormattedMessage id="medicine.status.stockPending"/>},
    { value: 13, label: <FormattedMessage id="medicine.status.apply" /> },
    { value: 14, label: <FormattedMessage id="medicine.status.fzn" /> },
    // {value: 15, label: <FormattedMessage id="medicine.status.toBeApproved"/>},
    { value: 20, label: <FormattedMessage id="medicine.status.locked" /> },
    { value: 21, label: <FormattedMessage id="medicine.status.dsm" /> },
    { value: 22, label: <FormattedMessage id="medicine.status.dsh" /> },
    { value: 23, label: <FormattedMessage id="medicine.status.shsb" /> },
];

export const batchStatus = [
    { value: 1, label: <FormattedMessage id="medicine.status.available" /> },
    { value: 2, label: <FormattedMessage id="medicine.status.delivered" /> },
    { value: 3, label: <FormattedMessage id="medicine.status.transit" /> },
    { value: 4, label: <FormattedMessage id="medicine.status.quarantine" /> },
    { value: 6, label: <FormattedMessage id="medicine.status.lose" /> },
    { value: 7, label: <FormattedMessage id="medicine.status.expired" /> },
    { value: 11, label: <FormattedMessage id="medicine.status.InOrder" /> },
    { value: 14, label: <FormattedMessage id="medicine.status.fzn" /> },
    { value: 20, label: <FormattedMessage id="medicine.status.locked" /> },
];

export const shipmentStatus = [
    { value: 1, label: <FormattedMessage id="shipment.status.requested" /> },
    { value: 2, label: <FormattedMessage id="shipment.status.transit" /> },
    { value: 3, label: <FormattedMessage id="shipment.status.received" /> },
    { value: 4, label: <FormattedMessage id="shipment.status.lose" /> },
    { value: 5, label: <FormattedMessage id="shipment.status.cancelled" /> },
    { value: 6, label: <FormattedMessage id="shipment.status.toBeConfirm" /> },
    { value: 7, label: <FormattedMessage id="shipment.status.apply" /> },
    { value: 8, label: <FormattedMessage id="shipment.status.end" /> },
    { value: 9, label: <FormattedMessage id="projects.status.close" /> }
];

export const shipmentMode = [
    { value: 1, label: <FormattedMessage id="shipment.mode.set" /> },
    { value: 2, label: <FormattedMessage id="shipment.mode.reSupply" /> },
    { value: 3, label: <FormattedMessage id="shipment.mode.max" /> },
    { value: 5, label: <FormattedMessage id="shipment.mode.supplyRatio" /> }
];


export const subjectStatus = [
    { value: 1, label: <FormattedMessage id="subject.status.registered" />, color: "#165DFF", background: "rgba(22, 93, 255, 0.1)" },
    { value: 2, label: <FormattedMessage id="subject.status.filtered" />, color: "#9FDB1D", background: "rgba(159,219,29, 0.1)" },
    { value: 3, label: <FormattedMessage id="subject.status.random" />, color: "#41CC82", background: "rgba(65,204,130, 0.1)" },
    { value: 4, label: <FormattedMessage id="subject.status.exited" />, color: "#F53F3F", background: "rgba(245,63,63, 0.1)" },
    { value: 5, label: <FormattedMessage id="subject.status.exited" />, color: "#F53F3F", background: "rgba(245,63,63, 0.1)" },
    { value: 6, label: <FormattedMessage id="subject.status.blinded.urgent" />, color: "#722ED1", background: "rgba(114,46,209, 0.1)" },
    { value: 7, label: <FormattedMessage id="subject.status.screen.success" />, color: "#9FDB1D", background: "rgba(159,219,29, 0.1)" },
    { value: 8, label: <FormattedMessage id="subject.status.screen.fail" />, color: "#F7BA1E", background: "rgba(247,186,30, 0.1)" },
    { value: 9, label: <FormattedMessage id="subject.status.finish" />, color: "#23D3F9", background: "rgba(35,211,249, 0.1)" },
    { value: 10, label: <FormattedMessage id="subject.status.invalid" />, color: "#677283", background: "rgba(173, 178, 186, 0.2)" },
    { value: 11, label: <FormattedMessage id="subject.status.join" />, color: "#41CC82", background: "rgba(159,219,29, 0.1)" }
];

export const atRandomSubjectStatus = [
    { value: 1, label: <FormattedMessage id="subject.status.to.be.random" />, color: "#165DFF", background: "rgba(22, 93, 255, 0.1)" },
    { value: 2, label: <FormattedMessage id="subject.status.filtered" />, color: "#9FDB1D", background: "rgba(159,219,29, 0.1)" },
    { value: 3, label: <FormattedMessage id="subject.status.random" />, color: "#41CC82", background: "rgba(65,204,130, 0.1)" },
    { value: 4, label: <FormattedMessage id="subject.status.exited" />, color: "#F53F3F", background: "rgba(245,63,63, 0.1)" },
    { value: 5, label: <FormattedMessage id="subject.status.exited" />, color: "#F53F3F", background: "rgba(245,63,63, 0.1)" },
    { value: 6, label: <FormattedMessage id="subject.status.blinded.urgent" />, color: "#722ED1", background: "rgba(114,46,209, 0.1)" },
    { value: 7, label: <FormattedMessage id="subject.status.to.be.random" />, color: "#165DFF", background: "rgba(22,93,255, 0.1)" },
    { value: 8, label: <FormattedMessage id="subject.status.screen.fail" />, color: "#F7BA1E", background: "rgba(247,186,30, 0.1)" },
    { value: 9, label: <FormattedMessage id="subject.status.finish" />, color: "#23D3F9", background: "rgba(35,211,249, 0.1)" },
    { value: 10, label: <FormattedMessage id="subject.status.invalid" />, color: "#677283", background: "rgba(173, 178, 186, 0.2)" },
];

export const randomNumberStatus = [
    { value: 1, label: <FormattedMessage id="randomization.random.number.unused" /> },
    { value: 2, label: <FormattedMessage id="randomization.random.number.block.used" /> },
    { value: 3, label: <FormattedMessage id="randomization.random.number.invalid" /> },
    { value: 4, label: <FormattedMessage id="randomization.random.number.invalid" /> },
    { value: 5, label: <FormattedMessage id="randomization.random.number.not.available" /> }
];

export const projectStatus = [
    { value: 0, label: <FormattedMessage id="projects.status.progress" /> },
    { value: 1, label: <FormattedMessage id="projects.status.finish" /> },
    { value: 2, label: <FormattedMessage id="projects.status.close" /> },
    { value: 3, label: <FormattedMessage id="projects.status.pause" /> },
    { value: 4, label: <FormattedMessage id="projects.status.terminate" /> }
];

export const orderAddTaskStatus = [
    { value: 0, label: <FormattedMessage id="project.task.status.notStarted" /> },
    { value: 1, label: <FormattedMessage id="projects.status.finish" /> },
]

export const approvalProcessStatus = [
    { value: 0, label: <FormattedMessage id="project.task.approvalStatus.notStarted" /> },
    { value: 1, label: <FormattedMessage id="project.task.approvalStatus.pass" /> },
    { value: 2, label: <FormattedMessage id="project.task.approvalStatus.reject" /> },
]

export const rolesData = [
    { value: 0, label: 'Biostatistician [blinded]' },
    { value: 1, label: 'Biostatistician [unblinded]' },
    { value: 2, label: 'CRA [blinded]' },
    { value: 3, label: 'CRA [unblinded]' },
    { value: 4, label: 'CRC [blinded, can see room number temporarily]' },
    { value: 5, label: 'CRC [blinded]' },
    { value: 6, label: 'CRC [unblinded]' },
    { value: 7, label: 'DM' },
    { value: 8, label: 'IP Officer' },
    { value: 9, label: 'IRT Designer' },
    { value: 10, label: 'PI [blinded,can unblind]' },
    { value: 11, label: 'PI [blinded]' },
    { value: 12, label: 'PI [unblinded]' },
    { value: 13, label: 'Pharmacist [blinded]' },
    { value: 14, label: 'Project Manager [blinded]' },
    { value: 15, label: 'Project Manager [unblinded]' },
    { value: 16, label: 'Safety Monitor [blinded,can unblind]' },
    { value: 17, label: 'Safety Monitor [unblinded]' },
    { value: 18, label: 'Shipper Manager [blinded]' },
    { value: 19, label: 'Shipper Manager [unblinded]' },
    { value: 20, label: 'Sub-I [blinded]' },
    { value: 21, label: 'Sub-I [unblinded]' },
    { value: 22, label: 'Supply Manager [blinded]' },
    { value: 23, label: 'Supply Manager [unblinded]' },
    { value: 24, label: 'Customer-admin' },

    { value: 25, label: 'Sys-admin' },
    { value: 26, label: 'Project-admin' },
    { value: 27, label: 'Read-only[blinded]' },
    { value: 28, label: 'Read-only[unblinded]' },
    { value: 29, label: 'Sponsor' },
    { value: 30, label: 'Reportor' },
]

export const unblindedApprovalReason = [
    { value: 0, label: <FormattedMessage id="subject.urgentUnblindingApproval.reason.other" /> },
    { value: 1, label: <FormattedMessage id="subject.urgentUnblindingApproval.reason.sae" /> },
    { value: 2, label: <FormattedMessage id="subject.urgentUnblindingApproval.reason.pregnancy" /> },
    { value: 3, label: <FormattedMessage id="subject.urgentUnblindingApproval.reason.policy" /> },
];

export const unblindedApprovalStatus = [
    { value: 0, label: <FormattedMessage id="subject.urgentUnblindingApproval.pending" /> },
    { value: 1, label: <FormattedMessage id="subject.urgentUnblindingApproval.agree" /> },
    { value: 2, label: <FormattedMessage id="subject.urgentUnblindingApproval.reject" /> },
];

export const permissionsDisable = [
    {
        type: 5, disables: [
            "operation.settings.roles.view",
            "operation.settings.roles.config",
            "operation.settings.roles.edit",
            "operation.settings.roles.add",
            "operation.projects.main.setting.permission.export",
            "operation.settings.roles.export",
        ]
    },
    {
        type: 6, disables: [
            "operation.settings.roles.view",
            "operation.settings.roles.config",
            "operation.settings.roles.edit",
            "operation.settings.roles.add",
            "operation.settings.users.view",
            "operation.settings.users.add",
            "operation.settings.users.setting",
            "operation.settings.users.edit",
            "operation.projects.main.create",
            "operation.project.status.view",
            "operation.project.task.view",
            "operation.project.dynamics.view",
            "operation.settings.users.close",
            "operation.settings.users.invite-again",
            "operation.settings.users.cancel",
            "operation.settings.users.export",
            "operation.settings.roles.export",
        ]
    },
    {
        type: 4, disables: [
            "operation.settings.roles.view",
            "operation.settings.roles.config",
            "operation.settings.roles.edit",
            "operation.settings.roles.add",
            "operation.projects.main.view",
            "operation.projects.main.create",
            "operation.projects.main.setting.function.view",
            "operation.projects.main.setting.function.admin",
            "operation.projects.main.setting.permission.export",
            "operation.settings.roles.export",
        ]
    },
    {
        type: 1, disables: [
            "operation.projects.main.view",
            "operation.projects.main.create",
            "operation.settings.roles.view",
            "operation.settings.roles.config",
            "operation.settings.roles.edit",
            "operation.settings.roles.add",
            "operation.project.status.view",
            "operation.project.task.view",
            "operation.project.dynamics.view",
            "operation.projects.main.setting.permission.export",
            //多语言
            "operation.projects.project.multiLanguage.view",
            "operation.projects.project.multiLanguage.add",
            "operation.projects.project.multiLanguage.edit",
            "operation.projects.project.multiLanguage.delete",
            "operation.projects.project.multiLanguage.trail",
            "operation.projects.project.multiLanguage.details.view",
            "operation.projects.project.multiLanguage.details.edit",
            "operation.projects.project.multiLanguage.details.preview",
            "operation.projects.project.multiLanguage.details.downloadTemplate",
            "operation.projects.project.multiLanguage.details.batchExport",
        ]
    },
    {
        type: 2, disables: [
            "operation.projects.main.view",
            "operation.projects.main.create",
            "operation.settings.roles.view",
            "operation.settings.roles.config",
            "operation.settings.roles.edit",
            "operation.settings.roles.add",
            // "operation.subject.medicine.reissue",
            "operation.build.storehouse.notice",
            "operation.build.storehouse.alarm",
            "operation.build.attribute.edit",
            "operation.build.randomization.group.edit",
            "operation.build.randomization.group.delete",
            "operation.build.randomization.group.inactivating",
            "operation.build.randomization.list.generate",
            "operation.build.randomization.list.sync",
            "operation.build.randomization.list.history",
            "operation.build.randomization.list.attribute",
            "operation.build.randomization.list.segmentation.view",
            "operation.build.medicine.configuration.add",
            "operation.build.medicine.configuration.delete",
            "operation.build.medicine.configuration.edit",
            "operation.build.medicine.configuration.setting.add",
            "operation.build.medicine.configuration.setting.delete",
            "operation.build.medicine.configuration.setting.edit",
            "operation.build.medicine.upload.delete",
            "operation.build.medicine.batch.setting",
            "operation.build.medicine.otherm.add",
            "operation.build.medicine.otherm.edit",
            "operation.build.medicine.otherm.delete",
            "operation.build.supply-plan.medicine.add",
            "operation.build.supply-plan.medicine.edit",
            "operation.build.supply-plan.medicine.delete",
            // "operation.supply.shipment.create",
            // "operation.supply.recovery.add",
            // "operation.build.site.dispensing",
            "operation.build.simulate-random.view",
            "operation.build.simulate-random.edit",
            "operation.build.simulate-random.add",
            "operation.build.simulate-random.run",
            "operation.build.simulate-random.site",
            "operation.build.simulate-random.factor",
            "operation.build.simulate-random.download",
            "operation.build.simulate-random.pdf.download",
            "operation.build.randomization.list.export",
            "operation.build.medicine.upload.upload",
            "operation.build.medicine.package.setting",
            "operation.build.medicine.examine",
            "operation.build.medicine.update",
            "operation.build.medicine.release",
            "operation.build.medicine.barcode.add",
            "operation.project.site.IPStatistics.download",
            "operation.project.depot.IPStatistics.download",
            "operation.report.siteIPStatisticsExport.download",
            "operation.report.siteIPStatisticsExport.download.template",
            "operation.report.depotIPStatisticsExport.download",
            "operation.report.depotIPStatisticsExport.download.template",
            "operation.build.medicine.upload.downdata",
            "operation.project.status.view",
            "operation.project.task.view",
            "operation.project.dynamics.view",
            "operation.report.auditTrailExport.build",
            "operation.report.auditTrailExport.settings",
            "operation.report.auditTrailExport.release-record",
            "operation.report.auditTrailExport.order",
            "operation.report.auditTrailExport.drug_recovery",
            "operation.report.auditTrailExport.subject",
            "operation.report.auditTrailExport.dispensing",
            "operation.report.auditTrailExport.ip",
            "operation.report.auditTrailExport.userLoginHistory",
            "operation.report.auditTrailExport.userRoleAssignHistory",
            "operation.projects.main.setting.permission.export",
            "operation.build.medicine.visit.update",
            "operation.build.medicine.visit.drag",
            "operation.build.medicine.visit.copy",
            "operation.build.medicine.visit.push",
            "operation.build.medicine.visit.add",
            "operation.build.medicine.visit.delete",
            "operation.build.medicine.visit.edit",
            "operation.build.medicine.barcode.scan",
            "operation.build.medicine.barcode.scanPackage",
            //多语言
            "operation.projects.project.multiLanguage.view",
            "operation.projects.project.multiLanguage.add",
            "operation.projects.project.multiLanguage.edit",
            "operation.projects.project.multiLanguage.delete",
            "operation.projects.project.multiLanguage.trail",
            "operation.projects.project.multiLanguage.details.view",
            "operation.projects.project.multiLanguage.details.edit",
            "operation.projects.project.multiLanguage.details.preview",
            "operation.projects.project.multiLanguage.details.downloadTemplate",
            "operation.projects.project.multiLanguage.details.batchExport",
        ]
    },
    {
        type: 3, disables: [
            "operation.projects.main.view",
            "operation.projects.main.create",
            "operation.settings.roles.view",
            "operation.settings.roles.config",
            "operation.settings.roles.edit",
            "operation.settings.roles.add",
            // "operation.subject.medicine.reissue",
            "operation.build.storehouse.notice",
            "operation.build.storehouse.alarm",
            "operation.build.attribute.edit",
            "operation.build.randomization.group.edit",
            "operation.build.randomization.group.delete",
            "operation.build.randomization.group.inactivating",
            "operation.build.randomization.list.generate",
            "operation.build.randomization.list.sync",
            "operation.build.randomization.list.history",
            "operation.build.randomization.list.attribute",
            "operation.build.randomization.list.segmentation.view",
            "operation.build.medicine.configuration.add",
            "operation.build.medicine.configuration.delete",
            "operation.build.medicine.configuration.edit",
            "operation.build.medicine.configuration.setting.add",
            "operation.build.medicine.configuration.setting.delete",
            "operation.build.medicine.configuration.setting.edit",
            "operation.build.medicine.upload.delete",
            "operation.build.medicine.batch.setting",
            "operation.build.medicine.otherm.add",
            "operation.build.medicine.otherm.edit",
            "operation.build.medicine.otherm.delete",
            "operation.build.supply-plan.medicine.add",
            "operation.build.supply-plan.medicine.edit",
            "operation.build.supply-plan.medicine.delete",
            // "operation.supply.shipment.create",
            // "operation.supply.recovery.add",
            // "operation.build.site.dispensing",
            "operation.build.simulate-random.view",
            "operation.build.simulate-random.edit",
            "operation.build.simulate-random.add",
            "operation.build.simulate-random.run",
            "operation.build.simulate-random.site",
            "operation.build.simulate-random.factor",
            "operation.build.simulate-random.download",
            "operation.build.simulate-random.pdf.download",
            "operation.build.randomization.list.export",
            "operation.build.medicine.upload.upload",
            "operation.build.medicine.package.setting",
            "operation.build.medicine.examine",
            "operation.build.medicine.update",
            "operation.build.medicine.release",
            "operation.build.medicine.barcode.add",
            "operation.project.site.IPStatistics.download",
            "operation.project.depot.IPStatistics.download",
            "operation.report.siteIPStatisticsExport.download",
            "operation.report.siteIPStatisticsExport.download.template",
            "operation.report.depotIPStatisticsExport.download",
            "operation.report.depotIPStatisticsExport.download.template",
            "operation.build.medicine.upload.downdata",
            "operation.project.status.view",
            "operation.project.task.view",
            "operation.project.dynamics.view",
            "operation.report.auditTrailExport.build",
            "operation.report.auditTrailExport.settings",
            "operation.report.auditTrailExport.release-record",
            "operation.report.auditTrailExport.order",
            "operation.report.auditTrailExport.drug_recovery",
            "operation.report.auditTrailExport.subject",
            "operation.report.auditTrailExport.dispensing",
            "operation.report.auditTrailExport.ip",
            "operation.report.auditTrailExport.userLoginHistory",
            "operation.report.auditTrailExport.userRoleAssignHistory",
            "operation.projects.main.setting.permission.export",
            "operation.build.medicine.visit.update",
            "operation.build.medicine.visit.drag",
            "operation.build.medicine.visit.copy",
            "operation.build.medicine.visit.push",
            "operation.build.medicine.visit.add",
            "operation.build.medicine.visit.delete",
            "operation.build.medicine.visit.edit",
            "operation.build.medicine.barcode.scan",
            "operation.build.medicine.barcode.scanPackage",
            //多语言
            "operation.projects.project.multiLanguage.view",
            "operation.projects.project.multiLanguage.add",
            "operation.projects.project.multiLanguage.edit",
            "operation.projects.project.multiLanguage.delete",
            "operation.projects.project.multiLanguage.trail",
            "operation.projects.project.multiLanguage.details.view",
            "operation.projects.project.multiLanguage.details.edit",
            "operation.projects.project.multiLanguage.details.preview",
            "operation.projects.project.multiLanguage.details.downloadTemplate",
            "operation.projects.project.multiLanguage.details.batchExport",
        ]
    }
];


export const permissionsCohortDisable = [
    // 发药按钮
    { key: "operation.subject.medicine.dispensing", value: [1, 3, 4] },
    { key: "operation.subject.medicine.reissue", value: [1, 3, 4] },
    { key: "operation.subject.medicine.replace", value: [1, 3, 4] },
    { key: "operation.subject.medicine.resume", value: [1, 3, 4] },
    { key: "operation.subject.medicine.retrieval", value: [1, 3, 4] },
    { key: "operation.subject.medicine.out-visit-dispensing", value: [1, 3, 4] },
    { key: "operation.subject.medicine.invalid", value: [1, 3, 4] },
    { key: "operation.subject.medicine.register", value: [1, 3, 4] },
    { key: "operation.subject.medicine.formula.update", value: [1, 3, 4] },
    { key: "operation.subject.medicine.formula.joinTime", value: [1, 3, 4] },
    { key: "operation.subject.medicine.formula.setUp", value: [1, 3, 4] },

    // 受试者
    { key: "operation.subject.random", value: [1, 3, 4, 5] },
    { key: "operation.subject.replace", value: [1, 3, 4] },
    { key: "operation.subject.registered", value: [1, 3, 4] },
    { key: "operation.subject.update", value: [1, 3, 4] },
    { key: "operation.subject.delete", value: [1, 3, 4] },
    { key: "operation.subject.secede", value: [1, 3, 4] },
    { key: "operation.subject.secede-registered", value: [1, 3, 4] },
    { key: "operation.subject.cohort.status", value: [1, 3, 4] },
    { key: "operation.subject.cohort.status", value: [3, 4] },
    { key: "operation.subject.medicine.transport", value: [1, 3, 4] },


    { key: "operation.subject-dtp.unblinding-application", value: [4] },
    { key: "operation.subject-dtp.unblinding-approval", value: [4] },
    { key: "operation.subject-dtp.unblinding-sms", value: [4] },

    { key: "operation.subject-dtp.random", value: [1, 3, 4] },
    { key: "operation.subject-dtp.replace", value: [1, 3, 4] },
    { key: "operation.subject-dtp.unblinding-pv", value: [1, 4] },
    { key: "operation.subject-dtp.medicine.dispensing", value: [1, 3, 4] },
    { key: "operation.subject-dtp.medicine.transport", value: [1, 3, 4] },
    { key: "operation.subject-dtp.medicine.reissue", value: [1, 3, 4] },
    { key: "operation.subject-dtp.medicine.replace", value: [1, 3, 4] },
    { key: "operation.subject-dtp.registered", value: [1, 3, 4] },
    { key: "operation.subject-dtp.update", value: [1, 3, 4] },
    { key: "operation.subject-dtp.delete", value: [1, 3, 4] },
    { key: "operation.subject-dtp.medicine.out-visit-dispensing", value: [1, 3, 4] },
    { key: "operation.subject-dtp.medicine.invalid", value: [1, 3, 4] },
    { key: "operation.subject-dtp.medicine.register", value: [1, 3, 4] },
    { key: "operation.subject-dtp.secede", value: [1, 3, 4] },
    { key: "operation.subject-dtp.secede-registered", value: [1, 3, 4] },


];

export const boolStatus = [
    { value: "true", label: <FormattedMessage id="common.yes" /> },
    { value: "false", label: <FormattedMessage id="common.no" /> }
];

export const logistics = [
    { value: 1, label: <FormattedMessage id="order.logistics.sf" /> },
    { value: 2, label: <FormattedMessage id="order.logistics.ems" /> },
    { value: 3, label: <FormattedMessage id="order.logistics.jd" /> },
    { value: 4, label: <FormattedMessage id="order.logistics.yt" /> },
    { value: 5, label: <FormattedMessage id="order.logistics.yd" /> },
    { value: 6, label: <FormattedMessage id="order.logistics.zt" /> },
    { value: 7, label: <FormattedMessage id="order.logistics.st" /> },
    { value: 8, label: <FormattedMessage id="order.logistics.jt" /> },
    { value: 9, label: <FormattedMessage id="order.logistics.other" /> },
];

export const eamilBodyContentEnum = [
    { value: "projectName", label: <FormattedMessage id="projects.name" /> },
    { value: "projectNumber", label: <FormattedMessage id="projects.number" /> },
    //{value: "envName", label: <FormattedMessage id="projects.envs"/>},
    { value: "siteName", label: <FormattedMessage id="projects.site.name" /> },
    { value: "siteNumber", label: <FormattedMessage id="projects.site.number" /> }
];

export const mailOrderContentEnum = [
    { value: "projectName", label: <FormattedMessage id="projects.name" /> },
    { value: "projectNumber", label: <FormattedMessage id="projects.number" /> },
    { value: "expectedArrivalTime", label: <FormattedMessage id="shipment.expectedArrivalTime" /> }
]


export const randomContentEnum = [
    { value: "projectName", label: <FormattedMessage id="projects.name" /> },
    { value: "projectNumber", label: <FormattedMessage id="projects.number" /> },
    //{value: "envName", label: <FormattedMessage id="projects.envs"/>},
    { value: "siteName", label: <FormattedMessage id="projects.site.name" /> },
    { value: "siteNumber", label: <FormattedMessage id="projects.site.number" /> },
    {
        value: "group", label: <Row><FormattedMessage id="notice.content.group" />
            <Tooltip
                trigger={["hover", "click"]}
                overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                placement="top"
                title={<FormattedMessage id="notice.config.content.group" />}>
                <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4, marginTop: 6 }}>
                    <use xlinkHref="#icon-xinxitishi" />
                </svg>
            </Tooltip></Row>
    },
]
export const eamilProjectBodyContentEnum = [
    { value: "projectName", label: <FormattedMessage id="projects.name" /> },
    { value: "projectNumber", label: <FormattedMessage id="projects.number" /> },
];

export const emailBodyStateEnum = [
    { value: "notice.subject.add", label: <FormattedMessage id="notice.subject.add" /> },
    { value: "notice.subject.random", label: <FormattedMessage id="notice.subject.random" /> },
    { value: "notice.subject.signOut", label: <FormattedMessage id="notice.subject.signOut" /> },
    { value: "notice.subject.replace", label: <FormattedMessage id="notice.subject.replace" /> },
    { value: "notice.subject.update", label: <FormattedMessage id="notice.subject.update" /> },
    { value: "notice.subject.dispensing", label: <FormattedMessage id="notice.subject.dispensing" /> },
    { value: "notice.subject.alarm", label: <FormattedMessage id="notice.subject.alarm" /> },
    { value: "notice.subject.unblinding", label: <FormattedMessage id="notice.subject.unblinding" /> },
    { value: "notice.medicine.isolation", label: <FormattedMessage id="notice.medicine.isolation" /> },
    { value: "notice.medicine.order", label: <FormattedMessage id="notice.medicine.order" /> },
    { value: "notice.medicine.reminder", label: <FormattedMessage id="notice.medicine.reminder" /> },
    { value: "notice.medicine.alarm", label: <FormattedMessage id="notice.medicine.alarm" /> },
    { value: "notice.storehouse.alarm", label: <FormattedMessage id="notice.storehouse.alarm" /> },
    { value: "notice.order.timeout", label: <FormattedMessage id="notice.order.timeout" /> },
];

export const dispensingContentEnum = [
    { value: "projectName", label: <FormattedMessage id="projects.name" /> },
    { value: "projectNumber", label: <FormattedMessage id="projects.number" /> },
    { value: "siteName", label: <FormattedMessage id="projects.site.name" /> },
    { value: "siteNumber", label: <FormattedMessage id="projects.site.number" /> },
    {
        value: "group", label: <Row><FormattedMessage id="notice.content.group" />
            <Tooltip
                trigger={["hover", "click"]}
                overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                placement="top"
                title={<FormattedMessage id="notice.config.content.group" />}>
                <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4, marginTop: 6 }} >
                    <use xlinkHref="#icon-xinxitishi" />
                </svg>
            </Tooltip></Row>
    },
    { value: "random_number", label: <FormattedMessage id="notice.content.number" /> },
];

export const dispensingStateEnum = [
    { value: "dispensing.plan-title", label: <FormattedMessage id="notice.state.dispensing" /> },
    { value: "dispensing.unscheduled-plan-title", label: <FormattedMessage id="notice.state.unscheduled" /> },
    { value: "dispensing.reissue-title", label: <FormattedMessage id="notice.state.re.dispensing" /> },
    { value: "dispensing.replace-title", label: <FormattedMessage id="notice.state.replace" /> },
    { value: "dispensing.retrieval-title", label: <FormattedMessage id="notice.state.retrieval" /> },
    { value: "dispensing.register-title", label: <FormattedMessage id="notice.state.register" /> },
    { value: "dispensing.not-attend-title", label: <FormattedMessage id="notice.state.not-attend" /> },
];

export const subjectUpdateStateEnum = [
    { value: "subject.update.form_factor", label: <FormattedMessage id="notice.state.form_factor" /> },
    { value: "subject.update.screen", label: <FormattedMessage id="notice.state.screen" /> },
    { value: "subject.update.stop", label: <FormattedMessage id="notice.state.stop" /> },
    { value: "subject.update.finish", label: <FormattedMessage id="notice.state.finish" /> },
    { value: "subject.update.shortname", label: <FormattedMessage id="subject.number" /> },
];

export const subjectScreenStateEnum = [
    { value: "subject.screen.success", label: <FormattedMessage id="subject.status.screen.success" /> },
    { value: "subject.screen.fail", label: <FormattedMessage id="subject.status.screen.fail" /> },
];
export const orderSceneEnum = [
    {
        value: "order.approval.add-title", label: <Row><FormattedMessage id="notice.order.scene.apply" />
            <Tooltip
                trigger={["hover", "click"]}
                overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                placement="top"
                title={<FormattedMessage id="notice.order.tip" />}>
                <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4, marginTop: 6 }} >
                    <use xlinkHref="#icon-xinxitishi" />
                </svg>
            </Tooltip></Row>
    },
    {
        value: "order.approval.failed-title", label: <Row><FormattedMessage id="notice.order.scene.applyFail" />
            <Tooltip
                trigger={["hover", "click"]}
                overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                placement="top"
                title={<FormattedMessage id="notice.order.tip" />}>
                <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4, marginTop: 6 }} >
                    <use xlinkHref="#icon-xinxitishi" />
                </svg>
            </Tooltip></Row>
    },
    { value: "order.medicine_order_title", label: <FormattedMessage id="notice.order.scene.create" /> },
    { value: "order.cancel_title", label: <FormattedMessage id="notice.order.scene.cancel" /> },
    { value: "order.no_automatic_success_title", label: <FormattedMessage id="notice.order.scene.confirm" /> },
    { value: "order.close_title", label: <FormattedMessage id="notice.order.scene.close" /> },
    { value: "order.send_title", label: <FormattedMessage id="notice.order.scene.transport" /> },
    { value: "order.receive_title", label: <FormattedMessage id="notice.order.scene.receive" /> },
    { value: "order.end_title", label: <FormattedMessage id="notice.order.scene.stop" /> },
    { value: "order.lost_title", label: <FormattedMessage id="notice.order.scene.lost" /> },
    {
        value: "order.automatic_success_title", label: <Row><FormattedMessage id="notice.order.scene.autoCreate" />
            <Tooltip
                trigger={["hover", "click"]}
                overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                placement="top"
                title={<FormattedMessage id="notice.order.tip" />}>
                <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4, marginTop: 6 }} >
                    <use xlinkHref="#icon-xinxitishi" />
                </svg>
            </Tooltip></Row>
    },
    {
        value: "order.automatic_error_title", label: <Row><FormattedMessage id="notice.order.scene.autoCreateFail" />
            <Tooltip
                trigger={["hover", "click"]}
                overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                placement="top"
                title={<FormattedMessage id="notice.order.tip" />}>
                <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4, marginTop: 6 }} >
                    <use xlinkHref="#icon-xinxitishi" />
                </svg>
            </Tooltip></Row>
    },
    { value: "order.automatic_alarm_title", label: <FormattedMessage id="notice.order.scene.autoAlarm" /> },
    { value: "order.change_title", label: <FormattedMessage id="notice.order.scene.change" /> },
    { value: "order.batch_expiration_title_dtp", label: <FormattedMessage id="notice.order.scene.batch" /> },
]

export const medicineAlarmEnum = [
    { value: "order.no_automatic_title", label: <FormattedMessage id="notice.alarm.scene.stock" /> },
    { value: "order.forecast_title", label: <FormattedMessage id="notice.alarm.scene.forecast" /> },
]

export const orderIsolationEnum = [
    { value: "medicine.freeze.title", label: <FormattedMessage id="notice.order.scene.isolation" /> },
    { value: "medicine.freeze.release", label: <FormattedMessage id="notice.order.scene.release" /> },
]
export const pushStatusColors = ["#FFDD55", "#41CC82", "#FF6666", "#BBBBBB", "#0000CD"];
export const pushStatus = [
    { value: 0, label: <FormattedMessage id="project.statistics.pushing" /> },
    { value: 1, label: <FormattedMessage id="project.statistics.succeeded" /> },
    { value: 2, label: <FormattedMessage id="project.edc.failed" /> },
    { value: 3, label: <FormattedMessage id="project.statistics.lose" /> },
    { value: 4, label: <FormattedMessage id="project.edc.processing" /> },
    { value: 5, label: "" }
];


export const pushLogStatusColors = ["#FFDD55", "#41CC82", "#FF6666", "#BBBBBB", "#0000CD", "#FF6600", "#BBBBBB"];
export const pushLogStatus = [
    { value: 0, label: <FormattedMessage id="project.statistics.pushing" /> },
    { value: 1, label: <FormattedMessage id="project.statistics.succeeded" /> },
    { value: 2, label: <FormattedMessage id="project.statistics.failed" /> },
    { value: 3, label: <FormattedMessage id="project.statistics.lose" /> },
    { value: 4, label: <FormattedMessage id="project.edc.processing" /> },
    { value: 5, label: <FormattedMessage id="project.received.failed" /> },
    { value: 6, label: <FormattedMessage id="project.statistics.lose" /> },     // 如果EDC不处理也用无效这个状态
];


export const pushSourceColors = ["", "geekblue", "orange"];
export const pushSource = [
    { value: 0, label: <FormattedMessage id="project.statistics.unknown" /> },
    { value: 1, label: <FormattedMessage id="subject.register" /> },
    { value: 2, label: <FormattedMessage id="project.statistics.update" /> },
    { value: 3, label: <FormattedMessage id="project.statistics.randomize" /> },
    { value: 4, label: <FormattedMessage id="project.statistics.dispense" /> },
    { value: 5, label: <FormattedMessage id="project.statistics.out-visit-dispensing" /> },
    { value: 6, label: <FormattedMessage id="project.statistics.replace" /> },
    { value: 7, label: <FormattedMessage id="project.statistics.reissue" /> },
    { value: 8, label: <FormattedMessage id="project.statistics.cancel" /> },
    { value: 9, label: <FormattedMessage id="project.statistics.retrieval" /> },
    { value: 10, label: <FormattedMessage id="project.statistics.realDispensing" /> },
    { value: 11, label: <FormattedMessage id="subject.register.replace" /> },
    { value: 12, label: <FormattedMessage id="report.attributes.random.actual.factor" /> },
    { value: 13, label: <FormattedMessage id="project.statistics.screen" /> },
    { value: 14, label: <FormattedMessage id="subject.switch.cohort" /> },
];

export const pushMode = [
    { value: 1, label: <FormattedMessage id="project.statistics.system.auto" /> },
    { value: 2, label: <FormattedMessage id="project.statistics.manual.push" /> },
];

export const DateType = [
    { value: "w", label: <FormattedMessage id="project.overview.week" /> },
    { value: "d", label: <FormattedMessage id="project.overview.day" /> },
    { value: "h", label: <FormattedMessage id="project.overview.hour" /> },
    { value: "m", label: <FormattedMessage id="project.overview.month" /> },
];

export const comparisonType = [
    { value: 1, label: <FormattedMessage id="project.overview.week" /> },
    { value: 2, label: <FormattedMessage id="project.overview.day" /> },
    { value: 3, label: <FormattedMessage id="project.overview.hour" /> },
];

export const FormulaData = [
    { symbol: "+", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.plus" />, eg: "1+2=3" },
    { symbol: "-", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.sub" />, eg: "8-3.5=4.5" },
    { symbol: "*", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.multiply" />, eg: "2*3=6" },
    { symbol: "/", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.division" />, eg: "5/2=2.5" },
    { symbol: "%", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.remainder" />, eg: "5%2=1" },
    { symbol: "^", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.integer.power" />, eg: "2^3=8" },
    { symbol: "()", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.brackets" />, eg: "(2+3)*4=20" },
    { symbol: "sqrt", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.square.root" />, eg: "sqrt(4)=2" },
    { symbol: "cbrt", illustrate: <FormattedMessage id="drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.cube.root" />, eg: "cbrt(27)=3" },
];

export const FormulaWeightType = [
    { value: 1, label: <FormattedMessage id="drug.configure.formula.weight.last" /> },
    { value: 2, label: <FormattedMessage id="drug.configure.formula.weight.actual" /> },
    { value: 3, label: <FormattedMessage id="drug.configure.formula.weight.random" /> },
];

export const ComparisonSymbolsType = [
    { value: 0, label: ">" },
    { value: 1, label: "<" },
    { value: 2, label: "<=" },
    { value: 3, label: ">=" },
];

export const AlertThresholdType = [
    {
        id: 1,
        name: <FormattedMessage id="subject.status.registered" />,
    },
    {
        id: 7,
        name: <FormattedMessage id="subject.status.screen.success" />,
    },
    {
        id: 3,
        name: <FormattedMessage id="projects.envs.cohorts.status2" />,
    },
];

export const DTPOption = [
    { value: 0, label: <FormattedMessage id="logistics.send.site" />, desc: <FormattedMessage id={'logistics.send.site.info'} /> },
    { value: 1, label: <FormattedMessage id="logistics.send.site.subject" />, desc: <FormattedMessage id={'logistics.send.site.subject.info'} /> },
    { value: 2, label: <FormattedMessage id="logistics.send.depot.subject" />, desc: <FormattedMessage id={'logistics.send.depot.subject.info'} /> },
];

export const DTPOptionIntl = (formatMessage) => [
    { value: 0, label: <FormattedMessage id="logistics.send.site" />, desc: formatMessage({id: 'logistics.send.site.info'})},
    { value: 1, label: <FormattedMessage id="logistics.send.site.subject" />, desc: formatMessage({id: 'logistics.send.site.subject.info'}) },
    { value: 2, label: <FormattedMessage id="logistics.send.depot.subject" />, desc: formatMessage({id: 'logistics.send.depot.subject.info'}) },
]


export const WeekDay = [
    {value: 2, label: <FormattedMessage id="week.monday" />},
    {value: 3, label: <FormattedMessage id="week.tuesday" />},
    {value: 4, label: <FormattedMessage id="week.wednesday" />},
    {value: 5, label: <FormattedMessage id="week.thursday" />},
    {value: 6, label: <FormattedMessage id="week.friday" />},
    {value: 7, label: <FormattedMessage id="week.saturday" />},
    {value: 1, label: <FormattedMessage id="week.sunday" />},
]

export const WeekDayIntl = [
    {value: 2, label: "week.monday"},
    {value: 3, label: "week.tuesday"},
    {value: 4, label: "week.wednesday"},
    {value: 5, label: "week.thursday"},
    {value: 6, label: "week.friday"},
    {value: 7, label: "week.saturday"},
    {value: 1, label: "week.sunday"},
]

export const SupplyPlanAuto = [
    {value: 1, label: <FormattedMessage id="projects.supplyPlan.drugBlind" />},
    {value: 2, label: <FormattedMessage id="projects.supplyPlan.drugOpen" />},
]

export const SupplyPlanSiteWarning = [
    {value: 1, label: <FormattedMessage id="projects.supplyPlan.drugBlind" />},
    {value: 2, label: <FormattedMessage id="projects.supplyPlan.drugOpen" />},
    {value: 3, label: <FormattedMessage id="projects.supplyPlan.drugBlindAuto" />},
]