import { useSafeState } from "ahooks";
import { Select, SelectProps } from "antd";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { useIntl } from "react-intl";

export const SelectUI: React.FC<SelectProps> = (props) => {

    const [visible, setVisible] = useSafeState(false);
    const [dropdown, setDropdown] = useSafeState(false);

    const intl = useIntl();

    return (
        <Select
            {...{ placeholder: intl.formatMessage({ id: "placeholder.select" }), ...props }}
            open={visible}
            onClick={() => { if (!props.mode || !visible) setVisible(!visible); }}
            onBlur={() => setVisible(false)}
            suffixIcon={dropdown ? <UpOutlined /> : <DownOutlined />}
            onDropdownVisibleChange={(open) => setDropdown(open)}
        />
    )
};
