import moment from "moment-timezone";
import { Timezone } from "../types/timezone";
import { timezones } from "../data/timezone";

// 获取所有可用时区
export const getTimezones = () => {
    return moment.tz.names();
};

// 获取所有可用时区及它们各自的偏移量
export const getTimezonesWithOffsets = () => {
    const timezonesWithOffsets: Timezone[] = [];
    // 遍历所有时区
    for (const timezone of moment.tz.names()) {
        const now = moment(); // 创建一个与当前时间关联的moment对象
        const tzMoment = moment.tz(now, timezone); // 将当前时间转换到指定时区
        const offset = tzMoment.utcOffset();
        const offsetStr = formatTimezoneOffset(offset);
        const str = `${timezone} - ${offsetStr}`;
        timezonesWithOffsets.push({ name: timezone, offset, str });
    }
    return timezonesWithOffsets;
};

function formatTimezoneOffset(offsetInMinutes: number): string {
    const sign = offsetInMinutes < 0 ? "-" : "+";
    const absOffset = Math.abs(offsetInMinutes);
    const hours = Math.floor(absOffset / 60)
        .toString()
        .padStart(2, "0");
    const minutes = (absOffset % 60).toString().padStart(2, "0");
    return `UTC${sign}${hours}:${minutes}`;
}

export const formatTimezone = (timezone: string) => {
    const now = moment();
    const tzMoment = moment.tz(now, timezone);
    const offset = tzMoment.utcOffset();
    const offsetStr = formatTimezoneOffset(offset);
    return `${timezone} - ${offsetStr}`;
};

export const getTimezoneOffset = (timezone: string) => {
    const now = moment();
    const tzMoment = moment.tz(now, timezone);
    const offset = tzMoment.utcOffset();
    const offsetStr = formatTimezoneOffset(offset);
    return offsetStr;
};

export const getTimezoneString = (timezone: string, lang: string) => {
    const now = moment();
    const tzMoment = moment.tz(now, timezone);
    const offset = tzMoment.utcOffset();
    const offsetStr = formatTimezoneOffset(offset);
    const tz = timezones.find((it) => it.location === timezone);
    return `(${offsetStr}) ${tz ? tz[lang === "zh" ? "zh" : "en"] : timezone}`;
};
