import qs from "qs";
import {getMockData, isMockMode} from "./mock-data";

interface Config extends RequestInit {
    params?: object,
    data?: object
}

export const http = async (path: string, { params, data, headers, ...rest }: Config = {}) => {
    if (isMockMode(path, rest.method)) return getMockData(path, rest.method)

    const token = sessionStorage.getItem("token") || "";
    const acceptLanguage = sessionStorage.getItem('lang') || 'en'
    const languageId = sessionStorage.getItem('languageId') || ''
    let roleId = ""
    if (sessionStorage.getItem("current_project")) {
        roleId = JSON.parse(sessionStorage.getItem("current_project") as string)?.permissions?.role_id
    }
    const defaultHeaders = {'roleId':roleId, 'token': token, 'Accept-Language': acceptLanguage, 'languageId': languageId}
    let _headers: any = {...defaultHeaders, 'Content-Type': 'application/json', ...headers };
    if (!data || data instanceof FormData) {
        _headers = {...defaultHeaders, ...headers};
    }
    const config = {
        ...rest,
        method: rest.method?.toUpperCase() || "GET",
        headers: _headers
    };

    if (params) {
        path += `?${qs.stringify(params)}`;
    }

    if (data) {
        if (data instanceof FormData) {
            config.body = data;
        } else {
            config.body = JSON.stringify(data || {})
        }
    }

    return window.fetch(`/api/${path.indexOf("/") === 0 ? path.substring(1) : path}`, config).then(
        async response => {
            if (response.ok) {
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.indexOf("application/json") > -1) {
                    const data = await response.json();
                    return data;
                } else {
                    const disposition = response.headers.get("content-disposition");
                    if (disposition != null && disposition.indexOf("attachment") > -1) {
                        const result = await response.blob();
                        let url = window.URL.createObjectURL(result);
                        let a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = decodeURI(disposition.split("filename=")[1]).replace(new RegExp('"', 'g'), '');
                        a.target = "_blank";
                        document.body.appendChild(a);
                        a.click();
                        a.remove();
                        return Promise.resolve();
                    }
                }
            } else {
                return Promise.reject(response);
            }
        }
    );
};

export const get = ((...[path, config]: Parameters<typeof http>) => http(path, { ...config, method: "GET" }));
export const post = ((...[path, config]: Parameters<typeof http>) => http(path, { ...config, method: "POST" }));
export const patch = ((...[path, config]: Parameters<typeof http>) => http(path, { ...config, method: "PATCH" }));
export const put = ((...[path, config]: Parameters<typeof http>) => http(path, { ...config, method: "PUT" }));
export const del = ((...[path, config]: Parameters<typeof http>) => http(path, { ...config, method: "DELETE" }));
