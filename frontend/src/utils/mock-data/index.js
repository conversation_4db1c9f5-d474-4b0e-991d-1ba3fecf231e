import {projectMockData} from "./project"
import {commonMockData} from "./common";
import {findMockData} from "./util";

const mockData = [...projectMockData, ...commonMockData]

export const isMockMode = (path, method) => {
    const previewMode = !!sessionStorage.getItem('project_preview')
    return previewMode && !!findMockData(mockData, path, method)
}

export const getMockData = (path, method) => {
    return new Promise((resolve) => {
        resolve(findMockData(mockData, path, method).data)
    })
}