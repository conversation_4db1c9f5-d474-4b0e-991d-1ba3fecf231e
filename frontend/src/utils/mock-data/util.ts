import {commonMockData} from "./common";
import _ from "lodash";

const getMockUrl = (path: string, method: string) => {
    let _path = path.indexOf('/') !== 0 ? `/${path}` : path
    if (_path.indexOf('?') !== -1) {
        _path = _path.substring(0, _path.indexOf('?'))
    }
    const _method = method.toLowerCase()
    return _method + _path
}

export const findMockData = (mockData: any[], path: string, method: string) => {
    return mockData.find(it => getMockUrl(it.path, it.method) === getMockUrl(path, method))
}


declare global {
    var $mockData: any[]
}
globalThis.$mockData = []
const addApi = (path: string, method: string, data: any) => {
    if (!findMockData(commonMockData, path, method)) {
        globalThis.$mockData.push({
            method: method,
            path: path,
            data: data
        })
    }
}
const printApi = () => {
    console.log(_.uniqWith(globalThis.$mockData, (one, two) => {
        return getMockUrl(one.method, one.path) === getMockUrl(two.method, two.path)
    }))
}

const initApi = () => {
    globalThis.$mockData = []
}