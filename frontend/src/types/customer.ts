export enum CustomerModeEnum { List, View }

export interface CustomerMode {
    last?: CustomerModeEnum;
    current: CustomerModeEnum;
}

export interface Customer {
    id: string;
    name: string;
    contact?: string;
    mobile?: string;
    description?: string;
    apps: CustomerApp[];
    createdAt?: number;
    createdBy?: string;
}

export interface CustomerApp {
    tag: string;
    admins: string[];
    expire: string;
}

export interface SearchCustomer {
    keyword?: string;
    contact?: string;
    app?: string;
}

export interface SelectCustomer {
    id: string;
    name: string;
    types: string[];
}
