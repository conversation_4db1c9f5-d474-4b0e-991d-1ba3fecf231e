export const message = {
    "error.unknown": "Unknown Error",
    "error.invalid-request": "Invalid Request",
    "error.not-logged-in": "Not logged in",
    "error.unauthorized": "Unauthorized",
    "error.resource-not-found": "Resource Not Found",
    "common.options": "옵션",
    "common.all-options": "모든 옵션",
    "common.collapse": "접습니다",
    "common.expand": "확장합니다",
    "common.pagination.tip.total": "총 {total}개 항목입니다, ",
    "common.pagination.tip.pagesize-prefix": "페이지당 ",
    "common.pagination.tip.pagesize-suffix": "개씩",
    "common.all": "모두",
    "common.selectUser": "고객 선택",
    "common.add": "신규",
    "common.copy": "복사",
    "common.delete": "삭제",
    "common.recover": "리커버리",
    "common.save": "저장",
    "common.edit": "편집",
    "common.edit.batch": "Batch Edit",
    "common.submit": "제출",
    "common.search": "검색",
    "common.reset": "초기화",
    "common.select": "선택",
    "common.ok": "확실",
    "common.cancel": "취소",
    "common.next": "다음",
    "common.open": "열기",
    "common.close": "닫기",
    "common.operation": "조작",
    "common.enable": "실행",
    "common.active": "활성화",
    "common.more": "더 많은",
    "common.status": "상태",
    "common.yes": "예",
    "common.no": "아니오",
    "common.update": "업데이트",
    "common.page": "페이지",
    "common.type": "분류",
    "common.setting": "설정",
    "common.setting.batch": "Batch Setting",
    "common.print": "인쇄",
    "common.created.at": "만든 시각",
    "common.created.by": "만든이",
    "common.updated.at": "수정 시각",
    "common.updated.by": "수정인",
    "common.operator": "조작자",
    "common.operation.time": "조작 시각",
    "common.operation.content": "조작 내용",
    "common.operation.type": "조작 방법",
    "common.history": "방향",
    "common.register": "회원가입",
    "common.login": "로그인",
    "common.logout": "회원탈퇴",
    "common.username": "아이디",
    "common.password": "비밀번호",
    "common.remember": "비밀번호를 기억하세요",
    "common.forget": "비밀번호를 잊어버리셨습니까?",
    "common.user.info": "나의 프로필 정보",
    "common.tips": "알림",
    "common.task.automatic": "Automated Tasks",
    "common.task.manual": "Manual Tasks",
    "common.email.language": "Email Language",
    "common.email.language.zh": "Chinese",
    "common.email.language.en": "English",
    "common.email.language.zh_en": "Chinese And English",
    "common.email": "이메일",
    "common.phone": "핸드폰 번호",
    "common.number": "일련번호",
    "common.name": "명칭",
    "common.name.new": "새 이름",
    "common.full_name": "이름",
    "common.company": "회사",
    "common.contacts": "연락처",
    "common.address": "주소",
    "common.description": "설명",
    "common.confirm.delete": "정말로 삭제 하시겠습니까?",
    "common.administrator": "매니저",
    "common.administrator.project": "프로젝트 매니저",
    "common.select.role": "캐릭터를 선택하세요",
    "common.role": "캐릭터",
    "common.role.assigned":
        "The system detects that users have been added to the current customer group, but roles and permissions have not been assigned. Please contact the administrator for settings",
    "common.upload.fileSize": "업로드 할 파일이 50MB를 넘지 말아야 합니다!",
    "common.upload": "업로드",
    "common.download": "다운로드",
    "common.download.data": "데이터 다운로드",
    "common.download.template": "양식 다운로드",
    "common.generate": "생성",
    "common.site": "중심",
    "common.depot": "창고",
    "common.export": "도출",
    "common.between": "구간",
    "common.download.fail": "다운로드 실패",
    "common.not-logged-in": "로그인하지 않았거나 로그인 제한 시간",
    "common.classification": "분류하다",
    "common.effective": "유효했어",
    "common.invalid": "무효이었어",
    "common.menu": "메뉴/모듈",
    "common.role.setting": "권한 설정",
    "common.serial": "번호",
    "common.common": "통용",
    "common.template": "거푸집",
    "common.addTo": "덧붙이다",
    "common.success": "작업 성공",
    "common.confirm": "확인",
    "common.previous": "이전의",
    "common.time": "Time",
    "common.open.switch": "Open",
    "common.close.switch": "Close",
    "common.have": "Have",
    "common.nothing": "Nothing",
    "common.serialNumber": "Serial Number",
    "common.time.start": "开始时间",
    "common.time.end": "结束时间",
    "common.pagination.seleted": "Selected",
    "common.pagination.record": " ",
    "common.pagination.all": "Total",
    "common.pagination.empty": "Empty",
    "common.lock": "Lock",
    "common.unlock": "Unlock",
    "common.required.prefix": "Please enter ",
    "common.confirm-change": "Confirm Change",
    "common.delete.confirm": "Confirm delete？",
    "common.delete.ok": "Delete succeeded",
    "common.copy.ok": "Copy succeeded",
    "common.create": "Create",
    "common.please.confirm": "Please confirm.",

    "role.setting.operation": "작업 선택",
    "role.operation.template": "권한 템플릿",
    "role.name.enter": "역할 이름을 입력하십시오.",
    "role.name": "역할 이름",

    "project.task": "Task",
    "project.task.status": "Task Status",
    "project.task.status.all": "All",
    "project.task.status.notStarted": "Not Started",
    "project.task.status.completed": "Completed",
    "project.task.approvalStatus.notStarted": "Submit application",
    "project.task.approvalStatus.passed": "Approved",
    "project.task.approvalStatus.rejected": "Denied",
    "project.task.approvalStatus.pass": "Approved",
    "project.task.approvalStatus.reject": "Deny",
    "project.task.estimatedCompletionTime": "Estimated completion",
    "project.task.approvalTime": "Actual completion time",
    "project.task.addOrder.title": "Site Shipment Application",
    "project.task.approval.deadline": "Deadline",

    "project.noPermission": "프로젝트 내 권한이 할당되지 않았습니다",
    "projects.all": "모든 프로젝트",
    "projects.all.admin": "All administrators",
    "projects.add": "새로운 프로젝트",
    "projects.edit": "편집 프로젝트",
    "projects.number": "프로젝트 일련번호",
    "projects.name": "프로젝트 명칭",
    "projects.name.enter": "프로젝트 이름을 입력하십시오",
    "projects.description": "프로젝트 묘사",
    "projects.sponsor": "신청인 측",
    "projects.name.sponsor": "스폰서를 입력하십시오",
    "projects.connectEdc": "데이터 수집 동시 진행",
    "projects.plannedCases": "Planned Cases",
    "projects.cycle": "Project Cycle",
    "projects.plannedCases.enter": "Please enter planned cases",
    "projects.plannedCases.enter.min": "The planned cases must be greater than 0",
    "projects.second": "Cohort",
    "projects.third": "Re-randomization",

    "projects.connectAli": "안심 코드",
    "projects.connectAliID": "안심 코드 아이디",
    "projects.type": "프로젝트 분류",

    "projects.types.first": "기본 연구",
    "projects.types.second": "Cohort Study",
    "projects.types.third": "Re-randomization Study",
    "projects.administrators": "프로젝트 매니저",
    "projects.envs": "프로젝트 환경",
    "projects.envs.all": "All Environments",
    "projects.envs.create": "시작 환경",
    "projects.envs.add": "Add",
    "projects.envs.delete": "해당 환경 삭제",
    "projects.envs.empty": "환경 명칭은 비울 수 없습니다",
    "projects.envs.duplicate.name": "환경 명칭은 중복될 수 없습니다",
    "projects.envs.cohorts.capacity": "그룹원이 한계치입니다.",
    "projects.envs.cohorts.factor": "요소",
    "projects.envs.cohorts.stage": "전 단계",
    "projects.envs.cohorts.status1": "초안",
    "projects.envs.cohorts.status2": "그룹에 들어가기",
    "projects.envs.cohorts.status3": "완성",
    "projects.envs.cohorts.status4": "멈춤",
    "projects.envs.copy": "복제환경",
    "projects.envs.the.copy": "해당환경 복제",

    "projects.envs.name": "환경명칭",
    "projects.envs.current": "Current Environment",
    "projects.envs.new": "New Environment",
    "projects.users": "프로젝트 구성원",
    "projects.users.add": "구성원 추가",
    "projects.users.edit": "편집인",
    "customers.add": "새로운 클라이언트",
    "customers.edit": "클라이언트 수정",
    "customers.name": "클라이언트 명칭",
    "roles.add": "새로운 역할",
    "roles.edit": "역할 수정",
    "roles.name": "역할 명칭",
    "roles.scope": "분류",
    "user.add": "새로운 유저",
    "user.edit": "유저 편집",
    "user.name": "이름",
    "user.allocationRole": "역할 분배",
    "user.allocationRole.info": "먼저 분배가 필요한 역할을 선택해주세요",
    "user.allocationProject": "프로젝트 분배",
    "user.list": "유저 목록",
    "user.no.exist": "유저가 존재하지 않습니다",
    "user.status.notActive": "비활성화",
    "user.status.active": "활성화됨",
    "user.resend.invite.email": "다시 보내기",
    "user.resend.invite.email.info": "계정 활성화 메일을 다시 보내시겠습니까?",
    "user.customer.admin.add": "이 계정에 admin 권한을 부여하시겠습니까?",
    "user.customer.admin.remove": "이 계정의 admin 권한을 취소 하시겠습니까?",
    "user.customer.learn": "수업 시작",
    "user.customer.unLearn": "아직 학습을 완료하지 않았습니다!!!",
    "user.customer.isLearn": "학습을 전부 마치셨습니까?",
    "user.customer.cancel": "로그인 화면으로 돌아가기, 잠시 후에 마치겠습니다.",
    "user.customer.welcome": "로그인을 환영합니다",
    "user.customer.invite": "관리자가 당신을 시스템 교육 학습을 완료하길 희망합니다.",

    "form.setting": "차트 설정",
    "form.preview": "미리 보기",
    "form.field.name": "변량 명칭",
    "form.field.label": "필드 명칭",
    "form.modify": "수정 가능 여부",
    "form.list.modify": "Editable or Not",
    "form.required": "필수의",
    "form.control.type": "제어 장치 종류",
    "form.control.type.input": "입력란",
    "form.control.type.inputNumber": "숫자 입력란",
    "form.control.type.textArea": "많은 줄 텍스트 셀",
    "form.control.type.checkbox": "여러 셀 선택하기",
    "form.control.type.radio": "셀 하나 선택하기",
    "form.control.type.switch": "스위치",
    "form.control.type.date": "날짜 선택란",
    "form.control.type.dateTime": "시간 선택란",
    "form.control.type.select": "드롭다운 리스트",
    "form.control.type.maximum": "인원수 제한",
    "form.control.type.exact": "인원수 정확도",
    "form.control.type.le": "작거나 같음",
    "form.control.type.eq": "같음",
    "form.control.type.options": "옵션",
    "form.control.type.label": "명칭",
    "form.control.type.value": "값",
    "form.control.type.format": "형식 유형",
    "form.control.type.format.characterLength": "문자 길이",
    "form.control.type.format.numberLength": "길이",
    "form.control.type.format.decimalLength": "십진 길이",
    "form.control.type.format.checkbox": "확인란",
    "form.control.type.variableFormat": "가변 형식",
    "form.control.type.variableRange": "가변 범위",
    "form.control.type.variableRange.min": "최소값을 입력하십시오.",
    "form.control.type.variableRange.max": "최대값을 입력하십시오.",
    "form.control.type.variableRange.validate": "최대값은 최소값보다 크거나 같아야 합니다.다시 입력하십시오.",
    "form.control.type.options.lack": "누락 옵션",
    "form.validate.info": "각 요소 필드를 수정할 수 있음으로 설정할 수 없음",
    "projects.attributes.random": "랜덤 여부",
    "projects.attributes.isRandom": "Randomize",
    "projects.attributes.randomYes": "Randomize",
    "projects.attributes.randomNo": "non-randomized",
    "projects.attributes.random.control": "Randomization Supply Check",
    "projects.attributes.random.control.info":
        "After opening, when the inventory in the site / depot is insufficient or the IP is not configured, it is not allowed to continue random.",
    "projects.attributes.is.random.showNumber": "Display Randomization Number ",
    "projects.attributes.is.random.number.show": "yes",
    "projects.attributes.is.random.number.notShow": "no",
    "projects.attributes.dispensingDesign": "Dispensing Design",
    "projects.attributes.dispensing.yes": "Dispense",
    "projects.attributes.dispensing.no": "With no dispensing operation",
    "projects.attributes.dtp.rule": "DTP Rules",
    "projects.attributes.dtp.rule.ip": "IP",
    "projects.attributes.dtp.rule.ip.tooltip": "In the project dimension, add IP shipped using the confirmed DTP method, and can be applied on subject dispensation scenario.",
    "projects.attributes.dtp.rule.visitFlow": "Visit Flow",
    "projects.attributes.dtp.rule.notApplicable": "Not Applicable",
    "projects.attributes.dispensing.noInfo":
        "After saving, the system will automatically hide the menus and functions related to dispensing, and it will take effect after logging in again.",
    "projects.attributes.isBlind": "Blind Design",
    "projects.attributes.isBlind.blind": "Blind",
    "projects.attributes.isBlind.open": "Open",
    "projects.attributes.isFreeze.info":
        "After enabling, when running the shipping algorithm, the isolated items are calculated as the available inventory of the research center by default.",
    "projects.attributes.saveInfo":
        'Confirmation succeeded. If "no dispensing" is selected for the project, the menu and permission about dispensing in the project will be hidden automatically, and it will take effect after re login.',
    "projects.attributes.isFreeze": "Quarantine IP Calculation Rules",
    "projects.attributes.prefix": "Subject No. Prefix",
    "projects.attributes.subject.replace.text": "Replacement text for Subject Number ",
    "projects.attributes.dispensing": "발약 여부",
    "projects.attributes.blind": "맹법 여부",
    "projects.attributes.instituteLayered": "센터 분층",
    "projects.attributes.edc.label": "EDC 제품 을 연구 하 다 설정 라벨",
    "projects.attributes.systemRules": "System configuration",
    "projects.attributes.subject.numberRules": "피험자 넘버 이용법",
    "projects.attributes.other.numberRules": "다른 이용법",
    "projects.attributes.subject.type.site.numberPrefix": "센터를 접두사로 사용 여부",
    "projects.attributes.subject.prefixConnector": "접두사 연결 부호",
    "projects.attributes.subject.other.prefix": "피험자 번호가 다른 접두사",
    "projects.attributes.subject.prefix.text": "접두사 원문",
    "projects.attributes.subject.isRandom": "센터에서 랜덤으로 번호를 받지 못하면 그룹에 입장 할 수 없습니다",

    "projects.randomization.configure": "랜덤설정",
    "projects.randomization.list": "랜덤 목록",
    "projects.randomization.factor": "분층 요소",
    "projects.randomization.separate": "랜덤 분단",
    "projects.randomization.type": "랜덤 유형",
    "projects.randomization.blockRandom": "랜덤으로 그룹 설정",
    "projects.randomization.factorBlock": "레이어별로 랜덤 그룹 설정",
    "projects.randomization.minimize": "랜덤을 최소화",
    "projects.randomization.source": "Source of Randomization Number",
    "projects.randomization.upload": "시스템 업로드",
    "projects.randomization.generate": "시스템 생성",
    "projects.randomization.group": "치료 그룹",
    "projects.randomization.groupRatio": "그룹 간 비율",
    "projects.randomization.groupMapping": "그룹별 영사도",
    "projects.randomization.factorRatio": "계층별 요소 가중치",
    "projects.randomization.probability": "편향 확률",
    "projects.randomization.blockLength": "그룹별 길이",
    "projects.randomization.total": "총 예시",
    "projects.randomization.seed": "랜덤 종자",
    "projects.randomization.prefix": "랜덤 접두사",
    "projects.randomization.count": "랜덤 수량",
    "projects.randomization.useCount": "랜덤 번호(이미 사용됨/총 갯수)",
    "projects.randomization.defaultNumber": "초기 넘버",
    "projects.randomization.numberLength": "번호 길이",
    "projects.randomization.numberPrefix": "번호 접두사",
    "projects.randomization.blockConfig": "구간 그룹 설정",
    "projects.randomization.groupConfig": "그룹별 설정",
    "projects.randomization.weight": "가중",
    "projects.randomization.confirmUpload": "파일을 업로드해주세요",
    "projects.randomization.lockFactor": "분층이 부족합니다",
    "projects.randomization.design": "랜덤 설계",
    "projects.randomization.form": "차트 설정",
    "projects.randomization.allocation.factor": "기타 분층 비우기",

    "projects.site.list": "관련 센터",
    "projects.site.no.supplyPlan": "공급 계획을 관련 센터에 제출하십시오.",
    "projects.site.supplyPlan.0init_supply": "초기 발약량이 0이므로 발약을 초기화할 수 없습니다",
    "projects.site.send.medicine": "발약 확인",
    "projects.site.not.enough.medicine": "제품 을 연구 하 다({0}) 재고량이 부족합니다.",
    "project.site.add_order_error": "이 센터는 이미 활성화 되어 초기 오더를 생성을 할 수 없습니다.",
    "projects.site.medicine": "중심제품 을 연구 하 다",
    "projects.storehouse.list": "창고 목록",
    "projects.storehouse.name": "창고 명칭",
    "projects.storehouse.inStorehouse": "해당 창고의 제품 을 연구 하 다 반입을 확인 하셨습니까?",
    "projects.site.name": "센터 명칭",
    "projects.site.name.standard": "센터 표준 이름",
    "projects.site.short.name": "센터 약어",
    "projects.site.number": "Site Number",
    "projects.site.supply": "Automatic order supply",
    "projects.site.open": "Open",
    "projects.site.close": "Close",
    "projects.drug.alert": "제품 을 연구 하 다 경계",
    "projects.drug.alert.check": "제품 을 연구 하 다명 또는 경계치를 선택하세요",
    "visit.cycle.name": "방문 주기",
    "visit.cycle.setting.unscheduled_visit": "Unscheduled Visit",
    "visit.cycle.setting.nameZh": "Chinese Name",
    "visit.cycle.setting.nameEn": "English Name",
    "visit.cycle.visitNumber": "방문 넘버",
    "visit.cycle.visitName": "방문시 명칭",
    "visit.cycle.startDays": "시작 일수",
    "visit.cycle.endDays": "종료 일수",
    "visit.cycle.dispensing": "발약 여부",
    "visit.cycle.random": "랜덤 여부",
    "visit.cycle.dtp": "是否允许DTP",

    "drug.configure": "제품 을 연구 하 다제품 을 연구 하 다설정",
    "drug.configure.group": "그룹별",
    "drug.configure.drugName": "제품 을 연구 하 다명칭",
    "drug.configure.visitName": "방문시 명칭",
    "drug.configure.roomNumber": "룸 넘버",
    "drug.configure.drugNumber": "발약 수량",
    "drug.configure.drugNumber.limit": "发放数量限制",
    "drug.configure.drugLabel": "발약 라벨",
    "drug.configure.showAll": "Distribute and display all",
    "drug.configure.routine.visit.mapping": "일반 뷰 맵",
    "drug.configure.drugSpecifications": "제품 을 연구 하 다 규격",
    "drug.configure.PkgSpecifications": "포장규격",
    "drug.configure.delete":
        "제품 을 연구 하 다 설정 프로필을 삭제하면 제품 을 연구 하 다의 프로필에 영향을 미칠 수 있습니다, 정말로 삭제하시겠습니까?",
    "drug.list": "제품 을 연구 하 다 목록",
    "drug.list.batchMag": "화물 관리",
    "drug.list.packlist": "포장명세서",
    "drug.list.isolation": "격리",
    "drug.list.release": "Release",
    "drug.list.lost": "분실/ 폐기",
    "drug.list.setUse": "사용 가능하게 설정",
    "drug.list.drugNumber": "제품 번호 연구",
    "drug.list.expireDate": "유효기간",

    "drug.list.batch": "화물 넘버",
    "drug.list.status": "상태",
    "drug.list.verificationCode": "제품 을 연구 하 다 인증 코드",
    "drug.list.delete": "일괄 삭제",
    "drug.list.delete.info": "삭제할 제품 을 연구 하 다을 선택해주세요",
    "drug.list.availableCount": "재고",
    "drug.list.number.placeholder": "제품 검토 번호를 입력하십시오.",
    "drug.batch.management.stock": "재고 화물 넘버",
    "drug.batch.management.status": "제품 을 연구 하 다 상태",
    "drug.batch.management.update": "화물 넘버 업데이트",
    "drug.batch.management.selectStatus": "제품 을 연구 하 다 상태를 선택하세요",
    "drug.batch.management.updateExpireDate": "유효기간을 갱신",
    "drug.upload.uploadDrug": "연구 제품 번호 업로드",
    "drug.other": "일련번호 미상 제품 을 연구 하 다",
    "drug.medicine": "번호 연구 제품",
    "drug.other.name": "제품 을 연구 하 다명칭",
    "drug.other.count": "수량",
    "drug.other.singleCount": "单品数量",
    "drug.other.packageCount": "包装数量",
    "drug.other.add.error": "The quantity of items does not match the packaging ratio, please reconfirm.",
    "drug.medicine.packlist": "포장 넘버",
    "drug.medicine.package.serial_number": "The package sequence number",
    "drug.medicine.packlist.upload": "포장 명세서 업로드",
    "drug.medicine.packlist.downtemplate": "포장 명세서 양식 다운로드",
    "drug.medicine.packlist.select.file": "업로드 할 포장 명세서 파일을 선택하세요",
    "drug.medicine.package.setting.isOpen": "按包装运输",
    "drug.medicine.package.setting.info": "开启后，订单新增等会按照包装运输",
    "drug.medicine.package.setting": "包装配置",
    "medicine.status.available": "사용 가능",
    "medicine.status.delivered": "확인",
    "medicine.status.sending": "배송됨",
    "medicine.status.quarantine": "격리됨",
    "medicine.status.used": "이미 사용 중",
    "medicine.status.transit": "배송됨",
    "medicine.status.lose": "분실/ 폐기",
    "medicine.status.expired": "기간이 지남",
    "medicine.status.receive": "이미 수취한 제품 을 연구 하 다",
    "medicine.status.return": "이미 반품됨",
    "medicine.status.destroy": "이미 소각됨",
    "medicine.status.InOrder": "확인을 기다리다",
    "medicine.status.stockPending": "입고 대기",
    "medicine.status.inStorage": "입고 중",
    "medicine.status.apply": "신청됨",
    "medicine.status.locked": "Locked",
    "validator.message.phone": "유효하지 않은 휴대폰 번호",
    "validator.message.replace.medicine": "Please Select replacement IP",
    "randomization.config.type": "랜덤 유형",
    "randomization.config.group": "치료 그룹",
    "randomization.config.factor": "분층 요소",
    "randomization.config.factors": "분층",
    "randomization.config.randomList": "랜덤 목록",
    "randomization.config.groupAdd": "Add Block",
    "randomization.config.groupEdit": "Edit Block",
    "randomization.config.factorAdd": "Add Stratification Factor",
    "randomization.config.factorEdit": "Edit Stratification Factor",
    "randomization.config.desc": "묘사",
    "randomization.config.groupSize": "수용 가능한 그룹의 크기(여러개의 그룹들과 분리)",
    "randomization.config.number": "필드 일련번호",
    "randomization.config.code": "그룹 코드",
    "randomization.random.list.invalid": "폐기",
    "randomization.random.list.invalid.doubt.title": "폐기가 확실합니까?",
    "randomization.random.list.invalid.doubt": "폐기된 후, 이 랜덤 시계는 회복할 수 없습니다.",
    "randomization.random.list.invalid.doubt.first":
        " that  subjects already complete randomization/dispensing operation,",
    "randomization.random.list.invalid.doubt.info": "System detects",
    "randomization.random.list.invalid.doubt.second": "폐기 후에는 회복할 수 없을 것이다.",
    "projects.randomization.randomizationSubTab": "랜덤 목록 속성",
    "projects.randomization.randomNumberTab": "랜덤 번호 그룹",
    "projects.randomization.block": "그룹",
    "projects.randomization.randomNumber": "랜덤 번호",
    "projects.randomization.groupList": "그룹별",
    "projects.randomization.planNumber": "계획 랜덤수",
    "projects.randomization.randomCount": "랜덤 수량",
    "projects.randomization.actualPeople": "실제 인원수",
    "projects.randomization.planPeople": "예상 인원수",
    "projects.randomization.alertPeople": "경계 인원수",
    "projects.randomization.alertSetFactor": "랜덤 목록이 분층에 분배되지 않음",
    "projects.randomization.settingPeople": "인원수 설정",
    "projects.randomization.settingRandom": "현재 랜덤으로 설정되지 않은 프로젝트입니다, 속성을 설정하십시오.",
    "projects.randomization.settingFactor": "랜덤 목록에 계층적 요소가 할당되지 않음",
    "projects.randomization.selectBlock": "구성을 선택하십시오",
    "projects.randomization.nullFactor": "계층적 요소가 선택되지 않음",
    "projects.randomization.nullSite": "错误！分配机构为空",
    "projects.randomization.distribution": "분배",
    "projects.randomization.distributionCenter": "그룹을 센터에 할당",
    "projects.randomization.distributionFactor": "그룹을 분층에 분배",
    "projects.randomization.sourceSite": "분배기관",
    "projects.randomization.targetSite": "접수기관",
    "projects.randomization.enterSourceSite": "분배기구를 선택하세요",
    "projects.randomization.enterTargetSite": "접수기관을 선택하세요",
    "projects.randomization.enterFactor": "분층을 선택하세요",
    "projects.randomization.factorRules": "분층 규칙",
    "projects.randomization.otherFactor": "기타 분층",
    "projects.randomization.generateVerificationTips1": "그룹별 길이의 비율은 배수여야만 합니다",
    "projects.randomization.generateVerificationTips2": "각 그룹별 길이의 비율은 배수여야만 합니다",
    "projects.randomization.generateGroupWeightRatio": "그룹 설정 (가중치)",
    "projects.randomization.generateWeightRatio": "가중치",
    "projects.randomization.generateBlockNumber": "그룹 수",
    "projects.randomization.generateLayeredWeightRatio": "분층 설정 (가중치)",
    "projects.randomization.last.group": "전 단계 그룹",
    "storehouse.name": "창고",
    "projects.statistics.site": "중심",
    "projects.statistics.selectField": "이유를 선택하세요",
    "projects.storehouse.statistics.summary": "요약 서술",
    "projects.storehouse.statistics.sku": "단품",
    "projects.storehouse.statistics.other.sku": "번호가 없는 제품 을 연구 하 다 단품",
    "projects.statistics.sku.expirationDate": "유통기한",
    "projects.statistics.sku.place": "위치",
    "projects.statistics.sku.status": "상태",
    "projects.storehouse.statistics.send.unit": "발송 기관",
    "projects.storehouse.no.storehouse": "창고에 연락하십시오",
    "projects.storehouse.bind": "관련 창고",
    "projects.storehouse.connected": "연결을 확인하셨습니까?",
    "projects.sitePharmacy.medicineReceive": "제품 을 연구 하 다 접수",
    "projects.sitePharmacy.storehouseMedicine": "재고가 있는 제품 을 연구 하 다",
    "projects.sitePharmacy.availableMedicine": "사용 가능한 제품 을 연구 하 다",
    "projects.sitePharmacy.usedMedicine": "이미 사용한 제품 을 연구 하 다",
    "projects.sitePharmacy.isolateMedicine": "격리 제품 을 연구 하 다",
    "projects.sitePharmacy.wasteMedicine": "폐기 제품 을 연구 하 다",
    "projects.sitePharmacy.orderHistory": "예전 오더",
    "projects.sitePharmacy.order.status": "오더 상태",
    "projects.sitePharmacy.order.medicineQuantity": "제품 을 연구 하 다 수량",
    "projects.sitePharmacy.order.receiver": "받는이",
    "projects.sitePharmacy.order.receiveDate": "접수일자",
    "projects.sitePharmacy.no.site": "센터에 연락하세요",
    "drug.freeze.release": "제품 을 연구 하 다 격리 해제",
    "drug.freeze.reason": "원인",
    "drug.freeze.form-item.reason": "원인",
    "drug.freeze.selectData": "격리가 필요한 제품 을 연구 하 다을 먼저 선택하세요",
    "drug.freeze.number": "격리 넘버",
    "drug.freeze.startDate": "격리 날짜",
    "drug.freeze.operator": "조작자",
    "drug.freeze.endDate": "클로징 날짜",
    "drug.freeze.count": "수량",
    "drug.freeze.receive.count": "접수 수량",
    "drug.freeze.receiveFreeze.count": "Quarantined Quantity",
    "drug.freeze.institute": "격리 위치",
    "shipment.orderNumber": "오더 번호",
    "shipment.send": "운송시작지",
    "shipment.receive": "목적지",
    "shipment.status": "상태",
    "shipment.medicine": "제품 을 연구 하 다",
    "shipment.supplement.mode": "보충 방법",
    "shipment.other.drug": "기타제품 을 연구 하 다",
    "shipment.drug": "IP",
    "shipment.approval.confirm.title": "Approval confirmation",
    "shipment.approval.confirm": "Approval",
    "shipment.supply.info": "Choose non-default supply plan, it will creat orders automatically after approved.",
    "shipment.mode.set": "발약 수량",
    "shipment.mode.reSupply": "재공급량",
    "shipment.mode.max": "최대 완충량",
    "shipment.cancal.order": "주문 취소",
    "shipment.transit.order": "이 주문 배송을 하시겠습니까?",
    "shipment.supply": "Current supply plan",
    "shipment.supply.count": "Sum of Supply",
    "shipment.expectedArrivalTime": "Expected Arrival Time",
    "shipment.actualReceiptTime": "Actual Receipt Time",
    "shipment.lose.order": "오더 분실",
    "shipment.end.order": "배송 취소",
    "shipment.transit": "운송",
    "shipment.end": "중지",
    "shipment.lose": "분실됨",
    "shipment.received": "접수",
    "shipment.approvalTask": "Approval Record",
    "shipment.order.contacts.detail": "Detail",
    "shipment.order.fail.title": "Submit failed",
    "shipment.order.success.title": "The order will be created automatically after it is submitted successfully.",
    "shipment.order.received": "오더 접수",
    "shipment.order.create.modeMax.info":
        "The destination inventory has exceeded the maximum buffer, and IP order is not generated",
    "shipment.order.received.info":
        "사용가능한 제품 을 연구 하 다을 고르세요, 고르지 않은 제품 을 연구 하 다들은 자동으로 격리 처리되어 격리 기록이 생성됩니다.",
    "shipment.order.medicines": "제품 을 연구 하 다 주문정보",
    "shipment.order.sendAndReceive.info": "출발지와 목적지는 같을 수 없습니다",
    "shipment.order.create.info": "제품 을 연구 하 다명을 선택하세요",
    "shipment.order.create.count.info": "발약 수량을 입력해 주세요",
    "shipment.order.change.count.info": "Please fill in quantity",
    "shipment.order.availableCount": "재고 수량",
    "shipment.status.requested": "확인",
    "shipment.status.transit": "배송됨",
    "shipment.status.received": "수신됨",
    "shipment.status.lose": "분실됨",
    "shipment.status.cancelled": "취소됨",
    "shipment.status.toBeConfirm": "확인 대기중",
    "shipment.status.apply": "신청하다.",
    "shipment.status.end": "종료",
    "shipment.receive.reason": "미수취 제품 을 연구 하 다",
    "shipment.receive.user": "수취인",
    "shipment.receive.time": "접수 시간",
    "shipment.canceller": "취소하신 분",
    "shipment.cancel.time": "취소 시간",
    "shipment.list.role": "보기 권한 없음",
    "shipment.store.alarm": "재고조사",
    "shipment.confirm.order": "오더 확인",
    "shipment.oper.confirm.order": "확인",
    "shipment.receive.confirm.order": "곧 연구 제품을 받으십시오",
    "shipment.isolation.confirm.order": "연구 제품의 즉각적인 격리",
    "shipment.cancel.order": "주문 취소",
    "shipment.confirm.select": "확인할 데이터 선택해주세요",
    "shipment.medicine.availableCount": "库存",
    "projects.supplyPlan.warning": "제품 을 연구 하 다 경계치",
    "projects.supplyPlan.buffer": "최대 완충량",
    "projects.supplyPlan.secondSupply": "재공급량",
    "projects.supplyPlan.initSupply": "초기 발약량",
    "projects.supplyPlan.unDistributionDate": "배송하지 않는 일수",
    "projects.supplyPlan.unProvideDate": "방출하지 않는 일수",
    "projects.supplyPlan.validityReminder": "유효기간 알림",
    "projects.supplyPlan.autoSupply": "자동 제품 을 연구 하 다 조제",
    "projects.supplyPlan.autoSupplySize": "자동 조제량",
    "projects.supplyPlan.supplyMode": "보충 방법",
    "projects.supplyPlan.name": "계획 명칭",
    "projects.supplyPlan.site": "연구센터",
    "projects.supplyPlan.storehouse": "약국 재고",
    "projects.supplyPlan.description": "계획 설명",
    "projects.supplyPlan.allSupply": "모든 제품 을 연구 하 다 보충",
    "projects.supplyPlan.singleSupply": "단품 제품 을 연구 하 다 보충",
    "projects.supplyPlan.allSupplyAndMedicine": "모든 제품 을 연구 하 다 보충 + 1개의 랜덤 제품 을 연구 하 다번호",
    "projects.supplyPlan.singleSupplyAndMedicine": "단품 제품 을 연구 하 다 보충 + 1개의 랜덤 제품 을 연구 하 다번호",
    "projects.supplyPlan.control": "Supply Plan Control",
    "projects.supplyPlan.control.tips":  "Supply Plan Control: After activation, research products with different attributes can be independently configured and different central inventory verification methods can be applied; \n" +
        "Central Inventory Alert: Based on the \"Central Inventory Alert Value\" configured in the Plan, if the conditions are met, a central inventory alert email notification will be triggered; \n" +
        "Automatic Supply: The center will activate automatic supply, verify inventory based on the triggering conditions of automatic order verification, and create an automatic order when inventory is insufficient; \n" +
        "Blind Research Product Minimum Prediction Automatic Supply Deactivation: Due to the requirement of consistent automatic allocation methods for blind research products, this option is selected when blind product automatic supply is not needed, and the corresponding window period is configured as 0-0.",
    "projects.supplyPlan.siteWarning": "Site Inventory Alert",
    "projects.supplyPlan.auto": "Automatic Supply",
    "projects.supplyPlan.drugBlind": "Blind IP",
    "projects.supplyPlan.drugOpen": "Open IP",
    "projects.supplyPlan.drugBlindAuto": "Blind IP minimum predicted automatic supply shutdown",
    "projects.subject.selectSite": "센터를 선택하지 않았거나 보기 권한 없음",
    "subject.registration.success": " Registration Successful",
    "subject.status.registered": "등록됨",
    "subject.status.to.be.random": "To be randomized",
    "subject.status.filtered": "선별됨",
    "subject.status.random": "랜덤으로 설정됨",
    "subject.status.blinded.urgent": "알림 (긴급)",
    "subject.status.blinded.pv": "문맹( pv)",
    "subject.status.exited": "종료됨",
    "subject.status.replace": "변경됨",
    "subject.confirm.random": "무작위로 진행하시겠습니까?",

    "subject.confirm.exited": "종료하시겠습니까?",
    "subject.random": "랜덤",
    "subject.update": "고치다.",
    "subject.exited": "탈퇴하다.",
    "subject.unblinding.urgent": "블라인드 실험 (긴급)",
    "subject.unblinding.pv": "블라인드 실험 (pv)",

    "subject.trail": "방향",
    "subject.register": "등록",
    "subject.register.add": "피험자 등록",
    "subject.register.update": "피험자 수정",
    "subject.reason": "원인",
    "subject.sign.out": "제목 로그아웃",
    "subject.replace": "교체",
    "subject.register.replace": "피험자 교체",
    "subject.number": "피험자 넘버",
    "subject.number.replace": "제목 번호 바꾸기",
    "subject.replace.button": "Confirmation random",
    "subject.number.random.replace": "난수 바꾸기",
    "subject.number.no.empty": "기입 필수",
    "subject.already.unblinding": "블라인드 실험 완료",
    "subject.number.digit.no": "입력하신 길이는 실제 설정 길이와 같지 않습니다",
    "subject.unblinding.download": "블라인드 실험 다운로드",
    "subject.dispensing.download": "발약 다운로드",
    "subject.random.download": "랜덤 다운로드",
    "subject.dispensing.subjectInfo": "피험자 발약 정보",
    "subject.dispensing.info": "발약 정보",
    "subject.replace.info.old": "원본 피험자 정보",
    "subject.dispensing.subject": "피험자 정보",
    "subject.dispensing.record": "발약기록",
    "subject.dispensing.time": "발약 시간",
    "subject.dispensing.drugNumber": "제품 을 연구 하 다 넘버",
    "subject.dispensing.drugNumber.system": "제품 시스템 번호 검토",
    "subject.dispensing.selectDrugNumber": "제품 을 연구 하 다넘버를 선택하세요",
    "subject.dispensing.drugName": "제품 을 연구 하 다명칭",
    "subject.dispensing.drugLabel": "제품 을 연구 하 다 라벨",
    "subject.dispensing.dispensing": "약 효과 발생",
    "subject.dispensing.reissue": "재발급",
    "subject.dispensing.replace": "피험자 연구 제품 교체 확인",
    "subject.dispensing.replace.reason": "Replace Reason",
    "subject.dispensing.cancel": "취소",
    "subject.dispensing.visitSign": "Unscheduled",
    "subject.dispensing.visitSignDispensing": "Unscheduled Dispense",
    "subject.dispensing.visitSignDispensingReason": "Unscheduled Dispensation Reason",
    "subject.dispensing.room": "룸 넘버",
    "subject.dispensing.apply.subjectInfo.apply": "수험자 발약 신청 상세 정보",
    "subject.dispensing.apply.time": "신청 시간",
    "subject.dispensing.apply.order.tip":
        "연구 제품 주문 신청이 성공하면 공급업체에 제때에 제품을 제공하라고 통지해 주십시오.",
    "subject.dispensing.apply.order.tip.apply":
        "주문 번호 상태는 요청됨이며 제품 주문 검토 페이지에서 확인할 수 있습니다.",
    "subject.dispensing.apply.order.tip.to_send": "Order status is ‘Applied’, please confirm on ‘Order’ page.",

    "notice.type": "알림 종류",
    "notice.subject.random": "피험자 무작위",
    "notice.subject.dispensing": "피험자 발약",
    "notice.subject.alarm": "피험자 경계",
    "notice.subject.unblinding.type": "Unblinding Type",
    "notice.subject.unblinding": "긴급 블라인드 실험",
    "notice.subject.pv.unblinding": "PV揭盲",
    "notice.medicine.isolation": "제품 을 연구 하 다 격리",
    "notice.medicine.order": "제품 을 연구 하 다 주문 통지",
    "notice.medicine.reminder": "제품 을 연구 하 다 유효기간 알림",
    "notice.medicine.alarm": "제품 을 연구 하 다 경계 알림",
    "notice.storehouse.alarm": "창고 경계 알림",
    "notice.order.timeout": "오더 시간 초과",
    "notice.email.content": "邮件内容",
    "order.timeout.days": "오더 시간 초과 일수 설정",
    "notice.config.content": "内容配置",
    "notice.config.state": "scene",
    "notice.content.group": "组别",
    "notice.content.number": "随机号",
    "notice.state.dispensing": "发药",
    "notice.state.unscheduled": "计划外发药",
    "notice.state.re.dispensing": "补发",
    "notice.state.replace": "研究产品替换",
    "notice.state.retrieval": "研究产品取回",
    "notice.state.register": "登记实际使用研究产品",
    "tips.processing": "처리 중...",
    "tips.search.permission": "사용자 권한을 가져오는 중....",
    "history.randomization": "무작위 방향 설정",
    "history.order": "Order Trail",
    "history.dispensing": "발약 방향",
    "history.subject": "피험자 방향",
    "history.medicine": "제품 을 연구 하 다 방향",
    "history.supply-plan": "공급계획 방향",
    "history.supply-plan-medicine": "제품 을 연구 하 다 배치 공급 계획 방향",
    "history.sign": "연구자 서명",
    "history.date": "서명 날짜",
    "history.user.search": "연산자를 입력하십시오",
    "history.all": "모든 트랙",

    //未翻译内容
    "barcode.count": "Number of barcodes",
    "barcode.add": "Generate barcode",
    "barcode.available-count": "Number of available",
    "barcode.list": "Barcode List",
    "barcode.scan": "Code scanning and warehousing",
    "barcode.scan.confirm": "Are you sure to send code scanning warehousing task?",
    "barcode.scan.confirm.msg": "After confirmation, please log in to the app to complete the task.",
    "barcode": "Barcode",
    "shortCode.prefix": "Short Code prefix",
    "workTask.add.success": "Task sent successfully",
    "workTask.system.add.success":
        "The system has automatically sent the dispensing confirmation task. Please log in to the app in time.",
    "projects.attributes.code.config": "Code Config",
    "projects.attributes.code.config.method": "Coding Method",
    "projects.attributes.code.rule": "Code Rule",
    "projects.attributes.code.rule.manual": "Manual coding upload",
    "projects.attributes.code.rule.auto": "System automatic coding",
    "projects.attributes.code.rule.software.informed": "Software.informed",
    "projects.attributes.code.rule.information":
        "When using IRT app, users have a certain right to know about their relevant functions and information mobile phones. Common descriptions include user privacy agreement and other contents related to user accounts. After opening, users are supported to sign the informed process when the project uses the app barcode scanning function. After opening, modification is not supported temporarily.",
    "projects.attributes.code.rule.informed.agreement":
        "Please enter the content of the code scanning informed agreement confirmed by the company's legal affairs. The content can only be configured once and saved after confirmation.",
    "projects.attributes.code.rule.confirm.title": "Are you sure you want to save?",
    "projects.attributes.code.rule.confirm.msg": "After confirmation, the configuration cannot be changed.",
    "projects.attributes.segment": "Segment random",
    "projects.attributes.groupsInfo":
        "The system does not detect the group. Please set the group in the random configuration first",
    "common.random.tips": "Is random determined?",
    "projects.synchronization.mode": "Synchronization mode",

    "projects.push.rules": "Push Rules",
    "projects.subject.uid": "Subject UID",
    "projects.push.scene": "Push Scenarios",
    "projects.push.scene.select": "Please select push scenario",
    "projects.subject.update.front": "Subject modify(Before Random)",
    "projects.subject.update.after": "Subject modify(After Random)",
    "projects.subject.random.block": "Stratification checks inconsistency, and randomization blocking is performed",
    "projects.subject.no.tip": "Subject ID: Associated by subject ID as a unique identifier. When the subject ID is modified, the data will be overwritten or added with the unique judgment of the subject ID.",
    "projects.subject.uid.tip": "Subject UID: Associated by the unique ID of the subject in the database. When the subject ID is modified, the data will be overwritten or added with the prioritized binding relationship judgment.",

    "projects.step.by.synchronization": "Step by step synchronization",
    "projects.one.time.full.synchronization": "One time full synchronization",
    "projects.timezone": "TimeZone",
    "projects.envs.oper.lock.title": "Confirm locking?",
    "projects.envs.oper.unlock.title": "Confirm unlocking?",
    "projects.envs.oper.lock.message": "After locking, the data of some pages need to be unlocked before editing.",
    "projects.envs.oper.unlock.message": "After unlocking, the project data can be reconfigured.",
    "projects.envs.lock": "Lock configuration",
    "projects.envs.unlock": "Unlock configuration",
    "user.app.account": "APP Account",
    "projects.attributes.is.random.number": "Show random number",
    "projects.randomization.allocation.factorConfirm": "Confirm to remove factor？",
    "drug.list.entryTime": "Warehousing time",


    "medicine.but.examine": "Approval",
    "medicine.but.update": "Modify",
    "medicine.but.release": "Release",
    "medicine.status.dsm": "To be scanned",
    "medicine.status.dsh": "To be approved",
    "medicine.status.shsb": "Approval failed",
    "medicine.status.examine.tip": "Please select the IP to be reviewed.",
    "medicine.status.update.tip": "Please select the IP that need to be modified. After modification, the IP status will be updated to 'to be approved'.",
    "medicine.status.release.tip": "Please check the IP that need to be released. After release, the IP will be assigned to the corresponding depots.",
    "medicine.status.examine.confirm": "Approval Confirmation",

    "medicine.status.toBeWarehoused": "To be warehoused",
    "shipment.transit.work.task.title": "Are you sure to send shipping order confirmation task?",
    "shipment.transit.work.task.msg": "After confirmation, please log in to the app to complete the task.",
    "shipment.transit.confirm": "Confirm sending",
    "shipment.transit.skip": "Skipped, manually confirmed",
    "shipment.transit.artificial.confirm": "The order has been manually checked offline and confirmed to be correct.",
    "shipment.receive.work.task.title": "Are you sure to send research products receiving task?",
    "subject.check.random.form": "All forms must be completed",
    "subject.unblinding.sponsor": "Notify the sponsor",
    "subject.dispensing.retrieval": "Retrieval",
    "subject.dispensing.invalid": "Do Not Attend the Visit",
    "subject.dispensing.invalidTip": "Are you sure not to attend the visit?",
    "subject.dispensing.confirmInvalid": "If you don't attend, you will skip the drug distribution link in this visit.",
    "subject.dispensing.confirmRetrieval": "Are you sure to retrieve it?",
    "subject.dispensing.retrievalMessage":
        "After retrieval, the research products will be available again. This dispensing was unsuccessful.",
    "subject.unblinding.confirm": "Are the following subjects confirmed to be unblinded?",
    "subject.unblinding.confirmTip": "Unblinding confirm",

    "subject.dispensing.apply": "발약 신청",
    "subject.dispensing.visitSignDispensing.apply": "Unscheduled Dispensing Application",
    "subject.dispensing.reissue.apply": "신청서를 재발급 하다",
    "subject.dispensing.visitSignDispensingReason.apply": "외발약 신청 원인 방문",
    "subject.dispensing.order.view": "주문 보기",
    "subject.dispensing.detail": "돌아가기 상세 정보",
    "subject.dispensing.apply.success": "신청 성공",
    "subject.dispensing.dtp.success": "DTP 주문이 성공적으로 생성되었습니다.",
    "subject.random.confirm": "무작위 확인",
    "subject.random.select.confirm": "무작위를 확인하시겠습니까?",
    "subject.dispensing.confirm": "발약을 확인하시겠습니까?？",
    "subject.dispensing.confirm.replace": "교체를 확인하시겠습니까?？",
    "subject.dispensing.replace.info": "연구 제품 교체",
    "subject.dispensing.replace.select": "대체 연구 제품 선택",
    "subject.dispensing.replace.confirm": "피험자 연구 제품 교체 확인",

    "subject.random.info": "임의의 정보",
    "subject.random.success": "무작위 성공",
    "subject.random.fail": "무작위 실패",
    "subject.random.all": "모든 과목",

    "projects.status": "Project status",
    "projects.status.progress": "In Progress",
    "projects.status.finish": "Completed",
    "projects.status.close": "Closed",
    "projects.status.pause": "Paused",
    "projects.status.terminate": "Terminated",
    "projects.edit.status.progress.tips": "Is it confirmed to revise the status to ‘Progress’?",
    "projects.edit.status.progress.content":
        "After the project is restored to in progress, the project data can be operated.",
    "projects.edit.status.progress.reason": "Please describe the reason for project progress... (required)",
    "projects.edit.status.finish.tips": "Is it confirmed to revise the status to ‘Completed’?",
    "projects.edit.status.finish.content":
        "Please ensure that all execution work and tasks of the project have been completed. After the project is finished, the project data will be inoperable.",
    "projects.edit.status.close.tips": "Is it confirmed to revise the status to ‘Closed’?",
    "projects.edit.status.close.content":
        "After revision, the project data will be frozen and cannot be modified, but can only be viewed.",
    "projects.edit.status.pause.tips": "Is it confirmed to revise the status to ‘Paused’?",
    "projects.edit.status.pause.content":
        "After the project is paused, the project data will be temporarily restricted to operate.",
    "projects.edit.status.pause.reason": "Please describe the reason for project paused... (required)",
    "projects.edit.status.terminate.tips": "Is it confirmed to revise the status to ‘Terminated’?",
    "projects.edit.status.terminate.content":
        "Please make sure the current work of the project has been completed. After the project is terminated, all project data will be frozen and cannot be processed anymore.",
    "projects.edit.status.terminate.reason": "Please describe the reason for project terminated... (required)",
    "common.to.set": "To set",
    "projects.attributes.segment.length": "Random segment length",
    "common.country": "Country and region",

    "projects.attributes.Layered": "Layer",
    "projects.attributes.countryLayered": "Country(Set country as stratification factor)",
    "projects.attributes.country": "Country",
    "subject.Unblinded": "Unblinded",
    "validator.message.site": "Please enter 1-10 digits of numbers / letters",
    "subject.unblinding.remark": "Remark",
    "subject.unblinding.reason": "Reasons for Unblinding",
    "subject.unblinding.real.title":
        "Please confirm the consistency between the actual use of the subject and the research product randomly distributed by the system!",
    "subject.unblinding.real.true": "Consistent or registered,",
    "subject.unblinding.real.false":
        "In case of inconsistency, please register the research product actually used for follow-up tracking,",
    "subject.unblinding.real.continue": "Continue to uncover blindness>>",
    "subject.unblinding.real.register": "Go register>>",
    "user.customer.learnTrain": "Training and learning",
    "user.customer.learnTip": "培训学习提示",
    "user.customer.learnContext": "该项目环境下存在未完成的课程，是否确认参加培训学习",
    "projects.storehouse.logistics.supplier": "Logistics supplier",
    "projects.storehouse.logistics.shengsheng": "SHENGSHENG LOGISTICS",
    "projects.storehouse.logistics.catalent": "catalent",
    "projects.storehouse.logistics.baicheng": "Bioquick",
    "projects.storehouse.not.included": "Isolation study products are not included in the order",
    "storehouse.country.region": "Country and region",
    "projects.connectELearning": "Open eLearning",
    "projects.learning.need": "Must complete course",
    "projects.learning.system.courses": "系统课程",
    "projects.learning.courses": "项目课程",
    "projects.learning.compulsory": "强制学习",
    "projects.no.system.learning": "进入项目失败，当前有未完成的系统课程。",
    "projects.no.learning": "进入项目失败，当前有未完成的项目课程。",
    "projects.learning": "去学习",
    "projects.env": "Environment",
    "projects.orderCheck": "Order Check",
    "projects.orderCheckWeek.noEmpty": "Run shipping algorithm time cannot be empty",
    "projects.timing": "Timing",
    "projects.realTime": "Real Time",
    "projects.manualRun": "(including manual verification)",
    "projects.notApplicable": "Not Applicable",
    "projects.customTime": "Custom Time",
    "simulate_random.name": "이름",
    "simulate_random.site.count": "중심 수",
    "simulate_random.run.count": "실행 횟수",
    "simulate_random.subject.count": "피 실험자 수",
    "simulate_random.detail": "시 뮬 레이 션 상세 정보",
    "simulate_random.site.details": "필드 상세도",
    "simulate_random.site.disequilibrium-number": "중심 [불 균형 수]",
    "simulate_random.site.detail.total": "총계",
    "simulate_random.site.detail.people.count": "인원수",
    "simulate_random.site.detail.unbalanced": "불 균형",
    "simulate_random.run": "운행 하 다.",
    "simulate_random.site.overview": "센터 총람",
    "simulate_random.layered.overview": "레이 어 링 총람",
    "simulate_random.detail.meanStandard": "Mean ± standard deviation",
    "simulate_random.average.value": "평균 값",
    "simulate_random.standard.deviation": "표준 차",
    "projects.attributes.instituteLayer": "센터 분층",
    "subject.dispensing.selectedRepeatedly": "Research products cannot be selected repeatedly",
    "subject.dispensing.NotFilledDrug": "The name or quantity of the study product is incomplete",
    "common.site.country.null": "The country information corresponding to the site cannot be matched",

    "subject.dispensing.realDispensing": "Actual use research product",
    "subject.dispensing.realDispensingTip": "Are you sure to register?",
    "subject.dispensing.realDispensingConfirm": "After registration, the research product status is updated to used.",
    "subject.dispensing.number.register": "Registration of research product actually used",

    "page_notice.system_update": "시스템 업데이트",
    "page_notice.email_error": "메일 예외",
    "page_notice.show_detail": "자세히 보기",
    "page_notice.fail_reason": "실패 원인",
    "page_notice.mail_detail": "우편물 상세 정보",
    "page_notice.mail_resend": "다시 보내기",
    "page_notice.mail_resend_email": "메일박스 다시 보내기",
    "page_notice.message_center_all": "모두",
    "page_notice.message_center_read": "읽지 않음",
    "page_notice.message_center_all_read": "모두 읽음",

    "subject.dispensing.form.number": "Research product delivery system",
    "subject.dispensing.form.realNumber": "Actual use of research product",

    "check.select.project": "Select project",
    "check.select.site": "Select site",
    "check.select.time": "Enrollment time",
    "check.select.unlimited": "Unlimited",

    "dynamic.monitoring.total.random": "총 랜덤",
    "dynamic.monitoring.random.error": "임의 오류",
    "check.affiliated.site": "소속 센터",
    "check.random.group": "무작위 그룹",
    "check.blind.random.group": "블라인드 그룹",
    "check.blind.random.number": "블라인드 랜덤 번호",
    "check.random.time": "랜덤 타임",
    "check.search": "search",
    "check.dispensing.medicine": "Research products number/name",
    "check.dispensing.medicine.blind": "Research product blind",
    "check.dispensing.group": "Research products group",
    "check.dispensing.time": "Dispensing time",
    "check.dispensing.error": "Dispensing error",
    "check.subject.search": "Please enter the subject number to search",
    "check.system": "System monitoring",
    "check.cohort": "Cohort/Stage",
    "group.cohort": "Stage/Group",
    "common.sites": "All Sites",

    "subject.room.download": "Room number export",
    "subject.with.room": "Is include room number",
    "subject.dispensing.room.download": "Room number view record Download",

    "tips.locked": "The screen is locked. Please re-enter your password",
    "user.status.not.active": "비활성",
    "user.status.enable": "사용 가능",
    "user.status.disable": "사용 안 함",

    "menu.back": "메인페이지로 돌아가기",
    "menu.projects.project.subject.screening": "필터",
    "menu.projects.project.subject.register": "등록",
    "menu.projects.project.subject.randomization": "랜덤",
    "menu.projects.project.subject.unblinding": "블라인드 실험",
    "menu.projects.project.subject.dispensing": "약 효과 발생",
    "menu.projects.project.supply.drug_upload": "제품 을 연구 하 다 업로드",
    "menu.projects.project.supply.drug_freeze": "제품 을 연구 하 다 격리",
    "menu.projects.project.build.careDesign": "치료 방안",

    //---------------------------------菜单部分----------------------------------------
    "menu.home": "작업대",
    "menu.settings": "설정",
    "menu.settings.storehouse": "창고",
    "menu.settings.roles": "역할 권한",
    "menu.settings.users": "사용자",
    "menu.projects": "프로젝트",
    "menu.projects.main": "프로젝트",
    "menu.projects.main.env": "프로젝트 환경",
    "menu.projects.main.setting": "프로젝트 설정",
    "menu.projects.main.setting.base": "기본 정보",
    "menu.projects.main.setting.project": "프로젝트 정보",
    "menu.projects.main.setting.function": "비즈니스 기능",
    "menu.projects.main.setting.docking": "외부 도킹",
    "menu.projects.main.setting.permission": "프로젝트 권한",
    "menu.projects.main.setting.custom": "사용자 정의 프로세스",
    "menu.projects.project": "프로젝트 세부정보",
    "menu.projects.project.home": "첫 장",
    "menu.projects.project.subject": "과목 관리",
    "menu.projects.project.subject.urgent-unblinding": "눈가림 해제(긴급)",
    "menu.projects.project.subject.urgent-unblinding.unblinding": "눈가림 해제(긴급)",
    "menu.projects.project.subject.urgent-unblinding.approval-log": "승인 기록",
    "menu.projects.project.subject.urgent-unblinding-pv": "PV Unblinding",
    "menu.projects.project.subject.urgent-unblinding.unblinding-pv": "PV Unblinding",
    "menu.projects.project.subject.urgent-unblinding.approval-log-pv": "승인 기록",
    "menu.projects.project.supply": "공급 관리",
    "menu.projects.project.supply.storehouse": "창고 통계",
    "menu.projects.project.supply.storehouse.summary": "개요",
    "menu.projects.project.supply.storehouse.single": "단일 제품",
    "menu.projects.project.supply.site": "중앙약국",
    "menu.projects.project.supply.site.summary": "개요",
    "menu.projects.project.supply.site.single": "단일 제품",
    "menu.projects.project.supply.site.no_number": "번호가 없는 연구 제품 항목",
    "menu.projects.project.supply.shipment": "제품 주문 조사",
    "menu.projects.project.supply.shipment.approval": "Approval Record",
    "menu.projects.project.supply.shipment.logistics": "Logistics",
    "menu.projects.project.supply.drug_recovery": "연구 제품 재활용",
    "menu.projects.project.supply.release-record": "검역기록",
    "menu.projects.project.supply.drug": "제품을 연구하다",
    "menu.projects.project.supply.drug.order": "제품 주문 조사",
    "menu.projects.project.supply.drug.single": "단일 제품",
    "menu.projects.project.supply.drug.no_number": "번호가 없는 연구 제품 항목",
    "menu.projects.project.build": "프로젝트 빌드",
    "menu.projects.project.build.storehouse": "물류 창고 관리, 물류 관리",
    "menu.projects.project.build.site": "중앙 관리",
    "menu.projects.project.build.site.supply-plan": "供应计划",
    "menu.projects.project.build.attributes": "속성 구성",
    "menu.projects.project.build.code_rule": "인코딩 구성",
    "menu.projects.project.build.simulate_random": "무작위 시뮬레이션",
    "menu.projects.project.build.randomization": "임의 구성",
    "menu.projects.project.build.randomization.design": "무작위 디자인",
    "menu.projects.project.build.randomization.design.type": "랜덤 타입",
    "menu.projects.project.build.randomization.design.group": "치료군",
    "menu.projects.project.build.randomization.design.factor": "계층화 요인",
    "menu.projects.project.build.randomization.design.list": "랜덤 리스트",
    "menu.projects.project.build.randomization.design.attribute": "랜덤 리스트 속성",
    "menu.projects.project.build.randomization.design.block": "난수 블록",
    "menu.projects.project.build.randomization.form": "양식 구성",
    "menu.projects.project.build.randomization.design.factor-in": "계층화 요인",
    "menu.projects.project.build.drug": "제품 구성 연구",
    "menu.projects.project.build.drug.visit": "방문주기",
    "menu.projects.project.build.drug.config": "제품 구성 연구",
    "menu.projects.project.build.drug.list": "제품 목록 검토",
    "menu.projects.project.build.drug.no_number": "번호가 없는 연구 제품",
    "menu.projects.project.build.drug.batch": "일괄 관리",
    "menu.projects.project.build.drug.barcode": "바코드 목록",
    "menu.projects.project.build.plan": "공급 계획",
    "menu.projects.project.build.plan.config": "프로그램 관리",
    "menu.projects.project.build.history": "프로젝트 로그",
    "menu.projects.project.settings": "프로젝트 설정",
    "menu.projects.project.settings.notice": "알림 설정",
    "menu.projects.project.settings.user": "인사관리",
    "menu.projects.project.settings.export": "내보내기 구성",
    "menu.projects.project.settings.role": "역할 권한",
    "menu.projects.project.settings.config": "프로젝트 구성",
    "menu.projects.project.monitor": "동적 모니터링",

    "menu.projects.project.info": "Project Information",
    "operation.projects.project.info.view": "View",
    "menu.projects.project.basic.information": "Basic Information",
    "operation.projects.project.basic.information.view": "View",
    "menu.projects.project.basic.environment": "Project Environment",
    "operation.projects.project.basic.environment.view": "View",
    "menu.projects.project.business.functions": "Business Functions",
    "operation.projects.project.business.functions.view": "View",
    "menu.projects.project.external.docking": "External Docking",
    "operation.projects.project.external.docking.view": "View",
    "menu.projects.project.custom.process": "Custom Process",
    "operation.projects.project.custom.process.view": "View",
    "menu.projects.project.permissions": "Project Permissions",
    "operation.projects.project.permissions.view": "View",
    "menu.projects.notice.permissions": "Project Notification",
    "operation.projects.notice.permissions.view": "View",


    "menu.report": "보고",
    //---------------------------------菜单部分----------------------------------------
    //---------------------------------权限部分----------------------------------------
    "operation.settings.roles.view": "확인하다",
    "operation.settings.roles.add": "에 추가",
    "operation.settings.roles.edit": "편집하다",
    "operation.settings.roles.config": "권한 프로필",
    "operation.settings.roles.export": "동적 모니터링",
    "operation.settings.users.view": "확인하다",
    "operation.settings.users.add": "새로운",
    "operation.settings.users.setting": "설정",
    "operation.settings.users.edit": "편집하다",
    "operation.settings.storehouse.view": "확인하다",
    "operation.settings.storehouse.add": "새로운",
    "operation.settings.storehouse.edit": "편집하다",
    "operation.settings.storehouse.delete": "삭제",
    "operation.projects.main.view": "확인하다",
    "operation.projects.main.create": "프로젝트 생성",
    "operation.projects.main.config.view": "View",
    "operation.projects.main.config.create": "Add Environment",
    "operation.projects.main.config.unlock": "Unlock",
    "operation.projects.main.config.lock": "Lock",
    "operation.projects.main.config.add": "Add",
    "operation.projects.main.config.edit": "Edit",
    "operation.projects.main.config.delete": "Delete",
    "operation.projects.main.config.copy": "Copy",
    "operation.projects.main.setting.view": "확인하다",
    "operation.projects.main.setting.base.view": "확인하다",
    "operation.projects.main.setting.base.edit": "편집하다",

    "operation.projects.main.setting.function.view": "확인하다",
    "operation.projects.main.setting.function.edit": "편집하다",
    "operation.projects.main.setting.function.admin": "관리자 사용/사용 안 함",
    "operation.projects.main.setting.docking.view": "확인하다",
    "operation.projects.main.setting.docking.edit": "편집하다",
    "operation.projects.main.setting.custom.view": "확인하다",
    "operation.projects.main.setting.custom.edit": "편집하다",
    "operation.projects.main.setting.permission.view": "View",
    "operation.projects.main.setting.permission.add": "Add",
    "operation.projects.main.setting.permission.edit": "Edit",
    "operation.projects.main.setting.permission.setting": "Permission Setting",
    "operation.projects.main.setting.permission.export": "Export",
    "operation.projects.home.view": "확인하다",
    "operation.subject.view-list": "확인하다",
    "operation.subject.random": "무작위의",
    "operation.subject.replace": "주제 교체",
    "operation.subject.trail": "주제 궤적",
    "operation.subject.replace.confirm.is":
        "System will replace subject according to the submitted information, please confirm the following information.",
    "operation.subject.unblinding-pv": "揭盲(눈가림 해제(pv))",
    "operation.subject.medicine.view-dispensing": "피험자의 약물 정보 보기",
    "operation.subject.medicine.trail": "분배 트랙",
    "operation.subject.medicine.dispensing": "조제약",
    "operation.subject.medicine.reissue": "재발행",
    "operation.subject.medicine.replace": "연구 제품 교체",
    // "operation.subject.unblinding": "揭盲(눈가림 해제(긴급))",
    "operation.subject.unblinding": "보기",
    "operation.subject.unblinding-application": "눈가림 해제(긴급) 신청",
    "operation.subject.unblinding-approval": "눈가림 해제(긴급) 승인",
    "operation.subject.unblinding-log": "보기",
    "operation.subject.unblinding-sms": "SMS 보내기",
    "operation.subject.unblinding-print": "Print",
    "operation.subject.unblinding-pv-view": "View",
    "operation.subject.unblinding-pv-application": "Unblinding（PV）Application",
    "operation.subject.unblinding-pv-approval": "Unblinding（PV）Approval",
    "operation.subject.unblinding-pv-log": "보기",
    "operation.subject.unblinding-pv-sms": "SMS 보내기",
    "operation.subject.unblinding-pv-print": "Print",
    "operation.subject.download": "블라인드 다운로드",
    "operation.subject.medicine.export": "다운로드",
    "operation.subject.download-random": "랜덤 다운로드",
    "operation.subject.registered": "등록하다",
    "operation.subject.update": "주제 수정",
    "operation.subject.delete": "주제 삭제",
    "operation.subject.medicine.room": "방 번호 보기",
    "operation.subject.medicine.retrieval": "마약 회수",
    "operation.subject.medicine.out-visit-dispensing": "Unscheduled Dispense",
    "operation.subject.medicine.invalid": "이번 방문에 참여하지 않음",
    "operation.subject.medicine.room-download": "호실번호 조회기록 다운로드",
    "operation.subject.medicine.register": "약물의 실제 사용 등록",
    "operation.subject.secede": "피험자 철회",
    "operation.subject.print": "트랙 인쇄",
    "operation.subject-dtp.view-list": "보기",
    "operation.subject-dtp.random": "무작위",
    "operation.subject-dtp.replace": "수험자 교체",
    "operation.subject-dtp.trail": "피험자 경로",
    "operation.subject-dtp.unblinding-pv": "맹인 제거(pv)",
    "operation.subject-dtp.medicine.view-dispensing": "발약 신청 상세 정보",
    "operation.subject-dtp.medicine.trail": "발약 궤적",
    "operation.subject-dtp.medicine.dispensing": "신청하다.",
    "operation.subject-dtp.medicine.reissue": "신청서를 재발급 하다",
    "operation.subject-dtp.medicine.replace": "제품 교체 검토",
    // "operation.subject-dtp.unblinding": "맹인 제거(긴급)",
    "operation.subject-dtp.unblinding": "보기",
    "operation.subject-dtp.unblinding-application": "눈가림 해제(긴급) 신청",
    "operation.subject-dtp.unblinding-approval": "눈가림 해제(긴급) 승인",
    "operation.subject-dtp.unblinding-log": "보기",
    "operation.subject-dtp.unblinding-sms": "SMS 보내기",
    "operation.subject-dtp.download": "블로킹 다운로드",
    "operation.subject-dtp.medicine.export": "발약",
    "operation.subject-dtp.download-random": "무작위 다운로드",
    "operation.subject-dtp.registered": "등기하다",
    "operation.subject-dtp.update": "수험자 수정",
    "operation.subject-dtp.delete": "피험자 삭제",
    "operation.subject-dtp.medicine.room": "방 번호 보기",
    "operation.subject-dtp.medicine.out-visit-dispensing": "Unscheduled Dispense Apply",
    "operation.subject-dtp.medicine.invalid": "이번 방문에 불참하다",
    "operation.subject-dtp.medicine.export-room": "발약 다운로드(방 번호 포함)",
    "operation.subject-dtp.medicine.room-download": "룸 번호 보기 레코드 다운로드",
    "operation.subject-dtp.medicine.register": "실제 사용 약물 을 등록 하다",
    "operation.subject-dtp.secede": "피험자 종료",
    "operation.subject-dtp.print": "플롯 경로",
    "operation.supply.storehouse.medicine.summary": "확인하다",
    "operation.supply.storehouse.medicine.singe": "확인하다",
    "operation.supply.storehouse.medicine.use": "사용할 수 있도록",
    "operation.supply.storehouse.medicine.freeze": "격리",
    "operation.supply.storehouse.medicine.Lost": "Lost/Void",
    "operation.supply.storehouse.medicine.history": "과정",
    "operation.supply.storehouse.medicine.download": "데이터 다운로드",
    "operation.supply.site.medicine.summary": "확인하다",
    "operation.supply.site.medicine.singe": "확인하다",
    "operation.supply.site.medicine.use": "사용할 수 있도록",
    "operation.supply.site.medicine.freeze": "격리",
    "operation.supply.site.medicine.lost": "Lost/Void",
    "operation.supply.site.medicine.history": "과정",
    "operation.supply.site.medicine.download": "데이터 다운로드",
    "operation.supply.site.no_number.view": "확인하다",
    "operation.supply.site.no_number.freeze": "격리",
    "operation.supply.site.no_number.lost": "Lost/Void",
    "operation.supply.site.no_number.history": "과정",
    "operation.supply.drug.order.list": "보기",
    "operation.supply.drug.order.send": "주문 배송",
    "operation.supply.drug.order.receive": "주문 접수",
    "operation.supply.drug.order.end": "배송 취소",
    "operation.supply.drug.order.cancel": "주문 취소",
    "operation.supply.drug.order.reason": "이유",
    "operation.supply.drug.order.download": "다운로드",
    "operation.supply.drug.order.history": "궤적",
    "operation.supply.drug.single.sku": "보기",
    "operation.supply.drug.single.history": "궤적",
    "operation.supply.drug.single.download": "다운로드",
    "operation.supply.drug.single.delete": "분실/폐기",
    "operation.supply.drug.single.use": "사용 가능으로 설정",
    "operation.supply.drug.no_number.view": "보기",
    "operation.supply.shipment.create": "주문 추가",
    "operation.supply.shipment.cancel": "배송 주문",
    "operation.supply.shipment.send": "주문 취소",
    "operation.supply.shipment.lose": "분실 주문",
    "operation.supply.shipment.list": "확인하다",
    "operation.supply.shipment.receive": "주문을 받다",
    "operation.supply.shipment.download": "주문 보고서 다운로드",
    "operation.supply.shipment.alarm": "창고 확인",
    "operation.supply.shipment.history": "과정",
    "operation.supply.shipment.confirm": "주문 확인",
    "operation.supply.shipment.reason": "이유",
    "operation.supply.recovery.list": "확인하다",
    "operation.supply.shipment.approval": "Site Order Application Approval",
    "operation.supply.shipment.approval.view": "확인하다",
    "operation.supply.shipment.approval.print": "Print",
    "menu.projects.project.supply.shipment.detail": "IP Detail",
    "operation.supply.shipment.detail.change": "Replace",
    "operation.supply.shipment.detail.changeRecord": "Replacement Records",
    "operation.supply.shipment.detail.edit": "Edit",
    "menu.projects.project.supply.recovery.detail": "IP Detail",
    "operation.supply.recovery.detail.change": "Replace",
    "operation.supply.recovery.detail.changeRecord": "Replacement Records",
    "operation.supply.shipment.close": "Close",
    "operation.supply.shipment.terminated": "Terminated",
    "operation.supply.shipment.logistics.view": "View",
    "operation.supply.shipment.logistics.edit": "Edit",
    "operation.supply.shipment.detail.view": "Detail",
    "operation.supply.recovery.detail.view": "Detail",
    "operation.supply.recovery.add": "새로운",
    "operation.supply.recovery.receive": "연구제품 재활용 주문 접수",
    "operation.supply.recovery.confirm": "배송 연구 제품 재활용 주문",
    "operation.supply.recovery.cancel": "연구제품 재활용 주문 취소",
    "operation.supply.recovery.lose": "플래그 연구 제품 재활용 주문 누락",
    "operation.supply.recovery.download": "다운로드",
    "operation.supply.recovery.reason": "까닭",
    "operation.supply.recovery.history": "과정",
    "operation.supply.freeze.list": "확인하다",
    "operation.supply.freeze.release": "일괄 분리 분리",
    "operation.supply.freeze.delete": "분실/무효화됨",
    "operation.supply.freeze.history": "과정",
    "operation.build.storehouse.add": "새로운",
    "operation.build.storehouse.delete": "창고 삭제",
    "operation.build.storehouse.edit": "창고편집",
    "operation.build.storehouse.notice": "설정",
    "operation.build.storehouse.view": "확인하다",
    "operation.build.storehouse.alarm": "연구 제품 경고",
    "operation.build.site.view": "확인하다",
    "operation.build.site.edit": "편집하다",
    "operation.build.site.add": "센터 추가",
    "operation.build.site.dispensing": "약을 보내다",
    "operation.build.site.supply-plan.view": "View",
    "operation.build.site.supply-plan.edit": "Edit",
    "operation.build.attribute.view": "확인하다",
    "operation.build.attribute.edit": "편집하다",
    "operation.build.attribute.history": "트랙 보기",
    "operation.build.code-rule.view": "확인하다",
    "operation.build.code-rule.edit": "편집하다",
    "operation.build.simulate-random.view": "확인하다",
    "operation.build.simulate-random.edit": "편집하다",
    "operation.build.simulate-random.add": "신설",
    "operation.build.simulate-random.run": "실행",
    "operation.build.simulate-random.site": "센터 개요",
    "operation.build.simulate-random.factor": "계층화 개요",
    "operation.build.simulate-random.download": "데이터 다운로드",
    "operation.build.randomization.type.view": "확인하다",
    "operation.build.randomization.type.edit": "편집하다",
    // "operation.build.randomization.edc.mapping": "Mapping rules of EDC",
    "operation.build.randomization.group.add": "새로운",
    "operation.build.randomization.group.delete": "삭제",
    "operation.build.randomization.group.edit": "편집하다",
    "operation.build.randomization.group.view": "확인하다",
    "operation.build.randomization.factor.add": "새로운",
    "operation.build.randomization.factor.view": "확인하다",
    "operation.build.randomization.factor.delete": "레이어 삭제",
    "operation.build.randomization.factor.edit": "레이어 설정",
    "operation.build.randomization.factor.set-toplimit": "계층화 설정",
    "operation.build.randomization.list.view-summary": "확인하다",
    "operation.build.randomization.list.upload": "업로드",
    "operation.build.randomization.list.generate": "생성하다",
    "operation.build.randomization.list.active": "임의 목록 사용/사용 안 함",
    "operation.build.randomization.list.export": "내보내다",
    "operation.build.randomization.list.invalid": "무효의",
    "operation.build.randomization.list.edit": "Edit",
    "operation.build.randomization.list.segmentation.view": "확인하다",
    "operation.build.randomization.list.segmentation.clear": "다른 레이어 지우기",
    "operation.build.randomization.list.segmentation.site": "중심에 블록 할당",
    "operation.build.randomization.list.segmentation.factor": "계층에 블록 할당",
    "operation.build.randomization.list.history": "과정",
    "operation.build.randomization.list.attribute": "확인하다",
    "operation.build.randomization.factor-in.view": "확인하다",
    "operation.build.randomization.factor-in.set-people": "인원수를 설정하다",
    "operation.build.randomization.form.add": "새로운",
    "operation.build.randomization.form.delete": "삭제",
    "operation.build.randomization.form.edit": "편집하다",
    "operation.build.randomization.form.list": "확인하다",
    "operation.build.medicine.visit.add": "새로운",
    "operation.build.medicine.visit.delete": "삭제",
    "operation.build.medicine.visit.edit": "편집하다",
    "operation.build.medicine.visit.list": "확인하다",
    "operation.build.medicine.configuration.add": "새로운",
    "operation.build.medicine.configuration.delete": "삭제",
    "operation.build.medicine.configuration.edit": "편집하다",
    "operation.build.medicine.configuration.list": "확인하다",
    "operation.build.medicine.upload.list": "확인하다",
    "operation.build.medicine.upload.upload": "업로드",
    "operation.build.medicine.upload.downdata": "데이터 다운로드",
    "operation.build.medicine.packlist.upload": "포장 목록 업로드",
    "operation.build.medicine.package.setting": "Setting",
    "operation.build.medicine.examine": "审核",
    "operation.build.medicine.update": "修改",
    "operation.build.medicine.release": "放行",
    "operation.build.medicine.upload.delete": "일괄 삭제",
    "operation.build.medicine.upload.uploadHistory": "과정",
    "operation.build.medicine.otherm.add": "새로운",
    "operation.build.medicine.otherm.delete": "삭제",
    "operation.build.medicine.otherm.edit": "편집하다",
    "operation.build.medicine.otherm.list": "확인하다",
    "operation.build.medicine.batch.list": "확인하다",
    "operation.build.medicine.batch.edit": "편집하다",
    "operation.build.medicine.barcode.view": "확인하다",
    "operation.build.medicine.barcode.add": "바코드 생성",
    "operation.build.medicine.barcode.scan": "코드를 스캔하여 창고에 입장하세요",
    "operation.build.medicine.barcode.scanPackage": "Package Scanning",
    "operation.build.medicine.barcode.export": "Export",
    "operation.build.supply-plan.add": "새로운",
    "operation.build.supply-plan.delete": "삭제",
    "operation.build.supply-plan.edit": "편집하다",
    "operation.build.supply-plan.view": "확인하다",
    "operation.build.supply-plan.history": "과정",
    "operation.build.supply-plan.medicine.add": "새로운",
    "operation.build.supply-plan.medicine.delete": "삭제",
    "operation.build.supply-plan.medicine.edit": "편집하다",
    "operation.build.supply-plan.medicine.view": "확인하다",
    "operation.build.supply-plan.medicine.history": "과정",
    "operation.build.history.view": "확인하다",
    "operation.build.history.print": "인쇄",
    "operation.build.push.view": "확인하다",
    "operation.build.push.all.send": "All",
    "operation.build.push.batch.send": "Batch Send",
    "operation.build.push.send": "Resend",
    "operation.build.push.details": "Details",
    "operation.build.settings.notice.view": "확인하다",
    "operation.build.settings.notice.edit": "편집하다",
    "operation.build.settings.user.view": "확인하다",
    "operation.build.settings.user.edit": "운영 관리",
    "operation.build.settings.user.download": "데이터 다운로드",
    "operation.monitor.view": "확인하다",
    "operation.monitor.edit": "관리하다",
    "operation.build.randomization.info.view": "보기",
    "operation.build.randomization.info.export": "내보내기",
    //---------------------------------权限部分----------------------------------------
    "projects.research.attribute": "연구 속성",
    "projects.contact.information": "연락처",
    "projects.research.site": "센터 제공 관리",
    "projects.research.dtp": "환자용 직접(DTP)",
    "projects.research.site.describe": "센터 납품 관리: 연구 제품 발급은 임상센터에서 관리하고 수험자에게 납품한다.",
    "projects.research.dtp.describe":
        "환자를 직접 대상(DTP): 연구 제품은 창고에서 수험자에게 직접 배포되며 임상센터 관리를 거치지 않아도 됩니다.",
    "projects.status.progress.describe": "진행 중: 프로젝트가 시작되었고 실행 중입니다.",
    "projects.status.finish.describe": "완료됨: 프로젝트의 모든 그룹 가입 작업이 완료되었습니다.",
    "projects.status.close.describe": "닫힘: 프로젝트 전체가 완료되고 프로젝트 관리 심사를 거쳐 닫힙니다.",
    "projects.status.pause.describe": "일시 중지됨: 예외가 발생하여 항목을 일시 중지해야 합니다.",
    "projects.status.terminate.describe": "종료됨: 예외로 인해 프로젝트를 종료해야 합니다.",

    "projects.customer": "소속 고객",
    "projects.attribute": "항목 등록 정보",
    "projects.brief": "프로젝트 소개",
    "placeholder.select.common": "선택하십시오.",
    "placeholder.select.common.email.language": "Please Select Email Language",
    "placeholder.select.search": "Please select or enter a search",
    "placeholder.input.common": "입력하십시오.",
    "placeholder.input.order": "Please Enter Order No.",
    "placeholder.input.contact": "请输入，여러 개의 번호는 영문 쉼표로 구분할 수 있다",
    "input.error.common": "输入错误，다시 입력하십시오.",
    "model.note.title.common": "주의하세요!",
    "model.note.content.research":
        "환자용 직접 저장(DTP)을 선택하고 저장하면 수정할 수 없습니다. 신중하게 구성하십시오.",
    "model.note.title.edc": "버트 EDC 켜시겠습니까?",
    "model.note.start.success": "성공적으로 시작됨",
    "model.note.content.edc": "오픈 후 변경할 수 없습니다.",
    "model.note.title.medicine.use": "사용 가능으로 설정하시겠습니까?",
    "model.note.title.medicine.lose": "분실/폐기를 확인하시겠습니까?",
    "model.note.title.order.dtp.receive": "영수증 확인?",
    "model.note.content.order.dtp.receive1": "현재 주문은 배송 중입니다.",
    "model.note.content.order.dtp.receive2": "수령 후 배송 상태가 더 이상 조작되지 않습니다. ",
    "button.update": "수정",
    "message.save.success": "저장 성공",
    "tool.tip.timezone":
        "적용: 프로젝트 센터는 한 시간대 내에 외부와 연결할 때의 시간 계산과 사용자 시스템의 시간 계산에 적용된다.",
    "tool.tip.edc": "EDC를 켜면 동기화 검토에서 작동 데이터를 트리거할 수 있으며 켜면 닫을 수 없습니다.",
    "tool.tip.elearning": "Once enabled, it can configure the project dimension requirements for users to learn eLearning courses. ",
    "tool.tip.elearning.yes.tp": "Yes: The user must complete the learning course before entering the project work.",
    "tool.tip.elearning.no.tp": "No: You can click to ignore the learning process and enter the project work.",
    "tool.tip.elearning.config": "Configurable project dimensions require users to learn eLearning courses.",
    "tool.tip.elearning.config.yes":
        "Yes:Users are required to complete the course before entering the system/project work.",
    "tool.tip.elearning.config.no": "No:Click to ignore learning process and enter the system/project work.",
    "suppy.drug.order.visitNumber": "방문 번호",
    "input.error.only.number.letter": "입력 오류, 1-10자리 숫자/문자 입력",
    "project.setting.divider.unblind.control": "눈가림 해제 제어",
    "project.setting.switch.unblind.control": "눈가림 해제 제어",
    "project.setting.unblind.method": "제어 방법",
    "project.setting.checkbox.approval": "승인 확인",
    "project.setting.checkbox.unblinded-code": "블라인드되지 않은 코드",
    "tool.tip.unblind.control":
        "After open, emergency unblinding /PV unblinding is allowed to be continued after approval is succeed or unblinding code is correct.",
    "project.setting.divider.approval.control": "Site Order Application",
    "project.setting.switch.approval.control": "Approval control",
    "project.setting.approval.method": "Approval mode",
    "tool.tip.approval.control":
        "After open, blinded roles in the blinded project need to be allowed to creat orders after approval is succeed.",
    "project.setting.unblind.confirm": "확인 방법",
    "project.setting.checkbox.unblind.sms": "SMS",
    "project.setting.checkbox.unblind.process": "프로세스 작업",
    "project.setting.msg.unblind.sms":
        "SMS 승인은 중국 본토의 운영자만 지원하며 역할 사용자는 유효한 휴대전화 번호(+86)를 구성해야 합니다",
    "validator.msg.required": "필수",
    "validator.msg.number": "Must be an integer between 0 and 999",
    "modal.title.user.role": "역할 유지 관리",
    "form.label.unblinding.code.available": "사용 가능",
    "form.label.unblinding.code.used": "중고",
    "form.label.unblinding.code.indivual": "아",
    "tag.copy": "복사",
    "subject.urgentUnblindingApproval.reason.other": "기타",
    "subject.urgentUnblindingApproval.reason.sae": "SAE",
    "subject.urgentUnblindingApproval.reason.pregnancy": "임신",
    "subject.urgentUnblindingApproval.reason.policy": "정책 요구 사항",
    "subject.urgentUnblindingApproval.pending": "승인 보류 중",
    "subject.urgentUnblindingApproval.agree": "승인됨",
    "subject.urgentUnblindingApproval.reject": "거부됨",
    "subject.unblinding.reason.remark": "비고",
    "subject.unblinding.application": "눈가림 해제 응용 프로그램",
    "subject.unblinding.approval": "맹목 해제 승인",
    "subject.unblinding.application.alert":
        "약품의 실제 배포가 시스템의 무작위 배포와 일치하지 않는 경우 시간 내에 등록하십시오",
    "subject.unblinding.application.type": "응용 방법",
    "subject.unblinding.unblinded.code.confirm": "블라인드 해제 코드 확인",
    "subject.unblinding.application.result.title": "눈가림 해제(긴급) 신청",
    "subject.unblinding.application.result.title.pv": "Pv Unblinding application",
    "subject.unblinding.approval.result.title": "눈가림 해제(긴급) 승인",
    "subject.unblinding.application.success": "응용 프로그램 성공",
    "subject.unblinding.approval.success": "승인 성공",
    "subject.unblinding.application.info":
        "맹검 해제(긴급) 신청서가 성공적으로 제출되었습니다. 승인자의 승인을 기다리십시오.",
    "subject.unblinding.application.info.pv":
        "The pv unblinding application has been submitted successfully, please wait for the approval of the approver.",
    "subject.unblinding.success": "눈가림 해제 성공",
    "subject.unblinding.approval.number": "승인 번호",
    "subject.unblinding.approval.reason": "이유",
    "subject.unblinding.approval.agree": "통과",
    "subject.unblinding.approval.reject": "거부",
    "subject.unblinding.application.resend": "SMS 보내기",
    "subject.edc.verification.factor": "Stratification Factor",
    "subject.edc.verification.source": "Data Source",
    "subject.edc.verification.return": "EDC Return",
    "subject.edc.verification.enter": "IRT Entry",
    "subject.edc.return.modification": "Back to revise",
    "subject.edc.continue.submitting": "Continue to submit",
    "subject.edc.continue.replace": "Continue to replace",
    "subject.edc.continue.register": "Continue Registration",
    "subject.edc.continue.update": "Continue Edit",
    "subject.edc.confirm.lamination1": "EDC返回分层/表单信息为空，需确认分层/表单信息录入是否正确。",
    "subject.edc.confirm.lamination2": "EDC返回分层信息为空，需确认分层信息录入是否正确。",
    "subject.edc.confirm.lamination3": "EDC返回表单信息为空，需确认表单信息录入是否正确。",
    "subject.edc.interface.error": "can not check subject status from EDC",
    "subject.edc.interface.site.error": "can not check site from EDC",
    "subject.edc.inconsistent.information": "IRT entry of stratification information is not consistent with the EDC system return, need to confirm the entry of stratification information is correct or not.",
    "subject.edc.random.failure.filter.failed": "System checks the status of the subject is screening failure and does not allow further randomization.",
    "subject.edc.random.failure.screen.failed": "System checks the status of the subject is screening and does not allow further randomization.",
    "subject.edc.random.failure.exit": "System checks the status of the subject is exited and does not allow further randomization.",
    "subject.edc.random.failure.complete.the.study": "System checks the status of the subject is complete study and does not allow further randomization.",
    "subject.edc.dispensing.failure.filter.failed": "system checks the status of the subject is screening failure and does not allow further dispensing.",
    "subject.edc.dispensing.failure.exit": "Subject status is exited, dispensing is not allowed.",
    "subject.edc.dispensing.failure.complete.the.study": "system checks the status of the subject is complete study and does not allow further dispensing.",
    "subject.edc.replace.inconsistent.information": "IRT entry of replace subject stratification information is not consistent with the EDC system return, need to confirm the entry of stratification information is correct or not.",
    "subject.edc.replace.register.information1": "The information entered in IRT for stratification/form does not match the information returned by the EDC system, please confirm the stratification/form information entry is correct or not.",
    "subject.edc.replace.register.information2": "The information entered in IRT for stratification does not match the information returned by the EDC system, please confirm the stratification information entry is correct or not.",
    "subject.edc.replace.register.information3": "The information entered in IRT for form does not match the information returned by the EDC system, please confirm the form information entry is correct or not.",
    "subject.edc.register.no.subject": "The subject is not in the EDC system, it will automatically create the subject in the EDC system if continuing registration. ",
    "subject.edc.update.no.subject": "The subject is not in the EDC system, it will automatically create the subject in the EDC system if continuing update. ",
    "subject.edc.update.handle.subject": "The EDC system will complete the data write operation later if the modification continues while the EDC system data is being processed.",
    "subject.edc.update.cover.subject": "This subject data of EDC system  processing, if continue to modify, the EDC system will later complete the data overwrite operation.",
    "subject.edc.cover.tip": "The operation is successful and the EDC system will automatically complete the data overwrite after the lRT system data is pushed.",
    "subject.edc.no.mapping": "There is no mapping rule configured in the EDC system, IRT will not push data.",
    "subject.edc.complete.registration": "Subject：{subjectNo}，already exists in the EDC system and consistent with the IRT system. Continuing registration will complete subject registration in the IRT system.",
    "subject.edc.complete.cohort.registration": "Subject:{subjectNo}, group:{cohortName}, already exists in the EDC system and is not consistent with the IRT system. Continuing registration will complete subject registration in the IRT system.",
    "subject.edc.complete.cohort.update": "Subject:{subjectNo}, cohort:{cohortName}, already exists in the EDC system and is not consistent with the IRT system. Continuing update will complete subject update in the IRT system.",
    "subject.edc.complete.cohort.random": "Subject:{subjectNo}, cohort:{cohortName}, already exists in the EDC system and is not consistent with the IRT system. Continuing randomize will complete subject randomize in the IRT system.",
    "subject.edc.create.subject": "EDC did not create a subject and the returned information was empty.",
    "subject.edc.replace.error": "Replace failed",
    "subject.edc.replace.failure.filter.failed": "System checks the status of the subject is screening failure and does not allow further replace.",
    "subject.edc.replace.failure.exit": "System checks the status of the subject is exited and does not allow further replace.",
    "subject.edc.replace.failure.complete.the.study": "System checks the status of the subject is complete study and does not allow further replace.",
    "subject.edc.replace.failure.screen.no.random": "System checks the status of the subject is screening and does not allow further replace.",
    "subject.edc.site.empty": "The site information retrieval of EDC has failed, please manually determine if the current subject is consistent between IRT and EDC sites.",
    "subject.edc.site.inconsistent": "The site information returned by EDC does not match the site where the subject is located. Please confirm if the site information in the IRT is correct.",
    "tip.site.disable": "Is it confirmed to invalid? After invalid, site binding users will automatic inactive.",
    "tip.user.disable": "After delete,site/depot and other data will automatic unbind",
    "toast.copy.success": "Copy succeeded",

    "order.logistics.sf": "顺丰",
    "order.logistics.ems": "EMS",
    "order.logistics.jd": "京东",
    "order.logistics.yt": "圆通",
    "order.logistics.yd": "韵达",
    "order.logistics.zt": "中通",
    "order.logistics.st": "申通",
    "order.logistics.jt": "极兔",
    "order.logistics.other": "其他",

    "logistics.confirm.deliver": "确认运送",
    "logistics.other.vendor": "其他物流",
    "logistics.number": "物流单号",
    "logistics.info": "物流",
    "logistics.dispensing.dtpIp": "DTP IP",
    "logistics.dispensing.method": "发药方式",
    "logistics.send.site": "中心（中心库存）",
    "logistics.send.site.info": "研究产品从中心发放，受试者去中心拿取。",
    "logistics.send.site.subject": "中心（直接寄送受试者）",
    "logistics.send.site.subject.info": "中心将研究产品采用快递的方式，直接寄送至受试者。",
    "logistics.send.depot.subject": "库房（直接寄送受试者）",
    "logistics.send.depot.subject.info": "库房将研究产品采用快递的方式，直接寄送至受试者。",

    "supply.plan.applicable.site": "Applicable Site",
    "supply.plan.all.site": "All Site",
    "supply.plan.status.invalid.title": "是否确认无效化供应计划？",
    "supply.plan.status.invalid.content": "中心已关联供应计划，无效后，中心将无对应供应计划。",
    "supply.plan.status.applicable.site.title": "是否确认修改？",
    "supply.plan.status.applicable.site.content": "系统检测到部分中心无绑定的供应计划。",
    "supply.plan.current": "当前供应计划",
    "storehouse.alert": "Alert Value",
    "permission.scope.study": "Study:项目/试验维度的数据权限角色",
    "permission.scope.depot": "Depot:库房维度的数据权限角色",
    "permission.scope.site": "Site:研究中心维度 的数据权限角色",
    "project.role.invalid.title": "是否确认无效角色？",
    "project.role.invalid.content": "无效后，将自动取消已分配用户的角色使用。",

    "project.statistics.mode": "数据推送方式",
    "project.statistics.select.mode": "请选择数据推送方式",
    "project.statistics.real": "EDC实时请求",
    "project.statistics.active": "IRT全量推送",
    "project.statistics.mode.real.tp": "EDC实时请求：随机/发放请求由EDC发起，实时请求IRT完成后，数据进行传输；",
    "project.statistics.mode.active.tp": "IRT全量推送：EDC与IRT均可登记受试者，在IRT完成随机/发放操作后，IRT实时推送数据给EDC，IRT并可追溯推送过程日志。",
    "project.statistics.url": "请输入EDC的URL地址",
    "project.statistics.folder.oid": "文件夹OID",
    "project.statistics.form.oid": "表单OID",
    "project.statistics.field.oid": "字段OID",
    "project.statistics.dispensing.mapping": "EDC发药数据映射",
    "project.statistics.randomize.mapping": "EDC随机数据映射",
    "project.statistics.randomize.number.oid": "随机号OID",
    "project.statistics.randomize.group.oid": "随机组别OID",
    "project.statistics.randomize.time.oid": "随机时间OID",
    "project.statistics.dispensing.number.oid": "发药药物OID",
    "project.statistics.dispensing.time.oid": "发药时间OID",
    "project.statistics.mapping": "EDC映射",
    "project.statistics.all": "全部推送",
    "project.statistics.pushing": "推送中",
    "project.statistics.succeeded": "成功",
    "project.statistics.failed": "推送失败",
    "project.statistics.lose": "无效",
    "project.received.failed": "入库失败",
    "project.edc.failed": "失败",
    "project.edc.processing": "处理中",
    "project.edc.return": "EDC返回",
    "project.statistics.enter.number": "请输入受试者号",
    "project.statistics.batch.send": "批量发送",
    "project.statistics.select.data": "请先勾选要发送的数据",
    "project.statistics.subject.number": "受试者号",
    "project.statistics.push.item": "推送内容",
    "project.statistics.randomize": "随机",
    "project.statistics.dispense": "发药",
    "project.statistics.push.mode": "推送方式",
    "project.statistics.manual.push": "手动推送",
    "project.statistics.system.auto": "系统自动",
    "project.statistics.push.time": "推送时间",
    "project.statistics.retry.times": "重试次数",
    "project.statistics.ls.resend": "确定重新发送吗？",
    "project.statistics.operation.succeed": "确定后，系统将在30s内推送至EDC。",
    "project.statistics.resend": "重新发送",
    "project.statistics.details": "详情",
    "project.statistics.randomization.number": "随机号",
    "project.statistics.group": "组别",
    "project.statistics.randomization.time": "随机时间",
    "project.statistics.return.edc": "EDC返回",
    "project.statistics.visit.number": "访视号",
    "project.statistics.dispensing.time": "发药时间",
    "project.statistics.registered": "登记",
    "project.statistics.update": "修改",
    "project.statistics.out-visit-dispensing": "访视外发药",
    "project.statistics.replace": "研究产品替换",
    "project.statistics.reissue": "补发",
    "project.statistics.cancel": "研究产品撤销",
    "project.statistics.retrieval": "研究产品取回",
    "project.statistics.realDispensing": "实际用药",
    "project.statistics.unknown": "未知",
    "project.focus.on": "仅显示关注项目",
    "project.focus.on.cancel": "取消关注",
    "project.focus.on.ok": "关注",

    "report": "Report",
    "report.role": "Object Role",
    "report.projectEnv": "Project/Environment",
    "report.latestDownloadTime": "Latest Download Time",
    "report.customTemplate": "Custom Template",
    "report.history": "History",
    "report.template": "Template",
    "report.template.name": "Template Name",
    "report.template.name.required": "Please enter a template name",
    "report.template.default": "Reference Default Template",
    "report.template.name.default": "Default Template",
    "report.attributes": "Field Library",
    "report.attributes.unit": "Items",
    "report.custom": "Custom",
    "report.filename": "Filename",
    "report.filesize": "Filesize",
    "report.exportTime": "Export Time",
    "report.filename.search.required": "Please enter the filename",
    "report.storehouse": "Depot",

    "report.randomizationStatisticsExport": "Randomization Statistics Report",
    "report.subjectStatisticsExport": "Subject Statistics Report",
    "report.siteIPStatisticsExport": "Site IP Statistics Report",
    "report.depotIPStatisticsExport": "Depot IP Statistics Report",
    "report.userLoginHistory": "User Login History",
    "report.userRoleAssignHistory": "User Role Assign History",
    "report.userRoleStatus": "User Role Status Report",
    "report.auditTrailExport": "Audit Trail",
    "report.projectPermissionConfigurationExport": "Project Permission Configuration Report",
    "report.configureReport": "Configure Report",
    "report.sourceIPExport": "Source IP",
    "report.sourceRandomizationListExport": "Source Randomization List Report",
    "report.randomizationSimulationReport": "Randomization Simulation Report",
    "report.randomizeReport": "Subject Detail Report",
    "report.dispenseReport": "Dispense Report",
    "report.unblindingReport": "Unblinding Report",
    "report.roomNumberViewHistory": "Room Number View History",
    "report.depotItemReport": "Depot Item Report",
    "report.siteItemReport": "Site Item Report",
    "report.shipmentOrdersReport": "Shipment Orders Report",
    "report.returnOrdersReport": "Return Orders Report",
    "report.randomizationSimulationResult": "Randomization Simulation Result",
    "report.site.warning": "No Site Selected",
    "report.role.warning": "No Role Selected",
    "report.template.all": "All Templates",
    "report.template.delete.confirm": "Confirm Delete?",
    "report.template.create.success": "Create Successfully",
    "report.template.edit.success": "Edit Successfully",

    "report.attributes.project": "Project",
    "report.attributes.project.number": "Project Number",
    "report.attributes.project.name": "Project Name",
    "report.attributes.info": "Basic Information",
    "report.attributes.info.user.name": "Name",
    "report.attributes.info.user.email": "Email",
    "report.attributes.info.user.role": "Role",
    "report.attributes.info.country": "Country (Stratification Property)",
    "report.attributes.info.site.number": "Site Number",
    "report.attributes.info.site.name": "Site Name",
    "report.attributes.info.site.country": "Country",
    "report.attributes.info.site.region": "Region",
    "report.attributes.info.subject.number": "Subject Number",
    "report.attributes.info.status": "Status",
    "report.attributes.info.storehouse.name": "Depots",
    "report.attributes.random": "Subject Randomization",
    "report.attributes.random.factor": "Stratification Factor",
    "report.attributes.random.time": "Randomization Time",
    "report.attributes.random.group": "Group(randomized by system)",
    "report.attributes.random.subject.number.replace": "Replace subject number",
    "report.attributes.random.number": "Randomization Number",
    "report.attributes.random.register.time": "Register Time",
    "report.attributes.random.register.operator": "Register Operator",
    "report.attributes.random.operator": "Randomization Operator",
    "report.attributes.random.form": "Form",
    "report.attributes.random.subject.replace.status": "Replace Subject Status",
    "report.attributes.random.cohort": "Cohort/Stage",
    "report.attributes.random.subject.replace.time": "Replace Time",
    "report.attributes.random.subject.replace.number": "Replace randomization number",
    "report.attributes.random.config.code": "Group Code",
    "report.attributes.dispensing": "Subject dispensing",
    "report.attributes.dispensing.room": "Room Number",
    "report.attributes.dispensing.cycle.name": "Visit Name",
    "report.attributes.dispensing.type": "Dispensing Type",
    "report.attributes.dispensing.time": "Operation Time",
    "report.attributes.dispensing.medicine": "IP Number",
    "report.attributes.dispensing.drug.name": "IP Name",
    "report.attributes.dispensing.label": "Dispensation IP label",
    "report.attributes.dispensing.medicine.replace": "Is Replaced IP",
    "report.attributes.dispensing.medicine.real": "Is Actual Use of IP",
    "report.attributes.dispensing.drug.other.number": "Unnumbered IP Number",
    "report.attributes.dispensing.useFormulas": "Formula Calculation Form",
    "report.attributes.dispensing.operator": "Dispensing operator",
    "report.attributes.dispensing.remark": "Dispensing Remark",
    "report.attributes.dispensing.out-visit-dispensing.reason": "Unscheduled Dispensation Reason",
    "report.attributes.dispensing.reissue.reason": "Reissue reason",
    "report.attributes.dispensing.medicine.real.number": "Actually Used IP Number",
    "report.attributes.unblinding": "Unblinding",
    "report.attributes.unblinding.sponsor": "Has Sponsor Been Notified or Not",
    "report.attributes.unblinding.mark": "Unblinding Remark",
    "report.attributes.unblinding.reason": "Unblinding Reason",
    "report.attributes.unblinding.reason.mark": "Unblinding Reason Remark",
    "report.attributes.unblinding.operator": "Unblinding Operator",
    "report.attributes.unblinding.time": "Unblinding Operate Time",
    "report.attributes.research": "IP",
    "report.attributes.research.medicine.serial-number": "Serial Number",
    "report.attributes.research.medicine.number": "IP Number",
    "report.attributes.research.medicine.code": "Short Code",
    "report.attributes.research.medicine.type": "Operation Type",
    "report.attributes.research.medicine.name": "IP Name",
    "report.attributes.research.batch": "Batch Number",
    "report.attributes.research.expireDate": "Expiration date",
    "report.attributes.research.packageNumber": "Package Number",
    "report.attributes.research.package.serialNumber": "The package sequence number",
    "report.attributes.research.place": "Location",
    "report.attributes.research.order.number": "Order Number",
    "report.attributes.research.status": "Status",
    "report.attributes.research.freeze.reason": "Freeze Reason",
    "report.attributes.research.freeze.operator": "Freeze Operator",
    "report.attributes.research.release.reason": "Release reason",
    "report.attributes.research.release.operator": "Release Operator",
    "report.attributes.research.lost.reason": "Lost/Void Reason",
    "report.attributes.research.lost.operator": "Lost/Void Operator",
    "report.attributes.research.lost.time": "Lost/Void Time",
    "report.attributes.research.use.reason": "Make Available Reason",
    "report.attributes.research.use.operator": "Make Available Operator",
    "report.attributes.research.use.time": "Make Available Time",
    "report.attributes.research.other": "Number",
    "report.attributes.order": "Shipment Orders",
    "report.attributes.order.detail": "Shipment Orders Details",
    "report.attributes.order.number": "Order Number",
    "report.attributes.order.status": "Status",
    "report.attributes.order.send": "Origin",
    "report.attributes.order.receive": "Destination",
    "report.attributes.order.medicineQuantity": "IP Quantity",
    "report.attributes.order.create.by": "Created By",
    "report.attributes.order.create.time": "Created Time",
    "report.attributes.order.cancel.by": "Canceller",
    "report.attributes.order.cancel.time": "Canceller Time",
    "report.attributes.order.cancel.reason": "Canceller Reason",
    "report.attributes.order.confirm.by": "Confirm By",
    "report.attributes.order.confirm.time": "Confirm Time",
    "report.attributes.order.close.by": "Closed By",
    "report.attributes.order.close.time": "Closed Time",
    "report.attributes.order.close.reason": "Closed Reason",
    "report.attributes.order.send.by": "Delivery By",
    "report.attributes.order.send.time": "Delivery time",
    "report.attributes.order.receive.by": "Receiver",
    "report.attributes.order.receive.time": "Receive Time",
    "report.attributes.order.lost.by": "Lost By",
    "report.attributes.order.lost.time": "Lost Time",
    "report.attributes.order.lost.reason": "Lost Reason",
    "report.attributes.order.end.by": "Terminated By",
    "report.attributes.order.end.time": "Terminated Time",
    "report.attributes.order.end.reason": "Terminated Reason",
    "report.attributes.order.supplier": "Logistics Vendor",
    "report.attributes.order.supplier.other": "Other Vendor",
    "report.attributes.order.supplier.number": "Logistics Number",
    "report.attributes.order.expectedArrivalTime": "Expected Arrival Time",
    "report.attributes.order.actualReceiptTime": "Actual Receipt Time",


    "report.download.success": "The download is successful, and downloading again is supported in the history.",

    "project.overview.carried.out": "진행 중인 프로젝트",
    "project.overview.still.far.from.the.end": "아직 끝이 멀었어",
    "project.overview.year": "년도",
    "project.overview.month": "달",
    "project.overview.day": "하늘",

    "project.overview.time": "프로젝트 시간",
    "project.overview.site.number": "총 센터 수",
    "project.overview.random.number": "임의 / 예약된 환자 수",

    "operation.settings.users.system.administrator": "시스템 관리자",
    "common.export.success": "내보내기 성공",


    "notice.exclude_recipient_list.email": "排除收件人",

    "week.all": "Every day",
    "week.monday": "Mon",
    "week.tuesday": "Tue",
    "week.wednesday": "Wed",
    "week.thursday": "Thu",
    "week.friday": "Fri",
    "week.saturday": "Sat",
    "week.sunday": "Sun",
    "time.hour": "Hour",
    "time.minute": "Minute",
    
};
