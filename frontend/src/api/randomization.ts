import {del, get, patch, post} from "utils/http";

export const getRandomization = (params: object = {}) => get(`randomization/configure`, { params });
export const addRandomization = (data: object = {}) => post("randomization/configure", { data });
export const delRandomizationDesignGroup = (data: object = {}) => del("randomization/random-design/group", { data });
export const updateSiteLayered = (data: object = {}) => post("randomization/attribute/site/layered", { data });
export const delRandomizationDesignFactor = (data: object = {}) => del("randomization/random-design/factor", { data });
export const updateRandomListStatus = (data: object = {}) =>
    patch("randomization/customers/project/env/random/update/status", { data });
export const getProjectAttribute = (params: object = {}) => get("randomization/attribute", { params });
export const getRandomizationType = (params: object = {}) => get("randomization/attribute/randomization/type", { params });
export const getProjectAttributes = (params: object = {}) => get("randomization/attributes", { params });
export const getRandomList = (params: object = {}) => get("randomization/random/list", { params });
export const updateRandomListInvalid = (data: object = {}) =>
    patch("randomization/customers/project/env/random/update/invalid", { data });
export const downloadRandomList = (params: object = {}) => get("randomization/random-list", { params });
export const getLastGroup = (params: object = {}) => get("randomization/random/list/last", { params });
export const downloadTemplate = (params: object = {}) => get("/randomization/template-file", { params });
export const uploadRandomizationFiles = (data: FormData) => post("/randomization/file", { data });
export const generateRandomNumber = (data: object = {}) => post("/randomization/random/generate", { data });
export const randomListUpdate = (data: object = {}) => post("/randomization/random/list/update", { data });
export const updateRandomization = (data: object = {}) => patch("/randomization/configure", { data });
export const getRandomNumberGroup = (params: object = {}, data: object = {}) =>
    post("/randomization/blocks", { params, data });
export const cleanFactor = (params: object = {}) => del("/randomization/random-list/block", { params });
export const getFactor = (params: object = {}) => get("/randomization/random-list/factor", { params });
export const getRandomListConfigure = (params: object = {}) => get("/randomization/random/configure", { params });
export const getBlock = (data: object = {}) => post("/randomization/block/project-site", { data });
export const getProjectSite = (params: object = {}) => get("/randomization/project-site", { params });
export const updateProjectSite = (params: object = {}, data: object = {}) =>
    patch("/randomization/random-list", { params, data });
export const getFactorCombination = (params: object = {}) =>
    get("randomization/random-list/factor/combination", { params });
export const updateFactor = (params: object = {}, data: object = {}) =>
    patch("randomization/random-list/factor", { params, data });
export const addFactor = (params: object = {}, data: object = {}) => post("randomization/random-list/factor", { params, data });
export const delFactor = (params: object = {}) => del("randomization/random-list/delFactor", { params });
export const updateProjectAttribute = (data: object = {}) => post("randomization/attribute", { data });
export const downloadReport = (params: object = {}) => get("randomization/report", { params });
export const getRandomStatisticsPie = (params: object = {}) => get("randomization/random-statistics/pie", { params });
export const getRandomStatisticsBar = (params: object = {}) => get("randomization/random-statistics/bar", { params });
export const getRandomListMany = (data: object = {}) => post("randomization/random-list/many", { data });
export const getProjectAttributeList = (params: object = {}) => get("randomization/attribute/list", { params });
export const getRegion = (params: object = {}) => get("randomization/region", { params });
export const addRegion = (data: object = {}) => post("randomization/region", { data });
export const delRegion = (data: object = {}) => del("randomization/region", { data });
export const randomListSync = (params: object = {}) => post("randomization/random-list/sync", { params });

export const getProjectAttributeConnect = (params: object = {}) => get("randomization/attribute/connect", { params });

export const getProjectAttributeConnectAli = (params: object = {}) => get("randomization/attribute/connect/ali", { params });
export const getProjectCohortRandom = (params: object = {}) => get("randomization/cohort/random", { params });
export const randomListInactivate = (data: object = {}) => post("randomization/random-list/inactivate", { data });
export const activateInactivate = (data: object = {}) => post("randomization/random-list/blocks/activate-inactivate", { data });

export const getGroupDistributionStatistics = (params: object = {}, data: object = {}) => post("/randomization/blocks/distribution-statistics", { params, data });

