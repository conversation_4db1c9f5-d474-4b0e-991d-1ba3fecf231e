import {get, patch, post} from "utils/http";

export const verify = (data: { token: string | undefined }) => post('/users/verify', { data });
export const customers = (params: object = {}) => get('/users/customers', { params });
export const getUserProjectEnvironmentRoles = (params: object = {}) => get('/users/roles', { params });
export const getLearn = (params: object = {}) => get('/users/learn', { params });
export const listUserRoles = (params: object = {}) => get('/users/roles/all', { params });
export const updateUserStorehouses = (params: object = {}, data: object = {}) => post('users/customers/projects/envs/storehouses', { params, data });
export const updateUserSites = (params: object = {}, data: object = {}) => post('users/customers/projects/envs/sites', { params, data });
export const updateUser = (params: object = {}, data: object = {}) => patch('users', { params, data });
export const addUser = (params: object = {}, data: object = {}) => post('customers/users', { params, data });
export const updateUserCustomerAdmin = (params: object = {}, data: object = {}) => patch('/customers/users/admin', { params, data });
export const closetCustomerUser = (params: object = {}) => post('/customers/users/close', { params});
export const batchCloseCustomerUser = (params: object = {}, data: object = {}) => post('/customers/users/batch-close', { params, data });
export const inviteAgainCustomerUser = (params: object = {}) => post('/customers/users/invite-again', { params});
export const setUserRoles = (params: object = {}, data: object = {}) => patch('/users/roles', { params, data });
export const getMainRolePermission = ( params: object = {}) => get('/users/main-page-permission', { params });
export const getProjectApi=(keyword: string) => get(`/users/project`,{params:{keyword:keyword}})

export const update = (params: object = {}, data: object = {}) => patch('/users/update-user', { params, data });


export const updateStatus = (params: object = {}) => post('/users/update-user-status', { params });

export const addBatchUser = (params: object = {}, data: object = {}) => post('customers/batch-users', { params, data });
export const addBatchUserVerify = (params: object = {}, data: object = {}) => post('customers/batch-users-verify', { params, data });
export const setUsersRoles = (params: object = {}, data: object = {}) => post('customers/batch-users-roles', { params, data });

export const updateUserRolesSitesDepots = (params: object = {}, data: object = {}) => post('users/customers/projects/envs/roles-sites-depots', { params, data });


export const getUserUnbindRoles = (params: object = {}, data: object = {}) => post('users/unbind-roles', { params, data });

export const getProjectEnvironmentRolesPermissions = (params: object = {}) => get('/users/roles/permissions', { params });

export const getUserInfoFromCloud = (params: object = {}) => get('/users/info-from-cloud', { params });