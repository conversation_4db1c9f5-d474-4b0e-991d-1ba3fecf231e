import { del, get, patch, post } from "utils/http";

export const getMedicineList = (params: object = {}) => get(`/medicines`, { params })
export const getMedicineFreezeList = (params: object = {}) => get(`/medicines/medicine-freeze`, { params })
export const medicineFreezeDetailsList = (params: object = {}) => get(`/medicines/medicine-freeze-details`, { params })
export const getBatchList = (params: object = {}) => get(`/medicines/batch`, { params })
export const getBatchGroupStatus = (params: object = {}) => get(`/medicines/batch/group/status`, { params })
export const getBatchGroupStatusMult = (data: object = {}) => patch(`/medicines/batch/group/mult`, { data })
export const getMedicineBatchList = (data: object = {}) => post(`/medicines/medicine-batch`, { data })
export const getMedicineForOrder = (data: object = {}) => patch(`/medicines/medicines-for-order`, { data })
export const downloadTemplate = (params: object = {}) => get(`/medicines/template-file`, { params })
export const downloadPacklistTemplate = (params: object = {}) => get(`/medicines/packlist-template-file`, { params })
export const downloadData = (params: object = {}) => get(`/medicines/download-data`, { params })
export const medicineSiteSku = (data: object = {}) => post(`/medicines/site-sku`, { data })
export const medicineDtpSku = (params: object = {}) => get(`/medicines/dtp-sku`, { params })
export const otherMedicineSiteSku = (data: object = {}) => post(`/medicines/site-other-sku`, { data })
export const otherMedicineSiteSkuWithDTP = (params: object = {}) => get(`/medicines/site-other-sku-dtp`, { params })
export const medicineStorehouseSku = (data: object = {}) => post(`/medicines/storehouse-sku`, { data })
export const otherMedicineStorehouseSku = (data: object = {}) => post(`/medicines/storehouse-other-sku`, { data })
export const allMedicineStorehouseSTAT = (params: object = {}) => get(`/medicines/all-medicine-storehouse-stat`, { params })
export const allMedicineSiteSTAT = (params: object = {}) => get(`/medicines/all-medicine-site-stat`, { params })
export const downloadStorehouseSku = (params: object = {}) => get(`/medicines/download-storehouse-sku`, { params })
export const downloadSiteSku = (params: object = {}) => get(`/medicines/download-site-sku`, { params })
export const downloadDtpSku = (params: object = {}) => get(`/medicines/download-dtp-sku`, { params })
export const uploadMedicines = (data: FormData) => post(`/medicines/upload`, { data })
export const uploadMedicinesPacklist = (data: FormData) => post(`/medicines/upload-packlist`, { data })
export const updateBatch = (data: object = {}) => patch(`/medicines/update-batch`, { data })
export const updateStatus = (data: object = {}) => patch(`/medicines/freeze-medicines`, { data })
export const freezeOtherDrug = (data: object = {}) => patch(`/medicines/freeze-other-medicines`, { data })
export const updateOtherDrug = (data: object = {}) => patch(`/medicines/other-medicines/update`, { data })
export const releaseMedicines = (data: object = {}) => patch(`/medicines/release-medicines`, { data })
export const medicineSummaryStorehouse = (data: object = {}) => post(`/medicines/medicine-summary-storehouse`, { data })
export const updateMedicineStatus = (data: object = {}) => patch(`/medicines/update-medicines`, { data })
export const medicineSummarySite = (data: object = {}) => post(`/medicines/medicine-summary-site`, { data })
export const siteMedicine = (params: object = {}, data: object = {}) => post(`/medicines/site-medicine`, { params, data })
export const deleteData = (data: object = {}) => del(`/medicines`, { data })
export const getMedicineForOrderGroup = (params: object = {}) => get(`/medicines/medicines-for-order-group`, { params })
export const getMedicineForOrderGroupAll = (data: object = {}) => patch(`/medicines/medicines-for-order-group-all`, { data })
export const getMedicineFreezeByIds = (data: object = {}) => patch("/medicines/medicine-freeze-by-ids", { data })
export const getOtherMedicineCount = (params: object = {}) => get("/medicines/other/count", { params })
export const getSiteForecast = (params: object = {}) => get("/medicines/site-forecast", { params })
export const getGroupNameDataNumber = (params: object = {}) => get("/medicines/group/name-data-number", { params })
export const toExamineFlowPath = (data: object = {}) => patch(`/medicines/to/examine/flow/path`, { data })
export const getMedicineName = (params: object = {}) => get("/medicines/get/medicine/name", { params })


export const getBatchSelectList = (data: object = {}) => post(`/medicines/batch/select`, { data })
