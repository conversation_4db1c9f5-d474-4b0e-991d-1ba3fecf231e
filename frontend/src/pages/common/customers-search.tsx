import {useEffect} from "react";
import {Form, Select,} from "antd";
import {useAuth} from "../../context/auth";
import {FormattedMessage, useIntl} from "react-intl";
import {useFetch} from "../../hooks/request";
import {customers} from "../../api/user";
import {Result} from "../../types/result";
import {useGlobal} from "../../context/global";
import React from "react";

export const CustomersSearch = ({projectAdmin,customerAdmin,left,systemAdministrator,onResetSelected }: { projectAdmin: boolean,customerAdmin: boolean, left?: number,systemAdministrator: boolean,onResetSelected: () => void }) => {
    const g = useGlobal()
    const intl = useIntl();

    const auth = useAuth();

    const onChange = (v: string | undefined) => {
        if (projectAdmin || customerAdmin) {
            auth.setAdminCustomerId(v);
        }else {
            auth.setCustomerId(v);
            if (v !== undefined) {
                sessionStorage.setItem("customerId", v)
            }
        }
        onResetSelected();

    }

    useEffect(
        () => {
                runAsync({projectAdmin,customerAdmin}).then(
                    (result: any) => {
                        const res = result as Result;
                        if (res.code === 0) {
                            if (projectAdmin || customerAdmin) {
                                auth.setAdminCustomers(res.data);
                                if (res.data && res.data.length > 0) {
                                    onChange(res.data[0].id);
                                }
                            } else {
                                auth.setCustomers(res.data);
                                if (res.data && res.data.length > 0) {
                                    onChange(res.data[0].id);
                                }
                            }
                        }
                    }
                );
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [g.lang]
    );

    const {runAsync, loading} = useFetch(customers, {manual: true});

    return (
        <>
            <Form.Item label={<span style={{color:'#1D2129'}}><FormattedMessage id="common.customer"/></span>}>
                <Select
                    // left={left} // 见SelectEmbeddeTitle组件
                    // _title={intl.formatMessage({ id: 'common.customer' })}
                    loading={loading}
                    className="full-width"
                    // containerClassName='irt-select-embedde-title'
                    placeholder={intl.formatMessage({id: 'customers.name'})}
                    showSearch
                    allowClear
                    disabled={systemAdministrator}
                    optionFilterProp="children"
                    value={!systemAdministrator?(projectAdmin || customerAdmin ? (auth.adminCustomers ? auth.adminCustomerId : null) : (auth.customers ? auth.customerId : null)):null}
                    onChange={onChange}
                    filterOption={
                        (input: string, option: any) => option.children.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0
                    }
                >
                    {
                        (projectAdmin || customerAdmin ? (auth.adminCustomers ? auth.adminCustomers : []) : (auth.customers ? auth.customers : [])).map(
                            (it: any, index: number) => (
                                <Select.Option key={index} value={it.id}>{it.name}</Select.Option>
                            )
                        )
                    }
                </Select>
            </Form.Item>
        </>
    )
};
