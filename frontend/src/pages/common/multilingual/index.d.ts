export type LocaleItemType = 'label' | 'menu'| 'tips' | 'button' | 'option' | 'placeholder' | 'warn'

export type BaseLocaleLanguage = 'cn' | 'en'

export interface LocaleItem {
    key: string,
    label: { [key in BaseLocaleLanguage]: string } | string,
    defaultLabel?: { [key in BaseLocaleLanguage]: string },
    type?: LocaleItemType,
    isTemplate?: boolean, // 是否模板类型
    path?: string[], // 路径数组
    link?: string, // 链接到公共字段
    translation?: boolean, // 是否已经翻译，context中判断状态使用
    options?: LocaleItem[], // select类型下选项
}

export interface LocaleModule {
    title: string, // 开发人员查看
    titleEn?: string,
    key: string,
    disabledEdit?: boolean, // 是否开放编辑，默认开放
    children: (LocaleItem | LocaleModule)[],
}


export interface SimpleLocaleItem {
    key: string,
    label: string,
}

export interface LocaleOption {
    value: string
    label: string
    labelEn: string
    children?: LocaleOption[]
}