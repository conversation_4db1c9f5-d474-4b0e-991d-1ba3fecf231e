export const external_docking = [
    {
        key: "projects.connectEdc",
    },
    {
        key: "tool.tip.edc",
        type: "tips",
    },
    {
        key: "projects.connectEdc.supplier",
    },
    {
        key: "project.statistics.mode",
    },
    {
        key: "project.statistics.real",
        type: "option",
    },
    {
        key: "project.statistics.active",
        type: "option",
    },
    {
        key: "project.statistics.mode.real.tp",
        type: "tips",
    },
    {
        key: "project.statistics.mode.active.tp",
        type: "tips",
    },
    {
        key: "projects.synchronization.mode",
    },
    {
        key: "projects.step.by.synchronization",
        type: "option",
    },
    {
        key: "projects.one.time.full.synchronization",
        type: "option",
    },
    {
        key: "project.statistics.url",
        type: "placeholder",
    },
    {
        key: "projects.push.rules",
    },
    {
        key: "projects.subject.uid",
        type: "option",
    },
    {
        key: "project.statistics.subject.number",
        type: "option",
    },
    {
        key: "projects.subject.no.tip",
        type: "tips",
    },
    {
        key: "projects.subject.uid.tip",
        type: "tips",
    },
    {
        key: "projects.push.scene",
    },
    {
        key: "projects.push.scene.select",
        type: "warn",
    },
    {
        key: "subject.register",
        type: "option",
    },
    {
        key: "projects.subject.update.front",
        type: "option",
    },
    {
        key: "projects.subject.update.after",
        type: "option",
    },
    {
        key: "subject.screen",
        type: "option",
    },
    {
        key: "projects.attributes.dispensing.yes",
        type: "option",
    },
    {
        key: "subject.random",
        type: "option",
    },
    {
        key: "projects.subject.random.block",
        type: "option",
    },
    {
        key: "model.note.title.edc",
        type: "warn",
    },
    {
        key: "model.note.content.edc",
        type: "tips",
    },
    {
        key: "model.note.start.success",
        type: "warn",
    },
    {
        key: "projects.connectEdc.mapping.configuration",
    },
    {
        key: "project.external.edc.rave.mapping.visitCode",
    },
    {
        key: "project.external.edc.rave.mapping.edcOid",
    },
    {
        key: "project.external.edc.rave.mapping.folderOid",
    },
    {
        key: "project.external.edc.rave.mapping.formOid",
    },
    {
        key: "project.external.edc.rave.mapping.ipNumberOid",
    },
    {
        key: "project.external.edc.rave.mapping.dispenseTimeOid",
    },
    {
        key: "project.external.edc.rave.mapping.randomizationField",
    },
    {
        key: "project.external.edc.rave.mapping.randomizationCode",
    },
    {
        key: "project.external.edc.rave.mapping.randomizationTime",
    },
    {
        key: "project.external.edc.rave.mapping.group",
    },
    {
        key: "project.external.edc.rave.mapping.factor",
    },
    {
        key: "project.external.edc.rave.mapping.cohort",
    },
    {
        key: "project.external.edc.rave.mapping.fieldOid",
    },
    {
        key: "tool.tip.elearning",
        type: "tips",
    },
    {
        key: "tool.tip.elearning.yes.tp",
        type: "tips",
    },
    {
        key: "tool.tip.elearning.no.tp",
        type: "tips",
    },
    {
        key: "projects.learning.system.courses",
        type: "option",
    },
    {
        key: "projects.learning.courses",
        type: "option",
    },
    {
        key: "projects.env",
    },
    {
        key: "projects.learning.compulsory",
        type: "option",
    },
    {
        key: "projects.subject.block.str-before"    },
    {
        key: "projects.subject.stratification",
        type: "option"    },
    {
        key: "projects.subject.form",
        type: "option"    },
    {
        key: "projects.subject.cohortName",
        type: "option"    },
    {
        key: "projects.subject.block.str-after"
    }
]