export const base_info = [
    {
        key: "projects.attribute",    },
    {
        key: "projects.type",    },
    {
        key: "projects.brief",    },
    {
        key: "projects.customer",    },
    {
        key: "projects.number",    },
    {
        key: "projects.sponsor",    },
    {
        key: "projects.name",    },
    {
        key: "projects.startDate",    },
    {
        key: "projects.endDate",    },
    {
        key: "projects.contact.information",    },
    {
        key: "projects.remark",
    },
    {
        key: "projects.types.second",
    },
    {
        key: "projects.types.third",
    },
    {
        key: "projects.types.first",
    },
    {
        key: "projects.sponsor.enter",
        type: "placeholder",
    },
    {
        key: "projects.name.enter",
        type: "placeholder",
    },
    {
        key: "projects.startDateTooltip",
        type: "tips",
    },
    {
        key: "projects.endDateTooltip",
        type: "tips",
    },
    {
        key: "placeholder.input.contact",
        type: "placeholder",
    },
    {
        key: "placeholder.select.common.project.type",
        type: "warn",
    },
    {
        key: "common.tips.save",
        type: "warn",
    },
    {
        key: "common.confirm-change",
        type: "button",
    },
    {
        key: "projects.status.progress",
        type: "option",
    },
    {
        key: "projects.status.finish",
        type: "option",
    },
    {
        key: "projects.status.close",
        type: "option",
    },
    {
        key: "projects.status.pause",
        type: "option",
    },
    {
        key: "projects.status.terminate",
        type: "option",
    },
    {
        key: "projects.edit.status.progress.tips",
        type: "warn",
    },
    {
        key: "projects.edit.status.progress.content",
        type: "warn",
    },
    {
        key: "projects.edit.status.progress.reason",
        type: "placeholder",
    },
    {
        key: "projects.edit.status.finish.tips",
        type: "warn",
    },
    {
        key: "projects.edit.status.finish.content",
        type: "warn",
    },
    {
        key: "projects.edit.status.close.tips",
        type: "warn",
    },
    {
        key: "projects.edit.status.close.content",
        type: "warn",
    },
    {
        key: "projects.edit.status.pause.tips",
        type: "warn",
    },
    {
        key: "projects.edit.status.pause.content",
        type: "warn",
    },
    {
        key: "projects.edit.status.pause.reason",
        type: "placeholder",
    },
    {
        key: "projects.edit.status.terminate.tips",
        type: "warn",
    },
    {
        key: "projects.edit.status.terminate.content",
        type: "warn",
    },
    {
        key: "projects.edit.status.terminate.reason",
        type: "placeholder",

    }
]