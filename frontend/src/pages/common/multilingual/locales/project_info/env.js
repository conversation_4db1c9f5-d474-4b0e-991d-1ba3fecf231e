export const env = [
    {
        key: "menu.projects.main.env",
    },
    {
        key: "projects.envs.all",
    },
    {
        key: "projects.envs.add",
    },
    {
        key: "projects.envs.edit",
    },
    {
        key: "projects.envs.empty",
    },
    {
        key: "projects.envs.duplicate.name",
    },
    {
        key: "projects.envs.cohorts.capacity",
    },
    {
        key: "projects.envs.cohorts.capacity.value",
    },
    {
        key: "projects.envs.cohorts.reminder.thresholds",
    },
    {
        key: "projects.envs.cohorts.reminder.thresholds.toplimit",
    },
    {
        key: "projects.envs.cohorts.factor",
    },
    {
        key: "common.name.current",
    },
    {
        key: "common.name.new",
    },
    {
        key: "projects.envs.cohorts.stage",
    },
    {
        key: "projects.envs.cohorts.status1",
        type: "option",
    },
    {
        key: "projects.envs.cohorts.status2",
        type: "option",
    },
    {
        key: "projects.envs.cohorts.status3",
        type: "option",
    },
    {
        key: "projects.envs.cohorts.status4",
        type: "option"    },
    {
        key: "projects.envs.cohorts.status5",
        type: "option",
    },
    {
        key: "subject.status.registered",
        type: "option",
    },
    {
        key: "subject.status.screen.success",
        type: "option",
    },
    {
        key: "projects.envs.oper.lock.title",
        type: "warn",
    },
    {
        key: "projects.envs.oper.unlock.title",
        type: "warn",
    },
    {
        key: "projects.envs.oper.lock.message",
        type: "tips",
    },
    {
        key: "projects.envs.oper.unlock.message",
        type: "tips",
    },
    {
        key: "projects.envs.name",
    },
    {
        key: "projects.envs.current",
    },
    {
        key: "projects.envs.new",
    },
    {
        key: "project.setting.cohort.copy.contain",
    },
    {
        key: "env.copy.prod.isCopy.prompt.language",
        type: "tips",
    },
    {
        key: "env.copy.prod.isCopy.prompt.language.tips",
        type: "tips",
    },
    {
        key: "tip.copy.title",
        type: "warn",
    },
    {
        key: "common.confirm.copy",
        type: "warn",
    }
]