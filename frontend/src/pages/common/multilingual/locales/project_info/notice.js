export const notice = [
    {
        key: "projects.notice.visit.notification",
        type: "option",
    },
    {
        key: "projects.notice.rules",
    },
    {
        key: "projects.notice.object",
    },
    {
        key: "projects.notice.rules.template.a",
    },
    {
        key: "projects.notice.rules.template.a.bracket",
    },
    {
        key: "projects.notice.rules.template.a.content",
    },
    {
        key: "projects.notice.rules.template.b",
    },
    {
        key: "projects.notice.rules.template.b.content",
    },
    {
        key: "projects.notice.rules.template.c",
    },
    {
        key: "projects.notice.rules.template.c.content",
    },
    {
        key: "projects.notice.rules.template.d",
    },
    {
        key: "projects.notice.rules.template.d.content",
    },
    {
        key: "projects.notice.rules.template.title",
    },
    {
        key: "projects.notice.rules.template.minimum",
    },
    {
        key: "projects.notice.rules.template.day",
    },
    {
        key: "projects.notice.rules.template.select",
    },
    {
        key: "projects.notice.visit.notification.required.prefix.rules",
        type: "warn",
    },
    {
        key: "projects.notice.visit.notification.required.prefix.recipient",
        type: "warn",
    }
]