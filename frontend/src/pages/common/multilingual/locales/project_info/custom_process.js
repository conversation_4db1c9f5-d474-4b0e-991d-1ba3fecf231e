export const custom_process = [
    {
        key: "project.setting.switch.unblind.control",
    },
    {
        key: "tool.tip.unblind.control",
        type: "tips",
    },
    {
        key: "notice.subject.unblinding.type",
    },
    {
        key: "notice.subject.unblinding",
        type: "option",
    },
    {
        key: "notice.subject.pv.unblinding",
        type: "option",
    },
    {
        key: "project.setting.unblind.method",
    },
    {
        key: "project.setting.checkbox.approval",
    },
    {
        key: "project.setting.checkbox.unblind.sms",
        type: "option",
    },
    {
        key: "project.setting.checkbox.unblind.process",
        type: "option",
    },
    {
        key: "project.setting.checkbox.unblinded-code",
        type: "option",
    },
    {
        key: "project.setting.msg.unblind.sms",
        type: "tips",
    },
    {
        key: "project.setting.divider.approval.control",
    },
    {
        key: "project.setting.switch.approval.control",
        type: "option",
    },
    {
        key: "tool.tip.approval.control",
        type: "tips",
    },
    {
        key: "project.setting.switch.approval.control.tip",
        type: "tips",
    },
    {
        key: "project.setting.approval.method",
    },
    {
        key: "project.setting.unblind.error1",
        type: "warn",
    },
    {
        key: "project.setting.unblind.error2",
        type: "warn",
    },
    {
        key: "project.setting.unblind.error3",
        type: "warn",

    }
]