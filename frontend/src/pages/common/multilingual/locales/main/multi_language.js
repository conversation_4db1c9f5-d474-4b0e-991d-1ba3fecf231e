export const multi_language = [
    {
        key: "common.required.prefix"
    },
    {
        key: "common.language"
    },
    {
        key: "multiLanguage.duplicate.name"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "common.addTo"
    },
    {
        key: "common.ok"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "multiLanguage.shared.system.library"
    },
    {
        key: "common.yes"
    },
    {
        key: "common.no"
    },
    {
        key: "common.add"
    },
    {
        key: "common.history"
    },
    {
        key: "common.serial"
    },
    {
        key: "common.operator"
    },
    {
        key: "common.operation.time"
    },
    {
        key: "common.operation.type"
    },
    {
        key: "common.operation.content"
    },
    {
        key: "common.confirm.delete"
    },
    {
        key: "common.cancel"
    },
    {
        key: "message.save.success"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.delete"
    },
    {
        key: "menu.projects"
    },
    {
        key: "multiLanguage.allLanguage"
    },
    {
        key: "multiLanguage.enable.status"
    },
    {
        key: "multiLanguage.translation.progress"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.pagination.tip.total"
    },
    {
        key: "common.pagination.tip.pagesize-prefix"
    },
    {
        key: "common.pagination.tip.pagesize-suffix"
    },
    {
        key: "common.pagination.seleted"
    },
    {
        key: "common.pagination.record"
    },
    {
        key: "common.pagination.all"
    },
    {
        key: "simulate_random.site.detail.total"
    },
    {
        key: "common.pagination.empty"
    },
    {
        key: "multiLanguage.language.library.system"
    },
    {
        key: "multiLanguage.language.library.construct"
    },
    {
        key: "multiLanguage.language.library.eIRT"
    },
    {
        key: "multiLanguage.translation.status.yes"
    },
    {
        key: "multiLanguage.translation.status.no"
    },
    {
        key: "common.save"
    },
    {
        key: "common.upload.fileSize.100",
        type: "warn"
    },
    {
        key: "upload.excel.header.error",
        type: "warn"
    },
    {
        key: "upload.excel.key.not-match",
        type: "warn"
    },
    {
        key: "upload.import.success"
    },
    {
        key: "common.select.list.tips"
    },
    {
        key: "common.type"
    },
    {
        key: "common.name"
    },
    {
        key: "common.success"
    },
    {
        key: "multiLanguage.translation.downloadTemplate.downloadCheckbox"
    },
    {
        key: "multiLanguage.translation.downloadTemplate.downloadAll"
    },
    {
        key: "multiLanguage.language.library"
    },
    {
        key: "multiLanguage.page.path"
    },
    {
        key: "projects.env"
    },
    {
        key: "check.cohort"
    },
    {
        key: "multiLanguage.translation.status"
    },
    {
        key: "check.search"
    },
    {
        key: "common.reset"
    },
    {
        key: "multiLanguage.translation.export"
    },
    {
        key: "form.preview"
    },
    {
        key: "common.download.template"
    },
    {
        key: "multiLanguage.translation.no.data"
    },
    {
        key: "notice.exclude_recipient_list.email.batch"
    },
    {
        key: "upload.click"
    },
    {
        key: "upload.drag"
    },
    {
        key: "upload.excel.reminder"
    }
]