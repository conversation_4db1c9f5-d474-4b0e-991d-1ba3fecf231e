export const main_setting = [
    {
        key: "main_depots",
        name: "menu.settings.storehouse",
        children: [
            {
                key: "common.add"
            },
            {
                key: "common.edit"
            },
            {
                key: "common.save"
            },
            {
                key: "common.number"
            },
            {
                key: "common.name"
            },
            {
                key: "common.tips"
            },
            {
                key: "common.confirm.delete"
            },
            {
                key: "common.ok"
            },
            {
                key: "common.required.prefix.name",
                type: "placeholder"
            },
            {
                key: "common.operation"
            },
            {
                key: "common.delete"
            }
        ]
    },
    {
        key: "main_roles",
        name: "menu.settings.roles",
        children: [
            {
                key: "common.edit"
            },
            {
                key: "common.addTo"
            },
            {
                key: "common.ok"
            },
            {
                key: "roles.name"
            },
            {
                key: "common.classification"
            },
            {
                key: "common.status"
            },
            {
                key: "common.effective"
            },
            {
                key: "common.invalid"
            },
            {
                key: "common.description"
            },
            {
                key: "common.export.success"
            },
            {
                key: "role.name.enter",
                type: "placeholder"
            },
            {
                key: "operation.settings.roles.export"
            },
            {
                key: "common.serial"
            },
            {
                key: "common.template"
            },
            {
                key: "common.common"
            },
            {
                key: "common.operation"
            },
            {
                key: "common.role.setting"
            },
            {
                key: "common.all"
            },
            {
                key: "common.menu"
            },
            {
                key: "role.setting.operation"
            }
        ]
    },
    {
        key: "main_users",
        name: "menu.settings.users",
        children: [
            {
                key: "common.authorization.success"
            },
            {
                key: "common.email.language.zh"
            },
            {
                key: "common.email.language.en"
            },
            {
                key: "common.add"
            },
            {
                key: "common.edit"
            },
            {
                key: "common.ok"
            },
            {
                key: "common.email.language"
            },
            {
                key: "placeholder.select.common.email.language"
            },
            {
                key: "common.email"
            },
            {
                key: "common.tips"
            },
            {
                key: "common.back.revise"
            },
            {
                key: "common.ignore.continue"
            },
            {
                key: "user.batch.add.email.tip1"
            },
            {
                key: "user.batch.add.email.tip2"
            },
            {
                key: "common.serial"
            },
            {
                key: "common.reason"
            },
            {
                key: "common.user.add.configure.permissions.role"
            },
            {
                key: "common.save"
            },
            {
                key: "common.role"
            },
            {
                key: "common.permission"
            },
            {
                key: "common.user.add.success"
            },
            {
                key: "common.user.add.configure.permissions.role.tip"
            },
            {
                key: "user.batch.add.email.required",
                type: "placeholder"
            },
            {
                key: "common.success"
            },
            {
                key: "user.settings.invite-again"
            },
            {
                key: "user.customer.admin.add"
            },
            {
                key: "user.customer.admin.remove"
            },
            {
                key: "common.confirm.close"
            },
            {
                key: "tip.user.close"
            },
            {
                key: "common.export.success"
            },
            {
                key: "common.cancel"
            },
            {
                key: "common.setting"
            },
            {
                key: "common.close"
            },
            {
                key: "common.close.batch"
            },
            {
                key: "user.status.close"
            },
            {
                key: "user.status.open"
            },
            {
                key: "user.status.active"
            },
            {
                key: "user.all"
            },
            {
                key: "common.required.prefix.keyword",
                type: "placeholder"
            },
            {
                key: "operation.settings.users.system.administrator"
            },
            {
                key: "operation.settings.users.export"
            },
            {
                key: "common.full_name"
            },
            {
                key: "common.phone"
            },
            {
                key: "common.company"
            },
            {
                key: "common.description"
            },
            {
                key: "common.status"
            },
            {
                key: "customer.user.status.Enabled",
                type: "tips"
            },
            {
                key: "customer.user.status.Activated",
                type: "tips"
            },
            {
                key: "customer.user.status.Closed",
                type: "tips"
            },
            {
                key: "common.operation"
            }
        ]
    }
]