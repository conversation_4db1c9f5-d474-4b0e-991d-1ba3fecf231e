export const main_projects = [
    {
        key: "projects.all.admin"
    },
    {
        key: "projects.all"
    },
    {
        key: "project.number.name",
        type: "placeholder"
    },
    {
        key: "operation.projects.main.create"
    },
    {
        key: "common.serial"
    },
    {
        key: "projects.type"
    },
    {
        key: "projects.number"
    },
    {
        key: "projects.name"
    },
    {
        key: "projects.sponsor"
    },
    {
        key: "projects.customer"
    },
    {
        key: "projects.status"
    },
    {
        key: "projects.status.progress.describe",
        type: "tips"
    },
    {
        key: "projects.status.finish.describe",
        type: "tips"
    },
    {
        key: "projects.status.close.describe",
        type: "tips"
    },
    {
        key: "projects.status.terminate.describe",
        type: "tips"
    },
    {
        key: "projects.status.pause.describe",
        type: "tips"
    },
    {
        key: "common.administrator"
    },
    {
        key: "common.operation"
    },
    {
        key: "menu.projects.main.setting"
    },
    {
        key: "input.error.common"
    },
    {
        key: "common.ok"
    },
    {
        key: "common.customer"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "projects.types.first"
    },
    {
        key: "projects.types.second"
    },
    {
        key: "projects.types.third"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "projects.contact.information"
    },
    {
        key: "placeholder.input.contact",
        type: "placeholder"
    },
    {
        key: "projects.description"
    },
    {
        key: "projects.status.progress",
        type: "option"
    },
    {
        key: "projects.status.finish",
        type: "option"
    },
    {
        key: "projects.status.close",
        type: "option"
    },
    {
        key: "projects.status.pause",
        type: "option"
    },
    {
        key: "projects.status.terminate",
        type: "option"
    }
]