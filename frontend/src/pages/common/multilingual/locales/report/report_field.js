export const report_field = [
    {
        "key": "menu.report.randomizeReport"
    },
    {
        "key": "menu.report.dispenseReport"
    },
    {
        "key": "menu.report.unblindingReport"
    },
    {
        "key": "menu.report.shipmentOrdersReport"
    },
    {
        "key": "menu.report.returnOrdersReport"
    },
    {
        "key": "menu.report.siteItemReport"
    },
    {
        "key": "menu.report.depotItemReport"
    },
    {
        "key": "menu.report.sourceRandomizationListExport"
    },
    {
        "key": "menu.report.sourceIPExport"
    },
    {
        "key": "menu.report.sourceIpUploadHistory"
    },
    {
        "key": "menu.report.randomizationSimulationResult"
    },
    {
        "key": "menu.report.RandomizationSimulationPDFExport"
    },
    {
        "key": "menu.report.configureReport"
    },
    {
        "key": "menu.report.auditTrailExport"
    },
    {
        "key": "menu.report.projectPermissionConfigurationExport"
    },
    {
        "key": "menu.report.ProjectNotificationsConfigurationReportExport"
    },
    {
        "key": "menu.report.userRoleStatus"
    },
    {
        "key": "menu.report.userLoginHistory"
    },
    {
        "key": "menu.report.userRoleAssignHistory"
    },
    {
        "key": "menu.report.siteIPStatisticsExport"
    },
    {
        "key": "menu.report.depotIPStatisticsExport"
    },
    {
        "key": "menu.report.randomizationStatisticsExport"
    },
    {
        "key": "menu.report.subjectStatisticsExport"
    },
    {
        "key": "menu.report.forecastingPrediction"
    },
    {
        "key": "menu.report.visitForecast"
    },
    {
        "key": "report.attributes.project.number"
    },
    {
        "key": "report.attributes.project.name"
    },
    {
        "key": "report.attributes.info.country"
    },
    {
        "key": "report.attributes.info.region"
    },
    {
        "key": "report.attributes.info.site.number"
    },
    {
        "key": "report.attributes.info.site.name"
    },
    {
        "key": "report.attributes.info.subject.number"
    },
    {
        "key": "report.attributes.info.status"
    },
    {
        "key": "report.attributes.random.factor"
    },
    {
        "key": "report.attributes.random.number"
    },
    {
        "key": "report.attributes.random.time"
    },
    {
        "key": "report.attributes.random.group"
    },
    {
        "key": "report.attributes.dispensing.medicine.real.group"
    },
    {
        "key": "report.attributes.random.config.code"
    },
    {
        "key": "report.attributes.random.subject.number.replace"
    },
    {
        "key": "report.attributes.random.subject.replace.number"
    },
    {
        "key": "report.attributes.random.subject.replace.status"
    },
    {
        "key": "report.attributes.random.subject.replace.time"
    },
    {
        "key": "report.attributes.random.sign.out.real.time"
    },
    {
        "key": "report.attributes.random.finish.remark"
    },
    {
        "key": "report.attributes.random.cohort"
    },
    {
        "key": "report.attributes.random.stage"
    },
    {
        "key": "report.attributes.info"
    },
    {
        "key": "report.attributes.random"
    },
    {
        "key": "report.attributes.random.register.time"
    },
    {
        "key": "report.attributes.random.register.operator"
    },
    {
        "key": "report.attributes.random.operator"
    },
    {
        "key": "report.attributes.random.form"
    },
    {
        "key": "report.attributes.random.factor.calc"
    },
    {
        "key": "report.attributes.random.actual.factor"
    },
    {
        "key": "report.attributes.random.sign.out.operator"
    },
    {
        "key": "report.attributes.random.sign.out.time"
    },
    {
        "key": "report.attributes.random.sign.out.reason"
    },
    {
        "key": "report.attributes.random.screen.time"
    },
    {
        "key": "report.attributes.random.icf.time"
    },
    {
        "key": "report.attributes.random.plan.time"
    },
    {
        "key": "report.attributes.random.sequence.number"
    },
    {
        "key": "report.attributes.dispensing.cycle.name"
    },
    {
        "key": "report.attributes.dispensing.type"
    },
    {
        "key": "report.attributes.dispensing.time"
    },
    {
        "key": "report.attributes.dispensing.operator"
    },
    {
        "key": "report.attributes.dispensing.medicine"
    },
    {
        "key": "report.attributes.dispensing.drug.name"
    },
    {
        "key": "report.attributes.research.batch"
    },
    {
        "key": "report.attributes.research.expireDate"
    },
    {
        "key": "report.attributes.dispensing.label"
    },
    {
        "key": "report.attributes.dispensing.dose"
    },
    {
        "key": "report.attributes.dispensing.medicine.replace"
    },
    {
        "key": "report.attributes.dispensing.medicine.real"
    },
    {
        "key": "report.attributes.dispensing.drug.other.number"
    },
    {
        "key": "report.attributes.dispensing.useFormulas"
    },
    {
        "key": "report.attributes.dispensing.reissue.remark"
    },
    {
        "key": "report.attributes.dispensing.out-visit-dispensing.remark"
    },
    {
        "key": "report.attributes.dispensing.replace.remark"
    },
    {
        "key": "report.attributes.dispensing"
    },
    {
        "key": "report.attributes.dispensing.planTime"
    },
    {
        "key": "report.attributes.dispensing.remark"
    },
    {
        "key": "report.attributes.dispensing.out-visit-dispensing.reason"
    },
    {
        "key": "report.attributes.dispensing.reissue.reason"
    },
    {
        "key": "report.attributes.research.packageNumber"
    },
    {
        "key": "report.attributes.dispensing.outsize"
    },
    {
        "key": "report.attributes.dispensing.doseFormulas"
    },
    {
        "key": "report.attributes.dispensing.retrieval.remark"
    },
    {
        "key": "report.attributes.dispensing.register.remark"
    },
    {
        "key": "report.attributes.dispensing.invalid.remark"
    },
    {
        "key": "report.attributes.dispensing.send.type"
    },
    {
        "key": "report.attributes.dispensing.logistics.info"
    },
    {
        "key": "report.attributes.dispensing.logistics.remark"
    },
    {
        "key": "report.attributes.unblinding.sponsor"
    },
    {
        "key": "report.attributes.unblinding.mark"
    },
    {
        "key": "report.attributes.unblinding.reason"
    },
    {
        "key": "report.attributes.unblinding.reason.mark"
    },
    {
        "key": "report.attributes.unblinding.operator"
    },
    {
        "key": "report.attributes.unblinding.time"
    },
    {
        "key": "report.attributes.dispensing.medicine.real.number"
    },
    {
        "key": "report.attributes.unblinding"
    },
    {
        "key": "report.attributes.order.number"
    },
    {
        "key": "report.attributes.order.status"
    },
    {
        "key": "report.attributes.order.send"
    },
    {
        "key": "report.attributes.order.receive"
    },
    {
        "key": "report.attributes.order.medicineQuantity"
    },
    {
        "key": "report.attributes.order.create.by"
    },
    {
        "key": "report.attributes.order.create.time"
    },
    {
        "key": "report.attributes.order.receive.by"
    },
    {
        "key": "report.attributes.order.receive.time"
    },
    {
        "key": "report.attributes.order.close.by"
    },
    {
        "key": "report.attributes.order.close.time"
    },
    {
        "key": "report.attributes.order.end.by"
    },
    {
        "key": "report.attributes.order.end.time"
    },
    {
        "key": "report.attributes.order.lost.by"
    },
    {
        "key": "report.attributes.order.lost.time"
    },
    {
        "key": "report.attributes.order.actualReceiptTime"
    },
    {
        "key": "report.attributes.research"
    },
    {
        "key": "report.attributes.research.other"
    },
    {
        "key": "report.attributes.order"
    },
    {
        "key": "report.attributes.order.cancel.by"
    },
    {
        "key": "report.attributes.order.cancel.time"
    },
    {
        "key": "report.attributes.order.cancel.reason"
    },
    {
        "key": "report.attributes.order.confirm.by"
    },
    {
        "key": "report.attributes.order.confirm.time"
    },
    {
        "key": "report.attributes.order.close.reason"
    },
    {
        "key": "report.attributes.order.send.by"
    },
    {
        "key": "report.attributes.order.send.time"
    },
    {
        "key": "report.attributes.order.lost.reason"
    },
    {
        "key": "report.attributes.order.end.reason"
    },
    {
        "key": "report.attributes.order.supplier"
    },
    {
        "key": "report.attributes.order.supplier.other"
    },
    {
        "key": "report.attributes.order.supplier.number"
    },
    {
        "key": "report.attributes.order.expectedArrivalTime"
    },
    {
        "key": "report.attributes.order.detail"
    },
    {
        "key": "report.attributes.research.packageMethod"
    },
    {
        "key": "report.attributes.research.status"
    },
    {
        "key": "report.attributes.research.medicine.number"
    },
    {
        "key": "report.attributes.research.medicine.name"
    },
    {
        "key": "report.attributes.research.spec"
    },
    {
        "key": "report.attributes.research.order.number"
    },
    {
        "key": "report.attributes.info.site.country"
    },
    {
        "key": "report.attributes.info.site.region"
    },
    {
        "key": "report.attributes.research.reason"
    },
    {
        "key": "report.attributes.research.operator"
    },
    {
        "key": "report.attributes.research.time"
    },
    {
        "key": "report.attributes.info.storehouse.name"
    },
    {
        "key": "report.simulate.random.block"
    },
    {
        "key": "report.simulate.random.group"
    },
    {
        "key": "projects.randomization.planNumber"
    },
    {
        "key": "report.attributes.random.g"
    },
    {
        "key": "report.attributes.research.package.serialNumber"
    },
    {
        "key": "report.attributes.research.medicine.serial-number"
    },
    {
        "key": "report.attributes.dispensing.medicine.is.replace"
    },
    {
        "key": "report.attributes.dispensing.medicine.is.real"
    },
    {
        "key": "source.ip.upload.history.name"
    },
    {
        "key": "source.ip.upload.history.rows"
    },
    {
        "key": "common.status"
    },
    {
        "key": "report.attributes.research.medicine.code"
    },
    {
        "key": "report.attributes.research.medicine.type"
    },
    {
        "key": "report.user.role.assign.content"
    },
    {
        "key": "report.simulate.random.name"
    },
    {
        "key": "simulated.random.list.runCount"
    },
    {
        "key": "common.role"
    },
    {
        "key": "common.menu"
    },
    {
        "key": "common.operation"
    },
    {
        "key": "export.notifications.configuration.report.type"
    },
    {
        "key": "export.notifications.configuration.report.role"
    },
    {
        "key": "export.notifications.configuration.report.content.configuration"
    },
    {
        "key": "export.notifications.configuration.report.scene"
    },
    {
        "key": "report.attributes.info.user.name"
    },
    {
        "key": "report.attributes.info.user.email"
    },
    {
        "key": "report.attributes.info.user.role"
    },
    {
        "key": "report.user.login.ip"
    },
    {
        "key": "report.user.login.time"
    },
    {
        "key": "report.user.login.success"
    },
    {
        "key": "report.user.role.assign.name"
    },
    {
        "key": "report.user.role.assign.email"
    },
    {
        "key": "report.user.role.assign.operType"
    },
    {
        "key": "report.user.role.assign.oper"
    },
    {
        "key": "report.user.role.assign.operTime"
    },
    {
        "key": "report.ip.statistics.status.available"
    },
    {
        "key": "report.ip.statistics.status.toBeConfirmed"
    },
    {
        "key": "report.ip.statistics.status.delivered"
    },
    {
        "key": "report.ip.statistics.status.sending"
    },
    {
        "key": "report.ip.statistics.status.quarantine"
    },
    {
        "key": "report.ip.statistics.status.used"
    },
    {
        "key": "report.ip.statistics.status.lose"
    },
    {
        "key": "report.ip.statistics.status.expired"
    },
    {
        "key": "report.ip.statistics.status.frozen"
    },
    {
        "key": "report.ip.statistics.status.locked"
    },
    {
        "key": "report.random.statistics.month"
    },
    {
        "key": "report.random.statistics.week"
    },
    {
        "key": "subject.register"
    },
    {
        "key": "subject.random"
    },
    {
        "key": "subject.exited"
    },
    {
        "key": "report.subject.unblinding.urgent"
    },
    {
        "key": "report.subject.unblinding.pv"
    },
    {
        "key": "subject.status.finish"
    },
    {
        "key": "report.forecast.depot"
    },
    {
        "key": "report.forecast.period"
    },
    {
        "key": "report.visit.forecast.visit.status"
    },
    {
        "key": "report.visit.forecast.notice.content"
    },
    {
        "key": "report.visit.forecast.notice.time"
    },
    {
        "key": "report.visit.forecast.notice.user"
    },
    {
        "key": "report.visit.forecast.notice.type"
    },
    {
        "key": "report.visit.forecast.notice.email"
    }
]