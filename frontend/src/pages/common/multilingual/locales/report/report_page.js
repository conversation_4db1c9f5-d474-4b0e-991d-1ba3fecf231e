export const report_page = [
    {
        "key": "report.template.edit.success"
    },
    {
        "key": "report.template.create.success"
    },
    {
        "key": "report.attributes.info.subject.number"
    },
    {
        "key": "report.attributes.random.subject.number.replace"
    },
    {
        "key": "subject.replace"
    },
    {
        "key": "common.reason"
    },
    {
        "key": "subject.unblinding.reason.remark"
    },
    {
        "key": "common.edit"
    },
    {
        "key": "common.create"
    },
    {
        "key": "report.template.default"
    },
    {
        "key": "common.cancel"
    },
    {
        "key": "common.ok"
    },
    {
        "key": "report.template.name"
    },
    {
        "key": "report.template.name.required"
    },
    {
        "key": "common.required.prefix",
        "type": "placeholder"
    },
    {
        "key": "report.history"
    },
    {
        "key": "common.serial"
    },
    {
        "key": "report.filename"
    },
    {
        "key": "report.filename.search.required",
        "type": "placeholder"
    },
    {
        "key": "report.filesize"
    },
    {
        "key": "report.exportTime"
    },
    {
        "key": "common.operation"
    },
    {
        "key": "common.download"
    },
    {
        "key": "report.cohort.warning"
    },
    {
        "key": "report.stage.warning"
    },
    {
        "key": "report.export.randomization.simulation.select.name"
    },
    {
        "key": "report.audit.trail.type.secret.warning"
    },
    {
        "key": "report.subject.warning"
    },
    {
        "key": "report.download.success"
    },
    {
        "key": "report.site.warning"
    },
    {
        "key": "report.role.warning"
    },
    {
        "key": "report.random.list.warning"
    },
    {
        "key": "common.role"
    },
    {
        "key": "projects.second"
    },
    {
        "key": "projects.third"
    },
    {
        "key": "report.storehouse"
    },
    {
        "key": "common.all",
        "type": "placeholder"
    },
    {
        "key": "shipment.send"
    },
    {
        "key": "shipment.receive"
    },
    {
        "key": "common.type"
    },
    {
        "key": "common.all",
        "type": "placeholder"
    },
    {
        "key": "common.time"
    },
    {
        "key": "common.time.start",
        "type": "placeholder"
    },
    {
        "key": "common.time.end",
        "type": "placeholder"
    },
    {
        "key": "common.export.format"
    },
    {
        "key": "project.report.locus.checkbox.password"
    },
    {
        "key": "common.password"
    },
    {
        "key": "common.name"
    },
    {
        "key": "placeholder.select.common",
        "type": "placeholder"
    },
    {
        "key": "report.export.forecast.date"
    },
    {
        "key": "report.attributes.unit"
    },
    {
        "key": "subject.number"
    },
    {
        "key": "common.select",
        "type": "placeholder"
    },
    {
        "key": "report.role"
    },
    {
        "key": "common.site"
    },
    {
        "key": "report.template"
    },
    {
        "key": "report.template.name.default",
        "type": "option"
    },
    {
        "key": "report.attributes"
    },
    {
        "key": "report.custom"
    },
    {
        "key": "form.preview"
    },
    {
        "key": "project.statistics.out-visit-dispensing"
    },
    {
        "key": "report.customTemplate"
    },
    {
        "key": "report.projectEnv"
    },
    {
        "key": "report"
    },
    {
        "key": "report.latestDownloadTime"
    },
    {
        "key": "report.template.delete.confirm"
    },
    {
        "key": "common.delete.ok"
    },
    {
        "key": "common.copy.ok"
    },
    {
        "key": "report.template.all"
    },
    {
        "key": "common.copy"
    },
    {
        "key": "common.delete"
    }
]