export const subject_visit = [
    {
        key: "project.visit.cycle.calendar"
    },
    {
        key: "calendar.button.today"
    },
    {
        key: "subject.visit.item.outsize"
    },
    {
        key: "subject.visit.item.undone"
    },
    {
        key: "project.task.status.notStarted"
    },
    {
        key: "subject.visit.item.on.schedule"
    },
    {
        key: "projects.notice.visit.notification.required.prefix",
        type: "placeholder"
    },
    {
        key: "projects.notice.visit.notification.required.prefix.recipient",
        type: "warn"
    },
    {
        key: "projects.notice.rules.template.a.content"
    },
    {
        key: "projects.notice.rules.template.b.content"
    },
    {
        key: "projects.notice.rules.template.c.content"
    },
    {
        key: "projects.notice.rules.template.d.content"
    },
    {
        key: "common.view"
    },
    {
        key: "calendar.button.site.visit.matter.notice"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.ok"
    },
    {
        key: "calendar.button.site.visit.matter.notice.send"
    },
    {
        key: "calendar.button.site.visit.matter.notice.history"
    },
    {
        key: "calendar.button.site.visit.matter.notice.history.content"
    },
    {
        key: "calendar.button.site.visit.matter.notice.history.time"
    },
    {
        key: "calendar.button.site.visit.matter.notice.history.people"
    },
    {
        key: "calendar.button.site.visit.matter.notice.history.way"
    },
    {
        key: "calendar.button.site.visit.matter.app.notice"
    },
    {
        key: "calendar.button.site.visit.matter.text.message"
    },
    {
        key: "calendar.button.site.visit.matter.notice.history.object"
    },
    {
        key: "common.template"
    },
    {
        key: "projects.notice.rules.template.a"
    },
    {
        key: "projects.notice.rules.template.a.bracket"
    },
    {
        key: "projects.notice.rules.template.b"
    },
    {
        key: "projects.notice.rules.template.c"
    },
    {
        key: "projects.notice.rules.template.d"
    },
    {
        key: "project.statistics.push.mode"
    },
    {
        key: "calendar.button.site.visit.matter.recipient"
    },
    {
        key: "common.role"
    },
    {
        key: "user.all"
    },
    {
        key: "report.attributes.unit"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "no.permission",
        type: "warn"
    },
    {
        key: "subject.visit.cycle.item"
    },
    {
        key: "subject.visit.cycle.item.site"
    },
    {
        key: "subject.visit.cycle.item.plan"
    },
    {
        key: "subject.visit.cycle.item.actual"
    },
    {
        key: "common.all"
    },
    {
        key: "common.site"
    },
    {
        key: "projects.storehouse.statistics.summary"
    },
    {
        key: "project.visit.cycle.calendar.summary.outsize.completed",
        type: "tips"
    },
    {
        key: "project.visit.cycle.calendar.summary.outsize.undone",
        type: "tips"
    },
    {
        key: "project.visit.cycle.calendar.summary.in.progress",
        type: "tips"
    },
    {
        key: "project.visit.cycle.calendar.summary.completed.on.schedule",
        type: "tips"
    },
    {
        key: "project.visit.cycle.calendar.summary.has.not.started",
        type: "tips"
    },
    {
        key: "subject.visit.outsize"
    },
    {
        key: "subject.visit.outsize.undone"
    },
    {
        key: "subject.visit.outsize.completed"
    },
    {
        key: "subject.visit.completed.on.schedule"
    },
    {
        key: "project.task.status.notStarted"
    }
]