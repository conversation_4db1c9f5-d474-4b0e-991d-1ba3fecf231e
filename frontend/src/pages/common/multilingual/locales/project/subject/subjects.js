export const subject_subjects = [
    {
        key: "subject.random.fail"
    },
    {
        key: "subject.register.add"
    },
    {
        key: "subject.register.update"
    },
    {
        key: "subject.random"
    },
    {
        key: "subject.edc.register.push.centre"
    },
    {
        key: "subject.edc.push.centre"
    },
    {
        key: "common.please.confirm"
    },
    {
        key: "subject.edc.return.modification"
    },
    {
        key: "subject.edc.continue.update"
    },
    {
        key: "subject.number",
        type: "warn"
    },
    {
        key: "subject.number.no.empty",
        type: "warn"
    },
    {
        key: "subject.number.digit.no",
        type: "warn"
    },
    {
        key: "common.success"
    },
    {
        key: "subject.edc.interface.error"
    },
    {
        key: "subject.edc.no.mapping"
    },
    {
        key: "subject.status.screen.fail"
    },
    {
        key: "subject.edc.subject.exited"
    },
    {
        key: "subject.edc.subject.screen.no.random"
    },
    {
        key: "subject.edc.subject"
    },
    {
        key: "subject.edc.subject.continue.update"
    },
    {
        key: "subject.edc.no.subject.push.centre"
    },
    {
        key: "common.tips"
    },
    {
        key: "subject.edc.no.subject"
    },
    {
        key: "subject.edc.update.no.subject"
    },
    {
        key: "subject.edc.register.no.subject"
    },
    {
        key: "subject.edc.continue.register"
    },
    {
        key: "subject.edc.complete.cohort.registration"
    },
    {
        key: "subject.edc.complete.registration"
    },
    {
        key: "subject.edc.cover.tip"
    },
    {
        key: "subject.registration.success"
    },
    {
        key: "subject.check.random.form",
        type: "warn"
    },
    {
        key: "subject.edc.create.subject"
    },
    {
        key: "subject.edc.inconsistent.information1",
        type: "warn"
    },
    {
        key: "subject.edc.continue.submitting"
    },
    {
        key: "subject.edc.site.inconsistent"
    },
    {
        key: "common.cancel"
    },
    {
        key: "subject.edc.site.empty"
    },
    {
        key: "subject.edc.random.failure.filter.failed"
    },
    {
        key: "subject.edc.random.failure.exit"
    },
    {
        key: "subject.edc.random.failure.complete.the.study"
    },
    {
        key: "subject.edc.random.failure.screen.failed"
    },
    {
        key: "subject.random.success"
    },
    {
        key: "common.confirm"
    },
    {
        key: "common.ok"
    },
    {
        key: "placeholder.select.common"
    },
    {
        key: "projects.second"
    },
    {
        key: "common.stage"
    },
    {
        key: "common.site"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "form.control.type.format.limit"
    },
    {
        key: "form.control.type.variableRange.validate.range"
    },
    {
        key: "subject.screen.field"
    },
    {
        key: "common.yes"
    },
    {
        key: "common.no"
    },
    {
        key: "subject.screen.time.field"
    },
    {
        key: "subject.screen.ICF.field"
    },
    {
        key: "subject.stop.time"
    },
    {
        key: "subject.reason"
    },
    {
        key: "subject.confirm.random.button"
    },
    {
        key: "subject.confirm.random"
    },
    {
        key: "subject.random.info"
    },
    {
        key: "subject.dispensing.retrieval"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "drug.freeze.count"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "subject.unblinding.remark"
    },
    {
        key: "common.select.list.tips"
    },
    {
        key: "subject.dispensing.confirmRetrieval"
    },
    {
        key: "subject.dispensing.retrievalMessage"
    },
    {
        key: "subject.dispensing.resumeTip"
    },
    {
        key: "subject.dispensing.reissue"
    },
    {
        key: "subject.dispensing.no.join"
    },
    {
        key: "subject.dispensing.outsize"
    },
    {
        key: "subject.dispensing.dispensing"
    },
    {
        key: "subject.apply"
    },
    {
        key: "operation.subject.medicine.replace"
    },
    {
        key: "operation.subject.medicine.resume"
    },
    {
        key: "subject.dispensing.invalid"
    },
    {
        key: "subject.dispensing.number.register"
    },
    {
        key: "drug.configure.roomNumber"
    },
    {
        key: "common.history"
    },
    {
        key: "subject.dispensing.apply.subjectInfo.apply"
    },
    {
        key: "subject.dispensing.subject.detail"
    },
    {
        key: "subject.dispensing.visitSignDispensing"
    },
    {
        key: "subject.dispensing.visitSignDispensing.apply"
    },
    {
        key: "subject.dispensing.reissue.apply"
    },
    {
        key: "subject.dispensing.subject"
    },
    {
        key: "common.edit",
        type: "tips"
    },
    {
        key: "projects.current.stage"
    },
    {
        key: "check.select.time"
    },
    {
        key: "subject.dispensing.form.factor.title"
    },
    {
        key: "subject.dispensing.factor.title"
    },
    {
        key: "subject.dispensing.actual.factor.title"
    },
    {
        key: "subject.dispensing.record"
    },
    {
        key: "common.setting"
    },
    {
        key: "common.serial"
    },
    {
        key: "visit.cycle.visitName"
    },
    {
        key: "subject.dispensing.plan.time"
    },
    {
        key: "common.operation.time"
    },
    {
        key: "subject.dispensing.realDispensing"
    },
    {
        key: "common.operation"
    },
    {
        key: "subject.dispensing.open.visit"
    },
    {
        key: "subject.dispensing.open.visit.tip1"
    },
    {
        key: "subject.dispensing.open.visit.tip2"
    },
    {
        key: "drug.medicine.packlist"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "drug.list.drugNumber"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.other.count"
    },
    {
        key: "report.attributes.dispensing.label"
    },
    {
        key: "report.attributes.dispensing.dose"
    },
    {
        key: "subject.edc.continue.dispense"
    },
    {
        key: "common.operation.error"
    },
    {
        key: "subject.edc.dispensing.failure.filter.failed"
    },
    {
        key: "subject.edc.dispensing.failure.exit"
    },
    {
        key: "subject.edc.dispensing.failure.complete.the.study"
    },
    {
        key: "workTask.system.add.success"
    },
    {
        key: "subject.dispensing.drug.formula.error",
        type: "warn"
    },
    {
        key: "projects.randomization.randomNumber"
    },
    {
        key: "visit.cycle.name"
    },
    {
        key: "subject.dispensing.formula.dispensing"
    },
    {
        key: "common.reason"
    },
    {
        key: "subject.dispensing.visitSignDispensingReason"
    },
    {
        key: "subject.dispensing.reissue.reason"
    },
    {
        key: "subject.dispensing.outsize.reason"
    },
    {
        key: "subject.dispensing.apply"
    },
    {
        key: "subject.dispensing.drugLabel"
    },
    {
        key: "logistics.send.site"
    },
    {
        key: "logistics.send.site.subject"
    },
    {
        key: "logistics.send.depot.subject"
    },
    {
        key: "subject.confirm.dispensing.button"
    },
    {
        key: "subject.dispensing.confirm.replace"
    },
    {
        key: "subject.dispensing.confirm"
    },
    {
        key: "subject.dispensing.info"
    },
    {
        key: "subject.dispensing.replace.info"
    },
    {
        key: "logistics.dispensing.method"
    },
    {
        key: "logistics.info"
    },
    {
        key: "subject.dispensing.replace.reason"
    },
    {
        key: "common.remark"
    },
    {
        key: "drug.configure.drugSpecifications"
    },
    {
        key: "subject.dispensing.invalidTip"
    },
    {
        key: "subject.dispensing.confirmInvalid"
    },
    {
        key: "subject.dispensing.replace.confirm"
    },
    {
        key: "validator.message.replace.medicine"
    },
    {
        key: "subject.dispensing.replace"
    },
    {
        key: "subject.dispensing.selectDrugNumber"
    },
    {
        key: "subject.dispensing.selectDrugNumber.label"
    },
    {
        key: "subject.dispensing.selectedRepeatedly"
    },
    {
        key: "subject.dispensing.label.selectedRepeatedly"
    },
    {
        key: "subject.dispensing.medicine.validator"
    },
    {
        key: "subject.dispensing.cancel"
    },
    {
        key: "subject.dispensing.replace.select"
    },
    {
        key: "subject.dispensing.placeholder.input.count"
    },
    {
        key: "drug.batch.treatmentDesign.openSetting"
    },
    {
        key: "subject.dispensing.registration.success"
    },
    {
        key: "subject.dispensing.realDispensing.res"
    },
    {
        key: "subject.dispensing.realDispensingTip"
    },
    {
        key: "subject.dispensing.realDispensingConfirm"
    },
    {
        key: "input.error.common"
    },
    {
        key: "subject.dispensing.form.number"
    },
    {
        key: "common.status"
    },
    {
        key: "subject.dispensing.medicine.available.frozen"
    },
    {
        key: "medicine.status.lose"
    },
    {
        key: "medicine.status.available"
    },
    {
        key: "subject.dispensing.form.realNumber"
    },
    {
        key: "subject.dispensing.placeholder.input.batch"
    },
    {
        key: "storehouse.name"
    },
    {
        key: "subject.dispensing.placeholder.input.expiration"
    },
    {
        key: "common.save"
    },
    {
        key: "subject.dispensing.subjectInfo"
    },
    {
        key: "subject.dispensing.drugNumber"
    },
    {
        key: "subject.dispensing.dose.tip"
    },
    {
        key: "subject.dispensing.label.dispensing"
    },
    {
        key: "subject.dispensing.drug.formula.tip.start"
    },
    {
        key: "subject.dispensing.drug.customer.formula.tip"
    },
    {
        key: "shipment.order.create.info"
    },
    {
        key: "subject.dispensing.drug.input.error"
    },
    {
        key: "subject.dispensing.placeholder.input.dispensing.count",
        type: "placeholder"
    },
    {
        key: "common.add",
        type: "tips"
    },
    {
        key: "common.delete",
        type: "tips"
    },
    {
        key: "common.addTo"
    },
    {
        key: "subject.dispensing.label.typeSelectedRepeatedly"
    },
    {
        key: "subject.dispensing.drugName.dispensing"
    },
    {
        key: "subject.dispensing.drugName"
    },
    {
        key: "subject.dispensing.drug.limit"
    },
    {
        key: "subject.edc.replace.register.information.subject.no"
    },
    {
        key: "subject.edc.replace.register.information.stratification"
    },
    {
        key: "subject.edc.replace.register.information.form"
    },
    {
        key: "subject.edc.replace.register.information.screen"
    },
    {
        key: "subject.edc.replace.register.information.a1"
    },
    {
        key: "subject.edc.replace.register.information.b"
    },
    {
        key: "subject.edc.replace.register.information.a2"
    },
    {
        key: "subject.edc.replace.register.information.a3"
    },
    {
        key: "subject.edc.continue.replace"
    },
    {
        key: "subject.edc.continue.screen"
    },
    {
        key: "subject.edc.verification.source"
    },
    {
        key: "subject.edc.verification.return"
    },
    {
        key: "subject.edc.verification.enter"
    },
    {
        key: "subject.register.edit"
    },
    {
        key: "projects.attributes.random.minimize.calc.actual.factor"
    },
    {
        key: "subject.confirm.finish"
    },
    {
        key: "subject.finish.title"
    },
    {
        key: "subject.finish.remark"
    },
    {
        key: "drug.configure.formula.height"
    },
    {
        key: "drug.configure.formula.weight"
    },
    {
        key: "drug.configure.formula.date"
    },
    {
        key: "subject.dispensing.drug.input.error.age"
    },
    {
        key: "subject.dispensing.drug.formula.weight.tip.start"
    },
    {
        key: "subject.dispensing.drug.formula.tip.end"
    },
    {
        key: "subject.dispensing.drug.formula.two.tip.start"
    },
    {
        key: "subject.dispensing.drug.formula.two.tip.end"
    },
    {
        key: "subject.random.section1"
    },
    {
        key: "subject.random.section2"
    },
    {
        key: "subject.random.section3"
    },
    {
        key: "common.confirm.delete"
    },
    {
        key: "common.confirm.delete.unable.recover"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "subject.random.download"
    },
    {
        key: "subject.dispensing.download"
    },
    {
        key: "subject.unblinding.download"
    },
    {
        key: "common.download"
    },
    {
        key: "subject.screen"
    },
    {
        key: "subject.update"
    },
    {
        key: "subject.replace"
    },
    {
        key: "subject.exited"
    },
    {
        key: "subject.transport"
    },
    {
        key: "subject.random.all"
    },
    {
        key: "projects.third"
    },
    {
        key: "common.all"
    },
    {
        key: "subject.invalid.list"
    },
    {
        key: "subject.register"
    },
    {
        key: "projects.randomization.randomSequenceNumber"
    },
    {
        key: "drug.configure.group"
    },
    {
        key: "drug.configure.subGroup"
    },
    {
        key: "subject.status.registered"
    },
    {
        key: "subject.status.random"
    },
    {
        key: "subject.status.exited"
    },
    {
        key: "subject.status.blinded.urgent"
    },
    {
        key: "subject.status.blinded.pv"
    },
    {
        key: "subject.status.screen.success"
    },
    {
        key: "subject.status.finish"
    },
    {
        key: "subject.status.to.be.random"
    },
    {
        key: "subject.status.join"
    },
    {
        key: "subject.unblinding.urgent"
    },
    {
        key: "subject.Unblinded"
    },
    {
        key: "subject.brokenBlinded"
    },
    {
        key: "project.task.urgent-unblinding.title",
    },
    {
        key: "project.task.pv-unblinding.title"
    },
    {
        key: "subject.already.unblinding"
    },
    {
        key: "subject.unblinding.pv"
    },
    {
        key: "projects.subject.selectSite"
    },
    {
        key: "notice.subject.random"
    },
    {
        key: "common.close"
    },
    {
        key: "common.view"
    },
    {
        key: "common.copy.ok"
    },
    {
        key: "logistics.send.site.info"
    },
    {
        key: "logistics.send.site.subject.info"
    },
    {
        key: "logistics.send.depot.subject.info"
    },
    {
        key: "subject.dispensing.order.view"
    },
    {
        key: "subject.dispensing.detail"
    },
    {
        key: "subject.dispensing.apply.success"
    },
    {
        key: "subject.dispensing.dtp.success"
    },
    {
        key: "subject.dispensing.apply.order.tip"
    },
    {
        key: "subject.dispensing.apply.order.tip.apply"
    },
    {
        key: "subject.dispensing.apply.order.tip.to_send"
    },
    {
        key: "shipment.orderNumber"
    },
    {
        key: "calendar.button.today"
    },
    {
        key: "subject.edc.replace.error"
    },
    {
        key: "subject.edc.replace.failure.filter.failed"
    },
    {
        key: "subject.edc.replace.failure.exit"
    },
    {
        key: "subject.edc.replace.failure.complete.the.study"
    },
    {
        key: "subject.edc.replace.failure.screen.no.random"
    },
    {
        key: "subject.edc.replace.register.information.cohort"
    },
    {
        key: "subject.edc.replace.register.information.stage"
    },
    {
        key: "subject.replace.site.tip.title"
    },
    {
        key: "subject.replace.site.tip.content"
    },
    {
        key: "subject.replace.factor.tip.content"
    },
    {
        key: "subject.register.replace"
    },
    {
        key: "subject.number.replace"
    },
    {
        key: "subject.number.random.replace"
    },
    {
        key: "operation.subject.replace.confirm"
    },
    {
        key: "operation.subject.replace.confirm.is"
    },
    {
        key: "subject.replace.info.old"
    },
    {
        key: "operation.subject.beReplace.info"
    },
    {
        key: "subject.edc.screen.no.subject"
    },
    {
        key: "subject.screen.title"
    },
    {
        key: "subject.confirm.exited"
    },
    {
        key: "subject.sign.out"
    },
    {
        key: "subject.confirm.transport"
    },
    {
        key: "subject.confirm.transport.content"
    },
    {
        key: "subject.current.site"
    },
    {
        key: "subject.new.site"
    },
    {
        key: "subject.unblinding.application.alert",
        type: "warn"
    },
    {
        key: "subject.unblinding.sponsor"
    },
    {
        key: "subject.unblinding.reason"
    },
    {
        key: "subject.unblinding.reason.remark"
    },
    {
        key: "common.password"
    },
    {
        key: "subject.unblinding.confirmTip"
    },
    {
        key: "subject.unblinding.confirm"
    },
    {
        key: "common.print"
    },
    {
        key: "subject.unblinding.success"
    },
    {
        key: "subject.unblinding.approval"
    },
    {
        key: "subject.unblinding.application"
    },
    {
        key: "menu.projects.project.subject.urgent-unblinding.unblinding"
    },
    {
        key: "menu.projects.project.subject.urgent-unblinding.unblinding-pv"
    },
    {
        key: "subject.unblinding.application.type"
    },
    {
        key: "common.approval.confirm"
    },
    {
        key: "project.setting.checkbox.unblinded-code"
    },
    {
        key: "subject.unblinding.unblinded.code.confirm"
    },
    {
        key: "subject.unblinding.approval.number"
    },
    {
        key: "subject.urgentUnblindingApproval.pending"
    },
    {
        key: "subject.urgentUnblindingApproval.agree"
    },
    {
        key: "subject.urgentUnblindingApproval.reject"
    },
    {
        key: "subject.unblinding.approval.agree"
    },
    {
        key: "subject.unblinding.approval.reject"
    },
    {
        key: "common.reason"
    },
    {
        key: "menu.projects.project.subject.urgent-unblinding.approval-log"
    },
    {
        key: "project.task.urgent-unblinding.title"
    },
    {
        key: "subject.unblinding.application.result.title.pv"
    },
    {
        key: "subject.dispensing.apply.success"
    },
    {
        key: "subject.unblinding.application.info"
    },
    {
        key: "subject.unblinding.application.info.pv"
    },
    {
        key: "common.toBeApproved"
    },
    {
        key: "subject.unblinding.approval.success"
    },
    {
        key: "common.collapse"
    },
    {
        key: "common.expand"
    },
    {
        key: "subject.unblinding.application.resend"
    },
    {
        key: "group.cohort"
    },
    {
        key: "projects.randomization.groupList"
    },
    {
        key: "randomization.config.subGroup"
    },
    {
        key: "check.cohort"
    },
    {
        key: "subject.dispensing.visitSign"
    },
    {
        key: "subject.status.filtered",
        type: "option"
    },
    {
        key: "subject.status.invalid",
        type: "option"
    },
    {
        key: "drug.configure.formula.weight.last",
        type: "option"
    },
    {
        key: "drug.configure.formula.weight.actual",
        type: "option"
    },
    {
        key: "drug.configure.formula.weight.random",
        type: "option"
    },
    {
        key: "projects.subject.stratification"
    },
    {
        key: "projects.subject.form"
    },
    {
        key: "projects.subject.cohortName"
    },
    {
        key: "subject.switch.cohort"
    },
    {
        key: "subject.confirm.switch.cohort"
    },
    {
        key: "subject.confirm.switch.cohort.content"
    },
    {
        key: "subject.current.cohort"
    },
    {
        key: "subject.new.cohort"
    },
]