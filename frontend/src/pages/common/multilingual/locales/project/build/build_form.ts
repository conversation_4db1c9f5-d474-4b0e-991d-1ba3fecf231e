import {LocaleItem} from "../../../index";

export const build_form: LocaleItem[] = [
    {
        key: "form.onlyID",
        label: {
            cn: "表单ID",
            en: "Form ID"
        },
    },
    {
        key: "form.preview",
        label: {
            cn: "预览",
            en: "Preview"
        },
        type: "button"
    },
    {
        key: "form.field.name",
        label: {
            cn: "变量名称",
            en: "Variable Name"
        }
    },
    {
        key: "form.field.nameNew",
        label: {
            cn: "变量格式",
            en: "Variable format"
        }
    },
    {
        key: "form.field.label",
        label: {
            cn: "字段名称",
            en: "Field Name"
        }
    },
    {
        key: "form.application.type",
        label: {
            cn: "应用类型",
            en: "Application Type"
        }
    },
    {
        key: "form.application.type.register",
        label: {
            cn: "受试者登记",
            en: "Subject Registration"
        },
        type: "option"
    },
    {
        key: "form.application.type.formula",
        label: {
            cn: "公式计算",
            en: "Custom formula"
        },
        type: "option"
    },
    {
        key: "form.application.type.doseAdjustment",
        label: {
            cn: "剂量调整",
            en: "Dose Adjustment"
        },
        type: "option"
    },
    {
        key: "form.application.type.variable",
        label: {
            cn: "变量ID",
            en: "Variable ID"
        }
    },
    {
        key: "form.modify",
        label: {
            cn: "可修改",
            en: "Editable "
        },
        type: "option"
    },
    {
        key: "form.list.modify",
        label: {
            cn: "是否可修改",
            en: "Editable or Not"
        }
    },
    {
        key: "form.required",
        label: {
            cn: "必填",
            en: "Required"
        },
        type: "option"
    },
    {
        key: "form.control.type",
        label: {
            cn: "控件类型",
            en: "Control Type"
        }
    },
    {
        key: "form.control.type.input",
        label: {
            cn: "输入框",
            en: "Input Box"
        },
        type: "option"
    },
    {
        key: "form.control.type.inputNumber",
        label: {
            cn: "数字输入框",
            en: "Numeric Input Box"
        },
        type: "option"
    },
    {
        key: "form.control.type.textArea",
        label: {
            cn: "多行文本框",
            en: "Multiline Text Box"
        },
        type: "option"
    },
    {
        key: "form.control.type.checkbox",
        label: {
            cn: "复选框",
            en: "Checkbox"
        },
        type: "option"
    },
    {
        key: "form.control.type.radio",
        label: {
            cn: "单选框",
            en: "Radio Box"
        },
        type: "option"
    },
    {
        key: "form.control.type.switch",
        label: {
            cn: "开关",
            en: "Switch"
        },
        type: "option"
    },
    {
        key: "form.control.type.date",
        label: {
            cn: "日期选择框",
            en: "Date Selection Box"
        },
        type: "option"
    },
    {
        key: "form.control.type.dateTime",
        label: {
            cn: "时间选择框",
            en: "TimePickerDialog"
        },
        type: "option"
    },
    {
        key: "form.control.type.select",
        label: {
            cn: "下拉框",
            en: "Drop-down Box"
        },
        type: "option"
    },
    {
        key: "form.control.type.maximum",
        label: {
            cn: "受试者号位数",
            en: "Subject ID Digit"
        }
    },
    {
        key: "form.control.type.exact",
        label: {
            cn: "受试者号位数精确值",
            en: "Exact value for Subject ID"
        }
    },
    {
        key: "form.control.type.le",
        label: {
            cn: "小于等于",
            en: "Less than or Equal to"
        },
        type: "option"
    },
    {
        key: "form.control.type.eq",
        label: {
            cn: "等于",
            en: "Equal to"
        },
        type: "option"
    },
    {
        key: "form.control.type.options",
        label: {
            cn: "选项值",
            en: "Option"
        }
    },
    {
        key: "form.control.type.label",
        label: {
            cn: "名称",
            en: "Name"
        }
    },
    {
        key: "form.control.type.format",
        label: {
            cn: "格式类型",
            en: "Format type"
        }
    },
    {
        key: "form.control.type.format.characterLength",
        label: {
            cn: "字符长度",
            en: "Character Length"
        },
        type: "option"
    },
    {
        key: "form.control.type.format.numberLength",
        label: {
            cn: "数字长度",
            en: "Number Length"
        },
        type: "option"
    },
    {
        key: "form.control.type.format.decimalLength",
        label: {
            cn: "小数（整数+小数点+小数）",
            en: "Decimal Length"
        },
        type: "option"
    },
    {
        key: "form.control.type.format.checkbox",
        label: {
            cn: "多选框",
            en: "Checkbox"
        },
        type: "option"
    },
    {
        key: "form.control.type.format.limit",
        label: {
            cn: "字符长度限制{length}，请重新输入",
            en: "The character length is limited to {length},please re-enter."
        }
    },
    {
        key: "form.control.type.variableFormat",
        label: {
            cn: "变量格式",
            en: "Variable Format"
        }
    },
    {
        key: "form.control.type.variableRange",
        label: {
            cn: "变量范围",
            en: "Variable Range"
        }
    },
    {
        key: "form.control.type.variableRange.min",
        label: {
            cn: "请输入min值",
            en: "Please enter min value"
        }
    },
    {
        key: "form.control.type.variableRange.max",
        label: {
            cn: "请输入max值",
            en: "Please enter max value"
        }
    },
    {
        key: "form.control.type.variableRange.validate",
        label: {
            cn: "max值须大于等于min值，请重新输入",
            en: "The maximum value must be greater than or equal to the minimum value. please re-enter."
        }
    },
    {
        key: "form.control.type.variableRange.validate.range",
        label: {
            cn: "输入错误，超出变量格式范围。",
            en: "Input error, out of variable format range."
        }
    },
    {
        key: "form.control.type.options.lack",
        label: {
            cn: "缺少选项",
            en: "Lack of options"
        }
    },
    {
        key: "form.picker.max.tip",
        label: {
            cn: "系统会自动计算约束，最大值选择为当前时间。",
            en: "The system automatically calculates the constraint and the maximum value is selected as the current time."
        }
    },
    {
        key: "form.label.unblinding.code.available",
        label: {
            cn: "可用",
            en: "Available"
        },
        type: "tips"
    },
    {
        key: "form.label.unblinding.code.used",
        label: {
            cn: "已用",
            en: "Dispensed"
        }
    },
    {
        key: "form.label.unblinding.code.indivual",
        label: {
            cn: "个",
            en: " "
        }
    },
    {
        key: "form.control.type.options.duplicate",
        label: {
            cn: "选项值已存在",
            en: "Duplicated Option"
        }
    },
    {
        key: "form.control.type.options.tip.one",
        label: {
            cn: "剂量水平：选项值仅支持系统提供的枚举应用；",
            en: "Dose Level: Option values only support the enumerated applications provided by the system; "
        },
        type: "tips"
    },
    {
        key: "form.control.type.options.tip.two",
        label: {
            cn: "访视判断：支持自定义或系统提供的枚举应用。",
            en: "Visit Judgment: Supports custom or system-provided enumerated applications."
        },
        type: "tips"
    },
    {
        key: "form.control.type.options.one",
        label: {
            cn: "初始剂量",
            en: "Initial Dose"
        },
        type: "option"
    },
    {
        key: "form.control.type.options.two",
        label: {
            cn: "维持上一次的剂量",
            en: "Maintain the dose from the last time"
        }
    },
    {
        key: "form.control.type.options.three",
        label: {
            cn: "剂量减少，选择将受试者当前剂量降低一个剂量级别",
            en: "Dose reduction, choose to decrease the subject's current dose by one dose level"
        },
        type: "tips"
    },
    {
        key: "form.control.type.options.selectedRepeatedly",
        label: {
            cn: "选项值不可重复选择",
            en: "Options cannot be selected repeatedly"
        }
    }
]