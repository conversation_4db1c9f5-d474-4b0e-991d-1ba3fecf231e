export const build_random_simulate = [
    {
        key: "common.add"
    },
    {
        key: "common.edit"
    },
    {
        key: "form.onlyID",
        type: "tips"
    },
    {
        key: "common.ok"
    },
    {
        key: "projects.second"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "report.attributes.random.stage"
    },
    {
        key: "simulate_random.name"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "projects.randomization.list"
    },
    {
        key: "simulate_random.country.count"
    },
    {
        key: "simulate_random.region.count"
    },
    {
        key: "simulate_random.site.count"
    },
    {
        key: "simulate_random.run.count"
    },
    {
        key: "simulate_random.subject.count"
    },
    {
        key: "simulate_random.factor.ratio"
    },
    {
        key: "common.ratio",
        type: "placeholder"
    },
    {
        key: "simulate_random.site.detail.people.count"
    },
    {
        key: "simulate_random.site.detail.unbalanced"
    },
    {
        key: "simulate_random.project"
    },
    {
        key: "simulate_random.site.detail.total"
    },
    {
        key: "common.serial"
    },
    {
        key: "common.site"
    },
    {
        key: "randomization.config.factors"
    },
    {
        key: "projects.attributes.country"
    },
    {
        key: "projects.attributes.regionLayered"
    },
    {
        key: "simulate_random.combination.factor"
    },
    {
        key: "simulate_random.project.overview"
    },
    {
        key: "simulate_random.site.overview"
    },
    {
        key: "simulate_random.layered.overview"
    },
    {
        key: "simulate_random.country.overview"
    },
    {
        key: "simulate_random.region.overview"
    },
    {
        key: "simulate_random.combination.factor.overview"
    },
    {
        key: "simulate_random.detail.unbalanced"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.details"
    },
    {
        key: "simulate_random.detail.meanStandard"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "simulate_random.overview.avgsd"
    },
    {
        key: "simulate_random.overview.min"
    },
    {
        key: "simulate_random.overview.unbalanced.run.count"
    },
    {
        key: "simulate_random.run"
    },
    {
        key: "simulate_random.overview"
    },
    {
        key: "common.download"
    },
    {
        key: "simulate_random.all"
    },
    {
        key: "simulate_random.subject.count.min"
    },
    {
        key: "simulate_random.unbalanced.run.count"
    }
]