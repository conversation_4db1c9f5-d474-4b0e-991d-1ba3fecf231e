export const build_randomization_design = [
    {
        key: "projects.randomization.design"
    },
    {
        key: "projects.randomization.form"
    },
    {
        key: "tip.sync.title"
    },
    {
        key: "tip.sync.content"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.ok"
    },
    {
        key: "message.save.success"
    },
    {
        key: "common.confirm.delete"
    },
    {
        key: "randomization.random.list.invalid.doubt.title"
    },
    {
        key: "randomization.random.list.invalid.doubt"
    },
    {
        key: "randomization.random.list.invalid.doubt.info"
    },
    {
        key: "randomization.random.list.invalid.doubt.first"
    },
    {
        key: "randomization.random.list.invalid.doubt.second"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "report.download.success"
    },
    {
        key: "projects.randomization.settingRandom"
    },
    {
        key: "randomization.config.type"
    },
    {
        key: "projects.randomization.blockRandom"
    },
    {
        key: "projects.randomization.minimize"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.delete"
    },
    {
        key: "projects.randomization.group"
    },
    {
        key: "random.list.inactivating.success",
        type: "button"
    },
    {
        key: "common.sync"
    },
    {
        key: "randomization.config.code"
    },
    {
        key: "common.name"
    },
    {
        key: "randomization.config.subGroup"
    },
    {
        key: "randomization.config.subGroup.duplicate.all"
    },
    {
        key: "common.status"
    },
    {
        key: "common.invalid"
    },
    {
        key: "common.effective"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.addTo"
    },
    {
        key: "common.required.prefix"
    },
    {
        key: "randomization.config.region"
    },
    {
        key: "randomization.config.region.duplicate"
    },
    {
        key: "randomization.config.factor"
    },
    {
        key: "projects.attributes.Layered"
    },
    {
        key: "projects.attributes.country"
    },
    {
        key: "common.site"
    },
    {
        key: "projects.attributes.regionLayered"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "common.options"
    },
    {
        key: "common.all-options"
    },
    {
        key: "randomization.random.list.invalid"
    },
    {
        key: "common.history"
    },
    {
        key: "common.export"
    },
    {
        key: "projects.randomization.list"
    },
    {
        key: "common.upload"
    },
    {
        key: "common.generate"
    },
    {
        key: "common.serial"
    },
    {
        key: "common.site"
    },
    {
        key: "supply.plan.all.site"
    },
    {
        key: "projects.randomization.last.group"
    },
    {
        key: "common.operator"
    },
    {
        key: "common.operation.time"
    },
    {
        key: "projects.randomization.useCount"
    },
    {
        key: "projects.randomization.generateVerificationTips1",
        type: "warn"
    },
    {
        key: "projects.randomization.generateVerificationTips2",
        type: "warn"
    },
    {
        key: "projects.randomization.endValueTips"
    },
    {
        key: "projects.randomization.generate"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "projects.randomization.defaultNumber"
    },
    {
        key: "projects.randomization.generateGroupWeightRatio"
    },
    {
        key: "projects.randomization.groupList"
    },
    {
        key: "projects.randomization.generateWeightRatio"
    },
    {
        key: "projects.randomization.generateLayeredWeightRatio"
    },
    {
        key: "randomization.config.factors"
    },
    {
        key: "projects.randomization.blockConfig"
    },
    {
        key: "projects.randomization.blockLength"
    },
    {
        key: "projects.randomization.generateBlockNumber"
    },
    {
        key: "projects.randomization.probability"
    },
    {
        key: "projects.randomization.total"
    },
    {
        key: "projects.randomization.numberLength"
    },
    {
        key: "projects.randomization.blockRule"
    },
    {
        key: "projects.randomization.blockRule.order"
    },
    {
        key: "projects.randomization.blockRule.reverse"
    },
    {
        key: "projects.randomization.randomNumberRule"
    },
    {
        key: "projects.randomization.blockRule.order"
    },
    {
        key: "projects.randomization.blockRule.reverse"
    },
    {
        key: "projects.randomization.endNumber"
    },
    {
        key: "projects.randomization.seed"
    },
    {
        key: "projects.randomization.numberPrefix"
    },
    {
        key: "randomization.config.factorAdd"
    },
    {
        key: "randomization.config.factorEdit"
    },
    {
        key: "random.factor.alert",
        type: "warn"
    },
    {
        key: "randomization.config.factor.calc"
    },
    {
        key: "randomization.config.number"
    },
    {
        key: "randomization.config.factor.calc.formula"
    },
    {
        key: "randomization.config.factor.calc.formula.tip"
    },
    {
        key: "randomization.config.factor.calc.formula.tip.age"
    },
    {
        key: "randomization.config.factor.calc.formula.tip.bmi"
    },
    {
        key: "drug.configure.formula.age"
    },
    {
        key: "randomization.config.factor.calc.type.bmi"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions",
        type: "tips"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.keep.decimal.places"
    },
    {
        key: "randomization.config.factor.calc.formula.round.up"
    },
    {
        key: "randomization.config.factor.calc.formula.round.down"
    },
    {
        key: "randomization.config.factor.label"
    },
    {
        key: "randomization.config.factor.label.option.mapping"
    },
    {
        key: "projects.randomization.lockFactor"
    },
    {
        key: "randomization.config.factor.label.option.value.range"
    },
    {
        key: "randomization.config.factor.label.option.value"
    },
    {
        key: "form.field.label"
    },
    {
        key: "form.field.name"
    },
    {
        key: "form.control.type"
    },
    {
        key: "form.control.type.select"
    },
    {
        key: "form.control.type.radio"
    },
    {
        key: "form.control.type.options"
    },
    {
        key: "form.control.type.label"
    },
    {
        key: "randomization.config.factor.calc.formula.title1"
    },
    {
        key: "randomization.config.factor.calc.formula.title2"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.symbol"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.eg"
    },
    {
        key: "randomization.config.factor.calc.formula.footer1"
    },
    {
        key: "randomization.config.factor.calc.formula.footer2"
    },
    {
        key: "common.confirm.inactivate.group"
    },
    {
        key: "random.list.inactivating.tips"
    },
    {
        key: "randomization.config.subGroup.duplicate",
        type: "warn"
    },
    {
        key: "randomization.config.groupAdd"
    },
    {
        key: "randomization.config.groupEdit"
    },
    {
        key: "projects.attributes.isBlind.blind"
    },
    {
        key: "projects.randomization.block"
    },
    {
        key: "projects.randomization.randomCount"
    },
    {
        key: "projects.randomization.otherFactor"
    },
    {
        key: "projects.randomization.allocation.factor"
    },
    {
        key: "common.active"
    },
    {
        key: "common.inactivating"
    },
    {
        key: "projects.randomization.allocation.factorConfirm"
    },
    {
        key: "projects.randomization.allocation.block.activate.tip"
    },
    {
        key: "projects.randomization.allocation.block.Inactivate.tip"
    },
    {
        key: "projects.randomization.allocation.block.activate.content"
    },
    {
        key: "projects.randomization.allocation.block.Inactivate.content"
    },
    {
        key: "projects.randomization.allocation.randomNumber.activate.tip"
    },
    {
        key: "projects.randomization.allocation.randomNumber.Inactivate.tip"
    },
    {
        key: "projects.randomization.allocation.randomNumber.activate.content"
    },
    {
        key: "projects.randomization.allocation.randomNumber.Inactivate.content"
    },
    {
        key: "projects.randomization.planNumber"
    },
    {
        key: "projects.randomization.randomNumber"
    },
    {
        key: "subject.number"
    },
    {
        key: "common.site"
    },
    {
        key: "menu.projects.project.build.randomization.tooltip"
    },
    {
        key: "projects.randomization.distributionCenter"
    },
    {
        key: "projects.randomization.distributionCountry"
    },
    {
        key: "projects.randomization.distributionRegion"
    },
    {
        key: "common.between"
    },
    {
        key: "randomization.random.number.block.status.tip.1",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.2",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.3",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.4",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.5",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.6",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.7",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.8",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.9",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.10",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.status.tip.11",
        type: "tips"
    },
    {
        key: "randomization.random.number.block.all.usable"
    },
    {
        key: "randomization.random.number.block.partially.usable"
    },
    {
        key: "randomization.random.number.block.used"
    },
    {
        key: "randomization.random.number.block.invalid"
    },
    {
        key: "randomization.random.number.not.available"
    },
    {
        key: "randomization.random.number.unused"
    },
    {
        key: "randomization.random.number.not.available"
    },
    {
        key: "randomization.random.number.invalid"
    },
    {
        key: "menu.settings"
    },
    {
        key: "projects.randomization.distributionFactor"
    },
    {
        key: "projects.randomization.distributions"
    },
    {
        key: "projects.randomization.nullSite",
        type: "warn"
    },
    {
        key: "projects.randomization.nullCountry",
        type: "warn"
    },
    {
        key: "projects.randomization.nullRegion",
        type: "warn"
    },
    {
        key: "projects.randomization.nullFactor",
        type: "warn"
    },
    {
        key: "placeholder.select.search",
        type: "placeholder"
    },
    {
        key: "storehouse.name"
    },
    {
        key: "common.setting"
    },
    {
        key: "projects.attributes.subject.isRandom.title"
    },
    {
        key: "projects.attributes.subject.isRandom.tips",
        type: "tips"
    },
    {
        key: "common.condition"
    },
    {
        key: "randomization.config.type"
    },
    {
        key: "projects.randomization.groupConfig"
    },
    {
        key: "projects.randomization.weight"
    },
    {
        key: "randomization.config.factor"
    },
    {
        key: "projects.randomization.list"
    },
    {
        key: "projects.randomization.randomNumberTab"
    },
    {
        key: "projects.randomization.randomizationSubTab"
    },
    {
        key: "common.tips"
    },
    {
        key: "projects.randomization.confirmUpload",
        type: "warn"
    },
    {
        key: "common.upload.fileSize",
        type: "warn"
    },
    {
        key: "projects.randomization.upload"
    },
    {
        key: "randomization.config.groupSize"
    },
    {
        key: "randomization.config.groupSize.placeholder",
        type: "placeholder"
    },
    {
        key: "common.upload.add"
    },
    {
        key: "common.upload.excel.tip2"
    },
    {
        key: "common.download.template"
    },
    {
        key: "common.upload.excel.tip3"
    },
    {
        key: "common.upload.excel.tip4"
    },
    {
        key: "common.upload.excel.tip5"
    },
    {
        key: "common.upload.excel.tip6"
    },
    {
        key: "randomization.config.factorName"
    },
    {
        key: "projects.randomization.actualPeople"
    },
    {
        key: "projects.randomization.planPeople"
    },
    {
        key: "projects.randomization.alertPeople"
    },
    {
        key: "common.fail"
    },
    {
        key: "randomization.config.factor.addError"
    },
    {
        key: "projects.randomization.selectFactor"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.plus",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.sub",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.multiply",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.division",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.remainder",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.integer.power",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.brackets",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.square.root",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.cube.root",
        type: "option"
    }
]