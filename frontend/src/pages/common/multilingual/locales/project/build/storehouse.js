export const build_storehouse = [
    {
        key: "validator.message.phone"
    },
    {
        key: "common.add"
    },
    {
        key: "common.edit"
    },
    {
        key: "form.onlyID",
        type: "tips"
    },
    {
        key: "common.ok"
    },
    {
        key: "storehouse.name"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "storehouse.country.region"
    },
    {
        key: "common.contacts"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "common.phone"
    },
    {
        key: "common.email"
    },
    {
        key: "common.address"
    },
    {
        key: "projects.storehouse.connected"
    },
    {
        key: "projects.storehouse.logistics.supplier"
    },
    {
        key: "projects.storehouse.logistics.shengsheng"
    },
    {
        key: "projects.storehouse.logistics.catalent"
    },
    {
        key: "projects.storehouse.logistics.baicheng"
    },
    {
        key: "projects.storehouse.logistics.eDRUG"
    },
    {
        key: "projects.storehouse.not.included"
    },
    {
        key: "common.confirm.delete"
    },
    {
        key: "common.cancel"
    },
    {
        key: "storehouse.all"
    },
    {
        key: "projects.storehouse.name"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "storehouse.alert"
    },
    {
        key: "projects.supplyPlan.validityReminder"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.setting"
    },
    {
        key: "common.delete"
    },
    {
        key: "validator.msg.required"
    },
    {
        key: "common.serial"
    },
    {
        key: "common.addTo"
    }
]