export const build_treatment_visit_management = [
    {
        key: "project.overview.week"
    },
    {
        key: "project.overview.hour"
    },
    {
        key: "project.overview.month"
    },
    {
        key: "project.overview.day"
    },
    {
        key: "common.copy"
    },
    {
        key: "common.add"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.ok"
    },
    {
        key: "visit.cycle.visitNumber"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "visit.cycle.visitName"
    },
    {
        key: "visit.cycle.group"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "visit.cycle.interval"
    },
    {
        key: "visit.cycle.day.tip"
    },
    {
        key: "visit.cycle.period"
    },
    {
        key: "visit.cycle.dispensing"
    },
    {
        key: "visit.cycle.random"
    },
    {
        key: "visit.management.allowed.to.randomize"
    },
    {
        key: "visit.cycle.dtp"
    },
    {
        key: "logistics.send.site"
    },
    {
        key: "logistics.send.site.subject"
    },
    {
        key: "logistics.send.depot.subject"
    },
    {
        key: "visit.cycle.replace"
    },
    {
        key: "drug.configure.setting.dose.form.doseAdjustment"
    },
    {
        key: "visit.management.allowed.to.showDoseAdjustment"
    },
    {
        key: "common.confirm.delete"
    },
    {
        key: "common.serial"
    },
    {
        key: "visit.cycle.window"
    },
    {
        key: "common.yes"
    },
    {
        key: "common.no"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.delete"
    },
    {
        key: "visit.cycle.version"
    },
    {
        key: "visit.cycle.type"
    },
    {
        key: "visit.cycle.type.tip.baseline"
    },
    {
        key: "visit.cycle.type.tip.lastdate"
    },
    {
        key: "common.modify"
    },
    {
        key: "common.stage.name"
    },
    {
        key: "visit.cycle.push.record"
    },
    {
        key: "visit.cycle.management.is.group.visit.save.release.unpublished",
        type: "tips"
    },
    {
        key: "visit.cycle.push"
    },
    {
        key: "common.setting"
    },
    {
        key: "visit.cycle.version.number"
    },
    {
        key: "visit.cycle.push.date"
    },
    {
        key: "common.view"
    },
    {
        key: "common.success"
    },
    {
        key: "visit.cycle.setting.unscheduled_visit"
    },
    {
        key: "visit.cycle.setting.nameZh"
    },
    {
        key: "visit.cycle.setting.nameEn"
    },
    {
        key: "visit.cycle.push.tip"
    },
    {
        key: "visit.cycle.management.is.group.visit.release.save"
    },
    {
        key: "visit.cycle.management.is.group.visit.release.save.treatmentDesign"
    },
    {
        key: "visit.cycle.push.visit"
    },
    {
        key: "visit.cycle.push.tip"
    },
    {
        key: 'visit.cycle.management.is.group',
        type: 'warn',
    },
    {
        key: 'visit.cycle.management.drug.configure.is.group',
        type: 'warn',
    },
    {
        key: 'visit.cycle.management.drug.configure.is.group.label',
        type: 'warn',
    },
]