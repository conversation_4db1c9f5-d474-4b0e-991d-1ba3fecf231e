export const build_supply_plan = [
    {
        key: "common.confirm.delete"
    },
    {
        key: "common.ok"
    },
    {
        key: "common.cancel"
    },
    {
        key: "message.save.success"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.delete"
    },
    {
        key: "projects.supplyPlan.all"
    },
    {
        key: "common.add"
    },
    {
        key: "common.serial"
    },
    {
        key: "projects.supplyPlan.name"
    },
    {
        key: "common.status"
    },
    {
        key: "common.invalid"
    },
    {
        key: "common.effective"
    },
    {
        key: "projects.supplyPlan.description"
    },
    {
        key: "common.operation"
    },
    {
        key: "supply.plan.status.invalid.title"
    },
    {
        key: "supply.plan.status.invalid.content"
    },
    {
        key: "supply.plan.status.applicable.site.title"
    },
    {
        key: "supply.plan.status.applicable.site.content"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "supply.plan.applicable.site"
    },
    {
        key: "supply.plan.all.site"
    },
    {
        key: "projects.supplyPlan.control"
    },
    {
        key: "projects.supplyPlan.control.tips"
    },
    {
        key: "projects.supplyPlan.siteWarning"
    },
    {
        key: "projects.supplyPlan.auto"
    },
    {
        key: "projects.supplyPlan.max.alert"
    },
    {
        key: "projects.supplyPlan.unDistributionDate.err"
    },
    {
        key: "menu.projects.project.build.plan"
    },
    {
        key: "common.save"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "projects.supplyPlan.autoSupplySize"
    },
    {
        key: "projects.supplyPlan.secondSupply.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.forecast.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.buffer.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.na.tip",
        type: "tips"
    },
    {
        key: "shipment.mode.max"
    },
    {
        key: "shipment.mode.reSupply"
    },
    {
        key: "shipment.mode.forecast"
    },
    {
        key: "projects.supplyPlan.na.alert"
    },
    {
        key: "projects.supplyPlan.initSupply"
    },
    {
        key: "projects.supplyPlan.warning.site"
    },
    {
        key: "projects.supplyPlan.warning.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.warning"
    },
    {
        key: "projects.supplyPlan.warning.dispensing"
    },
    {
        key: "projects.supplyPlan.warning.dispensing.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.buffer"
    },
    {
        key: "projects.supplyPlan.order.fail"
    },
    {
        key: "projects.supplyPlan.secondSupply"
    },
    {
        key: "projects.supplyPlan.unDistributionDate"
    },
    {
        key: "projects.supplyPlan.unDistributionDate.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.unProvideDate"
    },
    {
        key: "projects.supplyPlan.unProvideDate.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.notCountedDate"
    },
    {
        key: "projects.supplyPlan.notCountedDate.tip",
        type: "tips"
    },
    {
        key: "projects.supplyPlan.validityReminder"
    },
    {
        key: "projects.supplyPlan.forecast"
    },
    {
        key: "projects.supplyPlan.supplyMode"
    },
    {
        key: "projects.supplyPlan.allSupply"
    },
    {
        key: "projects.supplyPlan.singleSupply"
    },
    {
        key: "projects.supplyPlan.allSupplyAndMedicine"
    },
    {
        key: "projects.supplyPlan.singleSupplyAndMedicine"
    },
    {
        key: "projects.supplyPlan.drugBlind",
        type: "option"
    },
    {
        key: "projects.supplyPlan.drugOpen",
        type: "option"
    },
    {
        key: "projects.supplyPlan.drugBlindAuto",
        type: "option"
    }
]