export const build_treatment_drug_batch = [
    {
        key: "menu.projects.project.build.randomization.tooltip"
    },
    {
        key: "drug.batch.management.update.info"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "common.setting"
    },
    {
        key: "common.update"
    },
    {
        key: "common.serial"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "projects.statistics.sku.place"
    },
    {
        key: "drug.other.singleCount"
    },
    {
        key: "common.type"
    },
    {
        key: "common.depot"
    },
    {
        key: "common.site"
    },
    {
        key: "batchMag.type.order"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.ok"
    },
    {
        key: "drug.batch.management.storeGroup.add"
    },
    {
        key: "visit.cycle.management.is.group.visit.batch.save"
    },
    {
        key: "common.success"
    },
    {
        key: "drug.batch.management.status"
    },
    {
        key: "common.update.count"
    },
    {
        key: "drug.batch.management.updateError"
    },
    {
        key: "common.select"
    },
    {
        key: "common.cancel"
    },
    {
        key: "drug.medicine"
    },
    {
        key: "drug.list.drugNumber"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "drug.list.serialNumber"
    },
    {
        key: "common.pagination.empty"
    },
    {
        key: "drug.batch.management.update"
    },
    {
        key: "drug.batch.management.updateExpireDate"
    },
    {
        key: "drug.medicine.packlist"
    },
    {
        key: "drug.list.status"
    },
    {
        key: "medicine.status.available",
        type: "option"
    },
    {
        key: "medicine.status.delivered",
        type: "option"
    },
    {
        key: "medicine.status.transit",
        type: "option"
    },
    {
        key: "medicine.status.quarantine",
        type: "option"
    },
    {
        key: "medicine.status.lose",
        type: "option"
    },
    {
        key: "medicine.status.expired",
        type: "option"
    },
    {
        key: "medicine.status.InOrder",
        type: "option"
    },
    {
        key: "medicine.status.fzn",
        type: "option"
    },
    {
        key: "medicine.status.locked",
        type: "option"
    }
]