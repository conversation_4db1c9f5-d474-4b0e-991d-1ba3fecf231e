export const build_site = [
    {
        key: "menu.projects.project.build.plan"
    },
    {
        key: "project.site.add_order_error",
        type: "warn"
    },
    {
        key: "projects.site.no.storehouse",
        type: "warn"
    },
    {
        key: "projects.site.supplyPlan.0init_supply",
        type: "warn"
    },
    {
        key: "projects.site.not.enough.medicine"
    },
    {
        key: "common.success"
    },
    {
        key: "common.ok"
    },
    {
        key: "storehouse.name"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "shipment.order.availableCount"
    },
    {
        key: "projects.supplyPlan.initSupply"
    },
    {
        key: "projects.supplyPlan.warning"
    },
    {
        key: "projects.site.medicine"
    },
    {
        key: "drug.list.batch",
        type: "placeholder"
    },
    {
        key: "common.add"
    },
    {
        key: "common.required.positiveInteger"
    },
    {
        key: "projects.randomization.groupList",
        type: "placeholder"
    },
    {
        key: "projects.randomization.alertPeople",
        type: "placeholder"
    },
    {
        key: "projects.randomization.upperLimitPeople",
        type: "placeholder"
    },
    {
        key: "common.addTo"
    },
    {
        key: "drug.batch.management.group.add"
    },
    {
        key: "drug.batch.management.group.del"
    },
    {
        key: "projects.site.no.supplyPlan",
        type: "warn"
    },
    {
        key: "common.sites"
    },
    {
        key: "projects.site.number"
    },
    {
        key: "projects.site.name"
    },
    {
        key: "common.country"
    },
    {
        key: "projects.storehouse.name"
    },
    {
        key: "projects.site.supply"
    },
    {
        key: "common.close"
    },
    {
        key: "projects.site.open"
    },
    {
        key: "common.status"
    },
    {
        key: "common.invalid"
    },
    {
        key: "common.effective"
    },
    {
        key: "common.operation"
    },
    {
        key: "operation.build.site.edit"
    },
    {
        key: "menu.projects.project.subject.dispensing"
    },
    {
        key: "common.cancel"
    },
    {
        key: "supply.plan.current"
    },
    {
        key: "common.serial"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "projects.supplyPlan.warning.dispensing"
    },
    {
        key: "projects.supplyPlan.buffer"
    },
    {
        key: "projects.supplyPlan.secondSupply"
    },
    {
        key: "projects.supplyPlan.unDistributionDate"
    },
    {
        key: "projects.supplyPlan.unProvideDate"
    },
    {
        key: "projects.supplyPlan.validityReminder"
    },
    {
        key: "projects.supplyPlan.autoSupplySize"
    },
    {
        key: "shipment.mode.max"
    },
    {
        key: "shipment.mode.reSupply"
    },
    {
        key: "shipment.mode.forecast"
    },
    {
        key: "projects.supplyPlan.supplyMode"
    },
    {
        key: "projects.supplyPlan.allSupply"
    },
    {
        key: "projects.supplyPlan.singleSupply"
    },
    {
        key: "projects.supplyPlan.allSupplyAndMedicine"
    },
    {
        key: "projects.supplyPlan.singleSupplyAndMedicine"
    },
    {
        key: "tip.site.disable.title"
    },
    {
        key: "tip.site.disable"
    },
    {
        key: "drug.medicine.setting.application.err5",
        type: "warn"
    },
    {
        key: "drug.medicine.setting.application.err2",
        type: "warn"
    },
    {
        key: "common.tips"
    },
    {
        key: "drug.medicine.setting.application.err1"
    },
    {
        key: "drug.medicine.setting.application.err4"
    },
    {
        key: "drug.medicine.setting.application.err3"
    },
    {
        key: "projects.site.no.supplyPlan.store",
        type: "warn"
    },
    {
        key: "common.edit"
    },
    {
        key: "input.error.only.number.letter"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "projects.site.name.standard"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "projects.site.short.name"
    },
    {
        key: "common.timezone"
    },
    {
        key: "projects.attributes.regionLayered"
    },
    {
        key: "drug.medicine.order.supply.ratio"
    },
    {
        key: "shipment.order.packageMethod.single"
    },
    {
        key: "shipment.order.packageMethod.package"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "common.contacts"
    },
    {
        key: "projects.site.isDefault.contact"
    },
    {
        key: "projects.contact.information",
        type: "placeholder"
    },
    {
        key: "common.email",
        type: "placeholder"
    },
    {
        key: "common.address",
        type: "placeholder"
    },
    {
        key: "projects.attributes.visit.inheritance"
    }
]