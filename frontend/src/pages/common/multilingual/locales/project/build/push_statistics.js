export const build_push_statistics = [
    {
        key: "project.statistics.subject.number"
    },
    {
        key: "common.status"
    },
    {
        key: "project.statistics.push.time"
    },
    {
        key: "project.edc.return"
    },
    {
        key: "project.statistics.resend"
    },
    {
        key: "common.details"
    },
    {
        key: "project.statistics.ls.resend"
    },
    {
        key: "project.statistics.operation.succeed"
    },
    {
        key: "common.ok"
    },
    {
        key: "common.cancel"
    },
    {
        key: "project.statistics.select.data",
        type: "warn"
    },
    {
        key: "project.statistics.push.history.empty",
        type: "warn"
    },
    {
        key: "project.statistics.push.history.confirm"
    },
    {
        key: "subject.register"
    },
    {
        key: "projects.subject.update.front"
    },
    {
        key: "projects.subject.update.after"
    },
    {
        key: "subject.screen"
    },
    {
        key: "projects.attributes.dispensing.yes"
    },
    {
        key: "subject.random"
    },
    {
        key: "project.statistics.push.history.scenario"
    },
    {
        key: "project.statistics.out-visit-dispensing"
    },
    {
        key: "project.statistics.all"
    },
    {
        key: "common.time.start",
        type: "placeholder"
    },
    {
        key: "common.time.end",
        type: "placeholder"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "project.statistics.pushing"
    },
    {
        key: "project.statistics.succeeded"
    },
    {
        key: "project.edc.failed"
    },
    {
        key: "project.statistics.lose"
    },
    {
        key: "project.edc.processing"
    },
    {
        key: "project.statistics.enter.number",
        type: "placeholder"
    },
    {
        key: "project.statistics.push.history"
    },
    {
        key: "project.statistics.batch.send"
    },
    {
        key: "common.serial"
    },
    {
        key: "project.statistics.push.item"
    },
    {
        key: "project.statistics.push.mode"
    },
    {
        key: "project.statistics.retry.times"
    },
    {
        key: "common.operation"
    },
    {
        key: "project.statistics.failed",
        type: "option"
    },
    {
        key: "project.received.failed",
        type: "option"
    },
    {
        key: "project.statistics.unknown",
        type: "option"
    },
    {
        key: "subject.register",
        type: "option"
    },
    {
        key: "project.statistics.update",
        type: "option"
    },
    {
        key: "project.statistics.randomize",
        type: "option"
    },
    {
        key: "project.statistics.dispense",
        type: "option"
    },
    {
        key: "project.statistics.replace",
        type: "option"
    },
    {
        key: "project.statistics.reissue",
        type: "option"
    },
    {
        key: "project.statistics.cancel",
        type: "option"
    },
    {
        key: "project.statistics.retrieval",
        type: "option"
    },
    {
        key: "project.statistics.realDispensing",
        type: "option"
    },
    {
        key: "subject.register.replace",
        type: "option"
    },
    {
        key: "report.attributes.random.actual.factor",
        type: "option"
    },
    {
        key: "project.statistics.screen",
        type: "option"
    },
    {
        key: "project.statistics.system.auto",
        type: "option"
    },
    {
        key: "project.statistics.manual.push",
        type: "option"
    }
]