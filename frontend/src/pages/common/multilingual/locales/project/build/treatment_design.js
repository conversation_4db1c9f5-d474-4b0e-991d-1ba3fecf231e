export const build_treatment_design = [
    {
        key: "common.tips"
    },
    {
        key: "drug.configure.delete"
    },
    {
        key: "common.ok"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.setting"
    },
    {
        key: "common.add"
    },
    {
        key: "common.serial"
    },
    {
        key: "drug.configure.group"
    },
    {
        key: "drug.configure.subGroup"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "drug.configure.visitName"
    },
    {
        key: "drug.configure.drugLabel.config"
    },
    {
        key: "drug.configure.roomNumber"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.delete"
    },
    {
        key: "common.operation.error"
    },
    {
        key: "drug.configure.label.config.duplicate.error",
        type: "warn"
    },
    {
        key: "drug.configure.visitCycles.config.error"
    },
    {
        key: "drug.configure.label.config.error"
    },
    {
        key: "input.error.common"
    },
    {
        key: "drug.configure.visitCycles.name.err"
    },
    {
        key: "form.onlyID",
        type: "tips"
    },
    {
        key: "drug.configure.ip.tip"
    },
    {
        key: "drug.configure.sub-labels.tip"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "logistics.dispensing.method"
    },
    {
        key: "drug.configure.open.tip.one",
        type: "tips"
    },
    {
        key: "drug.configure.open.tip.two",
        type: "tips"
    },
    {
        key: "drug.configure.open.tip.three",
        type: "tips"
    },
    {
        key: "drug.configure.open.tip.four",
        type: "tips"
    },
    {
        key: "drug.configure.open.tip.six",
        type: "tips"
    },
    {
        key: "drug.configure.open.tip.seven",
        type: "tips"
    },
    {
        key: "drug.batch.treatmentDesign.openSetting"
    },
    {
        key: "drug.configure.label.config"
    },
    {
        key: "drug.configure.open.config"
    },
    {
        key: "drug.configure.formula.config"
    },
    {
        key: "drug.configure.formula"
    },
    {
        key: "drug.configure.formula.tip.one",
        type: "tips"
    },
    {
        key: "drug.configure.formula.tip.two",
        type: "tips"
    },
    {
        key: "drug.configure.formula.tip.three",
        type: "tips"
    },
    {
        key: "drug.batch.treatmentDesign.formula"
    },
    {
        key: "drug.configure.formula.age"
    },
    {
        key: "drug.configure.formula.weight"
    },
    {
        key: "drug.configure.formula.bsa"
    },
    {
        key: "drug.configure.formula.other"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula"
    },
    {
        key: "common.required.prefix"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions",
        type: "tips"
    },
    {
        key: "drug.configure.spec",
        type: "placeholder"
    },
    {
        key: "drug.configure"
    },
    {
        key: "drug.configure.drugNumber"
    },
    {
        key: "drug.configure.formula.unit.capacity",
        type: "placeholder"
    },
    {
        key: "drug.configure.drugSpecifications",
        type: "placeholder"
    },
    {
        key: "drug.configure.PkgSpecifications",
        type: "placeholder"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "drug.configure.formula.standard",
        type: "placeholder"
    },
    {
        key: "drug.other"
    },
    {
        key: "drug.configure.other.tip",
        type: "tips"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.automatic.recode"
    },
    {
        key: "drug.configure.formula.unit",
        type: "placeholder"
    },
    {
        key: "projects.supplyPlan.drugOpen"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.keep.decimal.places"
    },
    {
        key: "common.addTo"
    },
    {
        key: "drug.configure.showAll"
    },
    {
        key: "drug.configure.drugLabel"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "drug.configure.labels.tip"
    },
    {
        key: "drug.configure.routine.visit.mapping"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.title"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.symbol"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.eg"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.footer"
    },
    {
        key: "logistics.send.site"
    },
    {
        key: "logistics.send.site.subject"
    },
    {
        key: "logistics.send.depot.subject"
    },
    {
        key: "drug.configure.setting.dose.form.list.name.selectedRepeatedly",
        type: "warn"
    },
    {
        key: "subject.dispensing.selectedRepeatedly"
    },
    {
        key: "common.name"
    },
    {
        key: "drug.configure.setting.dose.form.list.group"
    },
    {
        key: "drug.configure.setting.dose.form.list.dose.distribution"
    },
    {
        key: "form.control.type.options.one"
    },
    {
        key: "drug.configure.setting.dose.form.list.name"
    },
    {
        key: "drug.configure.setting.dose.form.list.visit.inheritance"
    },
    {
        key: "common.occasions"
    },
    {
        key: "logistics.dispensing.dtpIp"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "projects.attributes.dtp.rule.ip.tooltip",
        type: "tips"
    },
    {
        key: "drug.configure.setting.dose.form.doseAdjustment"
    },
    {
        key: "drug.configure.setting.dose.form.type"
    },
    {
        key: "drug.configure.setting.dose.level"
    },
    {
        key: "drug.configure.setting.dose.level.desc"
    },
    {
        key: "drug.configure.setting.dose.visit.judgment"
    },
    {
        key: "drug.configure.setting.dose.visit.judgment.desc"
    },
    {
        key: "drug.configure.setting.dose.form"
    },
    {
        key: "drug.configure.setting.dose.level.set"
    },
    {
        key: "form.control.type.options.one",
        type: "warn"
    },
    {
        key: "drug.configure.setting.dose.level.set.tip1"
    },
    {
        key: "drug.configure.setting.dose.form.list.isFirstInitial"
    },
    {
        key: "drug.configure.setting.dose.form.list.isDoseReduction"
    },
    {
        key: "drug.configure.setting.dose.form.list.visit.inheritance.dispensing"
    },
    {
        key: "drug.configure.setting.dose.form.list.visit.inheritance.stop"
    },
    {
        key: "drug.configure.setting.dose.form.list.visit.inheritance.main"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.compared.with"
    },
    {
        key: "drug.batch.treatmentDesign.formula.weight.last.calculation"
    },
    {
        key: "drug.batch.treatmentDesign.formula.weight.last.actual"
    },
    {
        key: "drug.batch.treatmentDesign.formula.weight.random"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.is.used.for.this.calculation"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.the.change.is"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.weight.title1",
        type: "tips"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.weight.title2",
        type: "tips"
    },
    {
        key: "drug.configure.formula.age.range",
        type: "placeholder"
    },
    {
        key: "drug.configure.formula.weight.range",
        type: "placeholder"
    },
    {
        key: "drug.configure.formula.age.name"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.plus",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.sub",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.multiply",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.division",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.remainder",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.integer.power",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.brackets",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.square.root",
        type: "option"
    },
    {
        key: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.cube.root",
        type: "option"
    }
]