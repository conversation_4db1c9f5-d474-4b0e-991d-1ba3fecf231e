export const home_task = [
    {
        key: "project.task"
    },
    {
        key: "common.all"
    },
    {
        key: "project.task.status.notStarted"
    },
    {
        key: "project.task.addOrder.title"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.ok"
    },
    {
        key: "project.task.status"
    },
    {
        key: "project.task.estimatedCompletionTime"
    },
    {
        key: "project.task.approvalTime"
    },
    {
        key: "shipment.basic.information"
    },
    {
        key: "shipment.send"
    },
    {
        key: "shipment.receive"
    },
    {
        key: "projects.supplyPlan.supplyMode"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "shipment.supply"
    },
    {
        key: "shipment.supply.count"
    },
    {
        key: "common.contacts"
    },
    {
        key: "shipment.expectedArrivalTime"
    },
    {
        key: "drug.medicine"
    },
    {
        key: "shipment.blindCount"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "drug.other"
    },
    {
        key: "shipment.approval.confirm.title"
    },
    {
        key: "common.print"
    },
    {
        key: "shipment.approval.confirm"
    },
    {
        key: "common.reason"
    },
    {
        key: "project.task.approvalStatus.pass"
    },
    {
        key: "project.task.approvalStatus.reject"
    },
    {
        key: "shipment.order.packageMethod.package"
    },
    {
        key: "shipment.order.packageMethod.single"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "shipment.order.availableCount"
    },
    {
        key: "shipment.order.package.method"
    },
    {
        key: "drug.freeze.count"
    },
    {
        key: "menu.projects.project.subject.urgent-unblinding.unblinding"
    },
    {
        key: "menu.projects.project.subject.urgent-unblinding.unblinding-pv"
    },
    {
        key: "subject.unblinding.approval.number"
    },
    {
        key: "subject.urgentUnblindingApproval.pending"
    },
    {
        key: "medicine.status.toBeApproved"
    },
    {
        key: "subject.urgentUnblindingApproval.agree"
    },
    {
        key: "subject.urgentUnblindingApproval.reject"
    },
    {
        key: "subject.number"
    },
    {
        key: "projects.randomization.randomNumber"
    },
    {
        key: "subject.unblinding.reason"
    },
    {
        key: "shipment.mode.set",
        type: "option"
    },
    {
        key: "shipment.mode.reSupply",
        type: "option"
    },
    {
        key: "shipment.mode.max",
        type: "option"
    },
    {
        key: "shipment.mode.supplyRatio",
        type: "option"
    },
    {
        key: "project.task.approvalStatus.notStarted",
        type: "option"
    }
]