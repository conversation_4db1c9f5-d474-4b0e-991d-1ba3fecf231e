export const supply_freeze = [
    {
        key: "shipment.order.packageMethod.package"
    },
    {
        key: "shipment.order.packageMethod.single"
    },
    {
        key: "drug.isolation.quantity"
    },
    {
        key: "common.status"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "shipment.other.drug"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "shipment.order.package.method"
    },
    {
        key: "drug.isolation.single.quantity"
    },
    {
        key: "drug.isolation.package.quantity"
    },
    {
        key: "drug.freeze.all"
    },
    {
        key: "drug.freeze.institute"
    },
    {
        key: "common.all",
        type: "placeholder"
    },
    {
        key: "common.serial"
    },
    {
        key: "drug.freeze.number"
    },
    {
        key: "drug.freeze.startDate"
    },
    {
        key: "drug.freeze.count"
    },
    {
        key: "common.operation"
    },
    {
        key: "drug.list.release"
    },
    {
        key: "project.de.isolation.approval"
    },
    {
        key: "medicine.status.lose"
    },
    {
        key: "common.history"
    },
    {
        key: "subject.dispensing.selectDrugNumber",
        type: "warn"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.ok"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "drug.list.drugNumber",
        type: "placeholder"
    },
    {
        key: "drug.other.count"
    },
    {
        key: "project.reason.for.de.isolation"
    },
    {
        key: "drug.freeze.reason"
    },
    {
        key: "shipment.approval.confirm"
    },
    {
        key: "subject.unblinding.approval.agree"
    },
    {
        key: "subject.unblinding.approval.reject"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    }
]