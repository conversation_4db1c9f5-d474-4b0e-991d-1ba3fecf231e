export const supply_shipment = [
    {
        key: "shipment.order.supplyUnset.info"
    },
    {
        key: "shipment.order.sendAndReceive.info",
        type: "warn"
    },
    {
        key: "shipment.order.fail.title"
    },
    {
        key: "shipment.order.create.info",
        type: "warn"
    },
    {
        key: "shipment.order.create.count.info",
        type: "warn"
    },
    {
        key: "common.success"
    },
    {
        key: "shipment.order.success.title"
    },
    {
        key: "shipment.order.page.fail"
    },
    {
        key: "common.add"
    },
    {
        key: "common.ok"
    },
    {
        key: "shipment.basic.information"
    },
    {
        key: "shipment.send"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "shipment.receive"
    },
    {
        key: "projects.supplyPlan.supplyMode"
    },
    {
        key: "shipment.mode.set"
    },
    {
        key: "shipment.mode.reSupply"
    },
    {
        key: "shipment.mode.max"
    },
    {
        key: "shipment.mode.supplyRatio"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "shipment.supply"
    },
    {
        key: "common.contacts"
    },
    {
        key: "shipment.supply.count"
    },
    {
        key: "shipment.expectedArrivalTime"
    },
    {
        key: "drug.medicine"
    },
    {
        key: "shipment.blindCount"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "shipment.other.drug"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "shipment.order.availableCount"
    },
    {
        key: "shipment.order.package.method"
    },
    {
        key: "drug.freeze.count"
    },
    {
        key: "shipment.order.packageMethod.package"
    },
    {
        key: "shipment.order.packageMethod.single"
    },
    {
        key: "shipment.approvalTask"
    },
    {
        key: "common.print"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "drug.other"
    },
    {
        key: "shipment.approval.confirm.title"
    },
    {
        key: "shipment.approval.confirm"
    },
    {
        key: "drug.freeze.reason"
    },
    {
        key: "subject.unblinding.approval.number"
    },
    {
        key: "subject.urgentUnblindingApproval.pending"
    },
    {
        key: "project.task.approvalStatus.passed"
    },
    {
        key: "project.task.approvalStatus.rejected"
    },
    {
        key: "common.edit.batch"
    },
    {
        key: "common.cancel"
    },
    {
        key: "shipment.order.medicine.batch.info"
    },
    {
        key: "shipment.order.change.count.info",
        type: "warn"
    },
    {
        key: "shipment.order.mediciens.changeRecords"
    },
    {
        key: "shipment.order.medicine.change.info"
    },
    {
        key: "drug.medicine.packlist"
    },
    {
        key: "drug.list.drugNumber"
    },
    {
        key: "shipment.status"
    },
    {
        key: "barcode"
    },
    {
        key: "form.preview"
    },
    {
        key: "packageBarcode"
    },
    {
        key: "shipment.order.medicines.changeCount"
    },
    {
        key: "common.close"
    },
    {
        key: "shipment.order.medicines.change.records"
    },
    {
        key: "shipment.order.change.success"
    },
    {
        key: "shipment.order.medicine.change.success.info"
    },
    {
        key: "common.serial"
    },
    {
        key: "shipment.order.old.medicine"
    },
    {
        key: "shipment.order.new.medicine"
    },
    {
        key: "shipment.order.change.operTime"
    },
    {
        key: "common.operator"
    },
    {
        key: "shipment.confirm.select",
        type: "warn"
    },
    {
        key: "shipment.confirm.order"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "drug.freeze.confirm.count"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "shipment.transit.order"
    },
    {
        key: "shipment.oper.confirm.order"
    },
    {
        key: "shipment.transit"
    },
    {
        key: "shipment.received"
    },
    {
        key: "shipment.end"
    },
    {
        key: "shipment.end-dtp"
    },
    {
        key: "shipment.lose"
    },
    {
        key: "shipment.lose-dtp"
    },
    {
        key: "common.history"
    },
    {
        key: "common.detail"
    },
    {
        key: "shipment.order.all"
    },
    {
        key: "shipment.orderNumber",
        type: "tips"
    },
    {
        key: "placeholder.select.search",
        type: "tips"
    },
    {
        key: "shipment.status.timeout",
        type: "option"
    },
    {
        key: "shipment.store.alarm"
    },
    {
        key: "common.download"
    },
    {
        key: "projects.sitePharmacy.order.medicineQuantity"
    },
    {
        key: "common.operation"
    },
    {
        key: "menu.projects.project.build.randomization.tooltip"
    },
    {
        key: "shipment.order.medicines"
    },
    {
        key: "shipment.order.medicines.change.records"
    },
    {
        key: "shipment.order.medicines.change"
    },
    {
        key: "common.statistics"
    },
    {
        key: "common.edit"
    },
    {
        key: "drug.freeze.receive.count"
    },
    {
        key: "shipment.receive.reason"
    },
    {
        key: "shipment.close.order"
    },
    {
        key: "shipment.lose.order"
    },
    {
        key: "shipment.end.order"
    },
    {
        key: "shipment.cancel.order"
    },
    {
        key: "model.note.title.order.dtp.receive"
    },
    {
        key: "common.previous"
    },
    {
        key: "common.next"
    },
    {
        key: "shipment.order.received"
    },
    {
        key: "drug.freeze.form-item.reason"
    },
    {
        key: "subject.unblinding.reason.remark"
    },
    {
        key: "shipment.order.received.info"
    },
    {
        key: "shipment.actualReceiptTime"
    },
    {
        key: "shipment.receive.confirm.order"
    },
    {
        key: "shipment.isolation.confirm.order"
    },
    {
        key: "drug.freeze.receiveFreeze.count"
    },
    {
        key: "shipment.cancel.order"
    },
    {
        key: "common.copy.ok"
    },
    {
        key: "common.modify"
    },
    {
        key: "projects.contact.information"
    },
    {
        key: "common.email"
    },
    {
        key: "common.address"
    },
    {
        key: "logistics.info"
    },
    {
        key: "logistics.info.isTransit"
    },
    {
        key: "common.yes"
    },
    {
        key: "common.no"
    },
    {
        key: "logistics.confirm.info"
    },
    {
        key: "common.required.prefix.search",
        type: "placeholder"
    },
    {
        key: "logistics.info.name"
    },
    {
        key: "logistics.number"
    },
    {
        key: "common.details"
    },
    {
        key: "medicine.status.delivered",
        type: "option"
    },
    {
        key: "medicine.status.transit",
        type: "option"
    },
    {
        key: "shipment.status.received",
        type: "option"
    },
    {
        key: "shipment.status.lose",
        type: "option"
    },
    {
        key: "shipment.status.cancelled",
        type: "option"
    },
    {
        key: "medicine.status.InOrder",
        type: "option"
    },
    {
        key: "medicine.status.apply",
        type: "option"
    },
    {
        key: "projects.status.terminate",
        type: "option"
    },
    {
        key: "projects.status.close",
        type: "option"
    },
    {
        key: "project.task.approvalStatus.notStarted",
        type: "option"
    },
    {
        key: "project.task.approvalStatus.pass",
        type: "option"
    },
    {
        key: "project.task.approvalStatus.reject",
        type: "option"
    }
]