export const supply_shipment_return = [
    {
        key: "shipment.order.sendAndReceive.info",
        type: "warn"
    },
    {
        key: "shipment.order.create.info",
        type: "warn"
    },
    {
        key: "shipment.order.create.count.info",
        type: "warn"
    },
    {
        key: "shipment.order.packageMethod.package"
    },
    {
        key: "shipment.order.packageMethod.single"
    },
    {
        key: "common.add"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.ok"
    },
    {
        key: "shipment.basic.information"
    },
    {
        key: "shipment.send"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "shipment.receive"
    },
    {
        key: "shipment.expectedArrivalTime"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "shipment.other.drug"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "shipment.order.package.method"
    },
    {
        key: "drug.list.status"
    },
    {
        key: "shipment.order.availableCount"
    },
    {
        key: "drug.freeze.count"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "drug.list.availableCount"
    },
    {
        key: "drug.list.number.placeholder",
        type: "tips"
    },
    {
        key: "drug.medicine.packlist"
    },
    {
        key: "drug.list.drugNumber"
    },
    {
        key: "common.pagination.tip.total"
    },
    {
        key: "common.pagination.tip.pagesize-prefix"
    },
    {
        key: "common.pagination.tip.pagesize-suffix"
    },
    {
        key: "common.pagination.seleted"
    },
    {
        key: "common.pagination.record"
    },
    {
        key: "common.pagination.all"
    },
    {
        key: "common.pagination.empty"
    },
    {
        key: "shipment.confirm.select",
        type: "warn"
    },
    {
        key: "shipment.confirm.order"
    },
    {
        key: "shipment.oper.confirm.order"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "drug.other"
    },
    {
        key: "drug.freeze.confirm.count"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "common.tips"
    },
    {
        key: "shipment.cancel.order"
    },
    {
        key: "shipment.lose.order"
    },
    {
        key: "shipment.end.order"
    },
    {
        key: "shipment.close.order"
    },
    {
        key: "common.confirm"
    },
    {
        key: "common.close"
    },
    {
        key: "shipment.transit.order",
        type: "button"
    },
    {
        key: "shipment.transit"
    },
    {
        key: "shipment.received"
    },
    {
        key: "shipment.end"
    },
    {
        key: "shipment.lose"
    },
    {
        key: "common.history"
    },
    {
        key: "common.details"
    },
    {
        key: "shipment.order.all"
    },
    {
        key: "shipment.orderNumber",
        type: "tips"
    },
    {
        key: "placeholder.select.search",
        type: "placeholder"
    },
    {
        key: "shipment.status"
    },
    {
        key: "shipment.status.timeout",
        type: "option"
    },
    {
        key: "common.download"
    },
    {
        key: "common.serial"
    },
    {
        key: "projects.sitePharmacy.order.medicineQuantity"
    },
    {
        key: "common.operation"
    },
    {
        key: "menu.projects.project.build.randomization.tooltip"
    },
    {
        key: "shipment.order.medicines"
    },
    {
        key: "shipment.order.medicines.change.records"
    },
    {
        key: "shipment.order.medicines.change"
    },
    {
        key: "common.statistics"
    },
    {
        key: "barcode"
    },
    {
        key: "form.preview"
    },
    {
        key: "packageBarcode"
    },
    {
        key: "drug.freeze.receive.count"
    },
    {
        key: "drug.freeze.reason"
    },
    {
        key: "common.previous"
    },
    {
        key: "common.next"
    },
    {
        key: "shipment.order.received"
    },
    {
        key: "drug.freeze.form-item.reason"
    },
    {
        key: "shipment.order.received.info"
    },
    {
        key: "shipment.actualReceiptTime"
    },
    {
        key: "drug.medicine"
    },
    {
        key: "shipment.receive.confirm.order"
    },
    {
        key: "shipment.isolation.confirm.order"
    },
    {
        key: "drug.freeze.receiveFreeze.count"
    },
    {
        key: "common.copy.ok"
    },
    {
        key: "common.modify"
    },
    {
        key: "logistics.info"
    },
    {
        key: "logistics.info.isTransit"
    },
    {
        key: "common.yes"
    },
    {
        key: "common.no"
    },
    {
        key: "logistics.confirm.info"
    },
    {
        key: "common.required.prefix.search",
        type: "placeholder"
    },
    {
        key: "logistics.info.name"
    },
    {
        key: "logistics.number"
    },
    {
        key: "common.details"
    },
    {
        key: "medicine.status.toBeWarehoused",
        type: "option"
    },
    {
        key: "medicine.status.available",
        type: "option"
    },
    {
        key: "medicine.status.delivered",
        type: "option"
    },
    {
        key: "medicine.status.transit",
        type: "option"
    },
    {
        key: "medicine.status.quarantine",
        type: "option"
    },
    {
        key: "medicine.status.used",
        type: "option"
    },
    {
        key: "medicine.status.lose",
        type: "option"
    },
    {
        key: "medicine.status.expired",
        type: "option"
    },
    {
        key: "medicine.status.receive",
        type: "option"
    },
    {
        key: "medicine.status.return",
        type: "option"
    },
    {
        key: "medicine.status.destroy",
        type: "option"
    },
    {
        key: "medicine.status.InOrder",
        type: "option"
    },
    {
        key: "medicine.status.apply",
        type: "option"
    },
    {
        key: "medicine.status.fzn",
        type: "option"
    },
    {
        key: "medicine.status.locked",
        type: "option"
    },
    {
        key: "medicine.status.dsm",
        type: "option"
    },
    {
        key: "medicine.status.dsh",
        type: "option"
    },
    {
        key: "medicine.status.shsb",
        type: "option"
    },
    {
        key: "medicine.status.delivered",
        type: "option"
    },
    {
        key: "medicine.status.transit",
        type: "option"
    },
    {
        key: "shipment.status.received",
        type: "option"
    },
    {
        key: "shipment.status.lose",
        type: "option"
    },
    {
        key: "shipment.status.cancelled",
        type: "option"
    },
    {
        key: "medicine.status.InOrder",
        type: "option"
    },
    {
        key: "medicine.status.apply",
        type: "option"
    },
    {
        key: "projects.status.terminate",
        type: "option"
    },
    {
        key: "projects.status.close",
        type: "option"
    }
]