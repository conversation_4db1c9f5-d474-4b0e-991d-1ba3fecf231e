export const supply_site = [
    {
        key: "projects.storehouse.statistics.summary"
    },
    {
        key: "projects.storehouse.statistics.sku"
    },
    {
        key: "projects.storehouse.statistics.other.sku"
    },
    {
        key: "single.freeze.setUse.info"
    },
    {
        key: "operation.supply.storehouse.medicine.use"
    },
    {
        key: "common.ok"
    },
    {
        key: "drug.list.drugNumber"
    },
    {
        key: "common.serial"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "drug.medicine.packlist"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "drug.list.status"
    },
    {
        key: "drug.freeze.reason"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "projects.statistics.sku.expirationDate"
    },
    {
        key: "menu.projects.project.build.randomization.tooltip"
    },
    {
        key: "projects.other.sku.freeze.info"
    },
    {
        key: "projects.other.sku.lost.info"
    },
    {
        key: "common.site"
    },
    {
        key: "projects.statistics.selectField",
        type: "placeholder"
    },
    {
        key: "medicine.status.lose"
    },
    {
        key: "drug.list.isolation"
    },
    {
        key: "projects.other.package.info"
    },
    {
        key: "common.site"
    },
    {
        key: "medicine.status.available"
    },
    {
        key: "medicine.status.InOrder"
    },
    {
        key: "medicine.status.delivered"
    },
    {
        key: "medicine.status.transit"
    },
    {
        key: "medicine.status.quarantine"
    },
    {
        key: "medicine.status.used"
    },
    {
        key: "medicine.status.lose"
    },
    {
        key: "medicine.status.expired"
    },
    {
        key: "medicine.status.fzn"
    },
    {
        key: "medicine.status.locked"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.history"
    },
    {
        key: "drug.list.setUse"
    },
    {
        key: "single.freeze.freeze.info"
    },
    {
        key: "single.freeze.lost.info"
    },
    {
        key: "subject.dispensing.placeholder.input.count",
        type: "warn"
    },
    {
        key: "shipment.order.packageMethod.package"
    },
    {
        key: "shipment.order.packageMethod.single"
    },
    {
        key: "drug.other"
    },
    {
        key: "shipment.order.package.method"
    },
    {
        key: "projects.other.sku.freeze.single"
    },
    {
        key: "projects.other.sku.lost.single"
    },
    {
        key: "shipment.orderNumber"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "common.status"
    },
    {
        key: "common.all",
        type: "placeholder"
    },
    {
        key: "common.download.data"
    },
    {
        key: "shipment.order.all-no"
    },
    {
        key: "projects.statistics.sku.status"
    },
    {
        key: "barcode"
    },
    {
        key: "form.preview"
    },
    {
        key: "packageBarcode"
    },
    {
        key: "drug.configure.spec"
    },
    {
        key: "projects.sitePharmacy.forecast"
    },
    {
        key: "drug.list.name"
    },
    {
        key: "projects.sitePharmacy.no.site"
    }
]