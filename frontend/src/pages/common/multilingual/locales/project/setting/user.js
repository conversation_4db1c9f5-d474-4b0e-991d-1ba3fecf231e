export const setting_user = [
    {
        key: "common.depot"
    },
    {
        key: "common.serial"
    },
    {
        key: "projects.storehouse.name"
    },
    {
        key: "common.operation"
    },
    {
        key: "user.batch.add.email.required",
        type: "placeholder"
    },
    {
        key: "common.success"
    },
    {
        key: "common.email.language.zh"
    },
    {
        key: "common.email.language.en"
    },
    {
        key: "user.settings.reauthorization"
    },
    {
        key: "user.settings.invite-again"
    },
    {
        key: "common.ok"
    },
    {
        key: "common.email.language"
    },
    {
        key: "placeholder.select.common.email.language"
    },
    {
        key: "validator.msg.number"
    },
    {
        key: "common.authorization.success"
    },
    {
        key: "env.user.edit.section1"
    },
    {
        key: "modal.title.user.role"
    },
    {
        key: "common.add"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.email"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "common.role"
    },
    {
        key: "project.setting.checkbox.unblinded-code"
    },
    {
        key: "form.label.unblinding.code.available"
    },
    {
        key: "form.label.unblinding.code.indivual"
    },
    {
        key: "form.label.unblinding.code.used"
    },
    {
        key: "common.tips"
    },
    {
        key: "common.back.revise"
    },
    {
        key: "common.ignore.continue"
    },
    {
        key: "user.batch.add.email.tip1"
    },
    {
        key: "user.batch.add.email.tip2"
    },
    {
        key: "common.reason"
    },
    {
        key: "env.user.edit.section3"
    },
    {
        key: "env.user.edit.section4"
    },
    {
        key: "env.user.edit.section5"
    },
    {
        key: "common.user.add.configure.permissions"
    },
    {
        key: "common.full_name"
    },
    {
        key: "common.site"
    },
    {
        key: "common.setting.batch"
    },
    {
        key: "common.all.select"
    },
    {
        key: "common.user.add.success"
    },
    {
        key: "common.user.add.configure.permissions.tip"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "common.confirm.unbinding"
    },
    {
        key: "tip.user.unbind"
    },
    {
        key: "common.invalid"
    },
    {
        key: "user.status.open"
    },
    {
        key: "common.effective"
    },
    {
        key: "common.unbinding"
    },
    {
        key: "common.unbinding.batch"
    },
    {
        key: "common.history"
    },
    {
        key: "projects.users.all"
    },
    {
        key: "common.download.data"
    },
    {
        key: "user.name"
    },
    {
        key: "common.status"
    },
    {
        key: "project.user.status.Valid",
        type: "tips"
    },
    {
        key: "project.user.status.Inactivated",
        type: "tips"
    },
    {
        key: "project.user.status.Invalid",
        type: "tips"
    },
    {
        key: "common.created.at"
    },
    {
        key: "user.app.account"
    },
    {
        key: "common.copy"
    },
    {
        key: "common.copy.ok"
    }
]

