export const setting_notice = [
    {
        key: "notice.basic.settings",
    },
    {
        key: "notice.subject.add",
    },
    {
        key: "notice.subject.random",
    },
    {
        key: "notice.subject.signOut",
    },
    {
        key: "notice.subject.replace",
    },
    {
        key: "notice.subject.screen",
    },
    {
        key: "notice.subject.update",
    },
    {
        key: "notice.subject.dispensing",
    },
    {
        key: "notice.subject.alarm",
    },
    {
        key: "notice.subject.unblinding",
    },
    {
        key: "notice.medicine.isolation",
    },
    {
        key: "notice.medicine.order",
    },
    {
        key: "notice.medicine.reminder",
    },
    {
        key: "notice.medicine.alarm",
    },
    {
        key: "notice.storehouse.alarm",
    },
    {
        key: "notice.order.timeout",
    },
    {
        key: "notice.subject.alert.threshold",
    },
    {
        key: "notice.subject.medicine.alarm",
    },
    {
        key: "notice.subject.medicine.capping",
    },
    {
        key: "common.serial"
    },
    {
        key: "common.email"
    },
    {
        key: "common.details"
    },
    {
        key: "notice.exclude_recipient_list.email.account"
    },
    {
        key: "notice.exclude_recipient_list.email.batch"
    },
    {
        key: "notice.exclude_recipient_list.email.batch.tip"
    },
    {
        key: "common.download.template"
    },
    {
        key: "common.delete"
    },
    {
        key: "user.batch.add.email.required",
        type: "placeholder"
    },
    {
        key: "common.email.language.zh"
    },
    {
        key: "common.email.language.en"
    },
    {
        key: "common.tips"
    },
    {
        key: "common.back.revise"
    },
    {
        key: "common.ignore.continue"
    },
    {
        key: "notice.exclude_recipient_list.email.tip1"
    },
    {
        key: "notice.exclude_recipient_list.email.tip2"
    },
    {
        key: "common.upload.fileSize",
        type: "warn"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.save"
    },
    {
        key: "input.error.common"
    },
    {
        key: "common.email.language"
    },
    {
        key: "common.task.automatic"
    },
    {
        key: "form.required"
    },
    {
        key: "common.task.manual"
    },
    {
        key: "notice.config.content"
    },
    {
        key: "notice.config.state"
    },
    {
        key: "notice.alarm.scene.forecast.time"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "order.timeout.days"
    },
    {
        key: "order.send.days"
    },
    {
        key: "common.role"
    },
    {
        key: "notice.exclude_recipient_list.email"
    },
    {
        key: "notice.exclude_recipient_list.email.tip3",
        type: "tips"
    },
    {
        key: "projects.name",
        type: "option"
    },
    {
        key: "projects.number",
        type: "option"
    },
    {
        key: "projects.site.name",
        type: "option"
    },
    {
        key: "projects.site.number",
        type: "option"
    },
    {
        key: "shipment.expectedArrivalTime",
        type: "option"
    },
    {
        key: "notice.content.group",
        type: "option"
    },
    {
        key: "notice.config.content.group",
        type: "option"
    },
    {
        key: "notice.content.number",
        type: "option"
    },
    {
        key: "notice.state.dispensing",
        type: "option"
    },
    {
        key: "notice.state.unscheduled",
        type: "option"
    },
    {
        key: "notice.state.re.dispensing",
        type: "option"
    },
    {
        key: "notice.state.replace",
        type: "option"
    },
    {
        key: "project.statistics.retrieval",
        type: "option"
    },
    {
        key: "notice.state.register",
        type: "option"
    },
    {
        key: "notice.state.not-attend",
        type: "option"
    },
    {
        key: "notice.state.form_factor",
        type: "option"
    },
    {
        key: "notice.state.screen",
        type: "option"
    },
    {
        key: "notice.state.stop",
        type: "option"
    },
    {
        key: "subject.number",
        type: "option"
    },
    {
        key: "subject.status.screen.success",
        type: "option"
    },
    {
        key: "subject.status.screen.fail",
        type: "option"
    },
    {
        key: "notice.order.scene.apply",
        type: "option"
    },
    {
        key: "notice.order.tip",
        type: "option"
    },
    {
        key: "notice.order.scene.applyFail",
        type: "option"
    },
    {
        key: "notice.order.scene.create",
        type: "option"
    },
    {
        key: "common.cancel",
        type: "option"
    },
    {
        key: "notice.order.scene.confirm",
        type: "option"
    },
    {
        key: "common.close",
        type: "option"
    },
    {
        key: "shipment.transit",
        type: "option"
    },
    {
        key: "shipment.received",
        type: "option"
    },
    {
        key: "shipment.end",
        type: "option"
    },
    {
        key: "shipment.lose",
        type: "option"
    },
    {
        key: "notice.order.scene.autoCreate",
        type: "option"
    },
    {
        key: "notice.order.scene.autoCreateFail",
        type: "option"
    },
    {
        key: "notice.order.scene.autoAlarm",
        type: "option"
    },
    {
        key: "notice.order.scene.change",
        type: "option"
    },
    {
        key: "notice.order.scene.batch",
        type: "option"
    },
    {
        key: "notice.alarm.scene.stock",
        type: "option"
    },
    {
        key: "notice.alarm.scene.forecast",
        type: "option"
    },
    {
        key: "drug.list.isolation",
        type: "option"
    },
    {
        key: "notice.order.scene.release",
        type: "option"
    }
]