export const main = {
    key: "main",
    name: "main",
    dir: ["pages/main"],
    // excludeDirs: [["projects"]],
    children: [
        {key: "main_home", name: "menu.home", dir: ['home']},
        {key: "main_project", name: "menu.projects", dir: ['projects/table.jsx', 'projects/add.tsx']},
        {key: "main_multiLanguage", name: "menu.projects.project.multiLanguage", dir: ['multiLanguage']},
        {key: "main_report", name: "menu.report", dir: ['report']},
        {
            key: "main_settings",
            name: "menu.settings",
            dir: ['settings'],
            children: [
                {key: "main_depots", name: "menu.settings.storehouse", dir: ['depots']},
                {key: "main_roles", name: "menu.settings.roles", dir: ['role']},
                {key: "main_users", name: "menu.settings.users", dir: ['user']},
            ]
        },
        {key: "main_layout", name: "layout", dir: ['layout']},
    ]
}

export const common = {
    key: "common",
    name: "common",
    dir: ["pages/common"],
    excludeDirs: [["multilingual"]],
    children: []
}

export const components = {
    key: "component",
    name: "component",
    dir: ["components"],
    children: []
}