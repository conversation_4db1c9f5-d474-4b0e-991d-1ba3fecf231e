export const project = {
    key: 'project_detail',
    name: 'menu.projects.project',
    dir: 'pages/project',
    excludeDirs: [['check'], ['settings', 'export'], ['build', 'code_rule'], ['layout']],
    discardPage: ['pages_project_home_main.tsx', 'pages_project_build_drug_pack-list-upload.tsx'],
    children: [
        {
            key: 'home',
            name: 'menu.projects.project.home',
            dir: 'home',
            children: [
                {
                    key: "home_overview",
                    name: "menu.projects.project.overview",
                    dir: ['overview.tsx', 'project_time_zone.tsx']
                },
                {
                    key: "home_random",
                    name: "menu.projects.project.random.statistics",
                    dir: ['random_statistics.tsx', 'cohort_select.tsx']
                },
                {
                    key: "home_subject",
                    name: "menu.projects.project.subject.statistics",
                    dir: ['subject_statistics.tsx']
                },
                {
                    key: "home_depot_ip",
                    name: "menu.projects.project.depot.ip.statistics",
                    dir: ['depot_Ip_statistics.jsx']
                },
                {
                    key: "home_site_ip",
                    name: "menu.projects.project.site.ip.statistics",
                    dir: ['site_Ip_statistics.jsx']
                },
                {
                    key: "home_analysis",
                    name: "menu.projects.project.analysis",
                    dir: ['analysis.tsx']
                },
                {
                    key: "home_dynamics",
                    name: "menu.projects.project.dynamics",
                    dir: ['project_dynamics.tsx']
                },
                {
                    key: "home_task",
                    name: "menu.projects.project.task",
                    dir: ['project_approval_task.tsx', 'approval_detail.tsx', 'unblinding_approval_detail.tsx']
                },
            ]
        },
        {
            key: "subject",
            name: "menu.projects.project.sub",
            dir: 'sub',
            children: [
                {key: "subject_subjects", name: "menu.projects.project.subject", dir: 'subject'},
                {key: "subject_visit", name: "menu.projects.project.subject.visit.cycle", dir: 'visit_cycle'}
            ]
        },
        {
            key: "supply",
            name: "menu.projects.project.supply",
            dir: 'supply',
            children: [
                {key: "supply_depot", name: "menu.projects.project.supply.storehouse", dir: 'storehouse_statistics'},
                {key: "supply_site", name: "menu.projects.project.supply.site", dir: 'site_pharmacy'},
                {key: "supply_shipment", name: "menu.projects.project.supply.shipment", dir: ['shipment']},
                {key: "supply_shipment_return", name: "menu.projects.project.supply.drug_recovery", dir: 'drug_recovery'},
                {key: "supply_freeze", dir: 'drug_freeze', name: "menu.projects.project.supply.release-record"},
            ]
        },
        {
            key: "build",
            name: "menu.projects.project.build",
            dir: 'build',
            children: [
                {key: "build_storehouse", name: "menu.projects.project.build.storehouse", dir: 'storehouse'},
                {key: "build_site", name: "menu.projects.project.build.site", dir: 'site'},
                {key: "build_attribute", name: "menu.projects.project.build.attributes", dir: 'attribute'},
                {key: "build_push_statistics", name: "menu.projects.project.build.push", dir: 'push'},
                {key: "build_random_simulate", name: "menu.projects.project.build.simulate_random", dir: 'simulate_random'},
                {
                    key: "build_randomization",
                    name: "menu.projects.project.build.randomization",
                    dir: 'randomization',
                    children: [
                        {
                            key: "build_randomization_design",
                            name: "menu.projects.project.build.randomization.design",
                            dir: [
                                'index.tsx', 'main.tsx', 'random-generate.tsx', 'random-list-factor.tsx',
                                'random-list-group.tsx', 'random-list-update.tsx', 'random-number-allocation.tsx',
                                'random-number-allocations.tsx', 'random-number-allocations-limit.tsx',
                                'random-number-configure.tsx', 'random-number-index.tsx', 'random-upload.tsx',
                                'randomization-factor.tsx',
                            ]
                        },
                        {
                            key: "build_randomization_form",
                            name: "menu.projects.project.build.randomization.form",
                            dir: ['form-add.tsx', 'form-index.tsx', 'form-view.tsx']
                        }
                    ]
                },
                {
                    key: "build_treatment",
                    name: "menu.projects.project.build.drug",
                    dir: 'drug',
                    children: [
                        {
                            key: "build_visit_management",
                            name: "menu.projects.project.build.drug.visit",
                            dir: ['visit-add.tsx', 'visit-index.tsx', 'visit-setting.tsx', 'visit-view.tsx']
                        },
                        {
                            key: "build_treatment_design",
                            name: "menu.projects.project.build.drug.config",
                            dir: [
                                'drug-configure-index.tsx', 'drug-configure-add.tsx', 'drug-configure-setting.tsx',
                                'formula.tsx'
                            ]
                        },
                        {
                            key: "build_barcode",
                            name: "menu.projects.project.build.drug.barcode",
                            dir: ['barcode-add.tsx', 'barcode-index.tsx']
                        },
                        {
                            key: "build_ip_list",
                            name: "menu.projects.project.build.drug.list",
                            dir: [
                                'medicine-list.jsx', 'medicine-folw-path.tsx', 'medicine-list-upload.tsx',
                                'package-mixed.tsx', 'package-setting.tsx'
                            ]
                        },
                        {
                            key: "build_ip_no_number",
                            name: "menu.projects.project.build.drug.no_number",
                            dir: ['medicine-other.tsx', 'medicine-other-add.tsx']
                        },
                        {

                            key: "build_drug_batch",
                            name: "menu.projects.project.build.drug.batch",
                            dir: [
                                'batch-management.tsx', 'batch-management-edit.tsx',
                                'batch-management-multipleEdit.tsx', 'medicine-data.tsx',
                            ]
                        },
                    ]
                },
                {key: "build_supply_plan", name: "menu.projects.project.build.plan", dir: 'plan'},
                {key: "build_history", name: "menu.projects.project.build.history", dir: 'history_info'},
            ]
        },
        {
            key: "setting",
            name: "menu.projects.project.settings",
            dir: 'settings',
            children: [
                {key: "setting_notice", name: "menu.projects.project.settings.notice", dir: 'notice',},
                {key: "setting_user", name: "menu.projects.project.settings.user", dir: 'user'},
                /*{key: "setting_export", name: "menu.projects.project.settings.export", dir: 'export'},*/
            ]
        },
    ]
}