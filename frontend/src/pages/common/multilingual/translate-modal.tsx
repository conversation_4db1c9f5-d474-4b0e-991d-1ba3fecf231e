import React, {useImperative<PERSON>andle, useRef} from "react"
import {Form, Input, InputRef, message, Modal} from "antd"
import {useSafeState} from "ahooks";
import {useAtom} from "jotai";
import {customLanguageAtom, customLocales<PERSON>tom} from "./context";
import {useFetch} from "../../../hooks/request";
import {updateTranslate} from "../../../api/multi_language";
import {useIntl} from "react-intl";
import {getLocalesData} from "./util";
import {LanguageLibraryOption} from "../../main/multiLanguage/translate/data";

export const TranslateModal = (props: { bind: any }) => {

    const {formatMessage} = useIntl()
    const [visible, setVisible] = useSafeState<boolean>(false)
    const [field, setField] = useSafeState<any>({})
    const [flatModules, setFlatModules] = useSafeState<any[]>([])
    const [flatTypes, setFlatTypes] = useSafeState<any[]>([])
    const inputRef = useRef<InputRef>(null);
    const [translateValue, setTranslateValue] = useSafeState<string>('')

    const { runAsync: doUpdateTranslate, loading } = useFetch(updateTranslate,{ manual: true });
    const [translateMessages, setTranslateMessages] = useAtom(customLocalesAtom)
    const [languageInfo] = useAtom(customLanguageAtom)

    useImperativeHandle(props.bind, () => ({ show }))

    const show = (key: string) => {
        const {data, flatModules, flatTypes} = getLocalesData(languageInfo.lang)
        const field = data.find(it => it.key === key)
        if (!field) return
        setField(field)
        setFlatModules(flatModules)
        setFlatTypes(flatTypes)
        setTranslateValue(translateMessages[key] || '')
        setVisible(true)
        inputFocus()
    }

    const hide = () => {
        setVisible(false)
    }

    const inputFocus = () => {
        setTimeout(() => {
            inputRef?.current?.focus()
        }, 0)
    }


    //单个翻译编辑
    const translate = ()=> {
        // 只有系统库使用
        const languageLibrary = LanguageLibraryOption.find(it => it.value === 1)
        const path = flatModules.filter(p => field.path?.includes(p.value))
        const type = flatTypes.find(it => it.value === field.type)

        doUpdateTranslate({ customerId: languageInfo.customerId, projectId: languageInfo.projectId },
            {
                id: languageInfo.id,
                languageId: languageInfo.id,
                customerId: languageInfo.customerId,
                projectId: languageInfo.projectId,
                languageLibrary: 1,
                envId: null,
                cohortId: null,
                pagePath: path,
                type: field.type,
                key: field.key,
                name: translateValue,
                languageValue: languageInfo.language,
                languageLibraryValueZh: languageLibrary?.labelCn,
                languageLibraryValueEn: languageLibrary?.labelEn,
                pagePathValueZh: path.map(it => it.label).join(' / '),
                pagePathValueEn: path.map(it => it.labelEn).join(' / '),
                typeValueZh: type?.label,
                typeValueEn: type?.labelEn,
                nameValueZh: field.defaultLabel?.cn,
                nameValueEn: field.defaultLabel?.en,
            }
        ).then(() => {
            setTranslateMessages({...translateMessages, [field.key]: translateValue})
            message.success(formatMessage({ id: 'message.save.success' }))
            hide()
        })
    }

    return <Modal
        open={visible}
        centered
        onCancel={hide}
        title={'自定义翻译'}
        closable
        className={'custom-tips-modal-with-content'}
        style={{minHeight: 400, maxHeight: 600}}
        maskClosable={false}
        onOk={translate}
        okButtonProps={{loading: loading}}
        zIndex={2002}
    >
        <Form
            labelCol={{ span: 5 }}
        >
            <Form.Item label={'English'}>{field.defaultLabel?.en}</Form.Item>
            <Form.Item label={languageInfo.language}>
                <Input
                    onClick={e => inputFocus()}
                    ref={inputRef}
                    value={translateValue}
                    onChange={(e) => setTranslateValue(e.target.value)}
                />
            </Form.Item>
        </Form>
    </Modal>
}