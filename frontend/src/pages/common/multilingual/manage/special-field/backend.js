// 后端返回的字段，需要在模块中维护
const backendReturn = {
    "project.attribute.subject_number_rule.info2": "受试者筛选流程需保持一致，已同步其他群组配置。",
    "project.attribute.subject_number_rule.info3": "受试者筛选流程需保持一致，已同步其它阶段配置。",
    "project.attribute.subject_number_rule.info4": "受试者号规则需保持一致，已同步其他群组配置。",
    "project.attribute.subject_number_rule.info5": "受试者号规则需保持一致，已同步其它阶段配置。",
    "project.attribute.subject_number_rule.info6": "受试者筛选流程/受试者号规则需保持一致，已同步其他群组配置。",
    "project.attribute.subject_number_rule.info7": "受试者筛选流程/受试者号规则需保持一致，已同步其它阶段配置。",
    "project.external.edc.rave.mapping.randomizationCode": "随机号",
    "project.external.edc.rave.mapping.randomizationTime": "随机时间",
    "project.external.edc.rave.mapping.group": "组别",
    "project.external.edc.rave.mapping.factor": "分层因素值",
    "project.external.edc.rave.mapping.cohort": "群组/再随机",
    // 报表
    "source.ip.upload.history.name": "盲底名称",
    "source.ip.upload.history.rows": "行数",
    // 其他设置-通知设置
    "notice.basic.settings": "基本设置",
    "notice.subject.add": "受试者登记",
    "notice.subject.signOut": "受试者停用",
    "notice.subject.replace": "受试者替换",
    "notice.subject.screen": "受试者筛选",
    "notice.subject.update": "受试者修改",
    "notice.subject.dispensing": "受试者发放",
    "notice.subject.alarm": "受试者警戒",
    "notice.medicine.isolation": "研究产品隔离",
    "notice.medicine.order": "研究产品订单",
    "notice.medicine.reminder": "研究产品有效期",
    "notice.medicine.alarm": "中心库存警戒",
    "notice.storehouse.alarm": "库房库存警戒",
    "notice.order.timeout": "订单超时",
    "notice.subject.medicine.alarm": "受试者库房/中心警戒提醒",
    "notice.subject.medicine.capping": "受试者库房/中心上限提醒",
    "notice.subject.alert.threshold": "受试者上限设置",
    // 研究产品管理-访视管理
    "visit.cycle.management.is.group": "组别已修改，请重新映射访视与组别的关系。",
    "visit.cycle.management.drug.configure.is.group": "组别已修改，请重新映射研究产品名称与组别的关系。",
    "visit.cycle.management.drug.configure.is.group.label": "研究产品配置已修改，请重新配置发放剂量。",
    // 受试者-访视管理
    "subject.visit.item.outsize": "超窗",
    "subject.visit.item.undone": "未完成",
    "subject.visit.item.in.progress": "进行中",
    "subject.visit.item.completed": "已完成",
    "subject.visit.item.has.not.started": "未开始",
    "subject.visit.item.on.schedule": "按期",
    "subject.dispensing.selectDrugNumber.label": "请选择研究产品标签",
    "project.task.urgent-unblinding.title": "揭盲（紧急）申请",
    "project.task.pv-unblinding.title": "揭盲（PV）申请",
    "subject.edc.replace.register.information.cohort": "群组",
    "subject.edc.replace.register.information.stage": "阶段",

    "menu.message.center.single": "消息",


    // 好像没用到了？
    "simulate_random.site.details": "中心详情",
    "history.randomization": "随机配置轨迹",
    "history.order": "订单轨迹",
    "history.dispensing": "发放轨迹",
    "history.subject": "受试者轨迹",
    "history.medicine": "研究产品轨迹",
    "history.users": "人员管理轨迹",
    "history.medicine.drugFreeze": "轨迹",

    // 需要改页面
    "medicine.status.sending": "已运送",
    "medicine.status.stockPending": "待入库",
    "medicine.status.inStorage": "入库中",
    "medicine.status.frozen": "已冻结",



}

export const backendKeys = Object.keys(backendReturn)