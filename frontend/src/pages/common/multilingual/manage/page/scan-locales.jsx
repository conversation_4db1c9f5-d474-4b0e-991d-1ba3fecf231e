import React, {useEffect, useState} from 'react'
import {LocalesTable} from "./table";
import {allFields, allModules} from "../../locales";
import {Button} from "antd";
import {unuseKeys} from "../special-field/unuse";
import {message as enmessage} from "../../../../../locales/en";
import {message as zhmessage} from "../../../../../locales/zh";
import _ from "lodash";
import {useAtom} from "jotai";
import {localesDataAtom} from "./context";

// 翻译不完全的字段
export const LocalesScanLocales = (props) => {
    const [data, setData] = useState([])
    const [showData, setShowData] = useState([])
    const [localesData] = useAtom(localesDataAtom)

    useEffect(() => {
        // TODO 增加类型和路径获取
        const data = Object.keys(zhmessage).map(key => ({key: key, value: zhmessage[key]}))
        setData(data)
        setShowData(data)
    }, [])


    const unMange = () => {
        const allKeys = allFields.map(it => it.key)
        setShowData(data.filter(it => !allKeys.includes(it.key) && !unuseKeys.includes(it.key)))
    }

    // 已经废弃的字段
    const unuseField = () => {
        setShowData(data.filter(it => unuseKeys.includes(it.key)))
    }

    const unTranslation = () => {
        setShowData(data.filter(it => !zhmessage[it.key] || !enmessage[it.key] || (zhmessage[it.key] === enmessage[it.key])))
    }

    const translationIsSame = () => {
        const counts = _.countBy(data, 'value')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        const showData = repeatNames.reduce((res, item) => {
            res = res.concat(data.filter(it => it.value === item))
            return res
        }, [])
        setShowData(showData)
    }

    const unuseButInMange = () => {
        const allKeys = allFields.map(it => it.key)
        setShowData(data.filter(it => unuseKeys.includes(it.key) && allKeys.includes(it.key)))
    }

    const noScan = () => {
        const scanKeys = localesData.scanFile.map(it => it.key)
        setShowData(data.filter(it => !scanKeys.includes(it.key)))
    }


    return <div>
        <div style={{marginBottom: '20px'}}>
            <Button.Group>
                <Button onClick={unTranslation}>未完全翻译字段</Button>
                <Button onClick={translationIsSame}>翻译一致的字段</Button>
                <Button onClick={unMange}>未纳入管理的字段</Button>
                <Button onClick={unuseField}>已经废弃的字段</Button>
                <Button onClick={unuseButInMange}>应该废弃但还是纳入管理的字段</Button>
                <Button onClick={noScan}>没有扫描出来的字段</Button>
                <Button onClick={() => setShowData(_.uniqBy(data, 'key'))}>去重</Button>
                <Button onClick={() => setShowData(data)}>重置</Button>
            </Button.Group>
        </div>
        <LocalesTable dataSource={showData} modules={allModules} />
    </div>

}