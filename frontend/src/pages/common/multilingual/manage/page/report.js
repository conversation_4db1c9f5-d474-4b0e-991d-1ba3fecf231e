import _ from "lodash";

const reportList = [
    {
        "name": "menu.report.randomizeReport",
        "defaultFields": [],
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.info.status"
                    },
                    {
                        "key": "report.attributes.random.factor"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.random.time"
                    },
                    {
                        "key": "report.attributes.random.group"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.random.config.code"
                    },
                    {
                        "key": "report.attributes.random.subject.number.replace"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.number"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.status"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.time"
                    },
                    {
                        "key": "report.attributes.random.sign.out.real.time"
                    },
                    {
                        "key": "report.attributes.random.finish.remark"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.info.status"
                    },
                    {
                        "key": "report.attributes.random.factor"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.random.time"
                    },
                    {
                        "key": "report.attributes.random.group"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.random.config.code"
                    },
                    {
                        "key": "report.attributes.random.subject.number.replace"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.number"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.status"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.time"
                    },
                    {
                        "key": "report.attributes.random.sign.out.real.time"
                    },
                    {
                        "key": "report.attributes.random.finish.remark"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.info.status"
                    },
                    {
                        "key": "report.attributes.random.factor"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.random.time"
                    },
                    {
                        "key": "report.attributes.random.group"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.random.config.code"
                    },
                    {
                        "key": "report.attributes.random.subject.number.replace"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.number"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.status"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.time"
                    },
                    {
                        "key": "report.attributes.random.sign.out.real.time"
                    },
                    {
                        "key": "report.attributes.random.finish.remark"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    }
                ]
            }
        ],
        "type": 14,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info",
                "children": [
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.info.status"
                    }
                ]
            },
            {
                "key": "report.attributes.random",
                "children": [
                    {
                        "key": "report.attributes.random.factor"
                    },
                    {
                        "key": "report.attributes.random.time"
                    },
                    {
                        "key": "report.attributes.random.group"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.random.register.time"
                    },
                    {
                        "key": "report.attributes.random.register.operator"
                    },
                    {
                        "key": "report.attributes.random.operator"
                    },
                    {
                        "key": "report.attributes.random.stage",
                        "required": true
                    },
                    {
                        "key": "report.attributes.random.subject.number.replace"
                    },
                    {
                        "key": "report.attributes.random.form"
                    },
                    {
                        "key": "report.attributes.random.factor.calc"
                    },
                    {
                        "key": "report.attributes.random.actual.factor"
                    },
                    {
                        "key": "report.attributes.random.cohort",
                        "required": true
                    },
                    {
                        "key": "report.attributes.random.subject.replace.number"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.status"
                    },
                    {
                        "key": "report.attributes.random.subject.replace.time"
                    },
                    {
                        "key": "report.attributes.random.config.code"
                    },
                    {
                        "key": "report.attributes.random.sign.out.operator"
                    },
                    {
                        "key": "report.attributes.random.sign.out.time"
                    },
                    {
                        "key": "report.attributes.random.sign.out.real.time"
                    },
                    {
                        "key": "report.attributes.random.sign.out.reason"
                    },
                    {
                        "key": "report.attributes.random.screen.time"
                    },
                    {
                        "key": "report.attributes.random.icf.time"
                    },
                    {
                        "key": "report.attributes.random.finish.remark"
                    },
                    {
                        "key": "report.attributes.random.plan.time"
                    },
                    {
                        "key": "report.attributes.random.sequence.number"
                    }
                ]
            }
        ],
        "latestDownloadTime": "2025-05-13 10:47:05(UTC+8)"
    },
    {
        "name": "menu.report.dispenseReport",
        "defaultFields": [],
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.attributes.dispensing.type"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.operator"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.dispensing.label"
                    },
                    {
                        "key": "report.attributes.dispensing.dose"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.other.number"
                    },
                    {
                        "key": "report.attributes.dispensing.useFormulas"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.attributes.dispensing.type"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.operator"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.dispensing.label"
                    },
                    {
                        "key": "report.attributes.dispensing.dose"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.other.number"
                    },
                    {
                        "key": "report.attributes.dispensing.useFormulas"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.attributes.dispensing.type"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.operator"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.dispensing.label"
                    },
                    {
                        "key": "report.attributes.dispensing.dose"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.other.number"
                    },
                    {
                        "key": "report.attributes.dispensing.useFormulas"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.attributes.dispensing.type"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.operator"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.dispensing.label"
                    },
                    {
                        "key": "report.attributes.dispensing.dose"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.other.number"
                    },
                    {
                        "key": "report.attributes.dispensing.useFormulas"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.attributes.dispensing.type"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.operator"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.dispensing.label"
                    },
                    {
                        "key": "report.attributes.dispensing.dose"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.other.number"
                    },
                    {
                        "key": "report.attributes.dispensing.useFormulas"
                    },
                    {
                        "key": "report.attributes.dispensing.reissue.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.out-visit-dispensing.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.replace.remark"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.attributes.dispensing.type"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.operator"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.dispensing.label"
                    },
                    {
                        "key": "report.attributes.dispensing.dose"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.other.number"
                    },
                    {
                        "key": "report.attributes.dispensing.useFormulas"
                    },
                    {
                        "key": "report.attributes.dispensing.reissue.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.out-visit-dispensing.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.replace.remark"
                    }
                ]
            }
        ],
        "type": 15,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info",
                "children": [
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    }
                ]
            },
            {
                "key": "report.attributes.dispensing",
                "children": [
                    {
                        "key": "report.attributes.random.cohort",
                        "required": true
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.random.stage",
                        "required": true
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.attributes.dispensing.type"
                    },
                    {
                        "key": "report.attributes.dispensing.planTime"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.operator"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.name"
                    },
                    {
                        "key": "report.attributes.dispensing.label"
                    },
                    {
                        "key": "report.attributes.dispensing.dose"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.real.group"
                    },
                    {
                        "key": "report.attributes.dispensing.drug.other.number"
                    },
                    {
                        "key": "report.attributes.dispensing.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.out-visit-dispensing.reason"
                    },
                    {
                        "key": "report.attributes.dispensing.reissue.reason"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.dispensing.outsize"
                    },
                    {
                        "key": "report.attributes.dispensing.doseFormulas"
                    },
                    {
                        "key": "report.attributes.dispensing.useFormulas"
                    },
                    {
                        "key": "report.attributes.dispensing.reissue.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.out-visit-dispensing.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.replace.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.retrieval.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.register.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.invalid.remark"
                    },
                    {
                        "key": "report.attributes.dispensing.send.type"
                    },
                    {
                        "key": "report.attributes.dispensing.logistics.info"
                    },
                    {
                        "key": "report.attributes.dispensing.logistics.remark"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.unblindingReport",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info.country"
            },
            {
                "key": "report.attributes.info.region"
            },
            {
                "key": "report.attributes.info.site.number"
            },
            {
                "key": "report.attributes.info.site.name"
            },
            {
                "key": "report.attributes.random.cohort"
            },
            {
                "key": "report.attributes.random.stage"
            },
            {
                "key": "report.attributes.info.subject.number"
            },
            {
                "key": "report.attributes.random.number"
            },
            {
                "key": "report.attributes.unblinding.sponsor"
            },
            {
                "key": "report.attributes.unblinding.mark"
            },
            {
                "key": "report.attributes.unblinding.reason"
            },
            {
                "key": "report.attributes.unblinding.reason.mark"
            },
            {
                "key": "report.attributes.unblinding.operator"
            },
            {
                "key": "report.attributes.unblinding.time"
            }
        ],
        "multiDefaultFields": null,
        "type": 16,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info",
                "children": [
                    {
                        "key": "report.attributes.info.country"
                    },
                    {
                        "key": "report.attributes.info.region"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    }
                ]
            },
            {
                "key": "report.attributes.random",
                "children": [
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.random.cohort",
                        "required": true
                    },
                    {
                        "key": "report.attributes.random.stage",
                        "required": true
                    }
                ]
            },
            {
                "key": "report.attributes.dispensing",
                "children": [
                    {
                        "key": "report.attributes.dispensing.medicine.real.number"
                    }
                ]
            },
            {
                "key": "report.attributes.unblinding",
                "children": [
                    {
                        "key": "report.attributes.unblinding.sponsor"
                    },
                    {
                        "key": "report.attributes.unblinding.mark"
                    },
                    {
                        "key": "report.attributes.unblinding.reason"
                    },
                    {
                        "key": "report.attributes.unblinding.reason.mark"
                    },
                    {
                        "key": "report.attributes.unblinding.operator"
                    },
                    {
                        "key": "report.attributes.unblinding.time"
                    }
                ]
            }
        ],
        "latestDownloadTime": "2025-03-25 16:21:37(UTC+8)"
    },
    {
        "name": "menu.report.shipmentOrdersReport",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.order.number"
            },
            {
                "key": "report.attributes.order.status"
            },
            {
                "key": "report.attributes.order.send"
            },
            {
                "key": "report.attributes.order.receive"
            },
            {
                "key": "report.attributes.order.medicineQuantity"
            },
            {
                "key": "report.attributes.order.create.by"
            },
            {
                "key": "report.attributes.order.create.time"
            },
            {
                "key": "report.attributes.order.receive.by"
            },
            {
                "key": "report.attributes.order.receive.time"
            },
            {
                "key": "report.attributes.order.close.by"
            },
            {
                "key": "report.attributes.order.close.time"
            },
            {
                "key": "report.attributes.order.end.by"
            },
            {
                "key": "report.attributes.order.end.time"
            },
            {
                "key": "report.attributes.order.lost.by"
            },
            {
                "key": "report.attributes.order.lost.time"
            },
            {
                "key": "report.attributes.order.actualReceiptTime"
            },
            {
                "key": "report.attributes.research"
            },
            {
                "key": "report.attributes.research.batch"
            },
            {
                "key": "report.attributes.research.expireDate"
            },
            {
                "key": "report.attributes.research.other"
            }
        ],
        "multiDefaultFields": null,
        "type": 20,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.order",
                "children": [
                    {
                        "key": "report.attributes.order.number"
                    },
                    {
                        "key": "report.attributes.order.status"
                    },
                    {
                        "key": "report.attributes.order.send"
                    },
                    {
                        "key": "report.attributes.order.receive"
                    },
                    {
                        "key": "report.attributes.order.medicineQuantity"
                    },
                    {
                        "key": "report.attributes.order.create.by"
                    },
                    {
                        "key": "report.attributes.order.create.time"
                    },
                    {
                        "key": "report.attributes.order.cancel.by"
                    },
                    {
                        "key": "report.attributes.order.cancel.time"
                    },
                    {
                        "key": "report.attributes.order.cancel.reason"
                    },
                    {
                        "key": "report.attributes.order.confirm.by"
                    },
                    {
                        "key": "report.attributes.order.confirm.time"
                    },
                    {
                        "key": "report.attributes.order.close.by"
                    },
                    {
                        "key": "report.attributes.order.close.time"
                    },
                    {
                        "key": "report.attributes.order.close.reason"
                    },
                    {
                        "key": "report.attributes.order.send.by"
                    },
                    {
                        "key": "report.attributes.order.send.time"
                    },
                    {
                        "key": "report.attributes.order.receive.by"
                    },
                    {
                        "key": "report.attributes.order.receive.time"
                    },
                    {
                        "key": "report.attributes.order.lost.by"
                    },
                    {
                        "key": "report.attributes.order.lost.time"
                    },
                    {
                        "key": "report.attributes.order.lost.reason"
                    },
                    {
                        "key": "report.attributes.order.end.by"
                    },
                    {
                        "key": "report.attributes.order.end.time"
                    },
                    {
                        "key": "report.attributes.order.end.reason"
                    },
                    {
                        "key": "report.attributes.order.supplier"
                    },
                    {
                        "key": "report.attributes.order.supplier.other"
                    },
                    {
                        "key": "report.attributes.order.supplier.number"
                    },
                    {
                        "key": "report.attributes.order.expectedArrivalTime"
                    },
                    {
                        "key": "report.attributes.order.actualReceiptTime"
                    }
                ]
            },
            {
                "key": "report.attributes.order.detail",
                "children": [
                    {
                        "key": "report.attributes.research"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.packageMethod"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.status"
                    },
                    {
                        "key": "report.attributes.research.other"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.returnOrdersReport",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.order.number"
            },
            {
                "key": "report.attributes.order.status"
            },
            {
                "key": "report.attributes.order.send"
            },
            {
                "key": "report.attributes.order.receive"
            },
            {
                "key": "report.attributes.order.medicineQuantity"
            },
            {
                "key": "report.attributes.order.create.by"
            },
            {
                "key": "report.attributes.order.create.time"
            },
            {
                "key": "report.attributes.order.receive.by"
            },
            {
                "key": "report.attributes.order.receive.time"
            },
            {
                "key": "report.attributes.order.close.by"
            },
            {
                "key": "report.attributes.order.close.time"
            },
            {
                "key": "report.attributes.order.end.by"
            },
            {
                "key": "report.attributes.order.end.time"
            },
            {
                "key": "report.attributes.order.lost.by"
            },
            {
                "key": "report.attributes.order.lost.time"
            },
            {
                "key": "report.attributes.research"
            },
            {
                "key": "report.attributes.research.batch"
            },
            {
                "key": "report.attributes.research.expireDate"
            },
            {
                "key": "report.attributes.research.other"
            },
            {
                "key": "report.attributes.order.actualReceiptTime"
            }
        ],
        "multiDefaultFields": null,
        "type": 21,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.order",
                "children": [
                    {
                        "key": "report.attributes.order.number"
                    },
                    {
                        "key": "report.attributes.order.status"
                    },
                    {
                        "key": "report.attributes.order.send"
                    },
                    {
                        "key": "report.attributes.order.receive"
                    },
                    {
                        "key": "report.attributes.order.medicineQuantity"
                    },
                    {
                        "key": "report.attributes.order.create.by"
                    },
                    {
                        "key": "report.attributes.order.create.time"
                    },
                    {
                        "key": "report.attributes.order.cancel.by"
                    },
                    {
                        "key": "report.attributes.order.cancel.time"
                    },
                    {
                        "key": "report.attributes.order.cancel.reason"
                    },
                    {
                        "key": "report.attributes.order.confirm.by"
                    },
                    {
                        "key": "report.attributes.order.confirm.time"
                    },
                    {
                        "key": "report.attributes.order.close.by"
                    },
                    {
                        "key": "report.attributes.order.close.time"
                    },
                    {
                        "key": "report.attributes.order.close.reason"
                    },
                    {
                        "key": "report.attributes.order.send.by"
                    },
                    {
                        "key": "report.attributes.order.send.time"
                    },
                    {
                        "key": "report.attributes.order.receive.by"
                    },
                    {
                        "key": "report.attributes.order.receive.time"
                    },
                    {
                        "key": "report.attributes.order.lost.by"
                    },
                    {
                        "key": "report.attributes.order.lost.time"
                    },
                    {
                        "key": "report.attributes.order.lost.reason"
                    },
                    {
                        "key": "report.attributes.order.end.by"
                    },
                    {
                        "key": "report.attributes.order.end.time"
                    },
                    {
                        "key": "report.attributes.order.end.reason"
                    },
                    {
                        "key": "report.attributes.order.supplier"
                    },
                    {
                        "key": "report.attributes.order.supplier.other"
                    },
                    {
                        "key": "report.attributes.order.supplier.number"
                    },
                    {
                        "key": "report.attributes.order.expectedArrivalTime"
                    },
                    {
                        "key": "report.attributes.order.actualReceiptTime"
                    }
                ]
            },
            {
                "key": "report.attributes.order.detail",
                "children": [
                    {
                        "key": "report.attributes.research"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.packageMethod"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.status"
                    },
                    {
                        "key": "report.attributes.research.other"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.siteItemReport",
        "defaultFields": null,
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            }
        ],
        "type": 19,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info",
                "children": [
                    {
                        "key": "report.attributes.info.site.country"
                    },
                    {
                        "key": "report.attributes.info.site.region"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    }
                ]
            },
            {
                "key": "report.attributes.research",
                "children": [
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    },
                    {
                        "key": "report.attributes.research.reason"
                    },
                    {
                        "key": "report.attributes.research.operator"
                    },
                    {
                        "key": "report.attributes.research.time"
                    },
                    {
                        "key": "report.attributes.research.other"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.depotItemReport",
        "defaultFields": null,
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.other"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            }
        ],
        "type": 18,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info",
                "children": [
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.random.cohort",
                        "required": true
                    },
                    {
                        "key": "report.attributes.random.stage",
                        "required": true
                    }
                ]
            },
            {
                "key": "report.attributes.research",
                "children": [
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.order.number"
                    },
                    {
                        "key": "report.attributes.research.status"
                    },
                    {
                        "key": "report.attributes.research.reason"
                    },
                    {
                        "key": "report.attributes.research.operator"
                    },
                    {
                        "key": "report.attributes.research.time"
                    },
                    {
                        "key": "report.attributes.research.other"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.sourceRandomizationListExport",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info.site.number"
            },
            {
                "key": "report.attributes.info.site.name"
            },
            {
                "key": "report.attributes.random.cohort"
            },
            {
                "key": "report.attributes.info.subject.number"
            },
            {
                "key": "report.attributes.info.status"
            },
            {
                "key": "report.attributes.random.time"
            },
            {
                "key": "report.simulate.random.block"
            },
            {
                "key": "report.attributes.random.number"
            },
            {
                "key": "report.simulate.random.group"
            },
            {
                "key": "report.attributes.dispensing.medicine.real.group"
            },
            {
                "key": "report.attributes.random.config.code"
            },
            {
                "key": "projects.randomization.planNumber"
            },
            {
                "key": "report.attributes.random.g"
            },
            {
                "key": "report.attributes.random.factor"
            },
            {
                "key": "report.attributes.random.subject.replace.time"
            },
            {
                "key": "report.attributes.random.subject.number.replace"
            },
            {
                "key": "report.attributes.random.subject.replace.number"
            },
            {
                "key": "report.attributes.random.subject.replace.status"
            }
        ],
        "multiDefaultFields": null,
        "type": 12,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.sourceIPExport",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.research.packageNumber"
            },
            {
                "key": "report.attributes.research.package.serialNumber"
            },
            {
                "key": "report.attributes.research.medicine.serial-number"
            },
            {
                "key": "report.attributes.research.medicine.name"
            },
            {
                "key": "report.attributes.research.medicine.number"
            },
            {
                "key": "report.attributes.research.expireDate"
            },
            {
                "key": "report.attributes.research.batch"
            },
            {
                "key": "report.attributes.research.status"
            },
            {
                "key": "report.attributes.info.subject.number"
            },
            {
                "key": "report.attributes.info.site.number"
            },
            {
                "key": "report.attributes.info.site.name"
            },
            {
                "key": "report.attributes.dispensing.time"
            },
            {
                "key": "report.attributes.dispensing.medicine.is.replace"
            },
            {
                "key": "report.attributes.dispensing.medicine.is.real"
            },
            {
                "key": "report.attributes.random.stage"
            },
            {
                "key": "report.attributes.random.cohort"
            }
        ],
        "multiDefaultFields": null,
        "type": 11,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info",
                "children": [
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    }
                ]
            },
            {
                "key": "report.attributes.dispensing",
                "children": [
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.is.replace"
                    },
                    {
                        "key": "report.attributes.dispensing.medicine.is.real"
                    },
                    {
                        "key": "report.attributes.random.cohort",
                        "required": true
                    },
                    {
                        "key": "report.attributes.random.stage",
                        "required": true
                    }
                ]
            },
            {
                "key": "report.attributes.research",
                "children": [
                    {
                        "key": "report.attributes.research.medicine.serial-number"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.package.serialNumber"
                    },
                    {
                        "key": "report.attributes.research.status"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.sourceIpUploadHistory",
        "defaultFields": null,
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "source.ip.upload.history.name"
                    },
                    {
                        "key": "report.attributes.research.operator"
                    },
                    {
                        "key": "report.attributes.research.time"
                    },
                    {
                        "key": "source.ip.upload.history.rows"
                    },
                    {
                        "key": "common.status"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.serial-number"
                    },
                    {
                        "key": "report.attributes.research.medicine.number"
                    },
                    {
                        "key": "report.attributes.research.medicine.code"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.packageNumber"
                    },
                    {
                        "key": "report.attributes.research.package.serialNumber"
                    },
                    {
                        "key": "report.attributes.research.medicine.type"
                    },
                    {
                        "key": "report.attributes.research.time"
                    },
                    {
                        "key": "report.attributes.research.operator"
                    },
                    {
                        "key": "report.user.role.assign.content"
                    }
                ]
            }
        ],
        "type": 25,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.randomizationSimulationResult",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.simulate.random.name"
            },
            {
                "key": "simulated.random.list.runCount"
            },
            {
                "key": "report.attributes.info.site.name"
            },
            {
                "key": "report.attributes.info.subject.number"
            },
            {
                "key": "report.simulate.random.block"
            },
            {
                "key": "report.attributes.random.number"
            },
            {
                "key": "report.attributes.random.config.code"
            },
            {
                "key": "report.simulate.random.group"
            },
            {
                "key": "report.attributes.random.factor"
            },
            {
                "key": "projects.randomization.planNumber"
            },
            {
                "key": "report.attributes.random.g"
            }
        ],
        "multiDefaultFields": null,
        "type": 22,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.RandomizationSimulationPDFExport",
        "defaultFields": null,
        "multiDefaultFields": null,
        "type": 23,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.configureReport",
        "defaultFields": [],
        "multiDefaultFields": null,
        "type": 10,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.auditTrailExport",
        "defaultFields": null,
        "multiDefaultFields": null,
        "type": 8,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.projectPermissionConfigurationExport",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "common.role"
            },
            {
                "key": "common.menu"
            },
            {
                "key": "common.operation"
            }
        ],
        "multiDefaultFields": null,
        "type": 9,
        "customizable": false,
        "latestDownloadTime": "2025-04-29 16:24:37(UTC+8)"
    },
    {
        "name": "menu.report.ProjectNotificationsConfigurationReportExport",
        "defaultFields": [
            {
                "key": "export.notifications.configuration.report.type"
            },
            {
                "key": "export.notifications.configuration.report.role"
            },
            {
                "key": "export.notifications.configuration.report.content.configuration"
            },
            {
                "key": "export.notifications.configuration.report.scene"
            }
        ],
        "multiDefaultFields": null,
        "type": 24,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.userRoleStatus",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info.user.name"
            },
            {
                "key": "report.attributes.info.user.email"
            },
            {
                "key": "report.attributes.info.user.role"
            },
            {
                "key": "report.attributes.info.site.name"
            },
            {
                "key": "report.attributes.info.storehouse.name"
            },
            {
                "key": "report.attributes.order.create.time"
            },
            {
                "key": "report.attributes.info.status"
            }
        ],
        "multiDefaultFields": null,
        "type": 7,
        "customizable": false,
        "latestDownloadTime": "2025-05-08 14:43:33(UTC+8)"
    },
    {
        "name": "menu.report.userLoginHistory",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info.user.name"
            },
            {
                "key": "report.attributes.info.user.email"
            },
            {
                "key": "report.user.login.ip"
            },
            {
                "key": "report.user.login.time"
            },
            {
                "key": "report.user.login.success"
            }
        ],
        "multiDefaultFields": null,
        "type": 5,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.userRoleAssignHistory",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.user.role.assign.name"
            },
            {
                "key": "report.user.role.assign.email"
            },
            {
                "key": "report.user.role.assign.operType"
            },
            {
                "key": "report.user.role.assign.content"
            },
            {
                "key": "report.user.role.assign.oper"
            },
            {
                "key": "report.user.role.assign.operTime"
            }
        ],
        "multiDefaultFields": null,
        "type": 6,
        "customizable": false,
        "latestDownloadTime": "2025-04-29 16:26:01(UTC+8)"
    },
    {
        "name": "menu.report.siteIPStatisticsExport",
        "defaultFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info.site.number"
            },
            {
                "key": "report.attributes.info.site.name"
            },
            {
                "key": "report.attributes.research.medicine.name"
            },
            {
                "key": "report.attributes.research.expireDate"
            },
            {
                "key": "report.attributes.research.batch"
            },
            {
                "key": "report.attributes.research.spec"
            },
            {
                "key": "report.ip.statistics.status.available"
            },
            {
                "key": "report.ip.statistics.status.toBeConfirmed"
            },
            {
                "key": "report.ip.statistics.status.delivered"
            },
            {
                "key": "report.ip.statistics.status.sending"
            },
            {
                "key": "report.ip.statistics.status.quarantine"
            },
            {
                "key": "report.ip.statistics.status.used"
            },
            {
                "key": "report.ip.statistics.status.lose"
            },
            {
                "key": "report.ip.statistics.status.expired"
            },
            {
                "key": "report.ip.statistics.status.frozen"
            },
            {
                "key": "report.ip.statistics.status.locked"
            }
        ],
        "multiDefaultFields": null,
        "type": 3,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info.site.number",
                "required": true
            },
            {
                "key": "report.attributes.info.site.name",
                "required": true
            },
            {
                "key": "report.attributes.research.medicine.name",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.available",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.toBeConfirmed",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.delivered",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.sending",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.quarantine",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.used",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.lose",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.expired",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.frozen",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.locked",
                "required": true
            },
            {
                "key": "report.attributes.research",
                "children": [
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.depotIPStatisticsExport",
        "defaultFields": null,
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.ip.statistics.status.available"
                    },
                    {
                        "key": "report.ip.statistics.status.toBeConfirmed"
                    },
                    {
                        "key": "report.ip.statistics.status.delivered"
                    },
                    {
                        "key": "report.ip.statistics.status.sending"
                    },
                    {
                        "key": "report.ip.statistics.status.quarantine"
                    },
                    {
                        "key": "report.ip.statistics.status.used"
                    },
                    {
                        "key": "report.ip.statistics.status.lose"
                    },
                    {
                        "key": "report.ip.statistics.status.expired"
                    },
                    {
                        "key": "report.ip.statistics.status.frozen"
                    },
                    {
                        "key": "report.ip.statistics.status.locked"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.ip.statistics.status.available"
                    },
                    {
                        "key": "report.ip.statistics.status.toBeConfirmed"
                    },
                    {
                        "key": "report.ip.statistics.status.delivered"
                    },
                    {
                        "key": "report.ip.statistics.status.sending"
                    },
                    {
                        "key": "report.ip.statistics.status.quarantine"
                    },
                    {
                        "key": "report.ip.statistics.status.used"
                    },
                    {
                        "key": "report.ip.statistics.status.lose"
                    },
                    {
                        "key": "report.ip.statistics.status.expired"
                    },
                    {
                        "key": "report.ip.statistics.status.frozen"
                    },
                    {
                        "key": "report.ip.statistics.status.locked"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.ip.statistics.status.available"
                    },
                    {
                        "key": "report.ip.statistics.status.toBeConfirmed"
                    },
                    {
                        "key": "report.ip.statistics.status.delivered"
                    },
                    {
                        "key": "report.ip.statistics.status.sending"
                    },
                    {
                        "key": "report.ip.statistics.status.quarantine"
                    },
                    {
                        "key": "report.ip.statistics.status.used"
                    },
                    {
                        "key": "report.ip.statistics.status.lose"
                    },
                    {
                        "key": "report.ip.statistics.status.expired"
                    },
                    {
                        "key": "report.ip.statistics.status.frozen"
                    },
                    {
                        "key": "report.ip.statistics.status.locked"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.ip.statistics.status.available"
                    },
                    {
                        "key": "report.ip.statistics.status.toBeConfirmed"
                    },
                    {
                        "key": "report.ip.statistics.status.delivered"
                    },
                    {
                        "key": "report.ip.statistics.status.sending"
                    },
                    {
                        "key": "report.ip.statistics.status.quarantine"
                    },
                    {
                        "key": "report.ip.statistics.status.used"
                    },
                    {
                        "key": "report.ip.statistics.status.lose"
                    },
                    {
                        "key": "report.ip.statistics.status.expired"
                    },
                    {
                        "key": "report.ip.statistics.status.frozen"
                    },
                    {
                        "key": "report.ip.statistics.status.locked"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.ip.statistics.status.available"
                    },
                    {
                        "key": "report.ip.statistics.status.toBeConfirmed"
                    },
                    {
                        "key": "report.ip.statistics.status.delivered"
                    },
                    {
                        "key": "report.ip.statistics.status.sending"
                    },
                    {
                        "key": "report.ip.statistics.status.quarantine"
                    },
                    {
                        "key": "report.ip.statistics.status.used"
                    },
                    {
                        "key": "report.ip.statistics.status.lose"
                    },
                    {
                        "key": "report.ip.statistics.status.expired"
                    },
                    {
                        "key": "report.ip.statistics.status.frozen"
                    },
                    {
                        "key": "report.ip.statistics.status.locked"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.storehouse.name"
                    },
                    {
                        "key": "report.attributes.research.medicine.name"
                    },
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    },
                    {
                        "key": "report.ip.statistics.status.available"
                    },
                    {
                        "key": "report.ip.statistics.status.toBeConfirmed"
                    },
                    {
                        "key": "report.ip.statistics.status.delivered"
                    },
                    {
                        "key": "report.ip.statistics.status.sending"
                    },
                    {
                        "key": "report.ip.statistics.status.quarantine"
                    },
                    {
                        "key": "report.ip.statistics.status.used"
                    },
                    {
                        "key": "report.ip.statistics.status.lose"
                    },
                    {
                        "key": "report.ip.statistics.status.expired"
                    },
                    {
                        "key": "report.ip.statistics.status.frozen"
                    },
                    {
                        "key": "report.ip.statistics.status.locked"
                    }
                ]
            }
        ],
        "type": 4,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info.storehouse.name",
                "required": true
            },
            {
                "key": "report.attributes.research.medicine.name",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.available",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.toBeConfirmed",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.delivered",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.sending",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.quarantine",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.used",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.lose",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.expired",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.frozen",
                "required": true
            },
            {
                "key": "report.ip.statistics.status.locked",
                "required": true
            },
            {
                "key": "report.attributes.research",
                "children": [
                    {
                        "key": "report.attributes.research.expireDate"
                    },
                    {
                        "key": "report.attributes.research.batch"
                    },
                    {
                        "key": "report.attributes.research.spec"
                    }
                ]
            }
        ],
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.randomizationStatisticsExport",
        "defaultFields": null,
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.factor"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.random.statistics.month"
                    },
                    {
                        "key": "report.random.statistics.week"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.factor"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.random.statistics.month"
                    },
                    {
                        "key": "report.random.statistics.week"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.random.factor"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.random.statistics.month"
                    },
                    {
                        "key": "report.random.statistics.week"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    }
                ]
            }
        ],
        "type": 1,
        "customizable": false,
        "latestDownloadTime": "2025-05-08 16:56:31(UTC+8)"
    },
    {
        "name": "menu.report.subjectStatisticsExport",
        "defaultFields": null,
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "subject.register"
                    },
                    {
                        "key": "subject.random"
                    },
                    {
                        "key": "subject.exited"
                    },
                    {
                        "key": "report.subject.unblinding.urgent"
                    },
                    {
                        "key": "report.subject.unblinding.pv"
                    },
                    {
                        "key": "subject.status.finish"
                    }
                ]
            },
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "subject.register"
                    },
                    {
                        "key": "subject.random"
                    },
                    {
                        "key": "subject.exited"
                    },
                    {
                        "key": "report.subject.unblinding.urgent"
                    },
                    {
                        "key": "report.subject.unblinding.pv"
                    },
                    {
                        "key": "subject.status.finish"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "subject.register"
                    },
                    {
                        "key": "subject.random"
                    },
                    {
                        "key": "subject.exited"
                    },
                    {
                        "key": "report.subject.unblinding.urgent"
                    },
                    {
                        "key": "report.subject.unblinding.pv"
                    },
                    {
                        "key": "subject.status.finish"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "subject.register"
                    },
                    {
                        "key": "subject.random"
                    },
                    {
                        "key": "subject.exited"
                    },
                    {
                        "key": "report.subject.unblinding.urgent"
                    },
                    {
                        "key": "report.subject.unblinding.pv"
                    },
                    {
                        "key": "subject.status.finish"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "subject.register"
                    },
                    {
                        "key": "subject.random"
                    },
                    {
                        "key": "subject.exited"
                    },
                    {
                        "key": "report.subject.unblinding.urgent"
                    },
                    {
                        "key": "report.subject.unblinding.pv"
                    },
                    {
                        "key": "subject.status.finish"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "subject.register"
                    },
                    {
                        "key": "subject.random"
                    },
                    {
                        "key": "subject.exited"
                    },
                    {
                        "key": "report.subject.unblinding.urgent"
                    },
                    {
                        "key": "report.subject.unblinding.pv"
                    },
                    {
                        "key": "subject.status.finish"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    }
                ]
            }
        ],
        "type": 2,
        "customizable": false,
        "latestDownloadTime": "2025-05-07 14:12:57(UTC+8)"
    },
    {
        "name": "menu.report.forecastingPrediction",
        "defaultFields": [
            {
                "key": "report.forecast.depot"
            },
            {
                "key": "report.attributes.info.site.number"
            },
            {
                "key": "report.attributes.info.site.name"
            },
            {
                "key": "report.attributes.info.subject.number"
            },
            {
                "key": "report.attributes.dispensing.cycle.name"
            },
            {
                "key": "report.forecast.period"
            },
            {
                "key": "report.attributes.research"
            },
            {
                "key": "report.attributes.order.medicineQuantity"
            },
            {
                "key": "report.simulate.random.group"
            },
            {
                "key": "report.attributes.random.cohort"
            },
            {
                "key": "report.attributes.random.stage"
            }
        ],
        "multiDefaultFields": null,
        "type": 26,
        "customizable": false,
        "latestDownloadTime": ""
    },
    {
        "name": "menu.report.visitForecast",
        "defaultFields": null,
        "multiDefaultFields": [
            {
                "type": 1,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.forecast.period"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.visit.forecast.visit.status"
                    }
                ]
            },
            {
                "type": 2,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.random.cohort"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.forecast.period"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.visit.forecast.visit.status"
                    }
                ]
            },
            {
                "type": 3,
                "defaultFields": [
                    {
                        "key": "report.attributes.project.number"
                    },
                    {
                        "key": "report.attributes.project.name"
                    },
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.random.stage"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.forecast.period"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.visit.forecast.visit.status"
                    }
                ]
            }
        ],
        "type": 27,
        "customizable": true,
        "customFields": [
            {
                "key": "report.attributes.project.number"
            },
            {
                "key": "report.attributes.project.name"
            },
            {
                "key": "report.attributes.info",
                "children": [
                    {
                        "key": "report.attributes.info.site.number"
                    },
                    {
                        "key": "report.attributes.info.site.name"
                    },
                    {
                        "key": "report.attributes.info.subject.number"
                    },
                    {
                        "key": "report.attributes.random.number"
                    },
                    {
                        "key": "report.attributes.random.cohort",
                        "required": true
                    },
                    {
                        "key": "report.attributes.random.stage",
                        "required": true
                    },
                    {
                        "key": "report.attributes.dispensing.cycle.name"
                    },
                    {
                        "key": "report.forecast.period"
                    },
                    {
                        "key": "report.attributes.dispensing.time"
                    },
                    {
                        "key": "report.visit.forecast.visit.status"
                    },
                    {
                        "key": "report.visit.forecast.notice.content"
                    },
                    {
                        "key": "report.visit.forecast.notice.time"
                    },
                    {
                        "key": "report.visit.forecast.notice.user"
                    },
                    {
                        "key": "report.visit.forecast.notice.type"
                    },
                    {
                        "key": "report.visit.forecast.notice.email"
                    }
                ]
            }
        ],
        "latestDownloadTime": "2025-05-12 15:00:19(UTC+8)"
    }
]


const reportName = reportList.map(it => it.name)

const getKeys = (fields) => {
    if (!fields) return []
    return fields.map(it => [it.key, getKeys(it.children)].flat()).flat()
}
const reportField = reportList.map(it => {
    const defaultKeys = it.defaultFields?.map(d => d.key) || []
    const multiDefaultKeys = it.multiDefaultFields?.map(m => m.defaultFields?.map(d => d.key) || [])?.flat() || []
    const customKeys = getKeys(it.customFields)
    return [defaultKeys, multiDefaultKeys, customKeys].flat()
}).flat()

export const reportModule = {
    key: "report",
    name: "menu.report",
    children:  _.uniq([...reportName, ...reportField]).map(it => ({
        key: it
    }))
}