import React, {useState} from 'react'
import {Button, Select, Spin} from "antd";
import {useFetch} from "../../../../../hooks/request";
import {flatModules, get, getAllKeys, post, splitModule} from "../util";
import {message as zhmessage} from "../../../../../locales/zh";
import {message as enmessage} from "../../../../../locales/en";
import {LocalesTable} from "./table";
import {allFields, editFields} from "../../locales";
import {unuseKeys} from "../special-field/unuse";
import {menuModule} from "./menu-permissions";
import _ from "lodash";
import {useAtom} from "jotai";
import {localesDataAtom} from "./context";
import {reportModule} from "./report";
import {localesProject} from "../../locales/module";

// 直接扫描文件中的id
export const LocalesScanFile = (props) => {

    const [data, setData] = useState([])
    const [showData, setShowData] = useState([])
    const [options, setOptions] = useState([])
    const [localesData, setLocalesData] = useAtom(localesDataAtom)

    const { run: getFields, loading } = useFetch(() => get('read-file'), {
        manual: true,
        onSuccess: (result) => {
            const module = [...result, menuModule, reportModule]
            for (let i = 0; i < module.length; i++) {
                if (module[i].key === 'project_setting') {
                    module[i] = splitModule(module[i], localesProject[1])
                }
            }

            console.log(module)

            const allKeys = getAllKeys(module)
            module.push({
                key: "un_scan",
                name: "没扫描到的字段",
                children: Object.keys(zhmessage).filter(it => !allKeys.includes(it)).map(it => ({
                    key: it
                }))
            })

            const m = flatModules(module)
            setData(m.fields)
            setShowData(m.fields)
            setOptions(m.options)
            setLocalesData({...localesData, scanFile: m.fields})
        },
        onError: (e) => {
            console.log(e)
        }
    })

    const { run: save } = useFetch((data) => post('add-file', data), {
        manual: true,
        onSuccess: (result) => {
            console.log(result)
        },
        onError: (e) => {
            console.log(e)
        }
    })

    const unTranslation = () => {
        setShowData(data.filter(it => !zhmessage[it.key] || !enmessage[it.key] || (zhmessage[it.key] === enmessage[it.key])))
    }

    const unuseButUse = () => {
        setShowData(data.filter(it => it.path.toString() !== 'un_scan').filter(it => unuseKeys.includes(it.key)))
    }

    const unMange = () => {
        const mangeKeys = allFields.map(it => it.key)
        setShowData(data.filter(it => !mangeKeys.includes(it.key)))
    }

    // 已经废弃的字段
    const unuseField = () => {
        setShowData(data.filter(it => unuseKeys.includes(it.key)))
    }

    const unuseButInMange = () => {
        const allKeys = allFields.map(it => it.key)
        setShowData(data.filter(it => unuseKeys.includes(it.key) && allKeys.includes(it.key)))
    }

    const noScanButInMange = () => {
        const allKeys = allFields.map(it => it.key)
        const unScanKeys = data.filter(it => it.path[0] === 'un_scan').map(it => it.key)
        setShowData(data.filter(it => allKeys.includes(it.key) && unScanKeys.includes(it.key)))
    }


    const repeatField = () => {
        const counts = _.countBy(data, 'key')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        const showData = repeatNames.reduce((res, item) => {
            res = res.concat(data.filter(it => it.key === item))
            return res
        }, [])
        setShowData(showData)
    }

    const reportField = () => {
        setShowData(data.filter(it => it.key.startsWith('report.')))
    }

    const pathNotSame = () => {
        const showData = data.filter(it =>
            !!editFields.find(f => f.key === it.key && f.path?.join('/') !== it.path?.join('/'))
        )
        setShowData(showData)
    }

    const notLocales = () => {
        const keys = Object.keys(zhmessage)
        setShowData(data.filter(it => !keys.includes(it.key)))
    }

    const translationIsSame = () => {
        const sameData = _.uniqBy(data, 'key')
        const counts = _.countBy(sameData, 'value')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        const showData = repeatNames.reduce((res, item) => {
            res = res.concat(sameData.filter(it => it.value === item))
            return res
        }, [])
        setShowData(showData)
    }

    const filterList = [
        {label: '未完全翻译字段', onClick: unTranslation},
        {label: '应该废弃但页面中使用到的字段', onClick: unuseButUse},
        {label: '还没被管理的字段', onClick: unMange},
        {label: '已经废弃的字段', onClick: unuseField},
        {label: '应该废弃但还是纳入管理的字段', onClick: unuseButInMange},
        {label: '没扫描到但是纳入管理的字段', onClick: noScanButInMange},
        // TODO 扫描到了但是相关路径下没有的字段，需要二次确认
        {label: '使用到但是路径不一致的字段', onClick: pathNotSame},
        {label: '重复出现的字段', onClick: repeatField},
        {label: '使用到的报表字段', onClick: reportField},
        {label: '扫描到但是没有配置的字段', onClick: notLocales},
        {label: '翻译一致的字段', onClick: translationIsSame},
        // TODO 路径下扫描出来了，但是模块下没有进行维护

    ].map((it, index) => ({...it, value: index}))

    /* TODO 一个页面展示所有问题字段 */
    return <Spin spinning={loading}>
        <div style={{marginBottom: '20px'}}>
            <Button.Group>
                <Button onClick={getFields} disabled={data.length > 0}>读取字段</Button>
                <Select
                    allowClear
                    placeholder={'选择过滤类型'}
                    style={{width: '300px'}}
                    options={filterList}
                    onChange={(value, option) => option ? option?.onClick() : setShowData(data)}
                />
                <Button onClick={() => setShowData(_.uniqBy(data, 'key'))}>去重</Button>
                <Button onClick={() => setShowData(data)}>重置</Button>
            </Button.Group>
        </div>
        <LocalesTable dataSource={showData} modules={options} />
    </Spin>

}