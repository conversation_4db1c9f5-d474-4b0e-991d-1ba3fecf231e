import React, {useEffect, useState} from 'react'
import {LocalesTable} from "./table";
import {allFields, allModules} from "../../locales";
import {Button} from "antd";
import {unuseKeys} from "../special-field/unuse";
import {message as enmessage} from "../../../../../locales/en";
import {message as zhmessage} from "../../../../../locales/zh";
import _ from "lodash";
import {useAtom} from "jotai";
import {localesDataAtom} from "./context";

// 翻译不完全的字段
export const LocalesScanMultilingual = (props) => {
    const [data, setData] = useState([])
    const [showData, setShowData] = useState([])
    const [localesData] = useAtom(localesDataAtom)

    useEffect(() => {
        setData(allFields)
        setShowData(allFields)
    }, [])


    // 翻译不一致的字段
    const translationIsNotSame = () => {
        const showData =  data.filter(it =>
            it.label.cn !== zhmessage[it.key] || it.label.en !== enmessage[it.key]
        )
        setShowData(showData)
    }

    const unuseButMange = () => {
        setShowData(data.filter(it => unuseKeys.includes(it.key)))
    }

    const repeatField = () => {
        const counts = _.countBy(data, 'key')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        const showData = repeatNames.reduce((res, item) => {
            res = res.concat(data.filter(it => it.key === item))
            return res
        }, [])
        setShowData(showData)
    }

    const moreScan = () => {
        const scanKeys = localesData.scanFile.filter(it => it.path.toString() !== 'un_scan').map(it => it.key)
        setShowData(data.filter(it => !scanKeys.includes(it.key)))
    }


    // 翻译一致的字段
    const translationIsSame = () => {
        const d =  _.uniqBy(data, 'key')
        const counts = _.countBy(d, 'label.cn')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        const showData = repeatNames.reduce((res, item) => {
            res = res.concat(d.filter(it => it.label.cn === item))
            return res
        }, [])

        const repeatGroup = repeatNames.reduce((res, item) => {
            res.push(d.filter(it => it.label.cn === item).reduce((r, i) => {
                r[i.key] = i.label.cn
                return r
            }, {}))
            return res
        }, [])
        console.log(repeatGroup)

        setShowData(showData)
    }

    // 翻译完全一样
    const trIsSame = () => {
        const messages = Object.keys(zhmessage).map(it => ({
            key: it,
            cn: zhmessage[it],
            en: enmessage[it]
        }))
        const groupData = messages
            .filter(it => !it.key.startsWith('operation.') && !it.key.startsWith('menu.') && !it.key.startsWith('report.'))
            .map(it => ({key: it.key, value: it.cn + it.en}))
        const showData = Object
            .values(_.groupBy(groupData, 'value'))
            .filter(it => it.length > 1)
            .map(it => it.map(item => ({key: item.key, cn: zhmessage[item.key], en: enmessage[item.key]})))

        console.log(showData)
        setShowData(groupData)
    }

    const getPartSame = (messages) => {
        const messageList = Object.keys(messages).map(it => ({
            key: it,
            cn: zhmessage[it],
            en: enmessage[it]
        }))
        const d = _.uniqWith(messageList, (one, two) => one.cn ===  two.cn && one.en === two.en)
        const counts = _.countBy(d, 'cn')
        const repeatNames =  Object.keys(counts).filter(key => counts[key] > 1)
        return repeatNames.reduce((res, item) => {
            res = res.concat(d.filter(it => it.cn === item))
            return res
        }, [])
    }


    // 中文一致但是英文不一致的字段
    const partSame = (messages) => {
        setShowData(getPartSame(messages))
    }


    return <div>
        <div style={{marginBottom: '20px'}}>
            <Button.Group>
                <Button onClick={translationIsNotSame}>翻译和locales中不一致的字段</Button>
                <Button onClick={unuseButMange}>应该废弃但还是纳入管理的字段</Button>
                <Button onClick={repeatField}>多个模块重复出现的key</Button>
                <Button onClick={translationIsSame}>翻译一致的key</Button>
                <Button onClick={trIsSame}>翻译完全一致的key</Button>
                <Button onClick={() => partSame(zhmessage)}>中文一致但是英文不一致的字段</Button>
                <Button onClick={moreScan}>比扫描结果多出来的字段</Button>
                {/* TODO 校验模块名是否正确 */}
                <Button onClick={() => setShowData(_.uniqBy(data, 'key'))}>去重</Button>
                <Button onClick={() => setShowData(data)}>重置</Button>
            </Button.Group>
        </div>
        <LocalesTable dataSource={showData} modules={allModules} />
    </div>

}