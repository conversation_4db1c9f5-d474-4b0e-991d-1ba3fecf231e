import React, {useMemo, useState} from "react";
import {Button, Cascader, Checkbox, Input, Select, Table} from "antd";
import {createUUid} from "../../../../../utils/uuid";
import {fieldType} from "../util";
import {message as zhmessage} from "../../../../../locales/zh";
import {message as enmessage} from "../../../../../locales/en";
import {unuseKeys} from "../special-field/unuse";
import {machineKeys} from "../special-field/machine";
import _ from "lodash";
import {backendKeys} from "../special-field/backend";
import ExcelJS from "exceljs";
import {saveAs} from "file-saver";

export const LocalesTable = (props) => {

    const [search, setSearch] = useState({
        path: [], type: '', value: '', excludeOperator: false, excludeMenu: false,
        excludeUnuse<PERSON>ey: false, excludeMachine<PERSON>ey: false, duplicateRemoval: false,
        excludeBackendKey: false, unScan: false
    })

    const columns = [
        {
           title: "#", dataIndex: "#", width: 80, render: (text, record, index) => index + 1
        },
        {
            title: 'key', dataIndex: 'key', key: 'key', width: 180,
        },
        {
            title: '中文', dataIndex: 'name', key: 'name', width: 200,
            render: (text, record) => zhmessage[record.key]
        },
        {
            title: '英文', dataIndex: 'name', key: 'name', width: 200,
            render: (text, record) => enmessage[record.key]
        },
        {
            title: '类型', dataIndex: 'type', key: 'type', width: 200,
            render: (text) => fieldType.find(it => it.value === text)?.label || '-'
        },
        {
            title: '路径', dataIndex: 'path', key: 'path', width: 250,
            render: (text) => text?.join('/')
        },
    ]

    const like = (text1, text2) => {
        if (!text1 || !text2) return false
        return text1?.toUpperCase().indexOf(text2?.toUpperCase()) !== -1
    }

    const showData = useMemo(() => {
        const {path, type, value, excludeOperator, excludeMenu,
            excludeUnuseKey, excludeMachineKey, duplicateRemoval, excludeBackendKey, unScan} = search
        const data = props.dataSource
            .filter(it => !path || path.length === 0 || it.path.slice(0, path.length).toString() === path.toString())
            .filter(it => !type || it.type === type)
            .filter(it => !value || like(it.key, value) || like(zhmessage[it.key], value) || like(enmessage[it.key], value))
            .filter(it => !excludeOperator || !it.key.startsWith('operation'))
            .filter(it => !excludeMenu || !it.key.startsWith('menu'))
            .filter(it => !excludeUnuseKey || !unuseKeys.includes(it.key))
            .filter(it => !excludeMachineKey || !machineKeys.includes(it.key))
            .filter(it => !excludeBackendKey || !backendKeys.includes(it.key))
            .filter(it => !unScan || it.path[0] !== 'un_scan')
        return duplicateRemoval ? _.uniqBy(data, 'key') : data
    }, [props.dataSource, search])

    const print = () => {
        const data = showData.reduce((res, item) => {
            res[item.key] = item.value
            return res
        }, {})
        console.log(data)
    }

    const printOrigin = () => {
        const data = showData.map(it => {
            const d = {key: it.key}
            // const d = {key: it.key, cn: zhmessage[it.key], en: enmessage[it.key]}
            if (it.type) d.type = it.type
            return d
        })
        console.log(data)
        // download(data)
    }

    const download = async (data) => {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet();
        worksheet.addRow(['key', "中文翻译", "英文翻译"])
        data.forEach((item) => {
            worksheet.addRow([item.key, item.cn, item.en])
        })
        // 生成 Excel 文件
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
        saveAs(blob, '翻译.xlsx')
    }


    return  <div>
        <div style={{marginBottom: '16px'}}>
            <Cascader
                options={props.modules}
                onChange={value => setSearch({...search, path: value})}
                style={{width: '300px'}}
                changeOnSelect
                expandTrigger="hover"
                placeholder="选择路径"
            />
            <Select
                style={{width: '200px'}}
                options={fieldType}
                placeholder={'选择类型'}
                allowClear
                onChange={value => setSearch({...search, type: value})}
            />
            <Input
                style={{width: '200px'}}
                placeholder={'输入key或翻译'}
                onChange={e => setSearch({...search, value: e.target.value})}
            />
            <Button onClick={print}>打印</Button>
            <Button onClick={printOrigin}>打印原格式</Button>

        </div>
        <div>
            <Checkbox
                style={{marginLeft: '12px'}}
                onChange={e => setSearch({...search, duplicateRemoval: e.target.checked})}
            >
                去重
            </Checkbox>
            <Checkbox
                style={{marginLeft: '12px'}}
                onChange={e => setSearch({...search, excludeOperator: e.target.checked})}
            >
                排除operation
            </Checkbox>
            <Checkbox
                onChange={e => setSearch({...search, excludeMenu: e.target.checked})}
            >
                排除menu
            </Checkbox>
            <Checkbox
                onChange={e => setSearch({...search, excludeUnuseKey: e.target.checked})}
            >
                排除人工维护的废弃key
            </Checkbox>
            <Checkbox
                onChange={e => setSearch({...search, excludeMachineKey: e.target.checked})}
            >
                排除人工维护的机翻key
            </Checkbox>
            <Checkbox
                onChange={e => setSearch({...search, excludeBackendKey: e.target.checked})}
            >
                排除后端返回字段
            </Checkbox>
            <Checkbox
                onChange={e => setSearch({...search, unScan: e.target.checked})}
            >
                排除没扫描到的字段
            </Checkbox>
        </div>
        <Table
            rowKey={() => createUUid()}
            columns={columns}
            dataSource={showData}
            scroll={{y: '50vh'}}
        />
    </div>


}