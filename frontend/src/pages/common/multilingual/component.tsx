import React, {useRef} from "react"
import {FormattedMessage as DefaultFormattedMessage, useIntl} from "react-intl"
import {TranslateModal} from "./translate-modal"

interface messageDescriptor {
    id: string,
    defaultMessage?: string,
    allowComponent?: boolean,
}

interface localeProps {
    children: any,
    filedKey: string,
    [key: string]: any,
}

interface messageProps {
    id: string,
    [key: string]: any,
}


const isPreviewMode = () => {
    return !!sessionStorage.getItem('project_preview')
}

export const CustomLocale = ({children, filedKey, ...props}: localeProps) => {
    if (isPreviewMode()) {
        const translateRef = useRef<any>()
        return <>
            <span
                style={{
                    background: 'rgba(255, 239, 204, 1)',
                    cursor: 'pointer',
                    fontSize: 'inherit',
                    // fontWeight: 'bold',
                    // color: 'rgba(0, 0, 0, 0.85)',
                    padding: '6px 2px',
                    width: '100%',
                    borderBottom: '2px solid rgba(255, 174, 0, 1)',
                    ...(children.props?.style || {})
                }}
                onClick={event => {
                    event.stopPropagation()
                    console.log('点击了：', filedKey)
                    translateRef?.current?.show(filedKey)
                }}
            >
                {children}
            </span>
            <TranslateModal bind={translateRef} />
        </>
    }
    return children
}

export const FormattedMessage = ({id, ...props}: messageProps) => {
    if (props.disabledPreview) return <DefaultFormattedMessage id={id} {...props} />
    return <CustomLocale filedKey={id}>
        <DefaultFormattedMessage id={id} {...props} />
    </CustomLocale>
}


export const useTranslation = () => {
    const intl = useIntl()

    const formatMessage = (descriptor: messageDescriptor, values?: Record<string, any>): string | any => {
        const {id, defaultMessage, allowComponent} = descriptor
        if (isPreviewMode() && allowComponent) {
            return <FormattedMessage id={id} defaultMessage={defaultMessage} values={values} />
        }
        return intl.formatMessage({id, defaultMessage: defaultMessage || id}, values)
    }

    const formatErrorMessage = (errMsg: string, errorData: any) => {
        // TODO
        return errMsg
    }

    return {
        locale: intl.locale,
        formatMessage,
        formatErrorMessage,
        formatDate: intl.formatDate,
        formatNumber: intl.formatNumber,
    }
}