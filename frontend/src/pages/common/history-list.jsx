import React from "react";
import { FormattedMessage } from "react-intl";
import { <PERSON><PERSON>, Col, ConfigProvider, Empty, Modal, Pagination, Row, Table, Spin } from "antd";
import ReactToPrint from "react-to-print";

import { HistoryTable } from "./history-list-table"
// import moment from "moment";
import moment from 'moment-timezone';
import { useSafeState } from "ahooks";
import { useFetch } from "../../hooks/request";
import { getHistoryList } from "../../api/history";
import { useAuth } from "../../context/auth";
import { getByCohort } from "../../api/projects";

import EmptyImg from 'images/empty.png';
import { CaretDownOutlined, CaretRightOutlined } from "@ant-design/icons";
import { medicineStatusAliData } from "../../tools/medicine_status";
import styled from "@emotion/styled";
import {AuthButton} from "./auth-wrap";
export const HistoryList = (props) => {
    const auth = useAuth()
    const [visible, setVisible] = useSafeState(false);
    const [key, setKey] = useSafeState(false);
    const [id, setId] = useSafeState(false);
    const [cohortId, setCohortId] = useSafeState(null);
    const [cohortName, setCohortName] = useSafeState(null);

    const [timeZone, setTimeZone] = useSafeState(null);
    const [tz, setTz] = useSafeState(null);
    const [currentPage, setCurrentPage] = useSafeState(1);
    const [pageSize, setPageSize] = useSafeState(20);
    const [total, setTotal] = useSafeState(0);
    const [data, setData] = useSafeState([]);
    const [loading, setLoading] = useSafeState(false);
    const [tableHeight, setTableHeight] = useSafeState(300);
    const [refresh, setRefresh] = useSafeState(0);
    const [print, setPrint] = useSafeState(true);
    const { runAsync, loading: getHistoryListLoading } = useFetch(getHistoryList, { manual: true })
    const connectEdc = auth.project.info.connect_edc;
    const pushMode = auth.project.info.push_mode;

    const { runAsync: getCohortNameRun, loading: getCohortNameLoading } = useFetch(getByCohort, { manual: true });


    const show = (key, id, timeZone, siteTimeZone, cohortId, tz) => {
        if (props.permission !== undefined) {
            setPrint(props.permission)
        }
        setVisible(true);
        setLoading(true);
        setKey(key);
        setId(id);
        setCohortId(cohortId);
        if (siteTimeZone) {
            setTimeZone(siteTimeZone)
        } else {
            setTimeZone((auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "" && auth.project.info.timeZoneStr !== undefined) ? Number(auth.project.info.timeZoneStr) : 8);
        }
        if (tz && tz !== "-") {
            setTz(tz)
        } else {
            setTz(null);
        }

        setRefresh(refresh + 1)
    };

    const hide = () => {
        setPrint(true)
        setData([]);
        setTotal(0);
        setCurrentPage(1);
        setPageSize(20);
        setVisible(false);
    };


    function formatTimezoneOffset(offset) {
        const negative = offset < 0;
        offset = Math.abs(offset);
        const hours = Math.floor(offset);
        const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

        const sign = negative ? "-" : "";
        const hh = String(hours).padStart(2, '0');
        const mm = String(minutes).padStart(2, '0');

        return `${sign}${hh}:${mm}`;
    }

    const formatTimezone = (timestamp, timezone) => {
        const tzMoment = moment.tz(timestamp * 1000, timezone);

        // 获取时区偏移分钟数（如 -330 表示 UTC-05:30）
        const offsetMinutes = tzMoment.utcOffset(); // 单位是分钟
        const totalHours = Math.abs(offsetMinutes);
        const hours = Math.floor(totalHours / 60);
        const minutes = totalHours % 60;

        // 构造 UTC±HH:mm 格式
        const sign = offsetMinutes < 0 ? "-" : "+";
        const formattedOffset = `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

        // 格式化时间
        const formattedTime = tzMoment.format('YYYY-MM-DD HH:mm:ss');

        // 拼接结果
        return `${formattedTime}(UTC${formattedOffset})`;
    };

    React.useEffect(
        () => {
            setTableHeight(document.documentElement.clientHeight - 130);
        },
        []
    );

    React.useEffect(
        () => {
            if (visible && id !== null && id !== "" && id !== "000000000000000000000000") {
                runAsync({
                    key: key,
                    oid: id,
                    start: (currentPage - 1) * pageSize,
                    limit: pageSize,
                    envId: auth.env.id,
                    cohortId: cohortId,
                    roleId: auth.project.permissions.role_id,
                }).then(
                    (result) => {
                        const data = result.data
                        setTotal(data.total);
                        setData(data.items);
                        setLoading(false);
                    }
                );
                if (cohortId !== null && cohortId !== "000000000000000000000000") {
                    getCohortNameRun({
                        customerId: auth.customerId,
                        projectId: auth.project.id,
                        envId: auth.env.id,
                        cohortId: cohortId,
                    }).then((result) => {
                        setCohortName(result.data);
                    });
                }
            } else {
                setLoading(false)
            }
        }, [refresh, currentPage, pageSize]
    )

    // 根据特定条件生成需要展开的行的 key
    const expandedRowKeys = data.reduce((acc, cur) => {
        if (cur?.tableInfo?.title !== null) {
            acc.push(cur.time);
        }
        return acc;
    }, []);

    const expandedDetail = (resultRecord) => {
        return resultRecord?.tableInfo?.title ?
            <ChildTable rowKey={(resultRecord, index) => (index)} dataSource={resultRecord?.tableInfo?.value}
                pagination={false}>
                {
                    resultRecord?.tableInfo?.title?.map((it) =>
                        <Table.Column
                            title={it}
                            dataIndex={it}
                            key={it}
                            ellipsis
                            width={250}
                            render={
                                (value, record, index) => (
                                    index === 0 ?
                                        value?.split(',').map((item, i) => (
                                            <div key={i}>{item}</div>
                                        )) : value
                                )
                            }

                        />
                    )
                }
            </ChildTable>
            : null
    };

    let componentRef = React.useRef();
    React.useImperativeHandle(props.bind, () => ({ show }));
    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"common.history"} />}
                visible={visible}
                onCancel={hide}
                centered
                maskClosable={false}

                destroyOnClose={true}
                className="custom-large-modal"
                footer={
                    (data.length > 20 || total > 20) && (
                        <Row justify="end">
                            <Col>
                                <Pagination
                                    hideOnSinglePage={false}
                                    className="text-right"
                                    current={currentPage}
                                    pageSize={pageSize}
                                    pageSizeOptions={['10', '20', '50', '100']}
                                    total={total}
                                    showSizeChanger
                                    showTotal={(total, range) => (`${range[0]} - ${range[1]} / ${total}`)}
                                    onChange={(page, pageSize) => {
                                        setCurrentPage(page);
                                    }}
                                    onShowSizeChange={(current, size) => {
                                        setCurrentPage(1);
                                        setPageSize(size);
                                    }}
                                />
                            </Col>
                        </Row>
                    )
                }
            >
                <Row justify={"end"}>
                    <div>
                        {
                            print && <ReactToPrint
                                trigger={() => {
                                    return <AuthButton
                                        show
                                        previewProps={{disabledClick: true}}
                                        type={"primary"}
                                    >
                                        <FormattedMessage id={"common.print"} />
                                    </AuthButton>;
                                }}
                                content={() => componentRef}
                            />
                        }
                    </div>
                </Row>
                <ConfigProvider
                    renderEmpty={
                        () => {
                            return <Empty
                                image={<img alt={""} src={EmptyImg} style={{ width: 200, height: 142 }}></img>}
                            />
                        }
                    }
                >
                    <Spin spinning={getHistoryListLoading}>
                        <Table
                            loading={loading}
                            style={{ marginTop: 16 }}
                            dataSource={data}
                            rowKey={(record) => (record.time)}
                            pagination={false}

                            expandable={{
                                rowExpandable: record => record?.tableInfo?.title !== null,
                                defaultExpandAllRows: true,
                                // expandedRowKeys: expandedRowKeys, // 默认展开符合条件的行
                                expandedRowRender: record => expandedDetail(record),
                                expandIcon: ({ expanded, onExpand, record }) =>
                                    record?.tableInfo?.title !== null?(
                                        expanded ? (
                                            <CaretDownOutlined onClick={e => onExpand(record, e)} />
                                        ) : (
                                            <CaretRightOutlined onClick={e => onExpand(record, e)} />
                                        )
                                    ):null,
                                columnWidth: 45,
                            }}

                        >
                            <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={64}
                                render={(text, record, index) => ((currentPage - 1) * pageSize + index + 1)} />
                            <Table.Column title={<FormattedMessage id="common.operator" />} key="user" dataIndex="user"
                                width={(connectEdc === 1 && pushMode === 1) ? 270 : 140} align="left" ellipsis />
                            <Table.Column
                                title={<FormattedMessage id="common.operation.time" />}
                                key="time"
                                dataIndex="time"
                                align="left"
                                width={255}
                                ellipsis
                                render={

                                    tz === null ?
                                        (value, record, index) => (
                                            (value === null || value === 0) ? '' : (moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss') + (timeZone >= 0 ? "(UTC+" : "(UTC") + formatTimezoneOffset(timeZone) + ")")
                                        ) :
                                        (value, record, index) => (
                                            (value === null || value === 0) ? '' : formatTimezone(value, tz)
                                        )
                                } />
                            <Table.Column title={<FormattedMessage id="common.operation.content" />} key="content"
                                dataIndex="content" align="left" />
                        </Table>
                    </Spin>
                </ConfigProvider>
                <div
                    style={{ display: "none" }}
                >
                    <div
                        ref={el => (componentRef = el)}>
                        <HistoryTable keys={key} data={data} tz={tz} cohortName={cohortName} loading={loading} tableHeight={tableHeight} timeZone={timeZone} />
                    </div>

                </div>
            </Modal>
        </React.Fragment>
    )
};

const ChildTable = styled(Table)`
  .ant-table-thead > tr > th {
    background: #ffffff;
  }
`