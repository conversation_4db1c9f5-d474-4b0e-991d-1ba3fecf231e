import React from 'react';
import {Col, Row, Table} from "antd";
import {FormattedMessage} from "react-intl";
// import moment from "moment";
import moment from 'moment-timezone';
import {useAuth} from "../../context/auth";
import {CaretDownOutlined, CaretRightOutlined} from "@ant-design/icons";

export const HistoryTable = (props) =>{
    const auth = useAuth()

    function formatTimezoneOffset(offset) {
        const negative = offset < 0;
        offset = Math.abs(offset);
        const hours = Math.floor(offset);
        const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

        const sign = negative ? "-" : "";
        const hh = String(hours).padStart(2, '0');
        const mm = String(minutes).padStart(2, '0');

        return `${sign}${hh}:${mm}`;
    }

    const formatTimezone = (timestamp, timezone) => {
        const tzMoment = moment.tz(timestamp * 1000, timezone);

        // 获取时区偏移分钟数（如 -330 表示 UTC-05:30）
        const offsetMinutes = tzMoment.utcOffset(); // 单位是分钟
        const totalHours = Math.abs(offsetMinutes);
        const hours = Math.floor(totalHours / 60);
        const minutes = totalHours % 60;

        // 构造 UTC±HH:mm 格式
        const sign = offsetMinutes < 0 ? "-" : "+";
        const formattedOffset = `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

        // 格式化时间
        const formattedTime = tzMoment.format('YYYY-MM-DD HH:mm:ss');

        // 拼接结果
        return `${formattedTime}(UTC${formattedOffset})`;
    };

    // 根据特定条件生成需要展开的行的 key
    const expandedRowKeys = props.data.reduce((acc, cur) => {
        if (cur?.tableInfo?.title !== null) {
            acc.push(cur.time);
        }
        return acc;
    }, []);

    const expandedDetail = (resultRecord) => {
        return resultRecord?.tableInfo?.title ?
            <Table rowKey={(resultRecord, index) => (index)} dataSource={resultRecord?.tableInfo?.value}
                pagination={false}>
                {
                    resultRecord?.tableInfo?.title?.map((it) =>
                        <Table.Column
                            title={it}
                            dataIndex={it}
                            key={it}
                            ellipsis
                            width={250}
                            render={
                                (value, record, index) => (
                                    index === 0 ?
                                        value?.split(',').map((item, i) => (
                                            <div key={i}>{item}</div>
                                        )) : value
                                )
                            }

                        />
                    )
                }
            </Table>
            : null
    };
    return (
        <React.Fragment>
            <div
                className="mar-all-10">

            <Row className={"history_title"}>{<FormattedMessage id={props.keys+""} />}</Row>
            <Row>{<FormattedMessage id="projects.name" />}:{auth.project.info.name}</Row>
            <Row>{<FormattedMessage id="projects.sponsor" />}:{auth.project.info.sponsor}</Row>
            {
                auth.project.info.type !== 1 && props.cohortName ?
                <Row>
                    {  
                        auth.project.info.type === 2?
                        <FormattedMessage id="report.attributes.random.cohort" />:
                        <FormattedMessage id="report.attributes.random.stage" />
                    }:{props.cohortName}
                </Row>:null
            }

            {
                props?.keys !== "history.medicine" && props?.keys !== "history.order" && props?.keys !== "history.medicine.drugFreeze"?
                <Row className="mar-top-10">
                    <Col span={8}>{<FormattedMessage id="history.sign" />}:</Col>
                    <Col span={8}>{<FormattedMessage id="history.date" />}:</Col>
                </Row>:null
            }

            <Table
                loading={props.loading}
                style={{height: props.tableHeight}}
                className="mar-top-10"
                size="small"
                dataSource={props.data}
                rowKey={(record) => (record.time)}
                // rowKey={(record,index) => (index)}
                pagination={false}

                expandable={{
                    rowExpandable: record => record?.tableInfo?.title !== null,
                    defaultExpandAllRows: true,
                    expandedRowKeys: expandedRowKeys, // 默认展开符合条件的行
                    expandedRowRender: record => expandedDetail(record),
                    expandIcon: ({ expanded, onExpand, record }) =>
                        record?.tableInfo?.title !== null?(
                            expanded ? (
                                <CaretDownOutlined onClick={e => onExpand(record, e)} />
                            ) : (
                                <CaretRightOutlined onClick={e => onExpand(record, e)} />
                            )
                        ):null,
                    columnWidth: 45,
                }}
            >
                <Table.Column title={<FormattedMessage id="common.serial"/>} dataIndex="#" key="#" width={35} render={(text, record, index) => (index + 1)} />
                <Table.Column
                    title={<FormattedMessage id="common.operator" />}
                    key="user"
                    dataIndex="user"
                    width={120}
                    align="left"
                    ellipsis
                    render={
                       (value, record, index) => (
                           <div
                               style={{ whiteSpace:'pre-line'}}
                           >
                               {value}
                           </div>
                       )
                    }/>
                <Table.Column
                    title={<FormattedMessage id="common.operation.time" />}
                    key="time"
                    dataIndex="time"
                    align="left"
                    width={260}
                    ellipsis
                    render={
                        props.tz === null?
                        (value, record, index) => (
                            (value === null || value === 0) ? '' : moment.unix(value).utc().add(props.timeZone,"hour").format('YYYY-MM-DD HH:mm:ss') + (props.timeZone >= 0 ? "(UTC+" : "(UTC") + formatTimezoneOffset(props.timeZone) + ")"
                        ):
                        (value, record, index) => (
                            (value === null || value === 0) ? '' : formatTimezone(value,props.tz)
                        )
                    }/>
                <Table.Column title={<FormattedMessage id="common.operation.content" />} key="content"
                    dataIndex="content"
                    align="left"
                    ellipsis
                    render={
                        (value, record, index) => (
                            <div
                                style={{ whiteSpace:'pre-line', width:'300px'}}
                            >
                                {value}
                            </div>
                        )
                }
                />
            </Table>
            </div>

        </React.Fragment>
    );
}