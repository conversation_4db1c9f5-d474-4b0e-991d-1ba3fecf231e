import React from "react"
import {<PERSON><PERSON>, Dropdown, <PERSON>u, <PERSON>over, Radio, Select, Space, Switch, Tooltip} from "antd"
import _ from "lodash"

interface authProps {
    show?: boolean,
    previewProps?: {
        disabledClick?: boolean, // 禁用点击事件
        hideOnPreview?: boolean // 预览模式下隐藏
    },
    [key: string]: any,
}

const isPreviewMode = () => {
    return !!sessionStorage.getItem('project_preview')
}

const previewButton = (children: any) => {
    if (!children.props) return children
    const style = {color: 'rgba(22, 93, 255, 1)', fontWeight: 'normal'}
    return React.cloneElement(children, {style: style}, children.ref)
}

const previewMenuItems = (items: any[]) => {
    return items.map((it: any) => ({
        ...it,
        style: {padding: 0},
        label: <div
            style={{
                width: '100%', height: '36px', display: 'flex', alignItems: 'center',
                background: 'rgba(255, 239, 204, 1)', cursor: 'pointer',
                borderBottom: '2px solid rgba(255, 174, 0, 1)', padding: '5px 12px'
            }}
        >
            {React.cloneElement(it.label, {style: {padding: '0', borderBottom: 'none'}}, it.label)}
        </div>
    }))
}

const wrapAuth = (ComposedComponent: any) => {
    return ({show, previewProps, ...restProps}: authProps) => {
        // 预览模式禁用所有点击事件
        if (isPreviewMode()) {
            restProps.onClick = () => {}
            restProps.onChange = () => {}
            if (ComposedComponent.displayName === 'Button') {
               return previewButton(restProps.children)
            }
            if (ComposedComponent.name === 'Dropdown3') {
                if (!!restProps?.menu?.items) {
                    restProps.menu.items = previewMenuItems(restProps.menu.items)
                }
            }
        }
        return <AuthWrap show={show} previewProps={previewProps}>
            <ComposedComponent {...restProps} />
        </AuthWrap>
    }
}

export const AuthWrap = ({show, previewProps, ...props}: authProps) => {
    if (isPreviewMode() && previewProps?.hideOnPreview) return null
    // show非必传，仅当明确传入false时隐藏
    return show === false ? null : props.children
}

export const authButtonFilter = (btns: any[]) => {
    if (isPreviewMode()) {
        return _.uniqWith(btns, (one, two) => one.key ===  two.key && one.label === two.label).map(it => ({
            ...it,
            onClick: () => {}
        }))
    }
    return btns.filter(it => it.show)
}

export const clickFilter = (fn: any) => {
    if (!fn) return fn
    return isPreviewMode() ? () => {} : fn
}

export const previewFilter = (data: any) => {
    return isPreviewMode() ? null : data
}

export const AuthButton = wrapAuth(Button)
export const AuthDropdown = wrapAuth(Dropdown)
export const AuthMenu = wrapAuth(Menu)
export const AuthMenuItem = wrapAuth(Menu.Item)
export const AuthTooltip = wrapAuth(Tooltip)

export const AuthPopover = wrapAuth(Popover)

export const AuthSpace = wrapAuth(Space)
export const AuthSelect = wrapAuth(Select)
export const AuthSwitch = wrapAuth(Switch)

export const AuthRadio = wrapAuth(Radio)

export const AuthSpan = wrapAuth(`span`)
