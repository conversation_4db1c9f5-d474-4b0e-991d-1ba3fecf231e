import styled from "@emotion/styled";
import ReactModal from "react-modal";
import {Button, Col, Image, Input, message, Row, Typography,Form} from "antd";
import {useIntl} from "react-intl";
import React from "react";
import {useAuth} from "../../context/auth";
import {useFetch} from "../../hooks/request";
import {verify} from "../../api/user";
import {useSafeState} from "ahooks";

import LockImage from 'images/lock.png'

export const LockWindow = () => {
    const auth = useAuth()
    const intl = useIntl();
    const [error, setError] = useSafeState(undefined);
    const [password, setPassword] = useSafeState(undefined);
    const { runAsync } = useFetch(verify,
        {
            manual: true,
            onError: async (resp) => {
                const res = await resp.json();
                if (res.code === 1002) {
                    message.error(intl.formatMessage({ id: "common.not-logged-in" }))
                    sessionStorage.removeItem("token");
                    sessionStorage.removeItem("lock");
                    sessionStorage.removeItem("current_project");
                    sessionStorage.removeItem("customerId");
                    sessionStorage.removeItem("cohort");
                    sessionStorage.removeItem("env");
                    sessionStorage.removeItem("learn_token");
                    sessionStorage.removeItem("learn");
                    sessionStorage.removeItem("learn_url");
                    sessionStorage.removeItem("projectMark");
                    sessionStorage.removeItem("systemMark");
                    window.location = auth.cloudUrl;
                } else {
                    setError(res.msg);
                }
            }
        }
    )

    const user_login = () => {
        if (auth.user) {
            runAsync({ username: auth.user?.info.email, password: password,type:3 }).then(
                (result) => {
                    auth.setIdle(false)
                    sessionStorage.removeItem("lock")
                    sessionStorage.setItem("token", result.data.token);
                    if (result) {
                        auth.setUser(result.data.user);
                    }
                }
            )
        } else {
            setError(intl.formatMessage({ id: "common.not-logged-in" }))
        }

    };

    return (
        <React.Fragment>
            {
                auth.user
                    ?
                    <ReactModal
                        ariaHideApp={false}
                        isOpen={sessionStorage.getItem("lock") != null || auth.idle}
                        onRequestClose={() => { }}
                        shouldCloseOnEsc={true}
                        shouldCloseOnOverlayClick={false}
                        style={
                            {
                                overlay: {
                                    width: "100%",
                                    height: "100%",
                                    background: "rgba(0, 0, 0, 0.6)",
                                    padding: 0,
                                    zIndex: 20000,
                                    backdropFilter: "blur(10px)",
                                },
                                content: {
                                    top: '50%',
                                    left: '50%',
                                    right: 'auto',
                                    bottom: 'auto',
                                    marginRight: '-50%',
                                    transform: 'translate(-50%, -50%)',
                                    padding: 0,
                                    border: "none",
                                    background: "rgba(0, 0, 0, 0)",
                                }
                            }
                        }
                    >
                        <WelcomeLayout className="all-center">
                            <input  type="text" style={{height:0,width:0 , borderWidth:0,opacity:0}}></input>

                            <Row justify="center">
                                <Col span={24}>
                                    <Image width={60} height={60} src={LockImage} preview={false} />
                                </Col>
                                <Col span={24} className="mar-top-10">
                                    <p style={{ fontSize: 20, fontWeight: 500 }}>{auth.user.info.email}</p>
                                </Col>

                                <Col span={24}>

                                    <Input.Group compact>
                                        <Input.Password
                                            type={"password"}
                                            value={password}
                                            onChange={(e) => { setError(undefined); setPassword(e.target.value); }}
                                            prefix={<i className="iconfont icon-suoping-mima" style={{ color: "#D3D6DD", fontSize: 18 }}></i>}
                                            size="large"
                                            style={{ width: '330px', textAlign: "left", height: 56 }}
                                            onPressEnter={user_login}
                                            onFocus={() => setError(undefined)}
                                            iconRender={(visible) => {
                                                if (visible) {
                                                    return <span ><i style={{ fontSize: 18 }} className="mouse iconfont icon-mimaxianshi"></i></span>
                                                }
                                                return <span ><i style={{ fontSize: 18 }} className="mouse iconfont icon-mimayincang"></i></span>
                                            }}

                                        />
                                        <Button type="primary" style={{ height: 56 }} size="large" onClick={user_login}>
                                            {intl.formatMessage({ id: "common.ok" })}
                                        </Button>
                                    </Input.Group>
                                </Col>
                                <Col className="hor-center">
                                    <Typography.Text type="danger" style={{ width: "400px", fontSize: "14px", textAlign: "left" }}>{error}</Typography.Text>
                                </Col>
                                <Col span={24} className="mar-top-10">
                                    <p style={{ fontSize: 16, fontWeight: 400 }}>{intl.formatMessage({ id: "tips.locked" })}</p>
                                </Col>
                            </Row>
                        </WelcomeLayout>
                    </ReactModal>
                    :
                    null
            }
        </React.Fragment>

    );
};

const WelcomeLayout = styled.div`
    width: 100%;
    height: 100%;
    position: relative;
    color: white;
    font-size: 20px;
`;