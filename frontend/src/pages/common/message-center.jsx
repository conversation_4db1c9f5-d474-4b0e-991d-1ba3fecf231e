import React, {useEffect, useState} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Divider, Form, Input, List, message, Modal, Row, Tabs, Typography} from "antd";
import {FormattedMessage, useIntl} from "react-intl";
import {useRafInterval, useSafeState} from 'ahooks';
import ReactHtmlParser from 'react-html-parser';
import InfiniteScroll from 'react-infinite-scroll-component';
import {useFetch} from "../../hooks/request";
import {
    mailAuth,
    messageAllRead,
    messageCenter,
    messageCenterQuantity,
    messageSystemQuantity, 
    messageQuantity,
    messageSingleRead,
    sendAgain
} from "../../api/page_notice";
import {useGlobal} from "../../context/global";
import {useNavigate} from "react-router-dom";
import dayjs from "dayjs";

export const MessageCenter = (props) => {
    const navigate = useNavigate();
    const g = useGlobal();
    const [form] = Form.useForm();
    // const [dot, setDot] = useSafeState(false);
    const [mailErrorDot, setMailErrorDot] = useSafeState(false);
    const [systemUpdateDot, setSystemUpdateDot] = useSafeState(false);
    const { TabPane } = Tabs;
    const [mailData, setMailData] = useSafeState([]);
    const [mailDataRead, setMailDataRead] = useSafeState([]);
    const [systemData, setSystemData] = useSafeState([]);
    const [systemReadData, setSystemReadData] = useSafeState([]);
    // const [mailUnreadData, setMailUnreadData] = useSafeState([]);
    // const [mailReadUnreadData, setMailReadUnreadData] = useSafeState([]);
    // const [systemUnreadData, setSystemUnreadData] = useSafeState([]);
    // const [systemReadUnreadData, setSystemReadUnreadData] = useSafeState([]);
    const { Text, Paragraph } = Typography;
    const intl = useIntl();
    const { formatMessage } = intl;
    const [tabsKey, setTabsKey] = useSafeState("1");
    const [confirmLoading, setConfirmLoading] = useState(false);
    
    const [mailQuantity, setMailQuantity] = useSafeState(0);
    const [mailReadQuantity, setMailReadQuantity] = useSafeState(0);
    const [systemQuantity, setSystemQuantity] = useSafeState(0);
    const [systemReadQuantity, setSystemReadQuantity] = useSafeState(0);

    const [isHover, setIsHover] = useState(false);
    const [isIndex, setIsIndex] = useState(-1);

    const [isRemind, setIsRemind] = useState(false);
    
    const [mailSkip, setMailSkip] = useSafeState(0);
    const [mailReadSkip, setMailReadSkip] = useSafeState(0);
    const [systemSkip, setSystemSkip] = useSafeState(0);
    const [systemReadSkip, setSystemReadSkip] = useSafeState(0);
    const [systemHasMore, setSystemHasMore] = useSafeState(false);
    const [systemReadHasMore, setSystemReadHasMore] = useSafeState(false);
    const [mailHasMore, setMailHasMore] = useSafeState(false);
    const [mailReadHasMore, setMailReadHasMore] = useSafeState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageCenterId, setMessageCenterId] = useSafeState(false);
    const [mailAuthority, setMailAuthority] = useSafeState(0);
    const operations = <div onClick={() => onReadAll()} style={{marginBottom: 0, cursor:" pointer", fontFamily:"PingFang SC",fontSize: 12, fontWeight:400, color:"#1D2129"}}><svg t="1677824634751" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3118" className="iconfont mouse" style={{ verticalAlign:"middle",}} width="12" height="12"><path d="M307.2 776.533333c-8.533333 0-25.6 0-34.133333-8.533333l-256-238.933333C0 512 0 477.866667 17.066667 460.8c17.066667-17.066667 51.2-17.066667 68.266666 0l221.866667 204.8 401.066667-477.866667c17.066667-17.066667 42.666667-25.6 68.266666-8.533333 17.066667 17.066667 25.6 42.666667 8.533334 68.266667L341.333333 759.466667c-8.533333 17.066667-17.066667 17.066667-34.133333 17.066666z" fill="#ADB2BA" p-id="3119"/><path d="M537.6 810.666667c-8.533333 0-25.6-8.533333-34.133333-17.066667L366.933333 665.6l25.6-25.6 42.666667-34.133333 93.866667 93.866666L938.666667 221.866667c17.066667-17.066667 51.2-25.6 68.266666-8.533334 17.066667 17.066667 25.6 42.666667 8.533334 68.266667l-435.2 512c-8.533333 8.533333-25.6 17.066667-42.666667 17.066667z" fill="#ADB2BA" p-id="3120"></path></svg><span style={{ paddingLeft: '4px' }}><FormattedMessage id="page_notice.message_center_all_read"/></span></div>
    ;

    const showModal = (id) => {
        setMessageCenterId(id);
        setIsModalOpen(true);
    };
    
    const handleOk = () => {
        setConfirmLoading(true);
        form.validateFields().then(values => {
            onResend(messageCenterId,values.email);
        })
    };
    
    const handleCancel = () => {
        setIsModalOpen(false);
        form.setFieldValue()
    };

    const getNew = () => {
        onMailAuth()
        setSystemSkip(0)
        getSystem(0);
        setSystemReadSkip(0)
        getSystemRead(0,1)
        setMailSkip(0)
        getMail(0)
        setMailReadSkip(0)
        getMailRead(0,1)
        getMailQuantity()
        getMailReadQuantity()
        getSystemQuantity()
        getSystemReadQuantity()
    }


    const { runAsync: run_mail } = useFetch(messageCenter, { manual: true });
    const { runAsync: run_mail_read } = useFetch(messageCenter, { manual: true });
    const { runAsync: run_system } = useFetch(messageCenter, { manual: true });
    const { runAsync: run_system_read } = useFetch(messageCenter, { manual: true });

    const { runAsync: run_mail_quantity } = useFetch(messageCenterQuantity, { manual: true });
    const { runAsync: run_mail_read_quantity } = useFetch(messageCenterQuantity, { manual: true });
    const { runAsync: run_system_quantity } = useFetch(messageCenterQuantity, { manual: true });
    const { runAsync: run_system_read_quantity } = useFetch(messageCenterQuantity, { manual: true });

    const { runAsync: run_message_quantity } = useFetch(messageQuantity, { manual: true });
    const { runAsync: run_message_system_quantity } = useFetch(messageSystemQuantity, { manual: true });

    const getMessageQuantity = () => {
        run_message_quantity({
        }).then((data) => {
            const result = data.data;
            g.setQuantity(result);
        });
    }

    const getMessageSystemQuantity = () => {
        run_message_system_quantity({
        }).then((data) => {
            const result = data.data;
            g.setQuantity(result)
        });
    }

    const getMailQuantity = () => {
        run_mail_quantity({
            noticeType: 2,
        }).then((data) => {
            const result = data.data;
            setMailQuantity(result);
        });
    }

    const getMailReadQuantity = () => {
        run_mail_read_quantity({
            noticeType: 2,
            isRead: 1,
        }).then((data) => {
            const result = data.data;
            setMailReadQuantity(result);
        });
    }

    const getSystemQuantity = () => {
        run_system_quantity({
            noticeType: 1,
        }).then((data) => {
            const result = data.data;
            setSystemQuantity(result);
        });
    }

    const getSystemReadQuantity = () => {
        run_system_read_quantity({
            noticeType: 1,
            isRead: 1,
        }).then((data) => {
            const result = data.data;
            setSystemReadQuantity(result);
        });
    }

    const getSystem = (skip) => {
        const limit = 10
        run_system({
            noticeType: 1,
            start: skip,
            limit: limit
        }).then((data) => {
            const result = data.data;
            let systems = []
            let systemUnread = []
            if (result != null && result.length > 0) {
                if (result.length === limit) {
                    setSystemHasMore(true)
                } else {
                    setSystemHasMore(false);
                }
                for (let i = 0; i < result.length; i++) {
                    let r = result[i];
                    systems.push(r);
                    if (r.read === false) {
                        systemUnread.push(r)
                    }
                }
                if (skip === 0) {
                    setSystemData([...systems])
                } else {
                    setSystemData([...systemData, ...systems])
                }

                // setSystemReadUnreadData(systemUnread);
                if (systemUnread.length !== 0 || mailErrorDot) {
                    // setDot(true)
                }
                if (systemUnread.length !== 0) {
                    setSystemUpdateDot(true)
                }
            }
        })
    }

    const getSystemRead = (skip,key) => {
        const limit = 10
        run_system_read({
            noticeType: 1,
            isRead: key,
            start: skip,
            limit: limit
        }).then((data) => {
            const result = data.data;
            let systems = []
            let systemUnread = []
            if (result != null && result.length > 0) {
                if (result.length === limit) {
                    setSystemReadHasMore(true)
                } else {
                    setSystemReadHasMore(false);
                }
                for (let i = 0; i < result.length; i++) {
                    let r = result[i];
                    systems.push(r);
                    if (r.read === false) {
                        systemUnread.push(r)
                    }
                }
                if (skip === 0) {
                    setSystemReadData([...systems])
                } else {
                    setSystemReadData([...systemReadData, ...systems])
                }

                // setSystemUnreadData(systemUnread);
                if (systemUnread.length !== 0 || mailErrorDot) {
                    // setDot(true)
                }
                if (systemUnread.length !== 0) {
                    setSystemUpdateDot(true)
                }
            }
        })
    }

    const getMail = (skip) => {
        const limit = 10
        run_mail({
            noticeType: 2,
            start: skip,
            limit: limit,
        }).then((data) => {
            const result = data.data;
            let mails = []
            let mailUnread = []
            if (result != null && result.length > 0) {
                if (result.length === limit) {
                    setMailHasMore(true)
                } else {
                    setMailHasMore(false);
                }
                for (let i = 0; i < result.length; i++) {
                    let r = result[i];
                    mails.push(r)
                    if (r.read === false) {
                        mailUnread.push(r)
                    }
                }
                if (skip === 0) {
                    setMailData([...mails]);
                } else {
                    setMailData([...mailData, ...mails]);
                }

                // setMailUnreadData(mailUnread);
                if (systemUpdateDot || mailUnread.length !== 0) {
                    // setDot(true)
                }
                if (mailUnread.length !== 0) {
                    setMailErrorDot(true)
                }
            }
        });
    }


    const getMailRead = (skip,key) => {
        const limit = 10
        run_mail_read({
            noticeType: 2,
            isRead: key,
            start: skip,
            limit: limit
        }).then((data) => {
            const result = data.data;
            let mails = []
            let mailUnread = []
            if (result != null && result.length > 0) {
                if (result.length === limit) {
                    setMailReadHasMore(true)
                } else {
                    setMailReadHasMore(false);
                }
                for (let i = 0; i < result.length; i++) {
                    let r = result[i];
                    mails.push(r)
                    if (r.read === false) {
                        mailUnread.push(r)
                    }
                }
                if (skip === 0) {
                    setMailDataRead([...mails]);
                } else {
                    setMailDataRead([...mailDataRead, ...mails]);
                }
                // setMailReadUnreadData(mailUnread);
                if (systemUpdateDot || mailUnread.length !== 0) {
                    // setDot(true)
                }
                if (mailUnread.length !== 0) {
                    setMailErrorDot(true)
                }
            }
        });
    }

    const changingOverTabs = (key) => {
        setTabsKey(key);
    }

    const showDetail = (content,key) => {
        onReadSingle(key);
        // window.open(`/message-single/` + key);
        navigate("/message-single/" + key);

        getMailQuantity();
        getMailReadQuantity();
        if(mailDataRead.length > 0){
            // systemReadData = systemReadData.filter(element => element !== item)
            setSystemReadData([...mailDataRead.filter(element => element.id !== key)])
        }
        if(mailData.length > 0){
            for (let i = 0; i < mailData.length; i++) { 
                if(mailData[i].id === key){
                    mailData[i].read = true;
                }
            }
        }
    }


    const showSystemDetail = (item) => {
        if(item.read === false){
            // onReadSingle(item.id)
            run_read_single({
                id: item.id,
            }).then(() => {
                getSystemQuantity()
                getSystemReadQuantity()
                if(systemReadData.length > 0){
                    // systemReadData = systemReadData.filter(element => element !== item)
                    setSystemReadData([...systemReadData.filter(element => element.id !== item.id)])
                }
                if(systemData.length > 0){
                    for (let i = 0; i < systemData.length; i++) { 
                        if(systemData[i].id === item.id){
                            systemData[i].read = true;
                        }
                    }
                }
            })
            // getMessageQuantity();
            onMailAuth();
        }
    }




    const handleMouseEnter = (index) => {
       setIsHover(true);
       setIsIndex(index);
    };
    const handleMouseLeave = (index) => {
       setIsHover(false);
       setIsIndex(index);
    };
 




    // const { runAsync: run_read } = useFetch(readOperation, { manual: true });
    const { runAsync: run_read_single } = useFetch(messageSingleRead, { manual: true });
    const { runAsync: run_read_all } = useFetch(messageAllRead, { manual: true });
    
    const onReadSingle = (key) => {
        run_read_single({
            id: key,
        }).then(() => {
            // getMessageQuantity();
            onMailAuth();
        })
    }

    const onReadAll = () => {
        run_read_all({
        }).then((resp) => {
            getMailQuantity()
            getMailReadQuantity()
            getSystemQuantity()
            getSystemReadQuantity()
            setIsRemind(true)
            // g.setQuantity(0)
            // getMessageQuantity();
            onMailAuth();
            message.success(resp.msg)
        })
    }

    const { runAsync: run_resend } = useFetch(sendAgain, { manual: true });
    const { runAsync: run_mailAuth } = useFetch(mailAuth, { manual: true });
    
    const onMailAuth = () => {
        run_mailAuth({ 
        }).then((resp) => {
            const result = resp.data;
            setMailAuthority(result);
            if(result === 0){
                getMessageSystemQuantity()
            }else if(result === 1){
                getMessageQuantity()
            }
        })
    }
    const onResend = (id,to) => {
        run_resend({ 
            id: id, 
            to: to,
        }).then((resp) => {
            setMailSkip(0)
            getMail(0)
            message.success(resp.msg)
            setIsModalOpen(false);

            form.setFieldValue();
            if(mailData.length > 0){
                let mailNumber = [...mailData.filter(element => element.id !== messageCenterId)];
                setMailData(mailNumber)
                setMailQuantity(mailNumber.length);
            }
            if(mailDataRead.length > 0){
                let mailReadNumber = [...mailDataRead.filter(element => element.id !== messageCenterId)];
                setMailDataRead(mailReadNumber)
                setMailReadQuantity(mailReadNumber.length);
            }
            setConfirmLoading(false);
            // getMessageQuantity();
            onMailAuth();
        })
    }
    const onSystemScroll = () => {
        const skip = systemSkip + 10
        setSystemSkip(skip)
        getSystem(skip);
    };

    const onSystemReadScroll = () => {
        const skip = systemReadSkip + 10
        setSystemReadSkip(skip)
        getSystemRead(skip,1);
    };

    
    const onMailScroll = () => {
        const skip = mailSkip + 10
        setMailSkip(skip)
        getMail(skip);
    };

    const onMailReadScroll = () => {
        const skip = mailReadSkip + 10
        setMailReadSkip(skip)
        getMailRead(skip,1);
    };
    useRafInterval(() => getNew(), 1000 * 60 * 5);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    useEffect(() => getNew(), [g.lang])
    return (
        <>
            <div style={{display:"flex", justifyContent: 'center', alignItems: 'center', margin: 0}}>
                <div  style={{width:1000}}>
        {
            mailAuthority === 1?
            // style={{width:1000,alignItems:"center",  align:"center"}}
                <Card  bordered={false} className="message-tab-top-ink-bar" >
                    <Tabs tabBarExtraContent={tabsKey === "1"?operations:""} defaultActiveKey="1" 
                        onTabClick={(key)=> changingOverTabs(key)}
                    >
                                <TabPane tab={
                                    <>
                                        <div style={{fontFamily:"PingFang SC",fontSize: 14, fontWeight:400,textAlign:"left" }}>
                                            <FormattedMessage id="page_notice.email_error" />
                                        </div>
                                    </>
                                } key="2">

                                        <Tabs defaultActiveKey="0"
                                            onTabClick={(key) =>{}}
                                            tabBarStyle={{backgroundColor:"#FAFAFA",borderBottom:"ubset"}}
                                            tabBarGutter={24}
                                            className="message-tab-ink-bar"
                                        >
                                            {
                                            <TabPane tab={
                                                <div style={{marginLeft:16 }}>  
                                                    <FormattedMessage id="common.all"/>{" (" + mailQuantity + ") "}
                                                    {/* <Badge dot={mailErrorDot} /> */}
                                                </div>
                                            } key="0" >
                                                <div>
                                                    <InfiniteScroll
                                                        dataLength={mailData.length}
                                                        // next={onMailScroll}
                                                        hasMore={mailHasMore}
                                                        loader={<></>}
                                                        endMessage={<></>}
                                                        height={"calc(100vh - 220px)"}
                                                    >
                                                        <List
                                                            itemLayout="vertical"
                                                            // bordered={true}
                                                            dataSource={mailData}
                                                            renderItem={(item,index) => (
                                                                <List.Item onMouseEnter={() => handleMouseEnter(index)} onMouseLeave={() => handleMouseLeave(index)} onClick={() => showDetail(item.content,item.id)}
                                                                style={{
                                                                    backgroundColor: isHover && isIndex === index ? '#FAFAFA' : '',
                                                                    cursor:" pointer",
                                                                }}>
                                                                    <Row>
                                                                        <Badge dot={!item.read} style={{ marginTop: 0 }} ></Badge>
                                                                        <Col>
                                                                            <svg t="1677824436834" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2981" className="iconfont mouse" style={{ marginTop: 0 }} width="24" height="24"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2982"></path><path d="M230.4 311.466667h563.2c21.333333 0 29.866667 8.533333 29.866667 29.866666v349.866667c0 21.333333-8.533333 29.866667-29.866667 29.866667H230.4c-21.333333 0-29.866667-8.533333-29.866667-29.866667V345.6c0-21.333333 8.533333-34.133333 29.866667-34.133333z" fill="#FFB84E" p-id="2983"></path><path d="M230.4 311.466667h563.2c17.066667 0 29.866667 12.8 29.866667 29.866666v55.466667c-174.933333 123.733333-281.6 183.466667-311.466667 183.466667-29.866667 0-136.533333-59.733333-311.466667-183.466667V345.6c0-17.066667 12.8-34.133333 29.866667-34.133333z" fill="#FFCD4E" p-id="2984"></path></svg>
                                                                        </Col>
                                                                        <Col style={{marginLeft: 6}}>
                                                                            <Text strong>{item.title}</Text>
                                                                        </Col>
                                                                    </Row>
                                                                    <Row>
                                                                        <Col style={{marginLeft: (!item.read)?35:30}}>
                                                                            <Paragraph style={{ whiteSpace: 'pre-wrap', maxHeight:100, fontFamily:"PingFang SC",fontSize: 14, color:"#677283",marginLeft: (item.html === 1)?-14:0}} ellipsis={true} >
                                                                                {ReactHtmlParser(item.content)}
                                                                            </Paragraph>
                                                                        </Col>
                                                                    </Row>
                                                                    <Row justify="space-between">
                                                                        <Col style={{marginLeft: (!item.read)?35:30}}>
                                                                            <Text type="danger">{<FormattedMessage
                                                                                id="page_notice.fail_reason" />}{': '}{item.reason}</Text>
                                                                        </Col>
                                                                        <Col>
                                                                            {
                                                                                <Button
                                                                                    onClick={(e) => {
                                                                                        e.stopPropagation();
                                                                                        showModal(item.id)}}
                                                                                    >
                                                                                    {<FormattedMessage id="page_notice.mail_resend" />}
                                                                                </Button> 
                                                                            }

                                                                        </Col>
                                                                    </Row>
                                                                    <Row justify="space-between">
                                                                        <Col  style={{marginLeft: (!item.read)?35:30}}>
                                                                            <Text type="secondary" style={{fontSize: 12, color:"#ADB2BA"}}>
                                                                                {(item.time === null || item.time === 0) ? '' : dayjs.unix(item.time).format('YYYY-MM-DD HH:mm:ss')}
                                                                            </Text>
                                                                        </Col>
                                                                    </Row>
                                                                </List.Item>
                                                            )}
                                                        />
                                                        {
                                                            mailHasMore?
                                                            <div style={{marginTop: -24}}>
                                                                <Divider/>
                                                                <div style={{ display:"flex",justifyContent:"center",alignItems:"center" , fontFamily:"PingFang SC",fontSize: 12, fontWeight:400,textAlign:"left", color:"#165DFF" }}><div  onClick={() => onMailScroll()} style={{cursor:" pointer"}}><FormattedMessage id="system.message.center.see.more" /><svg t="1679282390648" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" className="iconfont mouse" style={{ verticalAlign:"middle",}} width="20" height="20"><path d="M827.733333 273.066667c25.6-25.6 59.733333-25.6 76.8 0 25.6 25.6 25.6 59.733333 0 76.8L546.133333 716.8c-25.6 25.6-59.733333 25.6-76.8 0L102.4 349.866667c-25.6-25.6-25.6-59.733333 0-76.8 25.6-25.6 59.733333-25.6 76.8 0L503.466667 597.333333l324.266666-324.266666z" fill="#165DFF" p-id="2726"></path></svg></div></div>
                                                            </div>
                                                            :""
                                                        }
                                                    </InfiniteScroll>
                                                </div>
                                            </TabPane>
                                    }
                                    <TabPane tab={
                                        <>
                                            <div>
                                                <FormattedMessage id="page_notice.message_center_read" />{" (" + mailReadQuantity + ") "}
                                            </div>
                                        </>
                                    } key="1">
                                                <div>
                                                    <InfiniteScroll
                                                        dataLength={mailDataRead.length}
                                                        // next={onMailReadScroll}
                                                        hasMore={mailReadHasMore}
                                                        loader={<></>}
                                                        endMessage={<></>}
                                                        height={"calc(100vh - 220px)"}
                                                    >
                                                        <List
                                                            itemLayout="vertical"
                                                            // bordered={true}
                                                            dataSource={mailDataRead}
                                                            renderItem={(item,index) => (
                                                                <List.Item  onMouseEnter={() => handleMouseEnter(index)} onMouseLeave={() => handleMouseLeave(index)} onClick={() => showDetail(item.content,item.id)}
                                                                style={{
                                                                    backgroundColor: isHover && isIndex === index ? '#FAFAFA' : '',
                                                                    cursor:" pointer",
                                                                }}>
                                                                    <Row>
                                                                        <Badge dot={!item.read} style={{ marginTop: 0 }} ></Badge>
                                                                        <Col>
                                                                            <svg t="1677824436834" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2981" className="iconfont mouse" style={{ marginTop: 0 }} width="24" height="24"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2982"></path><path d="M230.4 311.466667h563.2c21.333333 0 29.866667 8.533333 29.866667 29.866666v349.866667c0 21.333333-8.533333 29.866667-29.866667 29.866667H230.4c-21.333333 0-29.866667-8.533333-29.866667-29.866667V345.6c0-21.333333 8.533333-34.133333 29.866667-34.133333z" fill="#FFB84E" p-id="2983"></path><path d="M230.4 311.466667h563.2c17.066667 0 29.866667 12.8 29.866667 29.866666v55.466667c-174.933333 123.733333-281.6 183.466667-311.466667 183.466667-29.866667 0-136.533333-59.733333-311.466667-183.466667V345.6c0-17.066667 12.8-34.133333 29.866667-34.133333z" fill="#FFCD4E" p-id="2984"></path></svg>
                                                                        </Col>
                                                                        <Col style={{marginLeft: 6}}>
                                                                            <Text strong>{item.title}</Text>
                                                                        </Col>
                                                                    </Row>
                                                                    <Row>
                                                                        <Col style={{marginLeft: (!item.read)?35:30}}>
                                                                            <Paragraph style={{ whiteSpace: 'pre-wrap', maxHeight:100, fontFamily:"PingFang SC",fontSize: 14, color:"#677283",marginLeft: (item.html === 1)?-14:0}} ellipsis={true} >
                                                                                {ReactHtmlParser(item.content)}
                                                                            </Paragraph>
                                                                        </Col>
                                                                    </Row>
                                                                    <Row justify="space-between">
                                                                        <Col  style={{marginLeft: (!item.read)?35:30}}>
                                                                            <Text type="danger">{<FormattedMessage
                                                                                id="page_notice.fail_reason" />}{': '}{item.reason}</Text>
                                                                        </Col>
                                                                        <Col>
                                                                            {
                                                                                <Button
                                                                                onClick={(e) => {
                                                                                    e.stopPropagation();
                                                                                    showModal(item.id)}}
                                                                                >
                                                                                    {<FormattedMessage id="page_notice.mail_resend" />}
                                                                                </Button> 
                                                                            }

                                                                        </Col>
                                                                    </Row>
                                                                    <Row justify="space-between">
                                                                        <Col  style={{marginLeft: (!item.read)?35:30}}>
                                                                            <Text type="secondary" style={{fontSize: 12, color:"#ADB2BA"}}>
                                                                                {(item.time === null || item.time === 0) ? '' : dayjs.unix(item.time).format('YYYY-MM-DD HH:mm:ss')}
                                                                            </Text>
                                                                        </Col>
                                                                    </Row>
                                                                </List.Item>
                                                            )}
                                                        />
                                                        {
                                                            mailReadHasMore?
                                                            <div style={{marginTop: -24}}>
                                                                <Divider/>
                                                                <div style={{ display:"flex",justifyContent:"center",alignItems:"center" , fontFamily:"PingFang SC",fontSize: 12, fontWeight:400,textAlign:"left", color:"#165DFF" }}><div onClick={() => onMailReadScroll()} style={{cursor:" pointer"}}><FormattedMessage id="system.message.center.see.more" /><svg t="1679282390648" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" className="iconfont mouse" style={{ verticalAlign:"middle",}} width="20" height="20"><path d="M827.733333 273.066667c25.6-25.6 59.733333-25.6 76.8 0 25.6 25.6 25.6 59.733333 0 76.8L546.133333 716.8c-25.6 25.6-59.733333 25.6-76.8 0L102.4 349.866667c-25.6-25.6-25.6-59.733333 0-76.8 25.6-25.6 59.733333-25.6 76.8 0L503.466667 597.333333l324.266666-324.266666z" fill="#165DFF" p-id="2726" ></path></svg></div></div>
                                                            </div>
                                                            :""
                                                        }
                                                    </InfiniteScroll>
                                                </div>
                                    </TabPane>
                                </Tabs>
                        </TabPane>

                        <TabPane tab={
                            <>
                                <div style={{fontFamily:"PingFang SC",fontSize: 14, fontWeight:400,textAlign:"left" }}>
                                    <FormattedMessage id="page_notice.system_update" />
                                </div>
                            </>
                        } key="1">


                            <Tabs defaultActiveKey="0"
                                onTabClick={(key) => {}}
                                tabBarStyle={{backgroundColor:"#FAFAFA",borderBottom:"ubset"}}
                                tabBarGutter={24}
                                className="message-tab-ink-bar"
                            >
                                {
                                    
                                        <TabPane tab={
                                            <div style={{marginLeft:16 }}>  
                                                <FormattedMessage id="common.all" />{" (" + systemQuantity + ") "}
                                            </div>
                                        } key="0">
                                            <div>
                                                <InfiniteScroll
                                                    dataLength={systemData.length}
                                                    // next={onSystemScroll}
                                                    hasMore={systemHasMore}
                                                    loader={<></>}
                                                    endMessage={<></>}
                                                    height={"calc(100vh - 220px)"}
                                                >
                                                    <List
                                                        itemLayout="vertical"
                                                        // bordered={true}
                                                        dataSource={systemData}
                                                        renderItem={(item,index) => (
                                                            <List.Item  onMouseEnter={() => handleMouseEnter(index)} onMouseLeave={() => handleMouseLeave(index)}  onClick={() => showSystemDetail(item)}
                                                            style={{
                                                                backgroundColor: isHover && isIndex === index ? '#FAFAFA' : '',
                                                                cursor:" pointer",
                                                            }}>
                                                                <Row>
                                                                    <Badge dot={!item.read && !isRemind} style={{ marginTop: 0 }} ></Badge>
                                                                    <Col>
                                                                        <svg t="1677823815085" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2843" className="iconfont mouse" style={{ marginTop: 0 }} width="24" height="24"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2844"></path><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2845"></path><path d="M512 836.266667c-46.933333 0-85.333333-38.4-85.333333-85.333334s38.4-85.333333 85.333333-85.333333 85.333333 38.4 85.333333 85.333333c0 51.2-38.4 85.333333-85.333333 85.333334z m0-550.4c-12.8 0-25.6-12.8-25.6-25.6V213.333333c0-12.8 12.8-25.6 25.6-25.6s25.6 12.8 25.6 25.6v51.2c0 12.8-8.533333 21.333333-25.6 21.333334z" fill="#47E7EB" p-id="2846"></path><path d="M823.466667 712.533333c0 21.333333-17.066667 42.666667-42.666667 42.666667H243.2c-25.6 0-42.666667-17.066667-42.666667-42.666667 0-21.333333 17.066667-42.666667 42.666667-42.666666h-21.333333c38.4 0 68.266667-29.866667 68.266666-68.266667v-123.733333c0-123.733333 102.4-226.133333 226.133334-226.133334s226.133333 102.4 226.133333 226.133334v123.733333c0 38.4 29.866667 68.266667 68.266667 68.266667h-25.6c21.333333 0 38.4 21.333333 38.4 42.666666z" fill="#2F77F1" p-id="2847"></path></svg>
                                                                    </Col>
                                                                    <Col style={{marginLeft: 6}}>
                                                                        {
                                                                            g.lang === "en"
                                                                                ?
                                                                                <Text strong ellipsis={true}>{item.titleEn}</Text>
                                                                                :
                                                                                <Text strong ellipsis={true}>{item.title}</Text>
                                                                        }
                                                                        
                                                                    </Col>
                                                                    {
                                                                        index===0?
                                                                        <Col style={{marginLeft:"8px",textAlign: "center",alignItems: "center",}}>
                                                                            <div 
                                                                                style={{
                                                                                    borderRadius: "2px",
                                                                                    // opacity: 0.1,
                                                                                    background: "#e8efff",
                                                                                    width: g.lang==="zh"?"48px":"80px",
                                                                                    height: "24px",
                                                                                    flexShrink: 0,
                                                                                    // letterSpacing: "0px",
                                                                                }}
                                                                            >
                                                                                <span
                                                                                    style={{
                                                                                        color: "#165DFF",
                                                                                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                                                        fontSize: "10px",
                                                                                        fontStyle: "normal",
                                                                                        fontWeight: 400,
                                                                                        lineHeight: "normal",
                                                                                    }}
                                                                                >
                                                                                    {formatMessage({id: "page_notice.system_update.current"})}
                                                                                </span>
                                                                            </div>
                                                                        </Col>:null
                                                                    }
                                                                </Row>
                                                                <Row>
                                                                    <Col style={{marginLeft: (!item.read && !isRemind)?35:30}}>
                                                                        {
                                                                            g.lang === "en"
                                                                                ?
                                                                                <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283"}} ellipsis={true}>
                                                                                    {item.contentEn}
                                                                                </Paragraph>
                                                                                :
                                                                                <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283"}} ellipsis={true}>
                                                                                    {item.content}
                                                                                </Paragraph>
                                                                        }
                                                                    </Col>
                                                                </Row>
                                                                <Row>
                                                                    <Col style={{marginLeft: (!item.read && !isRemind)?35:30}}>
                                                                        <Text type="secondary" style={{fontSize: 12, color:"#ADB2BA"}}>
                                                                            {(item.time === null || item.time === 0) ? '' : dayjs.unix(item.time).format('YYYY-MM-DD')}
                                                                        </Text>
                                                                    </Col>
                                                                </Row>
                                                            </List.Item>
                                                        )}
                                                    />
                                                    {
                                                        systemHasMore?
                                                        <div style={{marginTop: -24}}>
                                                            <Divider/>
                                                            <div style={{ display:"flex", justifyContent:"center", alignItems:"center" , fontFamily:"PingFang SC",fontSize: 12, fontWeight:400,textAlign:"left", color:"#165DFF" }}><div onClick={() => onSystemScroll()} style={{cursor:" pointer"}}><FormattedMessage id="system.message.center.see.more" /><svg t="1679282390648" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" className="iconfont mouse" style={{ verticalAlign:"middle",}} width="20" height="20"><path d="M827.733333 273.066667c25.6-25.6 59.733333-25.6 76.8 0 25.6 25.6 25.6 59.733333 0 76.8L546.133333 716.8c-25.6 25.6-59.733333 25.6-76.8 0L102.4 349.866667c-25.6-25.6-25.6-59.733333 0-76.8 25.6-25.6 59.733333-25.6 76.8 0L503.466667 597.333333l324.266666-324.266666z" fill="#165DFF" p-id="2726"></path></svg></div></div>
                                                        </div>
                                                        :""
                                                    }
                                                </InfiniteScroll>
                                            </div>
                                        </TabPane> 
                                }
                                <TabPane tab={
                                    <>
                                        <div>  
                                            <FormattedMessage id="page_notice.message_center_read" />{" (" + systemReadQuantity + ") "}
                                        </div>
                                    </>
                                } key="1">
                                            <div>
                                                <InfiniteScroll
                                                    dataLength={systemReadData.length}
                                                    // next={onSystemReadScroll}
                                                    hasMore={systemReadHasMore}
                                                    loader={<></>}
                                                    endMessage={<></>}
                                                    height={"calc(100vh - 220px)"}
                                                >
                                                    <List
                                                        itemLayout="vertical"
                                                        // bordered={true}
                                                        dataSource={systemReadData}
                                                        renderItem={(item,index) => (
                                                            <List.Item onMouseEnter={() => handleMouseEnter(index)} onMouseLeave={() => handleMouseLeave(index)} onClick={() => showSystemDetail(item)} 
                                                            style={{
                                                                backgroundColor: isHover && isIndex === index ? '#FAFAFA' : '',
                                                                cursor:" pointer",
                                                            }}>
                                                                <Row>
                                                                    <Badge dot={!item.read && !isRemind} style={{ marginTop: 0 }} ></Badge>
                                                                    <Col>
                                                                        <svg t="1677823815085" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2843" className="iconfont mouse" style={{ marginTop: 0 }} width="24" height="24"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2844"></path><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2845"></path><path d="M512 836.266667c-46.933333 0-85.333333-38.4-85.333333-85.333334s38.4-85.333333 85.333333-85.333333 85.333333 38.4 85.333333 85.333333c0 51.2-38.4 85.333333-85.333333 85.333334z m0-550.4c-12.8 0-25.6-12.8-25.6-25.6V213.333333c0-12.8 12.8-25.6 25.6-25.6s25.6 12.8 25.6 25.6v51.2c0 12.8-8.533333 21.333333-25.6 21.333334z" fill="#47E7EB" p-id="2846"></path><path d="M823.466667 712.533333c0 21.333333-17.066667 42.666667-42.666667 42.666667H243.2c-25.6 0-42.666667-17.066667-42.666667-42.666667 0-21.333333 17.066667-42.666667 42.666667-42.666666h-21.333333c38.4 0 68.266667-29.866667 68.266666-68.266667v-123.733333c0-123.733333 102.4-226.133333 226.133334-226.133334s226.133333 102.4 226.133333 226.133334v123.733333c0 38.4 29.866667 68.266667 68.266667 68.266667h-25.6c21.333333 0 38.4 21.333333 38.4 42.666666z" fill="#2F77F1" p-id="2847"></path></svg>
                                                                    </Col>
                                                                    <Col style={{marginLeft: 6}}>
                                                                        {
                                                                            g.lang === "en"
                                                                                ?
                                                                                <Text strong ellipsis={true}>{item.titleEn}</Text>
                                                                                :
                                                                                <Text strong ellipsis={true}>{item.title}</Text>
                                                                        }
                                                                    </Col>
                                                                    {
                                                                        index===0?
                                                                        <Col style={{marginLeft:"8px",textAlign: "center",alignItems: "center",}}>
                                                                            <div 
                                                                                style={{
                                                                                    borderRadius: "2px",
                                                                                    // opacity: 0.1,
                                                                                    background: "#e8efff",
                                                                                    width: g.lang==="zh"?"48px":"80px",
                                                                                    height: "24px",
                                                                                    flexShrink: 0,
                                                                                    // letterSpacing: "0px",
                                                                                }}
                                                                            >
                                                                                <span
                                                                                    style={{
                                                                                        color: "#165DFF",
                                                                                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                                                        fontSize: "10px",
                                                                                        fontStyle: "normal",
                                                                                        fontWeight: 400,
                                                                                        lineHeight: "normal",
                                                                                    }}
                                                                                >
                                                                                    {formatMessage({id: "page_notice.system_update.current"})}
                                                                                </span>
                                                                            </div>
                                                                        </Col>:null
                                                                    }
                                                                </Row>
                                                                <Row>
                                                                    <Col style={{marginLeft: (!item.read && !isRemind)?35:30}}>
                                                                        {
                                                                            g.lang === "en"
                                                                                ?
                                                                                <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283" }} ellipsis={true} >
                                                                                    {item.contentEn}
                                                                                </Paragraph>
                                                                                :
                                                                                <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283"}} ellipsis={true} >
                                                                                    {item.content}
                                                                                </Paragraph>
                                                                        }
                                                                    </Col>
                                                                </Row>
                                                                <Row>
                                                                    <Col style={{marginLeft: (!item.read && !isRemind)?35:30}}>
                                                                        <Text type="secondary" style={{fontSize: 12, color:"#ADB2BA"}}>
                                                                            {(item.time === null || item.time === 0) ? '' : dayjs.unix(item.time).format('YYYY-MM-DD')}
                                                                        </Text>
                                                                    </Col>
                                                                </Row>
                                                            </List.Item>
                                                        )}
                                                    />
                                                    {
                                                        systemReadHasMore?
                                                        <div style={{marginTop: -24}}>
                                                            <Divider/>
                                                            <div style={{ display:"flex",justifyContent:"center",alignItems:"center" , fontFamily:"PingFang SC",fontSize: 12, fontWeight:400,textAlign:"left", color:"#165DFF" }}><div onClick={() => onSystemReadScroll()} style={{cursor:" pointer"}}><FormattedMessage id="system.message.center.see.more" /><svg t="1679282390648" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" className="iconfont mouse" style={{ verticalAlign:"middle",}} width="20" height="20"><path d="M827.733333 273.066667c25.6-25.6 59.733333-25.6 76.8 0 25.6 25.6 25.6 59.733333 0 76.8L546.133333 716.8c-25.6 25.6-59.733333 25.6-76.8 0L102.4 349.866667c-25.6-25.6-25.6-59.733333 0-76.8 25.6-25.6 59.733333-25.6 76.8 0L503.466667 597.333333l324.266666-324.266666z" fill="#165DFF" p-id="2726"></path></svg></div></div>
                                                        </div>
                                                        :""
                                                    }
                                                </InfiniteScroll>
                                            </div>
                                </TabPane>
                            </Tabs>
                        </TabPane>
                    </Tabs>
                </Card>
            :
            <Card className="message-tab-top-ink-bar">
            <Tabs tabBarExtraContent={operations} defaultActiveKey="1"
                onTabClick={(key)=> changingOverTabs(key)}
            >

                <TabPane tab={
                    <>
                        <div style={{fontFamily:"PingFang SC",fontSize: 14, fontWeight:400,textAlign:"left" }}>
                            <FormattedMessage id="page_notice.system_update" />
                        </div>
                    </>
                } key="1">


                    <Tabs defaultActiveKey="0"
                        onTabClick={(key) => {}}
                        tabBarStyle={{backgroundColor:"#FAFAFA",borderBottom:"ubset"}}
                        tabBarGutter={24}
                        className="message-tab-ink-bar"
                    >
                        {
                            
                                <TabPane tab={
                                    <div style={{marginLeft:16 }}>  
                                        <FormattedMessage id="common.all" />{" (" + systemQuantity + ") "}
                                    </div>
                                } key="0">
                                    <div>
                                        <InfiniteScroll
                                            dataLength={systemData.length}
                                            // next={onSystemScroll}
                                            hasMore={systemHasMore}
                                            loader={<></>}
                                            endMessage={<></>}
                                            height={"calc(100vh - 220px)"}
                                        >
                                            <List
                                                itemLayout="vertical"
                                                // bordered={true}
                                                dataSource={systemData}
                                                renderItem={(item,index) => (
                                                    <List.Item  onMouseEnter={() => handleMouseEnter(index)} onMouseLeave={() => handleMouseLeave(index)}  onClick={() => showSystemDetail(item)}
                                                    style={{
                                                        backgroundColor: isHover && isIndex === index ? '#FAFAFA' : '',
                                                        cursor:" pointer",
                                                    }}>
                                                        <Row>
                                                            <Badge dot={!item.read && !isRemind} style={{marginTop: 0}} />
                                                            <Col>
                                                                {/* <svg  className="iconfont mouse" style={{ marginTop: 0 }} width={22} height={22}>
                                                                    <use xlinkHref="#icon-xiaoxi"></use>
                                                                </svg> */}
                                                                <svg t="1677823815085" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2843" className="iconfont mouse" style={{ marginTop: 0 }} width="24" height="24"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2844"></path><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2845"></path><path d="M512 836.266667c-46.933333 0-85.333333-38.4-85.333333-85.333334s38.4-85.333333 85.333333-85.333333 85.333333 38.4 85.333333 85.333333c0 51.2-38.4 85.333333-85.333333 85.333334z m0-550.4c-12.8 0-25.6-12.8-25.6-25.6V213.333333c0-12.8 12.8-25.6 25.6-25.6s25.6 12.8 25.6 25.6v51.2c0 12.8-8.533333 21.333333-25.6 21.333334z" fill="#47E7EB" p-id="2846"></path><path d="M823.466667 712.533333c0 21.333333-17.066667 42.666667-42.666667 42.666667H243.2c-25.6 0-42.666667-17.066667-42.666667-42.666667 0-21.333333 17.066667-42.666667 42.666667-42.666666h-21.333333c38.4 0 68.266667-29.866667 68.266666-68.266667v-123.733333c0-123.733333 102.4-226.133333 226.133334-226.133334s226.133333 102.4 226.133333 226.133334v123.733333c0 38.4 29.866667 68.266667 68.266667 68.266667h-25.6c21.333333 0 38.4 21.333333 38.4 42.666666z" fill="#2F77F1" p-id="2847"></path></svg>
                                                            </Col>
                                                            <Col style={{marginLeft: 6}}>
                                                                {
                                                                    g.lang === "en"
                                                                        ?
                                                                        <Text strong ellipsis={true}>{item.titleEn}</Text>
                                                                        :
                                                                        <Text strong ellipsis={true}>{item.title}</Text>
                                                                }
                                                                
                                                            </Col>
                                                            {
                                                                index===0?
                                                                <Col style={{marginLeft:"8px",textAlign: "center",alignItems: "center",}}>
                                                                    <div 
                                                                        style={{
                                                                            borderRadius: "2px",
                                                                            // opacity: 0.1,
                                                                            background: "#e8efff",
                                                                            width: g.lang==="zh"?"48px":"80px",
                                                                            height: "24px",
                                                                            flexShrink: 0,
                                                                            // letterSpacing: "0px",
                                                                        }}
                                                                    >
                                                                        <span
                                                                            style={{
                                                                                color: "#165DFF",
                                                                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                                                fontSize: "10px",
                                                                                fontStyle: "normal",
                                                                                fontWeight: 400,
                                                                                lineHeight: "normal",
                                                                            }}
                                                                        >
                                                                            {formatMessage({id: "page_notice.system_update.current"})}
                                                                        </span>
                                                                    </div>
                                                                </Col>:null
                                                            }
                                                        </Row>
                                                        <Row>
                                                            <Col style={{marginLeft: (!item.read && !isRemind)?35:30}}>
                                                                {
                                                                    g.lang === "en"
                                                                        ?
                                                                        <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283"}} ellipsis={true}>
                                                                            {item.contentEn}
                                                                        </Paragraph>
                                                                        :
                                                                        <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283"}} ellipsis={true}>
                                                                            {item.content}
                                                                        </Paragraph>
                                                                }
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col style={{marginLeft: (!item.read && !isRemind)?35:30}}>
                                                                <Text type="secondary" style={{fontSize: 12, color:"#ADB2BA"}}>
                                                                    {(item.time === null || item.time === 0) ? '' : dayjs.unix(item.time).format('YYYY-MM-DD')}
                                                                </Text>
                                                            </Col>
                                                        </Row>
                                                    </List.Item>
                                                )}
                                            />
                                            {
                                                systemHasMore?
                                                <div style={{marginTop: -24}}>
                                                    <Divider/>
                                                    <div style={{ display:"flex",justifyContent:"center",alignItems:"center" , fontFamily:"PingFang SC",fontSize: 12, fontWeight:400,textAlign:"left", color:"#165DFF" }}><div onClick={() => onSystemScroll()} style={{cursor:" pointer"}}><FormattedMessage id="system.message.center.see.more" /><svg t="1679282390648" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" className="iconfont mouse" style={{ verticalAlign:"middle",}} width="20" height="20"><path d="M827.733333 273.066667c25.6-25.6 59.733333-25.6 76.8 0 25.6 25.6 25.6 59.733333 0 76.8L546.133333 716.8c-25.6 25.6-59.733333 25.6-76.8 0L102.4 349.866667c-25.6-25.6-25.6-59.733333 0-76.8 25.6-25.6 59.733333-25.6 76.8 0L503.466667 597.333333l324.266666-324.266666z" fill="#165DFF" p-id="2726"></path></svg></div></div>
                                                </div>
                                                :""
                                            }
                                        </InfiniteScroll>
                                    </div>
                                </TabPane> 
                        }
                        <TabPane tab={
                            <>
                                <div>  
                                    <FormattedMessage id="page_notice.message_center_read" />{" (" + systemReadQuantity + ") "}
                               </div>
                            </>
                        } key="1">
                                    <div>
                                        <InfiniteScroll
                                            dataLength={systemReadData.length}
                                            // next={onSystemReadScroll}
                                            hasMore={systemReadHasMore}
                                            loader={<></>}
                                            endMessage={<></>}
                                            height={"calc(100vh - 220px)"}
                                        >
                                            <List
                                                itemLayout="vertical"
                                                // bordered={true}
                                                dataSource={systemReadData}
                                                renderItem={(item,index) => (
                                                    <List.Item onMouseEnter={() => handleMouseEnter(index)} onMouseLeave={() => handleMouseLeave(index)} onClick={() => showSystemDetail(item)} 
                                                    style={{
                                                        backgroundColor: isHover && isIndex === index ? '#FAFAFA' : '',
                                                        cursor:" pointer",
                                                    }}>
                                                        
                                                        <Row>
                                                            <Badge dot={!item.read && !isRemind} style={{ marginTop: 0 }} ></Badge>
                                                            <Col>
                                                                <svg t="1677823815085" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2843" className="iconfont mouse" style={{ marginTop: 0 }} width="24" height="24"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2844"></path><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F2F3F5" opacity=".8" p-id="2845"></path><path d="M512 836.266667c-46.933333 0-85.333333-38.4-85.333333-85.333334s38.4-85.333333 85.333333-85.333333 85.333333 38.4 85.333333 85.333333c0 51.2-38.4 85.333333-85.333333 85.333334z m0-550.4c-12.8 0-25.6-12.8-25.6-25.6V213.333333c0-12.8 12.8-25.6 25.6-25.6s25.6 12.8 25.6 25.6v51.2c0 12.8-8.533333 21.333333-25.6 21.333334z" fill="#47E7EB" p-id="2846"></path><path d="M823.466667 712.533333c0 21.333333-17.066667 42.666667-42.666667 42.666667H243.2c-25.6 0-42.666667-17.066667-42.666667-42.666667 0-21.333333 17.066667-42.666667 42.666667-42.666666h-21.333333c38.4 0 68.266667-29.866667 68.266666-68.266667v-123.733333c0-123.733333 102.4-226.133333 226.133334-226.133334s226.133333 102.4 226.133333 226.133334v123.733333c0 38.4 29.866667 68.266667 68.266667 68.266667h-25.6c21.333333 0 38.4 21.333333 38.4 42.666666z" fill="#2F77F1" p-id="2847"></path></svg>
                                                            </Col>
                                                            <Col style={{marginLeft: 6}}>
                                                                {
                                                                    g.lang === "en"
                                                                        ?
                                                                        <Text strong ellipsis={true}>{item.titleEn}</Text>
                                                                        :
                                                                        <Text strong ellipsis={true}>{item.title}</Text>
                                                                }
                                                            </Col>
                                                            {
                                                                index===0?
                                                                <Col style={{marginLeft:"8px",textAlign: "center",alignItems: "center",}}>
                                                                    <div 
                                                                        style={{
                                                                            borderRadius: "2px",
                                                                            // opacity: 0.1,
                                                                            background: "#e8efff",
                                                                            width: g.lang==="zh"?"48px":"80px",
                                                                            height: "24px",
                                                                            flexShrink: 0,
                                                                            // letterSpacing: "0px",
                                                                        }}
                                                                    >
                                                                        <span
                                                                            style={{
                                                                                color: "#165DFF",
                                                                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                                                fontSize: "10px",
                                                                                fontStyle: "normal",
                                                                                fontWeight: 400,
                                                                                lineHeight: "normal",
                                                                            }}
                                                                        >
                                                                            {formatMessage({id: "page_notice.system_update.current"})}
                                                                        </span>
                                                                    </div>
                                                                </Col>:null
                                                            }
                                                        </Row>
                                                        <Row>
                                                            <Col style={{marginLeft: 30}}>
                                                                {
                                                                    g.lang === "en"
                                                                        ?
                                                                        <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283" }} ellipsis={true} >
                                                                            {item.contentEn}
                                                                        </Paragraph>
                                                                        :
                                                                        <Paragraph style={{ whiteSpace: 'pre-wrap', fontFamily:"PingFang SC",fontSize: 14, color:"#677283"}} ellipsis={true} >
                                                                            {item.content}
                                                                        </Paragraph>
                                                                }
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col style={{marginLeft: 30}}>
                                                                <Text type="secondary" style={{fontSize: 12, color:"#ADB2BA"}}>
                                                                    {(item.time === null || item.time === 0) ? '' : dayjs.unix(item.time).format('YYYY-MM-DD')}
                                                                </Text>
                                                            </Col>
                                                        </Row>
                                                    </List.Item>
                                                )}
                                            />
                                            {
                                                systemReadHasMore?
                                                <div style={{marginTop: -24}}>
                                                    <Divider/>
                                                    <div style={{ display:"flex",justifyContent:"center",alignItems:"center" , fontFamily:"PingFang SC",fontSize: 12, fontWeight:400,textAlign:"left", color:"#165DFF" }}><div onClick={() => onSystemReadScroll()} style={{cursor:" pointer"}}><FormattedMessage id="system.message.center.see.more" /><svg t="1679282390648" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" className="iconfont mouse" style={{ verticalAlign:"middle",}} width="20" height="20"><path d="M827.733333 273.066667c25.6-25.6 59.733333-25.6 76.8 0 25.6 25.6 25.6 59.733333 0 76.8L546.133333 716.8c-25.6 25.6-59.733333 25.6-76.8 0L102.4 349.866667c-25.6-25.6-25.6-59.733333 0-76.8 25.6-25.6 59.733333-25.6 76.8 0L503.466667 597.333333l324.266666-324.266666z" fill="#165DFF" p-id="2726"></path></svg></div></div>
                                                </div>
                                                :""
                                            }
                                        </InfiniteScroll>
                                    </div>
                        </TabPane>
                    </Tabs>
                </TabPane>
            </Tabs>
        </Card>
        }



                    <Modal title={<FormattedMessage id="page_notice.mail_resend" />} open={isModalOpen} okText={formatMessage({id: 'common.ok'})} onOk={handleOk} confirmLoading={confirmLoading} onCancel={handleCancel} >
                        <Form form={form}>
                            <Form.Item label={formatMessage({id: 'page_notice.mail_resend_email'})} name="email"
                                rules={[{required: true}]}
                                style={{marginBottom: "16px"}}>
                                <Input placeholder={intl.formatMessage({id: "placeholder.input.common"})} className="full-width"
                                    allowClear/>
                            </Form.Item>
                        </Form>
                    </Modal>
                    </div>
            </div>
        </>
    )
}