export const mockProject = {
    "administrators": [
        "622eb5f0825a5d11f3e126d5",
        "610b49c1e3ea63e00e3bdb3c",
        "6090e4318d9d12afad9d4ebd"
    ],
    "create_time": [],
    "customerId": "6090e4318d9d12afad9d4ebc",
    "customerName": "cjc",
    "envs": [
        {
            "cohorts": [],
            "id": "64dc2d65de9209a4e22d3ceb",
            "lockconfig": false,
            "name": "DEV",
            "status": 2
        },
        {
            "alert_thresholds": null,
            "capacity": null,
            "cohorts": [],
            "id": "67480fd6c5f92dd4c9b3b98e",
            "is_copy": false,
            "lockConfig": false,
            "lockconfig": false,
            "name": "未编号",
            "reminder_thresholds": null,
            "status": 2
        },
        {
            "cohorts": [],
            "id": "650a8c7d48ad3b1129bcfcdc",
            "lockconfig": false,
            "name": "TEST",
            "status": 2
        },
        {
            "cohorts": [],
            "id": "656d31b3d8ee2971492d13fc",
            "name": "PROD",
            "status": 2
        },
        {
            "capacity": null,
            "cohorts": [],
            "id": "661f6a4b3da5c35c74c3a1bf",
            "lockconfig": false,
            "name": "仅发药",
            "reminder_thresholds": null,
            "status": 2
        },
        {
            "alert_thresholds": null,
            "capacity": null,
            "cohorts": [],
            "id": "66600546ea2e653f253ca35b",
            "lockConfig": false,
            "lockconfig": false,
            "name": "UAT",
            "reminder_thresholds": null,
            "status": 2
        },
        {
            "cohorts": [],
            "id": "66a1bc766e8350d278b296f3",
            "name": "PROD-copy",
            "status": 2
        },
        {
            "cohorts": [],
            "id": "66a1bfd25ac700c6fe75283d",
            "name": "PROD-dev",
            "status": 2
        },
        {
            "alert_thresholds": null,
            "capacity": null,
            "cohorts": [],
            "id": "681c60fa46a69fa668148772",
            "lockConfig": false,
            "name": "05081",
            "reminder_thresholds": null,
            "status": 1
        },
        {
            "alert_thresholds": null,
            "capacity": null,
            "cohorts": [],
            "id": "681c619046a69fa6681487dc",
            "lockConfig": false,
            "name": "0518",
            "reminder_thresholds": null,
            "status": 1
        },
        {
            "alert_thresholds": null,
            "capacity": null,
            "cohorts": [],
            "id": "681c61a646a69fa6681487e8",
            "lockConfig": false,
            "name": "DEV-copy",
            "reminder_thresholds": null,
            "status": 1
        }
    ],
    "id": "64dc2d36de9209a4e22d3cb9",
    "info": {
        "ali_project_id": "",
        "bound": 0,
        "connect_ali": 0,
        "connect_edc": 2,
        "connect_learning": 1,
        "de_isolation_approval": 1,
        "description": "",
        "edc_supplier": 1,
        "edc_url": "",
        "end_time": 0,
        "ip_unblinding_process": 1,
        "ip_unblinding_sms": 0,
        "ip_unblinding_type": 1,
        "name": "公式计算1",
        "need_learning": 2,
        "need_learning_env": [
            "PROD"
        ],
        "need_system_courses": 0,
        "number": "0816",
        "order_approval_control": 1,
        "order_approval_process": 1,
        "order_approval_sms": 0,
        "order_check": 1,
        "order_check_day": [
            1,
            2,
            3,
            4,
            5,
            6,
            7
        ],
        "order_check_time": "08:00",
        "order_confirmation": 1,
        "phone": "",
        "planned_cases": null,
        "push_mode": 1,
        "push_rules": 0,
        "push_scenario": {
            "cohort_random_block_push": false,
            "dispensing_push": false,
            "form_random_block_push": false,
            "random_block_push": false,
            "random_push": false,
            "register_push": false,
            "screen_push": false,
            "update_random_after_push": false,
            "update_random_front_push": false
        },
        "pv_unblinding_process": 1,
        "pv_unblinding_sms": 0,
        "pv_unblinding_type": 1,
        "research_attribute": 0,
        "room": true,
        "sponsor": "xxx",
        "start_time": 0,
        "status_reason": "",
        "synchronization_mode": 1,
        "system_courses": 1,
        "timeZone": 8,
        "timeZoneStr": "8",
        "type": 1,
        "unblinding_code": 0,
        "unblinding_control": 1,
        "unblinding_process": 1,
        "unblinding_sms": 0,
        "unblinding_type": 1,
        "visit_Randomization": {
            "randomizations": null,
            "visits": null
        }
    },
    "meta": {
        "created_at": 1692151094,
        "created_by": "000000000000000000000000",
        "updated_at": 0,
        "updated_by": "000000000000000000000000"
    },
    "sign": "0",
    "status": 0,
    "permissions": {
        "_id": "64dc2d36de9209a4e22d3cbf",
        "role_id": "64dc2d36de9209a4e22d3cbf",
        "role": "IP Officer",
        "customer_id": "000000000000000000000000",
        "project_id": "000000000000000000000000",
        "scope": "study",
        "template": 1,
        "status": 1,
        "permissions": [
            "operation.projects.home.view",
            "operation.subject.trail",
            "operation.subject.unblinding-pv",
            "operation.subject.medicine.trail",
            "operation.subject.medicine.room",
            "operation.subject.medicine.room-download",
            "operation.subject.unblinding",
            "operation.subject.unblinding-application",
            "operation.subject.unblinding-approval",
            "operation.supply.storehouse.medicine.summary",
            "operation.supply.storehouse.medicine.singe",
            "operation.supply.storehouse.medicine.use",
            "operation.supply.storehouse.medicine.freeze",
            "operation.supply.site.medicine.singe",
            "operation.supply.site.medicine.use",
            "operation.supply.site.medicine.freeze",
            "operation.supply.site.no_number.view",
            "operation.supply.site.no_number.freeze",
            "operation.build.storehouse.add",
            "operation.build.storehouse.delete",
            "operation.build.storehouse.edit",
            "operation.build.storehouse.notice",
            "operation.build.storehouse.view",
            "operation.build.storehouse.alarm",
            "operation.build.site.view",
            "operation.build.site.add",
            "operation.build.site.dispensing",
            "operation.build.attribute.view",
            "operation.build.attribute.edit",
            "operation.build.attribute.history",
            "operation.build.code-rule.view",
            "operation.build.code-rule.edit",
            "operation.build.simulate-random.view",
            "operation.build.simulate-random.edit",
            "operation.build.simulate-random.add",
            "operation.build.simulate-random.run",
            "operation.build.simulate-random.site",
            "operation.build.simulate-random.factor",
            "operation.build.randomization.type.view",
            "operation.build.randomization.type.edit",
            "operation.build.randomization.list.view-summary",
            "operation.build.randomization.list.upload",
            "operation.build.randomization.list.generate",
            "operation.build.randomization.list.active",
            "operation.build.randomization.list.export",
            "operation.build.randomization.list.invalid",
            "operation.build.randomization.list.attribute",
            "operation.build.randomization.factor-in.view",
            "operation.build.randomization.factor-in.set-people",
            "operation.build.randomization.form.add",
            "operation.build.randomization.form.delete",
            "operation.build.randomization.form.list",
            "operation.build.medicine.configuration.add",
            "operation.build.medicine.configuration.delete",
            "operation.build.medicine.configuration.edit",
            "operation.build.medicine.configuration.list",
            "operation.build.medicine.packlist.upload",
            "operation.build.medicine.otherm.add",
            "operation.build.medicine.otherm.delete",
            "operation.build.medicine.otherm.edit",
            "operation.build.medicine.otherm.list",
            "operation.build.supply-plan.medicine.add",
            "operation.build.supply-plan.medicine.delete",
            "operation.build.supply-plan.medicine.edit",
            "operation.build.supply-plan.medicine.view",
            "operation.build.supply-plan.medicine.history",
            "operation.build.supply-plan.add",
            "operation.build.supply-plan.delete",
            "operation.build.supply-plan.edit",
            "operation.build.supply-plan.view",
            "operation.build.supply-plan.history",
            "operation.build.history.view",
            "operation.build.history.print",
            "operation.build.settings.notice.view",
            "operation.build.settings.notice.edit",
            "operation.build.settings.user.download",
            "operation.build.randomization.info.view",
            "operation.build.randomization.info.export",
            "operation.build.site.supply-plan.edit",
            "operation.build.site.supply-plan.view",
            "operation.build.site.edit",
            "operation.supply.shipment.approval.view",
            "operation.supply.shipment.approval.print",
            "operation.supply.shipment.logistics.view",
            "operation.supply.shipment.logistics.edit",
            "operation.monitor.view",
            "operation.monitor.edit",
            "operation.supply.freeze.list",
            "operation.supply.freeze.release",
            "operation.supply.freeze.delete",
            "operation.supply.freeze.approval",
            "operation.supply.recovery.list",
            "operation.supply.recovery.add",
            "operation.supply.recovery.receive",
            "operation.supply.recovery.confirm",
            "operation.supply.recovery.cancel",
            "operation.supply.recovery.lose",
            "operation.supply.recovery.reason",
            "operation.supply.recovery.determine",
            "operation.supply.recovery.close",
            "operation.supply.recovery.end",
            "operation.subject.medicine.trail.print",
            "operation.subject.print",
            "operation.build.randomization.form.edit",
            "operation.build.randomization.form.preview",
            "operation.supply.storehouse.medicine.print",
            "operation.supply.site.medicine.print",
            "operation.supply.shipment.print",
            "operation.supply.recovery.print",
            "operation.supply.freeze.print",
            "operation.build.randomization.list.print",
            "operation.build.medicine.upload.print",
            "operation.supply.storehouse.medicine.history",
            "operation.supply.site.medicine.history",
            "operation.supply.site.no_number.history",
            "operation.supply.site.no_number.print",
            "operation.supply.shipment.history",
            "operation.supply.recovery.history",
            "operation.supply.freeze.history",
            "operation.build.randomization.list.history",
            "operation.build.medicine.upload.uploadHistory",
            "operation.build.push.view",
            "operation.build.push.all.send",
            "operation.build.push.batch.send",
            "operation.build.push.send",
            "operation.build.push.details",
            "operation.build.randomization.group.add",
            "operation.build.randomization.group.delete",
            "operation.build.randomization.group.edit",
            "operation.build.randomization.group.view",
            "operation.build.randomization.edc.mapping",
            "operation.subject.download-random",
            "operation.subject.download-random.template",
            "operation.subject.medicine.export",
            "operation.subject.medicine.export.template",
            "operation.build.simulate-random.download",
            "operation.build.simulate-random.pdf.download",
            "operation.subject.download",
            "operation.subject.download.template",
            "operation.supply.shipment.download",
            "operation.supply.shipment.download.template",
            "operation.supply.recovery.download",
            "operation.supply.recovery.download.template",
            "operation.supply.storehouse.medicine.download",
            "operation.supply.storehouse.medicine.download.template",
            "operation.build.medicine.upload.downdata",
            "operation.build.medicine.upload.downdata.template",
            "operation.report.auditTrailExport.download",
            "operation.report.auditTrailExport.build",
            "operation.report.auditTrailExport.settings",
            "operation.report.auditTrailExport.release-record",
            "operation.report.auditTrailExport.order",
            "operation.report.auditTrailExport.drug_recovery",
            "operation.report.auditTrailExport.subject",
            "operation.report.auditTrailExport.dispensing",
            "operation.report.auditTrailExport.ip",
            "operation.report.userLoginHistory.download",
            "operation.report.userRoleAssignHistory.download",
            "operation.report.randomizationStatisticsExport.download",
            "operation.report.subjectStatisticsExport.download",
            "operation.project.status.view",
            "operation.project.task.view",
            "operation.project.random.view",
            "operation.project.random.download",
            "operation.project.subject.view",
            "operation.project.subject.download",
            "operation.project.depot.IPStatistics.view",
            "operation.project.depot.IPStatistics.download",
            "operation.project.site.IPStatistics.view",
            "operation.project.site.IPStatistics.download",
            "operation.project.analysis.view",
            "operation.project.dynamics.view",
            "operation.supply.storehouse.no_number.history",
            "operation.supply.storehouse.no_number.print",
            "operation.build.randomization.factor.add",
            "operation.build.randomization.factor.view",
            "operation.build.randomization.factor.delete",
            "operation.build.randomization.factor.edit",
            "operation.build.randomization.factor.set-toplimit",
            "operation.build.randomization.list.sync",
            "operation.build.randomization.list.segmentation.view",
            "operation.build.randomization.list.segmentation.clear",
            "operation.build.randomization.list.segmentation.site",
            "operation.build.randomization.list.segmentation.factor",
            "operation.build.randomization.list.segmentation.region",
            "operation.build.randomization.list.segmentation.country",
            "operation.build.medicine.upload.list",
            "operation.build.medicine.upload.upload",
            "operation.build.medicine.upload.delete",
            "operation.build.medicine.package.setting",
            "operation.build.settings.user.view",
            "operation.build.settings.user.edit",
            "operation.build.settings.user.unbind",
            "operation.build.settings.user.history",
            "operation.build.settings.user.print",
            "operation.build.settings.users.invite-again",
            "operation.supply.drug_recovery.logistics.view",
            "operation.supply.drug_recovery.logistics.edit",
            "operation.build.medicine.visit.update",
            "operation.build.medicine.visit.drag",
            "operation.build.medicine.visit.copy",
            "operation.build.medicine.visit.push",
            "operation.build.medicine.visit.add",
            "operation.build.medicine.visit.delete",
            "operation.build.medicine.visit.edit",
            "operation.build.medicine.visit.list",
            "operation.build.medicine.visit.push.record",
            "operation.supply.site.medicine.summary",
            "operation.supply.site.medicine.summary.formula",
            "operation.projects.project.info.view",
            "operation.projects.project.basic.information.view",
            "operation.projects.project.business.functions.view",
            "operation.projects.project.external.docking.view",
            "operation.project.subject.visit.cycle.view",
            "operation.project.subject.visit.cycle.notice.view",
            "operation.project.subject.visit.cycle.send.notice",
            "operation.build.randomization.factor-in.delete",
            "operation.build.randomization.factor-in.add",
            "operation.build.settings.user.add",
            "operation.build.settings.user.role",
            "operation.build.settings.user.site",
            "operation.build.settings.user.depot",
            "operation.build.settings.user.app",
            "operation.build.settings.user.reauthorization",
            "operation.build.randomization.list.edit",
            "operation.build.medicine.examine",
            "operation.build.medicine.update",
            "operation.build.medicine.release",
            "operation.subject.view-list",
            "operation.subject.random",
            "operation.subject.replace",
            "operation.subject.registered",
            "operation.subject.update",
            "operation.subject.delete",
            "operation.subject.secede",
            "operation.subject.secede-registered",
            "operation.subject.cohort.status",
            "operation.subject.screen",
            "operation.subject.finish",
            "operation.subject.medicine.transport",
            "operation.build.medicine.barcode.view",
            "operation.build.medicine.barcode.add",
            "operation.build.medicine.barcode.scan",
            "operation.build.medicine.barcode.scanPackage",
            "operation.build.medicine.barcode.export",
            "operation.supply.shipment.detail.change",
            "operation.supply.shipment.detail.changeRecord",
            "operation.supply.shipment.contacts",
            "operation.supply.shipment.reason",
            "operation.report.forecastingPrediction.download",
            "operation.report.visitForecast.download",
            "operation.report.visitForecast.download.template",
            "operation.build.medicine.visit.setting.edit",
            "operation.build.medicine.visit.setting.list",
            "operation.build.medicine.configuration.setting.add",
            "operation.build.medicine.configuration.setting.delete",
            "operation.build.medicine.configuration.setting.edit",
            "operation.build.medicine.configuration.setting.list",
            "operation.supply.shipment.close-dtp",
            "operation.supply.shipment.terminated-dtp",
            "operation.supply.shipment.confirm-dtp",
            "operation.supply.shipment.create",
            "operation.supply.shipment.send",
            "operation.supply.shipment.lose",
            "operation.supply.shipment.list",
            "operation.supply.shipment.receive",
            "operation.supply.shipment.alarm",
            "operation.supply.shipment.confirm",
            "operation.supply.shipment.approval",
            "operation.supply.shipment.close",
            "operation.supply.shipment.terminated",
            "operation.supply.shipment.cancel",
            "operation.supply.shipment.cancel-dtp",
            "operation.build.push.history",
            "operation.build.medicine.batch.update",
            "operation.build.medicine.batch.list",
            "operation.build.medicine.batch.edit",
            "operation.build.medicine.batch.setting",
            "operation.subject.invalid-list",
            "operation.source.ip.upload.history.downdata",
            "operation.projects.notice.permissions.view",
            "operation.subject.medicine.view-dispensing",
            "operation.subject.medicine.dispensing",
            "operation.subject.medicine.reissue",
            "operation.subject.medicine.replace",
            "operation.subject.medicine.resume",
            "operation.subject.medicine.retrieval",
            "operation.subject.medicine.out-visit-dispensing",
            "operation.subject.medicine.invalid",
            "operation.subject.medicine.register",
            "operation.subject.medicine.joinTime",
            "operation.subject.medicine.setUp",
            "operation.build.projectNotificationsConfigurationReport.download",
            "operation.report.siteIPStatisticsExport.download",
            "operation.report.siteIPStatisticsExport.download.template",
            "operation.report.depotIPStatisticsExport.download",
            "operation.report.depotIPStatisticsExport.download.template",
            "operation.supply.site.medicine.download",
            "operation.supply.site.medicine.download.template",
            "operation.subject.unblinding-pv-view",
            "operation.subject.unblinding-pv-application",
            "operation.subject.unblinding-pv-approval",
            "operation.subject.unblinding-log",
            "operation.subject.unblinding-sms",
            "operation.subject.unblinding-print",
            "operation.subject.unblinding-pv-log",
            "operation.subject.unblinding-pv-sms",
            "operation.subject.unblinding-pv-print"
        ],
        "page": [
            "/project/build/drug",
            "/project/settings/export",
            "/project/home",
            "/project/sub/subject",
            "/project/supply/storehouse",
            "/project/build/attribute",
            "/project/build/simulate-random",
            "/project/build/randomization",
            "/check",
            "/project/supply/drug-freeze",
            "/project/build/push-statistics",
            "/project/build/storehouse",
            "/project/settings/user",
            "/project/sub/visit-cycle",
            "/project/build/site",
            "/project/build/supply-plan",
            "/project/build/history",
            "/project/settings/notice",
            "/project/supply/shipment",
            "",
            "/project/supply/site",
            "/project",
            "/project/supply/drug-recovery"
        ],
        "cohortDisablePermission": [
            {
                "permission": "operation.subject.unblinding",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.unblinding-application",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.unblinding-approval",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.unblinding-sms",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.unblinding-pv-view",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.unblinding-pv-application",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.unblinding-pv-approval",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.unblinding-pv-sms",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.dispensing",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.reissue",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.replace",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.resume",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.retrieval",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.out-visit-dispensing",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.invalid",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.register",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.formula.update",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.joinTime",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.setUp",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.random",
                "cohortDisableStatus": [
                    1,
                    3,
                    4,
                    5
                ]
            },
            {
                "permission": "operation.subject.replace",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.registered",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.switch.cohort",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.update",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.delete",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.secede",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.secede-registered",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.finish",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject.medicine.transport",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.unblinding-application",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.unblinding-approval",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.unblinding-sms",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.random",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.replace",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.unblinding-pv",
                "cohortDisableStatus": [
                    1,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.medicine.dispensing",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.medicine.transport",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.medicine.reissue",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.medicine.replace",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.registered",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.update",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.delete",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.medicine.out-visit-dispensing",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.medicine.invalid",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.medicine.register",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.secede",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.finish",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.subject-dtp.secede-registered",
                "cohortDisableStatus": [
                    1,
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.medicine.use",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.medicine.freeze",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.medicine.lost",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.medicine.use",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.medicine.freeze",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.medicine.lost",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.no_number.freeze",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.storehouse.no_number.lost",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.logistics.edit",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.order.send",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.order.receive",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.order.cancel",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.order.end",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.order.reason",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.order.confirm",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.order.close",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.single.delete",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug.single.use",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.site.medicine.use",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.site.medicine.freeze",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.site.medicine.lost",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.site.no_number.freeze",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.site.no_number.lost",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.logistics.edit",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.create",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.cancel",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.cancel-dtp",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.send",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.lose",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.receive",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.alarm",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.confirm",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.confirm-dtp",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.approval",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.close",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.close-dtp",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.terminated",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.shipment.terminated-dtp",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.drug_recovery.logistics.edit",
                "cohortDisableStatus": [
                    3,
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.receive",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.confirm",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.cancel",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.lose",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.determine",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.close",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.recovery.end",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.freeze.release",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.freeze.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.supply.freeze.approval",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.storehouse.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.storehouse.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.storehouse.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.storehouse.notice",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.site.supply-plan.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.site.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.site.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.site.dispensing",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.attribute.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.simulate-random.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.simulate-random.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.simulate-random.run",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.type.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.group.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.group.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.group.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.group.inactivating",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.factor.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.factor.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.factor.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.factor.set-toplimit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.sync",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.upload",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.generate",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.active",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.invalid",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.upload",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.generate",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.active",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.invalid",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.segmentation.activate",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.segmentation.deactivate",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.segmentation.clear",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.segmentation.site",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.segmentation.region",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.segmentation.country",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.list.segmentation.factor",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.factor-in.set-people",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.factor-in.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.factor-in.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.form.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.form.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.randomization.form.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.setting.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.setting.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.update",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.drag",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.copy",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.push",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.visit.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.configuration.setting.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.configuration.setting.list",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.configuration.setting.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.configuration.setting.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.configuration.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.configuration.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.configuration.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.package.setting",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.examine",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.update",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.release",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.upload.upload",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.upload.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.upload.upload",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.upload.downdata",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.package.setting",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.examine",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.update",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.release",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.upload.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.otherm.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.otherm.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.otherm.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.batch.update",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.batch.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.batch.setting",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.barcode.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.barcode.scan",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.barcode.scanPackage",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.medicine.barcode.export",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.supply-plan.medicine.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.supply-plan.medicine.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.supply-plan.medicine.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.supply-plan.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.supply-plan.delete",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.supply-plan.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.push.batch.send",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.push.send",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.push.batch.send",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.push.send",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.notice.edit",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.role",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.users.site",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.depot",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.app",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.unbind",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.users.invite-again",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.reauthorization",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.add",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.role",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.users.site",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.depot",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.app",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.unbind",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.users.invite-again",
                "cohortDisableStatus": [
                    4
                ]
            },
            {
                "permission": "operation.build.settings.user.reauthorization",
                "cohortDisableStatus": [
                    4
                ]
            }
        ]
    }
}