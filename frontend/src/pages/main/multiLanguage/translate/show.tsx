import React, {useRef} from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {
    Form,
    Input,
    message,
    Modal,
    Select,
    Col,
    Row,
    Table,
    Button,
    Tooltip,
    Cascader,
    CascaderProps,
    Dropdown,
    Menu,
    Spin,
    ConfigProvider,
} from "antd";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import {useMultiLanguage} from "../context";
import {DownOutlined, } from "@ant-design/icons";
import { useFetch } from "../../../../hooks/request";
import {getTranslateMap, updateTranslate} from "../../../../api/multi_language";
import { PaginationView } from "../pagination";
import {getLocalesData, languageCountryCode} from "../../../common/multilingual/util";
import { useCacheTable } from "./cache-table";
import Search from "antd/lib/input/Search";
import ExcelJS from "exceljs";
import {saveAs} from "file-saver";
import {getUserInfoFromCloud} from "../../../../api/user";
import {LocalesPreview} from "../preview";
import {useAtom} from "jotai";
import {customLanguageAtom, customLocalesAtom} from "../../../common/multilingual/context";
import {TranslateUpload} from "./upload";
import {LanguageLibraryOption, TranslationStatusOption} from "./data";
import {allowPreview} from "../preview/modules"

export const Show = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal();
    const ctx = useMultiLanguage();

    const [visible, setVisible] = useSafeState<any>(false);
    const [customerId, setCustomerId] = useSafeState<any>(null);
    const [projectId, setProjectId] = useSafeState<any>(null);
    const [projectType, setProjectType] = useSafeState<any>(null);
    const [projectNumber, setProjectNumber] = useSafeState<any>(null);
    const [id, setId] = useSafeState<any>(null);
    const [code, setCode] = useSafeState<any>(null);
    const [language, setLanguage] = useSafeState<any>(null);
    const [permissionList, setPermissionList] = useSafeState<any[]>([]);
    const [tableList, setTableList] = useSafeState<any[]>([])
    const [showData, setShowData] = useSafeState<any[]>([])

    // const tableData = useCacheTable({ dataSource: tableList });
    const [pagePathOptionList, setPagePathOptionList] = useSafeState<any[]>([]);
    const [typeOptionList, setTypeOptionList] = useSafeState<any[]>([]);
    const [flatPath, setFlatPath] = useSafeState<any[]>([])
    const [flatType, setFlatType] = useSafeState<any[]>([])

    const [envOptionList, setEnvOptionList] = useSafeState<any[]>([]);
    const [envList, setEnvList] = useSafeState<any[]>([]);
    const [cohortOptionList, setCohortOptionList] = useSafeState<any[]>([]);

    const [edit, setEdit] = useSafeState<any>(new Map());
    const [color, setColor] = useSafeState<any>(new Map());

    const [compareInputValue, setCompareInputValue] = useSafeState<any>(null);

    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<any[]>([]);

    const [form] = Form.useForm()
    const [pagePath, setPagePath] = useSafeState<string[]>([])


    const { runAsync: updateTranslateLoadingRun, loading: updateTranslateLoading } = useFetch(updateTranslate,{ manual: true });
    const { runAsync: getUserInfoFromCloudRun, loading: getUserInfoFromCloudLoading } = useFetch(getUserInfoFromCloud,{ manual: true });
    const { runAsync: getTranslate, loading: getTranslateListLoading } = useFetch(getTranslateMap, {manual: true})

    const previewRef = useRef<any>()
    const uploadRef = useRef<any>()
    const [translateMessages, setTranslateMessages] = useAtom(customLocalesAtom)
    const [_, setLanguageInfo] = useAtom(customLanguageAtom)

    const show = (project: any, initOptions: any, languageNameList: any, record: any, permissionList: any) => {
        setLanguageInfo({id: record.id, customerId: project?.customerId, projectId: project?.id, language: record.language, lang: g.lang})
        setVisible(true);
        setCustomerId(project?.customerId);
        setProjectId(project?.id);
        setProjectNumber(project?.info.number);
        setId(record.id);
        setLanguage(record.language);
        setCode(record.code);
        setPermissionList(permissionList);

        // 环境
        const envList: any [] =  project?.envs?.map((env: any) => ({
            label: env.name,
            value: env.id,
        })) || []
        setEnvOptionList(envList);
        setProjectType(project?.info.type);
        setEnvList(project?.envs);

        // 设置默认值
        initSystemLibrary()

        // 获得全部翻译
        const params = { customerId:  project?.customerId, projectId: project?.id }
        getTranslate(params, {languageId: record.id}).then((resp: any) => {
            setTranslateMessages(resp.data)
        })
    }

    const initSystemLibrary = () => {
        form.setFieldValue('languageLibrary', 1)
        const {data, modules, types, flatModules, flatTypes} = getLocalesData(g.lang)
        setTableList(data)
        setShowData(data)
        setPagePathOptionList(modules)
        setTypeOptionList(types)
        setFlatPath(flatModules)
        setFlatType(flatTypes)
        ctx.setTotal(data.length)
    }

    const onLanguageLibraryChange = (value: any) => {
        setPagePath([])
        if(value === 1){
            initSystemLibrary()
            form.setFieldValue("env", null);
            form.setFieldValue("cohort", null);
        } else if(value === 2){
            form.setFieldValue("env", envList[0].id);
            const cohortList: any [] = envList.find((env: any) => env.id === envList[0].id)?.cohorts?.map((cohort: any) => (
                {
                    label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                    value: cohort.id,
                }
            ))
            setCohortOptionList(cohortList);
            form.setFieldValue("cohort", cohortList[0].value);
            setPagePathOptionList([])
            setTypeOptionList([])
        }  else if(value === 3){
            form.setFieldValue("env", null);
            form.setFieldValue("cohort", null);
            setPagePathOptionList([])
            setTypeOptionList([])
        }
    };

    const hide = () => {
        setVisible(false);
        ctx.setTotal(0);
        setCustomerId(null);
        setProjectId(null);
        setProjectNumber(null);
        setId(null);
        setCode(null);
        setLanguage(null);
        setPermissionList([]);
        setTableList([]);
        setShowData([])
        setEnvOptionList([]);
        setPagePathOptionList([]);
        setSelectedRowKeys([]);
        setProjectType(null);
        setEnvList([]);
        setCohortOptionList([]);
        setTypeOptionList([]);
        setEdit(new Map());
        setColor(new Map());
        form.resetFields();
        props.refresh();
    };

    const refresh = () => {
        setSelectedRowKeys([]);
    }

    //查询
    const searchClick = ()=> {
        form.validateFields().then(values => {
            const {name, translationStatus, type} = values
            const data = tableList
                .filter(it => !name || it.label?.includes(name))
                .filter(it => !pagePath || pagePath.length === 0 || it.path.slice(0, pagePath.length).toString() === pagePath.toString())
                .filter(it => ((translationStatus ?? '') === '') || (!!translateMessages[it.key] === translationStatus))
                .filter(it => !type || it.type === type)
            setShowData(data)
        })
    }

    const afterUpload = () => {
        // 获得全部翻译
        const params = { customerId:  customerId, projectId: projectId }
        getTranslate(params, {languageId: id}).then((resp: any) => {
            setTranslateMessages(resp.data)
        }).then(searchClick)
    }


    //单个翻译编辑
    const singleSaveClick = (record: any)=>{
        const languageLibrary = LanguageLibraryOption.find(it => it.value === form.getFieldValue("languageLibrary")) || LanguageLibraryOption[0]
        const path = flatPath.filter(p => record.path.includes(p.value))
        const type = flatType.find(it => it.value === record.type)
        updateTranslateLoadingRun({ customerId:  customerId, projectId: projectId },
            {
                id: record.id,
                customerId: customerId,
                projectId: projectId,
                envId: form.getFieldValue("env"),
                cohortId: form.getFieldValue("cohort"),
                languageId: id,
                languageLibrary: form.getFieldValue("languageLibrary"),
                pagePath: path,
                type: record.type,
                key: record.key,
                name: compareInputValue,
                languageValue:language,
                languageLibraryValueZh: languageLibrary.labelCn,
                languageLibraryValueEn: languageLibrary.labelEn,
                pagePathValueZh: path.map(it => it.label).join(' / '),
                pagePathValueEn: path.map(it => it.labelEn).join(' / '),
                typeValueZh: type.label,
                typeValueEn: type.labelEn,
                nameValueZh: record.defaultLabel.cn,
                nameValueEn: record.defaultLabel.en,
            }
        ).then(
            (resp: any) => {
                setTranslateMessages({...translateMessages, [record.key]: compareInputValue})
                message.success(formatMessage({ id: 'message.save.success' }));
                setEdit(() => {
                    const newEdit = new Map();
                    newEdit.set(record.key, false);
                    return newEdit
                })
                setColor(() => {
                    const newColor = new Map();
                    newColor.set(record.key, 0);
                    return newColor;
                })
                setCompareInputValue(null)
            }
        );

    }

    //重置
    const resetClick = ()=>{
        ctx.setTotal(0);
        setTableList([])
        setShowData([])
        setPagePathOptionList([]);
        setSelectedRowKeys([]);
        setCohortOptionList([]);
        setTypeOptionList([]);
        setEdit(new Map());
        setColor(new Map());
        setPagePath([])
        form.resetFields();
    }

    React.useImperativeHandle(props.bind, () => ({ show }));


    const onPagePathChange = (path: any[]) => {
        setPagePath(path || [])
    };

    const onNameSearch = (value: any) => {
        searchClick();
    };

    const onEnvChange = (value: string) => {
        if(projectType === 1){
            setCohortOptionList([]);
        } else {
            let cohortList: any [] = [];
            envList.find((env: any) => env.id === value)?.cohorts?.forEach((cohort: any) => {
                cohortList.push({
                    label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                    value: cohort.id,
                });
            });
            setCohortOptionList(cohortList);
            form.setFieldValue("cohort", cohortList[0].value);
        }
    }

    const pagePathShowSearchConfig: CascaderProps<any>['showSearch'] = {
        filter: (inputValue: string, path: any[]) => {
            // 这里是你希望实现的过滤逻辑
            return path.some(
                option => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1,
            );
        }
    };

    const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys);
    };

    const rowSelection: any = {
        selectedRowKeys,
        type: "checkbox",
        onChange: onSelectChange,
        preserveSelectedRowKeys: true,
    };

    function renderLanguage(language: any, value: any, record: any) {
        if (['zh', 'en'].includes(code)) return record.defaultLabel[languageCountryCode[code].languageCode]
        return <>
            <Col>
                {
                    (((edit && edit.has(record.key)) ? edit.get(record.key) : false) === false)?value:
                    <Input.Group compact>
                        <Input
                            placeholder={formatMessage({id: "placeholder.input.common",})}
                            value={compareInputValue}
                            onChange={(e) => {
                                setCompareInputValue(e.target.value);
                            }}
                            suffix={
                                <Button
                                    size="small"
                                    type="primary"
                                    onClick={(e) => singleSaveClick(record)}
                                    loading={updateTranslateLoading}
                                >
                                {formatMessage({id: "common.save",})}
                                </Button>
                            }
                        />
                    </Input.Group>
                }
                {(permissionList.includes("operation.projects.project.multiLanguage.details.edit") && (((edit && edit.has(record.key)) ? edit.get(record.key) : false) === false)) ?
                    <Tooltip
                        title={formatMessage({id: "common.edit",})}
                    >
                        <i
                            style={{
                                marginLeft: 8,
                                cursor: "pointer",
                                color: (((color && color.has(record.key)) ? color.get(record.key) : 0) === 0) ? "#999999" : "#165DFF",
                            }}
                            className="iconfont icon-bianji"
                            onMouseEnter={() => {
                                setColor(() => {
                                    const newColor = new Map();
                                    newColor.set(record.key, 1);
                                    return newColor;
                                });
                            }}
                            onMouseLeave={() => {
                                setColor(() => {
                                    const newColor = new Map();
                                    newColor.set(record.key, 0);
                                    return newColor;
                                });
                            }}
                            onClick={(v) => {
                                setEdit(() => {
                                    const newEdit = new Map();
                                    newEdit.set(record.key, v);
                                    return newEdit;
                                });
                                setCompareInputValue(value);
                            }}
                        ></i>
                    </Tooltip>
                : null
                }
            </Col>
        </>
    }

    // 预览
    const previewClick = ()=>{
        if (!allowPreview(pagePath)) return
        previewRef?.current?.show(pagePath)
    }

    //批量导入
    const batchUploadClick = ()=>{
        form.validateFields(['languageLibrary']).then((values) => {
            uploadRef?.current?.show({
                language,
                projectType,
                customerId: customerId,
                projectId: projectId,
                envId: form.getFieldValue('env'),
                cohortId: form.getFieldValue('cohort'),
                languageId: id,
                languageLibrary: form.getFieldValue('languageLibrary'),
                envLabel: envOptionList.find(env => env.value === form.getFieldValue('env'))?.label,
                cohortLabel: cohortOptionList.find(env => env.value === form.getFieldValue('cohort'))?.label
            })
        }).catch((err) => {})
    };

    // 下载勾选项、下载全部 按钮单击事件
    const buttonMenuClick = (e: any) => {
        if (e.key === "1") {
            //下载勾选项
            const selectedRows = getSelectedRowsData();
            if (selectedRows.length === 0) {
                message.warn(intl.formatMessage({ id: "common.select.list.tips" }));
                return;
            }
            downloadExcel(selectedRows);

        } else if (e.key === "2") {
            //下载全部
            downloadExcel(showData);
        }
    };

    // 获取选中的行数据
    const getSelectedRowsData = () => {
        return showData.filter(row => selectedRowKeys.includes(row.key + row.path.toString()));
    };



    const downloadExcel = async (data: any) => {
        try {
            const userInfo: any = await getUserInfoFromCloudRun().catch(() => null);
            const userTimezone = userInfo?.data?.settings.tz || Intl.DateTimeFormat().resolvedOptions().timeZone;

            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet();

            // 添加表头（第一行）
            worksheet.addRow([
                formatMessage({id: "common.type"}),
                "key",
                formatMessage({id: "common.name"}),
                language,
            ]);

            // 添加数据行
            data.forEach((item: any) => {
                const typeStr = typeOptionList.find(it => it.value === item.type)?.label || ''; // 防止undefined
                const translation = translateMessages[item.key] || ''; // 确保始终有值

                worksheet.addRow([
                    typeStr,
                    item.key,
                    item.label,
                    translation // 用空字符串代替undefined
                ]);
            });

            // 锁定需要保护的区域（第一行和前三列）
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell, colNumber) => {
                    if (rowNumber === 1 || colNumber <= 3) { // 保护标题行和前三列
                        cell.protection = {locked: true};
                    } else {
                        cell.protection = {locked: false}; // 第四列可编辑
                    }
                });
            });

            // 启用工作表保护（随机密码）
            worksheet.protect(Math.random().toString(36).slice(2,10), {
                selectLockedCells: true,  // 允许选中锁定单元格
                formatColumns: true,     // 允许调整列宽
                formatCells: false,       // 禁止格式化
                insertRows: false,         // 禁止插入行
                insertColumns: false,   // 禁止插入列
                deleteColumns: false, // 禁止删除列
            });

            // 生成 Excel 文件
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
            const formatDateTime = (timezone: string) => {
                return new Date().toLocaleString('en-CA', {
                    timeZone: timezone,
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                })
                    .replace(/[^\d]/g, ''); // 移除非数字字符
            };
            const timeString = formatDateTime(userTimezone); // 默认使用本机时区
            const fileName = `${projectNumber}_TranslationTemplate_${timeString}.xlsx`;

            saveAs(blob, fileName);

            message.success(formatMessage({ id: "common.success" }));
        } catch (error: any) {
            message.error("生成 Excel 失败: " + error.message);
        }
    };


    const menu = (
        <Menu onClick={buttonMenuClick} disabled={tableList.length === 0}>
            <Menu.Item key="1">{formatMessage({ id: "multiLanguage.translation.downloadTemplate.downloadCheckbox" })} </Menu.Item>
            <Menu.Item key="2">{formatMessage({ id: "multiLanguage.translation.downloadTemplate.downloadAll" })} </Menu.Item>
        </Menu>
    );

    return (
        <React.Fragment>
            <Modal
                title={language + "-" + projectNumber}
                visible={visible}
                onCancel={hide}
                zIndex={1}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-medium-modal-multiLanguage'
                okText={formatMessage({id: 'common.ok'})}
                onOk={hide}
            >
                <div
                    style={{
                        top: 72,
                        left: 24,
                        borderWidth: "0px 0px 0px 0px",
                        borderStyle: "solid",
                        backgroundColor: "#F2F3F5",
                    }}
                >
                    <Form form={form} >
                        <Row>
                            <Form.Item
                                label={formatMessage({id: 'multiLanguage.language.library'})}
                                name='languageLibrary'
                                rules={[{required: true}]}
                                style={{ marginLeft: "16px", marginTop: "16px"}}
                            >
                                <Select
                                    showSearch
                                    onChange={onLanguageLibraryChange}
                                    placeholder={formatMessage({id: "placeholder.select.common",})}
                                    style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                    options={LanguageLibraryOption}
                                    allowClear
                                />
                            </Form.Item>
                            <Form.Item
                                label={formatMessage({id: 'multiLanguage.page.path'})}
                                rules={[{required: true}]}
                                style={{ marginLeft: "16px", marginTop: "16px"}}
                            >
                                <Cascader
                                    options={pagePathOptionList}
                                    onChange={onPagePathChange}
                                    value={pagePath}
                                    placeholder={formatMessage({id: "placeholder.select.common",})}
                                    showSearch={ pagePathShowSearchConfig }
                                    changeOnSelect
                                    expandTrigger="click"
                                    style={{ width: g.lang === "zh" ? 230 : 200, marginLeft: 0 }}
                                />
                            </Form.Item>
                            {
                                (
                                    form.getFieldValue("languageLibrary") !== undefined &&
                                    form.getFieldValue("languageLibrary") !== null &&
                                    form.getFieldValue("languageLibrary") === 2
                                ) ?
                                <>
                                    {
                                        projectType !== 1?
                                        <>
                                            <Form.Item
                                                label={formatMessage({id: 'projects.env'})}
                                                name='env'
                                                rules={[{required: true}]}
                                                style={{ marginLeft: "16px", marginTop: "16px"}}
                                            >
                                                <Select
                                                    showSearch
                                                    optionFilterProp="children"
                                                    filterOption={(input: any, option: any) =>
                                                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                                    }
                                                    onChange={onEnvChange}
                                                    disabled={(form.getFieldValue("languageLibrary") !== 2)?true:false}
                                                    placeholder={formatMessage({id: "placeholder.select.common",})}
                                                    style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                                    options={envOptionList}
                                                    allowClear
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                label={formatMessage({id: 'check.cohort'})}
                                                name='cohort'
                                                rules={[{required: true}]}
                                                style={{ marginLeft: "16px", marginTop: "16px"}}
                                            >
                                                <Select
                                                    showSearch
                                                    optionFilterProp="children"
                                                    filterOption={(input: any, option: any) =>
                                                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                                    }
                                                    disabled={(form.getFieldValue("languageLibrary") !== 2)?true:false}
                                                    placeholder={formatMessage({id: "placeholder.select.common",})}
                                                    style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                                    options={cohortOptionList}
                                                    allowClear
                                                />
                                            </Form.Item>
                                        </>:
                                        <>
                                            <Form.Item
                                                label={formatMessage({id: 'projects.env'})}
                                                name='env'
                                                rules={[{required: true}]}
                                                style={{ marginLeft: "16px", marginTop: "16px"}}
                                            >
                                                <Select
                                                    showSearch
                                                    optionFilterProp="children"
                                                    filterOption={(input: any, option: any) =>
                                                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                                    }
                                                    onChange={onEnvChange}
                                                    disabled={(form.getFieldValue("languageLibrary") !== 2)?true:false}
                                                    placeholder={formatMessage({id: "placeholder.select.common",})}
                                                    style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                                    options={envOptionList}
                                                    allowClear
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                label={formatMessage({id: 'common.name'})}
                                                name='name'
                                                style={{ marginLeft: "16px", marginTop: "16px"}}
                                            >
                                                <Search
                                                    placeholder={formatMessage({id: "placeholder.input.common",})}
                                                    onSearch={onNameSearch}
                                                    style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                                    loading={getTranslateListLoading}
                                                />
                                            </Form.Item>
                                        </>
                                    }

                                </>:
                                <>
                                    <Form.Item
                                        label={formatMessage({id: 'common.type'})}
                                        name='type'
                                        style={{ marginLeft: "16px", marginTop: "16px"}}
                                    >
                                        <Select
                                            showSearch
                                            optionFilterProp="children"
                                            // onChange={onLanguageLibraryChange}
                                            filterOption={(input: any, option: any) =>
                                                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                            }
                                            placeholder={formatMessage({id: "placeholder.select.common",})}
                                            style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                            options={typeOptionList}
                                            allowClear
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        label={formatMessage({id: 'common.name'})}
                                        name='name'
                                        style={{ marginLeft: "16px", marginTop: "16px"}}
                                    >
                                        <Search
                                            placeholder={formatMessage({id: "placeholder.input.common",})}
                                            onSearch={onNameSearch}
                                            style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                            loading={getTranslateListLoading}
                                        />
                                    </Form.Item>
                                </>
                            }
                        </Row>
                        <Row>
                            <Form.Item
                                label={formatMessage({id: 'multiLanguage.translation.status'})}
                                name='translationStatus'
                                style={{ marginLeft: "16px", marginTop: "16px"}}
                            >
                                <Select
                                    showSearch
                                    placeholder={formatMessage({id: "placeholder.select.common",})}
                                    style={{ width: g.lang==="zh"?230:200, marginLeft: 0 }}
                                    options={TranslationStatusOption}
                                    allowClear
                                />
                            </Form.Item>
                            <Col style={{marginLeft: form.getFieldValue("languageLibrary") === 2?650:650}}>
                                <Button
                                    style={{  marginLeft: "12px", marginTop: "16px" }}
                                    type="primary"
                                    onClick={() => searchClick()}
                                    loading={getTranslateListLoading}
                                >
                                    <FormattedMessage id="check.search" />
                                </Button>
                                <Button
                                    style={{  marginLeft: "12px", marginTop: "16px" }}
                                    // type="primary"
                                    onClick={() => resetClick()}
                                >
                                    <FormattedMessage id="common.reset" />
                                </Button>
                                {
                                    permissionList.includes("operation.projects.project.multiLanguage.details.batchExport") && code!=="zh"&&code!=="en"?
                                    <Button
                                        type="primary"
                                        onClick={() => batchUploadClick()}
                                        style={{  marginLeft: "12px", marginTop: "16px" }}
                                    >
                                        {formatMessage({ id: "multiLanguage.translation.export" })}
                                    </Button>:null
                                }
                            </Col>
                        </Row>
                    </Form>
                </div>

                {
                    (code!=="zh"&&code!=="en")?
                    <Row justify="end" style={{marginTop: 20, marginBottom: 20}}>
                        <Col className="mar-rgt-12">
                            {
                                permissionList.includes("operation.projects.project.multiLanguage.details.preview")?
                                <Button
                                    onClick={() => previewClick()}
                                    style={{marginLeft: 8}}
                                    disabled={!allowPreview(pagePath)}
                                >
                                    {formatMessage({ id: "form.preview" })}
                                </Button>:null
                            }
                            {
                                permissionList.includes("operation.projects.project.multiLanguage.details.downloadTemplate") ?
                                <span style={{marginLeft: 8}}>
                                    <Dropdown
                                        overlay={menu}
                                        trigger={['click']}
                                    >
                                        <Button>
                                            {formatMessage({ id: "common.download.template" })} <DownOutlined />
                                        </Button>
                                    </Dropdown>
                                </span>
                                :null
                            }

                        </Col>
                    </Row>:null
                }


                <Spin spinning={getTranslateListLoading}>
                    <ConfigProvider
                        renderEmpty={
                            () => {
                                return <div
                                    style={{
                                        backgroundColor: "#F9F9FA",
                                        height: "100px",
                                        width: "1360px",
                                        marginLeft: "-16px",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    {intl.formatMessage({ id: "multiLanguage.translation.no.data" })}
                                </div>
                            }
                        }
                    >
                        <Table
                            className="mar-top-10"
                            size="small"
                            pagination={false}
                            rowKey={(record) => record.key + record.path.toString()}
                            dataSource={useCacheTable({ dataSource: showData })}
                            rowSelection={(code!=="zh"&&code!=="en")?rowSelection:null}
                        >
                            <Table.Column
                                title={intl.formatMessage({ id: "common.serial" })}
                                dataIndex="#"
                                key="#"
                                width={70}
                                render={(text, record, index) => ((ctx.currentPage - 1) * ctx.pageSize + index + 1)}
                            />
                            <Table.Column
                                width={200}
                                title={formatMessage({ id: "common.type" })}
                                dataIndex={"type"}
                                key="type"
                                ellipsis
                                render={(text) => typeOptionList.find(it => it.value === text)?.label}
                            />
                            <Table.Column
                                width={200}
                                title={"key"}
                                dataIndex={"key"}
                                key="key"
                                ellipsis
                            />
                            <Table.Column
                                width={200}
                                title={formatMessage({ id: "common.name" })}
                                dataIndex={"label"}
                                key="label"
                                ellipsis
                            />
                            <Table.Column
                                width={150}
                                title={language}
                                dataIndex="compare"
                                key="compare"
                                ellipsis
                                render={(value: any, record: any, index: any) =>
                                    renderLanguage(language, translateMessages[record.key], record)
                                }
                            />
                        </Table>
                        {/* <PaginationView /> */}
                        <PaginationView
                            mode={"SELECTABLE"}
                            // mode={undefined}
                            selectedNumber={selectedRowKeys.length}
                            clearDisplay={true}
                            refresh={refresh}
                        />
                    </ConfigProvider>
                </Spin>
            </Modal>

            <TranslateUpload bind={uploadRef}
                             onUploadComplete={afterUpload}/>
            <LocalesPreview bind={previewRef} languageCode={code} />
        </React.Fragment>
    )
};
