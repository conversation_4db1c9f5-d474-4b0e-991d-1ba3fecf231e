import {Divider, message, Modal, Space, UploadProps} from "antd";
import Dragger from "antd/lib/upload/Dragger";
import FileIcon from "../../../../images/file.svg";
import React, {useImperativeHandle} from "react";
import {useSafeState} from "ahooks";
import {useIntl} from "react-intl";
import styled from "@emotion/styled";
import {HttpRequestHeader} from "antd/es/upload/interface";
import {RcFile} from "antd/lib/upload";
import ExcelJS from "exceljs";
import {getLocalesData} from "../../../common/multilingual/util";

interface KeyInfo {
    path: string;
    type: string; // 或其他具体类型
}

export const TranslateUpload = (props: any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const [uploadVisible, setUploadVisible] = useSafeState<any>(false);
    const [keyInfoMapData, setKeyInfoMapData] = useSafeState<any>(new Map<string, KeyInfo>());
    const [uploadData, setUploadData] = useSafeState<any>({})

    useImperativeHandle(props.bind, () => ({ show }))


    const show = (data: any) => {
        setUploadData(data)
        setUploadVisible(true)
    }

    const hideUpload = () => {
        setUploadVisible(false);
    }

    const getUploadHeaders = (): HttpRequestHeader => {
        const token = sessionStorage.getItem("token") || "";
        let roleId = ""
        if (sessionStorage.getItem("current_project")) {
            roleId = JSON.parse(sessionStorage.getItem("current_project") as string)?.permissions?.role_id
        }
        return {
            'roleId': roleId,
            'token': token,
            'Accept-Language': sessionStorage.getItem('lang') || 'en'
        };
    };

    const checkExcel =  async (file: RcFile) => {
        const result = {
            langMatch: true,
            keyExists: true,
            allKeyValid : true,
            keyInfoMap: new Map<string, KeyInfo>(),
        };

        // 创建工作簿实例
        const workbook = new ExcelJS.Workbook();
        try {
            // 读取文件（根据文件来源选择不同方法）
            // 方法A：从浏览器文件对象读取
            await workbook.xlsx.load(await file.arrayBuffer());
            // 方法B：从Node.js文件系统读取
            // await workbook.xlsx.readFile(filePath);

            // 获取第一个工作表
            const worksheet = workbook.worksheets[0];

            // 获取表头行
            const headerRow = worksheet.getRow(1);

            // 检查表头是否存在目标值
            let translateExists = false;
            let translatePosition = -1;
            let keyExists = false;
            let keyPosition = -1;

            headerRow.eachCell((cell, colNumber) => {
                if (cell.value === uploadData.language) {
                    translateExists = true;
                    translatePosition = colNumber;
                }
                if (String(cell.value).toLowerCase() === "key") {
                    keyExists = true;
                    keyPosition = colNumber;
                }
            });

            if (translateExists && keyExists) {
                let languageLibrary = uploadData.languageLibrary
                let zhObj: any
                if (languageLibrary === 1) {
                    zhObj = getLocalesData('cn');
                } else if (languageLibrary === 2) {
                    // todo
                } else if (languageLibrary === 3) {
                    // todo
                }

                // 构建一个map key-path 的映射关系
                const keyInfoMap = new Map<string, KeyInfo>();
                for (const item of zhObj?.data) {
                    keyInfoMap.set(item.key, {
                        path: item.path,
                        type: item.type
                    });
                }

                const valueSet = new Set(zhObj?.data?.map((item: any) => String(item.key ?? ''))); // 统一转为字符串比较
                const firstDataRow = 2;

                // 构建一个表格中存在的key 的 key-path 映射
                const tableKeyInfoMap = new Map();
                for (let rowNum = firstDataRow; rowNum <= worksheet.rowCount; rowNum++) {
                    const row = worksheet.getRow(rowNum);
                    const cell = row.getCell(keyPosition);
                    const cellValue = cell.value;

                    // 跳过空单元格
                    if (cellValue == null) continue;
                    const strValue = String(cellValue);

                    if (!valueSet.has(strValue)) {
                        result.allKeyValid = false;
                        break;
                    }
                    tableKeyInfoMap.set(strValue, keyInfoMap.get(strValue));
                }
                result.keyInfoMap = tableKeyInfoMap;
            } else {
                if (!translateExists) {
                    result.langMatch = false;
                }
                if (!keyExists) {
                    result.keyExists = false;
                }
            }

            return result;

        } catch (error) {
            console.error('读取Excel文件出错:', error);
            throw error;
        }
    }


    const beforeUpload = async (file: RcFile) => {
        if (file.size > 100 * 1024 * 1024) {
            message.error(intl.formatMessage({ id: "common.upload.fileSize.100" }));
            return Promise.reject();
        }
        // 检查excel 文件内容
        const checkResult = await checkExcel(file);
        if (!checkResult.langMatch || !checkResult.keyExists) {
            message.error(intl.formatMessage({ id: "upload.excel.header.error" }));
            return Promise.reject();
        }
        if (!checkResult.allKeyValid) {
            message.error(intl.formatMessage({ id: "upload.excel.key.not-match" }));
            return Promise.reject();
        }
        setKeyInfoMapData(checkResult.keyInfoMap);

        return true;
    };

    const uploadProps: UploadProps = {
        name: 'file',
        multiple: false,
        action: 'api/multiLanguage/upload',
        accept: '.csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers: getUploadHeaders(),
        data: {
            customerId: uploadData.customerId,
            projectId: uploadData.projectId,
            envId: uploadData.envId,
            cohortId: uploadData.cohortId,
            languageId: uploadData.languageId,
            languageLibrary: uploadData.languageLibrary,
            keyInfoMap: JSON.stringify(Object.fromEntries(keyInfoMapData)),
        },
        beforeUpload: beforeUpload,
        onChange(info) {
            const { status } = info.file;
            if (status === 'done') {
                if (info.file.response) {
                    const response = info.file.response;
                    if (response.code === 0) {
                        props.onUploadComplete();
                        message.success(intl.formatMessage({ id: "upload.import.success" }));
                        hideUpload();
                    } else {
                        // 如果接口返回了错误信息
                        const errorMsg = response.msg;
                        message.error(errorMsg);
                    }
                }
            } else if (status === 'error') {
                // 可能是网络错误，应该不会走到这里
                message.error("upload fail");
            }
        },
        showUploadList: false,

    };


    return <Modal
        className={"custom-small-modal"}
        bodyStyle={{padding: "16px 24px"}}
        title={formatMessage({ id: "notice.exclude_recipient_list.email.batch" })}
        open={uploadVisible}
        onCancel={hideUpload}
        footer={null}
        zIndex={9999}
    >
        {
            uploadData.languageLibrary === 2 ?
                <Space split={<StyledDivider type="vertical"/>} style={{transform: "translateY(-8px)"}}>
                    <span style={{ fontStyle: 'normal' }}>{formatMessage({id: "projects.env"})}: {uploadData.envLabel}</span>
                    {
                        uploadData.projectType !== 1 ?
                            <span>{formatMessage({id: "check.cohort"})}: {uploadData.cohortLabel}</span>
                            :
                            null
                    }
                </Space>
                :
                null
        }
        <div style={{height: "220px"}}>
            <Dragger {...uploadProps} style={{backgroundColor: "#FFFFFF"}}>
                <p className="ant-upload-drag-icon">
                    <img src={FileIcon} />
                </p>
                <p style={{ fontSize: "14px", fontWeight: "400" }}>
                    <span style={{color: "#2B74EA"}}>{formatMessage({ id: "upload.click" })}</span>
                    {formatMessage({ id: "upload.drag" })}
                </p>
                <p  style={{ fontSize: "13px", fontWeight: "400", color: "#999999" }}>
                    {formatMessage({ id: "upload.excel.reminder" })}
                </p>
            </Dragger>
        </div>
    </Modal>
}


const StyledDivider = styled(Divider)`
    border: 1px solid #C8C9CC;
`