import {FormattedMessage} from "react-intl";
import React from "react";


export const TranslationStatusOption = [
    {
        label: <FormattedMessage id={'multiLanguage.translation.status.yes'} />,
        value: true,
    },
    {
        label: <FormattedMessage id={'multiLanguage.translation.status.no'} />,
        value: false,
    }
]


export const LanguageLibraryOption = [
    {
        label: <FormattedMessage id={'multiLanguage.language.library.system'} />,
        labelCn: '项目库/系统库',
        labelEn: 'Project Library/System Library',
        value: 1,
    },
    {
        label: <FormattedMessage id={'multiLanguage.language.library.construct'} />,
        labelCn: '项目库/项目构建',
        labelEn: 'Project Library/Project Design',
        value: 2,
    },
    {
        label: <FormattedMessage id={'multiLanguage.language.library.eIRT'} />,
        labelCn: 'eIRT库',
        labelEn: 'eIRT Library',
        value: 3,
    },
]