import {Button, Col, Form, message, Row, Select, Space, Switch, Table, Typography} from "antd";
import React, {useEffect, useRef} from "react";
import {useSafeState} from "ahooks";
import {FormattedMessage, useIntl} from "react-intl";
import {useFetch} from "../../../hooks/request";
import {getName, getList, updateLanguage, deleteLanguage} from "../../../api/multi_language";
import {getViewMultiLanguage} from "../../../api/projects";
import {useMain} from "../context";
import {useMultiLanguage} from "./context";
import {useGlobal} from "../../../context/global";
import { useAuth } from "../../../context/auth";
import { Add } from "./add";
import { Edit } from "./edit";
import { Show } from "./translate/show";
import {getProjectEnvironmentRolesPermissions} from "../../../api/user";
import { CustomConfirmModal } from "../../../components/modal";
import { HistoryList } from "./history-list";
import {getLocalesData} from "../../common/multilingual/util";
import _ from "lodash";


const { Option } = Select;

export const Main = () => {
    const g = useGlobal();
    const auth = useAuth();
    const [languages, setLanguages] = useSafeState([]);
    const [nameList, setNameList] = useSafeState([]);

    
    const intl = useIntl();

    const { runAsync: getNameRun, loading: getNameLoading } = useFetch(getName,{ manual: true });
    const { runAsync: listRun, loading: getListLoading } = useFetch(getList,{ manual: true });
    const { runAsync: runGetProjectList, loading: getProjectListLoading } = useFetch(getViewMultiLanguage, { manual: true });
    const { runAsync: roleRunAsync, loading: getProjectEnvironmentRolesPermissionsLoading  } = useFetch(getProjectEnvironmentRolesPermissions,{manual: true,});
    const { runAsync: updateLanguageRun, loading: updateLanguageLoading } = useFetch(updateLanguage,{ manual: true });
    const { runAsync: deleteLanguageRun, loading: deleteLanguageLoading } = useFetch(deleteLanguage,{ manual: true });

    const main = useMain();
    const ctx = useMultiLanguage();
    const [projects, setProjects] = useSafeState<any[]>([]);
    const [selectProject, setSelectProject] = useSafeState<string>("");

    const lang_add: any = React.useRef();
    const lang_edit: any = React.useRef();
    const lang_show: any = React.useRef();
    const lang_history: any = React.useRef();

    const [permissionList, setPermissionList] = useSafeState<any[]>([]);

    const { formatMessage } = intl;

    const get_lists = () => {
        getNameRun({}).then((result: any) => {
            setNameList(result.data);
        });
        let num: any = 0;
        if(main.project?.info.type !== 1){
            main.project.envs.forEach((env: any) => {
                if(env.cohorts !== undefined && env.cohorts !== null && env.cohorts.length > 0){
                    num += env.cohorts.length;
                }
            });
        } else {
            num = 0;
        }
        if (main.project?.id) {
            roleRunAsync({ projectId: main.project.id}).then((res: any) => {
                if (res.data) {
                    if (res.data.length > 0) {
                        setPermissionList(res.data);
                        listRun({
                            customerId: main.project.customerId,
                            projectId: main.project.id,
                        }).then((result: any) => {
                            const languages: any = [];
                            result.data.forEach((it: any) => {
                                if (res.data?.includes("operation.projects.project.multiLanguage.view")) {
                                    if(it.code === "zh" || it.code === "en"){
                                        it.progress = "100%";
                                    } else {
                                        //项目库/系统库
                                        let systemLibraryCount = _.uniqBy(getLocalesData('zh')?.data || [], 'key').length;
                                        //项目库/项目构建----todo待完善
                                        let projectDesignCount = num * 0;
                                        //eIRT库----todo待完善
                                        let eIRTLibraryCount = 0;

                                        let total = systemLibraryCount + projectDesignCount +  eIRTLibraryCount;
                                        let completionRate = divideToPercent(it.translationQuantity, total);
                                        it.progress = completionRate;
                                    }
                                    languages.push(it);
                                }
                            });
                            // console.log("languages===" + JSON.stringify(languages));
                            setLanguages(languages);
                        });
                    } else {
                        setLanguages([]);
                        setPermissionList([]);
                    }
                }
            });
        }
    };

    function divideToPercent(num1: any, num2: any) {
        if (num2 === 0) {
            return 'Error: Division by zero';
        }
        let result = num1 / num2;
        // 转换为百分比形式
        result *= 100;
        // 截断到两位小数
        let truncatedResult = Math.floor(result * 100) / 100;
        // 使用 toFixed(1) 确保保留一位小数并添加百分号
        // 注意：toFixed 返回的是字符串
        let completionRate = truncatedResult.toFixed(1);
        if(completionRate === "0.0"){
            completionRate = "0";
        }
        return completionRate + '%';
    }

    useEffect(() => {
        if (main.project?.id) {
            roleRunAsync({ projectId: main.project.id}).then((res: any) => {
                if (res.data) {
                    setPermissionList(res.data);
                }
            });
        }
        runGetProjectList().then((res: any) => {
            const list: any = [];
            if (res.data) {
                res.data?.map((project: any) => {
                    list.push({
                        label: `${project.info.number}`,
                        value: project.id,
                        project: project,
                    });
                });
                if (list.length) {
                    onSelectChange(list[0].value, {
                        option: {
                            project: list[0].project,
                        },
                    });
                }
            }
            setProjects(list);
        });
    }, []);

    useEffect(() => {
        if (main.project?.id) {
            get_lists();
        }
    }, [main.project?.id]);

    const onSelectChange = (value: any, option: any) => {
        const { project } = option.option;
        main.setProject(project);
        setSelectProject(value);
    };

    
    //语言-轨迹
    const trailLang = (record: any) => {
        lang_history.current.show(main.project?.customerId, main.project?.id, permissionList);
    };

    //添加语言
    const addLang = (record: any) => {
        // 使用 map 提取 id
        const languageNameList = languages?languages.map((item: any) => item.language):[];
        lang_add.current.show(main.project?.customerId, main.project?.id, nameList, languageNameList);
    };

    //编辑语言
    const editLang = (record: any) => {
        // 使用 map 提取 id
        const languageNameList = languages?languages.map((item: any) => item.language):[];
        lang_edit.current.show(main.project?.customerId, main.project?.id, nameList, languageNameList, record);
    };

    //删除语言
    const deleteLang = (record: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: 'common.confirm.delete' }),
            okText:formatMessage({ id: 'common.ok' }),
            cancelText:formatMessage({ id: 'common.cancel' }),
            onOk: () => {
                deleteLanguageRun({ id: record.id }).then(
                    () => {
                        message.success(formatMessage({ id: 'message.save.success' }))
                        refresh()
                    }
                )
            }
        });
    };

    const showLang = (record: any) => {
       // 使用 map 提取 id
       const languageNameList = languages?languages.map((item: any) => item.language):[];
       lang_show.current.show(main.project, nameList, languageNameList, record, permissionList);
    };

    const refresh = () => {
        get_lists();
    };

    const openStatusChange = (v: any, record: any) => {
        let status = 0;
        if(v){
           status = 1;
        }
        updateLanguageRun({ customerId: main.project?.customerId, projectId: main.project?.id },
            {
                id: record.id,
                customerId: main.project?.customerId,
                projectId: main.project?.id,
                code: record.code,
                language: record.language,
                status: status,
                sharedSystemLibrary: record.sharedSystemLibrary,
            }).then(
                (resp:any) => {
                    message.success(resp.msg)
                    refresh();
                }
            )
    };

    const getOperateBtn = (record: any) => {
        return (
            <Row>
                <Space size={12}>
                    {
                        (!permissionList.includes("operation.projects.project.multiLanguage.edit") &&
                        !permissionList.includes("operation.projects.project.multiLanguage.delete") ) ||
                        (record.code === "zh" || record.code === "en")?
                        "-":
                        <>
                            {permissionList.includes("operation.projects.project.multiLanguage.edit") ? (
                                <Typography.Link
                                    onClick={() => editLang(record)}
                                >
                                    {formatMessage({ id: "common.edit" })}
                                </Typography.Link>
                            ) : null}
                            {(permissionList.includes("operation.projects.project.multiLanguage.delete") && record.progress === "0%")? (
                                <Typography.Link
                                    onClick={() => deleteLang(record)}
                                    // disabled
                                >
                                    {formatMessage({ id: "common.delete" })}
                                </Typography.Link>
                            ) : null}
                        </>

                    }
                </Space>
            </Row>
        );
    };

    return (
        <>

            <Row align={"middle"}>
                {formatMessage({ id: "menu.projects" })}：
                <Select
                    style={{ width: 300 }}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ maxWidth: 600 }}
                    onChange={onSelectChange}
                    value={selectProject}
                    showSearch
                    loading={getProjectListLoading}
                    filterOption={(input, option) =>
                        option.children.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0
                    }
                >
                    {projects.map((project: any, index) => {
                        return (
                            <Option
                                value={project.value}
                                id={project.value}
                                key={project.value}
                                option={project}
                            >
                                {project.label}
                            </Option>
                        );
                    })}
                </Select>
            </Row>
            <Row justify="space-between" style={{ marginTop: "16px" }}>
                <Col className="mar-rgt-12">
                    <Row justify="space-between">
                        <Row>
                            <div
                                style={{
                                    display: "flex",
                                    alignItems: "center",
                                }}
                            >
                                <svg
                                    className="iconfont"
                                    width={16}
                                    height={16}
                                >
                                    <use xlinkHref="#icon-quanbuyuyan"></use>
                                </svg>
                                <span
                                    style={{
                                        color: "#1D2129",
                                        fontWeight: 600,
                                        paddingLeft: "8px",
                                    }}
                                >
                                    {formatMessage({ id: "multiLanguage.allLanguage" })}
                                </span>
                            </div>
                        </Row>
                    </Row>
                </Col>
                <Row justify="end">
                    <Col className="mar-rgt-12">
                        {
                            permissionList.includes("operation.projects.project.multiLanguage.trail")?
                            <Button
                                onClick={trailLang}
                            >
                                <FormattedMessage id="common.history" />
                            </Button>:null
                        }
                        {
                            permissionList.includes("operation.projects.project.multiLanguage.add")?
                            <Button
                                type="primary"
                                onClick={addLang}
                                style={{  marginLeft: "12px", }}
                            >
                                <FormattedMessage id="common.addTo" />
                            </Button>:null
                        }
                    </Col>
                </Row>
            </Row>
            <Table
                dataSource={languages}
                loading={getListLoading || getProjectEnvironmentRolesPermissionsLoading}
                rowKey={(record: any) => record.id}
                className="mar-top-16"
                pagination={false}
                scroll={{ y: "calc(100vh - 190px)" }}
            >
                <Table.Column
                    title={formatMessage({ id: "common.language" })}
                    dataIndex="language"
                    render={
                        (value, record) => (
                            permissionList.includes("operation.projects.project.multiLanguage.details.view") ?
                                <Button 
                                    size="small" 
                                    type="link" 
                                    onClick={() => { showLang(record) }}
                                >
                                    {value}
                                </Button>
                                :
                                value
                        )
                    }
                    ellipsis
                />
                <Table.Column
                    title={formatMessage({ id: "multiLanguage.enable.status" })}
                    dataIndex="status"
                    render={(value, record: any, index) => (
                        <Switch
                            disabled={(!(permissionList.includes("operation.projects.project.multiLanguage.edit") && (record.code !== "zh" && record.code !== "en")))}
                            onChange={(v: any) => openStatusChange(v, record)}
                            loading={updateLanguageLoading}
                            defaultChecked={value === 1}
                            size="small" 
                        />
                    )}
                />
                <Table.Column
                    title={formatMessage({ id: "multiLanguage.translation.progress" })}
                    dataIndex="progress"
                    render={(value: any, record: any) => {
                        return value;
                    }}
                />
                <Table.Column
                    title={formatMessage({ id: "multiLanguage.shared.system.library" })}
                    dataIndex="sharedSystemLibrary"
                    render={(value) => 
                        (value) ? formatMessage({ id: "common.yes" }) : formatMessage({ id: "common.no" })
                    }
                />
                <Table.Column
                    title={formatMessage({ id: "common.operation" })}
                    dataIndex="#"
                    width={g.lang==="zh"?250:300}
                    render={(_, record: any) => {
                        return getOperateBtn(record);
                    }}
                />
            </Table>

            <Add bind={lang_add} refresh={refresh} />
            <Edit bind={lang_edit} refresh={refresh} />
            <Show bind={lang_show} refresh={refresh} />
            <HistoryList bind={lang_history} refresh={refresh}/>
        </>
    );
};
