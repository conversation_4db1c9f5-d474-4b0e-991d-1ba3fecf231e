import {InsertDivider} from "components/divider";
import {CaretDownOutlined, CaretRightOutlined} from "@ant-design/icons";
import {delCohort, getProject, updateLock} from "../../../api/projects";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../hooks/request";
import {useAuth} from "../../../context/auth";
import {FormattedMessage, useIntl} from "react-intl";
import {Button, Col, message, Row, Spin, Table} from "antd";
import styled from "@emotion/styled";
import React, {useEffect, useRef} from "react";
import ProjectInfoEnvAdd from "./project-info-env-add";
import ProjectInfoEnvCopy from "./project-info-env-copy";
import ProjectInfoCohort from "./project-info-cohort";
import ProjectInfoCohortCopy from "./project-info-cohort-copy";
import {CustomConfirmModal} from "components/modal";
import {Title as CustomTitle} from "../../../components/title";
import {useGlobal} from '../../../context/global';
import {projectSettingButtonPermission} from "../../../tools/permission";
import ProjectInfoEnvEdit from "./project-info-env-edit";
import {AlertThresholdType} from "../../../data/data";

const ProjectInfo = props => {

    const g = useGlobal()

    const envAdd = useRef();
    const envEdit = useRef();
    const envCopy = useRef();
    const cohort = useRef();
    const cohortCopy = useRef();

    const { formatMessage } = useIntl();

    const auth = useAuth();

    const [expandedProjectKey, setExpandedProjectKey] = useSafeState("");
    const [project, setProject] = useSafeState(null);
    const { runAsync: runGetProject, loading } = useFetch(() => getProject({ id: props.project?.id }), {
        refreshDeps: [props.project?.id],
        onSuccess: result => {
            const data = result.data;
            if (data != null && data.info.type === 3) {
                atRandom(data);
            }
            setProject(data);
            expandedProjectKey || setExpandedProjectKey(data?.envs?.[0]?.id);
        }
    });

    const [disable, setDisable] = useSafeState(false);
    useEffect(() => {
        if (project) {
            setDisable(project.status !== 0);
        }
    }, [project]);

    // 在随机格式化数据
    const atRandom = (data) => {
        if (data.envs != null) {
            data.envs.forEach((env) => {
                if (env.cohorts != null) {
                    env.cohorts.forEach((cohort) => {
                        cohort.lastItem = (env.cohorts.find(it => it.id === cohort.lastId) === undefined || env.cohorts.find(it => it.id === cohort.lastId) === null) ? "" : env.cohorts.find(it => it.id === cohort.lastId).name;
                    });
                }
            })
        }
        return data
    }

    const commonRender = v => v ? v : '-'

    const expandableColumns = (project, record) => {
        const columns = [
            {
                title: formatMessage({ id: 'common.serial' }),
                key: '#',
                width: 60,
                ellipsis: true,
                render: (v, r, i) => i + 1
            },
            {
                title: formatMessage({ id: 'common.name' }),
                dataIndex: 'name',
                ellipsis: true,
                render: commonRender
            },
            {
                title: formatMessage({ id: 'common.status' }),
                key: 'status',
                width: 80,
                ellipsis: true,
                render: v => v ? formatMessage({ id: `projects.envs.cohorts.status${v.status}` }) : '-'
            },
            {
                title: formatMessage({ id: 'projects.envs.cohorts.capacity' }),
                dataIndex: 'alertThresholds',
                width: 100,
                ellipsis: true,
                render: (v, r) => {
                    return v && v.length > 0 ? v.map((value) => <p style={{ margin: 0 }}>{getAlertThresholdType(value.type)}{formatMessage({ id: 'common.colon' })}{value.capacity}</p>) : '-'
                }
            },
            {
                title: formatMessage({ id: "projects.envs.cohorts.reminder.thresholds" }),
                dataIndex: 'alertThresholds',
                key: 'alertThresholds',
                width: 120,
                ellipsis: true,
                render: (v, r) => {
                    return v && v.length > 0 ? v.map((value) => value.thresholds ? <p style={{ margin: 0 }}>{getAlertThresholdType(value.type)}{formatMessage({ id: 'common.colon' })}{value.thresholds + '%'}</p> : "-") : '-'
                }
            },
        ]
        const factor = {
            title: formatMessage({ id: 'projects.envs.cohorts.factor' }),
            dataIndex: 'factor',
            width: 80,
            ellipsis: true,
            render: commonRender
        }
        const stage = {
            title: formatMessage({ id: "projects.envs.cohorts.stage" }),
            key: "stage",
            width: 100,
            ellipsis: true,
            render: (v, r) =>
                project.info.type === 3?(r.lastId && record.cohorts?.find(it => it.id === r.lastId)?.name || "-"):(r.lastId && record.cohorts?.find(it => it.id === r.lastId)?.reRandomName || "-")
        }
        const reRandomName = {
            title: formatMessage({ id: "projects.third.name" }),
            key: "reRandomName",
            width: 100,
            ellipsis: true,
            render: (v, r) => v.reRandomName ? v.reRandomName : "-"
        }
        if (project.info.connectEdc === 1 && (project.info.type === 3|| (project.info.type === 2 && record.cohorts?.find(it => it.type === 1))) ) {
            if (project.info.pushMode === 1) {
                columns.splice(2, 0, factor)
                columns.splice(1, 0, reRandomName)
            }
            columns.splice(4, 0, stage)
        } else if (project.info.connectEdc === 1) {
            if (project.info.pushMode === 1) {
                columns.splice(2, 0, factor)
            }
        } else if (project.info.type === 3 || (project.info.type === 2 && record.cohorts?.find(it => it.type === 1))) {
            columns.splice(3, 0, stage)
            columns.splice(2, 0, reRandomName)
        }
        if (props.editable) {
            columns.push({
                title: formatMessage({ id: 'common.operation' }),
                key: 'operation',
                width: g.lang === "zh" ? 150 : 180,
                fixed: 'right',
                render: (v, r) => {
                    const btns = []

                    projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.edit") &&
                        btns.push(
                            <Button style={{ padding: 0 }} size="small" type="link" onClick={() => {
                                cohort.current.show(record.lockConfig, props.project.info.pushMode, props.project.id, record.id, record.name, props.project.info.type, props.project.info.connectEdc, record.cohorts, r);
                            }} disabled={disable}><FormattedMessage id={"common.edit"} /></Button>
                        )
                    !record.lockConfig && projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.copy_cohort") &&
                        btns.push(
                            <Button style={{ padding: 0 }} size="small" type="link" onClick={() => {
                                cohortCopy.current.show(props.project.id, record.id, props.project.info.type, props.project.info.connectEdc, record.cohorts, r, props.project.info.pushMode);
                            }} disabled={disable}><FormattedMessage id={"common.copy"} /></Button>
                        )
                    !record.lockConfig && projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.delete") &&
                        btns.push(
                            <Button style={{ padding: 0 }} size="small" type="link" onClick={e => {
                                e.stopPropagation();
                                onDelCohort(record.id, r.id);
                            }} disabled={disable}><FormattedMessage id={"common.delete"} /></Button>
                        )
                    return InsertDivider(btns)
                }
            })
        }
        return columns
    }
    const getAlertThresholdType = (e) => {
        return AlertThresholdType.find((v) => v.id === e).name
    }
    const columns = () => {
        const columns = [
            {
                title: formatMessage({ id: 'projects.envs.name' }),
                dataIndex: 'name',
                ellipsis: true,
                render: commonRender
            },
        ]
        if (project?.info.type === 1) {
            columns.push(
                {
                    title: formatMessage({ id: 'common.status' }),
                    key: 'status',
                    width: 100,
                    ellipsis: true,
                    render: v => v ? formatMessage({ id: `projects.envs.cohorts.status${v.status}` }) : '-'
                }
            )
            columns.push(
                {
                    title: formatMessage({ id: 'projects.envs.cohorts.capacity' }),
                    dataIndex: 'alertThresholds',
                    render: (v, r) => {
                        return v && v.length > 0 ? v.map((value) => <p style={{ margin: 0 }}>{getAlertThresholdType(value.type)}{formatMessage({ id: 'common.colon' })}{value.capacity}</p>) : '-'
                    }
                }
            )
            columns.push(
                {
                    title: formatMessage({ id: 'projects.envs.cohorts.reminder.thresholds' }),
                    dataIndex: 'alertThresholds',
                    render: (v, r) => {
                        // console.log(v)
                        return v && v.length > 0 ? v.map((value) => value.thresholds ? <p style={{ margin: 0 }}>{getAlertThresholdType(value.type)}{formatMessage({ id: 'common.colon' })}{value.thresholds + '%'}</p> : "-") : '-'
                    }
                }
            )
        }

        if (props.editable) {
            columns.push({
                title: formatMessage({ id: 'common.operation' }),
                key: 'operation',
                width: 180,
                render: (v, r) => {
                    const btns = []

                    projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.unlock") && v?.lockConfig &&
                        btns.push(
                            <Button style={{ padding: 0 }} size='small' type='link' onClick={() => {
                                operLockEnv(props.project, r)
                            }}>
                                {<FormattedMessage id={'common.unlock'} />}
                            </Button>
                        )

                    projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.lock") && !(v?.lockConfig) &&
                        btns.push(
                            <Button style={{ padding: 0 }} size="small" type="link" onClick={() => {
                                operLockEnv(props.project, r);
                            }} disabled={disable}>
                                {<FormattedMessage id={"common.lock"} />}
                            </Button>
                        )
                    projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.copy") && !(v?.lockConfig) &&
                        btns.push(
                            <Button style={{ padding: 0 }} size="small" type="link" onClick={() => {
                                envCopy.current.show(props.project.id, r.id, r);
                            }} disabled={disable}><FormattedMessage id={"common.copy"} /></Button>
                        )

                    props.project.info.type === 1 && projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.edit_env") &&
                        btns.push(
                            <Button style={{ padding: 0 }} size="small" type="link" onClick={() => {
                                operEditEnv(props.project, r);
                            }} disabled={disable}>
                                {<FormattedMessage id={"common.edit"} />}
                            </Button>
                        )

                    props.project.info.type !== 1 && projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.add") &&
                        btns.push(
                            <Button style={{ padding: 0 }} size="small" type="link" onClick={() => {
                                cohort.current.show(v?.lockConfig, props.project.info.pushMode, props.project.id, r.id, r.name, props.project.info.type, props.project.info.connectEdc, r.cohorts);
                            }} disabled={disable}><FormattedMessage id={"common.addTo"} /></Button>
                        )

                    return InsertDivider(btns)
                }
            })
        }
        return columns
    }

    const { runAsync: runUpdateLock } = useFetch(updateLock, { manual: true })
    const operLockEnv = (project, env) => {
        if (project && project.id && env.id) {
            let title = formatMessage({ id: 'projects.envs.oper.lock.title' });
            let content = formatMessage({ id: 'projects.envs.oper.lock.message' });

            if (env.lockConfig) {
                title = formatMessage({ id: 'projects.envs.oper.unlock.title' });
                content = formatMessage({ id: 'projects.envs.oper.unlock.message' })
            }

            CustomConfirmModal({
                title: title,
                content: content,
                cancelText: formatMessage({ id: 'common.cancel' }),
                okText: formatMessage({ id: 'common.ok' }),
                onOk: () => {
                    runUpdateLock({ id: project.id, envID: env.id }, { lockConfig: !env.lockConfig, name: env.name }).then((result) => {
                        message.success(result.msg).then()
                        runGetProject()
                    }
                    )
                }
            })
        }
    }

    const operEditEnv = (project, env) => {
        envEdit.current.show(project, env);
    }

    const { runAsync: runDelCohort } = useFetch(delCohort, { manual: true })
    const onDelCohort = (envid, cohortid) => {
        CustomConfirmModal({
            title: formatMessage({ id: 'common.confirm.delete' }),
            // content: formatMessage({id: 'common.confirm.is_delete'}),
            okText: formatMessage({ id: 'common.ok' }),
            cancelText: formatMessage({ id: 'common.cancel' }),
            onOk: () => {
                const deleting = message.loading('Deleting...', 0)
                runDelCohort({ id: props.project.id, envID: envid, cohortID: cohortid }, {}).then((resp) => {
                    message.success(resp.msg)
                    runGetProject()
                    deleting()
                }).catch(() => deleting())
            }
        })
    }

    const expandedProps = {
        // columnTitle: formatMessage({id: 'projects.envs.name'}),
        expandIcon: ({ expanded, onExpand, record }) =>
            expanded ? (
                <CaretDownOutlined onClick={e => onExpand(record, e)} />
            ) : (
                <CaretRightOutlined onClick={e => onExpand(record, e)} />
            ),
        expandedRowKeys: [expandedProjectKey],
        onExpand: (expanded, record) => {
            if (expanded) {
                setExpandedProjectKey(record.id)
            } else {
                setExpandedProjectKey('')
            }
        },
        expandedRowRender: (record) => (
            <ExpandTable size='small' dataSource={record.cohorts || []} pagination={false}
                rowKey={(record) => (record.id)}
                         scroll={{ x: "max-content" }}
                columns={expandableColumns(props.project, record)}
            />
        )
    }

    return (
        <Spin spinning={loading}>
            <Row gutter={[8, 16]}>
                <Col span={24}>
                    <HeaderRow>
                        <Col span={12}>
                            <CustomTitle name={formatMessage({ id: "projects.envs.all" })} />
                        </Col>
                        {
                            !disable && projectSettingButtonPermission(props.project, auth, "operation.projects.main.config.create") &&
                            <Col span={12} style={{ textAlign: "right" }}>
                                <Button type="primary" onClick={() => {
                                    envAdd.current.show();
                                }}>
                                    <FormattedMessage id="projects.envs.add" />
                                </Button>
                            </Col>
                        }

                    </HeaderRow>
                </Col>
                <Col span={24}>
                    <Table size='small' dataSource={project?.envs || []} pagination={false}
                        rowKey={(record) => (record.id)}
                        // scroll={{ y: 'calc(100vh - 320px)' }}
                        scroll={{ y: 580 }}
                        columns={columns()}
                        expandable={!project?.envs || project.envs.length === 0 || props.project.info.type === 1 ? {} : expandedProps}
                    />
                </Col>
            </Row>
            <ProjectInfoEnvAdd bind={envAdd} refresh={runGetProject} project={project} />
            <ProjectInfoEnvEdit bind={envEdit} refresh={runGetProject} project={project} />
            <ProjectInfoEnvCopy bind={envCopy} refresh={runGetProject} project={project} />
            <ProjectInfoCohort bind={cohort} refresh={runGetProject} />
            <ProjectInfoCohortCopy bind={cohortCopy} refresh={runGetProject} />
        </Spin>
    )
}

const HeaderRow = styled(Row)`
    align-items: center !important;
`
const ExpandTable = styled(Table)`
    .ant-table-thead>tr>th{
        background-color:#fff;
        color: #4E5969
    }
`
export default ProjectInfo
