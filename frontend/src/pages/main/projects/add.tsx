import React, {useEffect} from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Col, Form, Input, message, Modal, Radio, Row, Select, Space, Spin, Tooltip} from "antd";
import {useFetch} from "../../../hooks/request";
import {addProject} from "../../../api/customer";
import {useAuth} from "../../../context/auth";
import {useSafeState} from "ahooks";
import {InfoCircleFilled, QuestionCircleFilled} from "@ant-design/icons";
import {useGlobal} from "../../../context/global";
import {customers} from "../../../api/user";

export const Add = (props: any) => {

    const g = useGlobal()

    const intl = useIntl();
    const { formatMessage } = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [showDTP, setShowDTP] = useSafeState<any>(false);
    const [form] = Form.useForm();
    const [customersSelect,setCustomersSelect] = useSafeState([])

    const { runAsync: addProjectRun, loading: addProjectLoading } = useFetch(addProject, { manual: true })
    const {runAsync:customersRun, loading:customersLoading} = useFetch(customers, {manual: true});

    const show = (data: any) => {
        if (data) {
            form.setFieldsValue({ email: data.email });
        }
        setVisible(true);
    };

    const hide = () => {
        form.resetFields();
        setShowDTP(false)
        setVisible(false);
    };

    const phoneNumberValidator = {
        validator: (rule: any, value: any, callback: any) => {
            if (value && value !== "") {
                const reg = /^[-,0-9 ]+$/
                if (!reg.test(value)) {
                    return Promise.reject(formatMessage({ id: 'input.error.common' }));
                }
            }
            return Promise.resolve();
        }
    };

    const showResearchAttribute = (e: any) => {
        if (e === 1) {
            setShowDTP(true)
        }else{
            setShowDTP(false)

        }
    };

    const save = () => {
        form.validateFields().then(values => {
            // 默认的项目配置
            const defaultOpt = {
                type: 1,
                name: '',
                number: '',
                phone: '',
                sponsor: '',
                connectEdc: 2,
                synchronizationMode: 1,
                description: '',
                timeZone: 0,
                timeZoneStr: "0",
                connectLearning: 2,
                orderCheck: 1,
                researchAttribute: 0
            }
            addProjectRun({ id:  values.customerId },
                {
                    // 注意顺序！！！
                    ...defaultOpt,
                    ...values
                }).then(
                    (resp:any) => {
                        message.success(resp.msg)
                        props.refresh();
                        hide();
                    }
                )
        }
        ).catch(() => { });
    }
    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 7 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 17 },
        },
    }

    useEffect(()=>{
        customersRun({projectAdmin:false,customerAdmin:true}).then((res:any)=>{
            setCustomersSelect(res.data)
        })
    },[g.lang])

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id="operation.projects.main.create" />}
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okText={formatMessage({id: 'common.ok'})}
                okButtonProps={{loading: addProjectLoading||customersLoading}}
                onOk={save}
            >
                <Spin spinning={customersLoading}>
                    <Form form={form} layout="horizontal" {...formItemLayout} >
                        <Form.Item
                            label={formatMessage({ id: 'common.customer' })}
                            name="customerId" rules={[{ required: true }]}
                        >
                            <Select placeholder={formatMessage({ id: 'placeholder.select.common' })}>
                                {
                                    customersSelect.map((e:any)=><Select.Option value={e.id}>{e.name}</Select.Option>)
                                }
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label={formatMessage({ id: 'projects.type' })}
                            name="type" rules={[{ required: true }]}
                        >
                            <Select placeholder={formatMessage({ id: 'placeholder.select.common' })}>
                                <Select.Option value={1}><FormattedMessage id="projects.types.first" /></Select.Option>
                                <Select.Option value={2}><FormattedMessage id="projects.types.second" /></Select.Option>
                                <Select.Option value={3}><FormattedMessage id="projects.types.third" /></Select.Option>
                            </Select>
                        </Form.Item>

                        <Form.Item label={formatMessage({ id: 'projects.number' })} name="number" rules={[{ required: true }]}>
                            <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear maxLength={20} showCount />
                        </Form.Item>

                        <Form.Item label={formatMessage({ id: 'projects.name' })} name="name" rules={[{ required: true }]}>
                            <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear maxLength={100} showCount />
                        </Form.Item>

                        <Form.Item label={formatMessage({ id: 'projects.sponsor' })} name="sponsor" rules={[{ required: true }]}>
                            <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear maxLength={100} showCount />
                        </Form.Item>
                        <Form.Item label={formatMessage({ id: 'projects.contact.information' })} name="phone" rules={[phoneNumberValidator]}>
                            <Input placeholder={formatMessage({ id: 'placeholder.input.contact' })} allowClear maxLength={100} showCount />
                        </Form.Item>

                        <Form.Item label={formatMessage({ id: 'projects.description' })} name="description">
                            <Input.TextArea placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear maxLength={500} showCount autoSize={{ minRows: 3, maxRows: 10 }} />
                        </Form.Item>
                    </Form>
                </Spin>

            </Modal>
        </React.Fragment>
    )
}
