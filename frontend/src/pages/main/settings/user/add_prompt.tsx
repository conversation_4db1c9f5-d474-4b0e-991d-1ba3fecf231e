import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Table, message, Modal, Row, Col, Button} from "antd";
import {useFetch} from "../../../../hooks/request";
import {addBatchUser} from "../../../../api/user";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import { UserAddTip } from "./add_tip";
import {useUser} from "./context";

export const UserAddPrompt = (props :any) => {

    const ctx = useUser();
    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal()

    const [visible, setVisible] = useSafeState(false);
    const [customerId, setCustomerId] = useSafeState(null);
    const [emailLanguage, setEmailLanguage] = useSafeState(null);
    const [emailList, setEmailList] = useSafeState([]);
    const [data, setData] = useSafeState([]);

    const user_tip: any = React.useRef();

    const [form] = Form.useForm();

    const show = (customerId:any, emailLanguage: any, emailList: any, data: any) => {
        setVisible(true);
        setCustomerId(customerId);
        setEmailLanguage(emailLanguage);
        setEmailList(emailList);
        setData(data);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
    };

    const refresh = () => {
        setVisible(false);
        form.resetFields();
        props.refresh();
    };

    const {runAsync:addBatchUserRun, loading:addBatchUserLoading} = useFetch(addBatchUser, {manual: true});



    const save = () => {
        // 使用map方法提取email属性
        let unEmail = data.map((item: any) => item.email);
        // 使用filter方法过滤出不存在于数组B中的元素
        const availableEmail = emailList.filter((item: any) => !unEmail.includes(item));
        addBatchUserRun({customerId},{ emailLanguage, emailList: availableEmail}).then(
            (result:any) => {
                let data = result.data
                if (data !== undefined && data !== null && data.length > 0) {
                    user_tip.current.show(data);
                    refresh();
                }
            }
        )

    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 8 },
        },

    }

    return (
        <React.Fragment>
            <Modal
                width={600}
                title={<FormattedMessage id={"common.tips"} />}
                visible={visible}
                onCancel={hide}
                // cancelText={formatMessage({id: 'common.back.revise'})}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                // okText={emailList.length !== data.length?formatMessage({id: 'common.ignore.continue'}):""}
                // okButtonProps={{loading: addBatchUserLoading}}
                // onOk={save}
                footer={
                    <Row style={{ paddingLeft: 24 }}>
                        <Col span={24} style={{ textAlign: 'right' }}>
                        <Button onClick={hide}>{formatMessage({ id: "common.back.revise" })}</Button>
                        {
                            emailList.length !== data.length?
                            <Button 
                                type={"primary"} 
                                onClick={save}
                                loading={addBatchUserLoading}
                            >
                                {formatMessage({ id: "common.ignore.continue" })}
                            </Button>:null
                        }
                      </Col>
                    </Row>
                }
            >
                <Row style={{ backgroundColor: "#165DFF1A", height: (emailList.length !== data.length && g.lang === "en")?54:32, marginBottom: 16, paddingLeft: 12, paddingBottom: 12 }}>
                    <Col style={{ width: 24 }}>
                        <svg className="iconfont" width={20} height={20} style={{ marginRight: 12, marginTop: 8 }} fill={"#165DFF"}>
                            <use xlinkHref="#icon-xinxitishi1" ></use>
                        </svg>
                    </Col>
                    <Col style={{ width: "calc(100% - 50px)", marginTop: 6 }}>
                        {
                            emailList.length === data.length?
                            formatMessage({ id: "user.batch.add.email.tip1" }):
                            formatMessage({ id: "user.batch.add.email.tip2" })
                        }
                    </Col>
                </Row>
                <Table
                    //style={{ marginTop: 16 }}
                    dataSource={data}
                    pagination={false}
                    scroll={{
                        y:
                            data.length < 20
                                ? "calc(100vh - 222px)"
                                : "calc(100vh - 262px)",
                    }}
                    rowKey={(record: any) => record.id}
                >
                    <Table.Column
                        title={
                            <FormattedMessage id="common.serial" />
                        }
                        dataIndex="#"
                        key="#"
                        width={60}
                        render={(text, record, index) =>
                            index + 1
                        }
                    />
                    <Table.Column
                        title={
                            <FormattedMessage id="common.email" />
                        }
                        dataIndex="email"
                        key="email"
                        ellipsis
                    />
                    <Table.Column
                        title={
                            <FormattedMessage id="common.reason" />
                        }
                        width={300}
                        dataIndex="reason"
                        key="reason"
                        ellipsis
                    />
                </Table>
            </Modal>
            <UserAddTip bind={user_tip} refresh={props.refresh}/>
        </React.Fragment>
    )
};