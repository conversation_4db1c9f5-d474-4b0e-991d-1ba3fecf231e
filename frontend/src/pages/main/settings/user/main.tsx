import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import {
    Badge,
    Button,
    Col,
    ConfigProvider,
    Empty,
    Input,
    message,
    Row,
    Spin,
    Table,
    Tooltip,
    Checkbox,
} from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import { UserAdd } from "./add_batch";
import { InviteAgain } from "./invite_again";
import { CustomersSearch } from "../../../common/customers-search";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import {
    batchCloseCustomerUser,
    closetCustomerUser,
    inviteAgainCustomerUser,
    updateUserCustomerAdmin,
} from "../../../../api/user";
import { useSafeState, useUpdateEffect } from "ahooks";
import { UserRoles } from "./roles";
import { listUsers, listUsersExport } from "../../../../api/customer";
import { permissions } from "../../../../tools/permission";
import { CustomConfirmModal } from "../../../../components/modal";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";
import AdminIcon from "images/admin.svg";
import {InsertDivider} from "../../../../components/divider";
import {useGlobal} from "../../../../context/global";
import {resendInviteEmail} from "../../../../api/projects";
import {SearchOutlined} from "@ant-design/icons";
import EmptyImg from 'images/empty.png';
import {useUser} from "./context";

export const Main = () => {
    const g = useGlobal();
    const auth = useAuth();
    const intl = useIntl();
    const { formatMessage } = intl;
    const page = usePage();
    const user = useUser();
    const [data, setData] = useSafeState<any>([]);
    const [refresh, setRefresh] = useSafeState(0);
    const [sysAdmin, setSysAdmin] = useSafeState(false);
    const [buttonLoading, setButtonLoading] = useSafeState<any>(false);
    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<React.Key[]>([]);

    const user_add: any = React.useRef();
    const user_roles: any = React.useRef();
    const user_inviteAgain: any = React.useRef();

    const { runAsync: listUsersRun, loading: listUsersLoading } = useFetch(
        listUsers,
        { manual: true }
    );
    const {
        runAsync: updateUserCustomerAdminRun,
        loading: updateUserCustomerAdminLoading,
    } = useFetch(updateUserCustomerAdmin, { manual: true });
    const {
        runAsync: closetCustomerUserRun,
        loading: closetCustomerUserLoading,
    } = useFetch(closetCustomerUser, { manual: true });

    const {runAsync: batchCloseCustomerUserRun, loading: batchCloseCustomerUserLoading} = useFetch(
        batchCloseCustomerUser, { manual: true }
    );

    const { runAsync: inviteAgainRun, loading: inviteAgainLoading } = useFetch(
        inviteAgainCustomerUser,
        { manual: true }
    );
    const {
        runAsync: resendInviteEmailRun,
        loading: resendInviteEmailLoading,
    } = useFetch(resendInviteEmail, { manual: true });
    const add = () => {
        user_add.current.show(auth.adminCustomerId);
    };
    const { runAsync: listUsersExportRun, loading: listUsersExportLoading } =
        useFetch(listUsersExport, { manual: true });

    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowIds: React.SetStateAction<React.Key[]>) => {
            setSelectedRowKeys(selectedRowIds);
        },
        selectedRowKeys: selectedRowKeys,
        getCheckboxProps: (record: any) => ({
            disabled: record.closeCustomer.includes(auth.adminCustomerId)
        }),
        preserveSelectedRowKeys: true,
    };

    const resetSelected = () => {
        setSelectedRowKeys([]);
    }

    const refreshList = () => {
        setRefresh(refresh + 1);
        resetSelected();
    };

    const search = (v: any) => {
        user.setKeyword(v);
        page.setCurrentPage(1);
        resetSelected();
    };

    const onSysAdminChange = (e: CheckboxChangeEvent) => {
        setSysAdmin(e.target.checked);
        resetSelected();
        if(e.target.checked){
            user.setKeyword("");
        }
    };

    const edit = (record: any, admin: any) => {
        let content: string;
        if (admin) {
            content = formatMessage({ id: "user.customer.admin.add" });
        } else {
            content = formatMessage({ id: "user.customer.admin.remove" });
        }
        CustomConfirmModal({
            title: formatMessage({ id: "common.tips" }),
            content: content,
            okText: formatMessage({ id: "common.ok" }),
            okButtonProps: { loading: updateUserCustomerAdminLoading },
            onOk: () =>
                updateUserCustomerAdminRun(
                    {
                        customerId: auth.adminCustomerId,
                        id: record.id,
                    },
                    { admin: admin }
                ).then((result: any) => {
                    setRefresh(refresh + 1);
                    message.success(result.msg).then(() => {});
                }),
        });
    };

    const close = (record: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.close" }),
            content: formatMessage({ id: "tip.user.close" }),
            okText: formatMessage({ id: "common.ok" }),
            okButtonProps: { loading: closetCustomerUserLoading },
            onOk: () =>
                closetCustomerUserRun({
                    customerId: auth.adminCustomerId,
                    userId: record.id,
                }).then((result: any) => {
                    setRefresh(refresh + 1);
                    setSelectedRowKeys([]);
                    message.success(result.msg).then(() => {});
                }),
        });
    };

    const batchClose = (record: any) => {
        if (selectedRowKeys.length === 0) {
            message.warn(intl.formatMessage({ id: "common.select.list.tips" }));
            return;
        }

        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.close" }),
            content: formatMessage({ id: "tip.user.close" }),
            okText: formatMessage({ id: "common.ok" }),
            okButtonProps: { loading: batchCloseCustomerUserLoading },
            onOk: () =>
                batchCloseCustomerUserRun({
                    customerId: auth.adminCustomerId,
                }, {ids: selectedRowKeys}).then((result: any) => {
                    setRefresh(refresh + 1);
                    setSelectedRowKeys([]);
                    message.success(result.msg).then(() => {});
                }),
        });
    };

    const inviteAgain = (record: any) => {
        user_inviteAgain.current.show(auth.adminCustomerId, record.id, 1);
        // inviteAgainRun({
        //     customerId: auth.adminCustomerId,
        //     userId: record.id,
        // }).then((result: any) => {
        //     setRefresh(refresh + 1);
        //     message.success(result.msg).then(() => {});
        // });
    };

    const resendInvite = (record: any) => {
        user_inviteAgain.current.show(auth.adminCustomerId, record.id, 2);
        // resendInviteEmailRun({
        //     customerId: auth.adminCustomerId,
        //     userId: record.id,
        // }).then((result: any) => {
        //     setRefresh(refresh + 1);
        //     message.success(result.msg).then(() => {});
        // });
    };

    function escapeRegexSpecialChars(str: any) {
        // 正则表达式特殊字符
        var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

        // 使用replace方法和函数作为第二个参数来转义每个匹配项
        return str.replace(specialChars, '\\$&');
    }

    const list = React.useCallback(
        () => {
            page.setTotal(0);
            setData([]);
            let key = user.keyword;
            if(user.keyword !== null && user.keyword !== undefined){
                key = user.keyword.trim();
            }
            if (auth.adminCustomerId) {
                listUsersRun({
                    sysAdmin,
                    customerId: auth.adminCustomerId,
                    keyword:key,
                    start: (page.currentPage - 1) * page.pageSize,
                    limit: page.pageSize,
                }).then((result: any) => {
                    page.setTotal(result.data.total);
                    setData(fillTableCellEmptyPlaceholder(result.data.items));
                });
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [
            sysAdmin,
            auth.adminCustomerId,
            user.keyword,
            page.currentPage,
            page.pageSize,
        ]
    );

    const leadingOut = () => {
        setButtonLoading(true);
        // let key = escapeRegexSpecialChars(user.keyword);
        let key = user.keyword;
        if(user.keyword !== null && user.keyword !== undefined){
            key = user.keyword.trim();
        }
        listUsersExportRun({
            sysAdmin,
            customerId: auth.adminCustomerId,
            key,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
        }).then(
            message.success(formatMessage({ id: "common.export.success" }))
        );
        setButtonLoading(false);
    };

    const operationBut = (value: any, record: any) => {
        let buts: any[] = [];
        if (auth.user.info.email === record.info.email) {
            return <span>-</span>;
        }
        if (
            permissions(auth.permissions, "operation.settings.users.cancel") &&
            value.admin
            && record.delete === false
            && record.status !== 2
        ) {
            buts.push(
                <Button
                    style={{ padding: 0 }}
                    size="small"
                    type="link"
                    onClick={() => edit(record, false)}
                >
                    <FormattedMessage id="common.cancel" />
                </Button>
            );
        } else if (
            permissions(auth.permissions, "operation.settings.users.setting") &&
            !value.admin
            && record.delete === false
            && record.status !== 2
        ) {
            buts.push(
                <Button
                    style={{ padding: 0 }}
                    size="small"
                    type="link"
                    onClick={() => edit(record, true)}
                >
                    <FormattedMessage id="common.setting" />
                </Button>
            );
        }
        //开启状态
        if (!record.closeCustomer.includes(auth.adminCustomerId)) {
            if (
                permissions(auth.permissions, "operation.settings.users.close")
            ) {
                buts.push(
                    <Button
                        style={{ padding: 0 }}
                        size="small"
                        type="link"
                        onClick={() => close(record)}
                    >
                        <FormattedMessage id="common.close" />
                    </Button>
                );
            }
            if (
                permissions(
                    auth.permissions,
                    "operation.settings.users.invite-again"
                ) &&
                record.status === 0
            ) {
                buts.push(
                    <Button
                        loading={resendInviteEmailLoading}
                        style={{ padding: 0 }}
                        size="small"
                        type="link"
                        onClick={() => resendInvite(record)}
                    >
                        <FormattedMessage id="user.settings.invite-again" />
                    </Button>
                );
            }
            //关闭状态
        } else if (
            permissions(
                auth.permissions,
                "operation.settings.users.invite-again"
            ) 
            && record.status !== 2
            && record.delete === false
        ) {
            buts.push(
                <Button
                    loading={inviteAgainLoading}
                    style={{ padding: 0 }}
                    size="small"
                    type="link"
                    onClick={() => inviteAgain(record)}
                >
                    <FormattedMessage id="user.settings.invite-again" />
                </Button>
            );
        }

        // if (permissions(auth.permissions, "operation.settings.users.close")){
        //     if (!record.closeCustomer.includes(auth.adminCustomerId)){
        //         buts.push( <Button style={{padding:0}} size="small" type="link"
        //                            onClick={() => close(record)}><FormattedMessage
        //             id="common.close"/></Button>)
        //     }else if(permissions(auth.permissions, "operation.settings.users.invite-again")){
        //         buts.push( <Button loading={inviteAgainLoading} style={{padding:0}} size="small" type="link"
        //                            onClick={() => inviteAgain(record)}><FormattedMessage
        //             id="user.settings.invite-again"/></Button>)
        //     }
        // }
        // if (permissions(auth.permissions, "operation.settings.users.invite-again") && record.status === 0){
        //     buts.push( <Button loading={resendInviteEmailLoading} style={{padding:0}} size="small" type="link"
        //                        onClick={() => resendInvite(record)}><FormattedMessage
        //         id="user.settings.invite-again"/></Button>)
        // }
        if (buts.length === 0) {
            return <span>-</span>;
        }
        return InsertDivider(buts);
    };

    function renderStatus(record: any) {
        return record.closeCustomer.includes(auth.adminCustomerId) ? (
            <div>
                <Badge color={"#C0C4CC"} style={{ marginRight: "8px" }} />
                {formatMessage({ id: "user.status.close" })}
            </div>
        ) : record.status === 0 ? (
            <div>
                <Badge color={"#FFAE00"} style={{ marginRight: "8px" }} />
                {formatMessage({ id: "user.status.open" })}
            </div>
        ) : (
            <div>
                <Badge color={"#41CC82"} style={{ marginRight: "8px" }} />
                {formatMessage({ id: "user.status.active" })}
            </div>
        );
    }
    
    React.useEffect(list, [
        sysAdmin,
        user.keyword,
        refresh,
        page.currentPage,
        page.pageSize,
    ]);
    useUpdateEffect(() => {
        page.setCurrentPage(1);
        setRefresh(refresh + 1);
    }, [auth.adminCustomerId]);
    return (
        <React.Fragment>
            {permissions(auth.permissions, "operation.settings.users.view") && (
                <>
                    <Row justify="space-between">
                        <Row>
                            <div
                                style={{
                                    display: "flex",
                                    alignItems: "center",
                                }}
                            >
                                <svg
                                    className="iconfont"
                                    width={16}
                                    height={16}
                                >
                                    <use xlinkHref="#icon-quanbuyonghu"></use>
                                </svg>
                                <span
                                    style={{
                                        color: "#1D2129",
                                        fontWeight: 600,
                                        paddingLeft: "8px",
                                    }}
                                >
                                    {formatMessage({ id: "user.all" })}
                                </span>
                            </div>
                        </Row>
                    </Row>
                    <Row justify="space-between" style={{ marginTop: "16px" }}>
                        <Col className="mar-rgt-12">
                            <Row gutter={16}>
                                <Col style={{ minWidth: 230 }}>
                                    {/* <Customers projectAdmin={false} customerAdmin={true} systemAdministrator={sysAdmin} /> */}
                                    <CustomersSearch
                                        projectAdmin={false}
                                        customerAdmin={true}
                                        systemAdministrator={sysAdmin}
                                        onResetSelected={resetSelected}
                                    />
                                </Col>
                                <Col>
                                    <Input
                                        placeholder={formatMessage({
                                            id: "common.required.prefix.keyword",
                                        })}
                                        allowClear
                                        value={user.keyword}
                                        suffix={
                                            <SearchOutlined
                                                style={{ color: "#BFBFBF" }}
                                            />
                                        }
                                        onChange={(e) => search(e.target.value)}
                                        disabled={!auth.adminCustomerId}
                                    />
                                </Col>
                                <Col style={{ marginTop: "7px" }}>
                                    <Checkbox onChange={onSysAdminChange}>
                                        <FormattedMessage id="operation.settings.users.system.administrator" />
                                    </Checkbox>
                                </Col>
                            </Row>
                        </Col>
                        <Row justify="end">
                            <Col className="mar-rgt-12">
                                {permissions(
                                    auth.permissions,
                                    "operation.settings.users.export"
                                ) ? (
                                    <Button
                                        onClick={() => leadingOut()}
                                        loading={buttonLoading}
                                    >
                                        <FormattedMessage id="operation.settings.users.export" />
                                    </Button>
                                ) : null}
                            </Col>
                            <Col className="mar-rgt-12">
                                {permissions(
                                    auth.permissions,
                                    "operation.settings.users.close"
                                ) ? (
                                    <Button
                                        onClick={batchClose}
                                    >
                                        <FormattedMessage id="common.close.batch" />
                                    </Button>
                                ) : null}
                            </Col>
                            <Col className="mar-rgt-12">
                                {permissions(
                                    auth.permissions,
                                    "operation.settings.users.add"
                                )  && !sysAdmin ? (
                                    <Button
                                        disabled={!auth.adminCustomerId}
                                        type="primary"
                                        onClick={add}
                                    >
                                        <FormattedMessage id="common.add" />
                                    </Button>
                                ) : null}
                            </Col>
                        </Row>
                    </Row>
                    <Spin spinning={listUsersLoading}>
                        <ConfigProvider
                            renderEmpty={() => {
                                return (
                                    <Empty
                                        image={
                                            <img
                                                src={EmptyImg}
                                                style={{
                                                    width: 300,
                                                    height: 213,
                                                }}
                                            />
                                        }
                                        imageStyle={{
                                            height: 240,
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                    />
                                );
                            }}
                        >
                            <Table
                                //style={{ marginTop: 16 }}
                                dataSource={data}
                                pagination={false}
                                scroll={{
                                    y:
                                        page.total < 20
                                            ? "calc(100vh - 222px)"
                                            : "calc(100vh - 262px)",
                                }}
                                rowKey={(record: any) => record.id}
                                rowSelection={rowSelection}
                            >
                                <Table.Column
                                    title={
                                        <FormattedMessage id="common.serial" />
                                    }
                                    dataIndex="#"
                                    key="#"
                                    width={60}
                                    render={(text, record, index) =>
                                        index +
                                        1 +
                                        (page.currentPage - 1) * page.pageSize
                                    }
                                />
                                <Table.Column
                                    title={
                                        <FormattedMessage id="common.email" />
                                    }
                                    dataIndex={["info", "email"]}
                                    key="email"
                                    ellipsis
                                />
                                <Table.Column
                                    title={
                                        <FormattedMessage id="common.full_name" />
                                    }
                                    dataIndex={["info", "name"]}
                                    key="name"
                                    ellipsis
                                />
                                <Table.Column
                                    title={
                                        <FormattedMessage id="common.phone" />
                                    }
                                    dataIndex={["info", "phone"]}
                                    key="phone"
                                    ellipsis
                                />
                                <Table.Column
                                    title={
                                        <FormattedMessage id="common.company" />
                                    }
                                    dataIndex={["info", "company"]}
                                    key="company"
                                    ellipsis
                                />
                                <Table.Column
                                    title={
                                        <FormattedMessage id="common.description" />
                                    }
                                    dataIndex={["info", "description"]}
                                    key="description"
                                    ellipsis
                                />
                                {!sysAdmin ? (
                                    <Table.Column
                                        title={
                                            <FormattedMessage id="common.role" />
                                        }
                                        dataIndex="roles"
                                        key="roles"
                                        width={80}
                                        render={(value, record: any) => (
                                            <div>
                                                <span
                                                    className="mouse link-btn"
                                                    onClick={() => {
                                                        if (
                                                            permissions(
                                                                auth.permissions,
                                                                "operation.settings.users.edit"
                                                            )
                                                        ) {
                                                            user_roles.current.show(
                                                                record
                                                            );
                                                        }
                                                    }}
                                                >
                                                    {record.roles ? (
                                                        record.roles.length
                                                    ) : (
                                                        0
                                                    )}
                                                </span>
                                                {record.admin ?(
                                                    <span>
                                                    &nbsp;
                                                        <img
                                                            src={
                                                                AdminIcon
                                                            }
                                                        />
                                                </span>
                                                ): null
                                                }

                                            </div>
                                        )}
                                    />
                                ) : (
                                    <Table.Column
                                        title={
                                            <FormattedMessage id="common.role" />
                                        }
                                        dataIndex="roles"
                                        key="roles"
                                        render={(value, record: any) => (
                                            <span>{"Sys-Admin"}</span>
                                        )}
                                    />
                                )}
                                {/* <Table.Column
                                    title={<FormattedMessage id="common.role"/>} dataIndex="roles" key="roles"
                                    width={80}
                                    render={
                                        (value, record: any) => (
                                            <div>
                                                <span
                                                    className="mouse link-btn"
                                                    onClick={() => {
                                                        if (permissions(auth.permissions, "operation.settings.users.edit")) {
                                                            user_roles.current.show(record)
                                                        }
                                                    }}
                                                >
                                                    {record.roles ? record.admin ? <span>{record.roles.length}&nbsp;<img src={AdminIcon} /></span> : record.roles.length : 0}
                                                </span>
                                            </div>
                                        )
                                    }
                                /> */}
                                <Table.Column
                                    width={120}
                                    title={
                                        <>
                                            <FormattedMessage id="common.status" />
                                            <Tooltip
                                                trigger={["hover", "click"]}
                                                overlayInnerStyle={{
                                                    width: 310,
                                                    fontSize: 12,
                                                    background: "#646566",
                                                }}
                                                placement="top"
                                                title={
                                                    <>
                                                        <Row>
                                                            <FormattedMessage id="customer.user.status.Enabled" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="customer.user.status.Activated" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="customer.user.status.Closed" />
                                                        </Row>
                                                    </>
                                                }
                                            >
                                                <svg
                                                    className="iconfont mouse"
                                                    width={12}
                                                    height={12}
                                                    style={{ marginLeft: 4 }}
                                                >
                                                    <use xlinkHref="#icon-xinxitishi" />
                                                </svg>
                                            </Tooltip>
                                        </>
                                    }
                                    render={(value, record: any) =>
                                        renderStatus(record)
                                    }
                                />
                                {!sysAdmin ? (
                                    <Table.Column
                                        width={g.lang === "zh" ? 200 : 230}
                                        title={
                                            <FormattedMessage id="common.operation" />
                                        }
                                        render={(value, record: any) =>
                                            operationBut(value, record)
                                        }
                                    />
                                ) : (
                                    <Table.Column
                                        width={g.lang === "zh" ? 200 : 230}
                                        title={
                                            <FormattedMessage id="common.operation" />
                                        }
                                        render={(value, record: any) => (
                                            <span>{"-"}</span>
                                        )}
                                    />
                                )}
                                {/* //<Table.Column
                                //     width={g.lang === "zh"?200:230}
                                //     title={<FormattedMessage id="common.operation"/>}
                                //     render={
                                //         (value, record: any) => (operationBut(value,record))
                                //     }
                                // /> */}
                            </Table>
                            {page.total > 20 && <PaginationView mode="SELECTABLE" selectedNumber={selectedRowKeys?.length} clearDisplay={true} refresh={refreshList}  />}
                        </ConfigProvider>
                    </Spin>
                </>
            )}
            <UserAdd bind={user_add} refresh={list} />
            <UserRoles bind={user_roles} refresh={list} />
            <InviteAgain bind={user_inviteAgain} refresh={list} />
        </React.Fragment>
    );
};
