import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {But<PERSON>, Col, ConfigProvider, Empty, Input, message, Row, Spin, Table} from "antd";
import {StorehouseAdd} from "./add";
import {useFetch} from "../../../../hooks/request";
import {delStorehouses, storehouses} from "../../../../api/storehouses";
import {useSafeState} from "ahooks";
import {PaginationView} from "../../../../components/pagination";
import {usePage} from "../../../../context/page";
import {permissions} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import {CustomConfirmModal} from "components/modal";
import {SearchOutlined} from "@ant-design/icons";
import { useDepots } from "./context";

import EmptyImg from 'images/empty.png';
import {AuthButton} from "../../../common/auth-wrap";

export const Main = () => {
    const auth = useAuth()
    const intl = useIntl();
    const depot = useDepots();
    const {formatMessage} = intl;
    const page = usePage()

    const {runAsync: getStorehousesRun, loading: getStorehousesLoading} = useFetch(storehouses, {
        manual: true,
        debounceWait: 500
    })
    const {runAsync: delStorehousesRun, loading: delStorehousesLoading} = useFetch(delStorehouses, {manual: true})

    const add = () => {
        site_add.current.show();
    };

    const edit = (data: any) => {
        site_add.current.show(data);
    };

    const remove = (id: any) => {
        CustomConfirmModal({
            title: formatMessage({id: 'common.tips'}),
            content: formatMessage({id: 'common.confirm.delete'}),
            okText:formatMessage({ id: 'common.ok' }),
            onOk: () => delStorehousesRun({id}).then((resp: any) => {
                message.success(resp.msg);
                list()
            })
        });
    };

    const search = (v: any) => {
        depot.setKeyword(v);
        page.setCurrentPage(1);
    };

    function escapeRegexSpecialChars(str: any) {
        // 正则表达式特殊字符
        var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

        // 使用replace方法和函数作为第二个参数来转义每个匹配项
        return str.replace(specialChars, '\\$&');
    }

    const list = React.useCallback(
        () => {
            // let key = escapeRegexSpecialChars(depot.keyword);
            let key = depot.keyword;
            if(depot.keyword !== null && depot.keyword !== undefined){
                key = depot.keyword.trim();
            }
            getStorehousesRun({
                keyword: key.trim(),
                start: (page.currentPage - 1) * page.pageSize,
                limit: page.pageSize
            }).then(
                (result: any) => {
                    page.setTotal(result.data.total);
                    page.setData(result.data.items);
                }
            )
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [depot.keyword, page.currentPage, page.pageSize]
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(list, [depot.keyword, page.currentPage, page.pageSize]);

    const site_add: any = React.useRef();

    return (
        <React.Fragment>
            {
                permissions(auth.permissions, "operation.settings.storehouse.view") &&
                <>
                        <Row justify="space-between">
                            <Col >
                                <Input
                                    placeholder={formatMessage({ id: 'common.required.prefix.name' })}
                                    allowClear
                                    value={depot.keyword}
                                    suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                    onChange={e => search(e.target.value)}
                                    onPressEnter={(e: any) => search(e.target.value)}
                                />
                            </Col>
                            {
                                permissions(auth.permissions, "operation.settings.storehouse.add") &&
                                <Col className="text-right">
                                    <Button type="primary" onClick={add}><FormattedMessage id="common.add"/></Button>
                                </Col>
                            }
                        </Row>
                        <Spin spinning={getStorehousesLoading}>
                            <ConfigProvider
                                renderEmpty={
                                    () => {
                                        return <Empty
                                            image={<img src={EmptyImg} style={{width: 300, height: 213}}/>}
                                            imageStyle={{
                                                height: 240,
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center"
                                            }}
                                        />
                                    }
                                }
                            >
                            <Table
                                size="small"
                                style={{marginTop:16}}
                                dataSource={page.data}
                                scroll={{y: 'calc(100vh - 230px)'}}
                                pagination={false}
                                rowKey={(record: any) => (record.id)}
                            >
                                <Table.Column title={<FormattedMessage id="common.number"/>} dataIndex="number" key="number"
                                              ellipsis/>
                                <Table.Column title={<FormattedMessage id="common.name"/>} dataIndex="name" key="name"
                                              ellipsis/>
                                <Table.Column
                                    title={<FormattedMessage id="common.operation"/>}
                                    width={120}
                                    render={
                                        (value, record: any) => (
                                            <>
                                                <AuthButton
                                                    show={permissions(auth.permissions, "operation.settings.storehouse.edit")}
                                                    style={{paddingLeft:0}} size="small" type="link"
                                                    onClick={() => edit(record)}
                                                >
                                                    <FormattedMessage id="common.edit"/>
                                                </AuthButton>
                                                <AuthButton
                                                    show={permissions(auth.permissions, "operation.settings.storehouse.delete")}
                                                    loading={delStorehousesLoading} size="small" type="link"
                                                    onClick={() => remove(record.id)}
                                                >
                                                    <FormattedMessage id="common.delete"/>
                                                </AuthButton>

                                            </>)
                                    }
                                />
                            </Table>
                            </ConfigProvider>
                        </Spin>
                        <PaginationView/>
                </>
            }
            <StorehouseAdd bind={site_add} refresh={list}/>
        </React.Fragment>

    );
}