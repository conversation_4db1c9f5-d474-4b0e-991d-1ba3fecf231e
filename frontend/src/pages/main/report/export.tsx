import {
    Button,
    Checkbox,
    Col,
    DatePicker,
    Form,
    Input,
    message,
    Modal,
    Popover,
    Radio,
    Row,
    Select,
    Space,
    Tabs,
    Typography,
} from "antd";
import useSafeState from "ahooks/lib/useSafeState";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useFetch } from "../../../hooks/request";
import { useMain } from "../context";
import { useReport } from "./context";
import styled from "@emotion/styled";
import {
    exportConfigurePdf,
    exportConfigureReport,
    exportReport,
    exportSimulateRandomPdf,
    exportSimulateRandomReport,
    exportSubjectPdf,
    exportSubjectReport,
    getTemplates
} from "../../../api/report";
import { EyeInvisibleOutlined, EyeTwoTone, SearchOutlined, } from "@ant-design/icons";
import { exportAuthority, getRandomizationSimulation, userSites as getUserSites, getUserSitesAndStorehouses, sitesAndStorehouses } from "../../../api/project_site";
import { userStoreHouses } from "../../../api/project_storehouse";
import { getProjectAttributeList, getRandomListMany, } from "../../../api/randomization";
import { getSubjectInfo } from "../../../api/subject";
import { useGlobal } from "../../../context/global";
import type { DatePickerProps, RangePickerProps } from "antd/es/date-picker";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import { roleIsBind } from "../../../api/roles";
import { getOrderSubject } from "../../../api/order";
import moment from "moment";
import { findProject } from "../../../api/projects";
import { getPermission } from "../../../api/projects_roles";
import { inRandomIsolation } from "../../../utils/in_random_isolation";

import dayjs from "dayjs";

export const Export = (props: any) => {
    const g = useGlobal();
    const report = useReport();
    const [projectCreatedAt, setProjectCreatedAt] = useSafeState<number>(0);
    // moment.locale(g.lang==="zh"?"zh_CN":"en_US");
    const RangePicker: any = DatePicker.RangePicker
    const [visible, setVisible] = useSafeState<boolean>(false);
    const [type, setType] = useSafeState<number>();
    const [title, setTitle] = useSafeState<string>("");
    const intl = useIntl();
    const [roles, setRoles] = useSafeState<any[]>([]);
    const main = useMain();
    const [previewFields, setPreviewFields] = useSafeState<any[]>([]);
    const [defaultFields, setDefaultFields] = useSafeState<any[]>([]);
    const [form] = Form.useForm();
    const [customTemplates, setCustomTemplates] = useSafeState<any[]>([]);

    const [sites, setSites] = useSafeState([]);
    const [subjects, setSubjects] = useSafeState([]);
    const [userSites, setUserSites] = useSafeState<any[]>([]);
    const [userDepots, setUserDepots] = useSafeState<any[]>([]);
    const [institutes, setInstitutes] = useSafeState<any[]>([]);
    const [projectRoles, setProjectRoles] = useSafeState<any[]>([]);

    const [inspectionType, setInspectionType] = useSafeState<any[]>(["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]);

    const [userSitesCheckKeys, setUserSitesCheckKeys] = useSafeState<string[]>([]);
    const [projectRoleCheckKeys, setProjectRoleCheckKeys] = useSafeState<string[]>([]);
    const [userSiteSearchKey, setUserSiteSearchKey] = useSafeState<string>("");
    const [projectRoleSearchKey, setProjectRoleSearchKey] = useSafeState<string>("");
    const [storehouses, setStorehouses] = useSafeState<any[]>([]);
    const [tickPassword, setTickPassword] = useSafeState<boolean>(false);
    const [isBindRoleF, setIsBlindRoleF] = useSafeState<boolean>(false);
    const [record, setRecord] = React.useState<any>();
    const [startDate, setStartDate] = useState<any>(null);
    const [endDate, setEndDate] = useState<any>(null);
    const { formatMessage } = intl;
    // const zipTypeList: SelectProps['options'] = [];
    const [zipTypeList, setZipTypeList] = useSafeState<any[]>([]);
    const [randomizationSimulationNameList, setRandomizationSimulationNameList] = useSafeState<any[]>([]);


    const [subjectPdfLoading, setSubjectPdfLoading] = useState<any>(false);
    const [configPdfLoading, setConfigPdfLoading] = useState<any>(false);
    const [simulateRandomPdfLoading, setSimulateRandomPdfLoading] = useState<any>(false);

    const { runAsync: runUserSites } = useFetch(getUserSites, { manual: true });
    const { runAsync: runUserDepots } = useFetch(userStoreHouses, { manual: true });
    const { runAsync: runProjectRoles } = useFetch(getPermission, { manual: true });
    const { runAsync: runExportAuthority } = useFetch(exportAuthority, { manual: true, });
    const { runAsync: runGetRandomizationSimulation } = useFetch(getRandomizationSimulation, { manual: true });
    const { runAsync: runGetTemplates } = useFetch(getTemplates, { manual: true, });
    const { runAsync: runStorehouses } = useFetch(userStoreHouses, { manual: true, });
    const { runAsync: run_getOrderSubject } = useFetch(getOrderSubject, { manual: true })
    const { runAsync: run_getProjectUserSites } = useFetch(getUserSitesAndStorehouses, { manual: true });
    const { runAsync: run_sitesAndStorehouses } = useFetch(sitesAndStorehouses, { manual: true, });
    const { runAsync: runGetProjectAttributeList } = useFetch(getProjectAttributeList, { manual: true });
    const { runAsync: runExportReport, loading: exportReportLoading } = useFetch(exportReport, { manual: true });

    const { runAsync: runExportConfigureReport, loading: exportConfigureReportLoading } = useFetch(exportConfigureReport, { manual: true });

    const { runAsync: runExportConfigurePdf, loading: exportConfigurePdfLoading } = useFetch(exportConfigurePdf, { manual: true });


    const { runAsync: runExportSubjectPdf, loading: exportSubjectPdfLoading } = useFetch(exportSubjectPdf, { manual: true });

    const { runAsync: runExportSubjectReport, loading: exportSubjectReportLoading } = useFetch(exportSubjectReport, { manual: true });

    const { runAsync: runExportSimulateRandomPdf, loading: exportSimulateRandomPdfLoading } = useFetch(exportSimulateRandomPdf, { manual: true });

    const { runAsync: runExportSimulateRandomReport, loading: exportSimulateRandomReportLoading } = useFetch(exportSimulateRandomReport, { manual: true });

    const handleChange = (value: string[]) => {
        form.setFieldValue("zipType", value);
        if (value.length > 0) {
            setInspectionType(value);
        } else {
            setInspectionType(["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]);
        }

    };

    const nameChange = (value: string[]) => {
        form.setFieldValue("randomizationSimulationName", value);
    };


    const onSearch = (value: string) => { };

    const onStartChangeDate: DatePickerProps["onChange"] = (
        date,
        dateString
    ) => {
        setStartDate(new Date(dateString).getTime());
    };

    const onEndChangeDate: DatePickerProps["onChange"] = (date, dateString) => {
        setEndDate(new Date(dateString).getTime());
    };

    const range = (start: any, end: any) => {
        let result: any = [];
        for (let i = start; i < end; i++) {
            result.push(i);
        }
        return result;
    };

    const rangeArr = (start: any, end: any, st: any, en: any) => {
        let result: any = [];
        for (let i = start; i < end; i++) {
            result.push(i);
        }
        for (let i = st; i < en; i++) {
            if (!result.includes(i)) {
                result.push(i);
            }
        }
        return result;
    };

    const onStartOkDate = (
        value: DatePickerProps["value"] | RangePickerProps["value"]
    ) => {
        setStartDate(value);
    };

    const onEndOkDate = (
        value: DatePickerProps["value"] | RangePickerProps["value"]
    ) => {
        setEndDate(value);
        // form.setFieldValue("selectTime", value);
    };

    const onChangePassword = (e: CheckboxChangeEvent) => {
        setTickPassword(e.target.checked);
        form.setFieldValue("selectPassword", e.target.checked);
    };

    const rangeConfig = () => {
        if (g.lang === "zh") {
            return {
                此刻: [moment(), moment()],
            };
        } else {
            return {
                Now: [moment(), moment()],
            };
        }
    };

    const ranges = rangeConfig();

    const disabledStartDate = (current: any) => {
        // 获取今天的日期
        const today = moment().startOf("day");
        // 将当前日期转换为 moment 对象
        const currentDate = moment(current).startOf("day");

        const date = moment(projectCreatedAt * 1000).startOf("day");

        if (endDate === null) {
            // 如果当前日期在今天之后，禁用该日期
            return currentDate.isAfter(today) || currentDate.isBefore(date);
        } else {
            return (
                currentDate.isAfter(moment(endDate).startOf("day")) ||
                currentDate.isAfter(today) ||
                currentDate.isBefore(date)
            );
        }
    };

    const disabledEndDate = (current: any) => {
        // 获取今天的日期
        const today = moment().startOf("day");
        // 将当前日期转换为 moment 对象
        const currentDate = moment(current).startOf("day");

        const date = moment(projectCreatedAt * 1000).startOf("day");

        if (startDate === null) {
            // 如果当前日期在今天之后，禁用该日期
            return currentDate.isAfter(today) || currentDate.isBefore(date);
        } else {
            return (
                currentDate.isBefore(moment(startDate).startOf("day")) ||
                currentDate.isAfter(today) ||
                currentDate.isBefore(date)
            );
        }
    };

    const disabledStartTime = (current: any) => {

        // 获取今天的日期
        const today = moment().startOf("day");
        // 将当前日期转换为 moment 对象
        const currentDate = moment(current).startOf("day");
        const currentHour = moment(current).hour();
        const currentMinute = moment(current).minutes();

        const date = moment(projectCreatedAt * 1000).startOf("day");
        //获取创建日期的小时
        const createdHour = moment(projectCreatedAt * 1000).hour();
        //获取创建日期的分钟
        const createdMinutes = moment(projectCreatedAt * 1000).minutes();
        return {
            disabledHours: () => {
                if (currentDate.isSame(today)) {
                    if (endDate !== null && currentDate.isSame(moment(endDate).startOf("day"))) {
                        return rangeArr(
                            currentHour + 1,
                            24,
                            moment(endDate).hour() + 1,
                            60
                        );
                    } else {
                        return range(currentHour + 1, 24);
                    }
                }
                if (currentDate.isSame(date)) {
                    if (endDate !== null && currentDate.isSame(moment(endDate).startOf("day"))) {
                        return rangeArr(
                            0,
                            createdHour,
                            moment(endDate).hour() + 1,
                            60
                        );
                    } else {
                        return range(0, createdHour);
                    }
                }
                if (endDate !== null && currentDate.isSame(moment(endDate).startOf("day"))) {
                    return range(moment(endDate).hour() + 1, 60);
                } else {
                    return [];
                }
            },
            disabledMinutes: (hour: any) => {
                if ((currentDate.isSame(today)) && (hour === currentHour)) {
                    if (endDate !== null && currentDate.isSame(moment(endDate).startOf("day")) && currentHour === moment(endDate).hour()) {
                        return rangeArr(
                            currentMinute + 1,
                            60,
                            moment(endDate).minutes() + 1,
                            60
                        );
                    } else {
                        return range(currentMinute + 1, 60);
                    }
                }
                if ((currentDate.isSame(date)) && (hour === createdHour)) {
                    if (endDate !== null && currentDate.isSame(moment(endDate).startOf("day")) && currentHour === moment(endDate).hour()) {
                        return rangeArr(
                            0,
                            createdMinutes,
                            moment(endDate).minutes() + 1,
                            60
                        );
                    } else {
                        return range(0, createdMinutes);
                    }
                }
                if (endDate !== null && currentDate.isSame(moment(endDate).startOf("day")) && currentHour === moment(endDate).hour()) {
                    return range(moment(endDate).minutes() + 1, 60);
                } else {
                    return [];
                }
            },
        };
    };

    const disabledEndTime = (current: any) => {

        // 获取今天的日期
        const today = moment().startOf("day");
        // 将当前日期转换为 moment 对象
        const currentDate = moment(current).startOf("day");
        const currentHour = moment(current).hour();
        const currentMinute = moment(current).minutes();

        const date = moment(projectCreatedAt * 1000).startOf("day");
        //获取创建日期的小时
        const createdHour = moment(projectCreatedAt * 1000).hour();
        //获取创建日期的分钟
        const createdMinutes = moment(projectCreatedAt * 1000).minutes();
        return {
            disabledHours: () => {
                if (currentDate.isSame(today)) {
                    if (startDate !== null && currentDate.isSame(moment(startDate).startOf("day"))) {
                        return rangeArr(
                            currentHour + 1,
                            24,
                            0,
                            moment(startDate).hour()
                        );
                    } else {
                        return range(currentHour + 1, 24);
                    }
                }
                if (currentDate.isSame(date)) {
                    if (startDate !== null && currentDate.isSame(moment(startDate).startOf("day"))) {
                        return rangeArr(
                            0,
                            createdHour,
                            0,
                            moment(startDate).hour()
                        );
                    } else {
                        return range(0, createdHour);
                    }
                }
                if (startDate !== null && currentDate.isSame(moment(startDate).startOf("day"))) {
                    return range(0, moment(startDate).hour());
                } else {
                    return [];
                }
            },
            disabledMinutes: (hour: any) => {
                if ((currentDate.isSame(today)) && (hour === currentHour)) {
                    if (startDate !== null && currentDate.isSame(moment(startDate).startOf("day")) && currentHour === moment(startDate).hour()) {
                        return rangeArr(
                            currentMinute + 1,
                            60,
                            0,
                            moment(startDate).minutes()
                        );
                    } else {
                        return range(currentMinute + 1, 60);
                    }
                }
                if ((currentDate.isSame(date)) && (hour === createdHour)) {
                    if (startDate !== null && currentDate.isSame(moment(startDate).startOf("day")) && currentHour === moment(startDate).hour()) {
                        return rangeArr(
                            0,
                            createdMinutes,
                            0,
                            moment(startDate).minutes()
                        );
                    } else {
                        return range(0, createdMinutes);
                    }
                }
                if (startDate !== null && currentDate.isSame(moment(startDate).startOf("day")) && currentHour === moment(startDate).hour()) {
                    return range(0, moment(startDate).minutes());
                } else {
                    return [];
                }
            },
        };
    };

    const show = (record: any) => {
        let {
            type,
            name,
            defaultFields: fields,
            multiDefaultFields,
            customizable,
            templateId,
            roles: rolesData,
            isOpenPackage,
            template,
        } = record;
        setRecord(record);
        run_isBlindRole({
            roleId: rolesData[0].role_id,
        }).then((result: any) => {
            const data = result.data;
            setIsBlindRoleF(data);
            if (main.project.info.type === 1) {
                fields = fields?.filter(
                    (field: any) =>
                        field.key !== "report.attributes.random.stage" &&
                        field.key !== "report.attributes.random.cohort"
                );
            } else if (main.project.info.type === 2) {
                fields = fields?.filter(
                    (field: any) =>
                        field.key !== "report.attributes.random.stage"
                );
            } else if (main.project.info.type === 3) {
                fields = fields?.filter(
                    (field: any) =>
                        field.key !== "report.attributes.random.cohort"
                );
            }

            if (
                (main.project.info.type === 2 ||
                    main.project.info.type === 3) &&
                type === 18 &&
                !data
            ) {
                //库房单品报表，如何项目是非盲项目，去掉字段
                fields = fields?.filter(
                    (field: any) =>
                        field.key !== "report.attributes.random.stage" &&
                        field.key !== "report.attributes.random.cohort"
                );
            }

            if ((type === 18 || type === 19) && !isOpenPackage) {
                fields = fields?.filter(
                    (field: any) =>
                        field.key !== "report.attributes.research.packageNumber"
                );
            }

            if (type === 11 && !isOpenPackage) {
                fields = fields?.filter(
                    (field: any) =>
                        field.key !== "report.attributes.research.packageNumber" &&
                        field.key !== "report.attributes.research.package.serialNumber"
                );
            }

            if (type === 16 || type === 15 || type === 14 || type === 27 || type === 2) {
                runGetProjectAttributeList({ envId: main.env.id }).then(
                    (res: any) => {
                        report.setAttrs(res?.data);
                        const showRandomNumber = res?.data?.find(
                            (it: any) => it.info.isRandomNumber === true
                        );
                        report.setShowRandomNumber(showRandomNumber);
                        const showRegisterGroup = res?.data?.find(
                            (it: any) => it.info.allowRegisterGroup === true
                        );
                        report.setShowRegisterGroup(showRegisterGroup);
                    }
                );
            }
            if (customizable) {
                runGetTemplates({
                    projectId: main.project.id,
                    envId: main.env.id,
                    type,
                }).then((res: any) => {
                    if (res.data) {
                        setCustomTemplates(res.data);
                    }
                });
            }
            if (templateId) {
                form.setFieldValue("template", templateId);
            }

            setType(type);
            report.setType(type);
            setTitle(formatMessage({ id: name }));

            if (template) {
                if (
                    (main.project.info.type === 2 ||
                        main.project.info.type === 3) &&
                    type === 18 &&
                    !data
                ) {
                    //库房单品报表，如何项目是非盲项目，去掉字段
                    template = template?.filter(
                        (field: any) =>
                            field.key !== "report.attributes.random.stage" &&
                            field.key !== "report.attributes.random.cohort"
                    );
                }
                setPreviewFields(template);
            } else {
                if (multiDefaultFields && multiDefaultFields.length > 0) {
                    let projectInfoType = main.project.info.type;
                    if (type === 25) {
                        projectInfoType = 1
                    }
                    let fields = multiDefaultFields.find(
                        (item: any) => item.type === projectInfoType
                    )?.defaultFields;
                    setPreviewFields(fields);
                    setDefaultFields(fields?.map((field: any) => field.key));
                } else {
                    setPreviewFields(fields);
                    setDefaultFields(fields?.map((field: any) => field.key));
                }
            }

            setRoles(rolesData);
            form.setFieldValue("role", rolesData[0].role_id);

            // 多个sheet
            if (type === 1 || type === 2 || type === 4 || type === 18 || type === 19 || type === 14 || type === 15 || type === 25) {
                let projectInfoType = main.project.info.type;
                if (type === 25) {
                    projectInfoType = 1
                }
                if (multiDefaultFields !== undefined && multiDefaultFields !== null) {
                    let typeKey: any = [];
                    multiDefaultFields
                        .filter((item: any) => item.type === projectInfoType)
                        .map((it: any) => {
                            typeKey.push(it.defaultFields);
                        });
                    report.setPreviewFieldsMany(typeKey);
                }
            }

            setVisible(true);
        });
    };

    const hide = () => {
        report.setPreviewFieldsMany([]);
        report.setSelectCohort([])
        form.resetFields();
        setUserSitesCheckKeys([]);
        setProjectRoleCheckKeys([]);
        setUserSiteSearchKey("");
        setProjectRoleSearchKey("");
        setRoles([]);
        setCustomTemplates([]);
        report.setAttrs([]);
        setVisible(false);
        setStartDate(null);
        setEndDate(null);
        setInspectionType(["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]);
    };

    const getWidth = (type: number | undefined): number => {
        if ([6, 7, 9, 10, 11, 20, 21].indexOf(type as number) > -1) {
            return 640;
        }
        return 800;
    };

    useImperativeHandle(props.bind, () => ({ show }));

    useEffect(() => {
        if (roles.length) {
            if (type === 16 || type === 19 || type === 15 || type === 14 || type === 27) {
                runUserSites({
                    roleId: roles[0].role_id,
                    envId: main.env.id,
                }).then((res: any) => {
                    let data = res.data || [];
                    setUserSites(res.data || []);
                    setUserSitesCheckKeys(data.map((role: any) => role.id));
                    form.setFieldsValue({
                        ...form.getFieldsValue,
                        site: data.map((site: any) => site.id),
                    });
                    var institutes: any = [];
                    var sites: any = [];
                    data.forEach((it: any) => {
                        institutes.push({ label: it.number + "-" + it.name, value: it.id });
                        sites.push({ label: it.number + "-" + it.name, value: it.id });
                    });

                });
            }
            if (type === 20) {
                run_getOrderSubject({
                    customerId: main.project.customerId,
                    projectId: main.project.id,
                    envId: main.env.id,
                    roleId: roles[0].role_id,
                }).then(
                    (result: any) => {
                        setSubjects(result.data ? result.data : [])
                    }
                )
                const scope = roles[0].scope;
                if (scope === "site") {
                    //中心
                    run_getProjectUserSites({
                        customerId: main.project.customerId,
                        projectId: main.project.id,
                        envId: main.env.id,
                        roleId: roles[0].role_id,
                    }).then((result: any) => {
                        const data = result.data;
                        if (data != null && data.site != null) {
                            setSites(data.site);
                        }
                    });
                } else if (scope === "depot") {
                    //仓库
                    run_getProjectUserSites({
                        customerId: main.project.customerId,
                        projectId: main.project.id,
                        envId: main.env.id,
                        roleId: roles[0].role_id,
                    }).then((result: any) => {
                        const data = result.data;
                        if (data != null && data.storehouse != null) {
                            setStorehouses(data.storehouse);
                            setInstitutes(data.storehouse);
                        }
                    });
                }
                run_sitesAndStorehouses({
                    customerId: main.project.customerId,
                    projectId: main.project.id,
                    envId: main.env.id,
                }).then((result: any) => {
                    const data = result.data;
                    if (data != null) {
                        let institutes: any = [];
                        if (scope === "depot") {
                            if (data.site != null) {
                                setSites(data.site);
                            }
                        } else {
                            if (data.storehouse != null) {
                                setStorehouses(data.storehouse);
                                data.storehouse.forEach((value: any) => {
                                    if (value.deleted !== 1) {
                                        //过滤删除掉的仓库
                                        institutes.push(value);
                                    }
                                });
                            }
                            if (data.site != null) {
                                data.site.forEach((value: any) => {
                                    institutes.push(value);
                                });
                                if (scope === "study") {
                                    setSites(data.site);
                                }
                            }
                            setInstitutes(institutes);
                        }
                    }
                });
            }
            if (type === 9) {
                runProjectRoles({
                    projectId: main.project.id,
                }).then((res: any) => {
                    let data = res.data || [];
                    setProjectRoles(res.data || []);
                    setProjectRoleCheckKeys(data.map((role: any) => role.id));
                    form.setFieldsValue({
                        ...form.getFieldsValue,
                        site: data.map((role: any) => role.id),
                    });

                });
            }
            if (type === 18) {
                runStorehouses({
                    envId: main.env.id,
                    roleId: roles[0].role_id,
                    storehouseId: [],
                }).then((res: any) => {
                    setStorehouses(
                        res.data?.map((item: any) => ({
                            label: item.name,
                            value: item.id,
                        })) || []
                    );
                });
            }
            if (type === 8) {
                findProject({ id: main.project.id }).then((resp: any) => {
                    const data = resp.data;
                    setProjectCreatedAt(data.meta.createdAt);
                });
                setTickPassword(false);
                runExportAuthority({ roleId: roles[0].role_id }).then(
                    (res: any) => {
                        setZipTypeList(
                            res.data?.map((item: any) => ({
                                label: item.name,
                                value: item.id,
                            })) || []
                        );
                    }
                );
                runUserSites({
                    roleId: roles[0].role_id,
                    envId: main.env.id,
                }).then((res: any) => {
                    let data = res.data || [];
                    setUserSites(res.data || []);
                    setUserSitesCheckKeys(data.map((role: any) => role.id));
                    form.setFieldsValue({
                        ...form.getFieldsValue,
                        site: data.map((site: any) => site.id),
                    });
                    var institutes: any = [];
                    var sites: any = [];
                    data.forEach((it: any) => {
                        institutes.push({ label: it.number + "-" + it.name, value: it.id });
                        sites.push({ label: it.number + "-" + it.name, value: it.id });
                    });
                });
            }
            if (type === 23 || type === 22) {
                runGetRandomizationSimulation({
                    projectId: main.project.id,
                    envId: main.env.id,
                    cohortId: main.env.cohorts?.length
                        ? main.env.cohorts[0].id
                        : null,
                }).then((res: any) => {
                    setRandomizationSimulationNameList(
                        res.data?.map((item: any) => ({
                            label: item.name,
                            value: item.id,
                        })) || []
                    );
                });
                form.setFieldValue("randomizationSimulationName", null);
                form.setFieldValue("randomizationSimulationNames", []);
            }

        }
    }, [roles]);

    const handleOk = async () => {
        const value = await form.validateFields();
        var downloadType = "1,2,3,4,5,6,7,8,9,10";
        if (value.zipType != null) {
            downloadType = value.zipType.join(",");
        }
        var start = 0;
        var end = 0;
        // if (value.selectTime != null) {
        //     start = Date.parse(value.selectTime[0]) / 1000;
        //     end = Date.parse(value.selectTime[1]) / 1000;
        // }

        if (startDate != null) {
            start = startDate / 1000;
        }
        if (endDate != null) {
            end = endDate / 1000;
        }
        var secret = null;
        if (tickPassword) {
            secret = value.password;
        }
        const params: any = {
            type,
            projectId: main.project.id,
            envId: main.env.id,
            roleId: value.role,
            cohortId: main.env.cohort?.length ? main.env.cohort[0].id : null,
            templateId: value.template,
            exportTypes: downloadType,
            startTime: start,
            endTime: end,
            cipher: secret,
            sendIds: value.send,
            receiveIds: value.receive,
            projectSiteIds: userSitesCheckKeys,
        };
        // if (type === 10) {
        //     params.cohortId = value.cohort;
        // }
        if (type === 23) {
            if (
                value.cohort === null ||
                value.cohort === "" ||
                value.cohort === undefined
            ) {
                if (main.project.info.type === 2) {
                    message.warn(
                        formatMessage({ id: "report.cohort.warning" })
                    );
                    return;
                } else if (main.project.info.type === 3) {
                    message.warn(formatMessage({ id: "report.stage.warning" }));
                    return;
                }
            } else {
                params.cohortId = value.cohort;
            }
            if (
                value.randomizationSimulationName === null ||
                value.randomizationSimulationName === "" ||
                value.randomizationSimulationName === undefined
            ) {
                message.warn(
                    formatMessage({
                        id: "report.export.randomization.simulation.select.name",
                    })
                );
                return;
            } else {
                params.simulateRandomId = value.randomizationSimulationName;
            }
        }
        if (type === 22) {
            if (
                value.cohort === null ||
                value.cohort === "" ||
                value.cohort === undefined
            ) {
                if (main.project.info.type === 2) {
                    message.warn(
                        formatMessage({ id: "report.cohort.warning" })
                    );
                    return;
                } else if (main.project.info.type === 3) {
                    message.warn(formatMessage({ id: "report.stage.warning" }));
                    return;
                }
            } else {
                params.cohortId = value.cohort;
            }
            if (
                value.randomizationSimulationNames === null ||
                value.randomizationSimulationNames === "" ||
                value.randomizationSimulationNames === undefined
            ) {
                message.warn(
                    formatMessage({
                        id: "report.export.randomization.simulation.select.name",
                    })
                );
                return;
            } else {
                params.simulateRandomIds = value.randomizationSimulationNames;
            }
        }
        if (type === 8) {
            params.exportFormat = value.exportFormat;
            if (
                tickPassword &&
                (secret === null || secret === "" || secret === undefined)
            ) {
                message.warn(
                    formatMessage({
                        id: "report.audit.trail.type.secret.warning",
                    })
                );
                return;
            }
            if ((inspectionType.length === 1 && inspectionType[0] === "6")) {
                if (value.subject && value.subject.length > 0) {
                    params.site = value.site;
                    // 过滤反选的ID组
                    const reqSubject = matchSubjectForTrail(value.subject);
                    params.subject = [...reqSubject];
                } else {
                    message.warn(formatMessage({ id: "report.subject.warning" }));
                    return;
                }
            } else {
                params.exportFormat = 1
            }


            if (value.exportFormat === 2) {
                setSubjectPdfLoading(true);

                // 后端weasyprint 生成多个受试者轨迹pdf 耗时太久，这个场景就还是使用前端pdfmake 方案
                runExportSubjectPdf(params).then(() => {
                    setSubjectPdfLoading(false);
                    hide();
                    message.success(formatMessage({ id: "report.download.success" }));
                    props.refresh();
                    setStartDate(null);
                    setEndDate(null);
                });


                return;
            }


        }
        if (type === 1 || type === 2 || type === 22) {
            params.cohortIds = value.cohort ? [value.cohort] : [];
        }
        if (type === 14 || type === 15 || type === 16 || type === 23 || type === 27) {
            params.cohortIds = value.cohort ? [...value.cohort] : [];
        }
        if (type === 10) {
            const cohorts = main.env.cohorts?.map((item: { id: string; }) => item.id)
            params.cohortIds = (value.cohort && value.cohort !== null && value.cohort.length !== 0) ? [...value.cohort] : cohorts;
        }
        if (type === 18) {
            if (
                (value.cohort === null || value.cohort === undefined) &&
                isBindRoleF
            ) {
                var projectType = main.project.info.type;
                if (projectType === 3) {
                    message.warn(formatMessage({ id: "report.stage.warning" }));
                    return;
                } else if (projectType === 2) {
                    message.warn(
                        formatMessage({ id: "report.cohort.warning" })
                    );
                    return;
                }
            }
            params.cohortIds = value.cohort ? value.cohort : "";
        }

        if (type === 16 || type === 19) {
            if (value.site.length === 0) {
                message.warn(formatMessage({ id: "report.site.warning" }));
                return;
            }
            params.projectSiteIds = value.site ? value.site : [];
        } else if (type === 9) {
            if (value.site.length === 0) {
                message.warn(formatMessage({ id: "report.role.warning" }));
                return;
            }
            params.projectRolePermissionIds = value.site ? value.site : [];
        } else if (type === 18) {
            if (value.storehouse) {
                params.storehouseIds = value.storehouse ? value.storehouse : [];
            }
        } else if (type === 12) {
            if (value.randomList && value.randomList.length > 0) {
                params.randomList = value.randomList;
                params.cohortIds = value.cohort ? value.cohort : [];
            } else {
                message.warn(
                    formatMessage({ id: "report.random.list.warning" })
                );
                return;
            }
        } else if (type === 15 || type === 14 || type === 27) {
            if (value.subject && value.subject.length > 0) {
                params.site = value.site;

                // 过滤反选的ID组
                const reqSubject = matchSubject(value.subject);
                params.subject = [...reqSubject];
            } else {
                message.warn(formatMessage({ id: "report.subject.warning" }));
                return;
            }
        } else if (type === 26) {
            params.startDay = value.forecastDay[0]?.format("YYYY/MM/DD");
            params.forecastDay = value.forecastDay[1]?.format("YYYY/MM/DD");
        }


        if (type === 10) {
            setConfigPdfLoading(true);

            runExportConfigurePdf(params).then(() => {
                setConfigPdfLoading(false);
                hide();
                message.success(formatMessage({ id: "report.download.success" }));
                props.refresh();
                setStartDate(null);
                setEndDate(null);
            });

            return;
        }


        if (type === 23) {
            setSimulateRandomPdfLoading(true);

            runExportSimulateRandomPdf(params).then(() => {
                setSimulateRandomPdfLoading(false);
                hide();
                message.success(formatMessage({ id: "report.download.success" }));
                props.refresh();
                setStartDate(null);
                setEndDate(null);
            });

            return;
        }

        runExportReport(params).then(() => {
            hide();
            message.success(formatMessage({ id: "report.download.success" }));
            props.refresh();
            setStartDate(null);
            setEndDate(null);
        });
    };

    const matchSubject = (subject: any[]) => {
        let addSub: any = [];
        report.subject?.map((it: any) => {
            if (subject.find((sub: any) => sub === it.number)) {
                addSub.push(it.id);
            }
        });
        report.repeatedlySubject?.map((it: any) => {
            if (subject.find((sub: any) => sub === it.number)) {
                addSub.push(it.id);
            }
        });
        return addSub;
    };

    const matchSubjectForTrail = (subject: any[]) => {
        let addSub: any = [];
        report.subject?.map((it: any) => {
            if (subject.find((sub: any) => sub === it.id)) {
                addSub.push(it.id);
            }
        });
        report.repeatedlySubject?.map((it: any) => {
            if (subject.find((sub: any) => sub === it.id)) {
                addSub.push(it.id);
            }
        });
        return addSub;
    };

    const { runAsync: run_isBlindRole } = useFetch(roleIsBind, {
        manual: true,
    });

    const isBindRoleBool = (value: any) => {
        run_isBlindRole({
            roleId: value,
        }).then((result: any) => {
            const data = result.data;
            setIsBlindRoleF(data);
        });
    };

    const onSelectCohort = () => {
        let cohort = form.getFieldValue("cohort")
            ? form.getFieldValue("cohort")
            : [];
        let list: any = [];
        if (cohort.length > 0) {
            list = report.attrs?.filter((it: any) =>
                cohort.find((item: any) => item === it.cohortId)
            );
        } else {
            list = report.attrs
        }
        const showRandomNumber = list?.find(
            (it: any) => it.info.isRandomNumber
        );
        report.setShowRandomNumber(showRandomNumber);
        const showRegisterGroup = list?.find(
            (it: any) => it.info.allowRegisterGroup
        );
        report.setShowRegisterGroup(showRegisterGroup);
    };

    const changeSelectCohort = (value: string[]) => {
        let cohort = form.getFieldValue("cohort")
            ? form.getFieldValue("cohort")
            : [];
        let list: any = [];
        if (cohort.length > 0) {
            list = report.attrs?.filter((it: any) =>
                cohort.find((item: any) => item === it.cohortId)
            );
        } else {
            list = report.attrs
        }
        const showRandomNumber = list?.find(
            (it: any) => it.info.isRandomNumber === true
        );
        report.setShowRandomNumber(showRandomNumber);
        const showRegisterGroup = list?.find(
            (it: any) => it.info.allowRegisterGroup === true
        );
        report.setShowRegisterGroup(showRegisterGroup);
        if (type === 23 || type === 22) {
            runGetRandomizationSimulation({
                projectId: main.project.id,
                envId: main.env.id,
                cohortId: value,
            }).then((res: any) => {
                setRandomizationSimulationNameList(
                    res.data?.map((item: any) => ({
                        label: item.name,
                        value: item.id,
                    })) || []
                );
            });
            form.setFieldValue("randomizationSimulationName", null,);
            form.setFieldValue("randomizationSimulationNames", [],);
        }
    };

    const onRoleChange = (value: any) => {
        if (type === 19 || type === 18) {
            isBindRoleBool(value);
        }
        if (type === 16 || type === 15 || type === 14 || type === 19 || type === 27) {
            runUserSites({ roleId: value, envId: main.env.id }).then(
                (res: any) => {
                    let data = res.data || [];
                    setUserSites(res.data || []);
                    setUserSitesCheckKeys(data.map((site: any) => site.id));
                    form.setFieldsValue({
                        ...form.getFieldsValue,
                        site: data.map((site: any) => site.id),
                    });
                    var institutes: any = [];
                    var sites: any = [];
                    data.forEach((it: any) => {
                        institutes.push({ label: it.number + "-" + it.name, value: it.id });
                        sites.push({ label: it.number + "-" + it.name, value: it.id });
                    });

                }
            );
        }
        if (type === 20) {
            run_getOrderSubject({
                customerId: main.project.customerId,
                projectId: main.project.id,
                envId: main.env.id,
                roleId: value,
            }).then(
                (result: any) => {
                    setSubjects(result.data ? result.data : [])
                }
            )
            let role: any = roles.filter(
                (item: any) => item._id === value
            )
            const scope = role[0].scope;
            if (scope === "site") {
                //中心
                run_getProjectUserSites({
                    customerId: main.project.customerId,
                    projectId: main.project.id,
                    envId: main.env.id,
                    roleId: value,
                }).then((result: any) => {
                    const data = result.data;
                    if (data != null && data.site != null) {
                        setSites(data.site);
                    }
                });
            } else if (scope === "depot") {
                //仓库
                run_getProjectUserSites({
                    customerId: main.project.customerId,
                    projectId: main.project.id,
                    envId: main.env.id,
                    roleId: value,
                }).then((result: any) => {
                    const data = result.data;
                    if (data != null && data.storehouse != null) {
                        setStorehouses(data.storehouse);
                        setInstitutes(data.storehouse);
                    }
                });
            }
            run_sitesAndStorehouses({
                customerId: main.project.customerId,
                projectId: main.project.id,
                envId: main.env.id,
            }).then((result: any) => {
                const data = result.data;
                if (data != null) {
                    let institutes: any = [];
                    if (scope === "depot") {
                        if (data.site != null) {
                            setSites(data.site);
                        }
                    } else {
                        if (data.storehouse != null) {
                            setStorehouses(data.storehouse);
                            data.storehouse.forEach((value: any) => {
                                if (value.deleted !== 1) {
                                    //过滤删除掉的仓库
                                    institutes.push(value);
                                }
                            });
                        }
                        if (data.site != null) {
                            data.site.forEach((value: any) => {
                                institutes.push(value);
                            });
                            if (scope === "study") {
                                setSites(data.site);
                            }
                        }
                        setInstitutes(institutes);
                    }
                }
            });
        }
        if (type === 9) {
            runProjectRoles({
                projectId: main.project.id,
            }).then((res: any) => {
                let data = res.data || [];
                setProjectRoles(res.data || []);
                setProjectRoleCheckKeys(data.map((role: any) => role.id));
                form.setFieldsValue({
                    ...form.getFieldsValue,
                    site: data.map((site: any) => site.id),
                });
            });
        }

        if (type === 18) {
            runStorehouses({
                envId: main.env.id,
                roleId: value,
                storehouseId: [],
            }).then((res: any) => {
                setStorehouses(
                    res.data?.map((item: any) => ({
                        label: item.name,
                        value: item.id,
                    })) || []
                );
            });
            form.setFieldValue("storehouse", []);
            //更新预览字段
            let {
                name,
                defaultFields: fields,
                multiDefaultFields,
                customizable,
                template,
            } = record;
            run_isBlindRole({
                roleId: value,
            }).then((result: any) => {
                const data = result.data;
                setIsBlindRoleF(data);
                if (main.project.info.type === 1) {
                    fields = fields?.filter(
                        (field: any) =>
                            field.key !== "report.attributes.random.stage" &&
                            field.key !== "report.attributes.random.cohort"
                    );
                } else if (main.project.info.type === 2) {
                    fields = fields?.filter(
                        (field: any) =>
                            field.key !== "report.attributes.random.stage"
                    );
                } else if (main.project.info.type === 3) {
                    fields = fields?.filter(
                        (field: any) =>
                            field.key !== "report.attributes.random.cohort"
                    );
                }

                if (
                    (main.project.info.type === 2 ||
                        main.project.info.type === 3) &&
                    type === 18 &&
                    !data
                ) {
                    //库房单品报表，如何项目是非盲项目，去掉字段
                    fields = fields?.filter(
                        (field: any) =>
                            field.key !== "report.attributes.random.stage" &&
                            field.key !== "report.attributes.random.cohort"
                    );
                }

                if (customizable) {
                    runGetTemplates({
                        projectId: main.project.id,
                        envId: main.env.id,
                        type,
                    }).then((res: any) => {
                        if (res.data) {
                            setCustomTemplates(res.data);
                        }
                    });
                }

                setTitle(formatMessage({ id: name }));
                if (template) {
                    if (
                        (main.project.info.type === 2 ||
                            main.project.info.type === 3) &&
                        type === 18 &&
                        !data
                    ) {
                        //库房单品报表，如何项目是非盲项目，去掉字段
                        template = template?.filter(
                            (field: any) =>
                                field.key !==
                                "report.attributes.random.stage" &&
                                field.key !== "report.attributes.random.cohort"
                        );
                    }
                    setPreviewFields(template);
                } else {
                    if (multiDefaultFields && multiDefaultFields.length > 0) {
                        let fields = multiDefaultFields.find(
                            (item: any) => item.type === main.project.info.type
                        ).defaultFields;
                        setPreviewFields(fields);
                        setDefaultFields(
                            fields?.map((field: any) => field.key)
                        );
                    } else {
                        setPreviewFields(fields);
                        setDefaultFields(
                            fields?.map((field: any) => field.key)
                        );
                    }
                }

                setVisible(true);
            });
        }
        if (type === 8) {
            runExportAuthority({ roleId: value }).then((res: any) => {
                setZipTypeList(
                    res.data?.map((item: any) => ({
                        label: item.name,
                        value: item.id,
                    })) || []
                );
            });
        }
    };

    const formItemLayout = {
        labelCol: { style: { width: g.lang === "en" ? type === 26 ? 165 : 100 : 80 } },
    };

    return (
        <CustomModal
            open={visible}
            destroyOnClose
            onCancel={hide}
            centered
            title={`${formatMessage({ id: "common.download" })}-${title}`}
            width={getWidth(type)}
            onOk={handleOk}
            confirmLoading={exportReportLoading || exportConfigureReportLoading || exportConfigurePdfLoading || exportSubjectPdfLoading || exportSubjectReportLoading || subjectPdfLoading || configPdfLoading || exportSimulateRandomPdfLoading || exportSimulateRandomReportLoading || simulateRandomPdfLoading}
            okText={formatMessage({ id: "common.ok" })}
            maskClosable={false}
        >
            <Form {...formItemLayout} form={form} autoComplete="OFF">
                <input type="password" style={{ display: "none" }} autoComplete="new-password"></input>
                {[1, 2, 3, 4, 5, 6, 7, 10, 11, 21, 24, 25].indexOf(
                    type as number
                ) > -1 ? (
                    <>
                        <RolesCohort
                            onSelectRole={onRoleChange}
                            radio={type === 10 ? false : true}
                            // cohortRequired={type === 10}
                            isCohort={
                                [1, 2, 10, 22].findIndex(
                                    (it: any) => it === type
                                ) > -1
                            }
                            onSelectCohort={(cohortId:any) => {
                                if (type === 2) {
                                    report.setSelectCohort([main.env.cohorts.find((item: any) => item.id === cohortId)])
                                }
                            }}
                            roles={roles}
                            types={type === 1 || type === 2}
                        />
                        <TemplateItem
                            form={form}
                            defaultFields={defaultFields}
                            customTemplates={customTemplates}
                            setPreviewFields={setPreviewFields}
                            previewFields={previewFields}
                            hidePreview={type === 10}
                            type={type}
                        />
                    </>
                ) : null}

                {/*项目权限配置报表*/}
                {type === 9 ? (
                    <>
                        <RolesCohort
                            onSelectRole={onRoleChange}
                            isCohort={false}
                            onSelectCohort={onSelectCohort}
                            roles={roles}
                        />
                        <TemplateItem
                            form={form}
                            defaultFields={defaultFields}
                            customTemplates={customTemplates}
                            setPreviewFields={setPreviewFields}
                            previewFields={previewFields}
                            type={type}
                        />
                        <SelectProjectRole
                            form={form}
                            // roles={roles}
                            projectRoles={projectRoles}
                            setProjectRoleSearchKey={setProjectRoleSearchKey}
                            setProjectRoleCheckKeys={setProjectRoleCheckKeys}
                            projectRoleSearchKey={projectRoleSearchKey}
                            projectRoleCheckKeys={projectRoleCheckKeys}
                        />
                    </>
                ) : null}

                {/*揭盲报表*/}
                {type === 16 ? (
                    <>
                        <RolesCohort
                            onSelectRole={onRoleChange}
                            isCohort={true}
                            onSelectCohort={onSelectCohort}
                            roles={roles}
                        />
                        <TemplateItem
                            form={form}
                            defaultFields={defaultFields}
                            customTemplates={customTemplates}
                            setPreviewFields={setPreviewFields}
                            previewFields={previewFields}
                            type={type}
                        />
                        <SelectSite
                            form={form}
                            roles={roles}
                            userSites={userSites}
                            setUserSiteSearchKey={setUserSiteSearchKey}
                            setUserSitesCheckKeys={setUserSitesCheckKeys}
                            userSiteSearchKey={userSiteSearchKey}
                            userSitesCheckKeys={userSitesCheckKeys}
                        />
                    </>
                ) : null}
                {/*库房单品报表*/}
                {type === 18 ? (
                    <>
                        <Row>
                            <Col
                                span={
                                    main.env.cohorts &&
                                        main.env.cohorts.length > 0
                                        ? 12
                                        : 24
                                }
                            >
                                <Form.Item
                                    required
                                    label={formatMessage({ id: "common.role" })}
                                    name={"role"}
                                >
                                    <Select
                                        options={roles.map((role: any) => {
                                            return {
                                                label: role.role,
                                                value: role.role_id,
                                            };
                                        })}
                                        onChange={onRoleChange}
                                    />
                                </Form.Item>
                            </Col>
                            {main.env.cohorts &&
                                main.env.cohorts.length > 0 &&
                                isBindRoleF && (
                                    <Col span={12}>
                                        <Form.Item
                                            labelCol={{
                                                style: {
                                                    width:
                                                        g.lang === "en"
                                                            ? 150
                                                            : 60,
                                                },
                                            }}
                                            required
                                            label={
                                                main.project.info.type === 2
                                                    ? formatMessage({
                                                        id: "projects.second",
                                                    })
                                                    : formatMessage({
                                                        id: "projects.third",
                                                    })
                                            }
                                            name={"cohort"}
                                        >
                                            <Select
                                                options={main.env.cohorts.map(
                                                    (item: any) => {
                                                        return {
                                                            label: item.name,
                                                            value: item.id,
                                                        };
                                                    }
                                                )}
                                                onChange={() => { }}
                                            />
                                        </Form.Item>
                                    </Col>
                                )}
                        </Row>
                        <Row>
                            <Col span={24}>
                                <Form.Item
                                    label={formatMessage({
                                        id: "report.storehouse",
                                    })}
                                    name={"storehouse"}
                                >
                                    <Select
                                        mode={"multiple"}
                                        options={storehouses}
                                        placeholder={formatMessage({
                                            id: "common.all",
                                        })}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <TemplateItem
                            form={form}
                            defaultFields={defaultFields}
                            customTemplates={customTemplates}
                            setPreviewFields={setPreviewFields}
                            previewFields={previewFields}
                            isBindRoleF={isBindRoleF}
                            type={type}
                        />
                    </>
                ) : null}
                {/*中心药房单品报表*/}
                {type === 19 ? (
                    <>
                        <Row>
                            <Col
                                span={
                                    main.env.cohorts &&
                                        main.env.cohorts.length > 0
                                        ? 12
                                        : 24
                                }
                            >
                                <Form.Item
                                    required
                                    label={formatMessage({ id: "common.role" })}
                                    name={"role"}
                                >
                                    <Select
                                        options={roles.map((role: any) => {
                                            return {
                                                label: role.role,
                                                value: role.role_id,
                                            };
                                        })}
                                        onChange={onRoleChange}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={24}>
                                <TemplateItem
                                    form={form}
                                    defaultFields={defaultFields}
                                    customTemplates={customTemplates}
                                    setPreviewFields={setPreviewFields}
                                    previewFields={previewFields}
                                    type={type}
                                />
                            </Col>
                        </Row>
                        <SelectSite
                            form={form}
                            roles={roles}
                            userSites={userSites}
                            setUserSiteSearchKey={setUserSiteSearchKey}
                            setUserSitesCheckKeys={setUserSitesCheckKeys}
                            userSiteSearchKey={userSiteSearchKey}
                            userSitesCheckKeys={userSitesCheckKeys}
                        />
                    </>
                ) : null}
                {/*研究产品订单报表*/}
                {type === 20 ? (
                    <>

                        <RolesCohort
                            onSelectRole={onRoleChange}
                            isCohort={
                                [1, 2, 10, 22].findIndex(
                                    (it: any) => it === type
                                ) > -1
                            }
                            onSelectCohort={() => { }}
                            roles={roles}
                        />
                        <Form.Item
                            required
                            label={formatMessage({ id: "shipment.send" })}
                            name={"send"}
                        >
                            <Select mode={"multiple"}
                                placeholder={formatMessage({ id: "common.all" })}
                                options={institutes} optionFilterProp="label"
                            />
                        </Form.Item>
                        <Form.Item
                            required
                            label={formatMessage({ id: "shipment.receive" })}
                            name={"receive"}
                        >
                            <Select mode={"multiple"}
                                placeholder={formatMessage({ id: "common.all" })}
                                // options={userSites.map((site: any) => {
                                //     return { label: site.number+"-"+site.name, value: site.id };
                                // })}
                                options={[...sites, ...subjects]} optionFilterProp="label"
                            />
                        </Form.Item>
                        <TemplateItem
                            form={form}
                            defaultFields={defaultFields}
                            customTemplates={customTemplates}
                            setPreviewFields={setPreviewFields}
                            previewFields={previewFields}
                            type={type}
                        />
                    </>
                ) : null}
                {type === 12 && (
                    <TypeSourceRandomizationList
                        form={form}
                        roles={roles}
                        customTemplates={customTemplates}
                        defaultFields={defaultFields}
                        previewFields={previewFields}
                        setPreviewFields={previewFields}
                    />
                )}
                {(type === 15 || type === 14 || type === 27) && (
                    <CustomerTemplate
                        defaultFields={defaultFields}
                        customTemplates={customTemplates}
                        setPreviewFields={setPreviewFields}
                        previewFields={previewFields}
                        visible={visible}
                        onSelectRole={onRoleChange}
                        form={form}
                        roles={roles}
                        userSites={userSites}
                        setUserSiteSearchKey={setUserSiteSearchKey}
                        setUserSitesCheckKeys={setUserSitesCheckKeys}
                        userSiteSearchKey={userSiteSearchKey}
                        userSitesCheckKeys={userSitesCheckKeys}
                        type={type}
                    />
                )}
                {/* 稽查轨迹 */}
                {type === 8 && (
                    <>
                        <RolesCohort
                            onSelectRole={onRoleChange}
                            isCohort={
                                [1, 2, 22, 23].findIndex(
                                    (it: any) => it === type
                                ) > -1
                            }
                            onSelectCohort={() => { }}
                            roles={roles}
                        />
                        <Col span={24}>
                            <Form.Item
                                required
                                label={formatMessage({ id: "common.type" })}
                                name={"zipType"}
                            >
                                <Select
                                    mode="multiple"
                                    placeholder={
                                        <span style={{ color: "#1D2129" }}>
                                            <FormattedMessage id="common.all" />
                                        </span>
                                    }
                                    options={zipTypeList}
                                    showArrow={true}
                                    onSearch={onSearch}
                                    filterOption={(input, option) =>
                                        (option?.label ?? "")
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    onChange={handleChange}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label={formatMessage({
                                    id: "common.time",
                                })}
                                name={"selectTime"}
                            >
                                <div
                                    style={{ width: "100%" }}
                                    className="full-width"
                                >
                                    <DatePicker
                                        style={{ width: "48%" }}
                                        placeholder={formatMessage({
                                            id: "common.time.start",
                                        })}
                                        showTime={{ format: "HH:mm" }}
                                        format="YYYY-MM-DD HH:mm"
                                        default-time="['00:00:00','23:59:59']"
                                        onChange={onStartChangeDate}
                                        onOk={onStartOkDate}
                                        disabledDate={disabledStartDate}
                                        disabledTime={disabledStartTime}
                                        showNow
                                    />
                                    <span
                                        style={{
                                            marginLeft: "8px",
                                            marginRight: "8px",
                                        }}
                                    >
                                        ~
                                    </span>
                                    <DatePicker
                                        style={{ width: "48%" }}
                                        placeholder={formatMessage({
                                            id: "common.time.end",
                                        })}
                                        showTime={{ format: "HH:mm" }}
                                        format="YYYY-MM-DD HH:mm"
                                        default-time="['00:00:00','23:59:59']"
                                        onChange={onEndChangeDate}
                                        onOk={onEndOkDate}
                                        disabledDate={disabledEndDate}
                                        disabledTime={disabledEndTime}
                                        showNow
                                    />
                                </div>
                            </Form.Item>
                        </Col>
                        {

                            // (inspectionType.includes("6")) &&
                            (inspectionType.length === 1 && inspectionType[0] === "6") &&
                            <Col span={24}>
                                <Form.Item
                                    label={formatMessage({ id: "common.export.format" })}
                                    name={"exportFormat"}
                                    initialValue={1}
                                >
                                    <Radio.Group style={{ marginLeft: g.lang === "en" ? 10 : 0 }}>
                                        <Radio value={1}>
                                            <span>{"Excel"}</span>
                                        </Radio>
                                        <Radio value={2}>
                                            <span>{"PDF"}</span>
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </Col>
                        }
                        <Col span={24}>
                            <div
                                style={{
                                    marginLeft: g.lang === "en" ? 100 : 80,
                                    width: g.lang === "en" ? 640 : 662,
                                    backgroundColor: "#F8F9FA",
                                }}
                            >
                                <Form.Item
                                    style={{ marginLeft: 8 }}
                                    required
                                    name={"selectPassword"}
                                >
                                    <Checkbox
                                        defaultChecked={false}
                                        onChange={onChangePassword}
                                    >
                                        {formatMessage({
                                            id: "project.report.locus.checkbox.password",
                                        })}
                                    </Checkbox>
                                </Form.Item>
                            </div>
                            <Form.Item
                                label={formatMessage({
                                    id: "common.password",
                                })}
                                required
                                style={{ display: !tickPassword ? "none" : "" }}
                                name={"password"}
                            >
                                <Input.Password
                                    placeholder={formatMessage({
                                        id: "common.required.prefix",
                                    })}
                                    maxLength={6}
                                    // disabled={!tickPassword}
                                    iconRender={(visible) =>
                                        visible ? (
                                            <EyeTwoTone />
                                        ) : (
                                            <EyeInvisibleOutlined />
                                        )
                                    }
                                />
                            </Form.Item>
                        </Col>
                        {

                            // (inspectionType.includes("6")) &&
                            (inspectionType.length === 1 && inspectionType[0] === "6") &&
                            <Col span={24}>
                                <CustomerSiteSubject
                                    visible={visible}
                                    form={form}
                                    roles={roles}
                                    userSites={userSites}
                                    setUserSiteSearchKey={setUserSiteSearchKey}
                                    setUserSitesCheckKeys={setUserSitesCheckKeys}
                                    userSiteSearchKey={userSiteSearchKey}
                                    userSitesCheckKeys={userSitesCheckKeys}
                                />
                            </Col>
                        }
                    </>
                )}
                {/* 模拟随机报告 */}
                {(type === 23 || type === 22) && (
                    <>
                        <RolesCohort
                            onSelectRole={onRoleChange}
                            isCohort={true}
                            onSelectCohort={changeSelectCohort}
                            roles={roles}
                            radio={true}
                        />
                        <TemplateItem
                            form={form}
                            defaultFields={defaultFields}
                            customTemplates={customTemplates}
                            setPreviewFields={setPreviewFields}
                            previewFields={previewFields}
                            hidePreview={true}
                            type={type}
                        />
                        <Col span={24}>
                            {
                                type === 22 &&
                                <Form.Item
                                    rules={[{ required: true }]}
                                    label={formatMessage({ id: "common.name" })}
                                    name={"randomizationSimulationNames"}
                                >
                                    <Select
                                        mode="multiple"
                                        placeholder={formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        options={randomizationSimulationNameList}
                                        showArrow={true}
                                        onSearch={onSearch}
                                        filterOption={(input, option) =>
                                            (option?.label ?? "")
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={nameChange}
                                    />
                                </Form.Item>
                            }
                            {type === 23 &&
                                <Form.Item rules={[{ required: true }]}
                                    label={formatMessage({ id: "common.name" })}
                                    name={"randomizationSimulationName"}
                                >
                                    <Select
                                        placeholder={formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        options={randomizationSimulationNameList}
                                        showArrow={true}
                                        onSearch={onSearch}
                                        filterOption={(input, option) =>
                                            (option?.label ?? "")
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={nameChange}
                                    />
                                </Form.Item>}
                        </Col>
                    </>
                )}

                {type === 26 ? (
                    <>
                        <RolesCohort
                            onSelectRole={onRoleChange}
                            radio={true}
                            // cohortRequired={type === 10}
                            isCohort={false}
                            onSelectCohort={() => { }}
                            roles={roles}
                        />
                        <Form.Item rules={[{ required: true }]} name={"forecastDay"} label={formatMessage({ id: "report.export.forecast.date" })}>
                            {/*<DatePicker  format={"YYYY/MM/DD"}*/}
                            {/*             disabledDate={(current: any) => {*/}
                            {/*                 return current && current.valueOf() <= dayjs().startOf('day').valueOf();*/}
                            {/*             }}*/}
                            {/*/>*/}
                            <RangePicker
                                disabledDate={(current: any) => {
                                    return current && current.valueOf() <= dayjs().startOf('day').valueOf();
                                }}
                                format={"YYYY/MM/DD"}
                            />
                        </Form.Item>
                        <TemplateItem
                            form={form}
                            defaultFields={defaultFields}
                            customTemplates={customTemplates}
                            setPreviewFields={setPreviewFields}
                            previewFields={previewFields}
                            hidePreview={false}
                            type={type}
                        />
                    </>
                ) : null}
            </Form>
        </CustomModal>
    );
};

interface TypeSourceRandomizationListProps {
    roles: any;
    form: any;
    defaultFields: any;
    customTemplates: any;
    setPreviewFields: any;
    previewFields: any;
}

const TypeSourceRandomizationList = (
    props: TypeSourceRandomizationListProps
) => {
    const intl = useIntl();
    const main = useMain();

    const { formatMessage } = intl;
    const {
        form,
        roles,
        defaultFields,
        customTemplates,
        setPreviewFields,
        previewFields,
    } = props;
    const [randomListSource, setRandomListSource] = useSafeState<any[]>([]);
    const [randomList, setRandomList] = useSafeState<any[]>([]);
    const [randomListCheckKeys, setRandomListCheckKeys] = useSafeState<any[]>(
        []
    );
    const [randomListSearchKey, setRandomListSearchKey] = useSafeState<any>("");
    const { runAsync: getRandomListManyRun } = useFetch(getRandomListMany, {
        manual: true,
    });

    const getRandomList = () => {
        getRandomListManyRun({
            env_id: main.env.id,
        }).then((data: any) => {
            if (data.data) {
                setRandomListSource(data.data);
                setRandomList(data.data);
                setRandomListCheckKeys(data.data.map((it: any) => it.id));
                form.setFieldValue(
                    "randomList",
                    data.data.map((it: any) => it.id)
                );
            }
        });
    };
    const onSelectCohort = (e: any) => {
        if (e.length > 0) {
            let list: any = [];
            e.map((items: any) => {
                let temp = randomListSource.filter(
                    (item: any) => item.cohortId === items
                );
                list = [...list, ...temp];
            });
            setRandomList(list);
            setRandomListCheckKeys(list.map((site: any) => site.id));
            form.setFieldsValue({
                ...form.getFieldsValue,
                randomList: list.map((site: any) => site.id),
            });
        } else {
            setRandomList(randomListSource);
        }
    };

    const onSelectALLRandomList = (e: any) => {
        if (e.target.checked) {
            setRandomListCheckKeys(randomList.map((site: any) => site.id));
            form.setFieldsValue({
                ...form.getFieldsValue,
                randomList: randomList.map((site: any) => site.id),
            });
        } else {
            setRandomListCheckKeys([]);
            form.setFieldsValue({ ...form.getFieldsValue, randomList: [] });
        }
    };

    const onSelectRandomList = () => {
        setRandomListCheckKeys(form.getFieldValue("randomList"));
    };

    useEffect(getRandomList, []);
    return (
        <>
            <RolesCohort
                onSelectRole={() => { }}
                isCohort={true}
                onSelectCohort={onSelectCohort}
                roles={roles}
            />
            <Row>
                <Col span={24}>
                    <TemplateItem
                        form={form}
                        defaultFields={defaultFields}
                        customTemplates={customTemplates}
                        setPreviewFields={setPreviewFields}
                        previewFields={previewFields}
                    />
                    <div style={{ height: 360 }}>
                        <div
                            style={{
                                width: "100%",
                                border: "1px solid #DDDEDF",
                            }}
                        >
                            <div
                                style={{
                                    height: 36,
                                    paddingLeft: 13,
                                    borderBottom: "1px solid #DDDEDF",
                                    lineHeight: "36px",
                                }}
                            >
                                <Checkbox
                                    onChange={onSelectALLRandomList}
                                    indeterminate={
                                        randomListCheckKeys.length > 0 &&
                                        randomListCheckKeys.length <
                                        randomList.length
                                    }
                                    checked={randomListCheckKeys.length > 0}
                                >
                                    {formatMessage({ id: "common.name" })}
                                    <span
                                        style={{
                                            fontSize: 12,
                                            color: "#aeb2b9",
                                        }}
                                    >
                                        ({randomListCheckKeys.length}/
                                        {randomList.length}
                                        {formatMessage({
                                            id: "report.attributes.unit",
                                        })}
                                        )
                                    </span>
                                </Checkbox>
                            </div>
                            <div style={{ padding: "8px 11px 8px 13px" }}>
                                <Input
                                    placeholder={formatMessage({
                                        id: "common.required.prefix",
                                    })}
                                    suffix={
                                        <SearchOutlined
                                            style={{ color: "#BFBFBF" }}
                                        />
                                    }
                                    onChange={(e) => {
                                        if (e.target.value !== "") {
                                            setRandomListSearchKey(
                                                e.target.value
                                            );
                                            let newRandomList =
                                                randomList.filter(
                                                    (it: any) =>
                                                        it.name.indexOf(
                                                            e.target.value
                                                        ) !== -1
                                                );
                                            setRandomList(newRandomList);
                                            setRandomListCheckKeys(
                                                newRandomList.map(
                                                    (it: any) => it.id
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                randomList: newRandomList.map(
                                                    (site: any) => site.id
                                                ),
                                            });
                                        } else {
                                            setRandomList(randomListSource);
                                            setRandomListCheckKeys(
                                                randomListSource.map(
                                                    (it: any) => it.id
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                randomList:
                                                    randomListSource.map(
                                                        (it: any) => it.id
                                                    ),
                                            });
                                        }
                                    }}
                                />
                                <Form.Item name={"randomList"}>
                                    <Checkbox.Group
                                        onChange={onSelectRandomList}
                                    >
                                        {randomList &&
                                            randomList.map((list: any) => (
                                                <div
                                                    key={list.id}
                                                    style={{
                                                        height: 32,
                                                        lineHeight: "32px",
                                                    }}
                                                >
                                                    <Checkbox value={list.id}>
                                                        {list.name}
                                                    </Checkbox>
                                                </div>
                                            ))}
                                    </Checkbox.Group>
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                </Col>
            </Row>
        </>
    );
};

interface CustomerTemplateProps {
    roles: any;
    visible: any;
    onSelectRole: any;
    form: any;
    userSites: any;
    setUserSitesCheckKeys: any;
    userSitesCheckKeys: any;
    setUserSiteSearchKey: any;
    userSiteSearchKey: any;
    defaultFields: any;
    customTemplates: any;
    setPreviewFields: any;
    previewFields: any;
    type: any;
}

const CustomerTemplate = (props: CustomerTemplateProps) => {
    const intl = useIntl();
    const main = useMain();
    const report = useReport();
    const g = useGlobal();

    const { formatMessage } = intl;
    const {
        defaultFields,
        customTemplates,
        setPreviewFields,
        previewFields,
        form,
        roles,
        userSites,
        setUserSitesCheckKeys,
        userSitesCheckKeys,
        setUserSiteSearchKey,
        userSiteSearchKey,
        onSelectRole,
        type,
    } = props;
    // const [subject, setSubject] = useSafeState<any[]>([]);
    const [subjectSource, setSubjectSource] = useSafeState<any[]>([]);
    const [subjectCheckKeys, setSubjectCheckKeys] = useSafeState<string[]>([]);
    const [subjectSearchKey, setSubjectSearchKey] = useSafeState<string>("");
    const { runAsync: getSubjectInfoRun } = useFetch(getSubjectInfo, {
        manual: true,
    });

    const getSubjectList = () => {
        if (userSitesCheckKeys.length === 0) {
            report.setRepeatedlySubject([]);
            report.setSubject([]);
            setSubjectSource([]);
            setSubjectCheckKeys([]);
            form.setFieldsValue({ ...form.getFieldsValue, subject: [] });
            return;
        }
        getSubjectInfoRun({
            env_id: main.env.id,
            cohort_id: form.getFieldValue("cohort")
                ? form.getFieldValue("cohort")
                : [],
            project_site_id: form.getFieldValue("site")
                ? form.getFieldValue("site")
                : [],
        }).then((data: any) => {
            if (data.data) {
                handleSubject(data);
            } else {
                report.setSubject([]);
                setSubjectSource([]);
                setSubjectCheckKeys([]);
            }
        });
    };

    // 过滤相同名称的受试者号
    const handleSubject = (data: any) => {
        let res: any = [];
        let repeatedly: any[] = [];
        let itemMap: any = {};
        data.data.forEach((it: any) => {
            if (itemMap[it.number]) {
                repeatedly.push(it);
            } else {
                itemMap[it.number] = true;
                res.push(it);
            }
        });
        report.setRepeatedlySubject(repeatedly);
        report.setSubject(res);
        setSubjectSource(res);
        setSubjectCheckKeys(res.map((item: any) => item.number));
        form.setFieldsValue({
            ...form.getFieldsValue,
            subject: res.map((item: any) => item.number),
        });
    };

    const onSelectCohort = () => {
        let cohort = form.getFieldValue("cohort")
            ? form.getFieldValue("cohort")
            : [];
        let list: any = [];
        if (cohort.length > 0) {
            list = report.attrs?.filter((it: any) =>
                cohort.find((item: any) => item === it.cohortId)
            );
        } else {
            list = report.attrs
        }
        const showRandomNumber = list?.find(
            (it: any) => it.info.isRandomNumber
        );
        report.setShowRandomNumber(showRandomNumber);
        const showRegisterGroup = list?.find(
            (it: any) => it.info.allowRegisterGroup
        );
        report.setShowRegisterGroup(showRegisterGroup);
        getSubjectList();
    };
    useEffect(() => {
        getSubjectList();
    }, [userSitesCheckKeys]);
    const selectSite = () => {
        setUserSitesCheckKeys(form.getFieldValue("site"));
        // getSubjectList()
    };
    return (
        <>
            <RolesCohort
                onSelectRole={onSelectRole}
                onSelectCohort={onSelectCohort}
                roles={roles}
                isCohort={true}
                type={type}
            />
            <Col span={24}>
                <TemplateItem
                    form={form}
                    defaultFields={defaultFields}
                    customTemplates={customTemplates}
                    setPreviewFields={setPreviewFields}
                    previewFields={previewFields}
                    type={type}
                />
                <Row style={{ height: 359 }}>
                    <Col style={{ width: 365 }}>
                        <SelectSite
                            selectSite={selectSite}
                            form={form}
                            roles={roles}
                            userSites={userSites}
                            setUserSiteSearchKey={setUserSiteSearchKey}
                            setUserSitesCheckKeys={setUserSitesCheckKeys}
                            userSiteSearchKey={userSiteSearchKey}
                            userSitesCheckKeys={userSitesCheckKeys}
                        />
                    </Col>

                    <Col style={{ width: 365, marginLeft: 8 }}>
                        <div
                            style={{
                                width: "100%",
                                border: "1px solid #DDDEDF",
                                borderBottom: "unset",
                            }}
                        >
                            <div
                                style={{
                                    height: 36,
                                    paddingLeft: 13,
                                    borderBottom: "1px solid #DDDEDF",
                                    lineHeight: "36px",
                                }}
                            >
                                <Checkbox
                                    onChange={(e) => {
                                        if (e.target.checked) {
                                            setSubjectCheckKeys(
                                                report.subject.map(
                                                    (site: any) => site.number
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: report.subject.map(
                                                    (site: any) => site.number
                                                ),
                                            });
                                        } else {
                                            setSubjectCheckKeys([]);
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: [],
                                            });
                                        }
                                    }}
                                    indeterminate={
                                        subjectCheckKeys.length > 0 &&
                                        subjectCheckKeys.length <
                                        report.subject.length
                                    }
                                    checked={subjectCheckKeys.length > 0}
                                >
                                    {/* {main.project.info.type === 1
                                        ? main.attr?.info.subjectReplaceText
                                        : formatMessage({
                                            id: "common.subject",
                                        })} */}
                                    {
                                        main.attr ?
                                            ((main.attr.info.subjectReplaceText === "" && main.attr.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((main.attr.info.subjectReplaceText !== "" && main.attr.info.subjectReplaceTextEn === "") ? main.attr.info.subjectReplaceText : ((main.attr.info.subjectReplaceText === "" && main.attr.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? main.attr.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? main.attr.info.subjectReplaceTextEn : main.attr.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })
                                    }
                                    <span
                                        style={{
                                            fontSize: 12,
                                            color: "#aeb2b9",
                                        }}
                                    >
                                        ({subjectCheckKeys.length}/
                                        {report.subject.length}
                                        {formatMessage({
                                            id: "report.attributes.unit",
                                        })}
                                        )
                                    </span>
                                </Checkbox>
                            </div>
                            <div style={{ padding: "8px 11px 0 13px" }}>
                                <Input
                                    placeholder={formatMessage({
                                        id: "common.required.prefix",
                                    })}
                                    suffix={
                                        <SearchOutlined
                                            style={{ color: "#BFBFBF" }}
                                        />
                                    }
                                    onChange={(e) => {
                                        if (e.target.value !== "") {
                                            // setSubjectSearchKey(e.target.value);
                                            let newSubject =
                                                report.subject.filter(
                                                    (it: any) =>
                                                        it.number.indexOf(
                                                            e.target.value
                                                        ) !== -1
                                                );
                                            report.setSubject(newSubject);
                                            setSubjectCheckKeys(
                                                newSubject.map(
                                                    (it: any) => it.number
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: newSubject.map(
                                                    (site: any) => site.number
                                                ),
                                            });
                                        } else {
                                            report.setSubject(subjectSource);
                                            setSubjectCheckKeys(
                                                subjectSource.map(
                                                    (it: any) => it.number
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: subjectSource.map(
                                                    (it: any) => it.number
                                                ),
                                            });
                                        }
                                    }}
                                />
                                <Form.Item
                                    name={"subject"}
                                    style={{
                                        height: "283px",
                                        overflow: "auto",
                                        marginBottom: "0",
                                        paddingBottom: "16px",
                                    }}
                                >
                                    <Checkbox.Group
                                        onChange={(e) => {
                                            setSubjectCheckKeys(
                                                form.getFieldValue("subject")
                                            );
                                        }}
                                    >
                                        {report.subject &&
                                            report.subject.map((site: any) => (
                                                <div
                                                    key={site.id}
                                                    style={{
                                                        height: 32,
                                                        lineHeight: "32px",
                                                    }}
                                                >
                                                    <Checkbox
                                                        value={site.number}
                                                    >
                                                        {site.number}
                                                    </Checkbox>
                                                </div>
                                            ))}
                                    </Checkbox.Group>
                                </Form.Item>
                            </div>
                        </div>
                    </Col>
                </Row>
            </Col>
        </>
    );
};


interface CustomerSiteSubjectProps {
    roles: any;
    visible: any;
    form: any;
    userSites: any;
    setUserSitesCheckKeys: any;
    userSitesCheckKeys: any;
    setUserSiteSearchKey: any;
    userSiteSearchKey: any;
}

const CustomerSiteSubject = (props: CustomerSiteSubjectProps) => {
    const intl = useIntl();
    const main = useMain();
    const report = useReport();
    const g = useGlobal();

    const { formatMessage } = intl;
    const {
        form,
        roles,
        userSites,
        setUserSitesCheckKeys,
        userSitesCheckKeys,
        setUserSiteSearchKey,
        userSiteSearchKey,
    } = props;
    // const [subject, setSubject] = useSafeState<any[]>([]);
    const [subjectSource, setSubjectSource] = useSafeState<any[]>([]);
    const [subjectCheckKeys, setSubjectCheckKeys] = useSafeState<string[]>([]);
    const [subjectCheckIds, setSubjectCheckIds] = useSafeState<string[]>([]);
    const [subjectSearchKey, setSubjectSearchKey] = useSafeState<string>("");
    const { runAsync: getSubjectInfoRun } = useFetch(getSubjectInfo, {
        manual: true,
    });

    const getSubjectList = () => {
        if (userSitesCheckKeys.length === 0) {
            report.setRepeatedlySubject([]);
            report.setSubject([]);
            setSubjectSource([]);
            setSubjectCheckKeys([]);
            form.setFieldsValue({ ...form.getFieldsValue, subject: [] });
            return;
        }
        getSubjectInfoRun({
            env_id: main.env.id,
            cohort_id: form.getFieldValue("cohort")
                ? form.getFieldValue("cohort")
                : [],
            project_site_id: form.getFieldValue("site")
                ? form.getFieldValue("site")
                : [],
        }).then((data: any) => {
            if (data.data) {
                handleSubject(data);
            } else {
                report.setSubject([]);
                setSubjectSource([]);
                // setSubjectCheckKeys([]);
                setSubjectCheckIds([]);
            }
        });
    };

    // 过滤相同名称的受试者号  // 原本是为了再随机项目去除重复受试者号。在2.12版本后需要展示已删除的受试者，所以这里对已删除的不再去重
    const handleSubject = (data: any) => {
        let res: any = [];
        let repeatedly: any[] = [];
        let itemMap: any = {};
        data.data.forEach((it: any) => {
            if (main.project.info.type === 3 && itemMap[it.number] && it.deleted !== true) {
                repeatedly.push(it);
            } else {
                itemMap[it.number] = true;
                res.push(it);
            }
        });
        report.setRepeatedlySubject(repeatedly);
        report.setSubject(res);
        setSubjectSource(res);
        // setSubjectCheckKeys(res.map((item: any) => item.number));
        setSubjectCheckIds(res.map((item: any) => item.id));
        form.setFieldsValue({
            ...form.getFieldsValue,
            subject: res.map((item: any) => item.id),
        });
    };

    useEffect(() => {
        getSubjectList();
    }, [userSitesCheckKeys]);
    const selectSite = () => {
        setUserSitesCheckKeys(form.getFieldValue("site"));
        // getSubjectList()
    };
    return (
        <>
            <Col span={24}>
                <Row style={{ height: 359 }}>
                    <Col style={{ width: 365 }}>
                        <SelectSite
                            selectSite={selectSite}
                            form={form}
                            roles={roles}
                            userSites={userSites}
                            setUserSiteSearchKey={setUserSiteSearchKey}
                            setUserSitesCheckKeys={setUserSitesCheckKeys}
                            userSiteSearchKey={userSiteSearchKey}
                            userSitesCheckKeys={userSitesCheckKeys}
                        />
                    </Col>

                    <Col style={{ width: 365, marginLeft: 8 }}>
                        <div
                            style={{
                                width: "100%",
                                border: "1px solid #DDDEDF",
                                borderBottom: "unset",
                            }}
                        >
                            <div
                                style={{
                                    height: 36,
                                    paddingLeft: 13,
                                    borderBottom: "1px solid #DDDEDF",
                                    lineHeight: "36px",
                                }}
                            >
                                <Checkbox
                                    onChange={(e) => {
                                        if (e.target.checked) {
                                            // setSubjectCheckKeys(
                                            setSubjectCheckIds(
                                                report.subject.map(
                                                    (site: any) => site.id
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: report.subject.map(
                                                    (site: any) => site.id
                                                ),
                                            });
                                        } else {
                                            // setSubjectCheckKeys([]);
                                            setSubjectCheckIds([]);
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: [],
                                            });
                                        }
                                    }}
                                    indeterminate={
                                        // subjectCheckKeys.length > 0 &&
                                        subjectCheckIds.length > 0 &&
                                        subjectCheckIds.length <
                                        report.subject.length
                                    }
                                    checked={subjectCheckIds.length > 0}
                                >
                                    {/* {main.project.info.type === 1
                                        ? main.attr?.info.subjectReplaceText
                                        : formatMessage({
                                              id: "common.subject",
                                          })} */}

                                    {
                                        main.attr ?
                                            ((main.attr.info.subjectReplaceText === "" && main.attr.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((main.attr.info.subjectReplaceText !== "" && main.attr.info.subjectReplaceTextEn === "") ? main.attr.info.subjectReplaceText : ((main.attr.info.subjectReplaceText === "" && main.attr.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? main.attr.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? main.attr.info.subjectReplaceTextEn : main.attr.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })
                                    }
                                    <span
                                        style={{
                                            fontSize: 12,
                                            color: "#aeb2b9",
                                        }}
                                    >
                                        ({subjectCheckIds.length}/
                                        {report.subject.length}
                                        {formatMessage({
                                            id: "report.attributes.unit",
                                        })}
                                        )
                                    </span>
                                </Checkbox>
                            </div>
                            <div style={{ padding: "8px 11px 0 13px" }}>
                                <Input
                                    placeholder={formatMessage({
                                        id: "common.required.prefix",
                                    })}
                                    suffix={
                                        <SearchOutlined
                                            style={{ color: "#BFBFBF" }}
                                        />
                                    }
                                    onChange={(e) => {
                                        if (e.target.value !== "") {
                                            // setSubjectSearchKey(e.target.value);
                                            let newSubject =
                                                report.subject.filter(
                                                    (it: any) =>
                                                        it.number.indexOf(
                                                            e.target.value
                                                        ) !== -1
                                                );
                                            report.setSubject(newSubject);
                                            // setSubjectCheckKeys(
                                            setSubjectCheckIds(
                                                newSubject.map(
                                                    (it: any) => it.id
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: newSubject.map(
                                                    (site: any) => site.id
                                                ),
                                            });
                                        } else {
                                            report.setSubject(subjectSource);
                                            // setSubjectCheckKeys(
                                            setSubjectCheckIds(
                                                subjectSource.map(
                                                    (it: any) => it.id
                                                )
                                            );
                                            form.setFieldsValue({
                                                ...form.getFieldsValue,
                                                subject: subjectSource.map(
                                                    (it: any) => it.id
                                                ),
                                            });
                                        }
                                    }}
                                />
                                <Form.Item
                                    name={"subject"}
                                    style={{
                                        height: "283px",
                                        overflow: "auto",
                                        marginBottom: "0",
                                        paddingBottom: "16px",
                                    }}
                                >
                                    <Checkbox.Group
                                        onChange={(e) => {
                                            // setSubjectCheckKeys(
                                            setSubjectCheckIds(
                                                form.getFieldValue("subject")
                                            );
                                        }}
                                    >
                                        {report.subject &&
                                            report.subject.map((site: any) => (
                                                <div
                                                    key={site.id}
                                                    style={{
                                                        height: 32,
                                                        lineHeight: "32px",
                                                    }}
                                                >
                                                    <Checkbox
                                                        value={site.id}
                                                    >
                                                        {
                                                            site.deleted === true?
                                                                <Typography.Text delete style={{color:"#ADB2BA"}}>{site.number}</Typography.Text>
                                                                :
                                                                site.number
                                                        }
                                                    </Checkbox>
                                                </div>
                                            ))}
                                    </Checkbox.Group>
                                </Form.Item>
                            </div>
                        </div>
                    </Col>
                </Row>
            </Col>
        </>
    );
};


const Preview = styled.div`
    background-color: #f2f3f5;
    overflow-y: hidden;
    overflow-x: auto;
    white-space: nowrap;
`;

const PreviewContent = styled.span`
    margin: 0 12px;
    height: 38px;
    line-height: 38px;
    font-size: 10px;
    font-weight: 500;
    font-family: PingFang SC;
    color: #677283;
    display: inline-block;
`;

interface RolesCohortProps {
    roles: any;
    isCohort: boolean;
    onSelectCohort: any;
    onSelectRole: any;
    radio?: any;
    types?: boolean;
    cohortRequired?: boolean;
    type?: any;
}

const RolesCohort = (props: RolesCohortProps) => {
    const intl = useIntl();
    const main = useMain();
    const {
        roles,
        onSelectCohort,
        isCohort,
        onSelectRole,
        radio,
        types,
        cohortRequired,
        type,
    } = props;
    const { formatMessage } = intl;
    const g = useGlobal();

    return (
        <Row>
            <Col
                span={
                    main.env.cohorts && main.env.cohorts.length > 0 && isCohort
                        ? 12
                        : 24
                }
            >
                <Form.Item
                    required
                    label={formatMessage({ id: "common.role" })}
                    name={"role"}
                >
                    <Select
                        options={roles.map((role: any) => {
                            return { label: role.role, value: role.role_id };
                        })}
                        onChange={onSelectRole}
                    />
                </Form.Item>
            </Col>
            {isCohort && main.env.cohorts && main.env.cohorts.length > 0 && (
                <Col span={12}>
                    {
                        main.project.info.type === 3 && !inRandomIsolation(main.project.info.number) && type != null && type === 14 ?
                            null
                            :
                            <Form.Item
                                rules={[{ required: cohortRequired }]}
                                labelCol={{
                                    style: { width: g.lang === "en" ? 150 : 60 },
                                }}
                                label={
                                    main.project.info.type === 2
                                        ? formatMessage({ id: "projects.second" })
                                        : formatMessage({ id: "projects.third" })
                                }
                                name={"cohort"}
                            >
                                {radio ? (
                                    <Select
                                        placeholder={formatMessage({
                                            id: types ? "common.all" : "common.select",
                                        })}
                                        options={main.env.cohorts.map((item: any) => {
                                            return { label: item.name, value: item.id };
                                        })}
                                        onChange={onSelectCohort}
                                    />
                                ) : (
                                    <Select
                                        placeholder={formatMessage({
                                            id: "common.all",
                                        })}
                                        mode="multiple"
                                        options={main.env.cohorts.map((item: any) => {
                                            return { label: item.name, value: item.id };
                                        })}
                                        onChange={onSelectCohort}
                                    />
                                )}
                            </Form.Item>
                    }
                </Col>
            )}
        </Row>
    );
};

interface SelectProjectRoleProps {
    // roles: any;
    form: any;
    // selectSite?: any;
    projectRoles: any;
    setProjectRoleCheckKeys: any;
    projectRoleCheckKeys: any;
    setProjectRoleSearchKey: any;
    projectRoleSearchKey: any;
}

const SelectProjectRole = (props: SelectProjectRoleProps) => {
    const {
        form,
        // selectSite,
        projectRoles,
        setProjectRoleCheckKeys,
        projectRoleCheckKeys,
        setProjectRoleSearchKey,
        projectRoleSearchKey,
    } = props;
    const intl = useIntl();
    const { formatMessage } = intl;

    return (
        <div style={{ height: 359 }}>
            <div
                style={{
                    width: "100%",
                    border: "1px solid #DDDEDF",
                    borderBottom: "unset",
                }}
            >
                <div
                    style={{
                        height: 36,
                        paddingLeft: 13,
                        borderBottom: "1px solid #DDDEDF",
                        lineHeight: "36px",
                    }}
                >
                    <Checkbox
                        onChange={(e) => {
                            if (e.target.checked) {
                                setProjectRoleCheckKeys(
                                    projectRoles.map((role: any) => role.id)
                                );
                                form.setFieldsValue({
                                    ...form.getFieldsValue,
                                    site: projectRoles.map((role: any) => role.id),
                                });
                            } else {
                                setProjectRoleCheckKeys([]);
                                form.setFieldsValue({
                                    ...form.getFieldsValue,
                                    site: [],
                                });
                            }
                        }}
                        indeterminate={
                            projectRoleCheckKeys.length > 0 &&
                            projectRoleCheckKeys.length < projectRoles.length
                        }
                        checked={projectRoleCheckKeys.length > 0}
                    >
                        {formatMessage({ id: "report.role" })}
                        <span style={{ fontSize: 12, color: "#aeb2b9" }}>
                            ({projectRoleCheckKeys.length}/{projectRoles.length}
                            {formatMessage({ id: "report.attributes.unit" })})
                        </span>
                    </Checkbox>
                </div>
                <div style={{ paddingTop: "8px" }}>
                    <div style={{ margin: "0 12px" }}>
                        <Input
                            placeholder={formatMessage({
                                id: "common.required.prefix",
                            })}
                            suffix={
                                <SearchOutlined style={{ color: "#BFBFBF" }} />
                            }
                            onChange={(e) => {
                                setProjectRoleSearchKey(e.target.value);
                            }}
                        />
                    </div>
                    <Form.Item
                        name={"site"}
                        style={{
                            height: "283px",
                            overflow: "auto",
                            marginBottom: "0",
                            paddingBottom: "16px",
                        }}
                    >
                        <Checkbox.Group
                            style={{ width: "100%", paddingTop: 8 }}
                            onChange={(e) => {
                                setProjectRoleCheckKeys(e);
                            }}
                        >
                            {projectRoles.map((role: any) => {
                                if (
                                    projectRoleSearchKey &&
                                    role.name.indexOf(projectRoleSearchKey) === -1
                                )
                                    return null;
                                return (
                                    <Row
                                        key={role.id}
                                        style={{
                                            minHeight: "32px",
                                            // lineHeight: "32px",
                                            padding: "0 12px",
                                        }}
                                    >
                                        <Checkbox
                                            value={role.id}
                                            style={{ marginRight: "8px" }}
                                        />
                                        {role.name}
                                    </Row>
                                );
                            })}
                        </Checkbox.Group>
                    </Form.Item>
                </div>
            </div>
        </div>
    );
};

interface SelectSiteProps {
    roles: any;
    form: any;
    selectSite?: any;
    userSites: any;
    setUserSitesCheckKeys: any;
    userSitesCheckKeys: any;
    setUserSiteSearchKey: any;
    userSiteSearchKey: any;
}

const SelectSite = (props: SelectSiteProps) => {
    const {
        form,
        selectSite,
        userSites,
        setUserSitesCheckKeys,
        userSitesCheckKeys,
        setUserSiteSearchKey,
        userSiteSearchKey,
    } = props;
    const intl = useIntl();
    const { formatMessage } = intl;

    return (
        <div style={{ height: 359 }}>
            <div
                style={{
                    width: "100%",
                    border: "1px solid #DDDEDF",
                    borderBottom: "unset",
                }}
            >
                <div
                    style={{
                        height: 36,
                        paddingLeft: 13,
                        borderBottom: "1px solid #DDDEDF",
                        lineHeight: "36px",
                    }}
                >
                    <Checkbox
                        onChange={(e) => {
                            if (e.target.checked) {
                                setUserSitesCheckKeys(
                                    userSites.map((site: any) => site.id)
                                );
                                form.setFieldsValue({
                                    ...form.getFieldsValue,
                                    site: userSites.map((site: any) => site.id),
                                });
                            } else {
                                setUserSitesCheckKeys([]);
                                form.setFieldsValue({
                                    ...form.getFieldsValue,
                                    site: [],
                                });
                            }
                        }}
                        indeterminate={
                            userSitesCheckKeys.length > 0 &&
                            userSitesCheckKeys.length < userSites.length
                        }
                        checked={userSitesCheckKeys.length > 0}
                    >
                        {formatMessage({ id: "common.site" })}
                        <span style={{ fontSize: 12, color: "#aeb2b9" }}>
                            ({userSitesCheckKeys.length}/{userSites.length}
                            {formatMessage({ id: "report.attributes.unit" })})
                        </span>
                    </Checkbox>
                </div>
                <div style={{ paddingTop: "8px" }}>
                    <div style={{ margin: "0 12px" }}>
                        <Input
                            placeholder={formatMessage({
                                id: "common.required.prefix",
                            })}
                            suffix={
                                <SearchOutlined style={{ color: "#BFBFBF" }} />
                            }
                            onChange={(e) => {
                                setUserSiteSearchKey(e.target.value);
                            }}
                        />
                    </div>
                    <Form.Item
                        name={"site"}
                        style={{
                            height: "283px",
                            overflow: "auto",
                            marginBottom: "0",
                            paddingBottom: "16px",
                        }}
                    >
                        <Checkbox.Group
                            style={{ width: "100%", paddingTop: 8 }}
                            onChange={(e) => {
                                setUserSitesCheckKeys(e);
                            }}
                        >
                            {userSites.map((site: any) => {
                                if (
                                    userSiteSearchKey &&
                                    site.name.indexOf(userSiteSearchKey) === -1
                                )
                                    return null;
                                return (
                                    <Row
                                        key={site.id}
                                        style={{
                                            minHeight: "32px",
                                            // lineHeight: "32px",
                                            padding: "0 12px",
                                        }}
                                    >
                                        <Checkbox
                                            value={site.id}
                                            style={{ marginRight: "8px" }}
                                        />
                                        {site.number}-{site.name}
                                    </Row>
                                );
                            })}
                        </Checkbox.Group>
                    </Form.Item>
                </div>
            </div>
        </div>
    );
};

interface templateProps {
    form: any;
    defaultFields: any;
    customTemplates: any;
    setPreviewFields: any;
    previewFields: any;
    hidePreview?: boolean;
    isBindRoleF?: boolean;
    type?: any;
}

const onChangeTemplate = (
    value: any,
    option: any,
    props: templateProps,
    main: any
) => {
    props.form.setFieldValue("template", value);
    var fields = option.fields.map((field: any) => ({ key: field }));
    let { isBindRoleF } = props;

    if (main.project.info.type === 1) {
        fields = fields?.filter(
            (field: any) =>
                field.key !== "report.attributes.random.stage" &&
                field.key !== "report.attributes.random.cohort"
        );
    } else if (main.project.info.type === 2) {
        fields = fields?.filter(
            (field: any) => field.key !== "report.attributes.random.stage"
        );
    } else if (main.project.info.type === 3) {
        fields = fields?.filter(
            (field: any) => field.key !== "report.attributes.random.cohort"
        );
    }

    if (
        (main.project.info.type === 2 || main.project.info.type === 3) &&
        props.type === 18 &&
        !isBindRoleF
    ) {
        //库房单品报表，如何项目是非盲项目，去掉字段
        fields = fields?.filter(
            (field: any) =>
                field.key !== "report.attributes.random.stage" &&
                field.key !== "report.attributes.random.cohort"
        );
    }
    props.setPreviewFields(fields);
};

const TemplateItem = (props: templateProps) => {
    const intl = useIntl();
    const main = useMain();
    const {
        form,
        defaultFields,
        customTemplates,
        setPreviewFields,
        previewFields,
        hidePreview,
        isBindRoleF,
    } = props;
    const { formatMessage } = intl;
    return (
        <Form.Item
            label={formatMessage({ id: "report.template" })}
            name="template"
        >
            <Space.Compact block>
                <Select
                    defaultValue={
                        form.getFieldValue("template") ||
                        formatMessage({ id: "report.template.name.default" })
                    }
                    options={[
                        {
                            label: formatMessage({
                                id: "report.template.name.default",
                            }),
                            value: null,
                            fields: defaultFields,
                        },
                        ...customTemplates?.map((template: any) => {
                            return {
                                label: template.name,
                                value: template.id,
                                fields: template.fields,
                            };
                        }),
                    ]}
                    onChange={(value, option: any) => {
                        onChangeTemplate(value, option, props, main);
                    }}
                    disabled={customTemplates?.length === 0}
                />
                {hidePreview ? null : (
                    <Popover
                        arrowPointAtCenter
                        color={"white"}
                        placement={"topRight"}
                        overlayClassName={"field-picker"}
                        overlayStyle={{ maxWidth: 800 }}
                        content={
                            previewFields ? (
                                <PreviewFieldsShow
                                    previewFields={previewFields}
                                    type={props.type}
                                />
                            ) : null
                        }
                    >
                        <Button icon={<i className="iconfont icon-yulan" />} />
                    </Popover>
                )}
            </Space.Compact>
        </Form.Item>
    );
};


interface previewFieldsProps {
    previewFields: any;
    type?: any;
}

const PreviewFieldsShow = (props: previewFieldsProps) => {
    const intl = useIntl();
    const { previewFields, type } = props;
    const { formatMessage } = intl;
    const main = useMain();
    const report = useReport();

    const changeKey = (field: any) => {
        if (main.project.info.type === 1 &&
            field.key ===
            "report.attributes.info.subject.number") {
            return <PreviewContent>
                {main.attr?.info.subjectReplaceText}
            </PreviewContent>
        }
        if (
            field.key ===
            "report.attributes.dispensing.out-visit-dispensing.reason") {
            return <PreviewContent>
                {report.outVisitStr + formatMessage({ id: "common.reason" })}
            </PreviewContent>
        }

        if (
            field.key ===
            "report.attributes.dispensing.out-visit-dispensing.remark") {
            return <PreviewContent>
                {report.outVisitStr +"-"+ formatMessage({ id: "subject.unblinding.reason.remark" })}
            </PreviewContent>
        }


        return <PreviewContent>
            <span
                style={{
                    textDecoration:
                        ((field.key === "report.attributes.random.number" && !report.showRandomNumber && report.type !== 12)
                            ||
                            (field.key === "report.attributes.dispensing.medicine.real.group" && !report.showRegisterGroup))
                            ? "line-through"
                            : "",
                }}
            >
                {
                    formatMessage({ id: field.key })
                }
            </span>

        </PreviewContent>

    }

    return (
        <>

            {previewFields.length !== 0 ? (
                type === 2 && main.project.info.type === 3 && report.selectCohort?.length > 0?
                    <Preview>
                        {
                            report.selectCohort[0]?.lastId === "000000000000000000000000" ?
                                previewFields.filter((field: any) => (field.key !== "export.random.toBeRandom")).map((field: any) => {
                                    return (
                                        changeKey(field)
                                    );
                                })
                                :
                                previewFields.filter((field: any) => (
                                    !(
                                        field.key === "subject.register" ||
                                        field.key === "subject.status.screen.success" ||
                                        field.key === "subject.status.screen.fail"
                                    )

                                )).map((field: any) => {
                                    return (
                                        changeKey(field)
                                    );
                                })


                        }
                    </Preview>
                    :
                    <Preview>
                        {previewFields.map((field: any) => {
                            return (
                                changeKey(field)
                            );
                        })}
                    </Preview>
            ) : (
                type === 18 || type === 19 || type === 4 || type === 15 ?
                    <Tabs>
                        {report.previewFieldsMany.map((fields: any, index: any) => {
                            return (
                                <Tabs.TabPane
                                    tab={"sheet" + (index + 1)}

                                    key={index}
                                >
                                    <Preview>
                                        {index === 0 ?
                                            previewFields.filter((field: any) => (field.key !== "report.attributes.research.other") && ((type === 4 && field.key !== "report.attributes.info.storehouse.name") || type !== 4)).map((field: any) => {
                                                return (
                                                    <PreviewContent>
                                                        <span
                                                            style={{
                                                                textDecoration:
                                                                    ((field.key === "report.attributes.random.number" && !report.showRandomNumber)
                                                                        ||
                                                                        (field.key === "report.attributes.dispensing.medicine.real.group" && !report.showRegisterGroup))
                                                                        ? "line-through"
                                                                        : "",
                                                            }}
                                                        >
                                                            {formatMessage({
                                                                id: field.key,
                                                            })}
                                                        </span>
                                                    </PreviewContent>
                                                );
                                            }) :
                                            previewFields.filter((field: any) => (field.key !== "report.attributes.research.medicine.number" && field.key !== "report.attributes.research.packageNumber")).map((field: any) => {
                                                return (
                                                    <PreviewContent>
                                                        <span
                                                            style={{
                                                                textDecoration:
                                                                    ((field.key === "report.attributes.random.number" && !report.showRandomNumber)
                                                                        ||
                                                                        (field.key === "report.attributes.dispensing.medicine.real.group" && !report.showRegisterGroup))
                                                                        ? "line-through"
                                                                        : "",
                                                            }}
                                                        >
                                                            {formatMessage({
                                                                id: field.key,
                                                            })}
                                                        </span>
                                                    </PreviewContent>
                                                );
                                            })
                                        }

                                    </Preview>
                                </Tabs.TabPane>
                            );
                        })}
                    </Tabs>
                    :
                    <Tabs>
                        {report.previewFieldsMany.map((fields: any, index: any) => {
                            return (
                                <Tabs.TabPane
                                    tab={"sheet" + (index + 1)}
                                    key={index}
                                >
                                    <Preview>
                                        {fields.map((field: any) => {
                                            return (
                                                <PreviewContent>
                                                    <span
                                                        style={{
                                                            textDecoration:
                                                                ((field.key === "report.attributes.random.number" && !report.showRandomNumber)
                                                                    ||
                                                                    (field.key === "report.attributes.dispensing.medicine.real.group" && !report.showRegisterGroup))
                                                                    ? "line-through"
                                                                    : "",
                                                        }}
                                                    >
                                                        {formatMessage({
                                                            id: field.key,
                                                        })}
                                                    </span>
                                                </PreviewContent>
                                            );
                                        })}
                                    </Preview>
                                </Tabs.TabPane>
                            );
                        })}
                    </Tabs>
            )
            }
        </>
    );
};

const CustomModal = styled(Modal)`
    .ant-modal-body {
        padding-bottom: 0;
    }
`;
