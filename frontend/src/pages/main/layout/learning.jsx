import React from "react";
import {Badge, Divider, message, Tooltip} from "antd";
import FormattedMessage from "react-intl/src/components/message";
import {useAuth} from "../../../context/auth";
import {useFetch} from "../../../hooks/request";
import {getLearn} from "../../../api/user";
import {useGlobal} from "../../../context/global";

export const Learning = () => {
    const auth = useAuth();
    const g = useGlobal();

    //所有项目都未开启系统课程和项目课程学习
    let sign = false;
    if (sessionStorage.getItem("projectMark") == null || sessionStorage.getItem("systemMark") ==  null){
        const {data: result, runAsync: learnRunAsync} = useFetch(()=>getLearn({"project_id":auth.project? auth.project.id:"", "env_id":auth.env? auth.env.id:""}), {manual: true, refreshOnWindowFocus: true, focusTimespan: 1000});
        if(result != null && result != undefined){
            if (result.data.courses > 0){
                sign = true;
            }
            sessionStorage.setItem("learn_url", result.data.url);
            sessionStorage.setItem("learn_token", result.data.token);
        }
    }

    return (
        <>
            <div
                style={{ display: "flex", marginRight: "0px", position: "relative" }}
                onClick={() => {
                    // console.log("learn_token:",sessionStorage.getItem("learn_token"),"learn_url:",sessionStorage.getItem("learn_url"))
                    window.open(sessionStorage.getItem("learn_url") + "?t=" + sessionStorage.getItem("learn_token")+"&lang="+g.lang);
                    sessionStorage.removeItem("completeLearning");
                    // @ts-ignore
                    window.location = "";
                }}
            >
                <Badge dot={sessionStorage.getItem("completeLearning") === "1" || sign} style={{ position: "absolute", left: "14px", top: "3px" }} />
                <Tooltip
                    overlayInnerStyle={{ width: 75 }}
                    placement="top"
                    title={<FormattedMessage id="user.customer.learnTrain" />}
                >
                    <svg offset={[-8]} className="iconfont mouse" width={22} height={22}>
                        <use xlinkHref="#icon-peixunxuexi"/>
                    </svg>
                </Tooltip>
            </div>
            <Divider type="vertical" style={{border: "0.5px solid #f0f0f0"}}/>
        </>
    );
};
