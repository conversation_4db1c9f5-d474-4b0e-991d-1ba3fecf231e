import {useUpdateEffect, useWebSocket} from "ahooks";
import {useLocation, useNavigate,} from "react-router-dom";
import {useAuth} from "context/auth";
import {useIntl} from "react-intl";
import { message } from "antd";
import { Header } from "../../src/pages/project/layout/header";
import { getUserProjectEnvironmentRoles } from "../api/user";
import { useFetch } from "hooks/request";
import { menuSelect, permissions } from "../../src/tools/permission";

export const WSIrt = () => {

    const location = useLocation();
    const navigate = useNavigate(); // 创建一个导航函数
    const intl = useIntl();
    const token = sessionStorage.getItem("token");
    const auth = useAuth();
    

    const { readyState, sendMessage, latestMessage, disconnect, connect } = useWebSocket(
        `${
            window.location.hostname === "localhost"
                ? "ws://localhost:8080"
                : `wss://${window.location.host}`
        }/ws/${sessionStorage.getItem("token")}/connect`,
        // `wss://${new URL(auth.cloudUrl).hostname}/ws/${sessionStorage.getItem("token")}/connect`,
        {
            reconnectLimit: 20,
            reconnectInterval: 30000,
        }
    );

    // const { readyState, sendMessage, latestMessage, disconnect, connect } = useWebSocket(`ws://localhost:8080/ws/${sessionStorage.getItem("token")}/connect`, {
    //         onOpen: () => console.log("✅ WebSocket连接成功"),
    //         onError: (err) => console.error("❌ WebSocket连接失败:", err),
    //         onMessage: (msg) => console.log("📨 收到原始消息:", msg.data), // 监听所有消息
    //         // 其他配置...
    //         reconnectLimit: 20,
    //         reconnectInterval: 30000,
    //     }
    // );

    //  权限接口
    const { runAsync: roleRunAsync } = useFetch(getUserProjectEnvironmentRoles, { manual: true })


    useUpdateEffect(
        () => {
            // 解析成对象
            if(latestMessage?.data){
                const messageObj = JSON.parse(latestMessage?.data);
                const customerId = messageObj.customerId;
                const projectId = messageObj.projectId;
                const envId = messageObj.envId;
                const roleId = messageObj.roleId;
                // 获取 text 字段
                const text = messageObj.text;
                const userId = messageObj.userId;
                switch (text) {
                    case "unbindUser":
                        if(auth.customerId && auth.env?.id && envId && auth.env?.id === envId){
                            message.info(intl.formatMessage({id: "tips.user.invalid"}));
                            setTimeout(() => { 
                                navigate('/'); // 替换 '/target-page' 为目标页面的路径
                            }, 3000)
                        } else if(location.pathname==="/"){
                            message.info(intl.formatMessage({id: "tips.user.invalid"}));
                            setTimeout(() => { 
                                // window.location.reload();
                                const timestamp = new Date().getTime();
                                const newUrl = window.location.origin + window.location.pathname + '?_=' + timestamp + window.location.hash;
                                window.location.href = newUrl;
                            }, 3000)
                        } 
                        break;
                    case "closeUser":
                        if(auth.env?.id && auth.customerId && customerId && auth.customerId === customerId && auth.user?.id && userId && auth.user?.id === userId){
                            message.info(intl.formatMessage({id: "tips.user.invalid"}));
                            setTimeout(() => { 
                                navigate('/'); // 替换 '/target-page' 为目标页面的路径
                            }, 3000)
                        } else if(location.pathname==="/" && auth.user?.id && userId && auth.user?.id === userId){
                            message.info(intl.formatMessage({id: "tips.user.invalid"}));
                            setTimeout(() => { 
                                // window.location.reload();
                                const timestamp = new Date().getTime();
                                const newUrl = window.location.origin + window.location.pathname + '?_=' + timestamp + window.location.hash;
                                window.location.href = newUrl;
                            }, 3000)
                        }
                        break;
                    case "refreshPermissions":
                        if(auth.customerId && auth.project?.id && projectId && auth.project.permissions.role_id && roleId && auth.user?.id && userId &&
                            auth.project?.id === projectId && auth.project.permissions.role_id === roleId && auth.user?.id === userId){
                            message.info(intl.formatMessage({id: "tips.user.Permission"}));
                            setTimeout(() => { 
                                let permissions = {}
                                roleRunAsync({
                                    customerId: auth.customerId,
                                    projectId: auth.project?.id,
                                    envId: auth.env.id,
                                    // cohortId: auth.env.cohort?.id
                                }).then(
                                    (result: any) => {
                                        let roleList = result.data.filter((it: any) => it.name !== "Customer-Admin" && it.name !== "Project-Admin" && it.name !== "Sys-Admin" && it.page !== undefined)
                                        let permission = roleList.find((item: any) => auth.project.permissions.role === item.role)
                                        if (permission) {
                                            permissions = permission
                                        } else {
                                            permissions = roleList[0]

                                        }
                                        auth.project.permissions = permissions
                                        //  修改权限 project
                                        auth.setProject(auth.project);
                                        //  修改 env
                                        auth.setEnv(auth.env)
                                        sessionStorage.setItem("current_project", JSON.stringify(auth.project))
                                        sessionStorage.setItem("env", JSON.stringify(auth.env));

                                        const token = sessionStorage.getItem("token");
                                        if (token) {
                                            sessionStorage.setItem("token", token);
                                        }

                                        const timestamp = new Date().getTime();
                                        const newUrl = window.location.origin + window.location.pathname + '?_=' + timestamp + window.location.hash;
                                        window.location.href = newUrl;
                                        
                                        //  重新加载页面
                                        // let path = menuSelect(permissions)
                                        // navigate(path);
                                    }
                                )
                            }, 3000)
                            // <Header />
                        }
                        break;
                    default:
                        break;
                }
            }
        },
        [latestMessage]
    );

    return (
        <>
        </>
    );
};
