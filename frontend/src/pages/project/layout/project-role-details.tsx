import React from "react"
import {Form, Modal, Radio} from "antd";
import {useIntl} from "react-intl";
import {QuestionCircleFilled} from "@ant-design/icons";
import useSafeState from "ahooks/lib/useSafeState";

export const ProjectRoleDetails = (props:any) => {
    const intl = useIntl();
    const {formatMessage} = intl;
    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>(null);
    const show = (record: any) => {
        setVisible(true);
        setRecord(record);
    };

    const hide = () => {
        setVisible(false);
        setRecord(null);
    };

    React.useImperativeHandle(props.bind, () => ({show}));
    return (
        <>
            <Modal
                destroyOnClose={true}
                width={500}
                title={formatMessage({ id: 'common.detail' })}
                visible={visible}
                onCancel={hide}
                footer={null}
                centered
                maskClosable={false}
            >
                <Form>
                    <Form.Item label={formatMessage({id: "roles.name"})} >
                        {record?.name}
                    </Form.Item>
                    <Form.Item label={formatMessage({id: "common.classification"})}
                               tooltip={{
                                   title:
                                       <>
                                           {formatMessage({id: 'permission.scope.study'})}<br/>
                                           {formatMessage({id: 'permission.scope.depot'})}<br/>
                                           {formatMessage({id: 'permission.scope.site'})}
                                       </>
                                   ,
                                   icon: <QuestionCircleFilled style={{color: "#D0D0D0"}}/>
                               }}>
                        {record?.scope}
                    </Form.Item>
                    <Form.Item label={formatMessage({id: "common.status"})} initialValue={1}>
                        <Radio.Group disabled={true} defaultValue={record?.status}>
                            <Radio value={1}>{formatMessage({id: "common.effective"})}</Radio>
                            <Radio value={2}>{formatMessage({id: "common.invalid"})}</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: "common.description"})}>
                        {
                            record?.description == "" ?
                                "-"
                            :
                                record?.description
                        }
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}