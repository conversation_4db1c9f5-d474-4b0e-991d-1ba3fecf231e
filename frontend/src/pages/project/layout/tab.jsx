import React, { useRef } from "react";
import {
    CaretDownOutlined,
    CaretRightOutlined,
    QuestionCircleFilled,
    SearchOutlined
} from "@ant-design/icons";
import {
    Button,
    Checkbox,
    Col,
    Divider,
    Empty,
    Form,
    Input,
    Layout,
    Modal,
    Radio,
    Row, Select, Spin,
    Switch,
    Table,
    Tabs,
    Tooltip, Typography,
} from "antd";
import styled from "@emotion/styled";
import { useMount, useSafeState } from "ahooks";
import { useAuth } from "../../../context/auth";
import { useGlobal } from "../../../context/global";
import { Title as CustomTitle } from "../../../components/title";
import useIntl from "react-intl/src/components/useIntl";
import FormattedMessage from "react-intl/src/components/message";
import { projectTypes, projectUtc } from "../../../data/data";
import { useFetch } from "../../../hooks/request";
import { projectCard } from "../../../api/projects";
import moment from "moment";
import { list, listPool } from "../../../api/projects_roles";
import { permissions } from "../../../tools/permission";
import EmptyImg from "../../../images/empty.png";
import { VisitNotice } from "./visit-notice";
import { InsertDivider } from "../../../components/divider";
import { ProjectRoleDetails } from "./project-role-details";
import { ProjectRoleAuthority } from "./project-role-authority";
import { all } from "../../../api/menu";
import { UnblindingControlView } from "./unblinding";
import { timezones } from "../../../data/timezone";
// import { Timezone } from "../../../types/timezone";
import { getTimezoneOffset, getTimezonesWithOffsets } from "../../../utils/timezones";
import { SelectUI } from "../../../components/SelectUi";

export const Tab = (props) => {
    const searchInput = useRef();
    const g = useGlobal();
    const intl = useIntl();
    const { formatMessage } = intl;
    const auth = useAuth();
    const { TabPane } = Tabs;
    const { Text } = Typography;

    const project_role_details_use = React.useRef();
    const project_role_authority_use = React.useRef();

    const [tzOptions, setTzOptions] = useSafeState([]);
    const [tzs, setTzs] = useSafeState([]);

    const [isModalVisible, setIsModalVisible] = useSafeState(false);
    const [administrators, setAdministrators] = useSafeState([]);
    const [menu, setMenu] = useSafeState([]);
    const [rolesPool, setRolesPool] = useSafeState([]);
    const [projectRolePermissions, setProjectRolePermissions] = useSafeState([]);
    const [changeKey, setChangeKey] = useSafeState(null);
    const [envStr, setEnvStr] = useSafeState("");
    const [projectData, setProjectData] = useSafeState({
        id: "",
        status: 0,
        customerName: "",
        envs: [{
            id: "",
            lockConfig: false,
            name: "",
            cohorts: []
        }],
        info: {
            type: 0,
            number: "",
            name: "",
            sponsor: "",
            phone: "",
            timeZone: 0,
            timeZoneStr: "0",
            connectEdc: 0,
            pushMode: 0,
            synchronizationMode: 0,
            edcUrl: "",
            description: "",
            connectLearning: 0,
            needLearning: 0,
            needLearningEnv: [],
            orderCheck: 0,
            orderConfirmation: 0,
            deIsolationApproval: 0,
            researchAttribute: 0,
            unblindingControl: 0,
            unblindingType: 0,
            unblindingSms: 0,
            unblindingProcess: 0,
            unblindingCode: 0,
            pvUnblindingType: 0,
            pvUnblindingSms: 0,
            pvUnblindingProcess: 0,
            orderApprovalControl: 0,
            orderApprovalSms: 0,
            orderApprovalProcess: 0,
            startTime: 0,
            endTime: 0,
            statusReason: 0,
            edcSupplier: null,
            visitRandomization: [],
        },
    });

    const { runAsync: getProjectCardRun, loading: getProjectCardLoading } = useFetch(projectCard, { manual: true });
    const { runAsync: getRoleList, loading: listLoading } = useFetch(list, { manual: true });
    const { runAsync: allRun, loading: allLoading } = useFetch(all, { manual: true });
    const { runAsync: listPoolRun, } = useFetch(listPool, { manual: true });

    const show = () => {
        projectInfoCard();
        getList("");
        setIsModalVisible(true);
        allRun().then((result) => {
            setMenu(result.data);
        });
        listPoolRun({ template: projectData.info.researchAttribute === 0 ? 1 : 2 }).then((result) => {
            setRolesPool(result.data);
        });

    };

    const handleClose = () => {
        setProjectRolePermissions([]);
        setAdministrators([]);
        setIsModalVisible(false);
        setEnvStr("");
        setChangeKey(1)
    };

    // 查询项目信息
    const projectInfoCard = () => {
        getProjectCardRun({
            id: auth.project.id
        }).then((res) => {
            if (res.code === 0) {
                //所属客户
                let customerName = res.data.customer.name;
                res.data.project.customerName = customerName;
                let envs = [];
                res.data.project.envs.forEach((env) => {
                    if (env.id === auth.env.id) {
                        envs.push(env);
                    }
                });
                res.data.project.envs = envs;

                setProjectData(res.data.project);
                setAdministrators(res.data.administrators);
                // 对接eLearning 环境
                if (res.data.project.info.needLearningEnv != null && res.data.project.info.needLearningEnv != "" && res.data.project.info.needLearningEnv.length > 0) {
                    let envStr = ""
                    res.data.project.info.needLearningEnv.forEach((env) => {
                        envStr += env + " ";
                    })
                    setEnvStr(envStr);
                };
            };
        }
        );
    };

    const getList = (keyword) => {
        getRoleList({ projectId: auth.project.id, "keywords": keyword, all: "1" }).then(
            (result) => {
                setProjectRolePermissions(result.data);
            }
        );
    };

    const onSearch = (e) => {
        getList(e.target.value);
    };

    // 项目环境
    const commonRender = v => v ? v : '-';
    const columns = () => {
        const columns = [
            {
                title: formatMessage({ id: 'projects.envs.name' }),
                dataIndex: 'name',
                ellipsis: true,
                render: commonRender
            }
        ]
        if (projectData?.info.type === 1) {
            columns.push(
                {
                    title: formatMessage({ id: 'projects.envs.cohorts.capacity' }),
                    dataIndex: 'capacity',
                    render: commonRender
                },
                {
                    title: formatMessage({ id: 'projects.envs.cohorts.reminder.thresholds' }),
                    dataIndex: 'reminderThresholds',
                    render: (v, r) => v ? v + '%' : '-'
                }
            )
        }
        return columns
    };

    const expandedProps = {
        expandIcon: ({ expanded, onExpand, record }) =>
            expanded ? (
                <CaretDownOutlined onClick={e => onExpand(record, e)} />
            ) : (
                <CaretRightOutlined onClick={e => onExpand(record, e)} />
            ),

        expandedRowRender: (record) => (
            <ExpandTable size='small' dataSource={record.cohorts || []} pagination={false}
                rowKey={(record) => (record.id)}
                columns={expandableColumns(projectData, record)}
            />
        )
    };

    const expandableColumns = (project, record) => {
        const columns = [
            {
                title: formatMessage({ id: 'common.serial' }),
                key: '#',
                width: 60,
                ellipsis: true,
                render: (v, r, i) => i + 1
            },
            {
                title: formatMessage({ id: 'common.name' }),
                dataIndex: 'name',
                ellipsis: true,
                render: commonRender
            },
            {
                title: formatMessage({ id: 'projects.envs.cohorts.capacity' }),
                dataIndex: 'capacity',
                width: 100,
                ellipsis: true,
                render: commonRender
            },
            {
                title: formatMessage({ id: 'common.status' }),
                key: 'status',
                width: 100,
                ellipsis: true,
                render: v => v ? formatMessage({ id: `projects.envs.cohorts.status${v.status}` }) : '-'
            },
            {
                title: formatMessage({ id: 'projects.envs.cohorts.reminder.thresholds' }),
                dataIndex: 'reminderThresholds',
                render: (v, r) => v ? v + '%' : '-'
            }
        ];
        const factor = {
            title: formatMessage({ id: 'projects.envs.cohorts.factor' }),
            dataIndex: 'factor',
            width: 100,
            ellipsis: true,
            render: commonRender
        };
        const stage = {
            title: formatMessage({ id: "projects.envs.cohorts.stage" }),
            key: "stage",
            width: 100,
            ellipsis: true,
            render: (v, r) =>
                r.lastId && record.cohorts?.find(it => it.id === r.lastId)?.name || "-"
        };
        if (project.info.connectEdc === 1 && project.info.type === 3) {
            if (project.info.pushMode === 1) {
                columns.splice(2, 0, factor)
            }
            columns.splice(4, 0, stage)
        } else if (project.info.connectEdc === 1) {
            if (project.info.pushMode === 1) {
                columns.splice(2, 0, factor)
            }
        } else if (project.info.type === 3) {
            columns.splice(3, 0, stage)
        }
        return columns
    };


    // 时区
    const getTimeZone = (timeZone) => {
        return projectUtc.find(it => it.value === timeZone)?.label || "-";
    };

    useMount(() => {
        setTzOptions(timezones.map((it) => ({ ...it, offset: getTimezoneOffset(it.location) })));
        setTzs(getTimezonesWithOffsets());
    });

    // 业务功能table筛选
    const getColumnSearchProps = (dataIndex) => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
            <div style={{ padding: 8 }}>
                <Input.Search
                    ref={searchInput}
                    placeholder={formatMessage({ id: dataIndex === "email" ? 'placeholder.input.email' : 'placeholder.input.name' })}
                    value={selectedKeys[0]}
                    onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onSearch={confirm}
                    onPressEnter={confirm}
                    enterButton
                />
            </div>
        ),
        filterIcon: (filtered) => (
            <SearchOutlined style={{ color: filtered ? '#1890ff' : '#677283' }} />
        ),
        onFilter: (value, record) =>
            record.info[dataIndex]
                .toString()
                .toLowerCase()
                .includes((value).toLowerCase()),
        onFilterDropdownOpenChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current?.select(), 100);
            }
        },
    });

    // const roleConfig = (record) => {
    //     ctx.setRole(record)
    //     let tree = getChildren(ctx.menu, record)
    //     ctx.setTree([...tree])
    //     ctx.setPermissionsSet(new Set(record.permissions))
    //     let role = ctx.rolesPool.find((it) => it.name === record.name)
    //     if (!role) {
    //         ctx.setDisableData([])
    //         ctx.setRoleConfigVisible(true)
    //         return
    //     }
    //     let disableData = permissionsDisable.find((it) => it.type === role.type)
    //     if (disableData) {
    //         ctx.setDisableData(disableData.disables)
    //     } else {
    //         ctx.setDisableData([])
    //     }
    //     ctx.setRoleConfigVisible(true)
    // }

    const operationBtn = (record) => {
        let btns = []
        // if (projectSettingPermission(props.project, auth, "operation.projects.main.setting.permission.edit")){
        btns.push(<Button style={{ borderLeft: 0, paddingLeft: 0 }} size="small" type="link" onClick={() => {
            projectRoleDetails(record);
        }}> <FormattedMessage id={"common.detail"} /></Button>)
        // }
        // if (projectSettingPermission(props.project, auth, "operation.projects.main.setting.permission.setting")){
        btns.push(<Button size="small" type="link" onClick={() => {
            projectRoleAuthority(record);
        }}><FormattedMessage id={"common.permission"} /></Button>)
        // }
        return InsertDivider(btns)
    };

    const projectRoleDetails = (record) => {
        project_role_details_use.current.show(record);
    };

    const projectRoleAuthority = (record) => {
        project_role_authority_use.current.show(record, menu, rolesPool, projectData);
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const dockingLayout = {
        labelCol: {
            style: { width: g.lang === "en" ? "136px" : "112px", textAlign: "right" },
        },
    };

    const getOptions = () => {
        const baseOptions = [
            {
                value: 1,
                label: formatMessage({ id: "projects.subject.stratification" }),
            },
            {
                value: 2,
                label: formatMessage({ id: "projects.subject.form" }),
            },
        ];

        if (projectData.info.type === 2 || projectData.info.type === 3) {
            baseOptions.push({
                value: 3,
                label: projectCtx.projectType === 3
                    ? formatMessage({ id: "projects.subject.stageName" })
                    : formatMessage({ id: "projects.subject.cohortName" }),
            });
        }

        return baseOptions;
    };

    return (
        <StyledModal
            destroyOnClose={true}
            title={<FormattedMessage id="menu.projects.main.setting.project" />}
            open={isModalVisible}
            bodyStyle={{
                display: "flex",
                paddingRight: 0,
                overflow: "hidden",
            }}
            // className="custom-large-modal"
            className="custom-medium-modal-batch"
            centered
            maskClosable={false}
            footer={null}
            onCancel={handleClose}
        >
            <Form
                style={{ flex: 1, width: "100%" }}
            >
                <Spin spinning={getProjectCardLoading}>
                    <Container lang={g.lang}>

                        {
                            permissions(auth.project.permissions, "operation.projects.project.basic.information.view")
                                || permissions(auth.project.permissions, "operation.projects.project.basic.environment.view")
                                || permissions(auth.project.permissions, "operation.projects.project.business.functions.view")
                                || permissions(auth.project.permissions, "operation.projects.project.external.docking.view")
                                || permissions(auth.project.permissions, "operation.projects.project.custom.process.view")
                                || permissions(auth.project.permissions, "operation.projects.project.permissions.view")
                                || permissions(auth.project.permissions, "operation.projects.notice.permissions.view") ?

                                <Tabs onChange={(key) => setChangeKey(key)} tabBarStyle={{ width: "200px", overflow: "auto" }} tabPosition="left">
                                    {
                                        permissions(auth.project.permissions, "operation.projects.project.basic.information.view") ?
                                            <TabPane
                                                tab={
                                                    <>
                                                        <i className="iconfont icon-jibenxinxi" style={{ marginRight: 8 }} />
                                                        <FormattedMessage id={"menu.projects.main.setting.base"} />
                                                    </>
                                                }
                                                key="1"
                                            >
                                                <Layout.Header style={{ height: "24px", background: "#FFFFFF" }} />
                                                <CustomTitle name={intl.formatMessage({ id: "projects.attribute" })} />
                                                <Form labelCol={{ span: g.lang === 'en' ? 5 : 5 }}>
                                                    <Form.Item label={<FormattedMessage id={"projects.type"} />}>
                                                        {projectTypes.find((it) => it.key === projectData.info.type)?.value}
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"common.status"} />}>
                                                        {projectData.status === 0 && (
                                                            <div
                                                                className="x-option x-option-green"
                                                                style={{
                                                                    background: "#ecfaf2",
                                                                    color: "#41cc82",
                                                                    fontStyle: "normal",
                                                                    fontWeight: 400,
                                                                    alignItems: "center",
                                                                    display: "flex",
                                                                    width: 110,
                                                                }}
                                                            >
                                                                <svg
                                                                    style={{
                                                                        width: 16,
                                                                        height: 16,
                                                                        marginLeft: 8,
                                                                        marginRight: 8,
                                                                    }}
                                                                    viewBox="0 0 12 12"
                                                                    fill="none"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                >
                                                                    <circle
                                                                        cx="6"
                                                                        cy="6"
                                                                        r="5.5"
                                                                        stroke="#41CC82"
                                                                    />
                                                                    <path
                                                                        d="M3 6.5H9L7.5 5"
                                                                        stroke="#41CC82"
                                                                        strokeLinecap="round"
                                                                        strokeLinejoin="round"
                                                                    />
                                                                </svg>
                                                                <FormattedMessage id="projects.status.progress" />
                                                            </div>
                                                        )}

                                                        {projectData.status === 1 && (
                                                            <div
                                                                style={{
                                                                    paddingLeft: "0px",
                                                                    display: "flex",
                                                                    alignItems: "center",
                                                                    justifyContent: "space-between",
                                                                }}
                                                            >
                                                                <div
                                                                    className="x-option"
                                                                    style={{
                                                                        background: "#e8efff",
                                                                        color: "#165DFF",
                                                                        fontStyle: "normal",
                                                                        fontWeight: 400,
                                                                        alignItems: "center",
                                                                        display: "flex",
                                                                        width: 110,
                                                                    }}
                                                                >
                                                                    <svg
                                                                        style={{
                                                                            width: 16,
                                                                            height: 16,
                                                                            marginLeft: 8,
                                                                            marginRight: 8,
                                                                        }}
                                                                        viewBox="0 0 12 12"
                                                                        fill="none"
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                    >
                                                                        <circle
                                                                            cx="6"
                                                                            cy="6"
                                                                            r="5.5"
                                                                            stroke="#165DFF"
                                                                        />
                                                                        <path
                                                                            fill-rule="evenodd"
                                                                            clip-rule="evenodd"
                                                                            d="M8.32786 3.75023L5.07116 7.00669L3.67172 5.60735C3.44288 5.37851 3.07182 5.37851 2.84297 5.60735C2.61419 5.83618 2.61419 6.20714 2.84297 6.43597L4.6565 8.2493L4.65679 8.24964C4.77124 8.36409 4.92118 8.4213 5.07116 8.4213C5.22114 8.4213 5.37108 8.36409 5.48553 8.24964L9.15653 4.57892C9.38537 4.35008 9.38537 3.97906 9.15653 3.75023C8.92769 3.52141 8.5567 3.52141 8.32786 3.75023Z"
                                                                            fill="#165DFF"
                                                                        />
                                                                    </svg>
                                                                    <FormattedMessage id="projects.status.finish" />
                                                                </div>
                                                            </div>
                                                        )}

                                                        {projectData.status === 2 && (
                                                            <div
                                                                style={{
                                                                    paddingLeft: "0px",
                                                                    display: "flex",
                                                                    alignItems: "center",
                                                                    justifyContent: "space-between",
                                                                }}
                                                            >
                                                                <div
                                                                    className="x-option x-option-red"
                                                                    lang={g.lang}
                                                                    style={{
                                                                        background: "#e5e7eb",
                                                                        color: "#4E5969",
                                                                        fontStyle: "normal",
                                                                        fontWeight: 400,
                                                                        alignItems: "center",
                                                                        display: "flex",
                                                                        width: 110,
                                                                    }}
                                                                >
                                                                    <svg
                                                                        style={{
                                                                            width: 16,
                                                                            height: 16,
                                                                            marginLeft: 8,
                                                                            marginRight: 8,
                                                                        }}
                                                                        viewBox="0 0 12 12"
                                                                        fill="none"
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                    >
                                                                        <circle
                                                                            cx="6"
                                                                            cy="6"
                                                                            r="5.5"
                                                                            stroke="#4E5969"
                                                                        />
                                                                        <path
                                                                            d="M8.24264 3.75776C8.04738 3.56249 7.7308 3.56249 7.53553 3.75776L6.12132 5.17197L4.70711 3.75776C4.51184 3.56249 4.19526 3.56249 4 3.75776C3.80474 3.95302 3.80474 4.2696 4 4.46486L5.41421 5.87908L4 7.29329C3.80474 7.48855 3.80474 7.80513 4 8.0004C4.19526 8.19566 4.51184 8.19566 4.70711 8.0004L6.12132 6.58618L7.53553 8.0004C7.7308 8.19566 8.04738 8.19566 8.24264 8.0004C8.4379 7.80513 8.4379 7.48855 8.24264 7.29329L6.82843 5.87908L8.24264 4.46486C8.4379 4.2696 8.4379 3.95302 8.24264 3.75776Z"
                                                                            fill="#4E5969"
                                                                        />
                                                                    </svg>
                                                                    <FormattedMessage id="projects.status.close" />
                                                                </div>
                                                            </div>
                                                        )}

                                                        {projectData.status === 3 && (
                                                            <div
                                                                style={{
                                                                    paddingLeft: "0px",
                                                                    display: "flex",
                                                                    alignItems: "center",
                                                                    justifyContent: "space-between",
                                                                }}
                                                            >
                                                                <div
                                                                    className="x-option x-option-yellow"
                                                                    style={{
                                                                        background: "#fff7e5",
                                                                        color: "#FFAE00",
                                                                        fontStyle: "normal",
                                                                        fontWeight: 400,
                                                                        alignItems: "center",
                                                                        display: "flex",
                                                                        width: 110,
                                                                    }}
                                                                >
                                                                    <svg
                                                                        style={{
                                                                            width: 18,
                                                                            height: 18,
                                                                            marginLeft: 8,
                                                                            marginRight: 8,
                                                                            color: "#FFAE00",
                                                                        }}
                                                                        viewBox="0 0 12 12"
                                                                        fill="none"
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                    >
                                                                        <circle
                                                                            cx="6"
                                                                            cy="6"
                                                                            r="5.5"
                                                                            stroke="#FFAE00"
                                                                        />
                                                                        <rect
                                                                            x="4"
                                                                            y="4"
                                                                            width="4"
                                                                            height="4"
                                                                            rx="1"
                                                                            fill="#FFAE00"
                                                                        />
                                                                    </svg>
                                                                    <FormattedMessage id="projects.status.pause" />
                                                                </div>
                                                            </div>
                                                        )}

                                                        {projectData.status === 4 && (
                                                            <div
                                                                style={{
                                                                    paddingLeft: "0px",
                                                                    display: "flex",
                                                                    alignItems: "center",
                                                                    justifyContent: "space-between",
                                                                }}
                                                            >
                                                                <div
                                                                    className="x-option x-option-red"
                                                                    style={{
                                                                        fontStyle: "normal",
                                                                        fontWeight: 400,
                                                                        alignItems: "center",
                                                                        display: "flex",
                                                                        color: "#F96964",
                                                                        backgroundColor: "#fef0ef",
                                                                        width: 110,
                                                                    }}
                                                                >
                                                                    <svg
                                                                        style={{
                                                                            width: 16,
                                                                            height: 16,
                                                                            marginLeft: 8,
                                                                            marginRight: 8,
                                                                            color: "#F96964",
                                                                        }}
                                                                        viewBox="0 0 12 12"
                                                                        fill="none"
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                    >
                                                                        <circle
                                                                            cx="6"
                                                                            cy="6"
                                                                            r="5.5"
                                                                            stroke="#F96964"
                                                                        />
                                                                        <path
                                                                            d="M1.93408 2.39111L2.64119 1.68401L10.0658 9.10863L9.3587 9.81573L1.93408 2.39111Z"
                                                                            fill="#F96964"
                                                                        />
                                                                    </svg>
                                                                    <FormattedMessage id="projects.status.terminate" />
                                                                </div>
                                                            </div>
                                                        )}
                                                    </Form.Item>
                                                </Form>
                                                <Divider dashed />
                                                <CustomTitle name={intl.formatMessage({ id: "projects.brief" })} />
                                                <Form labelCol={{ span: g.lang === 'en' ? 5 : 5 }}>
                                                    <Form.Item label={<FormattedMessage id={"projects.customer"} />}>
                                                        {
                                                            (projectData.customerName === null || projectData.customerName === "" || projectData.customerName === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                projectData.customerName
                                                        }
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"projects.number"} />}>
                                                        {
                                                            (projectData.info.number === null || projectData.info.number === "" || projectData.info.number === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                projectData.info.number
                                                        }
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"projects.sponsor"} />}>
                                                        {
                                                            (projectData.info.sponsor === null || projectData.info.sponsor === "" || projectData.info.sponsor === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                projectData.info.sponsor
                                                        }
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"projects.name"} />}>
                                                        {
                                                            (projectData.info.name === null || projectData.info.name === "" || projectData.info.name === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                projectData.info.name
                                                        }
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"projects.startDate"} />}>
                                                        {
                                                            (projectData.info.startTime === null || projectData.info.startTime === 0 || projectData.info.startTime === "" || projectData.info.startTime === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                moment.unix(projectData.info.startTime).format('YYYY-MM-DD')
                                                        }
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"projects.endDate"} />}>
                                                        {
                                                            (projectData.info.endTime === null || projectData.info.endTime === 0 || projectData.info.endTime === "" || projectData.info.endTime === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                moment.unix(projectData.info.endTime).format('YYYY-MM-DD')
                                                        }
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"projects.contact.information"} />}>
                                                        {
                                                            (projectData.info.phone === null || projectData.info.phone === "" || projectData.info.phone === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                projectData.info.phone
                                                        }
                                                    </Form.Item>
                                                    <Form.Item label={<FormattedMessage id={"projects.remark"} />}>
                                                        {
                                                            (projectData.info.description === null || projectData.info.description === "" || projectData.info.description === undefined)
                                                                ?
                                                                '-'
                                                                :
                                                                projectData.info.description
                                                        }
                                                    </Form.Item>
                                                </Form>
                                            </TabPane>
                                            :
                                            null
                                    }

                                    {
                                        permissions(auth.project.permissions, "operation.projects.project.basic.environment.view") ?
                                            <TabPane
                                                tab={
                                                    <>
                                                        <i className="iconfont icon-quanbuxiangmu" style={{ marginRight: 8 }} />
                                                        <FormattedMessage id={"projects.envs"} />
                                                    </>
                                                }
                                                key="4"
                                            >
                                                <Layout.Header style={{ height: "24px", background: "#FFFFFF" }} />
                                                <Row gutter={[8, 16]}>
                                                    <Col span={24}>
                                                        <HeaderRow>
                                                            <Col span={12}>
                                                                <CustomTitle name={formatMessage({ id: "projects.envs.all" })} />
                                                            </Col>
                                                        </HeaderRow>
                                                    </Col>
                                                    <Col span={24}>
                                                        <Table size='small' dataSource={projectData?.envs || []} pagination={false}
                                                            rowKey={(record) => (record.id)}
                                                            scroll={{ y: 580 }}
                                                            columns={columns()}
                                                            expandable={!projectData?.envs || projectData.envs.length === 0 || projectData.info.type === 1 ? {} : expandedProps}
                                                        />
                                                    </Col>
                                                </Row>
                                            </TabPane>
                                            :
                                            null
                                    }

                                    {
                                        permissions(auth.project.permissions, "operation.projects.project.business.functions.view") ?
                                            <TabPane
                                                tab={
                                                    <>
                                                        <i className="iconfont icon-yewuduijie" style={{ marginRight: 8 }} />
                                                        {formatMessage({ id: "menu.projects.main.setting.function" })}
                                                    </>
                                                }
                                                key="2"
                                            >
                                                <Layout.Header style={{ height: "24px", background: "#FFFFFF" }} />
                                                <Form labelCol={{ span: g.lang === 'en' ? 8 : 8 }}>
                                                    <Form.Item
                                                        label={
                                                            <>
                                                                {formatMessage({ id: "common.timezone" })}
                                                                <Tooltip
                                                                    overlayInnerStyle={{ width: 600 }}
                                                                    placement="top"
                                                                    title={<FormattedMessage id="tool.tip.timezone" />}
                                                                >
                                                                    <QuestionCircleFilled style={{ color: "#D0D0D0" }} />
                                                                </Tooltip>
                                                            </>
                                                        }
                                                        className="mar-ver-5"
                                                    >
                                                        {/* {getTimeZone(projectData.info.timeZone)} */}
                                                        <SelectUI
                                                            allowClear
                                                            placeholder={formatMessage({ id: "placeholder.select.common" })}
                                                            showSearch
                                                            filterOption={(input, option) =>
                                                                `${option.label}`.toLowerCase().includes(input.toLowerCase())
                                                            }
                                                            disabled={true}
                                                            defaultValue={projectData.info.tz}
                                                            style={{ width: '430px' }}
                                                            options={tzOptions.map((it) => ({
                                                                label: `(${it.offset}) ${it[g.lang === "zh" ? "zh" : "en"]}`,
                                                                value: it.location,
                                                            }))}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        label={formatMessage({ id: "projects.orderCheck" })}
                                                        className="mar-ver-5"
                                                    >
                                                        <Radio.Group disabled={true} defaultValue={projectData.info.orderCheck} style={{ width: 400 }}>
                                                            <Radio value={1}>
                                                                <FormattedMessage id="projects.timing" />
                                                            </Radio>
                                                            <Radio value={2}>
                                                                <FormattedMessage id="projects.realTime" />
                                                            </Radio>
                                                            <Radio value={3}>
                                                                <FormattedMessage id="projects.notApplicable" />
                                                            </Radio>
                                                        </Radio.Group>
                                                    </Form.Item>

                                                    <Form.Item
                                                        className="mar-ver-5"
                                                        label={
                                                            <>
                                                                <FormattedMessage id={"projects.recycling.confirmation"} />
                                                                <Tooltip
                                                                    overlayInnerStyle={{ width: 500 }}
                                                                    placement="top"
                                                                    title={
                                                                        <FormattedMessage id="projects.recycling.confirmation.tip" />
                                                                    }
                                                                >
                                                                    <QuestionCircleFilled
                                                                        style={{ marginLeft: "4px", color: "#D0D0D0" }}
                                                                    />
                                                                </Tooltip>
                                                            </>
                                                        }
                                                    >
                                                        <Switch
                                                            disabled={true}
                                                            size="small"
                                                            checked={projectData.info.orderConfirmation === 1}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        className="mar-ver-5"
                                                        label={
                                                            <>
                                                                <FormattedMessage id={"project.de.isolation.approval"} />
                                                                <Tooltip
                                                                    overlayInnerStyle={{ width: 500 }}
                                                                    placement="top"
                                                                    title={
                                                                        <FormattedMessage id="project.de.isolation.approval.tip" />
                                                                    }
                                                                >
                                                                    <QuestionCircleFilled
                                                                        style={{ marginLeft: "4px", color: "#D0D0D0" }}
                                                                    />
                                                                </Tooltip>
                                                            </>
                                                        }
                                                    >
                                                        <Switch
                                                            disabled={true}
                                                            size="small"
                                                            checked={projectData.info.deIsolationApproval === 1}
                                                        />
                                                    </Form.Item>
                                                </Form>
                                                <CustomTitle name={intl.formatMessage({ id: "common.administrator" })} />
                                                <Row>
                                                    <Col style={{ marginTop: 12 }}>
                                                        <Table
                                                            size="small"
                                                            dataSource={administrators}
                                                            pagination={false}
                                                            id="table"
                                                            rowKey={(record) => record.cloudId}
                                                            getPopupContainer={() => document.getElementById("table")}
                                                        >
                                                            <Table.Column
                                                                title={formatMessage({ id: "common.serial" })}
                                                                key="#"
                                                                width={100}
                                                                render={(v, r, i) => i + 1}
                                                            />
                                                            <Table.Column
                                                                title={<FormattedMessage id="common.email" />}
                                                                width={400}
                                                                dataIndex={["info", "email"]}
                                                                key="email"
                                                                {...getColumnSearchProps("email")}
                                                            />
                                                            <Table.Column
                                                                title={<FormattedMessage id="user.name" />}
                                                                width={300}
                                                                dataIndex={["info", "name"]}
                                                                key="name"
                                                                {...getColumnSearchProps("name")}
                                                                render={(v) => v || "-"}
                                                            />
                                                        </Table>
                                                    </Col>
                                                </Row>
                                            </TabPane>
                                            :
                                            null
                                    }

                                    {
                                        permissions(auth.project.permissions, "operation.projects.project.external.docking.view") ?
                                            <TabPane
                                                tab={
                                                    <>
                                                        <i className="iconfont icon-waibuduijie" style={{ marginRight: 8 }} />
                                                        {formatMessage({ id: "menu.projects.main.setting.docking" })}
                                                    </>
                                                }
                                                key="3"
                                            >
                                                <Layout.Header style={{ height: "24px", background: "#FFFFFF" }} />
                                                <Tabs defaultActiveKey="1" scrollable className="custom-ant-tab" size="small" >
                                                    <TabPane tab={
                                                        <>
                                                            <span style={{ marginRight: 10, marginLeft: -10 }}>EDC</span>
                                                        </>
                                                    } key="1" >
                                                        <Row gutter={12}>
                                                            <Col className="gutter-row" span={24}>
                                                                <Form {...dockingLayout}>
                                                                    <Form.Item
                                                                        label={
                                                                            <>
                                                                                <FormattedMessage id={"projects.connectEdc"} />
                                                                                <Tooltip
                                                                                    overlayInnerStyle={{ width: 450 }}
                                                                                    placement="top"
                                                                                    title={<FormattedMessage id="tool.tip.edc" />}
                                                                                >
                                                                                    <QuestionCircleFilled
                                                                                        style={{ color: "#C8C9CC", marginLeft: "2px" }}
                                                                                    />
                                                                                </Tooltip>
                                                                            </>
                                                                        }
                                                                        name={["info", "connectEdc"]}
                                                                        className="mar-ver-5"
                                                                    >
                                                                        <Switch
                                                                            disabled={true}
                                                                            size="small"
                                                                            checked={projectData.info.connectEdc === 1}
                                                                        />
                                                                    </Form.Item>
                                                                </Form>
                                                            </Col>

                                                            {
                                                                projectData.info.connectEdc === 1 ?
                                                                    <>
                                                                        <Col className="gutter-row" span={24}>
                                                                            <Form {...dockingLayout}>
                                                                                <Form.Item label={formatMessage({ id: "projects.connectEdc.supplier" })} className="mar-ver-5">
                                                                                    {projectData.info.edcSupplier !== 0 ? (projectData.info.edcSupplier == 1 ? "Clinflash  EDC" : "Medidata Rave  EDC") : "-"}
                                                                                </Form.Item>
                                                                            </Form>
                                                                        </Col>
                                                                        <Col className="gutter-row" span={24}>
                                                                            <Form {...dockingLayout}>
                                                                                <Form.Item
                                                                                    label={
                                                                                        <>
                                                                                            <FormattedMessage id={"project.statistics.mode"} />
                                                                                            <Tooltip overlayInnerStyle={{ width: 550 }} placement="top" title={
                                                                                                <>
                                                                                                    <Row>
                                                                                                        <FormattedMessage id="project.statistics.mode.real.tp" />
                                                                                                    </Row>
                                                                                                    <Row>
                                                                                                        <FormattedMessage id="project.statistics.mode.active.tp" />
                                                                                                    </Row>
                                                                                                </>
                                                                                            }>
                                                                                                <QuestionCircleFilled style={{ color: "#C8C9CC", marginLeft: "2px" }} />
                                                                                            </Tooltip>
                                                                                        </>
                                                                                    }
                                                                                    className="mar-ver-5"
                                                                                >
                                                                                    {
                                                                                        projectData.info.edcSupplier === 0 || projectData.info.edcSupplier === 1 ?
                                                                                            <Radio.Group
                                                                                                disabled={true}
                                                                                                defaultValue={projectData.info.pushMode}>
                                                                                                <Radio value={1}>
                                                                                                    <FormattedMessage id="project.statistics.real" />
                                                                                                </Radio>
                                                                                                <Radio value={2}>
                                                                                                    <FormattedMessage id="project.statistics.active" />
                                                                                                </Radio>
                                                                                            </Radio.Group> :
                                                                                            <Radio.Group
                                                                                                disabled={true}
                                                                                                defaultValue={projectData.info.pushMode}>
                                                                                                <Radio value={2}>
                                                                                                    <FormattedMessage id="project.statistics.active" />
                                                                                                </Radio>
                                                                                            </Radio.Group>
                                                                                    }
                                                                                </Form.Item>
                                                                            </Form>
                                                                        </Col>
                                                                        {
                                                                            projectData.info.edcSupplier === 1 ?
                                                                                <Col className="gutter-row" span={24}>

                                                                                    {
                                                                                        projectData.info.pushMode === 1 ?
                                                                                            <Form {...dockingLayout}>
                                                                                                <Form.Item
                                                                                                    label={formatMessage({
                                                                                                        id: "projects.synchronization.mode",
                                                                                                    })}
                                                                                                    className="mar-ver-5"
                                                                                                >
                                                                                                    <Radio.Group disabled={true} defaultValue={projectData.info.synchronizationMode}>
                                                                                                        <Radio value={1}>
                                                                                                            <FormattedMessage id="projects.step.by.synchronization" />
                                                                                                        </Radio>
                                                                                                        <Radio value={2}>
                                                                                                            <FormattedMessage id="projects.one.time.full.synchronization" />
                                                                                                        </Radio>
                                                                                                    </Radio.Group>
                                                                                                </Form.Item>
                                                                                            </Form>
                                                                                            :
                                                                                            <Form {...dockingLayout}>
                                                                                                <Form.Item
                                                                                                    label="URL"
                                                                                                    className="mar-ver-5"
                                                                                                >
                                                                                                    {projectData.info.edcUrl}
                                                                                                </Form.Item>
                                                                                                <Form.Item
                                                                                                    label={
                                                                                                        <>
                                                                                                            <FormattedMessage id="projects.push.rules" />
                                                                                                            <Tooltip
                                                                                                                overlayInnerStyle={{ width: 550 }}
                                                                                                                placement="top"
                                                                                                                title={
                                                                                                                    <>
                                                                                                                        <Row>
                                                                                                                            <FormattedMessage id="projects.subject.no.tip" />
                                                                                                                        </Row>
                                                                                                                        <Row>
                                                                                                                            <FormattedMessage id="projects.subject.uid.tip" />
                                                                                                                        </Row>
                                                                                                                    </>
                                                                                                                }
                                                                                                            >
                                                                                                                <QuestionCircleFilled
                                                                                                                    style={{
                                                                                                                        color: "#C8C9CC",
                                                                                                                        marginLeft: "2px",
                                                                                                                    }}
                                                                                                                />
                                                                                                            </Tooltip>
                                                                                                        </>
                                                                                                    }
                                                                                                    className="mar-ver-5"
                                                                                                >
                                                                                                    <Radio.Group disabled={true} defaultValue={projectData.info.pushRules}>
                                                                                                        <Radio value={2}>
                                                                                                            <FormattedMessage id="projects.subject.uid" />
                                                                                                        </Radio>
                                                                                                        <Radio value={1}>
                                                                                                            <FormattedMessage id="project.statistics.subject.number" />
                                                                                                        </Radio>
                                                                                                    </Radio.Group>
                                                                                                </Form.Item>

                                                                                                <Form.Item
                                                                                                    label={formatMessage({ id: "projects.push.scene" })}
                                                                                                    rules={[{ required: false }]}
                                                                                                    name=""
                                                                                                    className="mar-ver-5 colored-disable-checked-checkbox"
                                                                                                >
                                                                                                    <Row style={{ paddingTop: 5 }} justify="start" gutter={24}>
                                                                                                        <Col>
                                                                                                            <Checkbox
                                                                                                                disabled={true}
                                                                                                                checked={projectData.info.pushScenario.registerPush}
                                                                                                            >
                                                                                                                <FormattedMessage id="subject.register" />
                                                                                                            </Checkbox>
                                                                                                        </Col>
                                                                                                        <Col>
                                                                                                            <Checkbox
                                                                                                                disabled={true}
                                                                                                                checked={projectData.info.pushScenario.updateRandomFrontPush}
                                                                                                            >
                                                                                                                <FormattedMessage id="projects.subject.update" />
                                                                                                                <FormattedMessage style={{ fontSize: 12 }} id="projects.subject.update.front" />
                                                                                                            </Checkbox>
                                                                                                        </Col>
                                                                                                        <Col>
                                                                                                            <Checkbox
                                                                                                                disabled={true}
                                                                                                                checked={projectData.info.pushScenario.updateRandomAfterPush}
                                                                                                            >
                                                                                                                <FormattedMessage id="projects.subject.update" />
                                                                                                                <FormattedMessage style={{ fontSize: 12 }} id="projects.subject.update.after" />
                                                                                                            </Checkbox>
                                                                                                        </Col>
                                                                                                        <Col>
                                                                                                            <Checkbox
                                                                                                                disabled={true}
                                                                                                                checked={projectData.info.pushScenario.screenPush}
                                                                                                            >
                                                                                                                <FormattedMessage id="subject.screen" />
                                                                                                            </Checkbox>
                                                                                                        </Col>
                                                                                                        <Col>
                                                                                                            <Checkbox
                                                                                                                disabled={true}
                                                                                                                checked={projectData.info.pushScenario.dispensingPush}
                                                                                                            >
                                                                                                                <FormattedMessage id="projects.attributes.dispensing.yes" />
                                                                                                            </Checkbox>
                                                                                                        </Col>
                                                                                                    </Row>
                                                                                                    <Row style={{ paddingTop: 5 }} justify="start" gutter={24}>
                                                                                                        <Col style={{ paddingRight: 0 }}>
                                                                                                            <Checkbox
                                                                                                                disabled={true}
                                                                                                                checked={projectData.info.pushScenario.randomPush}
                                                                                                            >
                                                                                                                <FormattedMessage id="subject.random" />
                                                                                                            </Checkbox>
                                                                                                        </Col>
                                                                                                        {
                                                                                                            projectData.info.pushScenario.randomBlockPush || projectData.info.pushScenario.formRandomBlockPush || projectData.info.pushScenario.cohortRandomBlockPush ?
                                                                                                                <>
                                                                                                                    <Col style={{ paddingLeft: 0 }}>
                                                                                                                        <Divider type="vertical" className="edc-docking-setting-vertical-divider" />
                                                                                                                        <Text disabled={true}>
                                                                                                                            {formatMessage({ id: "projects.subject.block.str-after" })}
                                                                                                                        </Text>
                                                                                                                    </Col>
                                                                                                                    <Col>
                                                                                                                        <Checkbox
                                                                                                                            disabled={true}
                                                                                                                            checked={projectData.info.pushScenario.randomBlockPush}
                                                                                                                        >
                                                                                                                            <FormattedMessage id="projects.subject.stratification" />
                                                                                                                        </Checkbox>
                                                                                                                    </Col>
                                                                                                                    <Col>
                                                                                                                        <Checkbox
                                                                                                                            disabled={true}
                                                                                                                            checked={projectData.info.pushScenario.formRandomBlockPush}
                                                                                                                        >
                                                                                                                            <FormattedMessage id="projects.subject.form" />
                                                                                                                        </Checkbox>
                                                                                                                    </Col>
                                                                                                                    {
                                                                                                                        projectData.info.type === 2 || projectData.info.type === 3 ?
                                                                                                                            <Col>
                                                                                                                                <Checkbox
                                                                                                                                    disabled={true}
                                                                                                                                    checked={projectData.info.pushScenario.cohortRandomBlockPush}
                                                                                                                                >
                                                                                                                                    {
                                                                                                                                        projectData.info.type === 3 ?
                                                                                                                                            <FormattedMessage id="projects.subject.stageName" />
                                                                                                                                            :
                                                                                                                                            <FormattedMessage id="projects.subject.cohortName" />
                                                                                                                                    }
                                                                                                                                </Checkbox>
                                                                                                                            </Col>
                                                                                                                            : null
                                                                                                                    }


                                                                                                                    {/*<Checkbox*/}
                                                                                                                    {/*    disabled={true}*/}
                                                                                                                    {/*    checked={projectData.info.pushScenario.randomBlockPush}*/}
                                                                                                                    {/*>*/}
                                                                                                                    {/*    <FormattedMessage id="projects.subject.random.block" />*/}
                                                                                                                    {/*</Checkbox>*/}
                                                                                                                    {/*<Text disabled={true}>*/}
                                                                                                                    {/*    {formatMessage({ id: "projects.subject.block.str-before" })}*/}
                                                                                                                    {/*</Text>*/}
                                                                                                                    {/*<Select*/}
                                                                                                                    {/*    style={{*/}
                                                                                                                    {/*        marginLeft: 8,  // 左外边距*/}
                                                                                                                    {/*        marginRight: 8,  // 右外边距*/}
                                                                                                                    {/*        width: 250,*/}
                                                                                                                    {/*    }}*/}
                                                                                                                    {/*    showArrow*/}
                                                                                                                    {/*    className="custom-multi-select-with-slash"*/}
                                                                                                                    {/*    mode="multiple"*/}
                                                                                                                    {/*    placeholder={*/}
                                                                                                                    {/*        formatMessage({ id: "placeholder.select.common", })*/}
                                                                                                                    {/*    }*/}
                                                                                                                    {/*    disabled={true}*/}
                                                                                                                    {/*    value={[*/}
                                                                                                                    {/*        ...(projectData.info.pushScenario.randomBlockPush ? [1] : []),*/}
                                                                                                                    {/*        ...(projectData.info.pushScenario.formRandomBlockPush ? [2] : []),*/}
                                                                                                                    {/*        ...(projectData.info.type !== 1 && projectData.info.pushScenario.cohortRandomBlockPush ? [3] : [])*/}
                                                                                                                    {/*    ]}*/}
                                                                                                                    {/*    options={getOptions()}*/}
                                                                                                                    {/*    tagRender={(props) => {*/}
                                                                                                                    {/*        // 获取当前所有选中值*/}
                                                                                                                    {/*        const selectedValues = [*/}
                                                                                                                    {/*            ...(projectData.info.pushScenario.randomBlockPush ? [1] : []),*/}
                                                                                                                    {/*            ...(projectData.info.pushScenario.formRandomBlockPush ? [2] : []),*/}
                                                                                                                    {/*            ...(projectData.info.type !== 1 && projectData.info.pushScenario.cohortRandomBlockPush ? [3] : [])*/}
                                                                                                                    {/*        ];*/}

                                                                                                                    {/*        // 判断是否是最后一个选中项*/}
                                                                                                                    {/*        const isLastSelected = props.value === selectedValues[selectedValues.length - 1];*/}

                                                                                                                    {/*        return (*/}
                                                                                                                    {/*            <span className="no-tag-style">*/}
                                                                                                                    {/*                {props.label}*/}
                                                                                                                    {/*                {!isLastSelected && <span style={{ margin: '0' }}>/</span>}*/}
                                                                                                                    {/*            </span>*/}
                                                                                                                    {/*        );*/}
                                                                                                                    {/*    }}*/}
                                                                                                                    {/*>*/}
                                                                                                                    {/*</Select>*/}
                                                                                                                    {/*<Text disabled={true}>*/}
                                                                                                                    {/*    {formatMessage({ id: "projects.subject.block.str-after" })}*/}
                                                                                                                    {/*</Text>*/}
                                                                                                                </>
                                                                                                                :
                                                                                                                null
                                                                                                        }


                                                                                                    </Row>
                                                                                                </Form.Item>
                                                                                            </Form>

                                                                                    }
                                                                                </Col>
                                                                                :
                                                                                <>
                                                                                    <Col className="gutter-row" span={24}>
                                                                                        <Form {...dockingLayout}>
                                                                                            <Form.Item label="URL" className="mar-ver-5">
                                                                                                {projectData.info.edcUrl}
                                                                                            </Form.Item>
                                                                                        </Form>
                                                                                    </Col>
                                                                                    <CustomTitle name={intl.formatMessage({ id: "projects.connectEdc.mapping.configuration" })} />
                                                                                    {
                                                                                        projectData.info.type !== 1 ?
                                                                                            <Tabs defaultActiveKey="1" scrollable className="custom-ant-tab" size="small" >
                                                                                                {projectData.info.visitRandomization?.visits.map((visit, index) => (
                                                                                                    <TabPane tab={visit.name} key={index + 1}>
                                                                                                        <Table
                                                                                                            key={Math.random()}
                                                                                                            size="small"
                                                                                                            dataSource={visit.visitConfigs}
                                                                                                            pagination={false}
                                                                                                            style={{ marginLeft: 14, marginRight: 14 }}
                                                                                                            className='custom-table'
                                                                                                        >
                                                                                                            <Table.Column width={50} title={() => (
                                                                                                                <div style={{ position: "relative" }}>
                                                                                                                    <div style={{ textAlign: "right", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.edcOid' })}</div>
                                                                                                                    <div style={{ textAlign: "left", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.visitCode' })}</div>
                                                                                                                    <DividerLine />
                                                                                                                </div>)}
                                                                                                                dataIndex="visitCode" key={"visitCode"} ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    )
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.folderOid' })}
                                                                                                                dataIndex="folderOid" key="folderOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.formOid' })}
                                                                                                                dataIndex="formOid" key="formOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.ipNumberOid' })}
                                                                                                                dataIndex="ipNumberOid" key="ipNumberOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.dispenseTimeOid' })}
                                                                                                                dataIndex="dispenseTimeOid" key="dispenseTimeOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                        </Table>
                                                                                                    </TabPane>
                                                                                                ))}
                                                                                            </Tabs>
                                                                                            :
                                                                                            <>
                                                                                                {projectData.info.visitRandomization?.visits.map((visit, index) => (
                                                                                                    <div>
                                                                                                        <Table
                                                                                                            key={Math.random()}
                                                                                                            size="small"
                                                                                                            dataSource={visit.visitConfigs}
                                                                                                            // rowKey={(record,key) => (key)}
                                                                                                            pagination={false}
                                                                                                            style={{ marginLeft: 14, marginRight: 14 }}
                                                                                                            className='custom-table'
                                                                                                        >
                                                                                                            <Table.Column width={50}
                                                                                                                title={() => (<div style={{ position: "relative" }}>
                                                                                                                    <div style={{ textAlign: "right", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.edcOid' })}</div>
                                                                                                                    <div style={{ textAlign: "left", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.visitCode' })}</div>
                                                                                                                    <DividerLine />
                                                                                                                </div>)}
                                                                                                                dataIndex="visitCode" key={"visitCode"} ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.folderOid' })}
                                                                                                                dataIndex="folderOid" key="folderOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.formOid' })}
                                                                                                                dataIndex="formOid" key="formOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.ipNumberOid' })}
                                                                                                                dataIndex="ipNumberOid" key="ipNumberOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.dispenseTimeOid' })}
                                                                                                                dataIndex="dispenseTimeOid" key="dispenseTimeOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                        </Table>
                                                                                                    </div>
                                                                                                ))}
                                                                                            </>
                                                                                    }

                                                                                    {
                                                                                        projectData.info.type === 3 ?
                                                                                            <Tabs defaultActiveKey="1" scrollable className="custom-ant-tab" size="small" >
                                                                                                {projectData.info.visitRandomization?.randomizations.map((randomization, index) => (
                                                                                                    <TabPane tab={randomization.name} key={index + 1}>
                                                                                                        <Table
                                                                                                            key={Math.random()}
                                                                                                            size="small"
                                                                                                            dataSource={randomization.randomizationConfigs}
                                                                                                            pagination={false}
                                                                                                            style={{ marginLeft: 14, marginRight: 14 }}
                                                                                                            className='custom-table'
                                                                                                        >
                                                                                                            <Table.Column width={30}
                                                                                                                title={() => (<div style={{ position: "relative" }}>
                                                                                                                    <div style={{ textAlign: "right", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.edcOid' })}</div>
                                                                                                                    <div style={{ textAlign: "left", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.randomizationField' })}</div>
                                                                                                                    <DividerLine />
                                                                                                                </div>)}
                                                                                                                dataIndex="randomizationField" key={"randomizationField"} ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        <span>{formatMessage({ id: 'project.external.edc.rave.mapping.' + value })}</span>
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.folderOid' })}
                                                                                                                dataIndex="folderOid" key="folderOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.formOid' })}
                                                                                                                dataIndex="formOid" key="formOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                            <Table.Column width={30}
                                                                                                                title={formatMessage({ id: 'project.external.edc.rave.mapping.fieldOid' })}
                                                                                                                dataIndex="fieldOid" key="fieldOid" ellipsis
                                                                                                                render={(value, record, i) => {
                                                                                                                    return (
                                                                                                                        value
                                                                                                                    );
                                                                                                                }}
                                                                                                            />
                                                                                                        </Table>
                                                                                                    </TabPane>
                                                                                                ))}
                                                                                            </Tabs>
                                                                                            :
                                                                                            <>
                                                                                                {projectData.info.visitRandomization?.randomizations.map((randomization, index) => (
                                                                                                    <Table
                                                                                                        key={Math.random()}
                                                                                                        size="small"
                                                                                                        dataSource={randomization.randomizationConfigs}
                                                                                                        pagination={false}
                                                                                                        style={{ marginLeft: 14, marginRight: 14 }}
                                                                                                        className='custom-table'
                                                                                                    >
                                                                                                        <Table.Column width={30}
                                                                                                            title={() => (<div style={{ position: "relative" }}>
                                                                                                                <div style={{ textAlign: "right", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.edcOid' })}</div>
                                                                                                                <div style={{ textAlign: "left", lineHeight: "11px" }}>{formatMessage({ id: 'project.external.edc.rave.mapping.randomizationField' })}</div>
                                                                                                                <DividerLine />
                                                                                                            </div>)}
                                                                                                            dataIndex="randomizationField" key={"randomizationField"} ellipsis
                                                                                                            render={(value, record, i) => {
                                                                                                                return (
                                                                                                                    <span>{formatMessage({ id: 'project.external.edc.rave.mapping.' + value })}</span>
                                                                                                                );
                                                                                                            }}
                                                                                                        />
                                                                                                        <Table.Column width={30}
                                                                                                            title={formatMessage({ id: 'project.external.edc.rave.mapping.folderOid' })}
                                                                                                            dataIndex="folderOid" key="folderOid" ellipsis
                                                                                                            render={(value, record, i) => {
                                                                                                                return (
                                                                                                                    value
                                                                                                                );
                                                                                                            }}
                                                                                                        />
                                                                                                        <Table.Column width={30}
                                                                                                            title={formatMessage({ id: 'project.external.edc.rave.mapping.formOid' })}
                                                                                                            dataIndex="formOid" key="formOid" ellipsis
                                                                                                            render={(value, record, i) => {
                                                                                                                return (
                                                                                                                    value
                                                                                                                );
                                                                                                            }}
                                                                                                        />
                                                                                                        <Table.Column width={30}
                                                                                                            title={formatMessage({ id: 'project.external.edc.rave.mapping.fieldOid' })}
                                                                                                            dataIndex="fieldOid" key="fieldOid" ellipsis
                                                                                                            render={(value, record, i) => {
                                                                                                                return (
                                                                                                                    value
                                                                                                                );
                                                                                                            }}
                                                                                                        />
                                                                                                    </Table>
                                                                                                ))}
                                                                                            </>
                                                                                    }
                                                                                </>
                                                                        }
                                                                    </>
                                                                    :
                                                                    null
                                                            }
                                                        </Row>
                                                    </TabPane>
                                                    <TabPane tab={
                                                        <>
                                                            <span style={{ marginLeft: -20 }}>eLearning</span>
                                                            <Tooltip
                                                                overlayInnerStyle={{ width: 500 }}
                                                                placement="top"
                                                                title={
                                                                    <>
                                                                        <Row>
                                                                            <FormattedMessage id="tool.tip.elearning" />
                                                                        </Row>
                                                                        <Row>
                                                                            <FormattedMessage id="tool.tip.elearning.yes.tp" />
                                                                        </Row>
                                                                        <Row>
                                                                            <FormattedMessage id="tool.tip.elearning.no.tp" />
                                                                        </Row>
                                                                    </>
                                                                }
                                                            >
                                                                <QuestionCircleFilled style={{ marginLeft: "4px", color: "#D0D0D0" }} />
                                                            </Tooltip>
                                                        </>
                                                    } key="2">

                                                        <div style={{ backgroundColor: "#F5F5F5", color: "#BEBEBE" }}>
                                                            <Row gutter={16}>
                                                                <Col className="gutter-row" span={6} style={{ marginLeft: "80px" }}>
                                                                    <Form.Item
                                                                        label={formatMessage({ id: "projects.learning.system.courses" })}
                                                                        className="mar-ver-5"
                                                                    >
                                                                        <Switch
                                                                            disabled={true}
                                                                            size="small"
                                                                            checked={projectData.info.systemCourses === 1}
                                                                        />
                                                                    </Form.Item>
                                                                </Col>
                                                                {projectData.info.systemCourses === 1 ? (
                                                                    <>
                                                                        <Col className="gutter-row" span={6}>
                                                                            <Checkbox style={{ marginLeft: -50, paddingTop: 10 }}
                                                                                disabled={true}
                                                                                checked={projectData.info.needSystemCourses === 1}
                                                                            >
                                                                                <FormattedMessage id="projects.learning.compulsory" />
                                                                            </Checkbox>
                                                                        </Col>
                                                                    </>
                                                                ) : null}
                                                            </Row>
                                                        </div>
                                                        <br />
                                                        <div style={{ backgroundColor: "#F5F5F5", color: "#BEBEBE", height: 50 }}>
                                                            <Row gutter={16}>
                                                                <Col className="gutter-row" span={6} style={{ marginLeft: "80px" }}>
                                                                    <Form.Item
                                                                        label={formatMessage({ id: "projects.learning.courses" })}
                                                                        className="mar-ver-5"
                                                                    >
                                                                        <Switch
                                                                            disabled={true}
                                                                            size="small"
                                                                            checked={projectData.info.connectLearning === 1}
                                                                        />
                                                                    </Form.Item>
                                                                </Col>
                                                                {projectData.info.systemCourses === 1 ? (
                                                                    <>
                                                                        <Col className="gutter-row" span={6} style={{ marginLeft: -65 }}>
                                                                            <Form.Item
                                                                                label={formatMessage({ id: "projects.env" })}
                                                                                labelCol={{}}
                                                                                style={{ paddingLeft: 15, marginTop: 7 }}
                                                                            >
                                                                                {envStr}
                                                                            </Form.Item>
                                                                        </Col>
                                                                        <Col className="gutter-row" span={6} style={{ marginLeft: -65 }}>
                                                                            <Checkbox style={{ paddingLeft: 10, paddingTop: 10 }}
                                                                                disabled={true}
                                                                                checked={projectData.info.needLearning === 1}
                                                                            >
                                                                                <FormattedMessage id="projects.learning.compulsory" />
                                                                            </Checkbox>
                                                                        </Col>
                                                                    </>
                                                                ) : null}
                                                            </Row>
                                                        </div>
                                                    </TabPane>
                                                </Tabs>
                                            </TabPane>
                                            :
                                            null
                                    }

                                    {
                                        permissions(auth.project.permissions, "operation.projects.project.custom.process.view") ?
                                            <TabPane
                                                tab={
                                                    <>
                                                        <i className="iconfont icon-zidingyiliucheng" style={{ marginRight: 8 }} />
                                                        {formatMessage({ id: "menu.projects.main.setting.custom" })}
                                                    </>
                                                }
                                                key="5"
                                            >
                                                <Layout.Header style={{ height: "24px", background: "#FFFFFF" }} />
                                                <CustomTitle name={formatMessage({ id: 'project.setting.switch.unblind.control' })} />


                                                <UnblindingControlView project={projectData} disable={true} unblindingControl={projectData.info.unblindingControl} />


                                                <CustomTitle name={formatMessage({ id: 'project.setting.divider.approval.control' })} />
                                                <Row justify="start" style={{ marginTop: "9px", marginLeft: "80px" }}>
                                                    <Col className="gutter-row" span={24}>
                                                        <Row>
                                                            <Col style={{ marginBottom: "12px" }}>
                                                                <Form.Item
                                                                    label={
                                                                        <>
                                                                            <FormattedMessage
                                                                                id={"project.setting.switch.approval.control"}
                                                                            />
                                                                            <Tooltip
                                                                                overlayInnerStyle={{ width: 500 }}
                                                                                placement="top"
                                                                                title={<FormattedMessage id="tool.tip.approval.control" />}
                                                                            >
                                                                                <QuestionCircleFilled
                                                                                    style={{ marginLeft: "4px", color: "#D0D0D0" }}
                                                                                />
                                                                            </Tooltip>
                                                                        </>
                                                                    }
                                                                    className="mar-ver-5"
                                                                >
                                                                    <Switch
                                                                        disabled={true}
                                                                        size="small"
                                                                        checked={projectData.info.orderApprovalControl === 1}
                                                                    />

                                                                </Form.Item>
                                                            </Col>

                                                            {
                                                                projectData.info.orderApprovalControl === 1 &&
                                                                <Col style={{
                                                                    width: g.lang === "zh" ? "80%" : "62%",
                                                                    marginLeft: 6, marginTop: 12,
                                                                    color: "#677283",
                                                                }}>
                                                                    <svg className="iconfont" width={16} height={16} style={{ marginBottom: "-3px" }}>
                                                                        <use xlinkHref="#icon-jinggao"></use>
                                                                    </svg>
                                                                    {formatMessage({ id: "project.setting.switch.approval.control.tip" })}
                                                                </Col>
                                                            }

                                                            {
                                                                (projectData.info.orderApprovalControl === 1)
                                                                    ?
                                                                    <Row >
                                                                        <Col className="gutter-row">
                                                                            <Form.Item
                                                                                label={formatMessage({ id: "project.setting.approval.method" })}
                                                                            >
                                                                                <FormattedMessage
                                                                                    id={"project.setting.checkbox.unblind.process"}
                                                                                />
                                                                            </Form.Item>
                                                                        </Col>
                                                                    </Row>
                                                                    :
                                                                    null
                                                            }
                                                        </Row>
                                                    </Col>
                                                </Row>
                                            </TabPane>
                                            :
                                            null
                                    }

                                    {
                                        permissions(auth.project.permissions, "operation.projects.project.permissions.view") ?
                                            <TabPane
                                                tab={
                                                    <>
                                                        <i className="iconfont icon-xiangmuquanxian" style={{ marginRight: 8 }} />
                                                        {formatMessage({ id: "menu.projects.main.setting.permission" })}
                                                    </>
                                                }
                                                key="6"
                                            >
                                                <Layout.Header style={{ height: "24px", background: "#FFFFFF" }} />
                                                <Row>
                                                    <CustomTitle name={intl.formatMessage({ id: "common.all.role" })} />
                                                </Row>
                                                <Row justify="space-between" style={{ margin: "12px 0" }}>
                                                    <Input
                                                        placeholder={formatMessage({ id: "role.name.enter" })}
                                                        onChange={onSearch}
                                                        style={{ width: 200 }}
                                                        suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                                    />
                                                </Row>
                                                <Table
                                                    loading={listLoading}
                                                    size="small"
                                                    dataSource={projectRolePermissions}
                                                    rowKey={(record) => record.name}
                                                    pagination={false}
                                                >
                                                    <Table.Column title={formatMessage({ id: "common.serial" })} dataIndex="#" key="#" width={60}
                                                        render={(text, record, index) => (index + 1)} />
                                                    <Table.Column
                                                        title={formatMessage({ id: "roles.name" })}
                                                        key="name"
                                                        dataIndex="name"
                                                        ellipsis
                                                    />
                                                    <Table.Column
                                                        title={formatMessage({ id: "common.classification" })}
                                                        key="scope"
                                                        dataIndex="scope"
                                                        width={100}
                                                        ellipsis
                                                    />
                                                    <Table.Column
                                                        title={formatMessage({ id: "common.template" })}
                                                        key="template"
                                                        dataIndex="template"
                                                        ellipsis
                                                        width={100}
                                                        render={(template, record, index) => {
                                                            return (
                                                                template === 1 ?
                                                                    formatMessage({ id: "common.common" })
                                                                    :
                                                                    "DTP"
                                                            );
                                                        }}
                                                    />
                                                    <Table.Column
                                                        title={formatMessage({ id: "common.description" })}
                                                        key="description"
                                                        dataIndex="description"
                                                        ellipsis
                                                        render={(description, record, index) => {
                                                            return (
                                                                description === '' ?
                                                                    "-"
                                                                    :
                                                                    description
                                                            );
                                                        }}
                                                    />
                                                    <Table.Column
                                                        title={formatMessage({ id: "common.operation" })}
                                                        align={'left'}
                                                        key="operation"
                                                        dataIndex="operation"
                                                        width={g.lang === "zh" ? 200 : 250}
                                                        ellipsis
                                                        render={
                                                            (value, record, index) => (operationBtn(record))
                                                        }
                                                    />
                                                </Table>
                                            </TabPane>
                                            :
                                            null
                                    }

                                    {
                                        permissions(auth.project.permissions, "operation.projects.notice.permissions.view") ?
                                            <TabPane
                                                tab={
                                                    <>
                                                        <i className="iconfont icon-xiangmuquanxian" style={{ marginRight: 8 }} />
                                                        {formatMessage({ id: "menu.projects.main.setting.notice" })}
                                                    </>
                                                }
                                                key="7"
                                            >
                                                <Layout.Header style={{ height: "24px", background: "#FFFFFF" }} />
                                                <VisitNotice project={projectData} changeKey={changeKey} />
                                            </TabPane>
                                            :
                                            null
                                    }
                                </Tabs>
                                :
                                <div style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: '100%',
                                    height: '100%',
                                    paddingTop: 160
                                }}>
                                    <Empty image={<img src={EmptyImg} alt='empty' />} imageStyle={{ width: "300px", height: "213px" }} />
                                </div>
                        }
                    </Container>
                </Spin>
            </Form>

            <ProjectRoleDetails bind={project_role_details_use} />
            <ProjectRoleAuthority bind={project_role_authority_use} />

        </StyledModal>
    )
};

const StyledModal = styled(Modal)`
    height: calc(100vh - 120px);

    .ant-modal-content {
        height: inherit;
    }
    .ant-modal-body {
        padding-left: 0px;
        padding-bottom: 0px;
        padding-top: 0px;
        height: inherit;
        max-height: unset;
    }
    .ant-modal-footer {
        margin-left: 199px;
        border-left: 1px solid #f0f0f0;
    }
`;

const Container = styled.div`
    .ant-tabs-tab {
        padding-left: 24px !important;
    }
    .ant-tabs-tab.ant-tabs-tab-active {
        background: #e8efff;
    }
    .ant-tabs-ink-bar {
        display: none;
    }
    .custom-error-msg:is(.ant-form-item-has-error) .ant-form-item-explain {
        position: relative;

        ${(props) =>
        props.lang === "zh" &&
        `
            @media (min-width: 453px) {
                top: 20px;
            }

            @media (min-width: 574px) {
                top: 52px;
            }

            @media (min-width: 582px) {
                top: 44px;
            }

            @media (min-width: 594px) {
                top: 44px;
            }

            @media (min-width: 613px) {
                top: 36px;
            }

            @media (min-width: 636px) {
                top: 36px;
            }

            @media (min-width: 665px) {
                top: 20px;
            }

            @media (min-width: 873px) {
                top: -4px;
            }
        `}

        ${(props) =>
        props.lang === "en" &&
        `

            @media (max-width: 575px) {
                top: 20px;
            }

            @media (min-width: 576px) {
                top: 164px;
            }

            @media (min-width: 772px) {
                top: 148px;
            }

            @media (min-width: 787px) {
                top: 108px;
            }

            @media (min-width: 801px) {
                top: 100px;
            }

            @media (min-width: 837px) {
                top: 76px;
            }

            @media (min-width: 883px) {
                top: 52px;
            }

            @media (min-width: 935px) {
                top: 52px;
            }

            @media (min-width: 942px) {
                top: 36px;
            }

            @media (min-width: 1066px) {
                top: 20px;
            }

            @media (min-width: 1418px) {
                top: -4px;
            }
        `}
    }
    .ant-tabs-left > .ant-tabs-content-holder,
    .ant-tabs-left > div > .ant-tabs-content-holder {
        height: calc(100vh - 176px);
        overflow: auto;
        padding-bottom: ${(props) => !!props.edit ? "63px" : "0"};
        padding-right: 24px;
        width: 100%;
    }
`;

const HeaderRow = styled(Row)`
    align-items: center !important;
`;

const ExpandTable = styled(Table)`
    .ant-table-thead>tr>th{
        background-color:#fff;
        color: #4E5969
    }
`;

const ControlMode = styled(Form.Item)`
    .ant-form-item {
        margin-bottom: 0px;
    }

    .ant-form-item-control-input-content {
        display: flex;
        align-items: center;
    }
`;

const CustomCheckbox = styled(Checkbox)`
    .ant-checkbox-disabled .ant-checkbox-inner{
        background-color:#A2BEFF;
        color: #4E5969
    }
`;

const DividerLine = styled.div`
  position: absolute;
  width: 0.5px;
  height: 258px;
  top: -107px;
  left: 110px;
  background: #E3E4E6;
  transform: rotate(-77.5deg);
`;
