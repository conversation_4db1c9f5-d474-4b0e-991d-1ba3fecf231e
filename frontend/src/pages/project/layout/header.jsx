import React, { useCallback } from "react";
import { Bad<PERSON>, Col, Divider, Dropdown, Row, Tag, Tooltip } from "antd";
import { Person } from "pages/common/person";
import { Learning } from "pages/main/layout/learning";
import styled from "@emotion/styled";
import { RoleDropdown } from "pages/common/role";
import { useNavigate } from "react-router-dom";
import { FormattedMessage } from "react-intl";
import { useAuth } from "../../../context/auth";
import { menuSelect, permissions } from "../../../tools/permission";
import { useSafeState } from "ahooks";
import { Project } from "./project-select";
import { BeradcrumbItemUI, EnvTag } from "pages/common/ui"
import { useFetch } from "../../../hooks/request";
import { Tab } from "./tab";
import { getUserProjectEnvironmentRoles } from "../../../api/user";
import { useGlobal } from "../../../context/global";

export const Header = () => {
    const g = useGlobal();
    const auth = useAuth()
    const project = auth.project
    const env = auth.env
    const navigate = useNavigate();
    const configure_ref = React.useRef()
    const tab = React.useRef();

    const show = (data) => {
        if (permissions(auth.project.permissions, "operation.build.randomization.info.view")) {
            configure_ref.current.show(data)
        }
    }

    const projectCard = () => {
        tab.current.show()
    };

    function currentProject() {
        if (project) {
            return <>
                {
                    permissions(auth.project.permissions, "operation.monitor.view") && env.name === "PROD" && auth.project.info.research_attribute !== 1 && auth.systemCheck ?
                        <Tag className="mar-rgt-10 mouse" onClick={() => {
                            window.open(window.location.origin + "/check")
                        }}>
                            <FormattedMessage id={"check.system"} />
                        </Tag>
                        :
                        null
                }
                <div onClick={() => {
                    show(project)
                }} className="mouse">
                    {/* {project ? <Tag>{project.info.number}</Tag> : null} */}
                    {/* {env ? <Tag>{env.name}</Tag> : null} */}
                    {/* {cohort ? (cohort.name ? <Tag>{cohort.name}</Tag> : null) : null} */}
                </div>
                {/* {project.permissions && project.permissions.role ?
                    <Tag color={"blue"}>{project.permissions.role}</Tag> : null} */}
            </>;
        } else {
            return null;
        }
    }
    const backToHome = useCallback(() => {
        // 清空项目ID（防止返回控制台后，还是根据查询项目ID和环境ID查询eln）
        let project = auth.project;
        project.id = "";
        auth.setProject(project);
        // 清空环境ID
        let env = auth.env;
        env.id = "";
        auth.setEnv(env);
        navigate("/");
    }, [navigate]);

    // TODO 添加相关权限信息，添加对应业务的逻辑
    const [envMenuvisible, setMenuEnvVisible] = useSafeState(false);
    const onEnvSelect = useCallback((e) => {
        auth.setEnv(e);
        sessionStorage.setItem("env", JSON.stringify(e));
        // cohort 默认选择第一个
        let cohort = null
        if (e.cohorts && e.cohorts.length !== 0) {
            auth.setCohort(e.cohorts[0])
            cohort = e.cohorts[0]
            sessionStorage.setItem("cohort", JSON.stringify(e.cohorts[0]));
        }
        //  修改角色默认选择第一个
        setMenuEnvVisible(false);
        // 获取权限信息 写入state session
        setRole(auth.project, e, cohort)
    }, [])
    // 获取当前项目所有的相关信息
    //  权限接口
    const { runAsync: roleRunAsync } = useFetch(getUserProjectEnvironmentRoles, { manual: true })
    const setRole = (p, env, cohort) => {
        let permissions = {}
        roleRunAsync({
            customerId: auth.customerId,
            projectId: p.id,
            envId: env.id,
            cohortId: cohort?.id
        }).then(
            (result) => {
                let roleList = result.data.filter((it) => it.name !== "Customer-Admin" && it.name !== "Project-Admin" && it.name !== "Sys-Admin" && it.page !== undefined)
                let permission = roleList.find((item) => auth.project.permissions.role === item.role)
                if (permission) {
                    permissions = permission
                } else {
                    permissions = roleList[0]

                }
                p.permissions = permissions
                //  修改权限 project
                auth.setProject(p);
                //  修改 env
                auth.setEnv(env)
                //  修改 cohort
                auth.setCohort(cohort)
                sessionStorage.setItem("current_project", JSON.stringify(p))
                sessionStorage.setItem("env", JSON.stringify(env));
                sessionStorage.setItem("cohort", JSON.stringify(cohort));

                //  重新加载页面
                let path = menuSelect(permissions)
                navigate(path);
                window.location.reload();
            }
        )
    }


    const envDropMenu = React.useMemo(() => {
        const projectInfo = JSON.parse(sessionStorage.getItem("current_project"))
        return (
            <MenuContainer>
                <MenuBodyContainer>
                    {projectInfo.envs?.map(e => (
                        <MenuItemCotainer key={e.id}>
                            <MenuItemBody onClick={() => { onEnvSelect(e) }} isSelected={auth?.env?.id === e.id}>
                                <EnvTag type={e.name}></EnvTag>
                            </MenuItemBody>
                        </MenuItemCotainer>
                    )
                    )}
                </MenuBodyContainer>
            </MenuContainer>
        )
    }, [auth.env])

    const [cohortMenuVisible, setCohortMenuVisible] = useSafeState(false);
    const onCohortSelect = useCallback((c) => {
        auth.setCohort(c)
        // 获取权限信息 写入state session
        setRole(auth.project, auth.env, c)
        sessionStorage.setItem("cohort", JSON.stringify(c));
        setCohortMenuVisible(false);
    }, [])
    const cohortDropMenu = React.useMemo(() => {
        let cohorts = []
        if (env.cohorts && env.cohorts.length > 0) {
            cohorts.push(...env.cohorts.filter((e) => e.status === 2),
                ...env.cohorts.filter((e) => e.status === 5),
                ...env.cohorts.filter((e) => e.status === 3),
                ...env.cohorts.filter((e) => e.status === 4),
                ...env.cohorts.filter((e) => e.status === 1))
        }
        return (
            <MenuContainer>
                <MenuBodyContainer>
                    {env.cohorts && env.cohorts.length > 0 ? cohorts.map(c => (
                        <MenuItemCotainer key={c.id}>
                            <MenuItemBody onClick={() => { onCohortSelect(c) }} isSelected={auth?.cohort?.id === c.id}>
                                <CohortTag label={c.name} status={c.status} />
                            </MenuItemBody>
                        </MenuItemCotainer>
                    )) : null}
                </MenuBodyContainer>
            </MenuContainer>
        )
    }, [auth.env])


    return (
        <StyledHeader>
            <Row justify="space-between">
                <Col className="ver-center">
                    <>
                        <span>
                            <span>
                                <BeradcrumbItemUI onClick={backToHome}><FormattedMessage id="menu.home" /></BeradcrumbItemUI>
                                <span style={{ margin: "0px 4px", color: "#4E5969" }}>/</span>
                            </span>
                        </span>
                        <span style={{ marginLeft: 4, display: "flex" }} >
                            {env &&
                                <Dropdown
                                    visible={envMenuvisible}
                                    onVisibleChange={v => setMenuEnvVisible(v)}
                                    trigger={['click']}
                                    overlay={envDropMenu}
                                >
                                    <span><EnvTag type={env.name} /></span>
                                </Dropdown>}
                            {/*fixme 去掉cohort选择*/}
                            {/*{cohort && <Dropdown*/}
                            {/*    visible={cohortMenuVisible}*/}
                            {/*    onVisibleChange={v => setCohortMenuVisible(v)}*/}
                            {/*    trigger={['click']}*/}
                            {/*    overlay={cohortDropMenu}*/}
                            {/*>*/}
                            {/*    <span><CohortTag label={auth.cohort?.name} status={auth.cohort?.status}/></span>*/}
                            {/*</Dropdown>}*/}
                        </span>
                        {project && (<Project />)}
                    </>
                </Col>
                <Col style={{ marginLeft: "auto", marginRight: 12 }}>
                    <Row>
                        <Col className="ver-center">
                            {currentProject()}
                        </Col>
                        {/* <Col className="ver-center" style={{ marginRight: "12px" }}>
                            <Notice />
                        </Col> */}
                        <Col style={{ marginLeft: "-12px" }}>
                            <RoleDropdown />
                        </Col>
                        <Col>
                            <Divider type="vertical" style={{ border: "0.5px solid #f0f0f0" }} />
                        </Col>
                        {
                            permissions(auth.project.permissions, "operation.projects.project.info.view") ?
                                <Col className="ver-center">
                                    <div
                                        style={{ display: "flex", marginRight: "12px", position: "relative" }}
                                        onClick={projectCard}
                                    >
                                        <Badge style={{ position: "absolute", left: "14px", top: "3px" }} />
                                        <Tooltip
                                            overlayInnerStyle={{ width: 75 }}
                                            placement="top"
                                            title={<FormattedMessage id="menu.projects.project.info" />}
                                        >
                                            <svg offset={[-8]} className="iconfont mouse" width={22} height={22}>
                                                <use xlinkHref="#icon-xiangmuxinxi" />
                                            </svg>
                                        </Tooltip>
                                    </div>
                                </Col>
                                :
                                null
                        }
                        <Col className="ver-center" >
                            <Learning />
                        </Col>
                        <Col className="ver-center" >
                            <Person />
                        </Col>
                    </Row>
                </Col>
            </Row>
            <Tab bind={tab} />
        </StyledHeader>
    );
};

const CohortTag = (prop) => {
    let defaultStyle = {
        background: "rgba(65, 204, 130, 0.1)",
        borderRadius: "2px",
        color: "#41CC82",
    }
    switch (prop.status) {
        case 1:
            defaultStyle = {
                background: "#E8EAED",
                borderRadius: "2px",
                color: "#677283",
            }
            break;
        case 2:
            defaultStyle = {
                background: "rgba(65, 204, 130, 0.1)",
                borderRadius: "2px",
                color: "#41CC82",
            }
            break;
        case 5:
            defaultStyle = {
                background: "rgba(247, 186, 30, 0.1)",
                borderRadius: "2px",
                color: "#F7BA1E",
            }
            break;
        case 3:
            defaultStyle = {
                background: "rgba(22, 93, 255, 0.1)",
                borderRadius: "2px",
                color: "#165DFF",
            }
            break;
        case 4:
            defaultStyle = {
                background: "rgba(255, 92, 0, 0.1)",
                borderRadius: "2px",
                color: "#FF5C00",
            }
            break;
    }
    const style = {
        ...defaultStyle,
        // width: 64,
        textAlign: "center",
        overflow: "hidden",
        textOverflow: "ellipsis",
        display: "block",
        border: 'none'
    }

    return <Tag className="mouse" style={style} title={prop.label}> {prop.label}</Tag>
}

const StyledHeader = styled.div`
    margin-left: 8px;
    padding       : 0 10px !important;
	height        : 50px !important;
	line-height   : 50px !important;
	background    : white !important;
	border-bottom : #f0f0f0 solid 1px;
`

const MenuContainer = styled.div`
    position: absolute;
    // width: 104px;
    background: #FFFFFF;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
    border-radius: 2px;
`

const MenuBodyContainer = styled.div`
    padding: 2px 0px;
`

const MenuItemBody = styled.div`
    padding: 8px 0px;
    cursor: pointer;
    display: flex;
    ${props => props.isSelected === true && `
        &::after{
            content: '';
            display: inline-block;
            margin-left: auto;
            width: 8px;
            height: 16px;
            border-right: 2px solid #165DFF;
            border-bottom: 2px solid #165DFF;
            transform: rotate(40deg);
        }
    `}

`
const MenuItemCotainer = styled.div`
    padding: 0px 12px;
    :hover {
        background: #F9FAFB;
    }
`


