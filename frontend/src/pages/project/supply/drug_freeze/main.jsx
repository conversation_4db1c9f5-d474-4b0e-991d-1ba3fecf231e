import React from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { Input, Col, Form, Row, Select, Space, Table, Tooltip } from "antd";
import moment from "moment";
import { SelectedIsolateRelease } from "./selected_isolate_release"
import { FreezeQuantity } from "./freeze_quantity"
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { getUserSitesAndStorehouses, sitesAndStorehouses } from "../../../../api/project_site";
import { getMedicineFreezeList } from "../../../../api/medicine";
import { useAuth } from "../../../../context/auth";
import Footer from "../../../main/layout/footer";
import { PageContextProvider, usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";
import { permissions } from "../../../../tools/permission";
import { HistoryList } from "../../../common/history-list";
import { useGlobal } from "../../../../context/global";
import { AuthButton, AuthSpan } from "../../../common/auth-wrap";

export const Main = (props) => {
    const g = useGlobal()
    const page = usePage()
    const history_ref = React.useRef();
    const selected_isolate_release = React.useRef();
    const freeze_quantity = React.useRef();
    const [storehouses, setStorehouses] = useSafeState([]);
    const [sites, setSites] = useSafeState([]);
    const [freezeInstitutes, setFreezeInstitutes] = useSafeState([]);
    const [freezeInstituteId, setFreezeInstituteId] = useSafeState(undefined);
    const [ipNumber, setIpNumber] = useSafeState("");
    const [doSearch, setDoSearch] = useSafeState(0);
    const searchInputRef = React.useRef();
    const auth = useAuth();
    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
    const {
        runAsync: run_sitesAndStorehouses,
        loading: sitesAndStorehousesLoading
    } = useFetch(sitesAndStorehouses, { manual: true })
    const {
        runAsync: run_getUserSitesAndStorehouses,
        loading: getUserSitesAndStorehousesLoading
    } = useFetch(getUserSitesAndStorehouses, { manual: true })
    const {
        runAsync: run_getMedicineFreezeList,
        loading: getMedicineFreezeLoading
    } = useFetch(getMedicineFreezeList, { manual: true })
    const intl = useTranslation();
    const { formatMessage } = intl;
    const getList = () => {
        let scope = auth.project.permissions.scope
        run_sitesAndStorehouses(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
            }).then((result) => {
                const data = result.data
                if (data != null) {
                    let freezeInstitutes = [];
                    if (data.storehouse != null) {
                        setStorehouses(data.storehouse);
                        if (scope === "study") {
                            freezeInstitutes.push(...data.storehouse)
                        }
                    }
                    if (data.site != null) {
                        setSites(data.site);
                        if (scope === "study") {
                            freezeInstitutes.push(...data.site)
                        }
                    }
                    if (scope !== "study") {
                        run_getUserSitesAndStorehouses({
                            customerId: customerId,
                            projectId: projectId,
                            envId: envId,
                            roleId: auth.project.permissions.role_id
                        }).then((result) => {
                            let freezeInstitutes = [];
                            let data = result.data;
                            freezeInstitutes.push(...data.site)
                            freezeInstitutes.push(...data.storehouse)
                            setFreezeInstitutes(freezeInstitutes)
                        })
                    } else {
                        setFreezeInstitutes(freezeInstitutes)
                    }
                }
            }
            );
        //隔离管理列表查询
        run_getMedicineFreezeList(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                freezeInstituteId: freezeInstituteId,
                ipNumber: ipNumber,
                start: (page.currentPage - 1) * page.pageSize,
                limit: page.pageSize
            }).then((result) => {
                const data = result.data
                if (data != null) {
                    page.setData(data.items);
                    page.setTotal(data.total);
                }
            }
            );
    };

    // const edit = (data) => {
    //     selected_release.current.show(data);
    // };

    // 解隔离
    const isolate = (data, type) => {
        selected_isolate_release.current.show(data, type);
    };

    // 隔离数量
    const quantity = (data) => {
        freeze_quantity.current.show(data);
    };
    function renderInstitute(record) {
        if (!record) {
            return null;
        }
        if (record.institute_type === 1) { //中心
            const site = sites.find(it => (record.institute_id === it.value));
            return site ? site.label : null;
        } else { //仓库
            const storehouse = storehouses.find(it => (record.institute_id === it.value));
            return storehouse ? storehouse.label : null;
        }
    }

    //轨迹
    const showHistory = (oid) => {
        history_ref.current.show("history.medicine.drugFreeze", oid, timeZone);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(getList, [page.currentPage, page.pageSize, freezeInstituteId, g.lang, doSearch]);


    return (
        <React.Fragment>
            <Row >
                <Col>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <svg className="iconfont" width={16} height={16}>
                            <use xlinkHref="#icon-geliguanli" />
                        </svg>
                        <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px" }}>{formatMessage({ id: 'drug.freeze.all', allowComponent: true })}</span>
                    </div>
                </Col>
            </Row>
            <Row justify="space-left" className="mar-top-15">
                <Col className="mar-ver-5">
                    {/*<SelectEmbeddeTitle className="full-width"*/}
                    {/*                    style={{width:g.lang=== "en"?270:160}}*/}
                    {/*                    placeholder={formatMessage({id: 'placeholder.select.common'})}*/}
                    {/*                    _title={formatMessage({id: 'drug.freeze.institute'})}*/}
                    {/*                    onChange={e => {*/}
                    {/*                        page.setCurrentPage(1);*/}
                    {/*                        setFreezeInstituteId(e)*/}
                    {/*                    }}*/}
                    {/*                    allowClear*/}
                    {/*                    options={freezeInstitutes}*/}
                    {/*                    value={freezeInstituteId}*/}
                    {/*>*/}
                    {/*</SelectEmbeddeTitle>*/}
                    <Form.Item label={formatMessage({ id: 'drug.freeze.institute', allowComponent: true })}>
                        <Select style={{ width: "100%", minWidth: 200 }}
                            dropdownMatchSelectWidth={false}
                            dropdownStyle={{ maxWidth: 400 }}
                            placeholder={<span style={{ color: "#1D2129" }}>{formatMessage({ id: "common.all" })}</span>}
                            options={freezeInstitutes} value={freezeInstituteId} onChange={e => {
                                page.setCurrentPage(1);
                                setFreezeInstituteId(e)
                            }} allowClear />
                    </Form.Item>

                </Col>
                <Col className="mar-ver-5" style={{ paddingLeft: 20 }}>
                    <Form.Item label={formatMessage({ id: 'drug.freeze.ipNumber', allowComponent: true })}>
                        <Input.Search
                            style={{ width: 320 }}
                            placeholder={
                                formatMessage({ id: "common.required.prefix" }) +
                                formatMessage({ id: "drug.freeze.ipNumber" })
                            }
                            ref={searchInputRef}
                            value={ipNumber}
                            onChange={(e) => {
                                setIpNumber(e.target.value);
                            }}
                            allowClear
                            onSearch={() => {
                                searchInputRef?.current?.blur();
                                page.setCurrentPage(1);
                                setDoSearch(doSearch + 1);
                            }}
                        />
                    </Form.Item>
                </Col>
            </Row>
            <Table
                loading={sitesAndStorehousesLoading || getUserSitesAndStorehousesLoading || getMedicineFreezeLoading}
                dataSource={page.data}
                sticky
                style={{ maxHeight: "calc(100vh - 245px)", overflowY: "auto", }}
                pagination={false}
                rowKey={(record) => (record._id)}
            >
                <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={70}
                    render={(text, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)} />
                <Table.Column title={<FormattedMessage id="drug.freeze.number" />} key="number"
                    dataIndex="number" align="left" ellipsis />
                <Table.Column
                    title={<FormattedMessage id="drug.freeze.institute" />}
                    key="institute_id"
                    dataIndex="institute_id"
                    align="left"
                    ellipsis
                    render={(value, record, index) => (
                        <Tooltip arrow={false} overlayInnerStyle={{ background: "#646566" }} title={renderInstitute(record)}>
                            {renderInstitute(record)}
                        </Tooltip>
                    )
                    }
                />
                <Table.Column
                    width={180}
                    title={<FormattedMessage id="drug.freeze.startDate" />}
                    key={["meta", "created_at"]}
                    dataIndex={["meta", "created_at"]}
                    align="left"
                    ellipsis
                    render={
                        (value, record, index) => (
                            (value === null || value === 0) ? '' : moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                        )
                    }
                />
                {/* <Table.Column title={<FormattedMessage id="drug.freeze.operator"/>}
                                key={["meta", "createdUser", [0], "info", "name"]}
                                dataIndex={["meta", "createdUser", [0], "info", "name"]} align="left" ellipsis/>
                    <Table.Column
                        title={<FormattedMessage id="drug.freeze.endDate"/>}
                        key="close_date"
                        dataIndex="close_date"
                        align="left"
                        ellipsis
                        render={
                            (value, record, index) => (
                                (value === null || value === 0) ? '' : moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                            )
                        }
                    /> */}
                <Table.Column
                    title={<FormattedMessage id="drug.freeze.count" />}
                    key="count"
                    dataIndex="count"
                    align="center"
                    ellipsis
                    render={
                        (value, record, index) => (
                            record.count > 0 ?
                                <AuthSpan
                                    style={{ color: "#165DFF" }}
                                    className="mouse"
                                    onClick={() => quantity(record)}>
                                    {record.count}
                                </AuthSpan>
                                :
                                "-"
                        )
                    }
                />
                <Table.Column
                    title={<FormattedMessage id="common.operation" />}
                    width={250}
                    fixed="right"
                    render={
                        (value, record, index) => (
                            <Space size={4}>
                                {record.show > 0
                                    && permissions(auth.project.permissions, "operation.supply.freeze.release")
                                    && projectStatus !== 2
                                    && record.bl
                                    && <AuthButton
                                        size="small"
                                        type="link"
                                        onClick={() => isolate(record, 1)}
                                    ><FormattedMessage id="drug.list.release" /></AuthButton>
                                }
                                {record.show > 0
                                    && permissions(auth.project.permissions, "operation.supply.freeze.approval")
                                    && projectStatus !== 2
                                    && !record.bl
                                    && <AuthButton
                                        size="small"
                                        type="link"
                                        onClick={() => isolate(record, 2)}
                                    ><FormattedMessage id="project.de.isolation.approval" /></AuthButton>
                                }
                                {record.show > 0
                                    && record.show > record.isolationCount
                                    && permissions(auth.project.permissions, "operation.supply.freeze.delete")
                                    && projectStatus !== 2
                                    && <AuthButton
                                        size="small"
                                        type="link"
                                        onClick={() => isolate(record, 3)}
                                    ><FormattedMessage id="medicine.status.lose" /></AuthButton>
                                }
                                {permissions(auth.project.permissions, "operation.supply.freeze.history")
                                    && <AuthButton
                                        size="small"
                                        type="link"
                                        onClick={() => showHistory(record._id)}
                                    ><FormattedMessage id="common.history" /></AuthButton>
                                }
                            </Space>
                        )
                    }
                />
            </Table>
            <Footer>
                <PaginationView />
            </Footer>
            <PageContextProvider>
                <SelectedIsolateRelease bind={selected_isolate_release} refresh={getList} />
            </PageContextProvider>
            <PageContextProvider>
                <FreezeQuantity bind={freeze_quantity} />
            </PageContextProvider>
            <HistoryList bind={history_ref} permission={permissions(auth.project.permissions, "operation.supply.freeze.print")} />
        </React.Fragment >
    )
};

