import React from "react";
import { But<PERSON>, Col, Form, Input, InputNumber, message, Modal, Radio, Row, Table, Typography, Spin } from "antd";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { medicineFreezeDetailsList, releaseMedicines } from "../../../../api/medicine";
import styled from "@emotion/styled";
import { Title } from "components/title";
import { useGlobal } from "../../../../context/global";
import { combineRow } from "../../../../utils/merge_cell";
import { useRowSelection } from "./row-selection";
import { SelectableOrderTable, UnSelectableOrderTable } from "./order-table";
import { useCacheTable } from "hooks/cache-rowSpan-table";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";


export const SelectedIsolateRelease = (props) => {
    const g = useGlobal()
    const [visible, setVisible] = useSafeState(false);
    const [type, setType] = useSafeState(0);
    const [title, setTitle] = useSafeState("");
    const [reason, setReason] = useSafeState("");
    const [approvalStatus, setApprovalStatus] = useSafeState(0);
    const [instituteType, setInstituteType] = useSafeState(0);
    const [orderType, setOrderType] = useSafeState(0);
    const [otherTableData, setOtherTableData] = useSafeState(null);
    //const [medicineIds, setMedicineIds] = useSafeState([]);
    const [approvalMedicineIds, setApprovalMedicineIds] = useSafeState([]);
    const [medicines, setMedicines] = useSafeState([]);
    const [form] = Form.useForm();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [id, setId] = useSafeState("");
    const [status, setStatus] = useSafeState(0);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [havePackage, setHavePackage] = useSafeState(false);
    const [haveSingle, setHaveSingle] = useSafeState(false);
    const [data, setData] = useSafeState([]);
    //const son_page = usePage();
    const auth = useAuth();
    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const deIsolationApproval = auth.project.info.de_isolation_approval;
    const [ipNumber, setIpNumber] = useSafeState("");
    const searchInputRef = React.useRef();
    const [doSearch, setDoSearch] = useSafeState(0);
    const tableData = useCacheTable({ dataSource: data });

    const [upperNumber, setUpperNumber] = useSafeState("");
    const [lowerNumber, setLowerNumber] = useSafeState("");
    const { Link } = Typography;

    const show = (data, type) => {
        setInstituteType(data.institute_type);
        setOrderType(data.order_type);
        //setMedicineIds([]);
        setApprovalMedicineIds([])
        resetSelectedIds();
        setMedicines([]);
        setOtherTableData(null);
        // son_page.setTotal(0);
        // son_page.setData([]);
        setData([]);
        form.resetFields();
        // title
        let temporaryStatus = 0;
        if (type === 1) {
            setTitle(formatMessage({ id: 'drug.list.release' }))
            temporaryStatus = 4;
        } else if (type === 2) {
            setTitle(formatMessage({ id: 'project.de.isolation.approval' }))
            temporaryStatus = 15;
        } else {
            setTitle(formatMessage({ id: 'medicine.status.lose' }))
            temporaryStatus = 4;
        }
        // 赋值
        if (data) {
            setId(data._id);
            setStatus(temporaryStatus);
            setType(type);

            setReason(data.untie_reason);
            // if (data.medicines != null && data.medicines.length > 0) {
            getMedicineFreezeDetailsList(data._id, temporaryStatus, type);
            //}
            // if (type === 2 && data.other_medicines_new != null && data.other_medicines_new.length > 0) {
            //     let otherMedicines = [];
            //     data.other_medicines.forEach(it => {
            //         if (it.approved_quantity > 0) {
            //             otherMedicines.push(it)
            //         }
            //     });
            //     setOtherTableData(fillTableCellEmptyPlaceholder(otherMedicines ? otherMedicines : []));
            // } else {
            //     setOtherTableData(fillTableCellEmptyPlaceholder(data.other_medicines ? data.other_medicines : []));
            // }
            setVisible(true);
        }
    };

    const hide = () => {
        setVisible(false);
        setHavePackage(false);
        setHaveSingle(false);
        //setMedicineIds([]);
        resetSelectedIds();
        setApprovalMedicineIds(null);
        setMedicines([]);
        setOtherTableData(null);
        setTitle("");
        setId("");
        setType(0);
        setApprovalStatus(0);
        setOrderType(0);
        setInstituteType(0);
        setReason("");
        setIpNumber("");
        setUpperNumber("");
        setLowerNumber("");
        setStatus(0);
        form.resetFields();
        props.refresh();
    };
    const { runAsync: run_releaseMedicines, loading } = useFetch(releaseMedicines, { manual: true });
    const { runAsync: run_medicineFreezeDetailsList, loading: run_medicineFreezeDetailsListLoading } = useFetch(medicineFreezeDetailsList, { manual: true });
    function confirmRelease() {
        form.validateFields().then(values => {
            let status = 0;
            if (type === 1) {
                if (deIsolationApproval === 1) {  // 解隔离
                    status = 15 // 待审批
                } else {
                    status = 1  // 可用
                }
            } else if (type === 2) {       // 隔离审批
                if (approvalStatus === 1) { // 审批通过
                    status = 1  // 可用
                    values.reason = reason
                } else {  // 审批拒绝
                    status = 4   //隔离
                }
                if (otherTableData !== null && otherTableData.length > 0) {
                    otherTableData.forEach((item) => {
                        item.approved_quantity = item.count
                    });
                }
                if (approvalMedicineIds !== null) {
                    approvalMedicineIds.forEach((item) => {
                        medicineIds.push(item);
                    });
                }
            } else if (type === 3) {       //丢失作废
                status = 6 //作废丢弃
            }
            if ((medicineIds != null && medicineIds.length > 0) || (otherTableData != null && otherTableData.length > 0)) {
                run_releaseMedicines({
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    freezeId: id,
                    medicineIds: medicineIds,
                    reason: values.reason,
                    status: status,
                    approvalStatus: approvalStatus,
                    otherMedicines: otherTableData
                }).then((data) => {
                    message.success(data.msg);
                    props.refresh();
                    hide();
                })
            } else {
                message.error(formatMessage({ id: 'subject.dispensing.selectDrugNumber' }))
            }
        }).catch(errorInfo => {
            // 验证失败时的操作
            console.log('Validation failed:', errorInfo);
            if (errorInfo.errorFields.length > 0) {
                // 获取第一个出错字段的名字
                const firstErrorFieldName = errorInfo.errorFields[0].name.join('.');
                scrollToField(firstErrorFieldName);
            }
        });
    }



    const getMedicineFreezeDetailsList = (oid, temporaryStatus, oType) => {
        let oid_id = oid;
        let temporary_status = temporaryStatus;
        let mType = oType;
        if (id !== undefined && id !== "") {
            oid_id = id;
        }
        if (status !== undefined && status != null && status !== 0) {
            temporary_status = status;
        }
        if (type !== undefined && type != null && type !== 0) {
            mType = type;
        }

        if (oid_id !== undefined && oid_id !== "" && temporary_status !== undefined && temporary_status != null && temporary_status !== 0) {
            run_medicineFreezeDetailsList({
                id: oid_id,
                // start: (son_page.currentPage - 1) * son_page.pageSize,
                // limit: son_page.pageSize,
                // attributeId: auth.attribute.id,
                roleId: auth.project.permissions.role_id,
                status: temporary_status,
                ipNumber: ipNumber,
                upperNumber: upperNumber,
                lowerNumber: lowerNumber,
                sign: 2,                // sign 1 查询所有隔离的药物 2 查询待解隔离或者待审批的药物
            }).then(
                (result) => {
                    //son_page.setTotal(result.data.total);
                    setPackageIsOpen(result.data.packageIsOpen);
                    if (mType === 2) {
                        setApprovalMedicineIds(result.data.medicineIds)
                    }
                    if (result.data.items != null) {
                        if (result.data.packageIsOpen) {
                            var tableData = combineRow(result.data.items, "package_number", "package_number", false)
                            tableData = combineRow(result.data.items, "name", "name", false)
                            // son_page.setData(tableData);
                            setData(fillTableCellEmptyPlaceholder(tableData ? tableData : []))
                        } else {
                            // son_page.setData(result.data.items);
                            setData(fillTableCellEmptyPlaceholder(result.data.items ? result.data.items : []))
                        }
                    } else {
                        //son_page.setData([]);
                        setData([]);
                    }
                    if (result.data.otherData != null) {
                        setOtherTableData(fillTableCellEmptyPlaceholder(result.data.otherData ? result.data.otherData : []));
                        for (let i = 0; i < result.data.otherData.length; i++) {
                            if (result.data.otherData[i].package_method) {
                                setHavePackage(true)
                            } else {
                                setHaveSingle(true)
                            }
                        }
                    }
                }
            )
        }
    };

    const { selectedIds: medicineIds, resetSelectedIds, rowSelection } =
        useRowSelection({
            dataSource: tableData,
            allData: data,
            key: "_id",
            packageIsOpen: packageIsOpen,
            allSelected: false,
            renderCell: (checked, record, index, originNode, package_numberRowSpan) => {
                return {
                    children: originNode,
                    props: {
                        rowSpan: tableData[index] !== undefined ? tableData[index]["package_numberRowSpan"] : 1
                    }
                }
            }
        })



    const refreshList = () => {
        //setMedicineIds(null);
        setApprovalMedicineIds(null);
        resetSelectedIds();
        setMedicines(null);
    };

    const otherHandleChange = (value, record) => {
        const _data = [...otherTableData];
        _data.forEach(it => {
            if (it.id === record.id && it.name === record.name && it.batch === record.batch && it.expire_date === record.expire_date) {
                it.useCount = value["useCount"]
            }
        });
        setOtherTableData(_data)
    };

    function renderPackageMethod(packageMethod) {
        return packageMethod === true && (instituteType !== 1 || orderType === 1) ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }


    const onChange = (e) => {
        setApprovalStatus(e.target.value);
    };

    const scrollToField = fieldKey => {
        const labelNode = document.querySelector(`label[for="${fieldKey}"]`);
        if (labelNode) {
            labelNode.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    };

    const { TextArea } = Input;


    React.useEffect(getMedicineFreezeDetailsList, [upperNumber, lowerNumber]);

    React.useImperativeHandle(props.bind, () => ({ show }));
    React.useEffect(getMedicineFreezeDetailsList, [doSearch]);
    return (
        <Modal
            className="custom-blarge-modal"
            title={title}
            visible={visible}
            onCancel={hide}
            destroyOnClose={true}
            centered
            maskClosable={false}
            footer={
                (otherTableData != null && otherTableData.length > 0) || (data != null && data.length > 0) ?
                    <Row justify="end">
                        <Col>
                            <Button className="mar-rgt-10" loading={loading}
                                onClick={() => hide()}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                            <Button type="primary" loading={loading} onClick={() => confirmRelease()}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                    : null

            }
        >
            <Spin spinning={run_medicineFreezeDetailsListLoading}>
                <Form
                    form={form}
                >
                    {data != null && data.length > 0 &&
                        <>
                            {type === 2 ?
                                <>
                                    <Title name={formatMessage({ id: 'shipment.medicine' })} />
                                    <UnSelectableOrderTable dataSource={data} packageIsOpen={packageIsOpen} />
                                </>
                                :
                                <>
                                    <Title name={formatMessage({ id: 'shipment.medicine' })} />
                                    {/* <CustomSearch
                                        style={{ width: g.lang === "en" ? 250 : 200, marginTop: "12px", marginBottom: "12px" }}
                                        placeholder={formatMessage({ id: 'common.required.prefix' }) + formatMessage({ id: 'drug.list.drugNumber' })}
                                        ref={searchInputRef}
                                        value={ipNumber}
                                        onChange={e => {
                                            setIpNumber(e.target.value)
                                        }}
                                        allowClear onSearch={() => {
                                            searchInputRef?.current?.blur();
                                            //son_page.setCurrentPage(1);
                                            setDoSearch(doSearch + 1);
                                        }}
                                    /> */}
                                    <Row>
                                        <div style={{ textAlign: "left", display: "inline-block", marginTop: "16px", marginBottom: "16px" }}>
                                            <Col style={{ textAlign: "left" }}>
                                                {
                                                    formatMessage({
                                                        id: "drug.list.drugNumber",
                                                    }) + "："
                                                }
                                                <Input
                                                    placeholder={formatMessage({
                                                        id: "common.required.prefix",
                                                    })}
                                                    onChange={(e) => {
                                                        if(e.target.value !== undefined && e.target.value !== null && e.target.value !== ""){
                                                            const value = e.target.value.trim().replace(/\s+/g, ' ');
                                                            setUpperNumber(value);
                                                        } else {
                                                            setUpperNumber("");
                                                        }
                                                    }}
                                                    value={upperNumber}
                                                    style={{ width: 200, marginRight: 12 }}
                                                    allowClear
                                                />
                                                ～
                                                <Input
                                                    placeholder={formatMessage({
                                                        id: "common.required.prefix",
                                                    })}
                                                    onChange={(e) => {
                                                        if(e.target.value !== undefined && e.target.value !== null && e.target.value !== ""){
                                                            const value = e.target.value.trim().replace(/\s+/g, ' ');
                                                            setLowerNumber(value);
                                                        } else {
                                                            setLowerNumber("");
                                                        }
                                                    }}
                                                    value={lowerNumber}
                                                    style={{ width: 200, marginLeft: 12 }}
                                                    allowClear
                                                />
                                                {
                                                    ((upperNumber !== null && upperNumber !== undefined && upperNumber !== "") ||
                                                        (lowerNumber !== null && lowerNumber !== undefined && lowerNumber !== "")) &&
                                                    <Link
                                                        className="mar-rgt-5"
                                                        style={{ marginLeft: "8px" }}
                                                        onClick={(e) => {
                                                            setUpperNumber("");
                                                            setLowerNumber("");
                                                        }}
                                                    >
                                                        {formatMessage({ id: "common.pagination.empty" })}
                                                    </Link>
                                                }
                                            </Col>
                                        </div>
                                    </Row>
                                    <SelectableOrderTable
                                        dataSource={data}
                                        selectedIds={medicineIds}
                                        rowSelection={rowSelection}
                                        clearDisplay={true}
                                        refresh={refreshList}
                                        packageIsOpen={packageIsOpen}
                                    />
                                </>
                            }
                        </>
                    }
                    {otherTableData != null && otherTableData.length > 0 ?
                        <>
                            <div style={{ marginTop: 12, marginBottom: 12 }}>
                                <Title name={formatMessage({ id: 'shipment.other.drug' })}></Title>

                            </div>
                            <Table
                                className="mar-top-10"
                                size="small"
                                dataSource={otherTableData}
                                pagination={false}
                                rowKey={(record) => (record.id)}
                            >
                                <Table.Column
                                    title={<FormattedMessage id="drug.configure.drugName" />}
                                    dataIndex={"name"}
                                    key="name"
                                    ellipsis
                                    render={(value, record, index) =>
                                        <div style={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'normal',
                                            wordBreak: 'break-word',
                                        }}>
                                            {value}
                                        </div>
                                    }
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.list.expireDate" />}
                                    dataIndex={"expirationDate"}
                                    key="expirationDate"
                                    width={120}
                                    ellipsis
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.list.batch" />}
                                    dataIndex={"batchNumber"}
                                    key="batchNumber"
                                    width={140}
                                    ellipsis
                                />
                                {packageIsOpen && (
                                    <Table.Column
                                        title={intl.formatMessage({
                                            id: "shipment.order.package.method",
                                        })}
                                        width={110}
                                        dataIndex="package_method"
                                        key="package_method"
                                        ellipsis
                                        render={(value, record, index) =>
                                            renderPackageMethod(value)
                                        }
                                    />
                                )}
                                {type !== 2 ?
                                    <Table.Column
                                        title={(instituteType === 1 && orderType !== 1) ? <FormattedMessage id="drug.isolation.single.quantity" /> : (havePackage && !haveSingle) ? <FormattedMessage id="drug.isolation.package.quantity" /> : <FormattedMessage id="drug.isolation.quantity" />}
                                        dataIndex={"count"}
                                        key="count"
                                        width={90}
                                        ellipsis
                                        render={(value, record, index) => {
                                            return (instituteType === 1 && orderType !== 1) ? value : record.package_method ? record.allCount + "(" + value + ")" : value;
                                        }}
                                    />
                                    :
                                    <Table.Column
                                        title={<FormattedMessage id="drug.other.count" />}
                                        dataIndex={"count"}
                                        key="approved_quantity"
                                        width={90}
                                        ellipsis
                                    />
                                }

                                {type !== 2 ?
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.count" />}
                                        dataIndex={"useCount"}
                                        key="useCount"
                                        ellipsis
                                        width={120}
                                        render={
                                            (value, record, index) => (
                                                <InputNumber precision={0} min={0} step={1} max={record.count}
                                                    onChange={(e) => otherHandleChange({ useCount: e }, record)} />
                                            )
                                        }
                                    />
                                    : null
                                }
                            </Table></>
                        : null
                    }

                    {/* {type === 1 ?
                        <>
                            <br />
                            <div style={{ marginLeft: 0, marginBottom: 22 }}>
                                <Title name={formatMessage({ id: 'project.reason.for.de.isolation' })}></Title>
                            </div>
                        </>
                        :
                        null
                    }
                    {type === 3 ?
                        <>
                            <br />
                            <div style={{ marginLeft: 0, marginBottom: 22 }}>
                                <Title name={formatMessage({ id: 'report.attributes.research.lost.reason' })}></Title>
                            </div>
                        </>
                        :
                        null
                    } */}

                    {(otherTableData != null && otherTableData.length > 0) || (data != null && data.length > 0) ?
                        <>
                            {type === 2 &&
                                <>
                                    <br /> <div style={{ marginLeft: 0, marginBottom: 22 }}>
                                        <Title name={formatMessage({ id: 'project.reason.for.de.isolation' })}></Title>
                                    </div><br />
                                    <Row wrap={false} style={{ lineHeight: 1 }}>
                                        <Col>
                                            <p style={{ color: "#4E5969" }}><FormattedMessage
                                                id="drug.freeze.reason"></FormattedMessage>:</p>
                                        </Col>
                                        <Col flex={"auto"}>
                                            <p style={{ marginBottom: "12px", marginLeft: 8 }}>{reason}</p></Col>
                                    </Row>
                                    <div style={{ marginLeft: 0, marginTop: 24, marginBottom: 12 }}>
                                        <Title name={formatMessage({ id: 'project.de.isolation.approval' })}></Title>
                                    </div>
                                    <Form.Item
                                        label={formatMessage({ id: 'shipment.approval.confirm' })}
                                        rules={[{ required: true }]}
                                        name="approvalStatus"
                                        style={{ marginTop: "10px", marginBottom: "0px" }}>
                                        <Radio.Group style={{ width: 400 }} onChange={onChange}>
                                            <Radio value={1}><FormattedMessage
                                                id="subject.unblinding.approval.agree" /></Radio>
                                            <Radio value={2}><FormattedMessage
                                                id="subject.unblinding.approval.reject" /></Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                </>
                            }
                            {type !== 2 || (type === 2 && approvalStatus === 2) ?
                                <Form.Item label={formatMessage({ id: 'drug.freeze.reason' })} rules={[{ required: true }]} name="reason"
                                    style={{ marginBottom: "0px", marginTop: 16 }}>
                                    <TextArea allowClear placeholder={intl.formatMessage({ id: "placeholder.input.common" })} />
                                </Form.Item>
                                :
                                null
                            }
                        </>
                        :
                        null
                    }


                </Form>
            </Spin>
        </Modal>

    );
};

const CustomSearch = styled(Input.Search)`
    .ant-input-search-button {
        height: 31px !important;
    }
`
