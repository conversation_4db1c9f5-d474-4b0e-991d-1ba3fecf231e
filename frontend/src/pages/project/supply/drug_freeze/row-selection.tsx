import { useSafeState } from "ahooks";
import { Checkbox, Dropdown, Menu, Table } from "antd";
import { useCallback, useEffect } from "react";
import _ from "lodash";
import { DownOutlined } from "@ant-design/icons";
import {useIntl} from "react-intl";

interface rowSelectionProp {
    dataSource: any[];
    allData: any[];
    key: string;
    renderCell: any;
    packageIsOpen: boolean;
    allSelected: boolean;
    onChange: any,
    //changeKey: any,
    operation: any,
    mixPackage: any;
}

export const useRowSelection = (props: rowSelectionProp) => {

    const intl = useIntl();

    const [selectKeysSet] = useSafeState<Set<string>>(new Set());
    const [selectedIds, setSelectedIds] = useSafeState<any[]>([]);
    let [currentSelectList, setCurrentSelectList] = useSafeState<any>([]);

    const resetSelectedIds = useCallback(() => {
        selectKeysSet.clear();
        setSelectedIds([]);
        // props.changeKey([])
        setCurrentSelectList([]);
    }, []);
    const headerCheckbox = () => {
        const actualList = props.packageIsOpen
            ? props.dataSource.filter(
                (item: any) =>
                    (item.package_numberRowSpan !== undefined &&
                        item.package_numberRowSpan !== 0) ||
                    item.package_numberRowSpan === undefined
            )
            : props.dataSource;

        const selectedList = _.intersection(
            currentSelectList,
            actualList.map((item: any) => item._id)
        );

        const totalDataCount = props.dataSource.length;
        const selectedCount = selectedIds.length;

        const isChecked = selectedCount === totalDataCount;
        const isIndeterminate = selectedCount > 0 && selectedCount < totalDataCount;

        const onSelectAll = (b: any) => {
            if(b){
                const keys: string[] = [];
                props.allData.forEach((it: any) => {
                    keys.push(it[props.key]);
                    selectKeysSet.add(it[props.key]);
                });
                setCurrentSelectList(keys);
                setSelectedIds(keys);

                // props.dataSource.forEach((it: any) => {
                //     selectKeysSet.add(it[props.key]);
                // });

            } else {
                selectKeysSet.clear();
                setSelectedIds([]);
                // props.changeKey([])
                setCurrentSelectList([]);
            }
        };

        // 反选逻辑
        const onSelectInvert = () => {

            const actualList: any = [];
            selectKeysSet.forEach(value => actualList.push(value));

            //当页未选中的
            const aSelectedKeys = props.dataSource.filter((item: any) => !actualList.includes(item[props.key]));

            //当页已选中的
            const bKeys: string[] = [];
            const bSelectedKeys = props.dataSource.filter((item: any) => actualList.includes(item[props.key]));
            bSelectedKeys.forEach((it: any) => {
                bKeys.push(it[props.key]);
                selectKeysSet.delete(it[props.key]);
            });

            const cSelectedKeys = currentSelectList.filter((item: any) => !bKeys.includes(item));
            const dSelectedKeys = selectedIds.filter((item: any) => !bKeys.includes(item));

            aSelectedKeys.forEach((it: any) => {
                // keys.push(it[props.key]);
                selectKeysSet.add(it[props.key]);
                cSelectedKeys.push(it[props.key]);
                dSelectedKeys.push(it[props.key]);
            });

            setCurrentSelectList(cSelectedKeys);
            setSelectedIds(dSelectedKeys);
        };

        const menu = (
            <Menu>
                <Menu.Item onClick={() => onSelectAll(true)}>{intl.formatMessage({id: "common.select.all"})}</Menu.Item>
                <Menu.Item onClick={() => onSelectInvert()}>{intl.formatMessage({id: "common.select.invert.currentPage"})}</Menu.Item>
                <Menu.Item onClick={() => onSelectAll(false)}>{intl.formatMessage({id: "common.select.clearAll"})}</Menu.Item>
            </Menu>
        );
        

        return (
            <div style={{ display: 'flex', alignItems: 'center', height: '100%'}}>
                <Checkbox
                    checked={
                        !!selectedList.length && selectedList.length === actualList.length
                    }
                    indeterminate={
                        !!selectedList.length && selectedList.length < actualList.length
                    }
                    onChange={(e) => {
                        if (e.target.checked) {
                            actualList.forEach((item: any) => {
                                selectKeysSet.add(item._id);
                                currentSelectList.push(item._id);
                            });
                        } else {
                            var del: any = []
                            actualList.forEach((item: any) => {
                                selectKeysSet.delete(item._id);
                                del.push(item._id);
                                // currentSelectList = [];
                            });
                            _.pullAll(currentSelectList, del)
                        }
                        const keys: string[] = [];
                        if (props.packageIsOpen) {
                            selectKeysSet.forEach((v) => {
                                props.allData.forEach((it: any) => {
                                    if (v === it._id && it.package_numberRowSpan !== 0) {
                                        keys.push(v);
                                    }
                                });
                            });
                            //手动勾选选中全部数据
                            const allPackagekeys: string[] = [];
                            const allkeys: string[] = [];
                            props.allData.forEach((it: any) => {
                                if (
                                    !props.packageIsOpen ||
                                    (props.packageIsOpen && it.package_numberRowSpan !== 0)
                                ) {
                                    allPackagekeys.push(it[props.key]);
                                }
                                allkeys.push(it[props.key]);
                            });
                            if (
                                keys.length === allPackagekeys.length &&
                                allPackagekeys.filter((t) => !keys.includes(t))
                            ) {
                                setSelectedIds(allkeys);
                            } else {
                                setSelectedIds(keys);
                            }
                        } else {
                            selectKeysSet.forEach((v) => {
                                keys.push(v);
                            });
                            setSelectedIds(keys);
                        }
                        setCurrentSelectList([...currentSelectList]);
                    }}
                //onClick={onSelectAll}
                ></Checkbox>
                <Dropdown overlay={menu} trigger={['click']} >
                    <span
                        style={{
                            cursor: 'pointer',
                            padding: '4px 8px',
                            background: '#f0f0f0',
                            borderRadius: 4,
                            marginLeft: 8,
                            display: 'inline-flex',
                            alignItems: 'center',
                        }}
                    >
                        <DownOutlined />
                    </span>
                </Dropdown>
            </div>
        );
    };
    const rowSelection = {
        columnTitle: headerCheckbox,
        type: "checkbox" as "checkbox",
        selectedRowKeys: selectedIds,
        onSelect: (record: any, selected: boolean) => {
            if (selected) {
                selectKeysSet.add(record[props.key]);
                currentSelectList.push(record[props.key]);
            } else {
                selectKeysSet.delete(record[props.key]);
                const index = _.indexOf(currentSelectList, record[props.key])
                currentSelectList.splice(index, 1);
            }
            setCurrentSelectList([...currentSelectList]);
            const keys: string[] = [];
            if (props.packageIsOpen) {
                selectKeysSet.forEach((v) => {
                    props.dataSource.forEach((it: any) => {
                        if (v === it._id && it.package_numberRowSpan !== 0) {
                            keys.push(v);
                        }
                    });
                });
                if (props.operation === "add") {
                    let packageNames: any = props.mixPackage[record.name]
                    if (packageNames !== undefined && packageNames.length > 1) {
                        props.dataSource.forEach((it: any) => {
                            if (packageNames.includes(it.name) && record._id !== it._id && record.batchNumber === it.batchNumber && record.expirationData === it.expirationData && record.packageMethod === it.packageMethod) {
                                if (selected) {
                                    keys.push(it._id);
                                    selectKeysSet.add(it._id);
                                    currentSelectList.push(it._id);
                                } else {
                                    const index: any = keys.indexOf(it._id)
                                    if (index !== -1) {
                                        keys.splice(index, 1);
                                        selectKeysSet.delete(it._id);
                                    }
                                }
                            }
                        });
                    }
                }
                //手动勾选选中全部数据
                const allPackagekeys: string[] = [];
                const allkeys: string[] = [];
                props.dataSource.forEach((it: any) => {
                    if (
                        !props.packageIsOpen ||
                        (props.packageIsOpen && it.package_numberRowSpan !== 0)
                    ) {
                        allPackagekeys.push(it[props.key]);
                    }
                    allkeys.push(it[props.key]);
                });

                if (
                    keys.length === allPackagekeys.length &&
                    allPackagekeys.filter((t) => !keys.includes(t))
                ) {
                    setSelectedIds(allkeys);
                } else {
                    setSelectedIds(keys);
                }
            } else {
                selectKeysSet.forEach((v) => {
                    keys.push(v);
                });
                setSelectedIds(keys);
            }
            // if (props.changeKey !== undefined && props.changeKey !== null) {
            //     props?.changeKey(keys)
            // }
        },
        renderCell: props.renderCell,
        onChange: props.onChange,
        // 可以用 customRow 控制 checkbox 行内元素的样式
        getCheckboxProps: (record: any) => ({
            style: { marginLeft: -34, marginBottom: 0 }, // 确保每行的 checkbox 样式一致
        }),
    };
    useEffect(() => {
        // 数据为空时，清空状态
        if (props.dataSource.length === 0) {
            // selectKeysSet.clear();
            // setSelectedIds([]);
            //currentSelectList = [];
        } else {
            if (props.allSelected) {
                currentSelectList = [];
                const keys: string[] = [];
                props.allData.forEach((it: any) => {
                    selectKeysSet.add(it[props.key]);
                    keys.push(it[props.key]);
                    currentSelectList.push(it[props.key]);
                });
                setSelectedIds(keys);
                // props.changeKey(keys)
                setCurrentSelectList([...currentSelectList]);
            }
        }
    }, [props.dataSource]);
    return {
        selectedIds: selectedIds,
        resetSelectedIds: resetSelectedIds,
        setSelectedIds: setSelectedIds,
        rowSelection: rowSelection,
    };
};
