import React from "react";
import { <PERSON><PERSON>, Col, DatePicker, Form, Input, message, Row, Select, Spin, Table, Tooltip } from "antd";
import { SelectedOtherMedicineFreeze } from "./selected_other_medicine_freeze";
import { Reason } from "./reason";
import { permissions } from '../../../../tools/permission'
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { otherMedicineStorehouseSku } from "../../../../api/medicine";
import { HistoryList } from "../../../common/history-list.jsx";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";
import styled from "@emotion/styled";
import { useGlobal } from "../../../../context/global";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { InfoCircleFilled } from "@ant-design/icons";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { AuthButton } from "../../../common/auth-wrap";

export const OtherMedicineSku = (props) => {
    const g = useGlobal()
    const intl = useTranslation();
    const { formatMessage } = intl;

    const [field, setField] = useSafeState(undefined);
    const [fieldDate, setFieldDate] = useSafeState(null);
    const [fieldValue, setFieldValue] = useSafeState("");
    const searchInputRef = React.useRef();
    const [doSearch, setDoSearch] = useSafeState(0);
    const [isOpenPackage, setIsOpenPackage] = useSafeState(false);
    const [storehouse, setStorehouse] = useSafeState(undefined);
    const [summaryList, setSummaryList] = useSafeState([]);
    const [tableHeight, setTableHeight] = useSafeState(300);
    const [selecteds, setSelecteds] = useSafeState([]);
    const [selectedIds, setSelectedIds] = useSafeState([]);
    const [paramStorehouses, setParamStorehouses] = useSafeState([])
    const [paramStorehousesOp, setParamStorehousesOp] = useSafeState(1)
    const [storehouseMode, setStorehouseMode] = useSafeState({})
    const [storehouseOpen, setStorehouseOpen] = useSafeState(false)
    const selected_freeze = React.useRef()
    const reason_ref = React.useRef()
    const history_ref = React.useRef();
    const page = usePage();

    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0

    const fields = [
        { id: 'name', name: formatMessage({ id: 'drug.configure.drugName', allowComponent: true }) },
        { id: 'batch_number', name: formatMessage({ id: 'drug.list.batch', allowComponent: true }) },
        { id: 'expiration_date', name: formatMessage({ id: 'projects.statistics.sku.expirationDate', allowComponent: true }) },
    ]

    const { runAsync: run_otherMedicineStorehouseSku, loading } = useFetch(otherMedicineStorehouseSku, { manual: true })

    const search = () => {
        setIsOpenPackage(false);
        if (field !== undefined && field != null && field !== "") {
            let key = fieldValue;
            if (fieldValue !== null && fieldValue !== undefined) {
                key = fieldValue.trim();
            }
            run_otherMedicineStorehouseSku({
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                storehouseIds: paramStorehouses,
                //storehouseId: storehouse ? storehouse.split("__")[0] : null,
                field: field,
                fieldValue: key,
                roleId: auth.project.permissions.role_id,
                start: (page.currentPage - 1) * page.pageSize,
                limit: page.pageSize,
            }).then((result) => {
                let response = result.data
                // page.setData(response.items)
                page.setTotal(response.total)
                response.items.forEach(it => {
                    var groupStatus = [];
                    it.group.forEach(groupInfo => {
                        groupStatus[groupInfo.status] = groupInfo
                    })
                    it.count = groupStatus[1] ? groupStatus[1].statusCount : 0
                    it.to_be_send_count = groupStatus[2] ? groupStatus[2].statusCount : 0
                    it.in_transit_count = groupStatus[3] ? groupStatus[3].statusCount : 0
                    it.quarantined_count = groupStatus[4] ? groupStatus[4].statusCount : 0
                    it.used_count = groupStatus[5] ? groupStatus[5].statusCount : 0
                    it.lost_count = groupStatus[6] ? groupStatus[6].statusCount : 0
                    it.expired_count = groupStatus[7] ? groupStatus[7].statusCount : 0
                    it.to_be_confirm_count = groupStatus[11] ? groupStatus[11].statusCount : 0
                    it.locked_count = groupStatus[20] ? groupStatus[20].statusCount : 0
                    it.frozen_count = groupStatus[14] ? groupStatus[14].statusCount : 0
                    if (it.isOpenPackage) {
                        setIsOpenPackage(true);
                        it.packageCount = groupStatus[1] ? groupStatus[1].packageCount : 0
                        it.to_be_send_packageCount = groupStatus[2] ? groupStatus[2].packageCount : 0
                        it.in_transit_packageCount = groupStatus[3] ? groupStatus[3].packageCount : 0
                        it.quarantined_packageCount = groupStatus[4] ? groupStatus[4].packageCount : 0
                        it.used_packageCount = groupStatus[5] ? groupStatus[5].packageCount : 0
                        it.lost_packageCount = groupStatus[6] ? groupStatus[6].packageCount : 0
                        it.expired_packageCount = groupStatus[7] ? groupStatus[7].packageCount : 0
                        it.to_be_confirm_packageCount = groupStatus[11] ? groupStatus[11].packageCount : 0
                        it.locked_packageCount = groupStatus[20] ? groupStatus[20].packageCount : 0
                        it.frozen_packageCount = groupStatus[14] ? groupStatus[14].packageCount : 0
                    }
                });
                setSummaryList(fillTableCellEmptyPlaceholder(response.items ? response.items : []))
            })
        }
    }

    React.useEffect(
        () => {
            setTableHeight(document.documentElement.clientHeight - 250);
            if (props.storehouses && props.storehouses.length > 0) {
                let s = props.storehouses[0];
                setStorehouse(s.id + '__' + s.name);
            }
            setField(fields && fields.length > 0 && fields[0].id)
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const resetSelected = () => {
        setSelecteds([])
        setSelectedIds([])
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(search, [storehouse, doSearch, page.currentPage, page.pageSize])
    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(resetSelected, [page.pageSize, storehouse])

    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setSelecteds(selectedRows);
            setSelectedIds(selectedRowKeys)
        },
        selectedRows: selecteds,
        selectedRowKeys: selectedIds,
        getCheckboxProps: (record) => ({
            disabled: record.count <= 0 && record.expired_count <= 0
        }),
        preserveSelectedRowKeys: true,
    };

    function freezeMedicine(status) {
        if (selecteds == null || selecteds.length <= 0) {
            message.warn(formatMessage({ id: "menu.projects.project.build.randomization.tooltip" }))
        } else {
            let otherValidate = true;
            if (status === 4) { //隔离
                selecteds.forEach(it => {
                    if (it.count === undefined || it.count <= 0) {
                        otherValidate = false
                    }
                });
                if (!otherValidate) {
                    message.warn(formatMessage({ id: "projects.other.sku.freeze.info" }))
                }
            } else if (status === 6) {//丢失作废
                selecteds.forEach(it => {
                    if ((it.count === undefined || it.count <= 0) && (it.expired_count === undefined || it.expired_count <= 0)) {
                        otherValidate = false
                    }
                });
                if (!otherValidate) {
                    message.warn(formatMessage({ id: "projects.other.sku.lost.info" }))
                }
            }
            if (otherValidate) {
                selected_freeze.current.show(selecteds, storehouse.split("__")[0], status);
            }
        }
    }

    const showHistory = (id, timeZone) => {
        if (timeZone && timeZone !== "") {
            let str = timeZone.replace('UTC', '');
            history_ref.current.show("history.medicine", id, null, str);
        } else {
            history_ref.current.show("history.medicine", id, timeZone);
        }
    };

    const refreshList = () => {
        search();
        resetSelected()
    };



    return (
        <React.Fragment>
            <Spin spinning={loading}>
                <Row gutter={12} justify='space-between'>
                    <Col>
                        <Row gutter={12}>
                            <Col className="mar-top-5">
                                <Form.Item label={formatMessage({ id: 'storehouse.name', allowComponent: true })}>
                                    <Select
                                        value={paramStorehousesOp}
                                        style={{ width: 300 }}
                                        {...storehouseMode}
                                        maxTagCount={1}
                                        maxTagTextLength={12}
                                        open={storehouseOpen}
                                        onBlur={() => {
                                            setStorehouseOpen(false)
                                            if (paramStorehousesOp !== null) {
                                                setDoSearch(doSearch + 1)
                                            }
                                        }

                                        }
                                        showArrow={true}
                                        showSearch={false}
                                        onDropdownVisibleChange={(visible) => setStorehouseOpen(visible)}
                                        onChange={(value) => {
                                            if (value === 1 || (Array.isArray(value) && (value.find((i) => i === 1) || value.length === 0))) {
                                                if (storehouseMode.mode != null) {
                                                    setStorehouseMode({})
                                                    setStorehouseOpen(true)
                                                }
                                                setParamStorehouses([])
                                                setParamStorehousesOp(1)
                                                setDoSearch(doSearch + 1)
                                                setStorehouseOpen(false)
                                            } else {
                                                setStorehouseOpen(true)
                                                if (storehouseMode.mode !== "multiple") {
                                                    setStorehouseMode({ mode: "multiple", })
                                                    setDoSearch(doSearch + 1)
                                                    setStorehouseOpen(true)
                                                }
                                                if (!Array.isArray(value)) {
                                                    let siteIds = [value];
                                                    setParamStorehouses(siteIds)
                                                    setParamStorehousesOp(siteIds)
                                                    setDoSearch(doSearch + 1)
                                                } else {
                                                    setParamStorehouses(value)
                                                    setParamStorehousesOp(value)
                                                    setDoSearch(doSearch + 1)
                                                }
                                            }
                                        }}
                                    >
                                        <Select.Option value={1}>{formatMessage({ id: "common.all", allowComponent: true })}</Select.Option>
                                        {
                                            props.storehouses.map(
                                                it => (
                                                    <Select.Option key={it.id} value={it.id} >
                                                        {/* <Tooltip title={it.number + "-" + it.name}>{it.number + "-" + it.name}</Tooltip> */}
                                                        {it.name}
                                                    </Select.Option>
                                                )
                                            )
                                        }
                                    </Select>
                                    {/* <Select style={{ width: "200px" }}
                                        dropdownStyle={{ maxWidth: 400 }}
                                        dropdownMatchSelectWidth={false}
                                        filterOption={(input, option) => {
                                            const childrenText = option.props.children;
                                            if (typeof childrenText === 'string') {
                                                return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                            }
                                            return false;
                                        }}
                                        allowClear showSearch value={storehouse} onChange={(value) => { page.setCurrentPage(1); setStorehouse(value) }}
                               
                                    >
                                        {
                                            props.storehouses.map(
                                                it => (
                                                    <Select.Option key={it.id} value={it.id + "__" + it.name}>

                                                        {it.name}
                                                    </Select.Option>
                                                )
                                            )
                                        }
                                    </Select> */}
                                </Form.Item>
                            </Col>
                            <Col className="mar-top-5">
                                <Input.Group compact>
                                    <Select style={{ width: '204px' }}
                                        placeholder={<FormattedMessage id="projects.statistics.selectField" />}
                                        value={field} onChange={value => {
                                            setFieldValue(null);
                                            setField(value);
                                            setFieldDate(null);
                                        }}>
                                        {
                                            fields.map(
                                                it => (
                                                    <Select.Option key={it.id} value={it.id}>
                                                        {it.name}
                                                    </Select.Option>
                                                )
                                            )
                                        }
                                    </Select>
                                    {
                                        field === "info.expire_date" ?
                                            <DatePicker style={{ width: 'calc(100% - 204px)' }} allowClear value={fieldDate}
                                                onChange={e => {
                                                    setFieldDate(e);
                                                    setFieldValue(e ? e.format("YYYY-MM-DD") : null);
                                                    page.setCurrentPage(1);
                                                    setDoSearch(doSearch + 1);
                                                }} />
                                            :
                                            <CustomSearch
                                                placeholder={formatMessage({ id: 'placeholder.input.common' })}
                                                ref={searchInputRef}
                                                style={{ width: 'calc(100% - 204px)' }}
                                                value={fieldValue}
                                                onChange={e => { setFieldValue(e.target.value) }}
                                                allowClear onSearch={() => {
                                                    searchInputRef?.current?.blur();
                                                    page.setCurrentPage(1);
                                                    setDoSearch(doSearch + 1);
                                                }}
                                            />
                                    }
                                </Input.Group>
                            </Col>
                        </Row>
                    </Col>
                    <Col className="mar-top-5">
                        {
                            permissions(auth.project.permissions, "operation.supply.storehouse.no_number.lost") && paramStorehouses != null && paramStorehouses !== undefined && projectStatus !== 2 ?
                                <CustomButton className="mar-lft-5 mar-top-5 text-right" onClick={() => freezeMedicine(6)}><FormattedMessage
                                    id="medicine.status.lose" /></CustomButton>
                                :
                                null
                        }
                        {
                            permissions(auth.project.permissions, "operation.supply.storehouse.no_number.freeze") && paramStorehouses != null && paramStorehouses !== undefined && projectStatus !== 2 ?
                                <CustomButton className="mar-lft-5 mar-top-5 text-right" type='primary' onClick={() => freezeMedicine(4)}><FormattedMessage
                                    id="drug.list.isolation" /></CustomButton>
                                :
                                null
                        }
                    </Col>
                </Row>

                {isOpenPackage &&
                    <div
                        id="showErrorId"
                        style={{
                            height: "32px",
                            display: "flex",
                            alignItems: "center",
                        }}
                    >
                        <InfoCircleFilled
                            style={{
                                paddingLeft: "8px",
                                paddingRight: "8px",
                                color: "#4072e2",
                            }}
                        />
                        <span>{formatMessage({ id: 'projects.other.package.info', allowComponent: true })}</span>
                    </div>
                }
                <Table
                    className="mar-top-15"
                    loading={loading}
                    scroll={{ x: summaryList.length > 0 ? "100%" : false }}
                    dataSource={summaryList}
                    style={{ maxHeight: tableHeight, overflowY: "auto" }}
                    size="small"
                    pagination={false}
                    rowKey={(record) => (record.storehouse + record.name + record.batchNumber + record.expirationDate)}
                    rowSelection={rowSelection}
                >
                    <Table.Column title={<FormattedMessage id="common.depot" />}
                        dataIndex="storehouseName" fixed="left"
                        key="storehouseName"
                        //ellipsis 
                        width={220} />
                    <Table.Column
                        title={<FormattedMessage id="drug.configure.drugName" />}
                        dataIndex="name" key="name" ellipsis width={220}
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.list.batch" />}
                        dataIndex="batchNumber"
                        key="batchNumber" ellipsis width={220} />
                    <Table.Column title={<FormattedMessage id="projects.statistics.sku.expirationDate" />}
                        dataIndex="expirationDate"
                        key="expirationDate" ellipsis width={120} />
                    <Table.Column title={<FormattedMessage id="medicine.status.available" />}
                        dataIndex="count" key="count" width={110}
                        render={
                            (value, record, index) => record.isOpenPackage && record.count > 0 ? record.count + "(" + record.packageCount + ")" : record.count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.InOrder" />}
                        dataIndex="to_be_confirm_count" key="to_be_confirm_count" width={g.lang === "zh" ? 110 : 150}
                        render={
                            (value, record, index) => record.isOpenPackage && record.to_be_confirm_count > 0 ? record.to_be_confirm_count + "(" + record.to_be_confirm_packageCount + ")" : record.to_be_confirm_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.delivered" />}
                        dataIndex="to_be_send_count" key="to_be_send_count" width={110}
                        render={
                            (value, record, index) => record.isOpenPackage && record.to_be_send_count > 0 ? record.to_be_send_count + "(" + record.to_be_send_packageCount + ")" : record.to_be_send_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.transit" />}
                        dataIndex="in_transit_count" key="in_transit_count" width={110}
                        render={
                            (value, record, index) => record.isOpenPackage && record.in_transit_count > 0 ? record.in_transit_count + "(" + record.in_transit_packageCount + ")" : record.in_transit_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.quarantine" />}
                        dataIndex="quarantined_count" key="quarantined_count" width={120}
                        render={
                            (value, record, index) => record.isOpenPackage && record.quarantined_count > 0 ? record.quarantined_count + "(" + record.quarantined_packageCount + ")" : record.quarantined_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.used" />}
                        dataIndex="used_count" key="used_count" width={110}
                        render={
                            (value, record, index) => record.isOpenPackage && record.used_count > 0 ? record.used_count + "(" + record.used_packageCount + ")" : record.used_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.lose" />}
                        dataIndex="lost_count" key="lost_count" width={120}
                        render={
                            (value, record, index) => record.isOpenPackage && record.lost_count > 0 ? record.lost_count + "(" + record.lost_packageCount + ")" : record.lost_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.expired" />}
                        dataIndex="expired_count" key="expired_count" width={110}
                        render={
                            (value, record, index) => record.isOpenPackage && record.expired_count > 0 ? record.expired_count + "(" + record.expired_packageCount + ")" : record.expired_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.fzn" />}
                        dataIndex="frozen_count" key="frozen_count" width={110}
                        render={
                            (value, record, index) => record.isOpenPackage && record.frozen_count > 0 ? record.frozen_count + "(" + record.frozen_packageCount + ")" : record.frozen_count
                        }
                    />
                    <Table.Column title={<FormattedMessage id="medicine.status.locked" />}
                        dataIndex="locked_count" key="locked_count" width={110}
                        render={
                            (value, record, index) => record.isOpenPackage && record.locked_count > 0 ? record.locked_count + "(" + record.locked_packageCount + ")" : record.locked_count
                        }
                    />
                    {
                        permissions(auth.project.permissions, "operation.supply.storehouse.no_number.history") ?
                            <Table.Column
                                title={<FormattedMessage id="common.operation" />}
                                fixed="right"
                                width={100}
                                render={
                                    (_, record) => (
                                        <AuthButton style={{ padding: '0 14px 0 0' }} size="small" type="link" onClick={() => showHistory(record.id, record.timeZone)}>
                                            <FormattedMessage id="common.history" /></AuthButton>
                                    )
                                }
                            />
                            : null}

                </Table>
                <PaginationView mode='SELECTABLE' selectedNumber={selectedIds?.length} clearDisplay clearDivider refresh={resetSelected} numberStyle={{ fontWeight: 700, color: 'black' }} />
            </Spin>
            <SelectedOtherMedicineFreeze bind={selected_freeze} refresh={refreshList} search={search} />
            <Reason bind={reason_ref} refresh={refreshList} />
            <HistoryList bind={history_ref} permission={permissions(auth.project.permissions, "operation.supply.storehouse.no_number.print")} />
        </React.Fragment>
    )
};

const CustomSearch = styled(Input.Search)`
    .ant-input-affix-wrapper {
        height: 32px !important;
    }
`

const CustomButton = styled(Button)`
    margin-right: 7px !important;
`
