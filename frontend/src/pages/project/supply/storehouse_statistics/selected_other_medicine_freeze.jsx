import React from "react";
import { Col, Form, Input, InputNumber, message, Modal, Row, Table, Select } from "antd";
import { FormattedMessage, useIntl } from "react-intl";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { freezeOtherDrug, updateOtherDrug } from "../../../../api/medicine";
import { useSafeState } from "ahooks";
import { Title } from "components/title";
import { InfoCircleFilled } from "@ant-design/icons";
import { getProjectAttribute } from "../../../../api/randomization";

export const SelectedOtherMedicineFreeze = (props) => {
    const [visible, setVisible] = useSafeState(false);
    const [medicines, setMedicines] = useSafeState([]);
    const [instituteId, setInstituteId] = useSafeState(null);
    const [form] = Form.useForm();
    const intl = useIntl();
    const { formatMessage } = intl;
    const [submitting, setSubmitting] = useSafeState(false);
    const [operStatus, setOperStatus] = useSafeState(null);
    const [title, setTitle] = useSafeState("");
    const [havePackage, setHavePackage] = useSafeState(false);
    const [haveSingle, setHaveSingle] = useSafeState(false);
    const [reasonType, setReasonType] = useSafeState(-1);
    const [attribute, setAttribute] = useSafeState(null);
    const auth = useAuth()
    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;

    const show = (data, instituteId, status) => {
        form.resetFields()
        setVisible(true)
        if (data) {
            for (let i = 0; i < data.length; i++) {
                data[i].lostCount = null
                if (data[i].isOpenPackage) {
                    setHavePackage(true)
                } else {
                    setHaveSingle(true)
                }
            }
            setMedicines(data);
        }
        if (instituteId) {
            setInstituteId(instituteId);
        }
        setOperStatus(status);
        if (status === 4) {
            setTitle(formatMessage({ id: 'drug.list.isolation' }))
        } else {
            setTitle(formatMessage({ id: 'medicine.status.lose' }))
        }
        getAttribute(projectId, envId, "", customerId);
    };

    const hide = () => {
        setVisible(false);
        setReasonType(-1);
        setSubmitting(false);
        setHavePackage(false);
        setHaveSingle(false);
        setMedicines([]);
        form.resetFields();
        props.search();
    };

    const getAttribute = (projectId, envId, cohortId, customerId) => {
        getProjectAttributeRun({ projectId, env: envId, cohort: cohortId, customer: customerId }).then(
            (result) => {
                setAttribute(result.data);
            }
        )
    }
    const { runAsync: getProjectAttributeRun } = useFetch(getProjectAttribute, { manual: true })

    const { runAsync: run_freezeOtherDrug } = useFetch(freezeOtherDrug, { manual: true })
    const confirmFreeze = () => {
        form.validateFields().then(values => {
            setSubmitting(true);
            run_freezeOtherDrug(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    // instituteId: instituteId,
                    instituteType: 2,
                    otherMedicines: medicines,
                    reason: values.reason,
                    remark: values.remark,
                    status: 4
                }).then(
                    (data) => {
                        message.success(data.msg);
                        hide();
                        props.refresh();
                    }).catch(errors => setSubmitting(false))
        }).catch(errorInfo => {
            // 验证失败时的操作
            setSubmitting(false);
            console.log('Validation failed:', errorInfo);
            if (errorInfo.errorFields.length > 0) {
              // 获取第一个出错字段的名字
              const firstErrorFieldName = errorInfo.errorFields[0].name.join('.');
              scrollToField(firstErrorFieldName);
            }
        })
    };

    const { runAsync: run_lostOtherDrug } = useFetch(updateOtherDrug, { manual: true })
    const confirmLost = () => {
        form.validateFields().then(values => {
            setSubmitting(true);
            run_lostOtherDrug(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    // instituteId: instituteId,
                    instituteType: 2,
                    otherMedicines: medicines,
                    reason: values.reason,
                    status: 6
                }).then(
                    (data) => {
                        message.success(data.msg);
                        hide();
                        props.refresh();
                    }).catch(errors => setSubmitting(false))
        }).catch(errorInfo => {
            // 验证失败时的操作
            setSubmitting(false);
            console.log('Validation failed:', errorInfo);
            if (errorInfo.errorFields.length > 0) {
              // 获取第一个出错字段的名字
              const firstErrorFieldName = errorInfo.errorFields[0].name.join('.');
              scrollToField(firstErrorFieldName);
            }
        })
    };


    const save = () => {
        if (operStatus === 4) {
            //判断是否填写值
            let otherValidate = true;
            medicines.forEach(it => {
                if (it.freezeCount === undefined || it.freezeCount <= 0) {
                    otherValidate = false
                }
            });
            if (!otherValidate) {
                message.error(formatMessage({ id: 'subject.dispensing.placeholder.input.count' })).then()
                setSubmitting(false)
                return
            }
            confirmFreeze()
        } else if (operStatus === 6) {
            //判断是否填写值
            let otherValidate = true;
            medicines.forEach(it => {
                if (it.lostCount === undefined || it.lostCount <= 0) {
                    otherValidate = false
                }
            });
            if (!otherValidate) {
                message.error(formatMessage({ id: 'subject.dispensing.placeholder.input.count' })).then()
                setSubmitting(false)
                return
            }
            confirmLost()
        }
    }

    const scrollToField = fieldKey => {
        const labelNode = document.querySelector(`label[for="${fieldKey}"]`);
        if (labelNode) {
          labelNode.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    };

    const otherHandleChange = (value, record) => {
        const _data = [...medicines];
        _data.forEach(it => {
            if (it.name === record.name && it.batchNumber === record.batchNumber && it.expirationDate === record.expirationDate) {
                if (operStatus === 4) {
                    it.freezeCount = value["freezeCount"]
                } else if (operStatus === 6) {
                    it.lostCount = value["lostCount"]
                }

            }
        });
        setMedicines(_data)
    }

    function renderPackageMethod(packageMethod) {
        return packageMethod === true ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }


    const { TextArea } = Input;

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <Modal
            className='custom-large-modal'
            title={title}
            visible={visible}
            onCancel={hide}
            maskClosable={false}
            centered
            destroyOnClose
            okButtonProps={{ loading: submitting }}
            okText={formatMessage({ id: 'common.ok' })}
            onOk={save}
        >
            <Row gutter={[16, 16]}>
                <Col span={24}>
                    <Title name={formatMessage({ id: 'drug.other' })} />
                    {havePackage &&
                        <div
                            id="showErrorId"
                            style={{
                                height: "32px",
                                marginBottom: "8px",
                                marginTop: "8px",
                                display: "flex",
                                alignItems: "center",
                            }}
                        >
                            <InfoCircleFilled
                                style={{
                                    paddingLeft: "8px",
                                    paddingRight: "8px",
                                    color: "#4072e2",
                                }}
                            />
                            <span>{formatMessage({ id: 'projects.other.package.info' })}</span>
                        </div>
                    }
                    <Table
                        className="mar-top-5"
                        size="small"
                        dataSource={medicines}
                        pagination={false}
                        rowKey={(record) => (record.id)}
                        scroll={{ x: "max-content" }}
                    >
                        <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={70}
                            render={(text, record, index) => (index + 1)} />
                        <Table.Column title={<FormattedMessage id="common.depot" />}
                            dataIndex="storehouseName" fixed="left"
                            key="storehouseName"
                            width={220} />
                        <Table.Column title={formatMessage({ id: 'drug.configure.drugName' })} key="name"
                            dataIndex="name"
                            align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.expireDate' })} key="expirationDate"
                            dataIndex="expirationDate" align="left" ellipsis />
                        <Table.Column title={formatMessage({ id: 'drug.list.batch' })} key="batchNumber"
                            dataIndex="batchNumber" align="left" ellipsis />
                        {havePackage && <Table.Column
                            title={
                                <FormattedMessage id="shipment.order.package.method" />
                            }
                            dataIndex="isOpenPackage"
                            key="isOpenPackage"
                            ellipsis
                            render={(value, record, index) =>
                                renderPackageMethod(value)
                            }
                        />}
                        {operStatus === 4 &&
                            <>
                                <Table.Column title={formatMessage({ id: 'medicine.status.available' })} key="count"
                                    dataIndex="count" align="left" ellipsis
                                    render={(value, record, index) =>
                                        record.isOpenPackage ? record.count + "(" + record.packageCount + ")" : record.count
                                    } />
                                <Table.Column
                                    title={havePackage && haveSingle ? <FormattedMessage id="projects.other.sku.freeze.count" /> : havePackage && !haveSingle ? <FormattedMessage id="projects.other.sku.freeze.package" /> : <FormattedMessage id="projects.other.sku.freeze.single" />}
                                    dataIndex={"freezeCount"}
                                    key="freezeCount"
                                    //ellipsis
                                    render={
                                        (value, record, index) => (
                                            <InputNumber style={{ width: '100%' }} placeholder={formatMessage({ id: 'placeholder.input.common' })} precision={0} min={0} step={1} max={record.isOpenPackage ? record.packageCount : record.count}
                                                onChange={(e) => otherHandleChange({ freezeCount: e }, record)} />
                                        )
                                    }
                                />
                            </>}
                        {operStatus === 6 &&
                            <>
                                <Table.Column title={formatMessage({ id: 'medicine.status.available' })} key="count"
                                    dataIndex="count" align="left" ellipsis
                                    render={(value, record, index) =>
                                        record.isOpenPackage ? record.count + "(" + record.packageCount + ")" : record.count
                                    }
                                />
                                <Table.Column title={formatMessage({ id: 'medicine.status.expired' })} key="expired_count"
                                    dataIndex="expired_count" align="left" ellipsis
                                    render={(value, record, index) =>
                                        record.isOpenPackage ? record.expired_count + "(" + record.expired_packageCount + ")" : record.expired_count
                                    }
                                />
                                <Table.Column
                                    title={havePackage && haveSingle ? <FormattedMessage id="projects.other.sku.lost.count" /> : havePackage && !haveSingle ? <FormattedMessage id="projects.other.sku.lost.package" /> : <FormattedMessage id="projects.other.sku.lost.single" />}
                                    dataIndex={"lostCount"}
                                    key="lostCount"
                                    // ellipsis
                                    render={
                                        (value, record, index) => (
                                            <InputNumber style={{ width: '100%' }} placeholder={formatMessage({ id: 'placeholder.input.common' })} precision={0} min={0} step={1}
                                                max={record.expired_count ? record.isOpenPackage ? record.expired_packageCount : record.expired_count : record.isOpenPackage ? record.packageCount : record.count}
                                                onChange={(e) => otherHandleChange({ lostCount: e }, record)} />
                                        )
                                    }
                                />
                            </>}
                    </Table>
                </Col>
                <Col span={24}>
                    <Form form={form} layout="horizontal">
                        <Form.Item label={formatMessage({ id: 'drug.freeze.reason' })} rules={[{ required: true }]} name="reason"
                            className="mar-ver-5">
                            {operStatus === 4 && attribute?.info?.freezeReasonConfig !== null && attribute?.info?.freezeReasonConfig.length > 0 ?
                                <Select onChange={(e) => {
                                    setReasonType(e)
                                }}>
                                    {attribute?.info?.freezeReasonConfig.map((v) => <Select.Option key={v.reason} value={v.reason}>{v.reason}</Select.Option>)}
                                </Select> :
                                <TextArea placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear showCount maxLength={500} />
                            }
                        </Form.Item>
                        {
                            attribute?.info?.freezeReasonConfig !== null && attribute?.info?.freezeReasonConfig.find((e) => e.reason === reasonType)?.allowRemark ?
                                <Form.Item label={formatMessage({ id: 'subject.unblinding.reason.remark' })} name="remark" rules={[{ required: true }]} >
                                    <Input.TextArea allowClear className="full-width" ></Input.TextArea>
                                </Form.Item> : null
                        }
                    </Form>
                </Col>
            </Row>
        </Modal>
    );
};

