import React from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { Spin, Button, Col, Input, InputNumber, message, Modal, Row, Table, Form, DatePicker } from "antd";
import { usePage } from "../../../../context/page";
import { Title } from "components/title";
import { useAuth } from "../../../../context/auth";
import { useSafeState, useUpdateEffect } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { getOrderMedicines, recoverConfirmOrder } from "../../../../api/order";
import { permissions } from "../../../../tools/permission";
import { SearchOutlined } from "@ant-design/icons";
import { combineRow } from "../../../../utils/merge_cell";
import { SelectableOrderTable } from "../shipment/order-table";
import { useRowSelection } from "../../../../hooks/row-selection";
import { useCacheTable } from "hooks/cache-rowSpan-table";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";
import moment from "moment";


export const ConfirmOrder = (props) => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const auth = useAuth()
    const page = usePage()
    const [visible, setVisible] = useSafeState(false);
    const [submitting, setSubmitting] = useSafeState(false);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [tableData, setTableData] = useSafeState([]);
    const data = useCacheTable({ dataSource: tableData });
    //const [receiveMedicines, setReceiveMedicines] = useSafeState([]);
    //const [receiveMedicineIds, setReceiveMedicineIds] = useSafeState([]);
    const [expectedArrivalTimeValue, setExpectedArrivalTimeValue] = useSafeState(null);
    const [orderId, setOrderId] = useSafeState(null);
    const [orderNumber, setOrderNumber] = useSafeState(null);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [searchValue, setSearchValue] = useSafeState(null);
    const [allSelected, setAllSelected] = useSafeState(true);
    const envId = auth.env ? auth.env.id : null;
    const [form] = Form.useForm();
    const DatePickers = DatePicker;
    const { runAsync: run_getOrderMedicines, loading } = useFetch(getOrderMedicines, { manual: true })
    const show = (record) => {
        setVisible(true);
        setOrderId(record._id);
        setOrderNumber(record.order_number);
        setAllSelected(true);
        getOrderMedicineList();
        if (record.expected_arrival_time !== undefined && record.expected_arrival_time !== "") {
            // 将字符串日期解析为Date对象
            //var parsedDate = moment(record.expected_arrival_time, 'YYYY-MM-DD HH:mm:ss');
            form.setFieldValue("expectedArrivalTime", record.expected_arrival_time)
            setExpectedArrivalTimeValue(record.expected_arrival_time)
        }
    };

    const getOrderMedicineList = () => {
        run_getOrderMedicines({
            id: orderId,
            roleId: auth.project.permissions.role_id,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
            searchValue: searchValue,
            envId: envId,
        }).then(
            (result) => {
                const data = result.data
                if (data.order != null) {
                    let order = data.order[0];
                    setTableData(order.medicines);
                    if (data.packageIsOpen) {
                        var tableData = combineRow(order.medicines, "package_number", "package_number", false)
                        tableData = combineRow(order.medicines, "name", "name", false)
                        setTableData(fillTableCellEmptyPlaceholder(tableData ? tableData : []));
                    } else {
                        setTableData(fillTableCellEmptyPlaceholder(order.medicines ? order.medicines : []));
                    }
                    setPackageIsOpen(data.packageIsOpen);
                    setAllSelected(false);
                    //setReceiveMedicines([]);
                    //setReceiveMedicineIds([]);
                    //resetSelectedIds();
                    if (order.other_medicines != null && order.other_medicines.length > 0) {
                        order.other_medicines.forEach((otherMedicine) => {
                            otherMedicine.receive_count = otherMedicine.use_count;
                        });
                        // setOtherTableData(order.other_medicines)
                        setOtherTableData(fillTableCellEmptyPlaceholder(order.other_medicines ? order.other_medicines : []))
                    }
                } else {
                    setTableData([]);
                }
            }
        )
    }

    const disabledDate = (current) => {
        return current && current < moment().startOf('day');
    };

    function renderStatus(value) {
        return medicineStatusData(
            auth.codeRule,
            auth.project.info.research_attribute
        ).find((it) => it.value === value)?.label;
    }

    const hide = () => {
        setVisible(false);
        setSubmitting(false);
        //setReceiveMedicines([]);
        //setReceiveMedicineIds([]);
        resetSelectedIds();
        form.resetFields();
        setOtherTableData(null);
        setSearchValue(null);
        setExpectedArrivalTimeValue(null);
        props.refresh();
    };

    const { runAsync: run_confirmOrder } = useFetch(recoverConfirmOrder, { manual: true })
    const save = () => {
        setSubmitting(true);
        var count = receiveMedicineIds.length
        if (otherTableData != null) {
            otherTableData.forEach(it => {
                count = count + it.receive_count
            });
        }
        if (count <= 0) {
            message.error(formatMessage({ id: 'shipment.confirm.select' })).then();
            setSubmitting(false);
            return false;
        }

        form.validateFields()
            .then((values) => {
                // let expectedArrivalTime = values.expectedArrivalTime
                //     ? moment(values.expectedArrivalTime).format("YYYY-MM-DD HH:mm:ss")
                //     : "";
                let expectedArrivalTime = (values.expectedArrivalTime !== null && values.expectedArrivalTime !== undefined && values.expectedArrivalTime !== "-") ? values.expectedArrivalTime : "";
                run_confirmOrder(
                    {
                        id: orderId,
                        roleId: auth.project.permissions.role_id,
                        medicines: receiveMedicineIds,
                        otherMedicines: otherTableData,
                        expectedArrivalTime: expectedArrivalTime
                    }).then(
                        (data) => {
                            message.success(data.msg)
                            props.refresh();
                            hide();
                        }, (data) => {
                            setSubmitting(false);
                        });
            })
            .catch((errors) => {
                setSubmitting(false);
            });
    };

    const refresh = () => {
        //setReceiveMedicines([]);
        resetSelectedIds();
    }

    function renderPackageMethod(packageMethod) {
        return packageMethod === true ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }


    const { selectedIds: receiveMedicineIds, resetSelectedIds, rowSelection: rowSelection } =
        useRowSelection({
            dataSource: data,
            allData: tableData,
            key: "_id",
            packageIsOpen: packageIsOpen,
            allSelected: allSelected,
            renderCell: (checked, record, index, originNode, package_numberRowSpan) => {
                return {
                    children: originNode,
                    props: {
                        rowSpan: data[index] !== undefined ? data[index]["package_numberRowSpan"] : 1
                    }
                }
            }
        })

    const otherHandleChange = (value, record) => {
        const _data = [...otherTableData];
        _data.forEach(it => {
            if (it.id === record.id) {
                it.receive_count = value["receive_count"]
            }
        });
        setOtherTableData(_data)
    }

    React.useImperativeHandle(props.bind, () => ({ show }));
    useUpdateEffect(getOrderMedicineList, [orderId, searchValue, page.currentPage, page.pageSize]);
    //const [vt, set_components] = useVT(() => ({ scroll: { y: document.documentElement.clientHeight - 300 } }), [])
    return (
        <React.Fragment>
            <Modal
                className="custom-blarge-modal"
                title={formatMessage({ id: "shipment.confirm.order" }) + " - " + orderNumber}
                centered={true}
                maskClosable={false}
                visible={visible}
                onCancel={hide}
                footer={
                    <Row justify="space-between">
                        <Col>
                        </Col>
                        {
                            permissions(auth.project.permissions, "operation.supply.recovery.determine") &&
                            <Col>
                                <Button onClick={save} type="primary" loading={submitting}>
                                    <FormattedMessage id="shipment.oper.confirm.order" />
                                </Button>
                            </Col>
                        }
                    </Row>
                }
            >
                <Spin spinning={loading}>
                    <Form form={form}  >
                        <Title
                            name={formatMessage({
                                id: "shipment.basic.information",
                            })}
                        ></Title>
                        <Row gutter={32}>
                            <Col span={12}>
                                <Form.Item
                                    label={formatMessage({
                                        id: "shipment.expectedArrivalTime",
                                    })}
                                    name="expectedArrivalTime"
                                    className="mar-ver-5"
                                >
                                    {/* <DatePickers
                                        placeholder={formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        className={"full-width"}
                                        format={"YYYY-MM-DD HH:mm:ss"}
                                        showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                                    ></DatePickers> */}
                                    <CustomDateTimePicker
                                        disabledDate={disabledDate}
                                        disabledTime={"disBefore"}
                                        value={expectedArrivalTimeValue}
                                        onChange={setExpectedArrivalTimeValue}
                                        ph={'placeholder.select.common'}>
                                    </CustomDateTimePicker>
                                </Form.Item>
                            </Col>
                        </Row>
                        {(tableData != null && tableData.length > 0 || searchValue) &&
                            <>
                                <div style={{ marginBottom: 12 }}>
                                    <Title name={formatMessage({ id: 'shipment.medicine' })}></Title>
                                </div>
                                <Row gutter={8} justify="space-between" style={{ marginBottom: 12 }}>
                                    <Col xs={12} sm={12} md={12} lg={4} style={{ paddingRight: 0 }}>
                                        <Input
                                            placeholder={formatMessage({ id: "common.required.prefix" })}
                                            onChange={e => {
                                                setSearchValue(e.target.value)
                                            }}
                                            value={searchValue}
                                            allowClear
                                            style={{ width: 220, marginRight: 12 }}
                                            suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                        />
                                    </Col>
                                </Row>
                                <SelectableOrderTable
                                    dataSource={tableData}
                                    selectedIds={receiveMedicineIds}
                                    rowSelection={rowSelection}
                                    clearDisplay={true}
                                    refresh={refresh}
                                    packageIsOpen={packageIsOpen}
                                    isHasTip={false}
                                    showGroup={true && (searchValue == null || searchValue.length <= 0)}
                                    searchValue={searchValue}
                                />
                            </>}
                        {otherTableData != null && otherTableData.length > 0 ?
                            <>
                                <Title name={formatMessage({ id: "drug.other" })}></Title>
                                <Table
                                    className="mar-top-10"
                                    size="small"
                                    dataSource={otherTableData}
                                    pagination={false}
                                    rowKey={(record) => (record.id)}
                                >
                                    <Table.Column
                                        title={<FormattedMessage id="drug.configure.drugName" />}
                                        dataIndex={"name"}
                                        key="name"
                                        render={(value, record, index) =>
                                            <div style={{
                                                display: '-webkit-box',
                                                WebkitLineClamp: 2,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'normal',
                                                wordBreak: 'break-word',
                                            }}>
                                                {value}
                                            </div>
                                        }
                                        ellipsis
                                    />
                                    <Table.Column
                                        title={<FormattedMessage id="drug.list.expireDate" />}
                                        dataIndex={"expire_date"}
                                        key="expire_date"
                                        ellipsis
                                        width={120}
                                    />
                                    <Table.Column
                                        title={<FormattedMessage id="drug.list.batch" />}
                                        dataIndex={"batch"}
                                        key="batch"
                                        ellipsis
                                        width={140}
                                    />
                                    {packageIsOpen && (
                                        <Table.Column
                                            title={intl.formatMessage({
                                                id: "shipment.order.package.method",
                                            })}
                                            dataIndex="package_method"
                                            key="package_method"
                                            ellipsis
                                            width={110}
                                            render={(value, record, index) =>
                                                renderPackageMethod(value)
                                            }
                                        />
                                    )}
                                    <Table.Column
                                        title={formatMessage({ id: "drug.list.status" })}
                                        dataIndex="status"
                                        key="status"
                                        render={(value, record, index) => renderStatus(value, 0)}
                                        ellipsis
                                        width={100}
                                    />
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.count" />}
                                        dataIndex={"use_count"}
                                        key="use_count"
                                        ellipsis
                                        width={90}
                                    />
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.confirm.count" />}
                                        dataIndex={"receive_count"}
                                        key="receive_count"
                                        ellipsis
                                        width={120}
                                        render={
                                            (value, record, index) => (
                                                <InputNumber precision={0} defaultValue={record.receive_count} min={1} step={1}
                                                    max={record.use_count}
                                                    onChange={(e) => otherHandleChange({ receive_count: e }, record)} />
                                            )
                                        }
                                    />
                                </Table>
                            </>
                            : null
                        }
                    </Form>
                </Spin>
            </Modal>
            {/*<ReasonOrder bind={reason_ref} refresh={list}/>*/}
        </React.Fragment>
    )
};

