import React from "react";
import { FormattedMessage, useTranslation } from "../../../../common/multilingual/component";
import { Button, Checkbox, Col, Form, InputNumber, message, Modal, Row, Select, Spin, Table, DatePicker } from "antd";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../../context/auth";
import { useFetch } from "../../../../../hooks/request";
import { addRecoveryOrder } from "../../../../../api/order";
import { getMedicineForOrderGroup, getMedicineForOrderGroupAll } from "../../../../../api/medicine";
import { getOtherMedicines } from "../../../../../api/drug_other";
import { Md5 } from "ts-md5";
import { CacheGroupTable } from "./groupTable"
import { Title } from "../../../../../components/title";
import { fillTableCellEmptyPlaceholder } from "../../../../../components/table";
import { CustomDateTimePicker } from "../../../../../components/CustomDateTimePicker";
import { medicineStatusData } from "../../../../../tools/medicine_status";
import moment from "moment";

export const Add = (props: any) => {
	const intl = useTranslation();
	const { formatMessage } = intl;

	const [visible, setVisible] = useSafeState(false);
	const [institutes, setInstitutes] = useSafeState([]);
	const [storehouses, setStorehouses] = useSafeState([]);
	const [tableData, setTableData] = useSafeState([]);
	const [otherSelecteds, setOtherSelecteds] = useSafeState([]);
	const [otherSelectedIds, setOtherSelectedIds] = useSafeState([]);
	const [otherDrugs, setOtherDrugs] = useSafeState<any>(null);
	const [selectedIdsSet, setSelectedIdsSet] = useSafeState<Set<string>>(new Set())
	const [allIdsSet, setAllIdsSet] = useSafeState<Set<string>>(new Set())
	const [selectedGroupCountMap, setSelectedGroupCountMap] = useSafeState<Map<string, number>>(new Map())
	const [selectedSend, setSelectedSend] = useSafeState("")
	const [packageIsOpen, setPackageIsOpen] = useSafeState<any>(false);
	const [haveDrug, setHaveDrug] = useSafeState<any>(true);
	const [haveOtherDrug, setHaveOtherDrug] = useSafeState<any>(true);
	const DatePickers: any = DatePicker;
	const [expectedArrivalTimeValue, setExpectedArrivalTimeValue] = useSafeState(null);
	const [form] = Form.useForm();
	const auth = useAuth()
	const projectId = auth.project ? auth.project.id : null;
	const envId = auth.env ? auth.env.id : null;
	const customerId = auth.customerId;

	const { runAsync: run_getMedicineForOrderGroupAll } = useFetch(getMedicineForOrderGroupAll, { manual: true })
	const { runAsync: run_addRecoveryOrder, loading: submitting } = useFetch(addRecoveryOrder, { manual: true })
	const { runAsync: run_getMedicineForOrderGroup, loading: groupLoading } = useFetch(getMedicineForOrderGroup, { manual: true })

	const show = (institutes: any, storehouses: any, isOpenPackage: any, haveDrug: any, haveOtherDrug: any) => {
		setVisible(true);
		setHaveDrug(haveDrug);
		setHaveOtherDrug(haveOtherDrug);
		//获取中心和仓库信息
		setInstitutes(institutes.filter((item: any) => item.deleted === 2))
		setStorehouses(storehouses.filter((item: any) => item.deleted === 2))
		setPackageIsOpen(isOpenPackage)
	};

	//获取研究产品号信息
	const getMedicineBatchList = (sendId: any) => {
		run_getMedicineForOrderGroup({
			customerId: customerId,
			envId: envId,
			sendId: sendId,
			roleId: auth.project.permissions.role_id
		}).then((result: any) => {
			let data = result.data;
			if (data != null && data.items && data.items.length) {
				data.items.forEach((item: any) => {
					item.id = Md5.hashStr(item.name + item.expirationDate + item.batchNumber + item.saltName)
				})
				setTableData(data.items);
			} else {
				setTableData([]);
			}
		}
		)
	};

	function renderStatus(value: any) {
		return medicineStatusData(
			auth.codeRule,
			auth.project.info.research_attribute
		).find((it: any) => it.value === value)?.label;
	}

	//获取其它研究产品名称
	const { runAsync: run_getOtherMedicines, loading: fetchingOtherMedicines } = useFetch(getOtherMedicines, { manual: true })
	const getOtherDrugs = (sendId: any) => {
		setOtherDrugs([]);
		run_getOtherMedicines(
			{
				customerId: customerId,
				projectId: projectId,
				envId: envId,
				sendId: sendId,
				roleId: auth.project.permissions.role_id,
				mode: 1,
				orderType: 4
			}).then(
				(result: any) => {
					const data = result.data
					if (data != null) {
						setOtherDrugs(data);
					}
				}
			)
	};

	const hide = () => {
		setVisible(false);
		form.resetFields();
		setTableData([]);
		setOtherDrugs(null);
		setSelectedIdsSet(new Set())
		setAllIdsSet(new Set())
		setOtherSelectedIds([])
		setOtherSelecteds([]);
	};

	const save = () => {
		form.validateFields().then(values => {
			if (values.sendId === values.receiveId) {
				message.error(formatMessage({ id: 'shipment.order.sendAndReceive.info' }))
				return
			}
			if (selectedIdsSet.size === 0 && otherSelectedIds.length === 0) {
				message.error(formatMessage({ id: 'shipment.order.create.info' }))
				return
			}
			//其它研究产品勾选上的判断是否填写值
			let otherValidate = true;
			otherSelecteds.forEach((value: any) => {
				if (value.useCount === undefined || value.useCount === null || value.useCount <= 0) {
					otherValidate = false;
				}
			});
			if (!otherValidate) {
				message.error(formatMessage({ id: 'shipment.order.create.count.info' })).then()
				return
			}
			// let expectedArrivalTime = values.expectedArrivalTime
			// 	? moment(values.expectedArrivalTime).format("YYYY-MM-DD HH:mm:ss")
			// 	: "";
			let expectedArrivalTime = (values.expectedArrivalTime !== null && values.expectedArrivalTime !== undefined && values.expectedArrivalTime !== "-") ? values.expectedArrivalTime : "";
			run_addRecoveryOrder({
				customerId: customerId,
				projectId: projectId,
				envId: envId,
				...values,
				medicineIds: Array.from(selectedIdsSet),
				otherMedicinesCount: otherSelecteds,
				expectedArrivalTime: expectedArrivalTime,
			}).then((data: any) => {
				message.success(data.msg);
				props.refresh();
				hide();
			})
		})
	}



	const disabledDate = (current: any) => {
		return current && current < moment().startOf('day');
	};

	//选择起运地
	const changeSend = (send: any) => {
		selectedGroupCountMap.clear()
		selectedIdsSet.clear()
		allIdsSet.clear()
		setSelectedSend(send)
		getMedicineBatchList(send);
		getOtherDrugs(send)
		//判断起运的和目的地是否一样
		if (send === form.getFieldValue("receiveId")) {
			message
				.error(
					formatMessage({ id: "shipment.order.sendAndReceive.info" })
				)
				.then();
		}
	}

	//change 目的地
	const changeReceive = (receive: any) => {
		//判断起运的和目的地是否一样
		if (receive === form.getFieldValue("sendId")) {
			message
				.error(
					formatMessage({ id: "shipment.order.sendAndReceive.info" })
				)
				.then();
		}
	};

	const otherRowSelection = {
		type: "checkbox" as "checkbox",
		onChange: (selectedRowKeys: any, selectedRows: any) => {
			setOtherSelecteds(selectedRows);
			setOtherSelectedIds(selectedRowKeys);
		},
		selectedRowKeys: otherSelectedIds,
		selectedRows: otherSelecteds,
	};

	const otherHandleChange = (value: any, record: any) => {
		const _data = [...otherDrugs];
		_data.forEach(it => {
			if (it.name === record.name && it.expireDate === record.expireDate && it.batch === record.batch && it.id === record.id) {
				it.useCount = value["useCount"]
			}
		});
		setOtherDrugs(_data)
	}

	const headerCheckbox = () => {
		const allCount = tableData.reduce((a, c: any) => a + c.count, 0);
		let actualCount = 0;
		Array.from(selectedGroupCountMap).forEach(([_, count]) => {
			actualCount += count;
		});

		return (<Checkbox
			checked={actualCount > 0 && actualCount === allCount}
			indeterminate={
				actualCount > 0 && actualCount < allCount
			}
			onChange={async (e) => {
				if (!e.target.checked) {
					selectedIdsSet.clear()
					allIdsSet.clear()
					selectedGroupCountMap.clear()
				} else {
					await Promise.all(tableData.map((item: any) => {
						if (item) {
							return getMedicineForOrderGroupAll({
								customerId: customerId,
								envId: envId,
								sendId: selectedSend,
								roleId: auth.project.permissions.role_id,
								name: item.name,
								expirationDate: item.expirationDate,
								batchNumber: item.batchNumber,
								salts: item.salts,
							}).then(res => {
								if (res.data.items) {
									res.data.allItems?.forEach((i: string) => {
										allIdsSet.add(i)
									})
									res.data.items.forEach((i: string) => {
										selectedIdsSet.add(i)
									})
									selectedGroupCountMap.set(item.id, res.data.items.length)
								}
							})
						}
					}))
				}
				setSelectedGroupCountMap(new Map(selectedGroupCountMap))
			}}
		/>)
	};

	const useRowGroupSelection = () => {
		const rowGroupSelection = {
			type: "checkbox" as "checkbox",
			selectedRowKeys: Array.from(selectedGroupCountMap).filter(([_, count]) => {
				return count > 0
			}).map(([key]) => {
				return key
			}),
			onSelect: async (record: any, selected: boolean) => {
				const medicineIds = await run_getMedicineForOrderGroupAll({
					customerId: customerId,
					envId: envId,
					sendId: selectedSend,
					roleId: auth.project.permissions.role_id,
					name: record.name,
					expirationDate: record.expirationDate,
					batchNumber: record.batchNumber,
					salts: record.salts,
				})
				if (selected) {
					// @ts-ignore
					medicineIds.data.allItems.forEach(item => {
						allIdsSet.add(item)
					})
					// @ts-ignore
					medicineIds.data.items.forEach(item => {
						selectedIdsSet.add(item)
					})
					// @ts-ignore
					selectedGroupCountMap.set(record.id, medicineIds.data.items.length)
				} else {
					// @ts-ignore
					medicineIds.data.allItems.forEach(item => {
						allIdsSet.delete(item)
						selectedIdsSet.delete(item)
					})
					// @ts-ignore
					medicineIds.data.items.forEach(item => {
						selectedIdsSet.delete(item)
						allIdsSet.delete(item)
					})
					selectedGroupCountMap.set(record.id, 0)
				}
				setSelectedGroupCountMap(new Map(selectedGroupCountMap))
			},
			getCheckboxProps: (record: any) => {
				const props = {
					indeterminate: selectedGroupCountMap.has(record.id) && selectedGroupCountMap.get(record.id) !== 0 && selectedGroupCountMap.get(record.id) !== record.count
				}
				return props
			},
			columnTitle: headerCheckbox,
		};
		return {
			rowGroupSelection: rowGroupSelection,
		};
	}

	function renderPackageMethod(packageMethod: string) {
		return packageMethod === "true" ? (
			<FormattedMessage id="shipment.order.packageMethod.package" />
		) : (
			<FormattedMessage id="shipment.order.packageMethod.single" />
		);
	}

	React.useImperativeHandle(props.bind, () => ({ show }));
	const { rowGroupSelection } = useRowGroupSelection();

	return (
		<React.Fragment>
			<Modal
				centered
				maskClosable={false}
				className="custom-blarge-modal"
				title={<FormattedMessage id={"common.add"} />}
				open={visible}
				onCancel={hide}
				footer={
					<Row justify="end">
						<Col>
							<Button onClick={hide} style={{ marginRight: 12 }}>
								<FormattedMessage id="common.cancel" />
							</Button>
						</Col>
						<Col>
							<Button onClick={save} type="primary" loading={submitting}>
								<FormattedMessage id="common.ok" />
							</Button>
						</Col>
					</Row>
				}
			>
				<Spin spinning={groupLoading}>
					<Form form={form} layout="horizontal" labelCol={{ span: 8 }}>
						<Title
							name={formatMessage({
								id: "shipment.basic.information",
							})}
						></Title>
						<Row gutter={8}>
							<Col span={12}>
								<Form.Item
									label={formatMessage({ id: 'shipment.send' })}
									name="sendId"
									rules={[{ required: true }]}
									className="mar-ver-5"
								>
									<Select allowClear options={institutes} onChange={(e) => changeSend(e)}
										placeholder={formatMessage({
											id: "placeholder.select.common",
										})}
									/>
								</Form.Item>
							</Col>
							<Col span={12}>
								<Form.Item
									label={formatMessage({ id: 'shipment.receive' })}
									name="receiveId"
									rules={[{ required: true }]}
									className="mar-ver-5"
								>
									<Select allowClear options={storehouses} onChange={(e) => changeReceive(e)}
										placeholder={formatMessage({ id: "placeholder.select.common", })}
									/>
								</Form.Item>
							</Col>
						</Row>
						<Form.Item
							label={formatMessage({
								id: "shipment.expectedArrivalTime",
							})}
							name="expectedArrivalTime"
							className="mar-ver-5"
							labelCol={{ span: 4 }}
							wrapperCol={{ span: 24 }}
						>
							{/* <DatePickers
								placeholder={formatMessage({
									id: "placeholder.select.common",
								})}
								className="full-width"
								format={"YYYY-MM-DD HH:mm:ss"}
								showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
							></DatePickers> */}
							<CustomDateTimePicker
								disabledDate={disabledDate}
								disabledTime={"disBefore"}
								value={expectedArrivalTimeValue}
								onChange={setExpectedArrivalTimeValue}
								ph={'placeholder.select.common'}>
							</CustomDateTimePicker>
						</Form.Item>
						{haveDrug && <div style={{ marginTop: 24 }}>
							<div style={{ marginBottom: 12 }}>
								<Title name={formatMessage({ id: 'shipment.medicine' })}></Title>
							</div>
							<CacheGroupTable
								selectedIdsSet={selectedIdsSet}
								allIdsSet={allIdsSet}
								setAllIdsSet={setAllIdsSet}
								setSelectedIdsSet={setSelectedIdsSet}
								selectedGroupCountMap={selectedGroupCountMap}
								setSelectedGroupCountMap={setSelectedGroupCountMap}
								dataSource={tableData}
								rowSelection={rowGroupSelection}
								medicineListParams={{
									customerId,
									envId,
									sendId: selectedSend,
								}}
								packageIsOpen={packageIsOpen}
							/>
						</div>}
						{haveOtherDrug && <div style={{ marginTop: 24 }}>
							<div style={{ marginBottom: 12 }}>
								<Title name={formatMessage({ id: 'shipment.other.drug' })}></Title>
							</div>
							<Table
								className="mar-top-5"
								dataSource={otherDrugs ? fillTableCellEmptyPlaceholder(otherDrugs) : []}
								pagination={false}
								rowKey={(record) => (record.name + record.expirationDate + record.batchNumber + record.status)}
								rowSelection={otherRowSelection}
							>
								<Table.Column
									title={<FormattedMessage id="drug.configure.drugName" />}
									dataIndex={"name"}
									key="name"
									ellipsis
									render={(value, record, index) =>
										<div style={{
											display: '-webkit-box',
											WebkitLineClamp: 2,
											WebkitBoxOrient: 'vertical',
											overflow: 'hidden',
											textOverflow: 'ellipsis',
											whiteSpace: 'normal',
											wordBreak: 'break-word',
										}}>
											{value}
										</div>
									}
								/>
								<Table.Column
									width={120}
									title={<FormattedMessage id="drug.list.expireDate" />}
									dataIndex={"expirationDate"}
									key="expirationDate"
									ellipsis
								/>
								<Table.Column
									width={140}
									title={<FormattedMessage id="drug.list.batch" />}
									dataIndex={"batchNumber"}
									key="batchNumber"
									ellipsis
								/>
								{packageIsOpen && (
									<Table.Column
										width={110}
										title={intl.formatMessage({
											id: "shipment.order.package.method",
										})}
										dataIndex="packageMethod"
										key="packageMethod"
										ellipsis
										render={(value, record, index) =>
											renderPackageMethod(value)
										}
									/>
								)}
								<Table.Column
									width={100}
									title={formatMessage({ id: "drug.list.status" })}
									dataIndex="status"
									key="status"
									render={(value, record, index) => renderStatus(value)}
									ellipsis
								/>
								<Table.Column
									width={90}
									title={<FormattedMessage id="shipment.order.availableCount" />}
									dataIndex={"availableCount"}
									key="availableCount"
									ellipsis
								/>
								<Table.Column
									width={120}
									title={<FormattedMessage id="drug.freeze.count" />}
									dataIndex={"useCount"}
									key="useCount"
									ellipsis
									render={
										(value, record: any, index) => (
											<InputNumber
												className="full-width"
												placeholder={intl.formatMessage({ id: "placeholder.input.common" })}
												precision={0}
												min={0}
												step={1}
												max={record.availableCount}
												onChange={(e) => otherHandleChange({ useCount: e }, record)} />
										)
									}
								/>
							</Table>
						</div>}
					</Form>
				</Spin>
			</Modal>
		</React.Fragment >
	)
};

