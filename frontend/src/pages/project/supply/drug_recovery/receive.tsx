import React, { ReactNode, useRef } from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { Button, Col, DatePicker, Form, Input, InputNumber, message, Modal, Row, Spin, Table, Select } from "antd";
import { useSafeState, useUpdateEffect } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { getOrderMedicines, receiveRecoveryOrder } from "../../../../api/order";
import { useForm } from "antd/es/form/Form";
import { useRowSelection } from "../../../../hooks/row-selection";
import { InfoTips } from "../../../../components/tips";
import { Title } from "../../../../components/title";
import { SelectableOrderTable, UnSelectableOrderTable, } from "../shipment/order-table";
import { combineRow } from "../../../../utils/merge_cell";
import { SearchOutlined } from "@ant-design/icons";
import { useCacheTable } from "hooks/cache-rowSpan-table";
import { PageContextProvider } from "../../../../context/page";
import moment from "moment";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import dayjs from "dayjs";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { getProjectAttribute } from "../../../../api/randomization";

export const Receive = (props: any) => {
    const intl = useTranslation();
    const { formatMessage } = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [otherTableData, setOtherTableData] = useSafeState<any>([]);
    const [tableData, setTableData] = useSafeState<any>([]);
    const data = useCacheTable({ dataSource: tableData });
    const [tableAllData, setTableAllData] = useSafeState([]);
    const [orderId, setOrderId] = useSafeState<any>(null);
    const [orderNumber, setOrderNumber] = useSafeState<any>(null);
    const auth = useAuth();
    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;

    const { runAsync: run_getOrderMedicines, loading } = useFetch(
        getOrderMedicines,
        { manual: true }
    );
    const [actualReceiptTime, setActualReceiptTime] = useSafeState("");
    const formContainerRef = useRef<any>(null);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [allSelected, setAllSelected] = useSafeState(true);
    const [searchValue, setSearchValue] = useSafeState(null);
    const [reasonType, setReasonType] = useSafeState(-1);
    const [attribute, setAttribute] = useSafeState<any>(null);
    const [form] = useForm();

    const show = (record: any, institutes: any) => {
        setVisible(true);
        setOrderId(record._id);
        setOrderNumber(record.order_number);
        setAllSelected(true);
        getOrderMedicineList();
        setTableAllData([]);
        setActualReceiptTime("");
        getAttribute(projectId, envId, "", customerId);
    };

    const getAttribute = (projectId: any, envId: any, cohortId: any, customerId: any) => {
        getProjectAttributeRun({ projectId, env: envId, cohort: cohortId, customer: customerId }).then(
            (result: any) => {
                setAttribute(result.data);
            }
        )
    }
    const { runAsync: getProjectAttributeRun } = useFetch(getProjectAttribute, { manual: true })

    const getOrderMedicineList = () => {
        run_getOrderMedicines({
            id: orderId,
            roleId: auth.project.permissions.role_id,
            searchValue: searchValue,
            envId: envId,
        }).then((result: any) => {
            const data = result.data;
            if (data.order != null) {
                let order = data.order[0];
                var tableData = order.medicines;
                if (data.packageIsOpen) {
                    tableData = combineRow(
                        order.medicines,
                        "package_number",
                        "package_number", false
                    );
                    tableData = combineRow(
                        order.medicines,
                        "name",
                        "name", false
                    );
                }
                setTableData(fillTableCellEmptyPlaceholder(tableData ? tableData : []));
                if (searchValue === null || searchValue === "") {
                    setTableAllData(tableData);
                }
                setPackageIsOpen(data.packageIsOpen);
                setAllSelected(false);
                //resetSelectedIds();
                if (
                    order.other_medicines != null &&
                    order.other_medicines.length > 0
                ) {
                    order.other_medicines.forEach((otherMedicine: any) => {
                        otherMedicine.receive_count = otherMedicine.use_count;
                    });
                    setOtherTableData(fillTableCellEmptyPlaceholder(order.other_medicines ? order.other_medicines : []));
                }
            } else {
                setTableData([]);
            }
        });
    };

    const changeActualReceiptTime = (value: any, dateString: any) => {
        setActualReceiptTime(dateString);
    };

    const hide = () => {
        setReasonType(-1);
        setActualReceiptTime("");
        setTableData([]);
        setOtherTableData([]);
        resetSelectedIds();
        setIsolationTableData([]);
        setOtherIsolationTableData([]);
        setSelectedTableData([]);
        setSteps("SELECT");
        setVisible(false);
        setSearchValue(null);
        form.resetFields();
        props.refresh();
    };

    const changeSearchValue = (value: any) => {
        setSearchValue(value);
    };



    const beforeSave = () => {
        if (tableData.length !== orderReceiveIds.length ||
            otherIsolationTableData.length > 0) {
            var actualReceiptTime = form.getFieldsValue().actualReceiptTime;
            let actualReceiptTimeFormat = actualReceiptTime ? actualReceiptTime : "";
            form.validateFields()
                .then((values) => {
                    run_receiveRecoveryOrder({
                        id: orderId,
                        medicines: orderReceiveIds,
                        otherMedicines: selectedOtherTableData,
                        reason: values.reason,
                        remark: values.remark,
                        actualReceiptTime: actualReceiptTimeFormat,
                    });
                })
                .catch(() => {
                    if (formContainerRef.current) {
                        const el = formContainerRef.current as HTMLDivElement;
                        el.scrollIntoView();
                    }
                });
        } else {
            save();
        }
    };

    const { runAsync: run_receiveRecoveryOrder, loading: submitting } =
        useFetch(receiveRecoveryOrder, {
            manual: true,
            onSuccess: (data: any) => {
                message.success(data.msg);
                props.refresh();
                hide();
            },
        }
        );

    const save = () => {
        var actualReceiptTime = form.getFieldsValue().actualReceiptTime;
        let actualReceiptTimeFormat = actualReceiptTime ? actualReceiptTime : "";
        run_receiveRecoveryOrder({
            id: orderId,
            medicines: orderReceiveIds,
            otherMedicines: selectedOtherTableData,
            reason: form.getFieldsValue().reason,
            remark: form.getFieldsValue().remark,
            actualReceiptTime: actualReceiptTimeFormat,
        });
    };

    React.useImperativeHandle(props.bind, () => ({ show }));
    useUpdateEffect(getOrderMedicineList, [orderId, searchValue]);

    const [steps, setSteps] = useSafeState<"SELECT" | "CONFIRM">("SELECT");
    // 确认接收的产品table数据
    const [selectedTableData, setSelectedTableData] = useSafeState<any[]>([]);
    // 未接收研究产品的table数据(将被隔离的产品)
    // 即在SELECT阶段中tableData数据未被勾选，进入到CONFIRM的数据都是将被隔离的数据
    const [isolationTableData, setIsolationTableData] = useSafeState<any[]>([]);
    const [otherIsolationTableData, setOtherIsolationTableData] = useSafeState<any[]>([]);
    const [selectedOtherTableData, setSelectedOtherTableData] = useSafeState<any[]>([]);

    const {
        selectedIds: orderReceiveIds,
        resetSelectedIds,
        rowSelection: orderRowSelection,
    } = useRowSelection({
        dataSource: data,
        allData: tableData,
        key: "_id",
        packageIsOpen: packageIsOpen,
        allSelected: allSelected,
        onChange: null,
        operation: null,
        mixPackage: null,
        renderCell: (
            checked: any,
            record: any,
            index: any,
            originNode: any,
            package_numberRowSpan: any
        ) => {
            return {
                children: originNode,
                props: {
                    rowSpan:
                        data[index] !== undefined
                            ? data[index]["package_numberRowSpan"]
                            : 1,
                },
            };
        },
    });

    const refresh = () => {
        resetSelectedIds();
        setIsolationTableData([]);
        setSelectedTableData([]);
        setSearchValue(null);
        form.resetFields();
    };

    const previous = () => {
        // 保证有序性
        let set = new Set(orderReceiveIds);
        if (packageIsOpen) {
            tableAllData.forEach((item: any) => {
                if (set.has(item._id)) {
                    if (
                        item.package_number !== "" &&
                        item.package_numberRowSpan === 0
                    ) {
                        orderReceiveIds.splice(
                            orderReceiveIds.indexOf(item._id),
                            1
                        );
                    }
                }
            });
        }
        setSteps("SELECT");
        setIsolationTableData([]);
        setSelectedTableData([]);
        setOtherIsolationTableData([]);
        setSelectedOtherTableData([]);
        setTableData(tableAllData);
        setSearchValue(null);
        form.resetFields();
        setActualReceiptTime("");
    };

    const next = () => {
        setSteps("CONFIRM");
        // 保证有序性
        let set = new Set(orderReceiveIds);
        //判断是否有按照包装接收的数据
        const receiveDataMap: Map<string, string> = new Map();
        const recvData: any[] = [];
        const isolData: any[] = [];
        const otherIsolData: any[] = [];
        const receiveOtherData: any[] = [];

        tableAllData.forEach((item: any) => {
            if (set.has(item._id)) {
                recvData.push(item);
                if (packageIsOpen) {
                    var drugName = item.name;
                    var packageNumber = item.package_number;
                    receiveDataMap.set(drugName, packageNumber);
                }
            } else {
                if (
                    packageIsOpen &&
                    receiveDataMap.has(item.name) &&
                    item.package_number === receiveDataMap.get(item.name) &&
                    item.package_numberRowSpan !== undefined &&
                    item.package_numberRowSpan === 0
                ) {
                    recvData.push(item);
                    orderReceiveIds.push(item._id);
                    set.add(item._id);
                } else {
                    isolData.push(item);
                }
            }
        });
        otherTableData.forEach((item: any) => {
            if (item.use_count - item.receive_count > 0) {
                otherIsolData.push(item);
            }
            if (item.receive_count > 0) {
                receiveOtherData.push(item)
            }
        });
        setSelectedTableData(recvData);
        setIsolationTableData(isolData);
        setOtherIsolationTableData(otherIsolData);
        setSelectedOtherTableData(receiveOtherData);
    };

    const otherHandleChange = (value: any, record: any) => {
        const _data = [...otherTableData];
        _data.forEach((it) => {
            if (it.name === record.name && it.expire_date === record.expire_date && it.batch === record.batch && it.status === record.status) {
                it.receive_count = value["receive_count"]
                    ? value["receive_count"]
                    : 0;
            }
        });
        setOtherTableData(_data);
    };

    const footerBtns = () => {
        if (steps === "CONFIRM") {
            return (
                <Row justify="end">
                    <Col>
                        <Button
                            style={{ marginRight: 12, color: "#4B4B4B" }}
                            disabled={submitting}
                            onClick={previous}
                        >
                            {formatMessage({ id: "common.previous" })}
                        </Button>
                    </Col>
                    <Col>
                        <Button
                            disabled={loading}
                            onClick={beforeSave}
                            type="primary"
                            loading={submitting}
                        >
                            <FormattedMessage id="common.ok" />
                        </Button>
                    </Col>
                </Row>
            );
        }
        if (steps === "SELECT") {
            return (
                <Button type="primary" disabled={loading} onClick={next}>
                    {formatMessage({ id: "common.next" })}
                </Button>
            );
        }
    };

    return (
        <React.Fragment>
            <Modal
                className="custom-blarge-modal"
                title={
                    formatMessage({ id: "shipment.order.received" }) +
                    " - " +
                    orderNumber
                }
                visible={visible}

                maskClosable={false}
                centered={true}
                onCancel={hide}
                footer={footerBtns()}
            >
                <Spin spinning={loading}>
                    {steps === "SELECT" ? (
                        <Form form={form}>
                            <SelectStep
                                OrderDataSource={tableData}
                                ReceiveIds={orderReceiveIds}
                                OrderDataRowSelection={orderRowSelection}
                                OtherDataSource={otherTableData}
                                onOtherDataReceiveCountChange={otherHandleChange}
                                refresh={refresh}
                                clearDisplay={true}
                                packageIsOpen={packageIsOpen}
                                setSearchValue={changeSearchValue}
                                searchValue={searchValue}
                                setActualReceiptTime={setActualReceiptTime}
                                actualReceiptTime={actualReceiptTime}
                            />
                        </Form>
                    ) : (
                        <Form form={form}>
                            <ConfirmStep
                                ConfirmReceiveOrderDataSource={selectedTableData}
                                OtherDataSource={selectedOtherTableData}
                                ConfirmNotReceiveOrderDataSource={isolationTableData}
                                OtherNotReceiveDataSource={otherIsolationTableData}
                                packageIsOpen={packageIsOpen}
                                actualReceiptTime={actualReceiptTime}
                                form={form}
                                NotReceiveReasonNode={
                                    <div ref={formContainerRef}>
                                        <Form.Item label={formatMessage({
                                            id: "drug.freeze.form-item.reason",
                                        })} rules={[{ required: true }]} name="reason"
                                            className="mar-ver-5">
                                            {attribute?.info?.freezeReasonConfig !== null && attribute?.info?.freezeReasonConfig.length > 0 ?
                                                <Select onChange={(e: any) => {
                                                    setReasonType(e)
                                                }}>
                                                    {attribute?.info?.freezeReasonConfig.map((v: any) => <Select.Option key={v.reason} value={v.reason}>{v.reason}</Select.Option>)}
                                                </Select> :
                                                <Input.TextArea
                                                    autoSize={{
                                                        minRows: 3,
                                                        maxRows: 10,
                                                    }}
                                                    maxLength={500}
                                                    showCount
                                                />
                                            }
                                        </Form.Item>
                                        {
                                            attribute?.info?.freezeReasonConfig !== null && attribute?.info?.freezeReasonConfig.find((e: any) => e.reason === reasonType)?.allowRemark ?
                                                <Form.Item className="mar-ver-15" label={formatMessage({ id: 'subject.unblinding.reason.remark' })} name="remark" rules={[{ required: true }]} >
                                                    <Input.TextArea
                                                        autoSize={{
                                                            minRows: 3,
                                                            maxRows: 10,
                                                        }}
                                                        maxLength={500}
                                                        showCount
                                                    />
                                                </Form.Item> : null
                                        }
                                    </div>
                                }
                            />
                        </Form>
                    )}
                </Spin>
            </Modal>
        </React.Fragment >
    );
};
interface SelectStepProp {
    // 订单数据源
    OrderDataSource: any[];
    OrderDataRowSelection: any;
    // 接受的订单Id
    ReceiveIds: any[];
    // 未编号的产品数据源
    OtherDataSource: any[];
    onOtherDataReceiveCountChange: (value: any, record: any) => void;
    // 清空按钮
    clearDisplay?: boolean;
    // 清空函数
    refresh?: any;
    //是否按照包装运送
    packageIsOpen?: boolean;
    setSearchValue?: any;
    searchValue?: any;

    actualReceiptTime?: any;
    setActualReceiptTime?: any;
}

// const range = (start: number, end: number) => {
//     const result = [];
//     for (let i = start; i < end; i++) {
//         result.push(i);
//     }
//     return result;
// };

// const disabledDateTime = (current: any) => {
//     // 获取今天的日期
//     const today = moment().startOf("day");
//     const todayHour = moment().hour();
//     const todayMinute = moment().minutes();
//     // 将当前日期转换为 moment 对象
//     const currentDate = moment(current).startOf("day");
//     const currentHour = moment(current).hour();
//     //const currentMinute = moment(current).minutes();
//     return {
//         disabledHours: () => {
//             if (currentDate.isSame(today)) {
//                 return range(0, 24).splice(todayHour + 1, 20);
//             }
//         },
//         disabledMinutes: () => {
//             if (currentDate.isSame(today) && currentHour === todayHour) {
//                 return range(todayMinute + 1, 60);
//             }
//         }
//     };
// };


const disabledDate = (current: any) => {
    // 获取今天的日期
    const today = moment().startOf("day");
    // 将当前日期转换为 moment 对象
    const currentDate = moment(current).startOf("day");

    return (
        currentDate.isAfter(today) || current > dayjs()
    );
};

const SelectStep = (props: SelectStepProp) => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const DatePickers = DatePicker;
    return (
        <div>
            <InfoTips
                style={{ marginBottom: 24 }}
                content={intl.formatMessage({
                    id: "shipment.order.received.info",
                })}
            />
            <Title
                name={formatMessage({
                    id: "shipment.basic.information",
                })}
            ></Title>
            <Row gutter={32}>
                < Col span={12}>
                    <Form.Item
                        label={formatMessage({
                            id: "shipment.actualReceiptTime",
                        })}
                        name="actualReceiptTime"
                        className="mar-ver-5"
                    >
                        {/* <DatePickers
                            placeholder={formatMessage({
                                id: "placeholder.select.common",
                            })}
                            disabledDate={disabledDate}
                            disabledTime={disabledDateTime}
                            className={"full-width"}
                            format={"YYYY-MM-DD HH:mm:ss"}
                            showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                            onChange={(e: any, dateString: any) => {
                                props.setActualReceiptTime(e, dateString)
                            }}
                        ></DatePickers> */}
                        <CustomDateTimePicker
                            value={props.actualReceiptTime}
                            onChange={props.setActualReceiptTime}
                            disabledDate={disabledDate}
                            disabledTime={"disFuture"}
                            ph={'placeholder.select.common'}>
                        </CustomDateTimePicker>
                    </Form.Item>
                </Col>

            </Row>
            {/* 接收产品订单 */}

            {
                (props.OrderDataSource && props.OrderDataSource.length > 0 || props.searchValue) && (
                    <div>
                        <Title name={formatMessage({ id: "drug.medicine" })} />
                        <Row
                            gutter={8}
                            justify="space-between"
                            style={{ marginBottom: 12, marginTop: 12 }}
                        >
                            <Col xs={12} sm={12} md={12} lg={4} style={{ paddingRight: 0 }}>
                                <Input
                                    placeholder={formatMessage({
                                        id: "common.required.prefix",
                                    })}
                                    onChange={(e) => {
                                        props.setSearchValue(e.target.value);
                                    }}
                                    value={props.searchValue}
                                    style={{ width: 220, marginRight: 12 }}
                                    suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                    allowClear
                                />
                            </Col>
                        </Row>
                        <SelectableOrderTable
                            dataSource={props.OrderDataSource}
                            selectedIds={props.ReceiveIds}
                            rowSelection={props.OrderDataRowSelection}
                            clearDisplay={true}
                            refresh={props.refresh}
                            packageIsOpen={props.packageIsOpen}
                            showGroup={true && (props.searchValue == null || props.searchValue.length <= 0)}
                            searchValue={props.searchValue}
                        />
                    </div>)
            }
            {/* 未编号产品订单 */}
            {
                props.OtherDataSource && props.OtherDataSource.length > 0 && (
                    <div style={{ marginTop: 24 }}>
                        <Title name={formatMessage({ id: "drug.other" })}></Title>
                        <Table
                            className="mar-top-10"
                            size="small"
                            dataSource={props.OtherDataSource}
                            pagination={false}
                            rowKey={(record) => record.name + record.expire_date + record.batch + record.status}
                        >
                            <Table.Column
                                title={
                                    <FormattedMessage id="drug.configure.drugName" />
                                }
                                dataIndex={"name"}
                                key="name"
                                ellipsis
                                render={(value, record, index) =>
                                    <div style={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'normal',
                                        wordBreak: 'break-word',
                                    }}>
                                        {value}
                                    </div>
                                }
                            />
                            <Table.Column
                                title={
                                    <FormattedMessage id="drug.list.expireDate" />
                                }
                                dataIndex={"expire_date"}
                                key="expire_date"
                                ellipsis
                                width={120}
                            />
                            <Table.Column
                                title={<FormattedMessage id="drug.list.batch" />}
                                dataIndex={"batch"}
                                key="batch"
                                ellipsis
                                width={140}
                            />
                            {props.packageIsOpen && (
                                <Table.Column
                                    title={intl.formatMessage({
                                        id: "shipment.order.package.method",
                                    })}
                                    dataIndex="package_method"
                                    key="package_method"
                                    width={110}
                                    ellipsis
                                    render={(value, record, index) =>
                                        renderPackageMethod(value)
                                    }
                                />
                            )}
                            <Table.Column
                                title={formatMessage({ id: "drug.list.status" })}
                                dataIndex="status"
                                key="status"
                                render={(value, record, index) => renderStatus(value)}
                                ellipsis
                                width={100}
                            />
                            <Table.Column
                                title={<FormattedMessage id="drug.freeze.count" />}
                                dataIndex={"use_count"}
                                key="use_count"
                                ellipsis
                                width={90}
                            />
                            <Table.Column
                                title={
                                    <FormattedMessage id="drug.freeze.receive.count" />
                                }
                                dataIndex={"receive_count"}
                                key="receive_count"
                                width={120}
                                ellipsis
                                render={(value, record: any, index) => {
                                    return (
                                        <InputNumber
                                            placeholder={intl.formatMessage({
                                                id: "placeholder.input.common",
                                            })}
                                            precision={0}
                                            defaultValue={record.receive_count}
                                            min={0}
                                            step={1}
                                            max={record.use_count}
                                            onChange={(e) =>
                                                props.onOtherDataReceiveCountChange(
                                                    { receive_count: e },
                                                    record
                                                )
                                            }
                                        />
                                    );
                                }}
                            />
                        </Table>
                    </div>
                )
            }
        </div >
    );
};

interface ConfirmStepProp {
    // 订单数据源
    ConfirmReceiveOrderDataSource: any[];
    ConfirmNotReceiveOrderDataSource: any[];
    NotReceiveReasonNode: ReactNode;
    // 未编号的产品数据源
    OtherDataSource: any[];
    OtherNotReceiveDataSource: any[];
    //是否按照包装运送
    packageIsOpen?: boolean;
    actualReceiptTime?: any;
    form?: any;
}


function renderPackageMethod(packageMethod: boolean) {
    return packageMethod === true ? (
        <FormattedMessage id="shipment.order.packageMethod.package" />
    ) : (
        <FormattedMessage id="shipment.order.packageMethod.single" />
    );
}

function renderStatus(value: any) {
    return medicineStatusData(0, 0).find((it) => it.value === value)?.label;
}

const ConfirmStep = (props: ConfirmStepProp) => {
    if (props.actualReceiptTime !== undefined && props.actualReceiptTime !== "") {
        // 将字符串日期解析为Date对象
        // var parsedDate = moment(props.actualReceiptTime);
        props.form.setFieldValue("actualReceiptTime", props.actualReceiptTime)
    }
    const DatePickers: any = DatePicker;
    const intl = useTranslation();
    const { formatMessage } = intl;
    return (
        <div>
            <div>
                <Title
                    name={formatMessage({
                        id: "shipment.basic.information",
                    })}
                ></Title>
                <Row gutter={32}>
                    <Col span={12}>
                        <Form.Item
                            label={formatMessage({
                                id: "shipment.actualReceiptTime",
                            })}
                            name="actualReceiptTime"
                            className="mar-ver-5"
                        >
                            {
                                props.actualReceiptTime !== undefined && props.actualReceiptTime !== "" ?
                                    props.actualReceiptTime
                                    :
                                    "-"
                            }
                        </Form.Item>
                    </Col>
                </Row>
                {((props.ConfirmReceiveOrderDataSource &&
                    props.ConfirmReceiveOrderDataSource.length > 0) ||
                    (props.OtherDataSource && props.OtherDataSource.length > 0))
                    &&
                    (
                        <div
                            style={{
                                fontWeight: 600,
                                fontSize: 18,
                                marginBottom: 24,
                                display: "flex",
                                alignItems: "center",
                            }}
                        >
                            <svg
                                className="iconfont"
                                width={16}
                                height={16}
                                style={{ marginRight: 8 }}
                            >
                                <use xlinkHref="#icon-jijiangjieshouyanjiuchanpin" />
                            </svg>
                            {formatMessage({ id: "shipment.receive.confirm.order" })}
                        </div>
                    )
                }

                {props.ConfirmReceiveOrderDataSource &&
                    props.ConfirmReceiveOrderDataSource.length > 0 &&
                    <div>
                        <Title name={formatMessage({ id: "drug.medicine" })} />
                        <PageContextProvider>
                            <UnSelectableOrderTable
                                dataSource={props.ConfirmReceiveOrderDataSource}
                                packageIsOpen={props.packageIsOpen}
                            />
                        </PageContextProvider>
                    </div>}
                {props.OtherDataSource && props.OtherDataSource.length > 0 && (
                    <div style={{ marginTop: 24 }}>
                        <Title
                            name={formatMessage({ id: "drug.other" })}
                        ></Title>
                        <Table
                            className="mar-top-10"
                            size="small"
                            dataSource={props.OtherDataSource}
                            pagination={false}
                            rowKey={(record) => record.name + record.expire_date + record.batch + record.status}
                        >
                            <Table.Column
                                title={
                                    <FormattedMessage id="drug.configure.drugName" />
                                }
                                dataIndex={"name"}
                                key="name"
                                ellipsis
                                render={(value, record, index) =>
                                    <div style={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'normal',
                                        wordBreak: 'break-word',
                                    }}>
                                        {value}
                                    </div>
                                }
                            />
                            <Table.Column
                                title={
                                    <FormattedMessage id="drug.list.expireDate" />
                                }
                                dataIndex={"expire_date"}
                                key="expire_date"
                                ellipsis
                                width={120}
                            />
                            <Table.Column
                                title={
                                    <FormattedMessage id="drug.list.batch" />
                                }
                                dataIndex={"batch"}
                                key="batch"
                                ellipsis
                                width={140}
                            />
                            {props.packageIsOpen && (
                                <Table.Column
                                    title={intl.formatMessage({
                                        id: "shipment.order.package.method",
                                    })}
                                    dataIndex="package_method"
                                    key="package_method"
                                    width={110}
                                    ellipsis
                                    render={(value, record, index) =>
                                        renderPackageMethod(value)
                                    }
                                />
                            )}
                            <Table.Column
                                title={
                                    <FormattedMessage id="drug.freeze.receive.count" />
                                }
                                dataIndex={"receive_count"}
                                key="receive_count"
                                ellipsis
                                width={90}
                                render={(value, record: any, index) =>
                                    record.receive_count
                                }
                            />
                        </Table>
                    </div>
                )}
            </div>
            {((props.ConfirmNotReceiveOrderDataSource &&
                props.ConfirmNotReceiveOrderDataSource.length !== 0) ||
                (props.OtherNotReceiveDataSource &&
                    props.OtherNotReceiveDataSource.length > 0)) && (
                    <div>
                        <div
                            style={{
                                display: "flex",
                                alignItems: "center",
                                fontWeight: 600,
                                fontSize: 18,
                                marginBottom: 24,
                                // marginTop: 24,
                            }}
                        >
                            <svg
                                className="iconfont"
                                width={16}
                                height={16}
                                style={{ marginRight: 8 }}
                            >
                                <use xlinkHref="#icon-jijianggeliyanjiuchanpin" />
                            </svg>
                            {formatMessage({
                                id: "shipment.isolation.confirm.order",
                            })}
                        </div>

                        {props.ConfirmNotReceiveOrderDataSource &&
                            props.ConfirmNotReceiveOrderDataSource.length > 0 && (
                                <div style={{ marginBottom: 24 }}>
                                    <Title
                                        name={formatMessage({
                                            id: "drug.medicine",
                                        })}
                                    />
                                    <PageContextProvider>
                                        <UnSelectableOrderTable
                                            packageIsOpen={props.packageIsOpen}
                                            dataSource={
                                                props.ConfirmNotReceiveOrderDataSource
                                            }
                                        />
                                    </PageContextProvider>
                                </div>
                            )}
                        {props.OtherNotReceiveDataSource &&
                            props.OtherNotReceiveDataSource.length > 0 && (
                                <div style={{ marginBottom: 24 }}>
                                    <Title
                                        name={formatMessage({ id: "drug.other" })}
                                    ></Title>
                                    <Table
                                        className="mar-top-10"
                                        size="small"
                                        dataSource={props.OtherNotReceiveDataSource}
                                        pagination={false}
                                        rowKey={(record) => record.name + record.expire_date + record.batch + record.status}
                                    >
                                        <Table.Column
                                            title={
                                                <FormattedMessage id="drug.configure.drugName" />
                                            }
                                            dataIndex={"name"}
                                            key="name"
                                            ellipsis
                                            render={(value, record, index) =>
                                                <div style={{
                                                    display: '-webkit-box',
                                                    WebkitLineClamp: 2,
                                                    WebkitBoxOrient: 'vertical',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'normal',
                                                    wordBreak: 'break-word',
                                                }}>
                                                    {value}
                                                </div>
                                            }
                                        />
                                        <Table.Column
                                            title={
                                                <FormattedMessage id="drug.list.expireDate" />
                                            }
                                            dataIndex={"expire_date"}
                                            key="expire_date"
                                            ellipsis
                                            width={120}
                                        />
                                        <Table.Column
                                            title={
                                                <FormattedMessage id="drug.list.batch" />
                                            }
                                            dataIndex={"batch"}
                                            key="batch"
                                            ellipsis
                                            width={140}
                                        />
                                        {props.packageIsOpen && (
                                            <Table.Column
                                                title={intl.formatMessage({
                                                    id: "shipment.order.package.method",
                                                })}
                                                dataIndex="package_method"
                                                key="package_method"
                                                ellipsis
                                                render={(value, record, index) =>
                                                    renderPackageMethod(value)
                                                }
                                                width={110}
                                            />
                                        )}
                                        <Table.Column
                                            title={formatMessage({ id: "drug.list.status" })}
                                            dataIndex="status"
                                            key="status"
                                            render={(value, record, index) => renderStatus(value)}
                                            ellipsis
                                            width={100}
                                        />
                                        <Table.Column
                                            title={
                                                <FormattedMessage id="drug.freeze.receiveFreeze.count" />
                                            }
                                            dataIndex={"receive_count"}
                                            key="receive_count"
                                            ellipsis
                                            width={90}
                                            render={(value, record: any, index) =>
                                                record.use_count -
                                                record.receive_count
                                            }
                                        />
                                    </Table>
                                </div>
                            )}
                        {props.NotReceiveReasonNode}
                    </div>
                )}
        </div>
    );
};
