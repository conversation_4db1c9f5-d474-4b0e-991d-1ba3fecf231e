import React from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { Badge, Button, Col, Input, message, Modal, Popover, Row, Space, Spin, Table } from "antd";
import { useSafeState, useUpdateEffect } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { getOrderMedicines } from "../../../../api/order";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { medicineStatusColors } from "../../../../data/data"
import { Title } from "components/title";
import { combineRow } from "../../../../utils/merge_cell";
import Barcode from "react-barcode";
import { getBarcodeRule } from "../../../../api/barcode";
import { SearchOutlined } from "@ant-design/icons";
import { ChangeMedicines } from "../shipment/change_medicines";
import { permissions } from "../../../../tools/permission";
import { ChangeRecords } from "../shipment/change_records";
import _ from "lodash";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";

export const Medicines = (props) => {
    const intl = useTranslation();
    const { formatMessage } = intl;

    const [visible, setVisible] = useSafeState(false);
    const [tableData, setTableData] = useSafeState([]);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [orderNumber, setOrderNumber] = useSafeState(null);
    const [medicineData, setMedicineData] = useSafeState([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [codeRule, setCodeRule] = useSafeState(null);
    const [orderStatus, setOrderStatus] = useSafeState(null);
    const [orderType, setOrderType] = useSafeState(null);
    const [orderId, setOrderId] = useSafeState(null);
    var [updateMedicines, setUpdateMedicines] = useSafeState([]);
    var [updateMedicineIds, setUpdateMedicinesIds] = useSafeState([]);
    const [groupCount, setGroupCount] = useSafeState([]);
    const [selectedGroupCount, setSelectedGroupCount] = useSafeState([]);
    const [updateOtherMedicines, setUpdateOtherMedicines] = useSafeState([]);
    const [searchValue, setSearchValue] = useSafeState(null);
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const change_medicines_ref = React.useRef();
    const change_records_ref = React.useRef();
    const { runAsync: run_getOrderMedicines, loading } = useFetch(getOrderMedicines, { manual: true })
    const { runAsync: getBarcodeRun } = useFetch(getBarcodeRule, { manual: true })
    // const codeRule = auth.codeRule;


    const barcodeCodeRule = () => {
        getBarcodeRun({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
        }).then((response) => {
            if (response) {
                let data = response.data;
                setCodeRule(data.codeRule1);
            }
        });
    };

    const show = (record) => {
        setVisible(true);
        setOrderId(record._id)
        setOrderNumber(record.order_number);
        setOrderType(record.type);
        setOrderStatus(record.status);
        barcodeCodeRule();
        getOrderMedicinesList();
    };

    const getOrderMedicinesList = () => {
        if (orderId != null) {
            run_getOrderMedicines(
                {
                    id: orderId,
                    roleId: auth.project.permissions.role_id,
                    envId: envId,
                    searchValue: searchValue,
                }
            ).then(
                (result) => {
                    const data = result.data
                    if (data.order != null) {
                        const order = data.order[0]
                        if (data.packageIsOpen) {
                            var tableData = combineRow(order.medicines, "package_number", "package_number", false)
                            tableData = combineRow(order.medicines, "name", "name", false)
                            setTableData(fillTableCellEmptyPlaceholder(tableData ? tableData : []))
                            //setTableData(tableData);
                        } else {
                            setTableData(fillTableCellEmptyPlaceholder(order.medicines ? order.medicines : []))
                            //setTableData(order.medicines);
                        }
                        if (searchValue == null || searchValue.length <= 0) {
                            setMedicineData(order.medicines)
                            var groupInfo = new Map();
                            var groupName = _.groupBy(order.medicines, "name")
                            _.forEach(groupName, function (value, drugName) {
                                var groupBatch = _.groupBy(value, "batch_number")
                                _.forEach(groupBatch, function (batchVal, batch) {
                                    groupInfo.set(drugName + "/" + batch, batchVal.length)
                                });
                            });
                            setGroupCount(Array.from(groupInfo));
                        }
                        setPackageIsOpen(data.packageIsOpen);
                        if (order.other_medicines != null && order.other_medicines.length > 0) {
                            setOtherTableData(fillTableCellEmptyPlaceholder(order.other_medicines))
                        }
                    }
                }
            )
        }

    }

    const getChangeRecords = () => {
        change_records_ref.current.show(orderId, orderNumber, packageIsOpen, codeRule);
    }

    //更换
    const changeMedicines = () => {
        if (updateMedicines.length <= 0 && updateOtherMedicines.length <= 0) {
            message.warning(formatMessage({ id: 'menu.projects.project.build.randomization.tooltip' })).then();
            return false;
        } else {
            if (packageIsOpen) {
                var rows = [];
                var keys = [];
                updateMedicines.forEach((item) => {
                    if (item.package_numberRowSpan !== 0) {
                        rows.push(item)
                        keys.push(item._id)
                    }
                });
                updateMedicines = rows
                updateMedicineIds = keys
            }
            //处理数据
            // 保证有序性
            let set = new Set(updateMedicineIds);
            //判断是否有按照包装接收的数据
            const receiveDataMap = new Map();
            const selectedData = [];

            if (orderType !== 5 && orderType !== 6) {
                medicineData.forEach((item) => {
                    if (set.has(item._id)) {
                        selectedData.push(item);
                        var packageNumber = item.package_number;
                        if (packageIsOpen && packageNumber !== undefined && packageNumber !== null && packageNumber !== "") {
                            receiveDataMap.set(packageNumber, packageNumber);
                        }
                    }
                });
                medicineData.forEach((item) => {
                    if (
                        packageIsOpen && item.package_number !== undefined && item.package_number !== null && item.package_number !== "" &&
                        item.package_number === receiveDataMap.get(item.package_number) &&
                        !set.has(item._id)
                    ) {
                        selectedData.push(item);
                        updateMedicineIds.push(item._id);
                        set.add(item._id);
                    }
                });
            } else {
                tableData.forEach((item) => {
                    selectedData.push(item);
                });
            }

            setUpdateMedicines(selectedData)
            change_medicines_ref.current.show(orderId, orderNumber, packageIsOpen, codeRule, selectedData, updateOtherMedicines);
        }
    };

    const hide = () => {
        setVisible(false);
        setTableData([]);
        setOtherTableData([]);
        setUpdateMedicinesIds([]);
        setUpdateMedicines([]);
        setUpdateOtherMedicines([]);
        setSearchValue(null);
        setOrderId(null);
        setOrderNumber("");
        setGroupCount([]);
        setSelectedGroupCount([]);
    }

    const renderStatus = (value, shortCode) => {
        let code_rule = 1           // 自动编码
        if (shortCode === "") {
            code_rule = 0           // 手动上传
        }
        return <Badge color={medicineStatusColors[value]}
            text={medicineStatusData(code_rule, auth.project.info.research_attribute).find(it => (it.value === value)).label} />;
    }


    const rowSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setUpdateMedicines(selectedRows);
            setUpdateMedicinesIds(selectedRowKeys);
            var groupInfo = new Map();
            var groupName = _.groupBy(selectedRows, "name")
            _.forEach(groupName, function (value, drugName) {
                var groupBatch = _.groupBy(value, "batch_number")
                _.forEach(groupBatch, function (batchVal, batch) {
                    groupInfo.set(drugName + "/" + batch, batchVal.length)
                });
            });
            setSelectedGroupCount(Array.from(groupInfo));
        },
        selectedRows: updateMedicines,
        selectedRowKeys: updateMedicineIds,
        renderCell: (checked, record, index, originNode, package_numberRowSpan) => {
            return {
                children: originNode,
                props: {
                    rowSpan: tableData[index] !== undefined ? tableData[index]["package_numberRowSpan"] : 1
                }
            }
        }
    };

    const rowOtherSelection = {
        type: "checkbox",
        onChange: (selectedRowKeys, selectedRows) => {
            setUpdateOtherMedicines(selectedRows);
        },
        selectedRows: updateOtherMedicines,
    }

    function renderPackageMethod(packageMethod) {
        return packageMethod === true ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    useUpdateEffect(getOrderMedicinesList, [orderId, searchValue]);

    return (
        <React.Fragment>
            <Modal
                className="custom-blarge-modal"
                //className="drawer-width-percent"
                title={formatMessage({ id: "shipment.order.medicines" }) + " - " + orderNumber}
                open={visible}
                onCancel={hide}

                maskClosable={false}
                bodyStyle={{ padding: 24 }}
                footer={<Row justify={"end"}>
                    <Space>
                        <Button
                            onClick={() => {
                                hide();
                            }}
                        >
                            {formatMessage({ id: "common.cancel" })}
                        </Button>
                        {!(orderType === 5 || orderType === 6) && permissions(
                            auth.project.permissions,
                            "operation.supply.recovery.detail.changeRecord"
                        ) && <Button
                            onClick={(e) => {
                                getChangeRecords()
                            }}
                        >
                                <FormattedMessage id="shipment.order.medicines.change.records" />
                            </Button>
                        }
                        {!(orderType === 5 || orderType === 6) && (orderStatus === 1 || orderStatus === 6) && permissions(
                            auth.project.permissions,
                            "operation.supply.recovery.detail.change"
                        ) && <Button type="primary"
                            onClick={(e) => {
                                changeMedicines()
                            }}
                        >
                                <FormattedMessage id="shipment.order.medicines.change" />
                            </Button>}
                    </Space>
                </Row>}
            >
                <Spin spinning={loading}>
                    {tableData != null && tableData.length > 0 &&
                        <>
                            <div style={{ marginBottom: 12 }}>
                                <Title name={formatMessage({ id: 'shipment.medicine' })}></Title>
                                {
                                    !(orderType === 5 || orderType === 6) &&
                                    <Row gutter={8} justify="space-between" style={{ marginBottom: 12, marginTop: 12 }}>
                                        <Col xs={12} sm={12} md={12} lg={4} style={{ paddingRight: 0 }}>
                                            <Input
                                                placeholder={formatMessage({ id: "common.required.prefix" })}
                                                onChange={e => {
                                                    setSearchValue(e.target.value)
                                                }} marginTop
                                                value={searchValue}
                                                style={{ width: 220, marginRight: 12 }}
                                                suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                                allowClear
                                            />
                                        </Col>
                                    </Row>
                                }
                            </div>
                            {groupCount.length > 0 && <div style={{ marginTop: 12, marginBottom: 12 }}>
                                <div className="rectangle" style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }} >
                                    <span style={{ marginLeft: 8, marginTop: 8, marginBottom: 8, marginRight: 8 }}>
                                        {formatMessage({ id: 'common.statistics' })}：
                                        {
                                            groupCount.map(
                                                (it) => (
                                                    <>
                                                        <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }}>{it[0]}：</span>
                                                        <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#1D2129" }}>{it[1]}；</span>
                                                    </>
                                                )
                                            )
                                        }
                                    </span>
                                </div>
                            </div>}
                            <Table
                                size="large"
                                dataSource={tableData}
                                pagination={false}
                                scroll={{ x: 450 }}
                                rowKey={(record) => (record._id)}
                                rowSelection={!(orderType === 5 || orderType === 6) && (orderStatus === 1 || orderStatus === 6) && permissions(
                                    auth.project.permissions,
                                    "operation.supply.recovery.detail.change"
                                ) ? rowSelection : null}
                            >
                                {packageIsOpen ?
                                    <Table.Column
                                        title={formatMessage({ id: 'drug.configure.drugName' })}
                                        dataIndex={"name"}
                                        key="name"
                                        ellipsis
                                        render={(value, record, index) => {
                                            let newValue = ""
                                            if (value !== undefined && value !== "") {
                                                newValue = <div style={{
                                                    display: '-webkit-box',
                                                    WebkitLineClamp: 2,
                                                    WebkitBoxOrient: 'vertical',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'normal',
                                                    wordBreak: 'break-word',
                                                }}>
                                                    {value}
                                                </div>
                                            } else {
                                                newValue = "-"
                                            }
                                            let obj = {
                                                children: newValue,
                                                props: { rowSpan: tableData[index].nameRowSpan }
                                            }
                                            return obj
                                        }} />
                                    :
                                    <Table.Column
                                        title={formatMessage({ id: 'drug.configure.drugName' })}
                                        dataIndex={"name"}
                                        key="name"
                                        ellipsis
                                        render={(value, record, index) => {
                                            let newValue = ""
                                            if (value !== undefined && value !== "") {
                                                newValue = <div style={{
                                                    display: '-webkit-box',
                                                    WebkitLineClamp: 2,
                                                    WebkitBoxOrient: 'vertical',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'normal',
                                                    wordBreak: 'break-word',
                                                }}>
                                                    {value}
                                                </div>
                                            } else {
                                                newValue = "-"
                                            }
                                            return newValue
                                        }}
                                    />
                                }
                                {packageIsOpen && <Table.Column
                                    title={formatMessage({ id: 'drug.medicine.packlist' })}
                                    dataIndex={"package_number"}
                                    key="package_number"
                                    ellipsis
                                    width={180}
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        let obj = {
                                            children: newValue,
                                            props: { rowSpan: tableData[index].package_numberRowSpan }
                                        }
                                        return obj
                                    }}
                                />}
                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.drugNumber' })}
                                    dataIndex={"number"}
                                    key="number"
                                    ellipsis
                                    width={140}
                                />
                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.expireDate' })}
                                    dataIndex={"expiration_date"}
                                    key="expiration_date"
                                    ellipsis
                                    width={120}
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        return newValue
                                    }}
                                />
                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.batch' })}
                                    dataIndex={"batch_number"}
                                    key="batch_number"
                                    width={140}
                                    ellipsis
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        return newValue
                                    }}
                                />
                                {codeRule &&
                                    <Table.Column title={<FormattedMessage id="barcode" />} key="number"
                                        dataIndex="number" align="center" ellipsis
                                        render={(value, record, index) => {
                                            return (
                                                <Popover content={
                                                    <>
                                                        <Barcode value={value} displayValue={false} height={60} width={1}
                                                            format="CODE128" />
                                                        <br />
                                                        <span>&nbsp;&nbsp;{value}</span>
                                                    </>
                                                } trigger="click">
                                                    {
                                                        record.short_code !== "" && record.short_code !== "-" ?
                                                            <Button>
                                                                <FormattedMessage id="form.preview" />
                                                            </Button>
                                                            :
                                                            "-"
                                                    }
                                                </Popover>
                                            )
                                        }} />
                                }
                                {codeRule &&
                                    <Table.Column title={<FormattedMessage id="packageBarcode" />} key="package_number"
                                        dataIndex="package_number" align="center" ellipsis
                                        render={(value, record, index) => {
                                            return (
                                                <Popover content={
                                                    <>
                                                        <Barcode value={value} displayValue={false} height={60} width={1}
                                                            format="CODE128" />
                                                        <br />
                                                        <span>&nbsp;&nbsp;{value}</span>
                                                    </>
                                                } trigger="click">
                                                    {
                                                        record.short_code !== "" && record.short_code !== "-" && record.package_number !== "" && record.package_number !== "-" ?
                                                            <Button>
                                                                <FormattedMessage id="form.preview" />
                                                            </Button>
                                                            :
                                                            "-"
                                                    }
                                                </Popover>
                                            )
                                        }} />
                                }
                                <Table.Column
                                    title={formatMessage({ id: 'shipment.status' })}
                                    dataIndex={"status"}
                                    key="status"
                                    width={100}
                                    ellipsis
                                    render={(value, record, index) => (renderStatus(value, record.shortCode))}
                                />
                            </Table>
                            {selectedGroupCount.length > 0 &&
                                <div style={{ marginTop: 12, marginBottom: 12 }}>
                                    <div className="rectangle" style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }} >
                                        <span style={{ marginLeft: 8, marginTop: 8, marginBottom: 8, marginRight: 8 }}>
                                            {formatMessage({ id: 'common.statistics' })}：{
                                                selectedGroupCount.map(
                                                    (it) => (
                                                        <>
                                                            <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#677283" }}>{it[0]}：</span>
                                                            <span style={{ fontSize: 12, fontFamily: "PingFang SC", fontWeight: 400, color: "#1D2129" }}>{it[1]}；</span>
                                                        </>
                                                    )
                                                )
                                            }
                                        </span>
                                    </div>
                                </div>
                            }
                        </>}
                    {otherTableData != null && otherTableData.length > 0 &&
                        <><div style={{ marginTop: 12, marginBottom: 12 }}>
                            <Title name={formatMessage({ id: 'shipment.other.drug' })}></Title>
                        </div>
                            <Table
                                className="mar-top-10"
                                size="large"
                                dataSource={fillTableCellEmptyPlaceholder(otherTableData)}
                                pagination={false}
                                scroll={{ x: 450 }}
                                rowKey={(record) => (record.name + record.expire_date + record.batch)}
                                rowSelection={!(orderType === 5 || orderType === 6) && (orderStatus === 1 || orderStatus === 6) && permissions(
                                    auth.project.permissions,
                                    "operation.supply.recovery.detail.change"
                                ) ? rowOtherSelection : null}
                            >
                                <Table.Column
                                    title={<FormattedMessage id="drug.configure.drugName" />}
                                    dataIndex={"name"}
                                    key="name"
                                    ellipsis
                                    render={(value, record, index) =>
                                        <div style={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'normal',
                                            wordBreak: 'break-word',
                                        }}>
                                            {value}
                                        </div>
                                    }
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.list.expireDate" />}
                                    dataIndex={"expire_date"}
                                    key="expire_date"
                                    ellipsis
                                    width={120}
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.list.batch" />}
                                    dataIndex={"batch"}
                                    key="batch"
                                    width={140}
                                    ellipsis
                                />
                                {packageIsOpen && (
                                    <Table.Column
                                        title={intl.formatMessage({
                                            id: "shipment.order.package.method",
                                        })}
                                        dataIndex="package_method"
                                        key="package_method"
                                        ellipsis
                                        width={110}
                                        render={(value, record, index) =>
                                            renderPackageMethod(value)
                                        }
                                    />
                                )}
                                <Table.Column
                                    title={formatMessage({ id: "drug.list.status" })}
                                    dataIndex="status"
                                    key="status"
                                    width={100}
                                    render={(value, record, index) => renderStatus(value, 0)}
                                    ellipsis
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.freeze.count" />}
                                    dataIndex={"use_count"}
                                    key="use_count"
                                    width={90}
                                    ellipsis
                                />
                                {orderStatus !== 3 ?
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.receive.count" />}
                                        dataIndex="receive_count"
                                        key="receive_count"
                                        ellipsis
                                        width={90}
                                        render={
                                            (value, record, index) => "-"
                                        }
                                    /> :
                                    <Table.Column
                                        title={<FormattedMessage id="drug.freeze.receive.count" />}
                                        dataIndex="use_count"
                                        key="receive_count"
                                        width={90}
                                        ellipsis
                                    />
                                }
                            </Table>
                        </>
                    }
                </Spin>
            </Modal>
            <ChangeMedicines bind={change_medicines_ref} refresh={hide} />
            <ChangeRecords bind={change_records_ref} />
        </React.Fragment >
    )
};
