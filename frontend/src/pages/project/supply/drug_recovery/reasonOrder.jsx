import React from "react";
import { Form, Input, message, Modal } from "antd";
import { useTranslation } from "../../../common/multilingual/component";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { updateRecoveryOrder } from "../../../../api/order";


export const ReasonOrder = (props) => {
    const [isVisible, setIsVisible] = useSafeState(false);
    const [orderId, setOrderId] = useSafeState(null);
    const [status, setStatus] = useSafeState(null);
    const [title, setTitle] = useSafeState(null);
    const [form] = Form.useForm();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const { runAsync: run_updateOrderStatus, loading: submitting } = useFetch(updateRecoveryOrder, { manual: true })

    // 展示对话框
    const cancel = () => {
        setIsVisible(false);
    };

    const updateOrder = () => {
        form.validateFields().then(values => {
            run_updateOrderStatus({ id: orderId, status: status, reason: values.reason }).then((data) => {
                message.success(data.msg);
                props.refresh();
                hide();
            });
        })
    }

    const show = (id, status, orderNumber) => {
        form.resetFields()
        setIsVisible(true)
        if (id) {
            setOrderId(id);
        }
        if (status) {
            setStatus(status);
            if (status === 5) {//取消
                setTitle(formatMessage({ id: 'shipment.cancel.order' }) + " - " + orderNumber)
            } else if (status === 4) {//丢失订单
                setTitle(formatMessage({ id: 'shipment.lose.order' }) + " - " + orderNumber)
            } else if (status === 8) {//终止订单
                setTitle(formatMessage({ id: 'shipment.end.order' }) + " - " + orderNumber)
            } else if (status === 9) {//关闭订单
                setTitle(formatMessage({ id: 'shipment.close.order' }) + " - " + orderNumber)
            }
        }
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    const hide = () => {
        setIsVisible(false);
        form.resetFields();
    };

    return (
        <Modal maskClosable={false} destroyOnClose={true} width={500} title={title} visible={isVisible} onOk={updateOrder} okText={formatMessage({ id: 'common.ok' })} onCancel={cancel}
            confirmLoading={submitting} centered>
            <Form form={form}>
                <Form.Item label={formatMessage({ id: 'drug.freeze.reason' })} name="reason" className="mar-ver-5"
                    rules={[{ required: true }]}>
                    <Input.TextArea allowClear style={{ height: 80 }} placeholder={formatMessage({ id: 'placeholder.input.common' })} />
                </Form.Item>
            </Form>
        </Modal>
    );
};


