import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {Button, Descriptions, Form, Modal, Row, Steps, Table,} from "antd";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {Title} from "components/title";
import {PageContextProvider} from "context/page";
import {useCacheTable} from "hooks/cache-table";
import {PaginationView} from "components/pagination";
import {approvalProcessStatus, shipmentMode} from "../../../../data/data";
import styled from "@emotion/styled";
import moment from "moment";
import {getApplicableSiteSupplyPlan} from "../../../../api/supply_plan";
import {getApprovalProcess} from "../../../../api/approval_process";
import ReactToPrint from "react-to-print";
import { permissions } from "../../../../tools/permission";
import { ApprovalDetailPrint } from "./approval_detail_print";
import { fillTableCellEmptyPlaceholder } from "components/table";


export const Approval = (props: any) => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const auth = useAuth();
    const envId = auth.env ? auth.env.id : null;
    const [visible, setVisible] = useSafeState(false);
    const [mode, setMode] = useSafeState(1);
    const [approvalNumber, setApprovalNumber] = useSafeState("");
    const [submitting, setSubmitting] = useSafeState(false);
    const [send, setSend] = useSafeState("");
    const [receive, setReceive] = useSafeState("");
    const [contacts, setContacts] = useSafeState("");
    const [supplyCount, setSupplyCount] = useSafeState("");
    const [phone, setPhone] = useSafeState("");
    const [email, setEmail] = useSafeState("");
    const [address, setAddress] = useSafeState("");
    const [status, setStatus] = useSafeState(0);
    const [approvalStatus, setApprovalStatus] = useSafeState(0);
    const [drugNames, setDrugNames] = useSafeState([]);
    const [reason, setReason] = useSafeState("");
    const [estimatedCompletionTime, setEstimatedCompletionTime] =
        useSafeState(0);
    const [approvalTime, setApprovalTime] = useSafeState(0);
    const [applicationTime, setApplicationTime] = useSafeState(0);
    const [approvalByEmail, setApprovalByEmail] = useSafeState("");
    const [applicationByEmail, setApplicationByEmail] = useSafeState("");
    const [tableData, setTableData] = useSafeState([]);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [supplyName, setSupplyName] = useSafeState("");
    const [supplyId, setSupplyId] = useSafeState("");
    const [expectedArrivalTime, setExpectedArrivalTime] = useSafeState(null);
    const [packageIsOpen, setPackageIsOpen] = useSafeState<any>(false);
    const [blindCount, setBlindCount] = useSafeState(0);
    const [openDrugCount, setOpenDrugCount] = useSafeState([]);
    const [record, setRecord] = useSafeState("");
    const [form] = Form.useForm();
  const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
    let componentRef: any = React.useRef();

    const { runAsync: run_getApprovalProcess, loading } = useFetch(
        getApprovalProcess,
        { manual: true }
    );
    const show = (
        record: any,
        sites: any[],
        storehouses: any[],
        packageIsOpen: any
    ) => {
        setMode(record.mode);
        setVisible(true);
        run_getApprovalProcess({
            id: record.task_id,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            const data = result.data;
            if (data != null) {
                setRecord(data);
                setStatus(data.status);
                setApprovalStatus(data.approvalStatus);
                setEstimatedCompletionTime(data.estimatedCompletionTime);
                setApprovalTime(data.approvalTime);
                setApprovalByEmail(data.approvalByEmail);
                setTableData(data.data.detailData);
                setOtherTableData(data.data.otherDetailData);
                setReason(data.reason);
                setApprovalNumber(data.number);
                setApplicationTime(data.applicationTime);
                setApplicationByEmail(data.applicationByEmail);
                setSupplyId(data.data.supplyId);
                getSupplyPlanList(record.receive_id, data.data.supplyId);
                setPackageIsOpen(packageIsOpen);
                setContacts(data.data.contacts);
                setPhone(data.data.phone);
                setEmail(data.data.email);
                setAddress(data.data.address);
                setSupplyCount(data.data.supplyCount);
                setExpectedArrivalTime(data.data.expectedArrivalTime);
                setBlindCount(data.data.blindCount);
                setOpenDrugCount(data.data.openDrugCount);
                if (data.data.drugNames.length > 0) {
                    setDrugNames(data.data.drugNames);
                }
                if (record.type === 1 || record.type === 5) {
                    const storehouse = storehouses.find(
                        (it) => record.send_id === it.value
                    );
                    if (storehouse != null) {
                        setSend(storehouse.label);
                    }
                } else {
                    const site = sites.find(
                        (it) => record.send_id === it.value
                    );
                    if (site != null) {
                        setSend(site.label);
                    }
                }
                const site = sites.find((it) => record.receive_id === it.value);
                if (site != null) {
                    setReceive(site.label);
                }
            }
        });
    };

    interface DrugGroupCacheTableProp {
        dataSource: any[];
    }
    const DrugGroupCacheTable = (props: DrugGroupCacheTableProp) => {
        return (
            <PageContextProvider>
                <DrugGroupTable {...props}></DrugGroupTable>
            </PageContextProvider>
        );
    };

    function renderPackageMethod(packageMethod: Boolean) {
        return packageMethod === true ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }

    const DrugGroupTable = (props: DrugGroupCacheTableProp) => {
        var tableData = useCacheTable({ dataSource: props.dataSource });
        tableData = fillTableCellEmptyPlaceholder(tableData)
        return (
            <>
                <Table
                    className="mar-top-5"
                    pagination={false}
                    rowKey={(record: any) => record._id}
                    dataSource={tableData}
                >
                    <Table.Column
                        title={
                            <FormattedMessage id="drug.configure.drugName" />
                        }
                        dataIndex={"name"}
                        key="name"
                        ellipsis
                        width={180}
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.list.expireDate" />}
                        dataIndex={"expirationDate"}
                        key="expirationDate"
                        ellipsis
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.list.batch" />}
                        dataIndex={"batchNumber"}
                        key="batchNumber"
                        ellipsis
                    />
                    <Table.Column
                        title={
                            <FormattedMessage id="shipment.order.availableCount" />
                        }
                        dataIndex={"useCount"}
                        key="useCount"
                        ellipsis
                    />
                    {packageIsOpen && (
                        <Table.Column
                            title={
                                <FormattedMessage id="shipment.order.package.method" />
                            }
                            dataIndex={"packageMethod"}
                            key="packageMethod"
                            ellipsis
                            render={(value, record, index) =>
                                renderPackageMethod(value)
                            }
                        />
                    )}
                    <Table.Column
                        width={150}
                        title={<FormattedMessage id="drug.freeze.count" />}
                        dataIndex={"count"}
                        key="count"
                        ellipsis
                    />
                </Table>
                <PaginationView></PaginationView>
            </>
        );
    };

    //获取供应计划
    const { runAsync: run_getSupplyPlanList } = useFetch(
        getApplicableSiteSupplyPlan,
        { manual: true }
    );
    const getSupplyPlanList = (receiveId: any, suppyId: any) => {
        run_getSupplyPlanList({
            envId: envId,
            siteId: receiveId,
        }).then((result: any) => {
            const data = result.data;
            if (data != null) {
                if (data.optionalSupplyPlan.length > 0) {
                    var supplyPlan = data.optionalSupplyPlan.filter(
                        (it: any) => it.id === suppyId
                    );
                    if (supplyPlan.length > 0) {
                        setSupplyName(supplyPlan[0].name);
                    }
                }
            }
        });
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        setSubmitting(false);
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                className="custom-large-modal"
                title={<FormattedMessage id={"shipment.approvalTask"} />}
                centered={true}

                visible={visible}
                onCancel={hide}
                confirmLoading={submitting}
                footer={
                    <Row justify={"end"}>
                        {permissions(
                            auth.project.permissions,
                            "operation.supply.shipment.approval.print"
                        ) && (
                                <ReactToPrint
                                    trigger={() => {
                                        return (
                                            <Button type={"primary"}>
                                                {
                                                    <FormattedMessage
                                                        id={"common.print"}
                                                    />
                                                }
                                            </Button>
                                        );
                                    }}
                                    content={() => componentRef}
                                />
                            )}
                    </Row>
                }
            >
                <div style={{ marginBottom: 12 }}>
                    <Title
                        name={formatMessage({
                            id: "shipment.basic.information",
                        })}
                    ></Title>
                </div>
                <Descriptions
                    column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
                >
                    <Descriptions.Item
                        label={formatMessage({ id: "shipment.send" })}
                        labelStyle={{
                            width: "140px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {send}
                    </Descriptions.Item>
                    <Descriptions.Item
                        label={formatMessage({ id: "shipment.receive" })}
                        labelStyle={{
                            width: "100px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {receive}
                    </Descriptions.Item>
                    <Descriptions.Item
                        label={formatMessage({
                            id: "projects.supplyPlan.supplyMode",
                        })}
                        labelStyle={{
                            width: "140px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {shipmentMode.find((it) => mode === it.value)?.label}
                    </Descriptions.Item>
                    {mode !== 5 &&
                        <>
                            <Descriptions.Item
                                label={formatMessage({ id: "drug.configure.drugName" })}
                                labelStyle={{
                                    width: "100px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {drugNames.join(", ")}
                            </Descriptions.Item>
                            <Descriptions.Item
                                label={formatMessage({ id: "shipment.supply" })}
                                labelStyle={{
                                    minWidth: "140px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {supplyName}
                            </Descriptions.Item>
                            <Descriptions.Item
                                label={formatMessage({ id: "shipment.supply.count" })}
                                labelStyle={{
                                    minWidth: "140px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {supplyCount ? supplyCount : "-"}
                            </Descriptions.Item>
                        </>
                    }
                    <Descriptions.Item
                        label={formatMessage({ id: "common.contacts" })}
                        labelStyle={{
                            minWidth: "100px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {(contacts ? contacts : "-") +
                            "/" +
                            (phone ? phone : "-") +
                            "/" +
                            (email ? email : "-") +
                            "/" +
                            (address ? address : "-")}
                    </Descriptions.Item>
                    <Descriptions.Item
                        label={formatMessage({ id: "shipment.expectedArrivalTime" })}
                        labelStyle={{
                            minWidth: "100px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {expectedArrivalTime === null || expectedArrivalTime === ""
                            ? "-"
                            : expectedArrivalTime}
                    </Descriptions.Item>
                </Descriptions>
                {mode === 5 &&
                    <>
                        <div style={{ marginBottom: 12 }}>
                            <Title
                                name={formatMessage({
                                    id: "drug.medicine",
                                })}
                            ></Title>
                        </div>
                        <Descriptions key={1}
                            column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
                        >
                            <Descriptions.Item
                                label={formatMessage({
                                    id: "shipment.blindCount",
                                })}
                                labelStyle={{
                                    minWidth: "100px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {blindCount}
                            </Descriptions.Item>
                            <Descriptions.Item
                                label=""
                            >
                                <div></div>
                            </Descriptions.Item>
                            {openDrugCount.map((it: any) => (
                                <Descriptions.Item
                                    label={it.drugName}
                                    labelStyle={{
                                        minWidth: "100px",
                                        justifyContent: "flex-end",
                                        color: "#4E5969",
                                    }}
                                >
                                    {it.number}
                                </Descriptions.Item>
                            ))
                            }
                        </Descriptions>
                    </>
                }
                {tableData != null && tableData.length > 0 && (
                    <div style={{ marginTop: 24 }}>
                        <div style={{ marginBottom: 12 }}>
                            <Title
                                name={formatMessage({ id: "shipment.medicine" })}
                            ></Title>
                        </div>
                        <DrugGroupCacheTable
                            dataSource={tableData}
                        ></DrugGroupCacheTable>
                    </div>
                )}
                {otherTableData != null && otherTableData.length > 0 && (
                    <div>
                        <div style={{ marginBottom: 12 }}>
                            <Title
                                name={formatMessage({ id: "drug.other" })}
                            ></Title>
                        </div>
                        <DrugGroupCacheTable
                            dataSource={otherTableData}
                        ></DrugGroupCacheTable>
                    </div>
                )}
                <div style={{ marginTop: 24 }}>
                    <div style={{ marginBottom: 12 }}>
                        <Title
                            name={formatMessage({
                                id: "shipment.approval.confirm.title",
                            })}
                        ></Title>
                    </div>
                    <Form form={form}>
                        <Row>
                            <Form.Item
                                label={formatMessage({
                                    id: "shipment.approval.confirm",
                                })}
                                className="mar-ver-5"
                            >
                                {approvalStatus === 1 || approvalStatus === 2
                                    ? approvalProcessStatus.find(
                                        (it) => it.value === approvalStatus
                                    )?.label
                                    : ""}
                            </Form.Item>
                        </Row>
                        {approvalStatus === 2 ? (
                            <Row>
                                <Form.Item
                                    label={formatMessage({
                                        id: "drug.freeze.reason",
                                    })}
                                    className="mar-ver-5"
                                >
                                    {reason}
                                </Form.Item>
                            </Row>
                        ) : (
                            <StepsContainer>
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        marginBottom: 32,
                                    }}
                                >
                                    <span style={{ color: "#4E5969" }}>
                                        {intl.formatMessage({
                                            id: "subject.unblinding.approval.number",
                                        })}
                                        :
                                    </span>
                                    <span
                                        style={{
                                            marginLeft: 8,
                                            color: "#1D2129",
                                        }}
                                    >
                                        {approvalNumber}
                                    </span>
                                </div>
                                <Steps
                                    progressDot
                                    current={approvalStatus === 0 ? 0 : 1}
                                >
                                    <Steps.Step
                                        title={
                                            <div style={{ fontSize: 14 }}>
                                                {formatMessage({
                                                    id: "subject.urgentUnblindingApproval.pending",
                                                })}
                                            </div>
                                        }
                                        description={
                                            <div
                                                style={{
                                                    fontSize: 12,
                                                    color: "#4E5969",
                                                    alignItems: "center",
                                                }}
                                            >
                                                <div>{applicationByEmail}</div>
                                                <div>
                                                    {moment
                                                        .unix(applicationTime)
                                                        .utc()
                                                        .add(timeZone, "hour")
                                                        .format(
                                                            "YYYY-MM-DD HH:mm:ss"
                                                        )}
                                                </div>
                                            </div>
                                        }
                                    />
                                    <Steps.Step
                                        title={
                                            <div style={{ fontSize: 14 }}>
                                                {approvalStatus === 1 ? (
                                                    <FormattedMessage
                                                        id={
                                                            "project.task.approvalStatus.passed"
                                                        }
                                                    />
                                                ) : (
                                                    <FormattedMessage
                                                        id={
                                                            "project.task.approvalStatus.rejected"
                                                        }
                                                    />
                                                )}
                                            </div>
                                        }
                                        description={
                                            <div
                                                style={{
                                                    fontSize: 12,
                                                    color: "#4E5969",
                                                    alignItems: "center",
                                                }}
                                            >
                                                <div>{approvalByEmail}</div>
                                                <div>
                                                    {moment
                                                        .unix(approvalTime)
                                                        .utc()
                                                        .add(timeZone, "hour")
                                                        .format(
                                                            "YYYY-MM-DD HH:mm:ss"
                                                        )}
                                                </div>
                                            </div>
                                        }
                                    />
                                </Steps>
                            </StepsContainer>
                        )}
                    </Form>
                </div>
            </Modal>
            <div style={{ display: "none" }}>
                <div ref={(el) => (componentRef = el)}>
                    <ApprovalDetailPrint
                        record={record}
                        send={send}
                        receive={receive}
                        detailData={tableData}
                        otherTableData={otherTableData}
                        mode={mode}
                        drugNames={drugNames}
                        supplyName={supplyName}
                        packageIsOpen={true}
                        contacts={contacts}
                        phone={phone}
                        email={email}
                        address={address}
                        supplyCount={supplyCount}
                        blindCount={blindCount}
                        openDrugCount={openDrugCount}
                        expectedArrivalTime={expectedArrivalTime}
                    />
                </div>
            </div>
        </React.Fragment>
    );
};

const StepsContainer = styled.div`
    margin-bottom: 18px;
    padding: 16px 12px;
    border-radius: 2px;
    background: rgba(227, 228, 230, 0.2);
`;
