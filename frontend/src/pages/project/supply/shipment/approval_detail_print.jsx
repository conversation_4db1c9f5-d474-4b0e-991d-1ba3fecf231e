import React from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { Descriptions, Row, Steps, Table } from "antd";
import { useAuth } from "../../../../context/auth";
import { Title } from "components/title";
import { PageContextProvider } from "context/page";
import { useCacheTable } from "hooks/cache-table";
import { PaginationView } from "components/pagination";
import { approvalProcessStatus, shipmentMode } from "../../../../data/data";
import styled from "@emotion/styled";
import moment from "moment";


function renderPackageMethod(packageMethod) {
    return (
        packageMethod === true
            ?
            <FormattedMessage id="shipment.order.packageMethod.package" />
            :
            <FormattedMessage id="shipment.order.packageMethod.single" />
    );
}




export const ApprovalDetailPrint = (props) => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const auth = useAuth()
  const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;

    const DrugGroupCacheTable = (props) => {
        return (
            <PageContextProvider>
                <DrugGroupTable {...props}></DrugGroupTable>
            </PageContextProvider>
        )
    }

    const DrugGroupTable = (props) => {
        const tableData = useCacheTable({ dataSource: props.dataSource });
        return (
            <>
                <Table
                    className="mar-top-5"
                    pagination={false}
                    rowKey={(record) => (record._id)}
                    dataSource={tableData}
                >
                    <Table.Column
                        title={<FormattedMessage id="drug.configure.drugName" />}
                        dataIndex={"name"}
                        key="name"
                        ellipsis
                        width={180}
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.list.expireDate" />}
                        dataIndex={"expirationDate"}
                        key="expirationDate"
                        ellipsis
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.list.batch" />}
                        dataIndex={"batchNumber"}
                        key="batchNumber"
                        ellipsis
                    />
                    <Table.Column
                        title={<FormattedMessage id="shipment.order.availableCount" />}
                        dataIndex={"useCount"}
                        key="useCount"
                        ellipsis
                    />
                    {props.packageIsOpen &&
                        <Table.Column
                            title={<FormattedMessage id="shipment.order.package.method" />}
                            dataIndex={"packageMethod"}
                            key="packageMethod"
                            ellipsis
                            render={(value, record, index) => (renderPackageMethod(value))}
                        />
                    }
                    <Table.Column
                        width={150}
                        title={<FormattedMessage id="drug.freeze.count" />}
                        dataIndex={"count"}
                        key="count"
                        ellipsis
                    />
                </Table>
                <PaginationView ></PaginationView>
            </>
        )
    }


    return (
        <React.Fragment>
            <div className="mar-all-20">
                <Row className="history_title" style={{ paddingBottom: 24 }}>{<FormattedMessage id={props.record.name + ""} />}</Row>
                <div style={{ marginBottom: 12 }}>
                    <Title
                        name={formatMessage({
                            id: "shipment.basic.information",
                        })}
                    ></Title>
                </div>
                <Descriptions column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}>
                    <Descriptions.Item label={formatMessage({ id: 'shipment.send' })} labelStyle={{ minWidth: "140px", justifyContent: "flex-end", color: "#4E5969" }}>{props.send}</Descriptions.Item>
                    <Descriptions.Item label={formatMessage({ id: 'shipment.receive' })} labelStyle={{ minWidth: "100px", justifyContent: "flex-end", color: "#4E5969" }}>{props.receive}</Descriptions.Item>
                    <Descriptions.Item label={formatMessage({ id: 'projects.supplyPlan.supplyMode' })} labelStyle={{ minWidth: "140px", justifyContent: "flex-end", color: "#4E5969" }}>{shipmentMode.find(it => (props.mode === it.value))?.label}</Descriptions.Item>
                    {props.mode !== 5 &&
                        <>
                            <Descriptions.Item label={formatMessage({ id: 'drug.configure.drugName' })} labelStyle={{ minWidth: "100px", justifyContent: "flex-end", color: "#4E5969" }}>{props.drugNames.join(", ")}</Descriptions.Item>
                            <Descriptions.Item label={formatMessage({ id: 'shipment.supply' })} labelStyle={{ minWidth: "140px", justifyContent: "flex-end", color: "#4E5969" }}> {props.supplyName}    </Descriptions.Item>
                            <Descriptions.Item label={formatMessage({ id: 'shipment.supply.count' })} labelStyle={{ minWidth: "140px", justifyContent: "flex-end", color: "#4E5969" }}> {props.supplyCount ? props.supplyCount : "-"}    </Descriptions.Item>
                        </>
                    }
                    <Descriptions.Item label={formatMessage({ id: "common.contacts" })} labelStyle={{ minWidth: "100px", justifyContent: "flex-end", color: "#4E5969" }}>
                        {(props.contacts ? props.contacts : "-") + "/" +
                            (props.phone ? props.phone : "-") +
                            "/" +
                            (props.email ? props.email : "-") +
                            "/" +
                            (props.address ? props.address : "-")}
                    </Descriptions.Item>
                    <Descriptions.Item
                        label={formatMessage({ id: "shipment.expectedArrivalTime" })}
                        labelStyle={{
                            minWidth: "100px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {props.expectedArrivalTime === null || props.expectedArrivalTime === ""
                            ? "-"
                            : props.expectedArrivalTime}
                    </Descriptions.Item>
                </Descriptions>
                {props.mode === 5 &&
                    <>
                        <div style={{ marginBottom: 12 }}>
                            <Title
                                name={formatMessage({
                                    id: "drug.medicine",
                                })}
                            ></Title>
                        </div>
                        <Descriptions key={1}
                            column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
                        >
                            <Descriptions.Item
                                label={formatMessage({
                                    id: "shipment.blindCount",
                                })}
                                labelStyle={{
                                    minWidth: "100px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {props.blindCount}
                            </Descriptions.Item>
                            <Descriptions.Item
                                label=""
                            >
                                <div></div>
                            </Descriptions.Item>
                            {props.openDrugCount.map((it) => (
                                <Descriptions.Item
                                    label={it.drugName}
                                    labelStyle={{
                                        minWidth: "100px",
                                        justifyContent: "flex-end",
                                        color: "#4E5969",
                                    }}
                                >
                                    {it.number}
                                </Descriptions.Item>
                            ))
                            }
                        </Descriptions>
                    </>
                }
                {props.mode !== 5 && props.detailData != null && props.detailData.length > 0 &&
                    <div style={{ marginTop: 24 }}>
                        <div style={{ marginBottom: 12 }}>
                            <Title name={formatMessage({ id: 'shipment.medicine' })}></Title>
                        </div>
                        <DrugGroupCacheTable
                            dataSource={props.detailData}
                        ></DrugGroupCacheTable>
                    </div>
                }
                {props.mode !== 5 && props.otherTableData != null && props.otherTableData.length > 0 &&
                    <div >
                        <div style={{ marginBottom: 12 }}>
                            <Title name={formatMessage({ id: 'drug.other' })}></Title>
                        </div>
                        <DrugGroupCacheTable
                            dataSource={props.otherTableData}
                        ></DrugGroupCacheTable>
                    </div>
                }

                <div style={{ marginTop: 24 }}>
                    <div style={{ marginBottom: 12 }}>
                        <Title name={formatMessage({ id: 'shipment.approval.confirm.title' })}></Title>
                    </div>
                    <Descriptions column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}>
                        <Descriptions.Item label={formatMessage({ id: 'shipment.approval.confirm' })} labelStyle={{ color: "#4E5969" }}> {(props.record.approvalStatus === 1 || props.record.approvalStatus === 2) ? approvalProcessStatus.find(it => (it.value === props.record.approvalStatus))?.label : ""} </Descriptions.Item>
                    </Descriptions>
                    {props.record.approvalStatus === 2 ?
                        <Descriptions column={{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }}>
                            <Descriptions.Item label={formatMessage({ id: 'drug.freeze.reason' })} labelStyle={{ color: "#4E5969" }}>  {props.record.reason}  </Descriptions.Item>
                        </Descriptions>
                        : null}
                    <StepsContainer>
                        <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                            <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                            <span style={{ marginLeft: 8, color: "#1D2129" }}>{props.record.number}</span>
                        </div>
                        <Steps progressDot current={props.record.approvalStatus === 0 ? 0 : 1}>
                            <Steps.Step title={
                                <div style={{ fontSize: 14 }}>
                                    {formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}
                                </div>
                            }
                                description={
                                    <div style={{ fontSize: 12, color: "#4E5969", alignItems: "center" }}>
                                        <div>
                                            {props.record.applicationByEmail}
                                        </div>
                                        <div>
                                            {moment.unix(props.record.applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                        </div>
                                    </div>
                                } />
                            <Steps.Step title={
                                <div style={{ fontSize: 14 }}>
                                    {
                                        props.record.approvalStatus === 1 ? <FormattedMessage id={"project.task.approvalStatus.passed"} /> :
                                            <FormattedMessage id={"project.task.approvalStatus.rejected"} />
                                    }
                                </div>
                            }
                                description={
                                    <div style={{ fontSize: 12, color: "#4E5969", alignItems: "center" }}>
                                        <div>
                                            {props.record.approvalByEmail}
                                        </div>
                                        <div>
                                            {moment.unix(props.record.approvalTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                        </div>
                                    </div>
                                } />
                        </Steps>
                    </StepsContainer>

                </div>
            </div>
        </React.Fragment >
    )
};




const StepsContainer = styled.div`
    margin-bottom: 18px;
    padding: 16px 12px;
    border-radius: 2px;
    background: rgba(227,228,230,0.2);
`