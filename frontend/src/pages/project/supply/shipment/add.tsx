import React, { useState } from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { Col, DatePicker, Form, InputNumber, message, Modal, notification, Row, Select, Spin, Table, Space, Input } from "antd";
import dayjs from "dayjs";
import moment from "moment";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { getMedicineBatchList } from "../../../../api/medicine";
import { getOtherMedicines } from "../../../../api/drug_other";
import { addOrder } from "../../../../api/order";
import { getProjectSiteById } from "../../../../api/project_site";
import { getApplicableSiteSupplyPlan, getSupplyIdCount, } from "../../../../api/supply_plan";
import { Title } from "components/title";
import { PageContextProvider } from "context/page";
import { useRowSelection } from "hooks/row-selection";
import { useCacheTable } from "hooks/cache-table";
import { PaginationView } from "components/pagination";
import { createUUid } from "utils/uuid";
import { CheckCircleTwoTone, CloseCircleFilled } from "@ant-design/icons";
import { nilObjectId } from "data/data.jsx";
import { roleIsBind } from "../../../../api/roles";
import { getDrugNamesByRoleId } from "../../../../api/drug";
import styled from "@emotion/styled";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";



export const Add = (props: any) => {

    const intl = useTranslation();
    const auth = useAuth();
    const { Option } = Select;
    const { formatMessage } = intl;
    const [visible, setVisible] = useSafeState(false);
    const [mode, setMode] = useSafeState(null);
    const [submitting, setSubmitting] = useSafeState(false);
    const [institutes, setInstitutes] = useSafeState([]);
    const [sites, setSites] = useSafeState([]);
    const [data, setData] = useSafeState([]);
    const [otherDrugs, setOtherDrugs] = useSafeState<any>(null);
    const [contactsData, setContactsData] = useSafeState<any>(null);
    const [contacts, setContacts] = useSafeState<any>(null);
    const [tableData, setTableData] = useSafeState<any>([]);
    const [supplyPlanData, setSupplyPlanData] = useSafeState([]);
    const [supplyId, setSupplyId] = useSafeState("");
    const [showErrorMessage, setShowErrorMessage] = useSafeState("");
    const [selectedIds, setSelectedIds] = useSafeState([]);
    const [intervalStart, setIntervalStart] = useSafeState("");
    const [intervalEnd, setIntervalEnd] = useSafeState("");
    const [intervalStartStatus, setIntervalStartStatus] = useSafeState<any>(undefined);
    const [intervalEndStatus, setIntervalEndStatus] = useSafeState<any>(undefined);
    const [otherSelecteds, setOtherSelecteds] = useSafeState([]);
    const [otherSelectedIds, setOtherSelectedIds] = useSafeState([]);
    const [drugNameDefaultValue, setDrugNameDefaultValue] = useSafeState([]);
    const [expectedArrivalTimeValue, setExpectedArrivalTimeValue] = useSafeState(null);
    const [supplyCount, setSupplyCount] = useSafeState("-");
    const [packageIsOpen, setPackageIsOpen] = useSafeState<any>(false);
    const [openDrug, setOpenDrug] = useSafeState<any>([]);
    const [haveDrug, setHaveDrug] = useSafeState<any>(true);
    const [haveOtherDrug, setHaveOtherDrug] = useSafeState<any>(true);
    const [mixPackage, setMixPackage] = useSafeState<any>(null);
    const [orderApplicationConfig, setOrderApplicationConfig] = useSafeState<any>([]);
    const [applicationConfig, setApplicationConfig] = useSafeState<any>([]);
    // const [cohortId,setCohortId] = useSafeState(null);
    const [form] = Form.useForm();
    const projectId = auth.project ? auth.project.id : null;
    // const projectType = auth.project.info.type;
    // const cohorts = auth.env ? auth.env.cohorts : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const [isBlind, setIsBlind] = useSafeState(true);
    const [isSupplyRatio, setIsSupplyRatio] = useSafeState(false);
    const [canCreateOrder, setCanCreateOrder] = useSafeState<any>(false);
    const [blindSupplyRatio, setBlindSupplyRatio] = useSafeState(1);
    const DatePickers: any = DatePicker;
    const show = (
        institutes: any,
        sites: any,
        storehouses: any,
        isBlind: any,
        drugData: any,
        drugNameDefaultValue: any,
        isOpenPackage: any,
        haveDrug: any,
        haveOtherDrug: any,
        mixPackage: any,
    ) => {
        setHaveDrug(haveDrug);
        setHaveOtherDrug(haveOtherDrug);
        isBindRoleBool();
        //setContacts("");
        setInstitutes([]);
        setSites([]);
        setVisible(true);
        setDrugNameDefaultValue(drugNameDefaultValue);
        setPackageIsOpen(isOpenPackage);
        setMixPackage(mixPackage);
        form.setFieldsValue({ drugNames: drugNameDefaultValue }); // 默认草稿
        //获取中心和仓库信息
        if (isBlind) {
            setInstitutes(storehouses.filter((i: any) => i.deleted === 2));
        } else {
            setInstitutes(institutes.filter((i: any) => i.deleted === 2));
        }
        setSites(sites.filter((i: any) => i.deleted === 2));
    };

    const {
        runAsync: run_getMedicineBatchList,
        loading: fetchingMedicineBatchList,
    } = useFetch(getMedicineBatchList, { manual: true });

    //获取研究产品批次号
    const onGetMedicineBatchList = (sendId: any, mode: any, supplyId: any) => {
        setTableData([]);
        drugTableSelectIdsReset();
        setShowErrorMessage("");
        setCanCreateOrder(false);
        if (mode !== undefined && mode != null) {
            const drugNamesWithSalts: any = [];
            var drugNames = form.getFieldValue("drugNames");
            if (
                (mode !== 1 && mode !== 5 && mode !== 6) &&
                (supplyId === undefined ||
                    supplyId === null ||
                    supplyId.length <= 0) &&
                form.getFieldValue("receiveId") != null &&
                !isBlind
            ) {
                setCanCreateOrder(false);
                setShowErrorMessage(
                    formatMessage({ id: "shipment.order.supplyUnset.info" })
                );
                return;
            }
            if (
                (mode !== 1 &&
                    !isBlind &&
                    drugNames !== undefined &&
                    drugNames != null &&
                    drugNames.length > 0 &&
                    supplyId.length > 0) ||
                mode === 1 ||
                (isBlind &&
                    mode !== 1 &&
                    supplyId !== undefined &&
                    supplyId.length > 0)
            ) {
                if (
                    mode !== 1 &&
                    isBlind &&
                    drugNames === undefined &&
                    drugNameDefaultValue !== undefined &&
                    drugNameDefaultValue.length > 0
                ) {
                    drugNames = drugNameDefaultValue;
                }
                if (
                    mode !== 1 &&
                    drugNames !== undefined &&
                    drugNames != null &&
                    drugNames.length > 0
                ) {
                    drugNames.forEach((drugName: any) => {
                        var splitDrugName = drugName.split("/");
                        drugNamesWithSalts.push({
                            saltName: splitDrugName[0],
                            salt: splitDrugName[1],
                        });
                    });
                }
                run_getMedicineBatchList({
                    projectId: projectId,
                    customerId: customerId,
                    envId: envId,
                    sendId: sendId,
                    receiveId: form.getFieldValue("receiveId"),
                    roleId: auth.project.permissions.role_id,
                    mode: mode,
                    supplyPlanId: supplyId,
                    drugNamesWithSalts: drugNamesWithSalts,
                    drugNames: drugNames,
                }).then((result: any) => {
                    const data = result.data;
                    setCanCreateOrder(data.canCreateOrder);
                    if (!data.canCreateOrder && data.message !== "") {
                        setShowErrorMessage(data.message);
                    }
                    if (data.items != null && data.items !== undefined) {
                        const tableSource = data.items.map((it: any) => ({
                            _id: createUUid(),
                            name: it._id.name,
                            batchNumber: it._id.batchNumber,
                            expirationDate: it._id.expirationDate,
                            availableCount: it.availableCount,
                            packageMethod: it.packageMethod,
                            count: it.count,
                            salt: it.salt,
                            saltName: it.name,
                            isOpen: it.isOpen,
                            message: it.message,
                            mixPage: it.mixPage,
                            mixPageOther: it.mixPageOther,
                        }));
                        setTableData(tableSource);
                        drugTableSelectIdsReset();
                    } else {
                        setTableData([]);
                        drugTableSelectIdsReset();
                    }
                });
            } else {
                if (mode === 5) {
                    setCanCreateOrder(true)
                }
            }
        }
    };

    //获取供应计划
    const { runAsync: run_getSupplyPlanList } = useFetch(
        getApplicableSiteSupplyPlan,
        { manual: true }
    );

    const getSupplyPlanList = (receiveId: any) => {
        setSupplyId("");
        setSupplyPlanData([]);
        setShowErrorMessage("");
        setTableData([]);
        run_getSupplyPlanList({
            envId: envId,
            siteId: receiveId,
        }).then((result: any) => {
            const data = result.data;
            if (data != null) {
                if (
                    mode !== undefined &&
                    mode != null &&
                    mode !== 1 &&
                    data.bindSupplyPlan === undefined &&
                    !isBlind
                ) {
                    setCanCreateOrder(false);
                    setShowErrorMessage(
                        formatMessage({ id: "shipment.order.supplyUnset.info" })
                    );
                    setSupplyCount("-");
                    return;
                }

                if (
                    data.bindSupplyPlan !== undefined &&
                    data.bindSupplyPlan != null &&
                    mode !== 1
                ) {
                    setSupplyId(data.bindSupplyPlan.id);
                    getSupplyCountBySupplyId(
                        data.bindSupplyPlan.id,
                        form.getFieldValue("mode")
                    );
                    onGetMedicineBatchList(
                        form.getFieldValue("sendId"),
                        form.getFieldValue("mode"),
                        data.bindSupplyPlan.id
                    );
                    //获取未编号的研究产品名称
                    getOtherDrugs(form.getFieldValue("sendId"), data.bindSupplyPlan.id);
                }
                if (
                    data.optionalSupplyPlan !== undefined &&
                    data.optionalSupplyPlan != null &&
                    data.optionalSupplyPlan.length > 0
                ) {
                    const options = data.optionalSupplyPlan.map((it: any) => ({
                        label: it.name,
                        value: it.id,
                    }));
                    setSupplyPlanData(options);
                }
            }
        });
    };

    //计算供应量总和
    const { runAsync: run_getSupplyIdCount } = useFetch(getSupplyIdCount, {
        manual: true,
    });
    const getSupplyCountBySupplyId = (supplyId: any, mode: any) => {
        setSupplyCount("-");
        if (supplyId != null) {
            const drugNamesWithSalts: any = [];
            var drugNames = form.getFieldValue("drugNames");
            if (
                mode !== 1 &&
                drugNames !== undefined &&
                drugNames != null &&
                drugNames.length > 0
            ) {
                drugNames.forEach((drugName: any) => {
                    var splitDrugName = drugName.split("/");
                    drugNamesWithSalts.push({
                        saltName: splitDrugName[0],
                        salt: splitDrugName[1],
                    });
                });
                run_getSupplyIdCount({
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    supplyId: supplyId,
                    drugNamesWithSalts: drugNamesWithSalts,
                    drugNames: drugNames,
                    mode: mode,
                }).then((result: any) => {
                    const data = result.data;
                    if (data != null) {
                        setSupplyCount(data);
                    }
                });
            }
        }
    };

    const disabledDate = (current: any) => {
        return current && current < moment().startOf('day');
    };


    //获取其它研究产品名称
    const { runAsync: run_getOtherMedicines, loading: fetchingOtherMedicines } =
        useFetch(getOtherMedicines, { manual: true });
    const getOtherDrugs = (sendId: any, supplyId: any) => {
        setOtherDrugs([]);
        if (sendId !== undefined && sendId.length > 0) {
            const drugNamesWithSalts: any = [];
            var drugNames = form.getFieldValue("drugNames");
            if (
                mode !== 1 &&
                drugNames !== undefined &&
                drugNames != null &&
                drugNames.length > 0
            ) {
                drugNames.forEach((drugName: any) => {
                    var splitDrugName = drugName.split("/");
                    drugNamesWithSalts.push({
                        saltName: splitDrugName[0],
                        salt: splitDrugName[1],
                    });
                });
            }
            run_getOtherMedicines({
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                sendId: sendId,
                receiveId: form.getFieldValue("receiveId"),
                drugNamesWithSalts: drugNamesWithSalts,
                drugNames: drugNames,
                orderType: 1,
                mode: form.getFieldValue("mode"),
                supplyPlanId: supplyId,
                roleId: auth.project.permissions.role_id,
            }).then((result: any) => {
                const data = result.data;
                if (data != null) {
                    setOtherDrugs(data.filter((item: any) => item.availableCount > 0));
                }
            });
        }
    };

    //获取研究产品名称
    const { runAsync: run_getDrugNames } = useFetch(getDrugNamesByRoleId, { manual: true })
    const onGetDrugNames = (isBind: any) => {
        setDrugNameDefaultValue([])
        run_getDrugNames({
            customerId: customerId,
            envId: envId,
            roleId: auth.project.permissions.role_id
        }).then(
            (result: any) => {
                const data = result.data
                if (data.drugNames != null) {
                    var openDrugNames: any = [];
                    data.drugNames.forEach((it: any) => {
                        if (!it.isBlindedDrug) {
                            openDrugNames.push(it.drugName)
                        }
                    })
                    setOpenDrug(openDrugNames)
                    if (isBind) {
                        var df: any = [];
                        var options: any = [];
                        data.drugNames.forEach((it: any) => {
                            var value = (it.saltDrugName !== "") ? it.saltDrugName + "/" + it.salt : it.drugName
                            options.push({
                                label: it.drugName,
                                value: value,
                                // disabled:!it.isOther && !it.isOpen,
                                disabled: it.isBlindedDrug,
                            })
                            if (!it.isOther) {
                                df.push(value)
                            }
                        })
                        setData(options)
                        setDrugNameDefaultValue(df)
                        form.setFieldsValue({ drugNames: df });
                    } else {
                        const options = data.drugNames.map((it: any) => ({
                            label: it.drugName,
                            value: (it.saltDrugName !== "") ? it.saltDrugName + "/" + it.salt : it.drugName,
                        }));
                        setData(options);
                    }
                }
            }
        )
    };

    const { runAsync: run_isBlindRole } = useFetch(roleIsBind, { manual: true })
    const isBindRoleBool = () => {
        run_isBlindRole({
            customerId: customerId,
            envId: envId,
            projectId: projectId,
            roleId: auth.project.permissions.role_id
        }).then(
            (result: any) => {
                const data = result.data
                setIsBlind(data);
                //获取研究产品名称
                onGetDrugNames(data);
            }
        )
    };

    const hide = () => {
        setMode(null);
        setVisible(false);
        form.resetFields();
        setSubmitting(false);
        drugTableSelectIdsReset();
        setOtherSelectedIds([]);
        setOtherSelecteds([]);
        setOtherDrugs(null);
        setTableData([]);
        setSupplyId("");
        setSupplyPlanData([]);
        setShowErrorMessage("");
        setCanCreateOrder(false);
        setInstitutes([]);
        setSites([]);
        setIsBlind(true);
        setExpectedArrivalTimeValue(null);
        setIsSupplyRatio(false);
        setOrderApplicationConfig([]);
        setApplicationConfig([]);
        setDrugNameDefaultValue([]);
        setData([]);
        setContactsData([]);
        setContacts(null);
        form.setFieldValue("contacts", null);
        setIntervalStart("");
        setIntervalEnd("");
    };

    //change 补充方式
    const onChangeMode = (mode: any) => {
        drugTableSelectIdsReset();
        setMode(mode);
        setIntervalStart("");
        setIntervalEnd("");
        setTableData([]);
        if (mode === 5) { //供应比例
            setShowErrorMessage("");
            setCanCreateOrder(true);
            orderApplicationConfig.forEach((it: any) => {
                form.setFieldValue(it.drugName, "")
            });
        } else {
            if (mode !== 6) {
                var receive = form.getFieldValue("receiveId")
                //获取供应计划
                if ((mode === 2 || mode === 3) && receive !== undefined && receive !== "") {
                    getSupplyPlanList(receive);
                } else {
                    onGetMedicineBatchList(form.getFieldValue("sendId"), mode, supplyId);
                    getSupplyCountBySupplyId(supplyId, mode);
                    //获取未编号的研究产品名称
                    getOtherDrugs(form.getFieldValue("sendId"), supplyId);
                }
            }
        }
    };

    //change 研究产品名称
    const onChangeDrugNames = (drugName: any) => {
        //是否有混包
        if (packageIsOpen) {
            var drugNames = form.getFieldValue("drugNames");
            if (
                mode !== 1 &&
                drugNames !== undefined &&
                drugNames != null &&
                drugNames.length > 0
            ) {
                const newDrugNames: any = []
                drugNames.forEach((drugName: any) => {
                    // var splitDrugName = drugName.split("/");
                    // drugNamesWithSalts.push({
                    //     saltName: splitDrugName[0],
                    //     salt: splitDrugName[1],
                    // });
                    if (newDrugNames.indexOf(drugName) === -1) {
                        let mixPackages = mixPackage[drugName]
                        if (mixPackages !== undefined && mixPackages.length > 0) {
                            newDrugNames.push(...mixPackages)
                        } else {
                            newDrugNames.push(drugName)
                        }
                    }
                });
                form.setFieldValue("drugNames", newDrugNames)
            }
        }

        onGetMedicineBatchList(
            form.getFieldValue("sendId"),
            form.getFieldValue("mode"),
            supplyId
        );
        //获取未编号的研究产品名称
        getOtherDrugs(form.getFieldValue("sendId"), supplyId);
        getSupplyCountBySupplyId(supplyId, form.getFieldValue("mode"));
    };

    // 删除研究产品
    const onDelChangeDrugNames = (drugName: any) => {
        //是否有混包
        if (packageIsOpen) {
            var drugNames = form.getFieldValue("drugNames");
            if (
                mode !== 1 &&
                drugNames !== undefined &&
                drugNames != null &&
                drugNames.length > 0
            ) {
                const newDrugNames: any = []
                let mixPackages = mixPackage[drugName]
                if (mixPackages !== undefined && mixPackages.length > 0) {
                    drugNames.forEach((name: any) => {
                        if (mixPackages.indexOf(name) === -1) {
                            newDrugNames.push(name)
                        }
                    });
                    form.setFieldValue("drugNames", newDrugNames)
                }

            }
        }
        onGetMedicineBatchList(
            form.getFieldValue("sendId"),
            form.getFieldValue("mode"),
            supplyId
        );
        //获取未编号的研究产品名称
        getOtherDrugs(form.getFieldValue("sendId"), supplyId);
        getSupplyCountBySupplyId(supplyId, form.getFieldValue("mode"));
    }

    //change 供应计划
    const changeSupplyId = (supplyId: any) => {
        drugTableSelectIdsReset();
        setSelectedIds([]);
        setSupplyId(supplyId);
        onGetMedicineBatchList(
            form.getFieldValue("sendId"),
            form.getFieldValue("mode"),
            supplyId
        );
        //获取未编号的研究产品名称
        getOtherDrugs(form.getFieldValue("sendId"), supplyId);
        getSupplyCountBySupplyId(supplyId, form.getFieldValue("mode"));
    };

    //change 起运地
    const changeSend = (send: any) => {
        //判断起运的和目的地是否一样
        if (send === form.getFieldValue("receiveId")) {
            message
                .error(
                    formatMessage({ id: "shipment.order.sendAndReceive.info" })
                )
                .then();
        }

        var mode = form.getFieldValue("mode")

        if (mode === 6) {
            intervalQuery(intervalStart, intervalEnd);
        } else {
            onGetMedicineBatchList(send, mode, supplyId);
            //获取未编号的研究产品名称
            getOtherDrugs(send, supplyId);
        }


    };

    const { runAsync: run_getProjectSiteById } = useFetch(getProjectSiteById, {
        manual: true,
    });
    //查询项目中心信息
    const getProjectSiteInfo = (site: any) => {
        setContactsData([]);
        setContacts(null);
        form.setFieldValue("contacts", null);
        form.setFieldValue("mode", null);
        setMode(null);
        setTableData([]);
        setIsSupplyRatio(false);
        setOrderApplicationConfig([]);
        setApplicationConfig([]);
        if (site != null && site !== "") {
            run_getProjectSiteById({
                siteId: site,
            }).then((result: any) => {
                const data = result.data;

                const options: any = [];
                if (
                    data.contactGroup != null &&
                    data.contactGroup !== undefined
                ) {
                    var defult = "";
                    data.contactGroup.forEach((it: any) => {
                        options.push({
                            label: (
                                <>
                                    <span>
                                        {(it.contacts ? it.contacts : "-") +
                                            "/" +
                                            (it.phone ? it.phone : "-") +
                                            "/" +
                                            (it.email ? it.email : "-")}
                                    </span>
                                    <p />
                                    <span style={{ color: "#93969B" }}>
                                        {it.address ? it.address : "-"}
                                    </span>
                                </>
                            ),
                            value:
                                it.contacts +
                                "," +
                                it.phone +
                                "," +
                                it.email +
                                "," +
                                it.address,
                            title:
                                (it.contacts ? it.contacts : "-") +
                                "/" +
                                (it.phone ? it.phone : "-") +
                                "/" +
                                (it.email ? it.email : "-") +
                                "/" +
                                (it.address ? it.address : "-"),
                        });
                        if (it.isdefault === 1) {
                            defult =
                                it.contacts +
                                "," +
                                it.phone +
                                "," +
                                it.email +
                                "," +
                                it.address;
                        }
                    });
                    setContactsData(options);
                    setContacts(defult);
                    if (defult !== "") {
                        form.setFieldValue("contacts", defult);
                    }
                } else {
                    if (
                        (data.contacts != null && data.contacts !== "") ||
                        (data.phone != null && data.phone !== "") ||
                        (data.email != null && data.email !== "") ||
                        (data.address != null && data.address !== "")
                    ) {
                        const options = [
                            {
                                label: (
                                    <>
                                        <span>
                                            {(data.contacts
                                                ? data.contacts
                                                : "-") +
                                                "/" +
                                                (data.phone
                                                    ? data.phone
                                                    : "-") +
                                                "/" +
                                                (data.email ? data.email : "-")}
                                        </span>
                                        <p />
                                        <span style={{ color: "#93969B" }}>
                                            {data.address ? data.address : "-"}
                                        </span>
                                    </>
                                ),
                                value:
                                    data.contacts +
                                    "," +
                                    data.phone +
                                    "," +
                                    data.email +
                                    "," +
                                    data.address,
                                title:
                                    (data.contacts ? data.contacts : "-") +
                                    "/" +
                                    (data.phone ? data.phone : "-") +
                                    "/" +
                                    (data.email ? data.email : "-") +
                                    "/" +
                                    (data.address ? data.address : "-"),
                            },
                        ];
                        var defaultValue =
                            data.contacts +
                            "," +
                            data.phone +
                            "," +
                            data.email +
                            "," +
                            data.address;
                        setContactsData(options);
                        setContacts(
                            data.contacts +
                            "," +
                            data.phone +
                            "," +
                            data.email +
                            "," +
                            data.address
                        );
                        if (defaultValue !== "") {
                            form.setFieldValue("contacts", defaultValue);
                        }
                    }
                }
                setIsSupplyRatio(data.supplyRatio)
                if (data.supplyRatio && data.orderApplicationConfig != null && data.orderApplicationConfig.length > 0) {
                    var configure: any = []
                    data.orderApplicationConfig.forEach((applicatioConfigure: any) => {
                        applicatioConfigure.drugNames.forEach((drugName: any) => {
                            //判断是否是开放药物
                            if (openDrug.indexOf(drugName.drugName) > -1) {
                                configure.push(drugName)
                            }
                        });
                    });
                    setOrderApplicationConfig(configure);
                    setApplicationConfig(data.orderApplicationConfig);
                }
            });
        }
    };

    //change 目的地
    const changeReceive = (receive: any) => {
        //判断起运的和目的地是否一样
        if (receive === form.getFieldValue("sendId")) {
            message
                .error(
                    formatMessage({ id: "shipment.order.sendAndReceive.info" })
                )
                .then();
        }

        //获取供应计划
        if ((mode === 2 || mode === 3) && receive !== undefined && receive !== "") {
            getSupplyPlanList(receive);
        }
        //获取目的地联系人信息
        getProjectSiteInfo(receive);
    };

    //保存订单
    const { runAsync: run_addOrder } = useFetch(addOrder, {
        manual: true,
        onError: (err: any) => {
            err.json().then((data: any) =>
                notification.open({
                    message: (
                        <div
                            style={{
                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                fontSize: "14px",
                            }}
                        >
                            <CloseCircleFilled
                                style={{
                                    color: "#F96964",
                                    paddingRight: "8px",
                                }}
                            />
                            {formatMessage({ id: "shipment.order.fail.title" })}
                        </div>
                    ),
                    description: (
                        <div style={{ paddingLeft: "20px", color: "#646566" }}>
                            {data.msg}
                        </div>
                    ),
                    duration: 5,
                    placement: "top",
                    style: {
                        width: "720px",
                        background: "#FEF0EF",
                        borderRadius: "4px",
                    },
                })
            );
        },
    });
    const save = () => {
        form.validateFields()
            .then((values) => {
                let expectedArrivalTime = (values.expectedArrivalTime !== null && values.expectedArrivalTime !== undefined && values.expectedArrivalTime !== "-") ? values.expectedArrivalTime : "";

                // let expectedArrivalTime = values.expectedArrivalTime
                //     ? moment(values.expectedArrivalTime).format("YYYY-MM-DD HH:mm:ss")
                //     : "";
                if (values.sendId === values.receiveId) {
                    message
                        .error(
                            formatMessage({
                                id: "shipment.order.sendAndReceive.info",
                            })
                        )
                        .then();
                    return;
                }
                if (mode !== 5) {
                    if (
                        mode !== 1 &&
                        tableData.length === 0 &&
                        otherSelecteds.length === 0
                    ) {
                        message
                            .error(
                                formatMessage({ id: "shipment.order.create.info" })
                            )
                            .then();
                        return;
                    }
                    if (
                        mode === 1 &&
                        drugTableSelectIds.length === 0 &&
                        otherSelecteds.length === 0
                    ) {
                        message
                            .error(
                                formatMessage({ id: "shipment.order.create.info" })
                            )
                            .then();
                        return;
                    }
                }

                let validate = true;
                const selectRowInfo: any = [];
                const drugNamesWithSalts: any = [];
                const selectIdsSet = new Set(drugTableSelectIds);
                if (mode !== 5) {
                    if (mode === 1) {
                        tableData.forEach((value: any) => {
                            if (selectIdsSet.has(value._id)) {
                                selectRowInfo.push(value);
                            }
                        });
                        selectRowInfo.forEach((value: any) => {
                            if (
                                value.count === undefined ||
                                value.count === null ||
                                value.count <= 0
                            ) {
                                validate = false;
                            }
                        });
                        if (!validate) {
                            message
                                .error(
                                    formatMessage({
                                        id: "shipment.order.create.count.info",
                                    })
                                )
                                .then();
                            return;
                        }
                    } else if (mode === 6) {
                        tableData.forEach((value: any) => {
                            if (selectIdsSet.has(value._id)) {
                                selectRowInfo.push(value);
                            }
                        });
                    } else {
                        selectRowInfo.push(...tableData);
                    }
                }
                //其它研究产品勾选上的判断是否填写值
                let otherValidate = true;
                otherSelecteds.forEach((value: any) => {
                    if (
                        value.useCount === undefined ||
                        value.useCount === null ||
                        value.useCount <= 0
                    ) {
                        otherValidate = false;
                    }
                });
                if (!otherValidate) {
                    message
                        .error(
                            formatMessage({
                                id: "shipment.order.create.count.info",
                            })
                        )
                        .then();
                    return;
                }

                if (mode !== 1 && mode !== 5 && isBlind && values.drugNames.length > 0) {
                    values.drugNames.forEach((drugName: any) => {
                        var splitDrugName = drugName.split("/");
                        drugNamesWithSalts.push({
                            saltName: splitDrugName[0],
                            salt: splitDrugName[1],
                        });
                    });
                }
                var openDrugCount: any = []
                if (mode === 5 && orderApplicationConfig.length > 0) {
                    orderApplicationConfig.forEach((it: any) => {
                        var drugName = it.drugName
                        var count = form.getFieldValue(drugName)
                        openDrugCount.push({ drugName: drugName, number: count })
                    });
                }
                setSubmitting(true);
                run_addOrder({
                    ...values,
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    expectedArrivalTime: expectedArrivalTime,
                    //    cohortId: cohortId,
                    roleId: auth.project.permissions.role_id,
                    medicinesCount: selectRowInfo,
                    otherMedicinesCount: otherSelecteds,
                    drugNamesWithSalts: drugNamesWithSalts,
                    supplyId: supplyId,
                    supplyCount: supplyCount,
                    openDrugCount: openDrugCount,
                    intervalStart: intervalStart,
                    intervalEnd: intervalEnd,
                }).then(
                    (result: any) => {
                        const data = result.data;
                        if (data.id != null && data.id !== nilObjectId) {
                            message.success(result.msg);
                            hide();
                        } else {
                            notification.open({
                                message: (
                                    <div
                                        style={{
                                            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                            fontSize: "14px",
                                        }}
                                    >
                                        <CheckCircleTwoTone
                                            twoToneColor="#2DA641"
                                            style={{ paddingRight: "8px" }}
                                        />
                                        {formatMessage({
                                            id: "common.success",
                                        })}
                                    </div>
                                ),
                                description: (
                                    <div
                                        style={{
                                            paddingLeft: "20px",
                                            color: "#646566",
                                        }}
                                    >
                                        {formatMessage({
                                            id: "shipment.order.success.title",
                                        })}
                                    </div>
                                ),
                                duration: 5,
                                placement: "top",
                                style: {
                                    width: "720px",
                                    background: "#F0FAF2",
                                    borderRadius: "4px",
                                },
                            });
                            hide();
                        }
                        props.refresh();
                    },
                    (data) => {
                        setSubmitting(false);
                    }
                );
            })
            .catch((errors) => {
                setSubmitting(false);
            });
    };

    const otherRowSelection = {
        type: "checkbox" as "checkbox",
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            setOtherSelecteds(selectedRows);
            setOtherSelectedIds(selectedRowKeys);
        },
        selectedRowKeys: otherSelectedIds,
        selectedRows: otherSelecteds,
    };

    const changeBlindCount = (value: any) => {
        //所有盲态药物的比例值
        var ratioCount = 0
        applicationConfig.forEach((configure: any) => {
            configure.drugNames.forEach((drugNameConfig: any) => {
                //判断是否是盲态药物
                if (openDrug.indexOf(drugNameConfig.drugName) === -1) {
                    ratioCount = ratioCount + drugNameConfig.number
                }
            });
        });
        var blindCount = form.getFieldValue("blindCount")
        //比例四舍五入
        var ratio = Math.round(blindCount / ratioCount);
        setBlindSupplyRatio(ratio)
        orderApplicationConfig.forEach((configure: any) => {
            if (ratio <= 0) {
                form.setFieldValue(configure.drugName, "")
            } else {
                form.setFieldValue(configure.drugName, ratio * configure.number)
            }

        });
    }


    const handleChange = (value: any, record: any) => {
        const _data = [...tableData];
        //根据recored.name，找到包装的药物名称
        if (packageIsOpen) {
            let mixPackages = mixPackage[record.name]
            if (mixPackages !== undefined && mixPackages.length > 0) {
                _data.forEach((it: any) => {
                    if (
                        mixPackages.indexOf(it.name) !== -1 &&
                        it.expirationDate === record.expirationDate &&
                        it.batchNumber === record.batchNumber
                    ) {
                        it.count = value["count"];
                    }
                });
            } else {
                _data.forEach((it: any) => {
                    if (
                        it.name === record.name &&
                        it.expirationDate === record.expirationDate &&
                        it.batchNumber === record.batchNumber &&
                        it._id === record._id
                    ) {
                        it.count = value["count"];
                    }
                });
            }
        } else {
            _data.forEach((it: any) => {
                if (
                    it.name === record.name &&
                    it.expirationDate === record.expirationDate &&
                    it.batchNumber === record.batchNumber &&
                    it._id === record._id
                ) {
                    it.count = value["count"];
                }
            });
        }

        setTableData(_data);
    };

    const otherHandleChange = (value: any, record: any) => {
        const _data = [...otherDrugs];
        _data.forEach((it) => {
            if (
                it.name === record.name &&
                it.expirationDate === record.expirationDate &&
                it.batchNumber === record.batchNumber
            ) {
                it.useCount = value["useCount"];
            }
        });
        setOtherDrugs(_data);
    };

    React.useImperativeHandle(props.bind, () => ({ show }));


    // const changeKey = (key: any) => {
    //     let err = false
    //     let data: any = {}
    //     for (let i = 0; i < key.length; i++) {
    //         let value = tableData.find((it: any) => key[i] == it._id)
    //         if (value?.mixPage === 2) {
    //             data.name = value.name
    //             data.otherName = value.mixPageOther?.join(",")
    //             err = true
    //             break
    //         }
    //     }
    //     if (err) {
    //         setCanCreateOrder(false)
    //         setShowErrorMessage(formatMessage({ id: "shipment.order.page.fail" }, { "name": data.name, "otherName": data.otherName }))
    //     }
    //     if (!err || key?.length === 0) {
    //         setCanCreateOrder(true)
    //         setShowErrorMessage("")
    //     }
    // }

    const {
        selectedIds: drugTableSelectIds,
        setSelectedIds: setDrugTableSelectIds,
        resetSelectedIds: drugTableSelectIdsReset,
        rowSelection: drugTableRowSelection,
    } = useRowSelection({
        allData: tableData,
        dataSource: tableData,
        key: "_id",
        packageIsOpen: packageIsOpen,
        allSelected: false,
        renderCell: null,
        onChange: null,
        operation: "add",
        mixPackage: mixPackage,
        //changeKey: changeKey
    });


    const intervalQuery = (intervalStart: any, intervalEnd: any) => {
        if (intervalStart !== null && intervalStart !== "" && intervalEnd !== null && intervalEnd !== "") {
            run_getMedicineBatchList({
                projectId: projectId,
                customerId: customerId,
                envId: envId,
                sendId: form.getFieldValue("sendId"),
                roleId: auth.project.permissions.role_id,
                mode: mode,
                intervalStart: intervalStart,
                intervalEnd: intervalEnd,
            }).then((result: any) => {
                const data = result.data;
                setCanCreateOrder(data.canCreateOrder);
                if (!data.canCreateOrder && data.message !== "") {
                    setShowErrorMessage(data.message);
                }
                if (data.items != null && data.items !== undefined) {
                    const tableSource = data.items.map((it: any) => ({
                        _id: createUUid(),
                        name: it._id.name,
                        batchNumber: it._id.batchNumber,
                        expirationDate: it._id.expirationDate,
                        availableCount: it.availableCount,
                        packageMethod: it.packageMethod,
                        count: it.count,
                        salt: it.salt,
                        saltName: it.name,
                        isOpen: it.isOpen,
                        message: it.message,
                        mixPage: it.mixPage,
                        mixPageOther: it.mixPageOther,
                    }));
                    setTableData(tableSource);
                    drugTableSelectIdsReset();
                } else {
                    setTableData([]);
                    drugTableSelectIdsReset();
                }
            });
        }

    }




    return (
        <React.Fragment>
            <Modal
                className="custom-blarge-modal"
                title={<FormattedMessage id={"common.add"} />}
                centered={true}
                maskClosable={false}
                visible={visible}
                onCancel={hide}
                onOk={save}
                okText={formatMessage({ id: "common.ok" })}
                okButtonProps={{ disabled: !canCreateOrder }}
                confirmLoading={submitting}
            >
                {showErrorMessage !== "" && (
                    <div
                        id="showErrorId"
                        style={{
                            background: "#fef0ef",
                            height: "32px",
                            marginBottom: "16px",
                            display: "flex",
                            alignItems: "center",
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                paddingLeft: "8px",
                                paddingRight: "8px",
                                color: "#fe5b5a",
                            }}
                        />
                        <span>{showErrorMessage} </span>
                    </div>
                )}

                <Spin
                    spinning={
                        fetchingOtherMedicines || fetchingMedicineBatchList
                    }
                >
                    <CustomForm labelWrap form={form} labelCol={{ span: 8 }}>
                        <Title
                            name={formatMessage({
                                id: "shipment.basic.information",
                            })}
                        ></Title>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item
                                    label={formatMessage({
                                        id: "shipment.send",
                                    })}
                                    name="sendId"
                                    rules={[{ required: true }]}
                                    className="mar-ver-5"
                                >
                                    <Select
                                        placeholder={intl.formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        allowClear
                                        options={institutes}
                                        onChange={(e) => changeSend(e)}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label={formatMessage({
                                        id: "shipment.receive",
                                    })}
                                    name="receiveId"
                                    rules={[{ required: true }]}
                                    className="mar-ver-5"
                                >
                                    <Select
                                        placeholder={intl.formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        allowClear
                                        options={sites}
                                        onChange={(e) => changeReceive(e)}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item
                                    label={formatMessage({
                                        id: "projects.supplyPlan.supplyMode",
                                    })}
                                    name="mode"
                                    rules={[{ required: true }]}
                                    className="mar-ver-5"
                                >
                                    <Select
                                        placeholder={intl.formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        allowClear
                                        onChange={(e) => onChangeMode(e)}
                                    >

                                        <Option value={1}>
                                            <FormattedMessage id="shipment.mode.set" />
                                        </Option>
                                        <Option value={2}>
                                            <FormattedMessage id="shipment.mode.reSupply" />
                                        </Option>
                                        <Option value={3}>
                                            <FormattedMessage id="shipment.mode.max" />
                                        </Option>
                                        {!isSupplyRatio ? null : (
                                            <Option value={5}>
                                                <FormattedMessage id="shipment.mode.supplyRatio" />
                                            </Option>
                                        )}
                                        <Option value={6}>
                                            <FormattedMessage id="shipment.mode.serialNo" />
                                        </Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            {(mode === 2 || mode === 3) && !isBlind && (
                                <Col span={12}>
                                    <Form.Item
                                        label={formatMessage({
                                            id: "drug.configure.drugName",
                                        })}
                                        rules={[{ required: true }]}
                                        name="drugNames"
                                        className="mar-ver-5"
                                    >
                                        <Select
                                            onChange={(v) =>
                                                onChangeDrugNames(v)
                                            }
                                            onDeselect={(v) =>
                                                onDelChangeDrugNames(v)
                                            }
                                            placeholder={intl.formatMessage({
                                                id: "placeholder.select.common",
                                            })}
                                            mode="multiple"
                                            showArrow
                                            options={data}
                                        ></Select>
                                    </Form.Item>
                                </Col>
                            )}

                            {mode !== 1 && mode !== 5 && mode !== 6 && (
                                <Col span={12}>
                                    <Form.Item
                                        label={formatMessage({
                                            id: "shipment.supply",
                                        })}
                                        // name="supplyId"
                                        className="mar-ver-5"
                                    >
                                        {!isBlind ? (
                                            <>
                                                {supplyPlanData.length > 0 &&
                                                    supplyId !== "" ? (
                                                    <Select
                                                        defaultValue={supplyId}
                                                        showArrow
                                                        disabled
                                                        onChange={
                                                            changeSupplyId
                                                        }
                                                        options={supplyPlanData}
                                                        placement="bottomLeft"
                                                    ></Select>
                                                ) : (
                                                    "-"
                                                )}
                                            </>
                                        ) : (
                                            <>
                                                {supplyPlanData.length > 0 ? (
                                                    <Select
                                                        defaultValue={supplyId}
                                                        showArrow
                                                        onChange={
                                                            changeSupplyId
                                                        }
                                                        options={supplyPlanData}
                                                    ></Select>
                                                ) : (
                                                    "-"
                                                )}
                                            </>
                                        )}
                                    </Form.Item>
                                </Col>
                            )}

                            {/* 补充方式：序列号*/}
                            {mode === 6 &&
                                <Col span={12} >
                                    <Form.Item
                                        label={formatMessage({
                                            id: "shipment.number.interval",
                                        })}
                                        name="interval"
                                        className="mar-ver-5"
                                        rules={[
                                            {
                                                required: true,
                                            }
                                        ]}
                                    >
                                        <Space>
                                            <Input
                                                placeholder={intl.formatMessage({
                                                    id: "placeholder.input.common",
                                                })}
                                                value={intervalStart}
                                                onChange={(e) => {
                                                    setIntervalStart(e.target.value);
                                                    setIntervalStartStatus(!e.target.value ? "error" : undefined);
                                                    intervalQuery(e.target.value, intervalEnd)
                                                }}
                                                status={intervalStartStatus}
                                                allowClear
                                                className="full-width"
                                            />
                                            <span>~</span>
                                            <Input
                                                placeholder={intl.formatMessage({
                                                    id: "placeholder.input.common",
                                                })}
                                                value={intervalEnd}
                                                onChange={(e) => {
                                                    setIntervalEnd(e.target.value);
                                                    setIntervalEndStatus(!e.target.value ? "error" : undefined);
                                                    intervalQuery(intervalStart, e.target.value);
                                                }
                                                }
                                                status={intervalEndStatus}
                                                allowClear
                                                className="full-width"
                                            />
                                        </Space>
                                    </Form.Item>
                                </Col>
                            }

                            {(mode === 2 || mode === 3) && isBlind && (
                                <Col span={12}>
                                    <Form.Item
                                        label={formatMessage({
                                            id: "drug.configure.drugName",
                                        })}
                                        name="drugNames"
                                        className="mar-ver-5"
                                    >
                                        <Select
                                            onChange={(v) =>
                                                onChangeDrugNames(v)
                                            }
                                            placeholder={intl.formatMessage({
                                                id: "placeholder.select.common",
                                            })}
                                            mode="multiple"
                                            showArrow
                                            defaultValue={drugNameDefaultValue}
                                            options={data}
                                        ></Select>
                                    </Form.Item>
                                </Col>
                            )}

                            <Col span={12}>
                                <Form.Item
                                    label={formatMessage({
                                        id: "common.contacts",
                                    })}
                                    name="contacts"
                                    className="mar-ver-5"
                                >
                                    <Select
                                        placeholder={intl.formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                        showArrow
                                        allowClear
                                        defaultValue={contacts}
                                        options={contactsData}
                                    />
                                </Form.Item>
                            </Col>
                            {(mode === 2 || mode === 3) && (
                                <Col span={12}>
                                    <Form.Item
                                        label={formatMessage({
                                            id: "shipment.supply.count",
                                        })}
                                        className="mar-ver-5"
                                    >
                                        {supplyCount}
                                    </Form.Item>
                                </Col>
                            )}

                        </Row>
                        <Form.Item
                            label={formatMessage({
                                id: "shipment.expectedArrivalTime",
                            })}

                            name="expectedArrivalTime"
                            className="mar-ver-5"
                            labelCol={{ span: 4 }}
                            wrapperCol={{ span: 24 }}
                        >
                            {/* <DatePickers
                                placeholder={formatMessage({
                                    id: "placeholder.select.common",
                                })}
                                className="full-width"
                                format={"YYYY-MM-DD HH:mm:ss"}
                                showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                            ></DatePickers> */}
                            <CustomDateTimePicker
                                disabledDate={disabledDate}
                                disabledTime={"disBefore"}
                                value={expectedArrivalTimeValue}
                                onChange={setExpectedArrivalTimeValue}
                                ph={'placeholder.select.common'}>
                            </CustomDateTimePicker>
                        </Form.Item>


                        {mode === 5 &&
                            <>
                                <Title
                                    name={formatMessage({
                                        id: "drug.medicine",
                                    })}
                                ></Title>

                                <Form.Item
                                    label={formatMessage({
                                        id: "shipment.blindCount",
                                    })}
                                    name="blindCount"
                                    className="mar-ver-5"
                                    labelCol={{ span: 4 }}
                                    wrapperCol={{ span: 24 }}
                                    rules={[{ required: true }]}
                                >
                                    <InputNumber
                                        style={{ whiteSpace: "pre-wrap" }}
                                        className="full-width"
                                        placeholder={intl.formatMessage({
                                            id: "placeholder.input.common",
                                        })}
                                        precision={0}
                                        min={1}
                                        step={1}
                                        onChange={(e) =>
                                            changeBlindCount(e)
                                        }
                                    />
                                </Form.Item>

                                <Row gutter={8}>
                                    {orderApplicationConfig.map((it: any) => (
                                        <Col span={12} >
                                            <Form.Item
                                                label={it.drugName}
                                                name={it.drugName}
                                                className="mar-ver-5"
                                            >
                                                <InputNumber
                                                    className="full-width"
                                                    placeholder={intl.formatMessage({
                                                        id: "placeholder.input.common",
                                                    })}
                                                    precision={0}
                                                    min={1}
                                                    step={1}
                                                />
                                            </Form.Item>
                                        </Col>
                                    ))}
                                </Row>
                            </>
                        }
                    </CustomForm>

                    {(mode === 1 || mode === 6) && haveDrug && (
                        <div style={{ marginTop: 24 }}>
                            <div style={{ marginBottom: 12 }}>
                                <Title
                                    name={formatMessage({
                                        id: "drug.medicine",
                                    })}
                                ></Title>
                            </div>
                            <DrugCacheTable
                                mode={mode}
                                dataSource={tableData}
                                rowSelection={drugTableRowSelection}
                                selectIds={drugTableSelectIds}
                                packageIsOpen={packageIsOpen}
                                onCountChange={handleChange}
                            ></DrugCacheTable>
                        </div>
                    )}
                    {mode !== 1 && mode !== 5 && mode !== 6 && haveDrug && (
                        <div style={{ marginTop: 24 }}>
                            <div style={{ marginBottom: 12 }}>
                                <Title
                                    name={formatMessage({
                                        id: "drug.medicine",
                                    })}
                                ></Title>
                            </div>
                            <DrugGroupCacheTable
                                dataSource={tableData}
                                onCountChange={handleChange}
                                packageIsOpen={packageIsOpen}
                            ></DrugGroupCacheTable>
                        </div>
                    )}
                    {
                        haveOtherDrug && mode !== 5 && mode !== 6 &&
                        < div style={{ marginTop: 12 }}>
                            <div style={{ marginBottom: 12 }}>
                                <Title
                                    name={formatMessage({
                                        id: "shipment.other.drug",
                                    })}
                                ></Title>
                            </div>
                            <Table
                                className="mar-top-5"
                                dataSource={otherDrugs ? fillTableCellEmptyPlaceholder(otherDrugs) : []}
                                pagination={false}
                                rowKey={(record) => record.name + record.batchNumber + record.expirationDate}
                                rowSelection={otherRowSelection}
                            >
                                <Table.Column
                                    title={intl.formatMessage({
                                        id: "drug.configure.drugName",
                                    })}
                                    dataIndex="name"
                                    key="name"
                                    ellipsis
                                    render={(value, record, index) =>
                                        <div style={{
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'normal',
                                            wordBreak: 'break-word',
                                        }}>
                                            {value}
                                        </div>
                                    }
                                />
                                <Table.Column
                                    width={120}
                                    title={intl.formatMessage({ id: "drug.list.expireDate" })}
                                    dataIndex={"expirationDate"}
                                    key="expirationDate"
                                    ellipsis
                                />
                                <Table.Column
                                    width={140}
                                    title={intl.formatMessage({ id: "drug.list.batch" })}
                                    dataIndex={"batchNumber"}
                                    key="batchNumber"
                                    ellipsis
                                />
                                <Table.Column
                                    width={90}
                                    title={intl.formatMessage({
                                        id: "shipment.order.availableCount",
                                    })}
                                    dataIndex={"availableCount"}
                                    key="availableCount"
                                    ellipsis
                                />
                                {packageIsOpen && (
                                    <Table.Column
                                        width={110}
                                        title={intl.formatMessage({
                                            id: "shipment.order.package.method",
                                        })}
                                        dataIndex="packageMethod"
                                        key="packageMethod"
                                        ellipsis
                                        render={(value, record, index) =>
                                            renderPackageMethod(value)
                                        }
                                    />
                                )}
                                <Table.Column
                                    width={120}
                                    title={intl.formatMessage({ id: "drug.freeze.count" })}
                                    dataIndex={"useCount"}
                                    key="useCount"
                                    ellipsis
                                    render={(value, record: any, index) => (
                                        <InputNumber
                                            className="full-width"
                                            placeholder={formatMessage({
                                                id: "placeholder.input.common",
                                            })}
                                            precision={0}
                                            min={0}
                                            step={1}
                                            max={record.availableCount}
                                            value={
                                                record.useCount === null ||
                                                    record.useCount === undefined
                                                    ? record.useCount
                                                    : record.useCount <=
                                                        record.availableCount
                                                        ? record.useCount
                                                        : record.availableCount
                                            }
                                            onChange={(e) =>
                                                otherHandleChange(
                                                    { useCount: e },
                                                    record
                                                )
                                            }
                                        />
                                    )}
                                />
                            </Table>
                        </div>
                    }
                </Spin>
            </Modal>
        </React.Fragment >
    );
};

interface DrugCacheTableProp {
    mode: any;
    dataSource: any[];
    rowSelection: any;
    selectIds: any[];
    packageIsOpen: any;
    onCountChange: (v: any, r: any) => void;
}
const DrugCacheTable = (props: DrugCacheTableProp) => {
    return (
        <PageContextProvider>
            <DrugTable {...props}></DrugTable>
        </PageContextProvider>
    );
};

interface DrugGroupCacheTableProp {
    dataSource: any[];
    packageIsOpen: any;
    onCountChange: (v: any, r: any) => void;
}
const DrugGroupCacheTable = (props: DrugGroupCacheTableProp) => {
    return (
        <PageContextProvider>
            <DrugGroupTable {...props}></DrugGroupTable>
        </PageContextProvider>
    );
};

const render_availableCount = (record: any) => {
    return (
        <div>
            {record.availableCount < record.count ? (
                <span style={{ color: "red" }}> {record.availableCount}</span>
            ) : (
                record.availableCount
            )}
        </div>
    );
};

const render_drugName = (record: any) => {
    var showMessage = (!record.isOpen && (record.message !== undefined && record.message !== ""))
    return showMessage ? (
        <div>
            {" "}
            {record.name}
            <br />
            <span
                style={{
                    color: "red",
                    fontSize: "12px",
                    width: "300px",
                    whiteSpace: "pre-line",
                }}
            >
                {record.message}
            </span>
        </div>
    ) : (
        <div
            style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'normal',
                wordBreak: 'break-word',
            }}
        >
            {record.name}
        </div>
    )



};

function renderPackageMethod(packageMethod: string) {
    return packageMethod === "true" ? (
        <FormattedMessage id="shipment.order.packageMethod.package" />
    ) : (
        <FormattedMessage id="shipment.order.packageMethod.single" />
    );
}

const DrugTable = (props: DrugCacheTableProp) => {
    var tableData = useCacheTable({ dataSource: props.dataSource });
    tableData = fillTableCellEmptyPlaceholder(tableData)
    const intl = useTranslation();
    return (
        <>
            <Table
                className="mar-top-5"
                pagination={false}
                rowKey={(record: any) => record._id}
                dataSource={tableData}
                rowSelection={props.rowSelection}
                scroll={{ y: 170 }}
            >
                <Table.Column
                    title={intl.formatMessage({
                        id: "drug.configure.drugName",
                    })}
                    dataIndex="name"
                    key="name"
                    ellipsis
                    render={(value, record, index) =>
                        <div style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'normal',
                            wordBreak: 'break-word',
                        }}>
                            {value}
                        </div>
                    }
                />
                <Table.Column
                    width={120}
                    title={intl.formatMessage({ id: "drug.list.expireDate" })}
                    dataIndex="expirationDate"
                    key="expirationDate"
                    ellipsis
                />
                <Table.Column
                    width={140}
                    title={intl.formatMessage({ id: "drug.list.batch" })}
                    dataIndex="batchNumber"
                    key="batchNumber"
                    ellipsis
                />
                <Table.Column
                    width={90}
                    title={intl.formatMessage({
                        id: "shipment.order.availableCount",
                    })}
                    dataIndex="availableCount"
                    key="availableCount"
                    ellipsis
                />
                {props.packageIsOpen && (
                    <Table.Column
                        width={110}
                        title={intl.formatMessage({
                            id: "shipment.order.package.method",
                        })}
                        dataIndex="packageMethod"
                        key="packageMethod"
                        ellipsis
                        render={(value, record, index) =>
                            renderPackageMethod(value)
                        }
                    />
                )}
                {props.mode !== 6 &&
                    <Table.Column
                        width={120}
                        title={intl.formatMessage({ id: "drug.freeze.count" })}
                        dataIndex="count"
                        key="count"
                        ellipsis
                        render={(value, record: any, index) => (
                            <InputNumber
                                className="full-width"
                                placeholder={intl.formatMessage({
                                    id: "placeholder.input.common",
                                })}
                                precision={0}
                                min={0}
                                step={1}
                                max={record.availableCount}
                                value={record.count}
                                onChange={(e) =>
                                    props.onCountChange({ count: e }, record)
                                }
                            />
                        )}
                    />
                }
            </Table >
            <PaginationView
                mode="SELECTABLE"
                selectedNumber={props.selectIds.length}
            ></PaginationView>
        </>
    );
};

function renderCol(record: any, intl: any, props: any) {
    return record.isOpen ? (
        <InputNumber
            className="full-width"
            placeholder={intl.formatMessage({ id: "placeholder.input.common" })}
            precision={0}
            min={0}
            step={1}
            max={record.availableCount}
            value={record.count}
            onChange={(e) => props.onCountChange({ count: e }, record)}
        />
    ) : (
        record.count
    );
}

const DrugGroupTable = (props: DrugGroupCacheTableProp) => {
    var tableData = useCacheTable({ dataSource: props.dataSource });
    tableData = fillTableCellEmptyPlaceholder(tableData)
    const intl = useTranslation();
    return (
        <>
            <Table
                className="mar-top-5"
                pagination={false}
                rowKey={(record) => record._id}
                dataSource={tableData}
                scroll={{ y: 170 }}
            >
                <Table.Column
                    title={<FormattedMessage id="drug.configure.drugName" />}
                    dataIndex={"name"}
                    key="name"
                    ellipsis
                    render={(value, record, index) => render_drugName(record)}
                />
                <Table.Column
                    width={120}
                    title={<FormattedMessage id="drug.list.expireDate" />}
                    dataIndex={"expirationDate"}
                    key="expirationDate"
                    ellipsis
                />
                <Table.Column
                    width={140}
                    title={<FormattedMessage id="drug.list.batch" />}
                    dataIndex={"batchNumber"}
                    key="batchNumber"
                    ellipsis
                />
                <Table.Column
                    width={140}
                    title={
                        <FormattedMessage id="shipment.order.availableCount" />
                    }
                    dataIndex={"availableCount"}
                    key="availableCount"
                    ellipsis
                    render={(value, record, index) =>
                        render_availableCount(record)
                    }
                />
                {props.packageIsOpen && (
                    <Table.Column
                        width={110}
                        title={
                            <FormattedMessage id="shipment.order.package.method" />
                        }
                        dataIndex={"packageMethod"}
                        key="packageMethod"
                        ellipsis
                        render={(value, record, index) =>
                            renderPackageMethod(value)
                        }
                    />
                )}

                {/* <Table.Column
                    width={150}
                    title={<FormattedMessage id="drug.freeze.count" />}
                    dataIndex={"count"}
                    key="count"
                    ellipsis
                /> */}
                <Table.Column
                    width={120}
                    title={<FormattedMessage id="drug.freeze.count" />}
                    dataIndex={"count"}
                    key="count"
                    ellipsis
                    render={(value, record: any, index) =>
                        renderCol(record, intl, props)
                    }
                />
            </Table>
            <PaginationView></PaginationView>
        </>
    );
};



// 设置研究产品标签选值
const CustomForm = styled(Form)`
  .ant-form-item-label label {
    text-align: right;
  }
`;