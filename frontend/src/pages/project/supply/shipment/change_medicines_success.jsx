import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button, Form, Row, Space, Popover, Table, Modal } from "antd";
import Barcode from "react-barcode";
import { useSafeState } from "ahooks";
import { ChangeRecords } from "./change_records";
import * as _ from "lodash";
import { combineRow } from "../../../../utils/merge_cell";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";


export const ChangeMedicinesSuccess = (props) => {
    const intl = useIntl();
    const { formatMessage } = intl;
    const [visible, setVisible] = useSafeState(false);
    const [tableData, setTableData] = useSafeState([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [codeRule, setCodeRule] = useSafeState(null);
    const [orderId, setOrderId] = useSafeState(null);
    const [orderNumber, setOrderNumber] = useSafeState("");
    const [form] = Form.useForm();
    const change_records_ref = React.useRef();

    const show = (id, orderNumber, packageIsOpen, codeRule, data) => {
        setVisible(true);
        setOrderId(id);
        setOrderNumber(orderNumber);
        setPackageIsOpen(packageIsOpen);
        setCodeRule(codeRule);
        if (packageIsOpen) {
            var tableData = combineRow(data, "package_number", "package_number", false)
            tableData = combineRow(data, "name", "name", false)
            setTableData(tableData ? fillTableCellEmptyPlaceholder(tableData) : []);
        } else {
            setTableData(data ? fillTableCellEmptyPlaceholder(data) : []);
        }
    };

    const changeRecords = () => {
        change_records_ref.current.show(orderId, orderNumber, packageIsOpen, codeRule, tableData);
    }


    React.useImperativeHandle(props.bind, () => ({ show }));


    const hide = () => {
        setVisible(false);
        setTableData([]);
        setOrderId(null);
        setOrderNumber("");
        setPackageIsOpen(false);
        form.resetFields();
        props.refresh();
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                //className="custom-large-modal"
                width={"80%"}
                title={<>{formatMessage({ id: "shipment.order.mediciens.changeRecords" })}-<span style={{ fontWeight: 400 }}>{orderNumber}</span></>}
                open={visible}
                onCancel={hide}
                maskClosable={false}
                bodyStyle={{ padding: 24 }}
                footer={
                    <Row justify={"end"}>
                        <Space>
                            <Button
                                onClick={() => {
                                    hide();
                                }}
                            >
                                {formatMessage({ id: "common.close" })}
                            </Button>
                            <Button type={"primary"} onClick={changeRecords}>
                                {formatMessage({ id: "shipment.order.medicines.change.records" })}
                            </Button>
                        </Space>
                    </Row>
                }
            >
                <div style={{ textAlign: "center" }}>
                    <svg className="iconfont" width={52} height={52} fill={"#165DFF"}>
                        <use xlinkHref="#icon-chenggong" ></use>
                    </svg>
                    <br></br>
                    <span style={{
                        color: "#1D2129",
                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                        fontSize: "20px",
                        fontStyle: "normal",
                        fontWeight: 500,
                        lineHeight: "normal",
                    }}>{formatMessage({ id: "shipment.order.change.success" })}</span>
                </div>
                <div
                    id="showErrorId"
                    style={{
                        marginBottom: "12px",
                        display: "flex",
                        alignItems: "center",
                    }}
                >
                    <span>{formatMessage({ id: "shipment.order.medicine.change.success.info" })} </span>
                </div>

                {tableData != null && tableData.length > 0 &&
                    <>
                        <Table
                            size="large"
                            dataSource={tableData}
                            pagination={false}
                            scroll={{ x: 450 }}
                            rowKey={(record) => (record._id)}
                        >
                            {packageIsOpen && <Table.Column
                                title={formatMessage({ id: 'drug.medicine.packlist' })}
                                dataIndex="package_number"
                                key="package_number"
                                ellipsis
                                width={125}
                                render={(value, record, index) => {
                                    let newValue = ""
                                    if (value !== undefined && value !== "") {
                                        newValue = value
                                    } else {
                                        newValue = "-"
                                    }
                                    let obj = {
                                        children: newValue,
                                        props: { rowSpan: tableData[index].package_numberRowSpan }
                                    }
                                    return obj
                                }}
                            />}
                            {packageIsOpen ?
                                <Table.Column
                                    title={formatMessage({ id: 'drug.configure.drugName' })}
                                    dataIndex="name"
                                    key="name"
                                    ellipsis
                                    width={175}
                                    onCell={(_, index) => {
                                        return { rowSpan: tableData[index].nameRowSpan }
                                    }} />
                                :
                                <Table.Column
                                    title={formatMessage({ id: 'drug.configure.drugName' })}
                                    dataIndex="name"
                                    key="name"
                                    ellipsis
                                    width={175}
                                />
                            }

                            <Table.Column
                                title={formatMessage({ id: 'drug.list.drugNumber' })}
                                dataIndex="number"
                                key="number"
                                ellipsis
                                width={130}
                            />
                            <Table.Column
                                title={formatMessage({ id: 'drug.list.expireDate' })}
                                dataIndex="expiration_date"
                                key="expiration_date"
                                ellipsis
                                width={120}
                            />
                            <Table.Column
                                title={formatMessage({ id: 'drug.list.batch' })}
                                dataIndex="batch_number"
                                key="batch_number"
                                width={120}
                                ellipsis
                            />
                            <Table.Column
                                title={<FormattedMessage id="drug.freeze.count" />}
                                dataIndex="change_count"
                                key="change_count"
                                ellipsis
                                render={(value, record, index) => {
                                    let newValue = ""
                                    if (value !== undefined && value !== "") {
                                        newValue = value
                                    } else {
                                        newValue = "-"
                                    }
                                    return newValue
                                }}
                            />
                            {codeRule &&
                                <>
                                    <Table.Column title={<FormattedMessage id="barcode" />} key="number"
                                        dataIndex="number" align="center" ellipsis
                                        render={(value, record, index) => {
                                            return (
                                                <Popover content={
                                                    <>
                                                        <Barcode value={value} displayValue={false} height={60} width={1}
                                                            format="CODE128" />
                                                        <br />
                                                        <span>&nbsp;&nbsp;{value}</span>
                                                    </>
                                                } trigger="click">
                                                    {
                                                        record.short_code !== "" && record.short_code !== "-" ?
                                                            <Button type={"link"} size={"small"}>
                                                                <FormattedMessage id="form.preview" />
                                                            </Button>
                                                            :
                                                            "-"
                                                    }
                                                </Popover>
                                            )
                                        }} />
                                    <Table.Column title={<FormattedMessage id="packageBarcode" />} key="package_number"
                                        dataIndex="package_number" align="center" ellipsis
                                        render={(value, record, index) => {
                                            return (
                                                <Popover content={
                                                    <>
                                                        <Barcode value={value} displayValue={false} height={60} width={1}
                                                            format="CODE128" />
                                                        <br />
                                                        <span>&nbsp;&nbsp;{value}</span>
                                                    </>
                                                } trigger="click">
                                                    {
                                                        record.short_code !== "" && record.short_code !== "-" && record.package_number !== "-" && record.package_number !== "" ?
                                                            <Button type={"link"} size={"small"}>
                                                                <FormattedMessage id="form.preview" />
                                                            </Button>
                                                            :
                                                            "-"
                                                    }
                                                </Popover>
                                            )
                                        }} />
                                </>
                            }
                        </Table>
                    </>
                }
            </Modal>
            <ChangeRecords bind={change_records_ref} />
        </React.Fragment >
    )
};


