import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button, Form, Popover, Table, Modal, Drawer } from "antd";
import Barcode from "react-barcode";
import { useSafeState } from "ahooks";
import moment from "moment";
import { getChangeOrderRecord } from "../../../../api/order";
import { useFetch } from "../../../../hooks/request";
import { useAuth } from "../../../../context/auth";
import { combineRow } from "../../../../utils/merge_cell";
import { nilObjectId } from "../../../../data/data";

export const ChangeRecords = (props) => {
    const intl = useIntl();
    const { formatMessage } = intl;
    const [visible, setVisible] = useSafeState(false);
    const [tableData, setTableData] = useSafeState([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [codeRule, setCodeRule] = useSafeState(null);
    const [orderId, setOrderId] = useSafeState(null);
    const [orderNumber, setOrderNumber] = useSafeState("");
    const [form] = Form.useForm();
    const auth = useAuth()
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
    const { runAsync: run_getChangeOrderRecords } = useFetch(getChangeOrderRecord, { manual: true })

    const show = (id, orderNumber, packageIsOpen, codeRule) => {
        setVisible(true);
        setOrderId(id);
        setOrderNumber(orderNumber);
        setPackageIsOpen(packageIsOpen);
        setCodeRule(codeRule);
        getChangeRecords(id, packageIsOpen);
    };

    const getChangeRecords = (orderId, packageIsOpen) => {
        if (orderId != null) {
            run_getChangeOrderRecords({
                id: orderId,
                roleId: auth.project.permissions.role_id,
            }).then(
                (result) => {
                    const data = result.data
                    if (data != null) {
                        data.forEach(it => {
                            if (it.new_medicine_id !== undefined && it.new_medicine_id !== null && it.new_medicine_id !== "" && it.new_medicine_id !== nilObjectId) {
                                var newMedicine = it.newMedicine
                                it.new_name = newMedicine.name
                                it.new_number = newMedicine.number
                                it.new_batch_number = newMedicine.batch_number
                                it.new_expiration_date = newMedicine.expiration_date
                                it.new_package_number = newMedicine.package_number
                                it.new_short_code = newMedicine.short_code
                                it.packageDrug = newMedicine.packageDrug
                            }
                            if (it.old_medicine_id !== undefined && it.old_medicine_id !== null && it.old_medicine_id !== "" && it.old_medicine_id !== nilObjectId) {
                                var oldMedicine = it.oldMedicine
                                it.old_name = oldMedicine.name
                                it.old_number = oldMedicine.number
                                it.old_batch_number = oldMedicine.batch_number
                                it.old_expiration_date = oldMedicine.expiration_date
                                it.old_package_number = oldMedicine.package_number
                                it.old_short_code = oldMedicine.short_code
                                it.packageDrug = oldMedicine.packageDrug
                            }
                        });
                        if (packageIsOpen) {
                            var medicineData = combineRow(data, "old_package_number", "old_package_number", true)
                            medicineData = combineRow(data, "old_name", "old_name", false)
                            setTableData(medicineData);
                        } else {
                            setTableData(data);
                        }

                    } else {
                        setTableData([]);
                    }
                }
            )
        }
    }


    React.useImperativeHandle(props.bind, () => ({ show }));


    const hide = () => {
        setVisible(false);
        setTableData([]);
        setOrderId(null);
        setOrderNumber("");
        form.resetFields();
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Drawer
                width={"100%"}
                title={<>{formatMessage({ id: "shipment.order.medicines.change.records" })}-<span style={{ fontWeight: 400 }}>{orderNumber}</span></>}
                open={visible}
                onClose={hide}
                footer={null}
            >
                <>
                    <Table
                        size="large"
                        dataSource={tableData}
                        pagination={false}
                        scroll={{ x: 450 }}
                        rowKey={(record) => (record._id)}
                        bordered={0.5}
                    >
                        <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="index" key="index" width={70}
                            onCell={(_, index) => {
                                return { rowSpan: tableData[index].old_package_numberRowSpan }
                            }}
                            render={(text, record, index) => packageIsOpen ? record.index : index + 1} />
                        <Table.Column title={formatMessage({ id: 'shipment.order.old.medicine' })} dataIndex="old" key="old"
                            children={<>
                                {packageIsOpen && <Table.Column
                                    title={formatMessage({ id: 'drug.medicine.packlist' })}
                                    dataIndex="old_package_number"
                                    key="old_package_number"
                                    ellipsis
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        let obj = {
                                            children: newValue,
                                            props: { rowSpan: tableData[index].old_package_numberRowSpan }
                                        }
                                        return obj
                                    }}
                                />}
                                {packageIsOpen ?
                                    <Table.Column
                                        title={formatMessage({ id: 'drug.configure.drugName' })}
                                        dataIndex="old_name"
                                        key="old_name"
                                        ellipsis
                                        onCell={(_, index) => {
                                            return { rowSpan: tableData[index].nameRowSpan }
                                        }} />
                                    :
                                    <Table.Column
                                        title={formatMessage({ id: 'drug.configure.drugName' })}
                                        dataIndex="old_name"
                                        key="old_name"
                                        ellipsis
                                    />
                                }

                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.drugNumber' })}
                                    dataIndex="old_number"
                                    key="old_number"
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        return newValue
                                    }}
                                />
                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.expireDate' })}
                                    dataIndex="old_expiration_date"
                                    key="old_expiration_date"
                                    ellipsis
                                />
                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.batch' })}
                                    dataIndex="old_batch_number"
                                    key="old_batch_number"
                                    ellipsis
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.freeze.count" />}
                                    dataIndex="old_count"
                                    key="old_count"
                                    ellipsis
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "" && value !== 0) {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        return newValue
                                    }}
                                />
                                {codeRule &&
                                    <>
                                        <Table.Column title={<FormattedMessage id="barcode" />} key="old_number"
                                            dataIndex="old_number" align="center" ellipsis
                                            render={(value, record, index) => {
                                                return (
                                                    <Popover content={
                                                        <>
                                                            <Barcode value={value} displayValue={false} height={60} width={1}
                                                                format="CODE128" />
                                                            <br />
                                                            <span>&nbsp;&nbsp;{value}</span>
                                                        </>
                                                    } trigger="click">
                                                        {
                                                            record.old_short_code !== "" ?
                                                                <Button size="small"
                                                                    type="link">
                                                                    <FormattedMessage id="form.preview" />
                                                                </Button>
                                                                :
                                                                "-"
                                                        }
                                                    </Popover>
                                                )
                                            }} />
                                        <Table.Column title={<FormattedMessage id="packageBarcode" />} key="old_package_number"
                                            dataIndex="old_package_number" align="center" ellipsis
                                            render={(value, record, index) => {
                                                return (
                                                    <Popover content={
                                                        <>
                                                            <Barcode value={value} displayValue={false} height={60} width={1}
                                                                format="CODE128" />
                                                            <br />
                                                            <span>&nbsp;&nbsp;{value}</span>
                                                        </>
                                                    } trigger="click">
                                                        {
                                                            record.old_short_code !== "" && record.old_package_number !== "" ?
                                                                <Button size="small"
                                                                    type="link">
                                                                    <FormattedMessage id="form.preview" />
                                                                </Button>
                                                                :
                                                                "-"
                                                        }
                                                    </Popover>
                                                )
                                            }} />
                                    </>
                                }
                            </>
                            }
                        />
                        <Table.Column title={formatMessage({ id: 'shipment.order.new.medicine' })} dataIndex="new" key="new"
                            children={<>
                                {packageIsOpen && <Table.Column
                                    title={formatMessage({ id: 'drug.medicine.packlist' })}
                                    dataIndex="new_package_number"
                                    key="new_package_number"
                                    ellipsis
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        let obj = {
                                            children: newValue,
                                            props: { rowSpan: tableData[index].old_package_numberRowSpan }
                                        }
                                        return obj
                                    }}
                                />}
                                {packageIsOpen ?
                                    <Table.Column
                                        title={formatMessage({ id: 'drug.configure.drugName' })}
                                        dataIndex="new_name"
                                        key="new_name"
                                        ellipsis
                                        onCell={(_, index) => {
                                            return { rowSpan: tableData[index].nameRowSpan }
                                        }} />
                                    :
                                    <Table.Column
                                        title={formatMessage({ id: 'drug.configure.drugName' })}
                                        dataIndex="new_name"
                                        key="new_name"
                                        ellipsis
                                    />
                                }

                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.drugNumber' })}
                                    dataIndex="new_number"
                                    key="new_number"
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        return newValue
                                    }}
                                />
                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.expireDate' })}
                                    dataIndex="new_expiration_date"
                                    key="new_expiration_date"
                                    ellipsis
                                />
                                <Table.Column
                                    title={formatMessage({ id: 'drug.list.batch' })}
                                    dataIndex="new_batch_number"
                                    key="new_batch_number"
                                    ellipsis
                                />
                                <Table.Column
                                    title={<FormattedMessage id="drug.freeze.count" />}
                                    dataIndex="new_count"
                                    key="new_count"
                                    ellipsis
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "" && value !== 0) {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        return newValue
                                    }}
                                />
                                {codeRule &&
                                    <>
                                        <Table.Column title={<FormattedMessage id="barcode" />} key="new_number"
                                            dataIndex="new_number" align="center" ellipsis
                                            render={(value, record, index) => {
                                                return (
                                                    <Popover content={
                                                        <>
                                                            <Barcode value={value} displayValue={false} height={60} width={1}
                                                                format="CODE128" />
                                                            <br />
                                                            <span>&nbsp;&nbsp;{value}</span>
                                                        </>
                                                    } trigger="click">
                                                        {
                                                            record.new_short_code !== null && record.new_short_code !== "" ?
                                                                <Button size="small"
                                                                    type="link">
                                                                    <FormattedMessage id="form.preview" />
                                                                </Button>
                                                                :
                                                                "-"
                                                        }
                                                    </Popover>
                                                )
                                            }} />
                                        <Table.Column title={<FormattedMessage id="packageBarcode" />} key="new_package_number"
                                            dataIndex="new_package_number" align="center" ellipsis
                                            render={(value, record, index) => {
                                                return (
                                                    <Popover content={
                                                        <>
                                                            <Barcode value={value} displayValue={false} height={60} width={1}
                                                                format="CODE128" />
                                                            <br />
                                                            <span>&nbsp;&nbsp;{value}</span>
                                                        </>
                                                    } trigger="click">
                                                        {
                                                            record.new_short_code !== "" && record.new_package_number !== "" ?
                                                                <Button size="small"
                                                                    type="link">
                                                                    <FormattedMessage id="form.preview" />
                                                                </Button>
                                                                :
                                                                "-"
                                                        }
                                                    </Popover>
                                                )
                                            }} />
                                    </>
                                }
                            </>
                            }
                        />

                        <Table.Column title={<FormattedMessage id="shipment.order.change.operTime" />} key={["meta", "created_at"]}
                            dataIndex={["meta", "created_at"]} render={
                                (value, record, index) => (
                                    (value === null || value === 0) ? '' : (moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss') + (timeZone >= 0 ? "(UTC+" : "(UTC") + timeZone + ")")
                                )
                            } />
                        <Table.Column title={<FormattedMessage id="common.operator" />}
                            dataIndex={["meta", "createdUser", [0], "info"]}
                            key={["meta", "createdUser", [0], "info"]}
                            render={
                                (value, record, index) => (
                                    value.name + "(" + value.unicode + ")"
                                )
                            } />
                    </Table>
                </>
            </Drawer>
        </React.Fragment >
    )
};


