import { Button, DatePicker, Form, Input, message, Modal, Popover, Radio, Row, Select, Tabs, Timeline } from "antd";
import { permissions } from "../../../../tools/permission";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { useGlobal } from "../../../../context/global";
import React, { useImperativeHandle, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import copy from "copy-to-clipboard";
import { useFetch } from "../../../../hooks/request";
import { getCompanyCode, getLogistics, getLogisticsCompanyCode, updateOrderStatus } from "../../../../api/order";
import { getKeyHistoryList } from "../../../../api/history";
import dayjs from "dayjs";
import moment from "moment";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";

export const ShipDetail = React.forwardRef((_, ref) => {
  const auth = useAuth();
  const [form] = Form.useForm();
  const g = useGlobal();
  const formItemLayout = {
    labelCol: { style: { width: g.lang === "en" ? "160px" : "100px" } },
  };
  const intl = useIntl(); const { formatMessage } = intl;
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState({});
  const [id, setId] = useState(null);
  const [otherLogistics, setOtherLogistics] = useState(false);
  const [type, setType] = useState(0);
  const [disabled, setDisabled] = useState(false);
  const [firstForm, setFirstForm] = useState({});
  const [tagStyle, setTagStyle] = useState({ backgroundColor: "#F4F4F5", color: "#1D2129" });
  const [activeKey, setActiveKey] = useState("contact");
  const [logistics, setLogistics] = useSafeState([]);
  const [logisticsDetails, setLogisticsDetails] = useSafeState([]);
  const [open, setOpen] = useState(false);
  const [iconColor, setIconColor] = useSafeState(0);
  const [orderNumber, setOrderNumber] = useSafeState(null);
  const [value, setValue] = useState(undefined);
  const [actualReceiptTime, setActualReceiptTime] = useSafeState(null);
  const DatePickers = DatePicker;
  const [isEdit, setIsEdit] = useState(false);
  const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
  useImperativeHandle(ref, () => ({ show }));

  const show = (record, type) => {
    setActualReceiptTime(null);
    if (permissions(auth.project.permissions, "operation.supply.shipment.contacts")) {
      setActiveKey("contact")
    } else if (permissions(auth.project.permissions, "operation.supply.shipment.logistics.view")) {
      setActiveKey("logistics")
    } else if ((record.status === 4 || record.status === 5 || record.status === 8 || record.status === 9) && permissions(auth.project.permissions, "operation.supply.shipment.reason")) {
      setActiveKey("reason")
    }
    setRecord(record);
    setOrderNumber(record.order_number);
    setVisible(true);
    setId(record.id ? record.id : record._id);
    setType(type);
    form.setFieldsValue({ isTransit: 2, logistics: record.logistics_info?.logistics, other: record.logistics_info?.other, number: record.logistics_info?.number })
    setFirstForm({ isTransit: 2, logistics: record.logistics_info?.logistics, other: record.logistics_info?.other, number: record.logistics_info?.number })
    if (form.getFieldValue("logistics") === 9) {
      setOtherLogistics(true)
    }
    if (form.getFieldValue("logistics") === 0) {
      form.setFieldsValue({ ...form.getFieldsValue, logistics: null })
    }
    if (record.actual_receipt_time !== undefined && record.actual_receipt_time !== "") {
      form.setFieldValue("actualRecepitTime", record.actual_receipt_time)
      setActualReceiptTime(record.actual_receipt_time)
    }
    if (type === 1) {
      setDisabled(true);
    }

    if (record.logistics_info?.logistics === "") {
      form.setFieldValue("logistics", null)
    }
    run_getCompanyCode({ code: record.logistics_info?.logistics }).then(
      (res) => {
        setLogistics(
          res.data?.map((item) => ({
            label: item.name,
            value: item.code,
          })) || []
        );
        if (res.data.length === 1 && res.data[0].code === "qita") {
          setOtherLogistics(true)
        } else {
          setOtherLogistics(false)
        }
      }
    );


    run_getKeyHistoryList({
      key: "history.order",
      oid: record._id,
    }).then(
      (result) => {
        const data = result.data
        setIsEdit(data);
      }
    );

  };


  const handleSearch = (newValue) => {
    run_getLogisticsCompanyCode({ name: newValue }).then(
      (res) => {
        setLogistics(
          res.data?.map((item) => ({
            label: item.name,
            value: item.code,
          })) || []
        );
        // if(res.data.length === 1 && res.data[0].code === "qita"){
        //     setOtherLogistics(true)
        // }else{
        //     setOtherLogistics(false)
        // }
      }
    );
  };

  const handleOpenChange = (newOpen) => {
    if (newOpen != null) {
      if (newOpen === true) {
        setIconColor(1)
      } else {
        setIconColor(0)
      }
    }
    // if(form.getFieldValue("logistics") === "shunfeng"){
    //     window.open("https://www.sf-express.com/chn/sc/waybill/list", '_blank');
    //     return;
    // }else if(form.getFieldValue("logistics") === "shunfengkuaiyun"){
    //     window.open("https://www.sf-freight.com/yundanzhuizong.html", '_blank');
    //     return;
    // }else if(form.getFieldValue("logistics") === "fengwang"){
    //     window.open("https://www.51tracking.com/fwx-network-tracking", '_blank');
    //     return;
    // }
    let code = "";
    run_getLogistics(
      {
        com: form.getFieldValue("logistics"),
        num: form.getFieldValue("number").trim(),
        // phone: phone,//顺丰相关都要手机号
      }).then((res) => {
        if (res.data.message !== "ok") {
          message.warn(res.data.message);
          setOpen(false);
          return;
        } else {
          setLogisticsDetails(
            (res.data && res.data.data)?.map((item) => {
              const logisticsItem = {
                areaCenter: item.areaCenter,
                areaCode: item.areaCode,
                areaName: item.areaName,
                areaPinYin: item.areaPinYin,
                context: item.context,
                ftime: item.statusCode,
                location: item.location,
                time: item.time,
                status: item.status,
                statusCode: item.statusCode,
              };

              if (code === item.statusCode) {
                logisticsItem.status = "";
                logisticsItem.statusCode = "";
              }

              code = item.statusCode;

              return logisticsItem;
            }) || []
          );
          setOpen(newOpen);
        }
      }
      );
  };

  const handleMouseEnter = () => {
    setIconColor(1)
  };

  const handleMouseLeave = () => {
    setIconColor(0)
  };


  const handleChange = (newValue) => {
    if (newValue === "qita") {
      setOtherLogistics(true)
    } else {
      setOtherLogistics(false)
    }
    setValue(newValue);
  };

  const showReasonViewModal = (id, status, reason) => {
    let title = ""
    if (status) {
      if (status === 5) {//取消
        title = <FormattedMessage id="shipment.cancel.order" />;
      } else if (status === 4) {//丢失订单
        title = <FormattedMessage id="shipment.lose.order" />;
      } else if (status === 9) {//关闭订单
        title = <FormattedMessage id="shipment.close.order" />;
      } else if (status === 8) {//终止订单
        title = <FormattedMessage id="shipment.end.order" />;
      }
    }
    return title
    // CustomInfoModal({
    //     title: title,
    //     content: reason,
    //     centered: true,
    // })
  };

  const copyNumber = () => {
    copy(form.getFieldValue("number"))
    message.success(<FormattedMessage id="common.copy.ok" />)
  };

  const { runAsync: run_updateOrderStatus } = useFetch(updateOrderStatus, { manual: true })
  const { runAsync: run_getLogistics } = useFetch(getLogistics, { manual: true })
  const { runAsync: run_getLogisticsCompanyCode } = useFetch(getLogisticsCompanyCode, { manual: true })
  const { runAsync: run_getCompanyCode } = useFetch(getCompanyCode, { manual: true })
  const { runAsync: run_getKeyHistoryList } = useFetch(getKeyHistoryList, { manual: true })

  const save = () => {
    form.validateFields().then(
      value => {
        let actualReceiptTime = value.actualReceiptTime ? moment(value.actualReceiptTime).format("YYYY-MM-DD HH:mm:ss") : "";
        run_updateOrderStatus({
          id: id,
          status: value.isTransit,
          roleId: auth.project.permissions.role_id,
          logisticsInfo: { ...value },
          actualReceiptTime: actualReceiptTime,
        }).then((resp) => {
          message.success(resp.msg).then()
          cancel()
          setVisible(false)
          _.refresh()
        });
        form.setFieldValue(value)
      }
    )
  };

  const cancel = () => {
    if (firstForm.logistics === 9) {
      setOtherLogistics(true)
    } else {
      setOtherLogistics(false)
    }
    form.setFieldsValue({ ...firstForm })
    setDisabled(false)
  }


  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  const disabledDateTime = (current) => {
    // 获取今天的日期
    const today = moment().startOf("day");
    const todayHour = moment().hour();
    const todayMinute = moment().minutes();
    // 将当前日期转换为 moment 对象
    const currentDate = moment(current).startOf("day");
    const currentHour = moment(current).hour();
    //const currentMinute = moment(current).minutes();
    return {
      disabledHours: () => {
        if (currentDate.isSame(today)) {
          return range(0, 24).splice(todayHour + 1, 20);
        }
      },
      disabledMinutes: () => {
        if (currentDate.isSame(today) && currentHour === todayHour) {
          return range(todayMinute + 1, 60);
        }
      }
    };
  };


  const disabledDate = (current) => {
    // 获取今天的日期
    const today = moment().startOf("day");
    // 将当前日期转换为 moment 对象
    const currentDate = moment(current).startOf("day");

    return (
      currentDate.isAfter(today) || current > dayjs()
    );

  };

  return (
    <>
      <Modal
        title={formatMessage({ id: "common.detail" }) + " - " + orderNumber}
        open={visible}
        centered
        onCancel={() => {
          setOpen(false)
          setTimeout(() => {
            setVisible(false)
            setDisabled(false)
          }, 0)

        }}
        maskClosable={false}
        footer={activeKey === "logistics"
          ? (<Row justify="end">
            {
              disabled && <>
                <Button onClick={cancel}>{<FormattedMessage id="common.cancel" />}</Button>
                <Button onClick={save} type={"primary"}>{<FormattedMessage id="common.ok" />}</Button>
              </>
            }
            {permissions(auth.project.permissions, "operation.supply.shipment.logistics.edit")
              && ((record.status !== 1 && record.status !== 5 && record.status !== 6 && record.status !== 7 && record.status !== 9 && record.status !== 4) || (isEdit && record.status === 4))
              && !disabled
              && <Button onClick={() => {
                setDisabled(!disabled)
                if (record.actual_receipt_time !== undefined && record.actual_receipt_time !== "") {
                  // 将字符串日期解析为Date对象
                  // var parsedDate = moment(record.actual_receipt_time, 'YYYY-MM-DD HH:mm:ss');
                  form.setFieldValue("actualReceiptTime", record.actual_receipt_time)
                } else {
                  form.setFieldValue("actualReceiptTime", null)
                }
                let logistics = form.getFieldValue("logistics") !== 0 ? form.getFieldValue("logistics") : null;
                form.setFieldsValue({ ...firstForm, logistics });

              }}>{<FormattedMessage id="common.modify" />}</Button>}
          </Row>)
          : false
        }
      >
        <Tabs
          destroyInactiveTabPane={true}
          size="small"
          activeKey={activeKey}
          tabPosition="top"
          onTabClick={(key) => setActiveKey(key)}
        >
          {permissions(auth.project.permissions, "operation.supply.shipment.contacts") &&
            <Tabs.TabPane tab={<FormattedMessage id="common.contacts" />} key="contact">
              <Form layout="horizontal" labelCol={{ style: { width: "70px" } }} style={{ width: 350, minHeight: 180 }}>
                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="common.contacts" /></span>}>
                  {record.contacts || "-"}
                </Form.Item>
                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="projects.contact.information" /></span>}>
                  {record.phone || "-"}
                </Form.Item>
                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="common.email" /></span>}>
                  {record.email || "-"}
                </Form.Item>
                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="common.address" /></span>}>
                  {record.address || "-"}
                </Form.Item>
              </Form>
            </Tabs.TabPane>
          }
          {permissions(auth.project.permissions, "operation.supply.shipment.logistics.view") &&
            <Tabs.TabPane tab={<FormattedMessage id="logistics.info" />} key="logistics">
              <Form form={form} {...formItemLayout}>
                {
                  type === 1 &&
                  <Form.Item label={<FormattedMessage id="logistics.info.isTransit" />} name="isTransit" >
                    <Radio.Group >
                      <Radio value={2}>{<FormattedMessage id="common.yes" />}</Radio>
                      <Radio style={{ marginLeft: 16 }} value={0}>{<FormattedMessage id="common.no" />}</Radio>
                    </Radio.Group>
                  </Form.Item>
                }

                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="logistics.confirm.info" /></span>} name="logistics">
                  {disabled
                    ?
                    <Select
                      showSearch
                      value={value}
                      placeholder={<FormattedMessage id="common.required.prefix.search" />}
                      // style={props.style}
                      defaultActiveFirstOption={false}
                      showArrow={false}
                      filterOption={false}
                      onSearch={handleSearch}
                      onChange={handleChange}
                      notFoundContent={null}
                    // options={(logistics || []).map((d) => ({
                    //     value: d.value,
                    //     label: d.code,
                    // }))}
                    >
                      {(logistics || []).map(v => <Select.Option key={v.value} value={v.value}>{v.label}</Select.Option>)}
                    </Select>
                    // <Select onChange={selectOption} placeholder={<FormattedMessage id="placeholder.select.common" />}>
                    //   {logistics.map(v => <Select.Option key={v.value} value={v.value}>{v.label}</Select.Option>)}
                    // </Select>
                    : <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span>{logistics.find((v) => v.value === form.getFieldValue("logistics")) && form.getFieldValue("logistics") !== ""
                        ?
                        logistics.find((v) => v.value === form.getFieldValue("logistics"))?.label :
                        "-"
                      }</span>
                    </div>
                  }
                </Form.Item>
                {otherLogistics &&
                  <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="logistics.info.name" /></span>} name="other">
                    {disabled
                      ? <Input placeholder={formatMessage({ id: "common.required.prefix" })} />
                      : <span>{form.getFieldValue("other") !== "" ? form.getFieldValue("other") : "-"}</span>
                    }
                  </Form.Item>
                }
                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="logistics.number" /></span>} name="number" >
                  {disabled
                    ? <Input
                      placeholder={formatMessage({ id: "common.required.prefix" })}
                      suffix={<svg
                        onMouseOver={() => {
                          setTagStyle({ backgroundColor: "rgba(22, 93, 255, 0.1)", color: "#165DFF" })
                        }}
                        onMouseOut={() => {
                          setTagStyle({ backgroundColor: "#F4F4F5", color: "#1D2129" })
                        }}
                        onClick={() => copyNumber()}
                        style={{ ...tagStyle }}
                        className="iconfont" width={14} height={14}>
                        <use xlinkHref="#icon-fuzhihuise"></use>
                      </svg>}
                    />
                    :
                    <span>
                      {(typeof form.getFieldValue("number") !== "undefined" && form.getFieldValue("number") !== "" && form.getFieldValue("number") != null)
                        ?
                        ((form.getFieldValue("logistics") === "shunfeng") ? <a href="https://www.sf-express.com/chn/sc/waybill/list" target="_blank">{form.getFieldValue("number")}</a> :
                          ((form.getFieldValue("logistics") === "shunfengkuaiyun") ? <a href="https://www.sf-freight.com/yundanzhuizong.html" target="_blank">{form.getFieldValue("number")}</a> :
                            (form.getFieldValue("logistics") === "fengwang" ? <a href="https://www.51tracking.com/fwx-network-tracking" target="_blank">{form.getFieldValue("number")}</a> : form.getFieldValue("number"))))
                        : "-"
                      }
                      {
                        permissions(auth.project.permissions, "operation.supply.shipment.logistics.edit") &&
                        typeof form.getFieldValue("number") !== "undefined" && form.getFieldValue("number") !== "" && form.getFieldValue("number") != null &&
                        form.getFieldValue("logistics") !== "shunfeng" && form.getFieldValue("logistics") !== "shunfengkuaiyun" && form.getFieldValue("logistics") !== "fengwang" &&
                        <Popover
                          placement="right"
                          open={open}
                          content={
                            <div style={{ width: '420px', height: "480px", overflowY: "auto", }}>
                              <Timeline style={{ marginTop: "10px" }}>
                                {logisticsDetails.map((item, index) => (
                                  <Timeline.Item key={index} color={index === 0 ? "blue" : "gray"}>
                                    <h3><span style={{ color: index === 0 ? "#165DFF" : "#677283", fontSize: "14px", fontWeight: 500 }}>{item.status}</span><span style={{ color: index === 0 ? "#165DFF" : "#677283", fontSize: "12px", fontWeight: 400 }}>{"    " + item.time}</span></h3>
                                    <p style={{ color: "#ADB2BA", fontSize: "12px", fontWeight: 400 }}>{item.context}</p>
                                  </Timeline.Item>
                                ))}
                              </Timeline>
                            </div>
                          }
                          style={{ height: "180px", width: "260px !important", left: "496px", top: "155px", borderRadius: "2px", marginTop: '16px', marginLeft: '12px' }}
                          trigger="click"
                          onOpenChange={handleOpenChange}
                        >
                          {/* <Tooltip title={formatMessage({id:"common.details"})}> */}
                          <i
                            style={{
                              marginLeft: 8, cursor: "pointer",
                              color: iconColor === 0 ? "#165DFF" : "#165DFF"
                            }}
                            onMouseEnter={handleMouseEnter}
                            onMouseLeave={handleMouseLeave}
                          >
                            <svg className="iconfont" width={14} height={13.64} style={{ marginBottom: "-3px" }}>
                              <use xlinkHref="#icon-wuliu"></use>
                            </svg>
                            <span style={{ paddingLeft: "6px" }}>{formatMessage({ id: 'common.details' })}</span>
                          </i>
                          {/* </Tooltip> */}
                        </Popover>
                      }
                    </span>
                  }
                </Form.Item>
                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="shipment.expectedArrivalTime" /></span>} name="expectedArrivalTime" >
                  <span>{record.expected_arrival_time !== undefined && record.expected_arrival_time !== "" ? record.expected_arrival_time : "-"}</span>
                </Form.Item>
                <Form.Item label={<span style={{ color: "#4E5969" }}><FormattedMessage id="shipment.actualReceiptTime" /></span>} name="actualReceiptTime" style={{ marginBottom: 0 }}>
                  {disabled
                    ?
                    // <DatePickers
                    //   placeholder={formatMessage({
                    //     id: "placeholder.select.common",
                    //   })}
                    //   disabledDate={disabledDate}
                    //   disabledTime={disabledDateTime}
                    //   className={"full-width"}
                    //   format={"YYYY-MM-DD HH:mm:ss"}
                    //   showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                    // ></DatePickers>
                    <CustomDateTimePicker
                      value={actualReceiptTime}
                      onChange={setActualReceiptTime}
                      disabledDate={disabledDate}
                      disabledTime={"disFuture"}
                      ph={'placeholder.select.common'}>
                    </CustomDateTimePicker>
                    :
                    <span>{record.actual_receipt_time !== undefined && record.actual_receipt_time !== "" ? record.actual_receipt_time : "-"}</span>
                  }
                </Form.Item>
              </Form>
            </Tabs.TabPane>
          }
          {((record.status === 4 || record.status === 5 || record.status === 8 || record.status === 9) && permissions(auth.project.permissions, "operation.supply.shipment.reason"))
            ? (
              <Tabs.TabPane tab={<FormattedMessage id="drug.freeze.reason" />} key="reason">
                <Form layout="horizontal" labelCol={{ style: { width: g.lang === "zh" ? 75 : 120, minHeight: 160 } }}>
                  <Form.Item label={showReasonViewModal(record._id, record.status, record.reason)}  >
                    {record.reason}
                  </Form.Item>
                </Form>
              </Tabs.TabPane>
            )
            : null
          }
        </Tabs>
      </Modal>
    </>
  );
});
