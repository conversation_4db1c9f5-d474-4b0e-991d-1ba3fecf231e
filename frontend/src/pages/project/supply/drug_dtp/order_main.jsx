import React from "react";
import { <PERSON>ge, Button, Col, Dropdown, Input, Menu, message, Popover, Row, Select, Table } from "antd";
import { useAuth } from "../../../../context/auth";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import { useMount, useSafeState } from "ahooks";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import { permissions } from "../../../../tools/permission";
import { shipmentStatus } from "../../../../data/data";
import { useFetch } from "../../../../hooks/request";
import { downloadDtpOrder, getDtpOrderList } from "../../../../api/order";
import { HistoryList } from "../../../common/history-list";
import { Receive } from "./receive";
import { ReasonOrder } from "../shipment/reasonOrder";
import { Medicines } from "../shipment/medicines";
import { userSites } from "../../../../api/subject";
import { CustomInfoModal } from "../../../../components/modal";
import { Logistics } from "./logistics";
import { InsertDivider } from "../../../../components/divider";
import { Confirm } from "../shipment/confirm";
import { getProjectAttribute } from "../../../../api/randomization";
import { PageContextProvider } from "../../../../context/page";

export const OrderMain = (props) => {
    const auth = useAuth()
    const page = usePage()
    const searchInputRef = React.useRef();
    const [doSearch, setDoSearch] = useSafeState(0);
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    //const cohortId = auth.cohort ? auth.cohort.id : null;
    const projectStatus = auth.project.status ? auth.project.status : 0
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [number, setNumber] = useSafeState(null);
    const [attribute, setAttribute] = useSafeState(null);
    const [status, setStatus] = useSafeState(undefined);
    const [height, setHeight] = useSafeState(window.innerHeight - 233);
    const [siteData, setSiteData] = useSafeState([]);
    const [projectSiteId, setProjectSiteId] = useSafeState("");
    const receive_ref = React.useRef();
    const logistics_ref = React.useRef();
    const reason_ref = React.useRef();
    const history_ref = React.useRef();
    const medicines_ref = React.useRef();
    const confirm_ref = React.useRef();

    const handleResize = () => {
        setHeight(window.innerHeight - 233);
    };
    React.useEffect(
        () => {
            // 监听
            window.addEventListener("resize", handleResize);
            // 销毁
            return () => window.removeEventListener("resize", handleResize);
        }
    );

    const confirmOrder = (record) => {
        record.order_number = record.orderNumber
        confirm_ref.current.show(record);
    }

    const { runAsync: run_getDtpOrderList, loading } = useFetch(getDtpOrderList, { manual: true })
    const { runAsync: run_downloadDtpOrder, loading: downloading } = useFetch(downloadDtpOrder, { manual: true })
    const { runAsync: userSitesRun } = useFetch(userSites, { manual: true })
    const { runAsync: getProjectAttributeRun } = useFetch(getProjectAttribute, { manual: true });
    const list = () => {
        getProjectAttributeRun({
            projectId,
            env: envId,
            customer: customerId,
        }).then((result) => {
            let da = result.data;
            setAttribute(da);
            //订单列表查询
            run_getDtpOrderList(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    roleId: auth.project.permissions.role_id,
                    status: status,
                    number: number,
                    projectSiteId: projectSiteId,
                    start: (page.currentPage - 1) * page.pageSize,
                    limit: page.pageSize
                }).then((result) => {
                    const data = result.data
                    if (data != null) {
                        page.setTotal(data.total);
                        page.setData(data.items);
                    }
                }
                )
        })

    }


    //下载
    const download = () => {
        run_downloadDtpOrder(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                status: status,
                number: number,
            }).catch(() => message.error(formatMessage({ id: 'common.download.fail' })))
    };

    const updateOrder = (record, status) => {
        logistics_ref.current.show(record, 1)
    };

    const receiveOrder = (record) => {
        receive_ref.current.show(record.id, record.orderNumber, record.status);

    };
    const orderMedicines = (record) => {
        medicines_ref.current.show(record.id, record.orderNumber);
    }
    const showReasonModal = (id, status, orderNumber) => {
        reason_ref.current.show(id, status, orderNumber);
    };
    const siteOnChange = (value) => {
        if (value !== undefined) {
            setProjectSiteId(siteData[value].id);
        } else {
            setProjectSiteId("");

        }

    };
    const showReasonViewModal = (id, status, reason) => {
        let title = ""
        if (status) {
            if (status === 5) {//取消
                title = formatMessage({ id: 'shipment.cancel.order' })
            } else if (status === 4) {//丢失订单
                title = formatMessage({ id: 'shipment.lose.order' })
            } else if (status === 8) {
                title = formatMessage({ id: 'shipment.end.order' })

            }
        }
        CustomInfoModal({
            title: title,
            content: reason,
            centered: true,
        })
    };


    function renderStatus(value) {
        const colors = ["", "geekblue", "orange", "cyan", "red", "purple", "yellow", "volcano", "gold", "magenta"];
        return <Badge color={colors[value]} text={shipmentStatus.find(it => (it.value === value)).label} />;
    }


    //轨迹
    const showHistory = (oid) => {
        history_ref.current.show("history.order", oid, timeZone);
    }
    const renderOption = (record) => {
        let buttons = []
        let menus = []
        permissions(auth.project.permissions, "operation.supply.drug.order.history") &&
            buttons.push(<Button size="small" type="link"
                onClick={() => showHistory(record.id)}> <FormattedMessage
                    id="common.history" /></Button>);

        record.status === 7 && permissions(auth.project.permissions, "operation.supply.drug.order.cancel") && projectStatus !== 2 &&
            buttons.push(<Button size="small" type="link"
                onClick={() => showReasonModal(record.id, 5, record.orderNumber)}><FormattedMessage
                    id="common.cancel" /></Button>);

        record.status === 7 && permissions(auth.project.permissions, "operation.supply.drug.order.confirm") && projectStatus !== 2 &&
            buttons.push(<Button size="small" type="link"
                onClick={() => confirmOrder(record)}><FormattedMessage
                    id="common.confirm" /></Button>);

        if (record.status === 1 && permissions(auth.project.permissions, "operation.supply.drug.order.send") && projectStatus !== 2) {
            buttons.length < 3 ?
                buttons.push(<Button size="small" type="link"
                    onClick={() => updateOrder(record, 2)}><FormattedMessage
                        id="shipment.transit" /></Button>)
                :
                menus.push(<Menu.Item key={"transit"}
                    onClick={() => updateOrder(record, 2)}><FormattedMessage
                        id="shipment.transit" /></Menu.Item>)
        }



        if (record.status === 1 && permissions(auth.project.permissions, "operation.supply.drug.order.close") && projectStatus !== 2) {
            buttons.length < 3 ?
                buttons.push(<Button size="small" type="link"
                    onClick={() => showReasonModal(record.id, 9, record.orderNumber)}><FormattedMessage
                        id="common.close" /></Button>)
                :
                menus.push(<Menu.Item key={"close"}
                    onClick={() => showReasonModal(record.id, 9, record.orderNumber)}><FormattedMessage
                        id="common.close" /></Menu.Item>)
        }


        if ((record.status === 1 || record.status === 2) && permissions(auth.project.permissions, "operation.supply.drug.order.receive") && projectStatus !== 2) {
            buttons.length < 3 ?
                buttons.push(<Button size="small" type="link"
                    onClick={() => receiveOrder(record)}><FormattedMessage
                        id="shipment.received" /></Button>) :
                menus.push(<Menu.Item key={"receive"}
                    onClick={() => receiveOrder(record)}><FormattedMessage
                        id="shipment.received" /></Menu.Item>)
        }

        if ((record.status === 4 || record.status === 5 || record.status === 8 || record.status === 9)) {
            buttons.length < 3 ?
                buttons.push(<Popover placement="topRight" overlayClassName={"custom-popover"} title={<FormattedMessage id="drug.freeze.reason" />} content={record.reason} trigger="click"><Button size="small" type="link"><FormattedMessage
                    id="drug.freeze.reason" /></Button></Popover>)
                :
                menus.push(<Menu.Item key={"reason"}
                    onClick={() => showReasonViewModal(record.id, record.status, record.reason)}><FormattedMessage
                        id="drug.freeze.reason" /></Menu.Item>)
        }


        if ((record.status === 2) && permissions(auth.project.permissions, "operation.supply.drug.order.end") && projectStatus !== 2) {
            buttons.length < 3 ?
                buttons.push(<Button size="small" type="link"
                    onClick={() => showReasonModal(record.id, 8, record.orderNumber)}><FormattedMessage id="shipment.end" /></Button>)
                :
                menus.push(<Menu.Item key={"end"}
                    onClick={() => showReasonModal(record.id, 8, record.orderNumber)}><FormattedMessage id="shipment.end" /></Menu.Item>)
        }
        if (permissions(auth.project.permissions, "operation.supply.shipment.logistics.view")) {
            buttons.length < 3 ?
                buttons.push(<Button size="small" type="link"
                    onClick={() => logistics_ref.current.show(record, 2)}><FormattedMessage id="logistics.info" /></Button>)
                :
                menus.push(<Menu.Item key={"logistics"}
                    onClick={() => logistics_ref.current.show(record, 2)}><FormattedMessage id="logistics.info" /></Menu.Item>)
        }


        return <div style={{ display: "flex", alignItems: "center" }}>
            {
                buttons.length >= 3 ?
                    <div style={{ display: "flex", alignItems: "center" }}>
                        {InsertDivider(buttons)}
                        {menus.length !== 0 && <Dropdown overlay={
                            <Menu>
                                {menus.map((item) => (item))}
                            </Menu>
                        }>
                            <span style={{ marginLeft: 12 }} className="dropdown-button"><i className="iconfont icon-gengduo-changgui" /></span>
                        </Dropdown>}
                    </div>
                    :
                    InsertDivider(buttons)
            }

        </div>
    }

    React.useEffect(list, [doSearch, projectSiteId, status, page.currentPage, page.pageSize]);
    useMount(() => {
        // 查询中心
        userSitesRun({
            projectId: projectId,
            customerId: customerId,
            envId: envId,
            roleId: auth.project.permissions.role_id
        }).then(
            (result) => {
                setSiteData(result.data);
            }
        )
    })
    return (
        <>
            <Row gutter={8} justify={"start"}>
                <Col xs={24} sm={12} md={6} lg={4} className="mar-ver-5">
                    <Input.Search
                        placeholder={formatMessage({ id: 'shipment.orderNumber' }) + "/" + attribute?.info?.subjectReplaceText}
                        ref={searchInputRef}
                        value={number}
                        onChange={e => setNumber(e.target.value)} allowClear
                        onSearch={() => {
                            page.setCurrentPage(1)
                            searchInputRef?.current?.blur()
                            setDoSearch(doSearch + 1);
                        }}
                    />
                </Col>
                <Col xs={24} sm={12} md={6} lg={4} className="mar-ver-5">
                    <Select className="full-width" placeholder={formatMessage({ id: 'shipment.status' })}
                        value={status} onChange={e => setStatus(e)} options={shipmentStatus}
                        allowClear />
                </Col>
                <Col xs={24} sm={12} md={6} lg={4} className="mar-ver-5">
                    <Select
                        className="full-width"
                        placeholder={<FormattedMessage id={"common.site"} />}
                        showSearch
                        allowClear
                        optionFilterProp="children"
                        onChange={(v) => siteOnChange(v)}
                        filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    >
                        {(siteData ? siteData : []).map((it, index) =>
                            <Select.Option key={index} value={it._id}>{it.number + "-" + it.name}</Select.Option>
                        )}
                    </Select>
                </Col>
                <Col xs={24} sm={24} md={6} lg={4} className="mar-ver-5">
                    {
                        permissions(auth.project.permissions, "operation.supply.drug.order.download") &&
                        <Button loading={downloading} className="mar-rgt-5"
                            onClick={() => download()}><FormattedMessage
                                id="common.download" /></Button>}
                </Col>
            </Row>
            <Table
                loading={loading}
                size="small"
                className="mar-top-10"
                dataSource={page.data}
                pagination={false}
                style={{ overflowX: "auto", height: height }}
                rowKey={(record) => (record.id)}
            >
                <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={70}
                    render={(text, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)} />
                <Table.Column title={<FormattedMessage id="shipment.orderNumber" />} width={200}
                    dataIndex="orderNumber" key="orderNumber" ellipsis />
                <Table.Column
                    title={<FormattedMessage id="shipment.medicine" />}
                    dataIndex="count"
                    key="count"
                    ellipsis
                    width={135}
                    render={
                        (value, record, index) => (
                            <span style={{ color: "#3562EC" }} className="mouse"
                                onClick={() => orderMedicines(record)}>{record.count}</span>
                        )
                    }
                />
                <Table.Column title={auth.attribute ? auth.attribute.info.subjectReplaceText : formatMessage({ id: "subject.number" })} width={200}
                    dataIndex="subjectNumber" key="subjectNumber" ellipsis />
                <Table.Column title={<FormattedMessage id="suppy.drug.order.visitNumber" />} width={200}
                    dataIndex="visitNumber" key="visitNumber" ellipsis />
                <Table.Column title={<FormattedMessage id="common.site" />}
                    dataIndex="siteName"
                    key="siteName" ellipsis width={220} />
                <Table.Column
                    title={<FormattedMessage id="shipment.status" />}
                    dataIndex="status"
                    key="status"
                    ellipsis
                    width={140}
                    render={(value, record, index) => (renderStatus(value))}
                />
                <Table.Column
                    title={<FormattedMessage id="common.operation" />}
                    align="center"
                    width={250}
                    fixed="right"
                    render={
                        (value, record, index) => (
                            renderOption(record))
                    }
                />
            </Table>
            <PaginationView />
            <Logistics bind={logistics_ref} refresh={list} />
            <PageContextProvider>
                <Receive bind={receive_ref} refresh={list} />
            </PageContextProvider>
            <Medicines bind={medicines_ref} refresh={list} />
            <ReasonOrder bind={reason_ref} refresh={list} />
            <PageContextProvider>
                <Confirm bind={confirm_ref} refresh={list} />
            </PageContextProvider>
            <HistoryList bind={history_ref} permission={permissions(auth.project.permissions, "operation.supply.drug.order.print")} />
        </>
    )
};