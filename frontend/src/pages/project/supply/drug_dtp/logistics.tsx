import React, { useState } from 'react';
import { <PERSON><PERSON>, Col, DatePicker, Form, Input, message, Modal, Popover, Row, Select, Timeline } from "antd";
// import {logistics} from "../../../../data/data";
import { useGlobal } from "../../../../context/global";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { getCompanyCode, getLogistics, getLogisticsCompanyCode, updateOrderStatus, updateRecoveryOrder } from "../../../../api/order";
import { useTranslation } from "../../../common/multilingual/component";
import copy from "copy-to-clipboard";
import { useAuth } from "../../../../context/auth";
import { permissions } from "../../../../tools/permission";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";
import moment from 'moment';



export const Logistics = (props: any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [form] = Form.useForm();
    const [otherLogistics, setOtherLogistics] = useSafeState<boolean>(false)
    const [type, setType] = useSafeState<number>(0)
    const [disabled, setDisabled] = useSafeState<boolean>(false)
    const [visible, setVisible] = useSafeState<boolean>(false)
    const [firstForm, setFirstForm] = useSafeState<any>({})
    const [orderNumber, setOrderNumber] = useSafeState(null);
    const [orderType, setOrderType] = useSafeState(null);
    const [id, setId] = useSafeState<any>(null)
    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: { width: g.lang === "en" ? "160px" : "110px" } },
    };
    const [open, setOpen] = useState(false);
    const [iconColor, setIconColor] = useSafeState(0);
    const [expectedArrivalTimeValue, setExpectedArrivalTimeValue] = useSafeState(null);
    const [logistics, setLogistics] = useSafeState<any[]>([]);
    const [logisticsDetails, setLogisticsDetails] = useSafeState<any[]>([]);
    const DatePickers: any = DatePicker;
    // const [data, setData] = useSafeState<any[]>([]);
    const [value, setValue] = useState<string>();

    const show = (record: any, type: any) => {
        setOrderType(record.type);
        setOrderNumber(record.orderNumber ? record.orderNumber : record.order_number);
        setId(record.id ? record.id : record._id)
        setType(type)
        form.setFieldsValue({ isTransit: 2, logistics: record.logistics_info?.logistics, other: record.logistics_info?.other, number: record.logistics_info?.number })
        setFirstForm({ isTransit: 2, logistics: record.logistics_info?.logistics, other: record.logistics_info?.other, number: record.logistics_info?.number })
        if (form.getFieldValue("logistics") === 9) {
            setOtherLogistics(true)
        }
        if (form.getFieldValue("logistics") === 0) {
            form.setFieldsValue({ ...form.getFieldsValue, logistics: null })
        }
        if (record.expected_arrival_time !== undefined && record.expected_arrival_time !== "") {
            // 将字符串日期解析为Date对象
            //var parsedDate = moment(record.expected_arrival_time, 'YYYY-MM-DD HH:mm:ss');
            form.setFieldValue("expectedArrivalTime", record.expected_arrival_time)
            setExpectedArrivalTimeValue(record.expected_arrival_time)
        }
        setVisible(true)
        if (type === 1) {
            setDisabled(true)
        }

        if (record.logistics_info?.logistics === "") {
            form.setFieldValue("logistics", null)
        }

        run_getCompanyCode({ code: record.logistics_info?.logistics }).then(
            (res: any) => {
                setLogistics(
                    res.data?.map((item: any) => ({
                        label: item.name,
                        value: item.code,
                    })) || []
                );
                if (res.data.length === 1 && res.data[0].code === "qita") {
                    setOtherLogistics(true)
                } else {
                    setOtherLogistics(false)
                }
            }
        );

    }

    const cancel = () => {
        if (firstForm.logistics === 9) {
            setOtherLogistics(true)
        } else {
            setOtherLogistics(false)
        }
        form.setFieldsValue({ ...firstForm })
        setDisabled(false)
    }
    const [tagStyle, setTagStyle] = useSafeState({ backgroundColor: "#F4F4F5", color: "#1D2129" })

    React.useImperativeHandle(props.bind, () => ({ show }));

    const edit = () => {
        let logistics = form.getFieldValue("logistics") !== 0 ? form.getFieldValue("logistics") : null
        form.setFieldsValue({ ...firstForm, logistics: logistics })
        setDisabled(true)
    }

    const { runAsync: run_updateOrderStatus } = useFetch(updateOrderStatus, { manual: true })
    const { runAsync: run_updateRecoveryOrderStatus } = useFetch(updateRecoveryOrder, { manual: true })
    const { runAsync: run_getLogistics } = useFetch(getLogistics, { manual: true })
    const { runAsync: run_getLogisticsCompanyCode } = useFetch(getLogisticsCompanyCode, { manual: true })
    const { runAsync: run_getCompanyCode } = useFetch(getCompanyCode, { manual: true })


    const save = () => {
        form.validateFields().then(
            value => {
                let expectedArrivalTime = (value.expectedArrivalTime !== null && value.expectedArrivalTime !== undefined && value.expectedArrivalTime !== "-") ? value.expectedArrivalTime : "";
                // let expectedArrivalTime = value.expectedArrivalTime ? moment(value.expectedArrivalTime).format("YYYY-MM-DD HH:mm:ss") : "";
                // delete value.expectedArrivalTime
                if (orderType === 3 || orderType === 4) {
                    run_updateRecoveryOrderStatus({
                        id: id,
                        status: 2,
                        roleId: auth.project.permissions.role_id,
                        expectedArrivalTime: expectedArrivalTime,
                        logisticsInfo: { ...value }
                    }).then((resp: any) => {
                        message.success(resp.msg).then()
                        hide()
                        props.refresh()
                    })
                } else {
                    run_updateOrderStatus({
                        id: id,
                        status: 2,
                        roleId: auth.project.permissions.role_id,
                        expectedArrivalTime: expectedArrivalTime,
                        logisticsInfo: { ...value }
                    }).then((resp: any) => {
                        message.success(resp.msg).then()
                        hide()
                        props.refresh()
                    })
                }

            }
        )
    }

    const hide = () => {
        setId(null)
        setVisible(false)
        setDisabled(false)
        setOtherLogistics(false)
        setExpectedArrivalTimeValue(null);
        form.resetFields()
        setOrderType(null);
        setOpen(false);
        setIconColor(0)
    }

    const handleOpenChange = (newOpen: boolean) => {
        if (newOpen != null) {
            if (newOpen === true) {
                setIconColor(1)
            } else {
                setIconColor(0)
            }
        }
        // if(form.getFieldValue("logistics") === "shunfeng"){
        //     window.open("https://www.sf-express.com/chn/sc/waybill/list", '_blank');
        //     return;
        // }else if(form.getFieldValue("logistics") === "shunfengkuaiyun"){
        //     window.open("https://www.sf-freight.com/yundanzhuizong.html", '_blank');
        //     return;
        // }else if(form.getFieldValue("logistics") === "fengwang"){
        //     window.open("https://www.51tracking.com/fwx-network-tracking", '_blank');
        //     return;
        // }
        let code = "";
        run_getLogistics(
            {
                com: form.getFieldValue("logistics"),
                num: form.getFieldValue("number").trim(),
                // phone: phone,//顺丰相关都要手机号
            }).then((res: any) => {
                if (res.data.message !== "ok") {
                    message.warn(res.data.message);
                    setOpen(false);
                    return;
                } else {
                    setLogisticsDetails(
                        (res.data && res.data.data)?.map((item: { areaCenter: any; areaCode: any; areaName: any; areaPinYin: any; context: any; statusCode: any; location: any; time: any; status: any; }) => {
                            const logisticsItem = {
                                areaCenter: item.areaCenter,
                                areaCode: item.areaCode,
                                areaName: item.areaName,
                                areaPinYin: item.areaPinYin,
                                context: item.context,
                                ftime: item.statusCode,
                                location: item.location,
                                time: item.time,
                                status: item.status,
                                statusCode: item.statusCode,
                            };

                            if (code === item.statusCode) {
                                logisticsItem.status = "";
                                logisticsItem.statusCode = "";
                            }

                            code = item.statusCode;

                            return logisticsItem;
                        }) || []
                    );
                    setOpen(newOpen);
                }
            }
            );
    };

    const disabledDate = (current: any) => {
        return current && current < moment().startOf('day');
    };


    const handleMouseEnter = () => {
        setIconColor(1)
    };

    const handleMouseLeave = () => {
        setIconColor(0)
    };


    const handleSearch = (newValue: string) => {
        run_getLogisticsCompanyCode({ name: newValue }).then(
            (res: any) => {
                setLogistics(
                    res.data?.map((item: any) => ({
                        label: item.name,
                        value: item.code,
                    })) || []
                );
                // if(res.data.length === 1 && res.data[0].code === "qita"){
                //     setOtherLogistics(true)
                // }else{
                //     setOtherLogistics(false)
                // }
            }
        );
    };

    const handleChange = (newValue: string) => {
        if (newValue === "qita") {
            setOtherLogistics(true)
        } else {
            setOtherLogistics(false)
        }
        setValue(newValue);

    };

    const copyNumber = () => {
        copy(form.getFieldValue("number"))
        message.success(formatMessage({ id: 'common.copy.ok' }))
    }
    return <Modal
        forceRender
        centered
        onCancel={hide}
        title={type === 1 ? formatMessage({ id: "shipment.transit" }) + " - " + orderNumber : formatMessage({ id: "logistics.info" }) + " - " + orderNumber}
        visible={visible}
        destroyOnClose={true}
        maskClosable={false}
        footer={
            disabled ?
                type === 1 ?
                    <Row justify="space-between">
                        <Col>

                        </Col>
                        <Col>
                            <Button onClick={hide}>{formatMessage({ id: 'common.cancel' })}</Button>
                            <Button onClick={save} type={"primary"}>{formatMessage({ id: 'common.ok' })}</Button>
                        </Col>
                    </Row> :
                    <Row justify="space-between">
                        <Col></Col>
                        {
                            disabled &&
                            <Col>
                                <Button onClick={cancel}>{formatMessage({ id: 'common.cancel' })}</Button>
                                <Button onClick={save} type={"primary"}>{formatMessage({ id: 'common.save' })}</Button>
                            </Col>
                        }
                    </Row>
                :
                null
        }
    >
        <Form form={form} {...formItemLayout}>
            {
                type === 1 &&
                // <Form.Item label={formatMessage({id:"logistics.info.isTransit"})} name="isTransit" >
                //     <Radio.Group >
                //         <Radio value={2}>{formatMessage({id:"common.yes"})}</Radio>
                //         <Radio style={{marginLeft:16}} value={0}>{formatMessage({id:"common.no"})}</Radio>
                //     </Radio.Group>
                // </Form.Item>
                <Row>
                    <Col style={{ borderRadius: '2px 2px 2px 2px', backgroundColor: "#e8efff", padding: '10px', height: "32px", marginBottom: "24px", display: 'flex', alignItems: 'center', width: "500px" }}>
                        <div style={{ verticalAlign: 'middle' }}>
                            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" width="16" height="16" style={{ verticalAlign: "middle", }}><path d="M512 83.2a428.8 428.8 0 1 1 0 857.6 428.8 428.8 0 0 1 0-857.6z m44.8 345.6h-89.6v256h-44.8v64h172.8v-64h-44.8l6.4-256z m-89.6 0h-38.4v64h44.8v-64zM512 256c-38.4 0-64 25.6-64 64s25.6 64 64 64 64-25.6 64-64-25.6-64-64-64z" fill="#2B74EA" p-id="2726"></path></svg>
                            <span style={{ paddingLeft: '8px' }}>{formatMessage({ id: "logistics.info.isTransit.ok" })}</span>
                        </div>
                    </Col>
                </Row>
            }

            <Form.Item label={formatMessage({ id: "logistics.confirm.info" })} name="logistics">
                {
                    disabled ?
                        <Select
                            showSearch
                            value={value}
                            placeholder={formatMessage({ id: "common.required.prefix.search" })}
                            style={props.style}
                            defaultActiveFirstOption={false}
                            showArrow={false}
                            filterOption={false}
                            onSearch={handleSearch}
                            onChange={handleChange}
                            notFoundContent={null}
                        // options={(logistics || []).map((d) => ({
                        //     value: d.value,
                        //     label: d.code,
                        // }))}
                        >
                            {(logistics || []).map(v => <Select.Option key={v.value} value={v.value}>{v.label}</Select.Option>)}
                        </Select>
                        // <Select onChange={selectOption}  >
                        //     {logistics.map(v => <Select.Option key={v.value} value={v.value}>{v.label}</Select.Option>)}
                        // </Select>
                        :
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span>{logistics.find((v) => v.value === form.getFieldValue("logistics")) && form.getFieldValue("logistics") !== ""
                                ?
                                logistics.find((v) => v.value === form.getFieldValue("logistics"))?.label :
                                "-"
                            }</span>
                            {
                                (permissions(auth.project.permissions, "operation.supply.shipment.logistics.edit") || permissions(auth.project.permissions, "operation.supply.drug_recovery.logistics.edit")) &&
                                <Button type={"link"} onClick={edit} style={{ display: 'flex', alignItems: 'center', position: "absolute", right: 0 }}>
                                    <svg className="iconfont" width={16} height={16}>
                                        <use xlinkHref="#icon-bianji"></use>
                                    </svg>
                                    <span style={{ paddingLeft: "8px" }}>{formatMessage({ id: 'subject.update' })}</span>
                                </Button>
                            }


                        </div>

                }

            </Form.Item>
            {
                otherLogistics &&
                <Form.Item label={formatMessage({ id: "logistics.info.name" })} name="other">
                    {
                        disabled ?
                            <Input />
                            :
                            <span>{form.getFieldValue("other") !== "" ? form.getFieldValue("other") : "-"}</span>
                    }
                </Form.Item>
            }
            <Form.Item label={formatMessage({ id: "logistics.number" })} name="number" style={{ marginBottom: 0 }}>

                {
                    disabled ?
                        <Input placeholder={formatMessage({ id: "common.required.prefix" })} suffix={<svg
                            onMouseOver={() => {
                                setTagStyle({ backgroundColor: "rgba(22, 93, 255, 0.1)", color: "#165DFF" })
                            }}
                            onMouseOut={() => {
                                setTagStyle({ backgroundColor: "#F4F4F5", color: "#1D2129" })
                            }}
                            onClick={() => copyNumber()}
                            style={{ ...tagStyle }}
                            className="iconfont" width={14} height={14}>
                            <use xlinkHref="#icon-fuzhihuise"></use>
                        </svg>} />
                        :
                        <span>
                            {(typeof form.getFieldValue("number") !== "undefined" && form.getFieldValue("number") !== "" && form.getFieldValue("number") != null)
                                ?
                                ((form.getFieldValue("logistics") === "shunfeng") ? <a href="https://www.sf-express.com/chn/sc/waybill/list" target="_blank">{form.getFieldValue("number")}</a> :
                                    ((form.getFieldValue("logistics") === "shunfengkuaiyun") ? <a href="https://www.sf-freight.com/yundanzhuizong.html" target="_blank">{form.getFieldValue("number")}</a> :
                                        (form.getFieldValue("logistics") === "fengwang" ? <a href="https://www.51tracking.com/fwx-network-tracking" target="_blank">{form.getFieldValue("number")}</a> : form.getFieldValue("number"))))
                                : "-"
                            }
                            {
                                (permissions(auth.project.permissions, "operation.supply.shipment.logistics.edit") || permissions(auth.project.permissions, "operation.supply.drug_recovery.logistics.edit")) &&
                                typeof form.getFieldValue("number") !== "undefined" && form.getFieldValue("number") !== "" && form.getFieldValue("number") != null &&
                                form.getFieldValue("logistics") !== "shunfeng" && form.getFieldValue("logistics") !== "shunfengkuaiyun" && form.getFieldValue("logistics") !== "fengwang" &&
                                <Popover
                                    placement="right"
                                    open={open}
                                    // title={
                                    //     <div>
                                    //         {formatMessage({id:"common.modify"})}
                                    //         <Button onClick={hide}  type="text" icon={<CloseOutlined style={{color:"#666666"}}/>} style={{float: 'right', marginRight: -4, width:"15px", height:"15px" }}/>
                                    //     </div>
                                    // }
                                    content={
                                        <div style={{ width: '420px', height: "480px", overflowY: "auto", }}>
                                            <Timeline style={{ marginTop: "10px" }}>
                                                {logisticsDetails.map((item, index) => (
                                                    <Timeline.Item key={index} color={index === 0 ? "blue" : "gray"}>
                                                        <h3><span style={{ color: index === 0 ? "#165DFF" : "#677283", fontSize: "14px", fontWeight: 500 }}>{item.status}</span><span style={{ color: index === 0 ? "#165DFF" : "#677283", fontSize: "12px", fontWeight: 400 }}>{"    " + item.time}</span></h3>
                                                        <p style={{ color: "#ADB2BA", fontSize: "12px", fontWeight: 400 }}>{item.context}</p>
                                                    </Timeline.Item>
                                                ))}
                                            </Timeline>
                                        </div>
                                    }
                                    style={{ height: "180px", width: "260px !important", left: "496px", top: "155px", borderRadius: "2px", marginTop: '16px', marginLeft: '12px' }}
                                    trigger="click"
                                    onOpenChange={handleOpenChange}
                                >
                                    {/* <Tooltip title={formatMessage({id:"common.details"})}> */}
                                    <i
                                        // onClick={()=>drug.setVisitTypeView(true)}
                                        style={{
                                            marginLeft: 8, cursor: "pointer",
                                            color: iconColor === 0 ? "#165DFF" : "#165DFF"
                                        }}
                                        // className='iconfont icon-bianji'
                                        onMouseEnter={handleMouseEnter}
                                        onMouseLeave={handleMouseLeave}
                                    >
                                        <svg className="iconfont" width={14} height={13.64} style={{ marginBottom: "-3px" }}>
                                            <use xlinkHref="#icon-wuliu"></use>
                                        </svg>
                                        <span style={{ paddingLeft: "6px" }}>{formatMessage({ id: 'logistics.info.details' })}</span>
                                    </i>
                                    {/* </Tooltip> */}
                                </Popover>
                            }
                        </span>

                }
            </Form.Item>
            <Form.Item
                label={formatMessage({
                    id: "shipment.expectedArrivalTime",
                })}
                name="expectedArrivalTime"
                className="mar-ver-15"
            >
                {/* <DatePickers
                    placeholder={formatMessage({
                        id: "placeholder.select.common",
                    })}
                    className="full-width"
                    format={"YYYY-MM-DD HH:mm:ss"}
                    showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                ></DatePickers> */}
                <CustomDateTimePicker
                    disabledDate={disabledDate}
                    disabledTime={"disBefore"}
                    value={expectedArrivalTimeValue}
                    onChange={setExpectedArrivalTimeValue}
                    ph={'placeholder.select.common'}>
                </CustomDateTimePicker>
            </Form.Item>
        </Form>
    </Modal>
}
