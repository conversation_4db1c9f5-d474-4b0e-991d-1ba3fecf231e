import React from "react";
import { Col, Collapse, Empty, Form, Row, Select, Spin, Table, Tag, Tooltip } from "antd";
import { useFetch } from "../../../../hooks/request";
import { medicineSummarySite, getSiteForecast } from "../../../../api/medicine";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { medicineStatusAliData } from "../../../../tools/medicine_status";
import { PaginationView } from "../../../../components/pagination";
import { usePage } from "../../../../context/page";
import { CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons';
import { getProjectAttributeConnect } from "../../../../api/randomization";
import { permissions } from "../../../../tools/permission";
import {FormattedMessage} from "../../../common/multilingual/component";

export const Summary = (props) => {

    const [siteList, setSiteList] = useSafeState([]);
    const [defaultKeys, setDefaultKeys] = useSafeState([]);
    const [tableHeight, setTableHeight] = useSafeState(300);
    const [selectSite, setSelectSite] = useSafeState([]);
    const [siteForecast, setSiteForecast] = useSafeState([]);
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const page = usePage();

    const [ali, setAli] = useSafeState(0);

    React.useEffect(
        () => {
            setTableHeight(document.documentElement.clientHeight - 330);
        },
        []
    );

    const { runAsync: getProjectAttributeConnectRun, loading: getProjectAttributeConnectLoading } = useFetch(getProjectAttributeConnect, { manual: true })
    const { runAsync: run_medicineSummarySite, loading } = useFetch(medicineSummarySite, { manual: true })
    const { runAsync: runSiteForecast, siteForecastLoading } = useFetch(getSiteForecast, { manual: true })

    const runSiteForecastFun = () => {
        runSiteForecast({ envId: envId, roleId: auth.project.permissions.role_id }).then(
            (data) => {
                if (data.data) {
                    setSiteForecast(data.data)
                }
            }
        )
    }

    const search = () => {
        run_medicineSummarySite(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                siteId: selectSite,
                roleId: auth.project.permissions.role_id,
                start: (page.currentPage - 1) * page.pageSize,
                limit: page.pageSize
            }).then((result) => {
                let response = result.data
                let list = []
                if (response.items) {
                    page.setTotal(response.total);
                    response.items.forEach(site => {
                        let drugs = [];
                        let otherDrugs = [];
                        let batchMaps = {};
                        let tmpsite = { id: site._id, name: site.name, number: site.number }
                        site.medicine_others.forEach(info => {
                            let item = info._id;
                            let findIndex = otherDrugs.findIndex(drug => drug.name === item.name);
                            if (findIndex !== -1) {
                                otherDrugs[findIndex][item.status] = otherDrugs[findIndex][item.status] ? { count: otherDrugs[findIndex][item.status].count + info.count } : { count: info.count };;
                            } else {
                                let tmp = { name: item.name, id: "other", spec: item?.spec, "isOpenPackage": item.isOpenPackage }
                                tmp[item.status] = { count: info.count };
                                otherDrugs.push(tmp);
                            }
                            let batchs = []
                            if (!batchMaps[item.name + "other"]) {
                                batchMaps[item.name + "other"] = batchs;
                            } else {
                                batchs = batchMaps[item.name + "other"];
                            }
                            findIndex = batchs.findIndex(batch => batch.name === item.name && batch.batchNumber === item.batch_number && batch.id === "other");
                            if (findIndex !== -1) {
                                batchs[findIndex][item.status] = batchs[findIndex][item.status] ? { count: batchs[findIndex][item.status].count + info.count } : { count: info.count };
                            } else {
                                let tmp = { name: item.name, id: "other", batchNumber: item.batch_number, "isOpenPackage": item.isOpenPackage }
                                tmp[item.status] = { count: info.count };
                                batchs.push(tmp);
                            }
                        });
                        site.medicine.forEach(medicine => {
                            let item = medicine._id
                            let findIndex = drugs.findIndex(drug => drug.name === item.name && drug.spec === item.spec);
                            if (findIndex !== -1) {
                                drugs[findIndex][item.status] = drugs[findIndex][item.status] ? { count: drugs[findIndex][item.status].count + medicine.count } : { count: medicine.count };
                            } else {
                                let tmp = { name: item.name, id: "", spec: item?.spec }
                                tmp[item.status] = { count: medicine.count };
                                drugs.push(tmp);
                            }
                            let batchs = []
                            if (!batchMaps[item.name + " " + item.spec]) {
                                batchMaps[item.name + " " + item.spec] = batchs;
                            } else {
                                batchs = batchMaps[item.name + " " + item.spec];
                            }
                            findIndex = batchs.findIndex(batch => batch.name === item.name && batch.batchNumber === item.batch_number && batch?.spec === item?.spec,);
                            if (findIndex !== -1) {
                                batchs[findIndex][item.status] = batchs[findIndex][item.status] ? { count: batchs[findIndex][item.status].count + medicine.count } : { count: medicine.count };
                            } else {
                                let tmp = { name: item.name, id: " ", batchNumber: item.batch_number, spec: item?.spec }
                                tmp[item.status] = { count: medicine.count };
                                batchs.push(tmp);
                            }
                        });
                        drugs.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
                        tmpsite.drugs = drugs.concat(otherDrugs);
                        tmpsite.batchs = batchMaps;
                        list.push(tmpsite);
                    });
                }
                setDefaultKeys(list.map(e => e.id));
                setSiteList(list);
            })

        // if(auth.project.info.type === 1){
        getProjectAttributeConnectRun({ projectId: projectId, envId: envId }).then((res) => {
            if (res.data) {
                if (res.data.info.connectAli) {
                    setAli(1)
                }
            }
        });
        // }



    }


    const summaryStatus = (drug) => {
        return (
            <Row>
                {
                    medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(item => drug[item.value] ?
                        <Col key={item.value}><Tag>{item.label}: {drug[item.value]}</Tag></Col> :
                        <Col key={item.value}><Tag>{item.label}: 0</Tag></Col>)
                }
            </Row>
        )
    };



    const onExpandDetail = (it, drug) => {
        <Table
            dataSource={it.batchs[drug.name + (drug.id !== "" ? drug.id : " " + drug.spec)]}
            // size="small"
            width="100%"
            pagination={false}
            // style={{
            //     maxHeight: tableHeight,
            //     overflowX: "scroll"
            // }}
            className="ant-custom-table"
            style={{ overflowX: "auto", marginLeft: "12px" }}
            rowKey={(record) => (record.id)}
        >
            <Table.Column
                title={<FormattedMessage id="drug.list.batch" />}
                dataIndex="batchNumber"
                key="batchNumber"
                ellipsis
                width={250}
                render={(v) => (v ? v : "-")}
            />
            {
                medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(status =>
                (
                    <Table.Column
                        title={status.label}
                        dataIndex={status.value}
                        key={status.value}
                        width={120}
                        render={(value, record, index) =>
                            record[status.value] ? record[status.value].count : 0
                        }
                    />
                )
                )
            }
        </Table>
    }


    const expandedDetail = (it, drug) => {
        return <Spin spinning={false}>
            <Table
                dataSource={it.batchs[drug.name + (drug.id !== "" ? drug.id : " " + drug.spec)]}
                // size="small"
                width="100%"
                pagination={false}
                // style={{
                //     maxHeight: tableHeight,
                //     overflowX: "scroll"
                // }}
                className="ant-custom-table"
                style={{ overflowX: "auto", marginLeft: "12px" }}
                rowKey={(record) => (record.id)}
            >
                <Table.Column
                    title={<FormattedMessage id="drug.list.batch" />}
                    dataIndex="batchNumber"
                    key="batchNumber"
                    ellipsis
                    width={250}
                    render={(v) => (v ? v : "-")}
                />
                <Table.Column
                    title={<FormattedMessage id="drug.configure.spec" />}
                    dataIndex="spec"
                    key="spec"
                    ellipsis
                    width={140}
                />
                {
                    medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(status =>
                    (
                        <Table.Column
                            title={status.label}
                            dataIndex={status.value}
                            key={status.value}
                            width={120}
                            render={(value, record, index) =>
                                record[status.value] ? record[status.value].count : 0
                            }
                        />
                    )
                    )
                }
            </Table>
        </Spin>

            ;
    }



    React.useEffect(
        search,
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [selectSite, page.currentPage, page.pageSize]
    );
    React.useEffect(
        runSiteForecastFun,
        []
    );
    return (
        <React.Fragment>
            <Row gutter={8}>

                <Col xs={24} sm={24} md={24} lg={24} className="mar-ver-5">
                    <Form.Item style={{ width: "100%" }} label={<FormattedMessage id="common.site" />}>
                        <Select style={{ width: "200px" }}
                            dropdownStyle={{ maxWidth: 400 }}
                            dropdownMatchSelectWidth={false}
                            allowClear showSearch mode="multiple" value={selectSite}
                            // 如果发现下拉菜单跟随页面滚动，或者需要在其他弹层中触发 Select，请尝试使用 getPopupContainer={triggerNode => triggerNode.parentElement} 将下拉弹层渲染节点固定在触发器的父元素中。
                            getPopupContainer={(triggerNode) => triggerNode.parentElement}
                            filterOption={(input, option) => {
                                const childrenText = option.props.children;
                                if (typeof childrenText === 'string') {
                                    return childrenText.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                }
                                return false;
                            }}
                            onChange={setSelectSite}
                            showArrow={true}
                            placeholder={<span style={{ color: "#1D2129" }}><FormattedMessage
                                id="common.all" /></span>}
                        >
                            {
                                props.sites.map(
                                    it => (

                                        <Select.Option key={it.id} value={it.id}>
                                            {/* <Tooltip title={it.number + "-" + it.name}>{it.number + "-" + it.name}</Tooltip> */}
                                            {(it.number ? it.number + "-" : "") + it.name}
                                        </Select.Option>

                                    )
                                )
                            }
                        </Select>
                    </Form.Item>
                </Col>

                {
                    siteList.length ?
                        <React.Fragment>
                            <Col xs={24} sm={24} md={24} lg={24}>
                                <Spin spinning={loading}>
                                    <Collapse key="sites"
                                        // defaultActiveKey={defaultKeys}
                                        activeKey={defaultKeys}
                                        expandIcon={() => null}
                                        accordion={false}
                                        bordered={false}
                                        className="ant-collapse ant-custom-item"
                                    >
                                        {
                                            siteList.map(it => (
                                                <Collapse.Panel
                                                    header={it.number + "-" + it.name}
                                                    key={it.id}
                                                    defaultActive={false}
                                                    className="custom-collapse"
                                                >
                                                    {siteForecast !== null && siteForecast.length > 0 &&
                                                        permissions(auth.project.permissions, "operation.supply.site.medicine.summary.formula") &&
                                                        siteForecast?.find(value => value.id === it.id)?.date !== null && siteForecast?.find(value => value.id === it.id)?.date !== "" && siteForecast?.find(value => value.id === it.id)?.date !== "-" &&
                                                        <Row style={{ marginBottom: 16, background: "#165DFF1A", height: 32 }}>
                                                            <svg className="iconfont" width={16} height={16} style={{ marginLeft: 8, marginTop: 8, marginRight: 4, color: "#165DFF" }}>
                                                                <use xlinkHref="#icon-xinxitishi1"></use>
                                                            </svg>
                                                            <div style={{ marginTop: 4 }}>
                                                                <FormattedMessage id={"projects.sitePharmacy.forecast"}></FormattedMessage>:<span style={{ paddingLeft: 4, fontWeight: 400 }}>{siteForecast?.find(value => value.id === it.id)?.date}</span>
                                                            </div>
                                                        </Row>
                                                    }
                                                    {
                                                        <Table
                                                            dataSource={(it.drugs && it.drugs.length) ? it.drugs : []}
                                                            width="100%"
                                                            pagination={false}
                                                            className="ant-custom-thead"
                                                            style={{ overflowX: "auto" }}
                                                            rowKey={(record) => (it.id + record.name + " " + record.spec)}
                                                            expandable={{
                                                                expandedRowRender: record => expandedDetail(it, record),
                                                                expandIcon: ({ expanded, onExpand, record }) =>
                                                                    expanded ? (
                                                                        <CaretDownOutlined onClick={e => onExpand(record, e)} />
                                                                    ) : (
                                                                        <CaretRightOutlined onClick={e => onExpand(record, e)} />
                                                                    ),
                                                                onExpand: (expanded, record) => onExpandDetail(it, record),
                                                                columnWidth: 30,
                                                            }}
                                                        >
                                                            <Table.Column
                                                                title={<FormattedMessage id="drug.list.name" />}
                                                                dataIndex="name"
                                                                key="name"
                                                                ellipsis
                                                                width={250}
                                                            />
                                                            <Table.Column
                                                                title={<FormattedMessage id="drug.configure.spec" />}
                                                                dataIndex="spec"
                                                                key="spec"
                                                                ellipsis
                                                                width={140}
                                                            />
                                                            {
                                                                medicineStatusAliData(auth.codeRule, auth.project.info.research_attribute, ali).map(item =>
                                                                (
                                                                    <Table.Column
                                                                        title={item.label}
                                                                        dataIndex={item.value}
                                                                        key={item.value}
                                                                        width={140}
                                                                        render={(value, record, index) =>
                                                                            record[item.value] ? record[item.value].count : 0
                                                                        }
                                                                    />
                                                                )
                                                                )
                                                            }
                                                        </Table>


                                                        // <Collapse key={"drug" + it.id}
                                                        //         style={{backgroundColor: "#EDECECEC"}}>
                                                        //     {
                                                        //         it.drugs.map(drug => (
                                                        //             <Collapse.Panel key={drug.name+drug.id}
                                                        //                             header={drug.name}
                                                        //                             extra={summaryStatus(drug)}>
                                                        //                 {
                                                        //                     it.batchs && it.batchs[drug.name+drug.id] ?
                                                        //                         <Table
                                                        //                             dataSource={it.batchs[drug.name+drug.id]}
                                                        //                             size="small"
                                                        //                             width="100%"
                                                        //                             pagination={false}
                                                        //                             style={{
                                                        //                                 maxHeight: tableHeight,
                                                        //                                 overflowX: "scroll"
                                                        //                             }}
                                                        //                             rowKey={(record) => (record.id)}
                                                        //                         >
                                                        //                             <Table.Column
                                                        //                                 title={<FormattedMessage
                                                        //                                     id="drug.list.batch"/>}
                                                        //                                 dataIndex="batchNumber"
                                                        //                                 key="batchNumber"
                                                        //                                 ellipsis
                                                        //                                 width={250}
                                                        //                             />
                                                        //                             {
                                                        //                                 medicineStatusData(auth.codeRule, auth.project.info.research_attribute).map(status =>
                                                        //                                     (
                                                        //                                         <Table.Column
                                                        //                                             title={status.label}
                                                        //                                             dataIndex={status.value}
                                                        //                                             key={status.value}
                                                        //                                             width={120}
                                                        //                                             render={(text, record, index) => (record[status.value] ? record[status.value] : 0)}
                                                        //                                         />
                                                        //                                     )
                                                        //                                 )
                                                        //                             }
                                                        //                         </Table>
                                                        //                         :
                                                        //                         <Empty
                                                        //                             image={Empty.PRESENTED_IMAGE_SIMPLE}/>
                                                        //                 }
                                                        //             </Collapse.Panel>
                                                        //         ))
                                                        //     }
                                                        // </Collapse>

                                                        // :
                                                        // <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>
                                                    }
                                                </Collapse.Panel>
                                            ))
                                        }
                                    </Collapse>
                                </Spin>
                            </Col>
                            {/*<Col xs={24} sm={24} md={24} lg={24} className="pad-top-10">*/}
                            {/*    <Pagination*/}
                            {/*        hideOnSinglePage={false}*/}
                            {/*        className="text-right"*/}
                            {/*        current={currentPage}*/}
                            {/*        pageSize={pageSize}*/}
                            {/*        pageSizeOptions={['10', '20', '50', '100']}*/}
                            {/*        total={total}*/}
                            {/*        showSizeChanger*/}
                            {/*        showTotal={(total, range) => (`${range[0]} - ${range[1]} / ${total}`)}*/}
                            {/*        onChange={(page, pageSize) => {*/}
                            {/*            setCurrentPage(page);*/}
                            {/*        }}*/}
                            {/*        onShowSizeChange={(current, size) => {*/}
                            {/*            setCurrentPage(1);*/}
                            {/*            setPageSize(size);*/}
                            {/*        }}*/}
                            {/*    />*/}
                            {/*</Col>*/}
                        </React.Fragment>
                        :
                        <Col xs={24} sm={24} md={24} lg={24}>
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description={<FormattedMessage id="projects.sitePharmacy.no.site" />}
                            />
                        </Col>
                }
            </Row>
            <PaginationView />
        </React.Fragment>
    )
}
