import React from "react";
import {Form, Input, message, Modal} from "antd";
import {useFetch} from "../../../../hooks/request";
import {useIntl} from "react-intl";
import {useSafeState} from "ahooks";
import {updateMedicineStatus} from "../../../../api/medicine";


export const Reason = (props) => {
    const [isVisible, setIsVisible] = useSafeState(false);
    const [medicineId, setMedicineId] = useSafeState(null);
    const [submitting, setSubmitting] = useSafeState(false);
    const [status, setStatus] = useSafeState(null);
    const [form] = Form.useForm();
    const intl = useIntl();
    const {formatMessage} = intl;
    const [title, setTitle] = useSafeState(null);

    // 展示对话框
    const cancel = () => {
        setIsVisible(false);
    };
    const {runAsync: run_updateMedicineStatus} = useFetch(updateMedicineStatus, {manual: true})
    const save = () => {
        form.validateFields().then(values => {
            setSubmitting(true);
            run_updateMedicineStatus({medicineIds: [medicineId], status: status, ...values}).then((resp) => {
                message.success(resp.msg);
                hide();
                props.refresh();
            });
        }).catch(errors => {
            setSubmitting(false);
        })
    }

    const show = (id, status) => {
        form.resetFields()
        setIsVisible(true)
        if (id) {
            setMedicineId(id);
        }
        if (status) {
            setStatus(status);
            if (status === 6) {//丢失、作废
                setTitle(formatMessage({id: 'medicine.status.lose'}))
            } else if (status === 1) {//设为可用
                setTitle(formatMessage({id: 'drug.list.setUse'}))
            }
        }

    }
    React.useImperativeHandle(props.bind, () => ({show}));

    const hide = () => {
        setIsVisible(false);
        form.resetFields();
    };
    return (
        <Modal  width={500} title={title} visible={isVisible} onOk={save} okText={formatMessage({ id: 'common.ok' })} onCancel={cancel} loading={submitting}
               centered>
            <Form form={form}>
                <Form.Item label={formatMessage({id: 'drug.freeze.reason'})} name="reason" className="mar-ver-5"
                           rules={[{required: true}]}>
                    <Input.TextArea allowClear autoSize={{minRows: 3, maxRows: 10}}/>
                </Form.Item>
            </Form>
        </Modal>

    );
};