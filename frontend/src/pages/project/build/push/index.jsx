import React from "react";
import Footer from "../../../main/layout/footer";
import { PaginationView } from "../../../../components/pagination";
import { PageContextProvider } from "../../../../context/page";
import { PushListMain } from "./main";
import { Tabs } from "antd";
import { useAuth } from "../../../../context/auth";

export const PushList = (props) => {
    const auth = useAuth();
    const cohorts = auth.env?.cohorts;
    const projectType = auth.project.info.type;

    return (
        <PageContextProvider>
            {projectType === 1 ? (
                <>
                    <PushListMain />
                    <Footer>
                        <PaginationView />
                    </Footer>
                </>
            ) : (
                <Tabs
                    destroyInactiveTabPane={true}
                    size="small"
                    defaultActiveKey="1"
                    tabPosition="top"
                    items={cohorts.map((cohort, cohort_index) => {
                        const id = cohort.id;
                        return {
                            label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                            key: id,
                            children: <>
                                <PushListMain cohort={cohort} />
                                <Footer>
                                    <PaginationView />
                                </Footer>
                            </>,
                        };
                    })}
                ></Tabs>
            )}
        </PageContextProvider>

    )
};