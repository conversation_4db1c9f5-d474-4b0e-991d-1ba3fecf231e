import React from "react";
import {useIntl} from "react-intl";
import {Button, Form, InputNumber, message, Modal, Spin, Table, Row, Col, Select, notification} from "antd";
import {permissions, permissionsCohort} from '../../../../tools/permission'
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {getFactorCombination, updateFactor, addFactor, delFactor} from "../../../../api/randomization";
import {useGlobal} from "../../../../context/global";
import { TableCellPopover } from "components/popover";
import { CustomConfirmModal } from "components/modal";
import { CloseCircleFilled } from "@ant-design/icons";
import _ from "lodash";


export const RandomizationFactor = (props:any) => {

    const g = useGlobal()

    const auth = useAuth();

    const intl = useIntl();
    const {formatMessage} = intl;

    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const customerId = auth.customerId;
    const [data, setData] = useSafeState<any>([]);              // 模拟数据
    const [height, setHeight] = useSafeState<any>(window.innerHeight - 238);
    const [isModalVisible, setIsModalVisible] = useSafeState<any>(false);
    const [addModalVisable,setAddModalVisable] = useSafeState<any>(false);
    const [factorOptions,setFactorOptions] = useSafeState<any>([]);
    // const [ret, setRet] = useSafeState(true);   //
    const [record, setRecord] = useSafeState<any>(true);   //
    const [form] = Form.useForm();
    const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
    const projectStatus = auth.project.status ? auth.project.status : 0;

    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState({});

    const column :any = [
        {title: formatMessage({id: 'common.serial'}), dataIndex:"#", key:"#" ,width:70, render:(value: any, record: any, index: number) => (index + 1) },
        {
            title: formatMessage({id: 'randomization.config.factorName'}), dataIndex: 'layered_factors', key: 'layered_factors',
            render:
            (value: any, record: any, index: any) => {
                if (!value) return <></>;
                            const items: string[] = [];
                            (value as []).forEach((it: any) => {
                                items.push(it.label);
                            });
                            return (
                                <TableCellPopover
                                    title={
                                        <span
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                            }}
                                        >
                                            <svg
                                                className="iconfont"
                                                width={16}
                                                height={16}
                                            >
                                                <use xlinkHref="#icon-quanbuxuanxing"></use>
                                            </svg>
                                            <span style={{ marginLeft: 8 }}>
                                                {intl.formatMessage({
                                                    id: "randomization.config.factor",
                                                })}
                                            </span>
                                        </span>
                                    }
                                    items={items}
                                />
                            );
            }
        },
        {
            title: formatMessage({id: 'randomization.config.factor.label.option.value'}), dataIndex: 'layered_factors', key: 'layered_factors',
            render:
                (value: any, record: any, index: any) => {
                    if (!value) return <></>;
                    const items: string[] = [];
                    (value as []).forEach((it: any) => {
                        items.push(it.text);
                    });
                    return (
                        <TableCellPopover
                            title={
                                <span
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                            <svg
                                                className="iconfont"
                                                width={16}
                                                height={16}
                                            >
                                                <use xlinkHref="#icon-quanbuxuanxing"></use>
                                            </svg>
                                            <span style={{ marginLeft: 8 }}>
                                                {intl.formatMessage({
                                                    id: "randomization.config.factor",
                                                })}
                                            </span>
                                        </span>
                            }
                            items={items}
                        />
                    );
                }
        },
        {title: formatMessage({id: 'projects.randomization.actualPeople'}), dataIndex: 'count', key: 'count'},
        {title: formatMessage({id: 'projects.randomization.planPeople'}), dataIndex: 'estimate_number', key: 'estimate_number'},
        {title: formatMessage({id: 'projects.randomization.alertPeople'}), dataIndex: 'warn_number', key: 'warn_number'},
        {title: formatMessage({id: 'common.operation'}), align: "left",width: 180,
            render: (value: any, record: any, index: any) => (
                <React.Fragment>
                    {
                        permissionsCohort(auth.project.permissions,"operation.build.randomization.factor-in.set-people",props.cohort?.status) && projectStatus !== 2 &&
                            <Button size="small" type="link" onClick={() => {showModal(record)}}>{formatMessage({id: 'common.edit'})}</Button>
                    }
                    {
                        permissionsCohort(auth.project.permissions,"operation.build.randomization.factor-in.delete",props.cohort?.status) && projectStatus !== 2 &&
                            <Button size="small" type="link" onClick={() => {remove(record)}}>{formatMessage({id: 'common.delete'})}</Button>
                    }
                </React.Fragment>
            )
        }
    ];

    const showModal = (it:any) => {
        setRecord(it)
        if (it.layered_factors !== null && it.layered_factors.length>0 || it.estimate_number!==0 || it.warn_number !== 0){
            var layeredFactors = [];
            for (let i = 0; i < it.layered_factors.length; i++) {
                layeredFactors[i] = it.layered_factors[i].name+" "+it.layered_factors[i].value
            }
            form.setFieldsValue({layeredFactors:layeredFactors, estimateNumber:it.estimate_number,warnNumber: it.warn_number});
            setOldData({layeredFactors:layeredFactors, estimateNumber:it.estimate_number,warnNumber: it.warn_number});
        }
        setIsModalVisible(true);
    };

    const showAddModal = () =>{
        setAddModalVisable(true);
    };

    const remove = (record: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.delete" }),
            okText: formatMessage({ id: "common.ok" }),
            cancelText: formatMessage({id: 'common.cancel'}),
            onOk: () =>
                deleteFactorRun({ id:props.record,combinationId:record.id }).then(() => {
                    list();
                }),
        });
    };

    const handleOk = () => {
        form.validateFields().then( (value:any) => {
                var  mulFactors = value.layeredFactors
                let factorArray:any = [];
                var check:boolean = true;
                mulFactors.forEach((it: any) => {
                    var mulFactorSplit =  it.split(" ")
                    if (factorArray.includes(mulFactorSplit[0])) {
                        notification.open({
                            message: (
                                <div
                                    style={{
                                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                        fontSize: "14px",
                                    }}
                                >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                    {formatMessage({ id: "common.fail" })}
                                </div>
                            ),
                            description: (
                                <div
                                    style={{
                                        paddingLeft: "20px",
                                        color: "#646566",
                                    }}
                                >
                                    {formatMessage({ id: "randomization.config.factor.addError" })}
                                </div>
                            ),
                            duration: 3,
                            placement: "top",
                            style: {
                                width: "720px",
                                background: "#FEF0EF",
                                borderRadius: "4px",
                            },
                        });
                        check = false
                        return check
                    }else{
                        factorArray.push(mulFactorSplit[0])
                    }
                });
                if (check) {
                    updateFactorRun({id:record.id},value).then(
                        (data: any) => {
                            message.success(data.msg)
                            list();
                            form.resetFields();
                            setIsModalVisible(false);
                        }
                    )
                }

            }
        )
    };

    const addFactors =() =>{
        form.validateFields().then( (value:any) => {
                var  mulFactors = value.mulFactors
                let factorArray:any = [];
                var check:boolean = true;
                mulFactors.forEach((it: any) => {
                    var mulFactorSplit =  it.split(" ")
                    if (factorArray.includes(mulFactorSplit[0])) {
                        notification.open({
                            message: (
                                <div
                                    style={{
                                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                        fontSize: "14px",
                                    }}
                                >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                    {formatMessage({ id: "common.fail" })}
                                </div>
                            ),
                            description: (
                                <div
                                    style={{
                                        paddingLeft: "20px",
                                        color: "#646566",
                                    }}
                                >
                                    {formatMessage({ id: "randomization.config.factor.addError" })}
                                </div>
                            ),
                            duration: 3,
                            placement: "top",
                            style: {
                                width: "720px",
                                background: "#FEF0EF",
                                borderRadius: "4px",
                            },
                        });
                        check = false
                        return check
                    }else{
                        factorArray.push(mulFactorSplit[0])
                    }
                });
                if (check){
                    addFactorRun({id:props.record},value).then(
                        (data: any) => {
                            message.success(data.msg)
                            list();
                            form.resetFields();
                            setAddModalVisable(false);
                        }
                    )
                }
            }
        )
    }

    const handleCancel = () => {
        form.resetFields();
        setSend(true);
        setOldData({});
        setIsModalVisible(false);
    };

    const formChange = () => {
        const a = oldData;;
        // console.log("1===" + JSON.stringify(a)); 
        let b = form.getFieldsValue();
        // console.log("2===" + JSON.stringify(b)); 
        if (!compareObjects(a, b)) {
            setSend(false);
        } else {
            setSend(true);
        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) { 
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                if (!arraysAreEqual(obj1[key], obj2[key])) {
                    return false;
                }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }


    const cancelAddModal = () =>{
        form.resetFields();
        setAddModalVisable(false);
    }

    const handleResize = () => {
        setHeight(window.innerHeight - 238);
    };
    React.useEffect(() => {
            window.addEventListener("resize", handleResize);
            return () => window.removeEventListener("resize", handleResize);
        }
    );

    const {runAsync: getFactorCombinationRun, loading:getFactorCombinationLoading} = useFetch(getFactorCombination, {manual: true})
    const {runAsync: updateFactorRun} = useFetch(updateFactor, {manual: true})
    const {runAsync:addFactorRun} = useFetch(addFactor, {manual: true})
    const {runAsync:deleteFactorRun} = useFetch(delFactor,{manual:true})



    const list = React.useCallback(() => {
        getFactorCombinationRun({id:props.record}).then(
            (result:any) => {
                var resultData = result.data
                setData(resultData.data);
                var factorsData = resultData.factors;
                var options:any = [];
                factorsData?.forEach((f:any) => {
                    f?.options.forEach((e:any) => {
                        options.push({
                            label: f.label+ ":" +e.label,
                            value: f.name+" "+e.value,
                        })
                    })
                });              
                setFactorOptions(options)
            }
        )
        // eslint-disable-next-line react-hooks/exhaustive-deps
    },[customerId,props,projectId,envId,cohortId])
    React.useEffect(list,[list])

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 8 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 17 },
        },
    }


    return (
        <React.Fragment>
            <Spin spinning={getFactorCombinationLoading}>
                <Row justify='end'  style={{marginTop: 6, marginBottom: 16}}>
                    <Col>
                        <Row>
                            <Col className="mar-lft-10">
                                {
                                   permissionsCohort(auth.project.permissions, "operation.build.randomization.factor-in.add",props.cohort?.status) && projectStatus !==2 &&
                                        <Button onClick={() => showAddModal()}>
                                            {formatMessage({id: 'common.addTo'})}
                                        </Button>
                                }
                            </Col>
                        </Row>
                    </Col>
                </Row>
                <Table
                    size="small"
                    className="mar-top-10"
                    dataSource={data}
                    scroll={{ y: height }}
                    pagination={false}
                    rowKey={(record) => (record.id)}
                    columns={column}
                >
                </Table>
            </Spin>
            <Modal
                title={formatMessage({id: 'common.edit'})}
                open={isModalVisible} onOk={handleOk} okText={formatMessage({ id: 'common.ok' })} onCancel={handleCancel}
                destroyOnClose={true}
                className='custom-small-modal'
                centered
                maskClosable={false}
                okButtonProps={{disabled: send}}
                
            >
                <Form layout="horizontal" onValuesChange={formChange} form={form} {...formItemLayout}>
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: 'projects.randomization.selectFactor'})} name="layeredFactors">
                        <Select mode="multiple" placeholder={formatMessage({ id: 'placeholder.select.common' })} showArrow={true} showSearch={false}>
                            {
                                factorOptions.map((e:any)=><Select.Option value={e.value}>{e.label}</Select.Option>)
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: 'projects.randomization.planPeople'})} name="estimateNumber">
                        <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
                    </Form.Item>
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: 'projects.randomization.alertPeople'})} name="warnNumber">
                        <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
                    </Form.Item>
                </Form>
            </Modal>
            <Modal
                title={formatMessage({id: 'common.addTo'})}
                open={addModalVisable} onOk={addFactors} okText={formatMessage({ id: 'common.ok' })} onCancel={cancelAddModal}
                destroyOnClose={true}
                className='custom-small-modal'
                centered
                maskClosable={false}
            >
                <Form layout="horizontal" form={form} {...formItemLayout}>
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: 'projects.randomization.selectFactor'})} name="mulFactors">
                        <Select mode="multiple" placeholder={formatMessage({ id: 'placeholder.select.common' })} showArrow={true} showSearch={false}>
                            {
                                factorOptions.map((e:any)=><Select.Option value={e.value}>{e.label}</Select.Option>)
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: 'projects.randomization.planPeople'})} name="estimateNumber">
                        <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
                    </Form.Item>
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: 'projects.randomization.alertPeople'})} name="warnNumber">
                        <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
                    </Form.Item>
                </Form>
            </Modal>

        </React.Fragment>
    )
};
