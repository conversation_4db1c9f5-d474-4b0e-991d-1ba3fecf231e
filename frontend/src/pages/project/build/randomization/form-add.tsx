import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  TimePicker,
  Tooltip,
} from "antd";
import {MinusCircleFilled, PlusOutlined, QuestionCircleFilled,} from "@ant-design/icons";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {addForm, updateForm} from "../../../../api/form";
import {useGlobal} from "../../../../context/global";
import {controlTypes} from "../../../../data/data";
import styled from "@emotion/styled";
import _ from "lodash";
import {CustomDatePicker} from "../../../../components/CustomDatePicker";
import {CustomTimePicker} from "../../../../components/CustomTimePicker";
import dayjs from "dayjs";
import {message as en} from "../../../../locales/en";
import {CustomDatePickerEn} from "../../../../components/CustomDatePickerEn";

export const FormAdd = (props: any) => {
  const auth = useAuth();
  const intl = useIntl();
  const { formatMessage } = intl;

  const [visible, setVisible] = useSafeState<any>(false);
  const [fieldId, setFieldId] = useSafeState<any>(null);
  const [fieldName, setFieldName] = useSafeState<any>("");
  const [fields, setFields] = useSafeState<any>(null);
  const [id, setId] = useSafeState<any>(null);
  const [type, setType] = useSafeState<any>(null);
  const [options, setOptions] = useSafeState<any>(null);
  const [label, setLabel] = useSafeState<any>(null);
  const [variable, setVariable] = useSafeState<any>(null);
  const [used, setUsed] = useSafeState<any>(false);
  const [format, setFormat] = useSafeState<any>(null);
  const [picker, setPicker] = useSafeState<any>("date");
  const [range, setRange] = useSafeState<any>(null);
  const [dateRange, setDateRange] = useSafeState<any>({ min: "", max: "" });
  const [fieldObj, setFieldObj] = useSafeState<any>(null);
  const [form] = Form.useForm();
  const [send, setSend] = useSafeState(true);
  const [oldData, setOldData] = useSafeState(null);

  const connectEdc = auth.project ? auth.project.connectEdc : null;
  const [applicationType, setApplicationType] = useSafeState<any>(1);
  const { runAsync: addFormRun, loading: addFormLoading } = useFetch(addForm, {
    manual: true,
  });
  const { runAsync: updateFormRun, loading: updateFormLoading } = useFetch(
    updateForm,
    { manual: true }
  );

  const initOptions = [
    {
      label: "form.control.type.options.one",
      value: formatMessage({ id: "form.control.type.options.one" }),
    },
    {
      label: "form.control.type.options.two",
      value: formatMessage({ id: "form.control.type.options.two" }),
    },
    {
      label: "form.control.type.options.three",
      value: (
        <Tooltip
          placement="top"
          overlayStyle={{ maxWidth: "500px" }}
          title={
            <>
              <Row>
                {formatMessage({
                  id: "form.control.type.options.tip.one",
                })}
              </Row>
              <Row>
                {formatMessage({
                  id: "form.control.type.options.tip.two",
                })}
              </Row>
            </>
          }
        >
          {formatMessage({ id: "form.control.type.options.three" })}
        </Tooltip>
      ),
    },
  ];
  const [selectOption, setSelectOption] = useSafeState<any>([]);
  const [optionValues, setOptionValues] = useSafeState(
    initOptions.filter(
      (it) => selectOption.findIndex((v: any) => v === it.label) === -1
    )
  );
  const [selectIndex, setSelectIndex] = useSafeState(-1);
  const handleChange = (value: string) => {
    const arr = selectOption;
    if (arr.indexOf(value) === -1) {
      arr.push(value);
      setSelectOption(arr);
    }
    setSelectIndex(-1);
    // setOptionValues(initOptions.filter(it => arr.findIndex((v: any) => v === it.value) === -1));
  };

  const CustomOptionAdder = (v: any) => {
    const [opForm] = Form.useForm();
    const onAdd = () => {
      opForm.validateFields().then((values) => {
        const arr = selectOption;
        const opValue = values.op;
        if (arr.indexOf(opValue) === -1) {
          arr.push(opValue);
          setSelectOption(arr);
        }
        var option: any[] = [];
        option = [...form.getFieldsValue().options];
        if (option.length !== 0) {
          // let optionNumber = 1;
          // // if (options && options.length > 0) {
          // //     let str = options[options.length - 1].value;
          // //     optionNumber = parseInt(str.slice(6)) + 1;
          // // }
          // console.log("kkk==" + JSON.stringify(form.getFieldsValue().options))
          // if (form.getFieldsValue() && form.getFieldsValue().options !== null && form.getFieldsValue().options.length > 0) {
          //     form.getFieldsValue().options.slice().reverse().some((item: any) => {
          //         if(item !== null && item !== undefined){
          //             let str = item.value;
          //             optionNumber = parseInt(str.slice(6)) + 1;
          //             return true; // 结束循环
          //         }
          //     });
          // }
          const opson = {
            label: opValue,
            // value: "option" + (optionNumber)
          };
          for (let i = 0; i < option.length; i++) {
            if (i === selectIndex) {
              option[selectIndex] = opson;
            }
          }
        }
        form.setFieldsValue({ ...form.getFieldsValue(), options: option });
        setSelectIndex(-1);
      });
    };

    const validator = {
      validator: (_: any, value: string) => {
        if (
          value &&
          value.trim().length > 0 &&
          selectOption.findIndex((item: any) => item === value) === -1 &&
          optionValues.findIndex((item) => item.value === value) === -1
        ) {
          return Promise.resolve();
        } else if (!value || value.trim().length === 0) {
          return Promise.reject(
            formatMessage({ id: "common.required.prefix" }) +
              formatMessage({ id: "form.control.type.options" })
          );
        } else {
          return Promise.reject(
            formatMessage({ id: "form.control.type.options.duplicate" })
          );
        }
      },
    };

    return (
      <Form style={{ margin: "0 10px", width: "100%" }} form={opForm}>
        <Form.Item name="op" rules={[validator]}>
          <Input
            placeholder={formatMessage({ id: "placeholder.input.common" })}
            addonAfter={
              <Button type="link" size="small" onClick={onAdd}>
                <PlusOutlined /> <FormattedMessage id={"common.addTo"} />
              </Button>
            }
            onBlur={(e) => {
              if (!e.target.value || e.target.value.trim().length === 0) {
                // form.setFields([{name: 'op', errors: null}])
              }
            }}
          />
        </Form.Item>
      </Form>
    );
  };

  const show = (fields: any, id: any, field: any) => {
    form.setFieldsValue({ label: null, variable: null });
    setLabel(null);
    setVariable(null);
    setVisible(true);
    setSelectOption([]);
    setOptionValues(
      initOptions.filter(
        (it) => selectOption.findIndex((item: any) => item === it.label) === -1
      )
    );
    if (id) {
      setId(id);
    }
    setSend(true);
    if (field) {
      if (field.dateRange != null) {
        if (field.type === "datePicker") {
          if (field.dateRange.min != undefined && field.dateRange.min != "") {
            field.dateRange.min = dayjs(field.dateRange.min, field.dateFormat);
          }
          if (field.dateRange.max != undefined && field.dateRange.max != "") {
            field.dateRange.max === "currentTime"
              ? formatMessage({ id: "common.current.time" })
              : dayjs(field.dateRange.max, field.dateFormat);
          }
        }
        if (field.type === "timePicker") {
          if (field.dateRange.min != undefined && field.dateRange.min != "") {
            field.dateRange.min = dayjs(field.dateRange.min, field.timeFormat);
          }
          if (field.dateRange.max != undefined && field.dateRange.max != "") {
            field.dateRange.max === "currentTime"
              ? formatMessage({ id: "common.current.time" })
              : dayjs(field.dateRange.max, field.timeFormat);
          }
        }
        setDateRange({ min: field.dateRange.min, max: field.dateRange.max });
      }

      setOldData(field);
      if (field.status === null) {
        field.status = 1;
      }
      form.setFieldsValue({ ...field });
      setFieldObj({ ...field });
      setFieldName(field.name);
      if (field.type === "inputNumber"){
        setFormat(field.formatType)
    }
      if (field.type === "datePicker") {
        setFormat(field.dateFormat);
      }
      if (field.type === "timePicker") {
        setFormat(field.timeFormat);
      }
      if (field.dateFormat === "YYYY") {
        setPicker("year");
      } else if (
        field.dateFormat === "YYYY-MM" ||
        field.dateFormat === "MM-YYYY" ||
          field.dateFormat === "MMM-YYYY"
      ) {
        setPicker("month");
      }
      setApplicationType(field.applicationType);
      setType(field.type);
      setFieldId(field.id);
      setOptions(field.options);
      setLabel(field.label);
      setVariable(field.variable);
      setUsed(field.used);
      form.setFieldsValue({ label: field.label, variable: field.variable });
    }
    if (fields) {
      setFields(fields);
    }
  };

  const hide = () => {
    setOldData(null);
    setSend(true);
    props.refresh();
    setVisible(false);
    form.resetFields();
    setRange(null);
    setFieldObj(null);
    setFieldId(null);
    setFormat(null);
    setPicker("date");
    setType(null);
    form.setFieldsValue({ label: null });
    form.setFieldsValue({ variable: null });
    setLabel(null);
    setVariable(null);
    setUsed(false);
    setApplicationType(1);
    setSelectOption([]);
  };

  const formChange = () => {
    //fixme 暂时去掉，有嵌套对比没完善
    // if(fieldId){
    //     const a = oldData;
    //     let b = form.getFieldsValue();
    //     if (!compareObjects(a, b)) {
    //         setSend(false);
    //     } else {
    //         setSend(true);
    //     }
    // }
  };

  //比较两个JavaScript对象是否相同
  function compareObjects(obj1: any, obj2: any) {
    for (let key in obj1) {
      if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
        if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
          if (!arraysAreEqual(obj1[key], obj2[key])) {
            return false;
          }
        } else if (
          typeof obj1[key] === "object" &&
          typeof obj2[key] === "object"
        ) {
          if (!compareObjects(obj1[key], obj2[key])) {
            return false;
          }
        } else {
          if (obj1[key] !== obj2[key]) {
            return false;
          }
        }
      }
    }
    return true;
  }

  //比较两个数组是否相同
  function arraysAreEqual(arr1: any, arr2: any) {
    // 检查数组长度是否相同
    if (arr1.length !== arr2.length) {
      return false;
    }

    const a = _.cloneDeep(arr1);
    const b = _.cloneDeep(arr2);
    // 将数组转换为字符串并比较
    const str1 = JSON.stringify(a.sort());
    const str2 = JSON.stringify(b.sort());

    return str1 === str2;
  }

  const save = () => {
    // 编辑
    if (fieldId != null) {
      form
        .validateFields()
        .then((values) => {
          if (values.options != null) {
            //原有option最大的value
            let optionNumber = 1;
            if (options && options.length > 0) {
              let str = options[options.length - 1].value;
              optionNumber = parseInt(str.slice(6)) + 1;
            }

            values.options.forEach((option: any) => {
              if (option.value == null) {
                option.value = "option" + optionNumber;
                optionNumber++;
              }
            });
          }
          if (values.dateRange != null) {

            if (values.dateRange.min !== undefined && values.dateRange.min !== null) {
              values.dateRange.min =
                typeof values.dateRange.min === "string"
                  ? values.dateRange.min
                  : values.dateRange.min.format(format);
            }else {
              values.dateRange.min = ""
            }

            if (values.dateRange.max !== undefined && values.dateRange.max !== null) {
              values.dateRange.max =
                values.dateRange.max === "currentTime" ||
                values.dateRange.max === "Current Time" ||
                values.dateRange.max === "当前时间"
                  ? "currentTime"
                  : typeof values.dateRange.max === "string"
                  ? values.dateRange.max
                  : values.dateRange.max.format(format);
            }else {
              values.dateRange.max = ""
            }
          }

          updateFormRun(
            { id, fieldId },
            { ...values, id: fieldId, name: fieldName }
          ).then(() => {
            hide();
          });
        })
        .catch(() => {});
    } else {
      let cohortId: any = null;
      if (props.cohort != null) {
        cohortId = props.cohort.id;
      }
      form
        .validateFields()
        .then((values) => {
          if (values.options != null) {
            let optionNumber = 1;
            values.options.forEach((option: any) => {
              if (option.value == null) {
                option.value = "option" + optionNumber;
                optionNumber++;
              }
            });
          } else {
            values.options = [];
          }
          let fieldName = "";
          if (connectEdc !== 1) {
            //不和EDC对接的，自动生成
            //处理fieldName的值
            let fieldNumber = 0;
            if (fields != null && fields.length > 0) {
              let serial = fields.map((field: any) =>
                parseInt(field.name.slice(5))
              );
              let number = Math.max(...serial);
              fieldNumber = number + 1;
            }
            fieldName = "field" + fieldNumber;
          } else {
            fieldName = values["name"];
          }
          if (values.dateRange != null) {
            if (values.dateRange.min !== undefined) {
              values.dateRange.min =
                typeof values.dateRange.min === "string"
                  ? values.dateRange.min
                  : values.dateRange.min.format(format);
            }
            if (values.dateRange.max !== undefined) {
              values.dateRange.max =
                values.dateRange.max === "currentTime" ||
                values.dateRange.max === "当前时间"
                  ? "currentTime"
                  : typeof values.dateRange.max === "string"
                  ? values.dateRange.max
                  : values.dateRange.max.format(format);
            }
          }
          addFormRun(
            { customerId: auth.customerId, envId: auth.env.id },
            {
              fields: [{ ...values, name: fieldName }],
              projectId: auth.project.id,
              cohortId: cohortId,
              customerId: auth.customerId,
              envId: auth.env.id,
            }
          ).then(() => {
            hide();
          });
        })
        .catch(() => {});
    }
  };

  const radioStyle = {
    display: "block",
    height: "30px",
    lineHeight: "30px",
  };

  const changeType = (type: string) => {
    setType(type);
    if (type !== "select" && type !== "checkbox" && type !== "radio") {
      form.setFieldsValue({ options: [] });
    }
    if (type !== "input" && type !== "inputNumber") {
      form.setFieldsValue({ digit: 0 });
      form.setFieldsValue({ accuracy: 0 });
    }
    setRange(null);
    setDateRange({ min: "", max: "" });
    form.setFieldsValue({
      format: null,
      formatType: null,
      variableFormat: null,
      length: null,
      range: undefined,
      dateRange: undefined,
    });
  };

  const changeFormat = (format: string) => {
    setFormat(format);
    if (format === "YYYY") {
      setPicker("year");
    } else if (format === "YYYY-MM" || format === "MM-YYYY" || format === "MMM-YYYY") {
      setPicker("month");
    } else {
      setPicker("date");
    }
    setRange(null);
    setDateRange({ min: "", max: "" });
    form.setFieldsValue({
      length: null,
      range: undefined,
      dateRange: undefined,
    });
  };

  const onChangeLabel = (value: any) => {
    setLabel(value);
    form.setFieldsValue({ label: value });
    formChange();
  };

  const onChangeVariable = (value: any) => {
    setVariable(value);
    form.setFieldsValue({ variable: value });
  };

  const g = useGlobal();
  const formItemLayout = {
    labelCol: { style: { width: g.lang === "en" ? "130px" : "100px" } },
  };
  function renderControlType(type: any) {
    // @ts-ignore
    return controlTypes.find((it: any) => it.value === type)?.label;
  }

  function renderFormat(type: any) {
    if (
      (type === "input" || type === "textArea") &&
      fieldObj?.formatType === "characterLength"
    ) {
      return formatMessage({ id: "form.control.type.format.characterLength" });
    }
    if (type === "inputNumber") {
      if (fieldObj?.formatType === "numberLength") {
        return formatMessage({ id: "form.control.type.format.numberLength" });
      }
      if (fieldObj?.formatType === "decimalLength") {
        return formatMessage({ id: "form.control.type.format.decimalLength" });
      }
    }
    if (type === "checkbox" && fieldObj?.formatType === "checkbox") {
      return formatMessage({ id: "form.control.type.format.checkbox" });
    }
    return "";
  }

  const ValidatorList = () => {
    const intl = useIntl();
    const { formatMessage } = intl;
    return {
      validator: async (_: any, options: any) => {
        if (!options || options.length === 0) {
          return Promise.reject(
            new Error(formatMessage({ id: "form.control.type.options.lack" }))
          );
        }
        let option = [...form.getFieldsValue().options];
        const arr: any[] = [];
        if (option !== undefined && option !== null && option.length > 0) {
          option.forEach((op: any) => {
            if (op?.label !== undefined) {
              arr.push(op.label);
            }
          });
        }
        var isIdentical = new Set(arr).size !== arr.length;
        if (applicationType === 3 && isIdentical) {
          return Promise.reject(
            new Error(
              formatMessage({
                id: "form.control.type.options.selectedRepeatedly",
              })
            )
          );
        }
        return Promise.resolve(); // 返回一个 resolved 状态的 Promise
      },
    };
  };

  React.useImperativeHandle(props.bind, () => ({ show }));

  const handleMinPicker = () => {
    if (fieldObj && fieldObj.dateRange) {
      if (fieldObj.dateRange.min) {
        if (fieldObj.dateRange.min === "currentTime") {
          return formatMessage({ id: "common.current.time" });
        } else if (typeof fieldObj.dateRange.min === "string") {
          return fieldObj.dateRange.min;
        } else {
          return dayjs(fieldObj.dateRange.min).format(format);
        }
      }
    }
    return "";
  };

  const handleMaxPicker = () => {
    if (fieldObj && fieldObj.dateRange) {
      if (fieldObj.dateRange.max) {
        if (fieldObj.dateRange.max === "currentTime") {
          return formatMessage({ id: "common.current.time" });
        } else if (typeof fieldObj.dateRange.max === "string") {
          return fieldObj.dateRange.max;
        } else {
          return dayjs(fieldObj.dateRange.max).format(format);
        }
      }
    }
    return "";
  };

  return (
    <React.Fragment>
      <Modal
        maskClosable={false}
        centered
        className="custom-small-modal"
        title={
          <FormattedMessage id={!fieldId ? "common.add" : "common.edit"} />
        }
        visible={visible}
        onCancel={hide}
        destroyOnClose={true}
        footer={
          <Row justify="end">
            <Col style={{ marginRight: "16px" }}>
              <Button onClick={hide}>
                <FormattedMessage id="common.cancel" />
              </Button>
            </Col>
            <Col>
              <Button
                // fixme 暂时去掉对比 disabled={fieldId?send:false}
                onClick={save}
                type="primary"
                loading={updateFormLoading || addFormLoading}
              >
                <FormattedMessage id="common.ok" />
              </Button>
            </Col>
          </Row>
        }
      >
        <Form form={form} onValuesChange={formChange} {...formItemLayout}>
          <Form.Item
            label={formatMessage({ id: "form.application.type" })}
            name="applicationType"
            initialValue={1}
          >
            {!!fieldId ? (
              applicationType === 1 ? (
                formatMessage({ id: "form.application.type.register" })
              ) : applicationType === 2 ? (
                formatMessage({ id: "form.application.type.formula" })
              ) : applicationType === 3 ? (
                  formatMessage({ id: "drug.configure.setting.dose.form.doseAdjustment" })
              ):(
                formatMessage({ id: "randomization.config.factor.calc" })
              )
            ) : (
              <StyledRadioGroup
                disabled={!!fieldId}
                onChange={(v) => {
                  setApplicationType(v.target.value);
                  if (v.target.value === 4){
                   form.setFieldsValue({ required: true });
                  }else {
                    form.setFieldsValue({ required: null });
                  }
                  form.setFieldsValue({ variable: null });
                  form.setFieldsValue({ type: null });
                  form.setFieldsValue({ options: [] });
                  form.setFieldsValue({ formatType: null });
                  form.setFieldsValue({ options: [] });
                  form.setFieldsValue({ label: null });
                  form.setFieldsValue({ variable: null });
                  form.setFieldsValue({ modifiable: null });

                  setLabel(null);
                  setRange(null);
                  setFieldObj(null);
                  setFieldId(null);
                  setFormat(null);
                  setPicker("date");
                  setType(null);
                  setSelectOption([]);
                  setOptionValues(
                    initOptions.filter(
                      (it) =>
                        selectOption.findIndex((v: any) => v === it.label) ===
                        -1
                    )
                  );
                }}
              >
                <Radio value={1}>
                  {formatMessage({ id: "form.application.type.register" })}
                </Radio>
                <Radio value={2}>
                  {formatMessage({ id: "form.application.type.formula" })}
                </Radio>
                <Radio value={3}>
                  {formatMessage({id: "drug.configure.setting.dose.form.doseAdjustment"})}
                </Radio>
                <Radio value={4}>
                  {formatMessage({id: "randomization.config.factor.calc"})}
                </Radio>
              </StyledRadioGroup>
            )}
          </Form.Item>
          <Form.Item
            name="label"
            rules={[{ required: !used }]}
            label={formatMessage({ id: "form.field.label" })}
          >
            {used === true ? (
              <div style={{ marginTop: 6 }}>{label}</div>
            ) : (
              <Input
                placeholder={formatMessage({ id: "placeholder.input.common" })}
                allowClear
                value={label}
                onChange={(v) => onChangeLabel(v.target.value)}
              />
            )}
            <Row
              style={{ backgroundColor: "#F8F9FA", height: 32, marginTop: 12 }}
            >
              {(applicationType === 1 || applicationType === 4) && (
                <Form.Item name="modifiable" valuePropName="checked">
                  <Checkbox
                    defaultChecked={false}
                    style={{ marginLeft: "12px" }}
                  >
                    {formatMessage({ id: "form.modify" })}{" "}
                  </Checkbox>
                </Form.Item>
              )}

              <Form.Item name="required" valuePropName="checked" >
                <Checkbox style={{ marginLeft: "12px" }} disabled={applicationType === 4}>
                  {formatMessage({ id: "form.required" })}
                </Checkbox>
              </Form.Item>
            </Row>
          </Form.Item>
          <Form.Item
              label={formatMessage({ id: "form.application.type.variable" })}
              name="variable"
              rules={[{ required: !used || variable == null || variable === ""}]}
          >
            {used === true ? (
                <div style={{ marginTop: 6 }}>{variable}</div>
            ) : (
                <Input
                    placeholder={formatMessage({
                      id: "placeholder.input.common",
                    })}
                    allowClear
                    value={variable}
                    onChange={(v) => onChangeVariable(v.target.value)}
                />
            )}
          </Form.Item>
          {/*控件类型*/}
          {applicationType === 2 ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type" })}
              name="type"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                renderControlType(type)
              ) : (
                <Select
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  onChange={changeType}
                  style={{ marginBottom: "16px" }}
                >
                  <Select.Option style={radioStyle} value={"inputNumber"}>
                    <FormattedMessage id="form.control.type.inputNumber" />
                  </Select.Option>
                </Select>
              )}
            </Form.Item>
          ) : applicationType === 1 ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type" })}
              name="type"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                renderControlType(type)
              ) : (
                <Select
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  onChange={changeType}
                  style={{ marginBottom: "16px" }}
                >
                  <Select.Option style={radioStyle} value={"input"}>
                    <FormattedMessage id="form.control.type.input" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"inputNumber"}>
                    <FormattedMessage id="form.control.type.inputNumber" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"textArea"}>
                    <FormattedMessage id="form.control.type.textArea" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"select"}>
                    <FormattedMessage id="form.control.type.select" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"checkbox"}>
                    <FormattedMessage id="form.control.type.checkbox" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"radio"}>
                    <FormattedMessage id="form.control.type.radio" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"switch"}>
                    <FormattedMessage id="form.control.type.switch" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"datePicker"}>
                    <FormattedMessage id="form.control.type.date" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"timePicker"}>
                    <FormattedMessage id="form.control.type.dateTime" />
                  </Select.Option>
                </Select>
              )}
            </Form.Item>
          ) : applicationType === 3 ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type" })}
              name="type"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                renderControlType(type)
              ) : (
                <Select
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  onChange={changeType}
                  style={{ marginBottom: "16px" }}
                >
                  <Select.Option style={radioStyle} value={"select"}>
                    <FormattedMessage id="form.control.type.select" />
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"radio"}>
                    <FormattedMessage id="form.control.type.radio" />
                  </Select.Option>
                </Select>
              )}
            </Form.Item>
          ) :(
              <Form.Item
                  label={formatMessage({ id: "form.control.type" })}
                  name="type"
                  rules={[{ required: !used }]}
                  className="mar-ver-5"
              >
                {used === true ? (
                    renderControlType(type)
                ) : (
                    <Select
                        placeholder={formatMessage({
                          id: "placeholder.select.common",
                        })}
                        onChange={changeType}
                        style={{ marginBottom: "16px" }}
                    >
                      <Select.Option style={radioStyle} value={"input"}>
                        <FormattedMessage id="form.control.type.input" />
                      </Select.Option>
                      <Select.Option style={radioStyle} value={"inputNumber"}>
                        <FormattedMessage id="form.control.type.inputNumber" />
                      </Select.Option>
                      <Select.Option style={radioStyle} value={"datePicker"}>
                        <FormattedMessage id="form.control.type.date" />
                      </Select.Option>
                      <Select.Option style={radioStyle} value={"timePicker"}>
                        <FormattedMessage id="form.control.type.dateTime" />
                      </Select.Option>
                    </Select>
                )}
              </Form.Item>
          )}

          {["input", "textArea", "inputNumber", "checkbox"].indexOf(type) >
          -1 ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type.format" })}
              name="formatType"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                renderFormat(type)
              ) : (
                <Select
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  style={{ marginBottom: "16px" }}
                  onChange={changeFormat}
                >
                  {type === "input" || type === "textArea" ? (
                    <Select.Option style={radioStyle} value={"characterLength"}>
                      <FormattedMessage id="form.control.type.format.characterLength" />
                    </Select.Option>
                  ) : null}
                  {type === "inputNumber" ? (
                    <>
                      <Select.Option style={radioStyle} value={"numberLength"}>
                        <FormattedMessage id="form.control.type.format.numberLength" />
                      </Select.Option>
                      <Select.Option style={radioStyle} value={"decimalLength"}>
                        <FormattedMessage id="form.control.type.format.decimalLength" />
                      </Select.Option>
                    </>
                  ) : null}
                  {type === "checkbox" ? (
                    <>
                      <Select.Option style={radioStyle} value={"checkbox"}>
                        <FormattedMessage id="form.control.type.format.checkbox" />
                      </Select.Option>
                    </>
                  ) : null}
                </Select>
              )}
            </Form.Item>
          ) : null}

          {type === "datePicker" ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type.format" })}
              name="dateFormat"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                format
              ) : (
                <Select
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  style={{ marginBottom: "16px" }}
                  onChange={changeFormat}
                >
                  <Select.Option style={radioStyle} value={"YYYY"}>
                    YYYY
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"YYYY-MM"}>
                    YYYY-MM
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MM-YYYY"}>
                    MM-YYYY
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MMM-YYYY"}>
                    MMM-YYYY
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"YYYY-MM-DD"}>
                    YYYY-MM-DD
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"DD-MM-YYYY"}>
                    DD-MM-YYYY
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MM-DD-YYYY"}>
                    MM-DD-YYYY
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"DD-MMM-YYYY"}>
                    DD-MMM-YYYY
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MMM-DD-YYYY"}>
                    MMM-DD-YYYY
                  </Select.Option>
                </Select>
              )}
            </Form.Item>
          ) : null}

          {type === "timePicker" ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type.format" })}
              name="timeFormat"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                fieldObj?.timeFormat
              ) : (
                <Select
                  style={{ marginBottom: "16px" }}
                  onChange={changeFormat}
                >
                  <Select.Option style={radioStyle} value={"HH:mm:ss"}>
                    HH:mm:ss
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"HH:mm"}>
                    HH:mm
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"YYYY-MM-DD HH:mm"}>
                    YYYY-MM-DD HH:mm
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"YYYY-MM-DD HH:mm:ss"}>
                    YYYY-MM-DD HH:mm:ss
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"DD-MM-YYYY HH:mm"}>
                    DD-MM-YYYY HH:mm
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"DD-MM-YYYY HH:mm:ss"}>
                    DD-MM-YYYY HH:mm:ss
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MM-DD-YYYY HH:mm"}>
                    MM-DD-YYYY HH:mm
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MM-DD-YYYY HH:mm:ss"}>
                    MM-DD-YYYY HH:mm:ss
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"DD-MMM-YYYY HH:mm"}>
                    DD-MMM-YYYY HH:mm
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"DD-MMM-YYYY HH:mm:ss"}>
                    DD-MMM-YYYY HH:mm:ss
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MMM-DD-YYYY HH:mm"}>
                    MMM-DD-YYYY HH:mm
                  </Select.Option>
                  <Select.Option style={radioStyle} value={"MMM-DD-YYYY HH:mm:ss"}>
                    MMM-DD-YYYY HH:mm:ss
                  </Select.Option>
                </Select>
              )}
            </Form.Item>
          ) : null}

          {type === "input" || type === "textArea" || type === "inputNumber" ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type.variableFormat" })}
              name="length"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                fieldObj?.length
              ) : (
                <InputNumber
                  placeholder={formatMessage({
                    id: "placeholder.input.common",
                  })}
                  min={0}
                  precision={
                    format !== null &&
                    format !== undefined &&
                    format === "decimalLength" &&
                    type !== null &&
                    type !== undefined &&
                    type === "inputNumber"
                      ? undefined
                      : 0
                  }
                  step={
                    format !== null &&
                    format !== undefined &&
                    format === "decimalLength" &&
                    type !== null &&
                    type !== undefined &&
                    type === "inputNumber"
                      ? 0.1
                      : 1
                  }
                  style={{ marginBottom: "16px", width: "100%" }}
                />
              )}
            </Form.Item>
          ) : null}
          {type === "select" || type === "checkbox" || type === "radio" ? (
            <Form.Item
              style={{ marginBottom: 0 }}
              label={
                <>
                  {formatMessage({
                    id: "form.control.type.options",
                  })}
                  {applicationType === 3 ? (
                    <Tooltip
                      placement="top"
                      overlayStyle={{ maxWidth: "500px" }}
                      title={
                        <>
                          <Row>
                            {formatMessage({
                              id: "form.control.type.options.tip.one",
                            })}
                          </Row>
                          <Row>
                            {formatMessage({
                              id: "form.control.type.options.tip.two",
                            })}
                          </Row>
                        </>
                      }
                    >
                      <QuestionCircleFilled
                        style={{
                          color: "#ADB2BA",
                          marginLeft: "8px",
                        }}
                      />
                    </Tooltip>
                  ) : null}
                </>
              }
              className="mar-ver-5"
            >
              <Form.List name="options" rules={[ValidatorList()]}>
                {(fields, { add, remove }, { errors }) => (
                  <>
                    {fields.map((field, index) => (
                      <Space key={field.name} align="baseline">
                        <Form.Item
                          {...field}
                          name={[field.name, "label"]}
                          validateTrigger={["onChange", "onBlur"]}
                          rules={[
                            {
                              required: true,
                              message: formatMessage({
                                id: "form.control.type.label",
                              }),
                            },
                          ]}
                          className="mar-ver-5"
                        >
                          {applicationType !== 3 ? (
                            fieldId && 
                            fieldObj?.options?.[index]?.disable === true ? (
                              <div style={{ width: "320px", marginTop: -8 }}>
                                {fieldObj?.options?.[index]?.label}
                              </div>
                            ) : (
                              <Input
                                placeholder={formatMessage({
                                  id: "form.control.type.label",
                                })}
                                style={{ width: "320px" }}
                              />
                            )
                          ) : fieldId &&
                            fieldObj?.options?.[index]?.disable === true ? (
                            <div style={{ width: "320px", marginTop: -8 }}>
                              {fieldObj?.options?.[index]?.label !==
                                "form.control.type.options.one" &&
                              fieldObj?.options?.[index]?.label !==
                                "form.control.type.options.two" &&
                              fieldObj?.options?.[index]?.label !==
                                "form.control.type.options.three"
                                ? fieldObj?.options?.[index]?.label
                                : formatMessage({
                                    id: fieldObj?.options?.[index]?.label,
                                  })}
                            </div>
                          ) : (
                            <Select
                              // className="full-width"
                              open={
                                selectIndex === -1
                                  ? false
                                  : selectIndex === index
                                  ? true
                                  : false
                              }
                              onDropdownVisibleChange={(visible) =>
                                setSelectIndex(visible ? index : -1)
                              }
                              style={{
                                width: g.lang === "en" ? 430 : "460px",
                                marginTop: -8,
                              }}
                              allowClear
                              placeholder={formatMessage({
                                id: "placeholder.select.common",
                              })}
                              onChange={(value: any) => {
                                handleChange(value);
                                setSelectIndex(index);
                              }}
                              dropdownRender={(menu) => (
                                <>
                                  {optionValues && optionValues.length > 0
                                    ? menu
                                    : null}
                                  <SelectInput>
                                    <CustomOptionAdder />
                                  </SelectInput>
                                </>
                              )}
                            >
                              {optionValues.map((it) => (
                                <Select.Option key={it.label} value={it.label}>
                                  {it.value}
                                </Select.Option>
                              ))}
                            </Select>
                          )}
                        </Form.Item>
                        {fieldId &&
                        fieldObj?.options?.[index]?.disable === true ? null : (
                          <MinusCircleFilled
                            style={{ color: "#F96964", marginLeft: "8px" }}
                            onClick={() => {
                              remove(index);
                              if (fieldId) {
                                let option = [...form.getFieldsValue().options];
                                option.filter((v: any, i: any) => i !== index);
                                fieldObj.options = option;
                                setFieldObj(fieldObj);
                                remove(field.name);
                                form.setFieldsValue({
                                  ...form.getFieldsValue,
                                  options: option,
                                });
                              }
                              let option = [...form.getFieldsValue().options];
                              const arr: any[] = [];
                              if (
                                option !== undefined &&
                                option !== null &&
                                option.length > 0
                              ) {
                                option.forEach((op: any) => {
                                  arr.push(op.label);
                                });
                              }
                              setSelectOption(arr);
                            }}
                          />
                        )}
                      </Space>
                    ))}
                    {
                      <Form.Item>
                        <Button
                          type="dashed"
                          onClick={() => {
                            let option = form.getFieldsValue().options
                              ? [...form.getFieldsValue().options]
                              : [];
                            const arr: any[] = [];
                            if (
                              option !== undefined &&
                              option !== null &&
                              option.length > 0
                            ) {
                              option.forEach((op: any) => {
                                if (op?.label !== undefined) {
                                  arr.push(op.label);
                                }
                              });
                            }
                            setSelectOption(arr);
                            add();
                            setOptionValues(
                              initOptions.filter(
                                (it) =>
                                  arr.findIndex((v: any) => v === it.label) ===
                                  -1
                              )
                            );
                          }}
                          block
                          icon={<PlusOutlined />}
                          style={{ width: g.lang === "en" ? 430 : "460px" }}
                          className="mar-ver-5"
                        >
                          <FormattedMessage id="common.add" />
                        </Button>
                      </Form.Item>
                    }
                    <Form.ErrorList errors={errors} />
                  </>
                )}
              </Form.List>
            </Form.Item>
          ) : null}
          {type === "inputNumber" ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type.variableRange" })}
              name="variableRange"
              className="mar-ver-5"
              dependencies={["range"]}
              hasFeedback
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const range = getFieldValue("range");
                    if (
                      range !== undefined &&
                      range !== null &&
                      range.max !== null &&
                      range.min !== null &&
                      range.max < range.min
                    ) {
                      return Promise.reject(
                        new Error(
                          formatMessage({
                            id: "form.control.type.variableRange.validate",
                          })
                        )
                      );
                    }

                    if (type !== undefined && type === "inputNumber" && range) {
                      const formatType = getFieldValue("formatType");
                      const length = getFieldValue("length");
                      if (
                        formatType !== undefined &&
                        formatType === "numberLength"
                      ) {
                        if (length !== undefined) {
                          //最大值
                          let maxNumber = Math.pow(10, length) - 1;
                          if (
                            (range.max && range.max > maxNumber) ||
                            (range.min && range.min > maxNumber)
                          ) {
                            return Promise.reject(
                              new Error(
                                formatMessage({
                                  id: "form.control.type.variableRange.validate.range",
                                })
                              )
                            );
                          }
                        }
                      } else if (
                        formatType !== undefined &&
                        formatType === "decimalLength"
                      ) {
                        if (length !== undefined) {
                          // 将数字转换为字符串
                          var str = length.toString();
                          // 使用split()方法将整数部分和小数部分分开
                          var parts = str.split(".");
                          if (parts.length > 1) {
                            //取整数部分
                            let integerPart = parts[0];
                            //取小数部分
                            let decimalPart = parts[1];
                            //最大值
                            let maxNumber =
                              Math.pow(10, parseInt(integerPart)) -
                              1 +
                              (1 - Math.pow(10, -parseInt(decimalPart)));
                            if (
                              (range.max && range.max > maxNumber) ||
                              (range.min && range.min > maxNumber)
                            ) {
                              return Promise.reject(
                                new Error(
                                  formatMessage({
                                    id: "form.control.type.variableRange.validate.range",
                                  })
                                )
                              );
                            }
                            // 如果存在小数部分，则返回小数位数
                            if (range.min) {
                              var minDecimalPart = range.min
                                .toString()
                                .match(/\.(\d+)/);
                              if (
                                minDecimalPart &&
                                minDecimalPart[1].length > decimalPart
                              ) {
                                return Promise.reject(
                                  new Error(
                                    formatMessage({
                                      id: "form.control.type.variableRange.validate.range",
                                    })
                                  )
                                );
                              }
                            }
                            // 如果存在小数部分，则返回小数位数
                            if (range.max) {
                              var maxDecimalPart = range.max
                                .toString()
                                .match(/\.(\d+)/);
                              if (
                                maxDecimalPart &&
                                maxDecimalPart[1].length > decimalPart
                              ) {
                                return Promise.reject(
                                  new Error(
                                    formatMessage({
                                      id: "form.control.type.variableRange.validate.range",
                                    })
                                  )
                                );
                              }
                            }
                          } else {
                            //取整数部分
                            let integerPart = parts[0];
                            //最大值
                            let maxNumber =
                              Math.pow(10, parseInt(integerPart)) - 1;
                            if (
                              (range.max && range.max > maxNumber) ||
                              (range.min && range.min > maxNumber)
                            ) {
                              return Promise.reject(
                                new Error(
                                  formatMessage({
                                    id: "form.control.type.variableRange.validate.range",
                                  })
                                )
                              );
                            }
                          }
                        }
                      }
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Row>
                {used === true ? (
                  <Col>
                    <Form.Item name={["range", "min"]}>
                      {fieldObj?.range?.min}
                    </Form.Item>
                  </Col>
                ) : (
                  <Col>
                    <Form.Item name={["range", "min"]}>
                      <InputNumber
                        style={{ width: 225 }}
                        min={0}
                        precision={
                          format !== null &&
                          format !== undefined &&
                          format === "decimalLength" &&
                          type !== null &&
                          type !== undefined &&
                          type === "inputNumber"
                            ? undefined
                            : 0
                        }
                        step={
                          format !== null &&
                          format !== undefined &&
                          format === "decimalLength" &&
                          type !== null &&
                          type !== undefined &&
                          type === "inputNumber"
                            ? 0.1
                            : 1
                        }
                        placeholder={formatMessage({
                          id: "form.control.type.variableRange.min",
                        })}
                      />
                    </Form.Item>
                  </Col>
                )}
                <Col
                  span={2}
                  style={{
                    lineHeight: "32px",
                    textAlign: "center",
                  }}
                >
                  —
                </Col>
                <Col >
                  <Form.Item name={["range", "max"]}>
                    {used === true ? (
                      fieldObj?.range?.max
                    ) : (
                      <InputNumber
                        style={{ width: 225 }}
                        min={0}
                        precision={
                          format !== null &&
                          format !== undefined &&
                          format === "decimalLength" &&
                          type !== null &&
                          type !== undefined &&
                          type === "inputNumber"
                            ? undefined
                            : 0
                        }
                        step={
                          format !== null &&
                          format !== undefined &&
                          format === "decimalLength" &&
                          type !== null &&
                          type !== undefined &&
                          type === "inputNumber"
                            ? 0.1
                            : 1
                        }
                        placeholder={formatMessage({
                          id: "form.control.type.variableRange.max",
                        })}
                      />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </Form.Item>
          ) : null}
          {format &&
            (type == "datePicker" ||
              (type == "timePicker" &&
                format !== "HH:mm:ss" &&
                type == "timePicker" &&
                format !== "HH:mm")) && (
              <Form.Item
                label={formatMessage({ id: "form.control.type.variableRange" })}
                name="variableRange"
                className="mar-ver-5"
                dependencies={["dateRange"]}
                hasFeedback
              >
                <Row wrap={false}>
                  <Col style={{ flex: 1 }}>
                    <Form.Item name={["dateRange", "min"]}>
                      {used === true ? (
                        handleMinPicker()
                      ) : (
                        <CustomDatePickerEn
                            disabled={false}
                            value={dateRange.min}
                          disabledDate={(current: any) => {
                            if (
                              dateRange.max != "" &&
                              (dateRange.max !== "currentTime" ||
                                dateRange.max !== "当前时间")
                            ) {
                              return (
                                current &&
                                current > dayjs(dateRange.max, format)
                              );
                            }
                            return false;
                          }}
                          picker={picker}
                          format={format}
                          onChange={(value: any) =>
                            setDateRange({ min: value, max: dateRange.max })
                          }
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col
                    style={{
                      lineHeight: "32px",
                      padding: "0 8px",
                    }}
                  >
                    —
                  </Col>
                  <Col style={{ flex: 1 }}>

                    {used === true ? (
                        <Form.Item name={["dateRange", "max"]} noStyle>
                          { handleMaxPicker()}
                        </Form.Item>
                    ) : (
                      <>
                      <Form.Item name={["dateRange", "max"]} noStyle>
                          <CustomDatePicker
                            picker={picker}
                            format={format}
                            value={dateRange.max}
                            onChange={(v: any) =>
                              setDateRange({ min: dateRange.min, max: v })
                            }
                            disabledDate={(current: any) => {
                              if (dateRange.min != "") {
                                return (
                                  current &&
                                  current < dayjs(dateRange.min, format)
                                );
                              }
                              return false;
                            }}
                          />
                      </Form.Item>
                        {dateRange.max === "currentTime" ||
                        dateRange.max === "Current Time" ||
                        dateRange.max === "当前时间" ? (
                          <Row>
                            <span
                              style={{
                                paddingTop: 6,
                                color: "#1D2129",
                              }}
                            >
                              <svg
                                className="iconfont mouse"
                                width={12}
                                height={12}
                                style={{
                                  marginLeft: 4,
                                  marginTop: 8,
                                  marginRight: 6,
                                }}
                              >
                                <use xlinkHref="#icon-xinxitishi1" />
                              </svg>
                              {formatMessage({
                                id: "form.picker.max.tip",
                              })}
                            </span>
                          </Row>
                        ) : null}
                      </>
                    )}

                  </Col>
                </Row>
              </Form.Item>
            )}
          {format &&
            type == "timePicker" &&
            (format === "HH:mm:ss" || format === "HH:mm") && (
              <Form.Item
                label={formatMessage({ id: "form.control.type.variableRange" })}
                name="variableRange"
                className="mar-ver-5"
                dependencies={["dateRange"]}
                hasFeedback
              >
                <Row wrap={false}>
                  <Col style={{ flex: 1 }}>
                    <Form.Item name={["dateRange", "min"]}>
                      {used === true ? (
                        handleMinPicker()
                      ) : (
                        <TimePicker
                          style={{ width: "100%" }}
                          disabledDate={(current: any) => {
                            if (
                              dateRange.max != "" &&
                              (dateRange.max !== "currentTime" ||
                                dateRange.max !== "当前时间")
                            ) {
                              return (
                                current &&
                                current > dayjs(dateRange.max, format)
                              );
                            }
                            return false;
                          }}
                          onChange={(value: any) =>
                            setDateRange({ min: value, max: dateRange.max })
                          }
                          format={format}
                        />

                      )}
                    </Form.Item>
                  </Col>

                  <Col
                    style={{
                      lineHeight: "32px",
                      padding: "0 8px",
                    }}
                  >
                    —
                  </Col>
                  <Col style={{ flex: 1 }}>

                    {used === true ? (
                        <Form.Item name={["dateRange", "max"]} noStyle>
                          {handleMaxPicker()}
                        </Form.Item>
                    ) : (
                      <>
                        <Form.Item name={["dateRange", "max"]} noStyle>
                          <CustomTimePicker
                            disabledDate={(current: any) => {
                              if (dateRange.min != "") {
                                return (
                                  current &&
                                  current < dayjs(dateRange.min, format)
                                );
                              }
                              return false;
                            }}
                            onChange={(v: any) =>
                              setDateRange({ min: dateRange.min, max: v })
                            }
                            format={format}
                            value={dateRange.max}
                          />
                        </Form.Item>
                        {dateRange.max === "currentTime" ||
                        dateRange.max === "Current Time" ||
                        dateRange.max === "当前时间" ? (
                          <Row>
                            <span
                              style={{
                                paddingTop: 6,
                                // marginLeft: g.lang === "en" ? 530 : 355,
                                color: "#677283",
                              }}
                            >
                              <svg
                                className="iconfont mouse"
                                width={12}
                                height={12}
                                style={{
                                  marginLeft: 4,
                                  marginTop: 8,
                                  marginRight: 6,
                                }}
                              >
                                <use xlinkHref="#icon-xinxitishi1" />
                              </svg>
                              {formatMessage({
                                id: "form.picker.max.tip",
                              })}
                            </span>
                          </Row>
                        ) : null}
                      </>
                    )}

                  </Col>
                </Row>
              </Form.Item>
            )}
          {connectEdc === 1 ? (
            <Form.Item
              label={formatMessage({ id: "form.control.type.variableFormat" })}
              name="name"
              rules={[{ required: !used }]}
              className="mar-ver-5"
            >
              {used === true ? (
                fieldObj?.nameNew
              ) : (
                <Input
                  allowClear
                  placeholder={formatMessage({
                    id: "placeholder.input.common",
                  })}
                />
              )}
            </Form.Item>
          ) : null}
          <Form.Item
            label={formatMessage({ id: "common.status" })}
            name="status"
            className="mar-ver-5"
            initialValue={1}
          >
            <Radio.Group>
              <Radio value={1}>
                {formatMessage({ id: "common.effective" })}
              </Radio>
              <Radio value={2}>{formatMessage({ id: "common.invalid" })}</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </React.Fragment>
  );
};

const SelectInput = styled.div`
  margin: 6px 0 0 0;
  .ant-input-group-addon {
    background: unset !important;
    border: unset !important;
  }
`;

const StyledRadioGroup = styled(Radio.Group)`  
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 6px;

  .ant-radio-button-wrapper {
    width: 50%;
    margin-bottom: 8px;
  }`;