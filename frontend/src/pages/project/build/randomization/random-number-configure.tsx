import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Descriptions, List, Row} from "antd";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {getRandomListConfigure} from "../../../../api/randomization";
import styled from "@emotion/styled";
import {useAuth} from "../../../../context/auth";

export const RandomNumberConfigure = (props:any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const [data, setData] = useSafeState<any>([]);
    const [loading, setLoading] = useSafeState<any>(true);
    const {runAsync: getRandomListConfigureRun} = useFetch(getRandomListConfigure, {manual: true})
    const auth = useAuth()
    const list = React.useCallback(
        () => {
            getRandomListConfigureRun({id:props.record}).then(
                (result:any) => {
                    setData(result.data);
                    setLoading(false)
                }
            )
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []
    )
    const itemOption = (item:any) => {
        if (item.options === null){
            item.options = []
        }
        return item.options.map((value:any) => (
                <Row key={value.value}>{value.label}</Row>
            )
        )
    }
    const renderLayer = () =>{
        let layer = ""
        if (props.attribute.info.instituteLayered){
            return formatMessage({id:'common.site'})
        }else if(props.attribute.info.countryLayered){
            return formatMessage({id:'projects.attributes.country'})
        }else if ((props.attribute.info.regionLayered)){
            return formatMessage({id:'projects.attributes.regionLayered'})
        }
        return  layer
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(list, []);

    return (
        <>
            {
                !loading?
                    <Descriptions size="small" column={{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }} layout={data.config.initialValue === 0? "vertical":"horizontal"}  bordered>
                        <Descriptions.Item label={formatMessage({id: 'randomization.config.type'})} >{data.design.type === 1 ?
                            <FormattedMessage id="projects.randomization.blockRandom"/> :
                            <FormattedMessage id="projects.randomization.minimize"/>}
                        </Descriptions.Item>
                        {
                            data.config.initialValue === 0?
                                null
                                :
                                <>
                                    <Descriptions.Item label={formatMessage({id: 'common.name'})} >
                                        {data.name}
                                    </Descriptions.Item>
                                    <Descriptions.Item label={formatMessage({id: 'projects.randomization.defaultNumber'})} >
                                        {data.config.initialValue}
                                    </Descriptions.Item>
                                    <Descriptions.Item label={formatMessage({id: 'projects.randomization.numberLength'})} >
                                        {data.config.numberLength}
                                    </Descriptions.Item>
                                    <Descriptions.Item label={formatMessage({id: 'projects.randomization.seed'})} >
                                        {data.config.seed}
                                    </Descriptions.Item>
                                    <Descriptions.Item label={formatMessage({id: 'projects.randomization.numberPrefix'})} >
                                        {data.config.prefix !== ""?data.config.prefix :"-"}
                                    </Descriptions.Item>
                                    {
                                        data.design.type === 1?
                                            <Descriptions.Item label={formatMessage({id: 'projects.randomization.blockRule'})} >
                                                {data.config.blocksRule === 0?formatMessage({id: 'projects.randomization.blockRule.order'}):
                                                    formatMessage({id: 'projects.randomization.blockRule.reverse'})}
                                            </Descriptions.Item>:null
                                    }

                                    <Descriptions.Item label={formatMessage({id: 'projects.randomization.randomNumberRule'})} >
                                        {data.config.rule === 0?formatMessage({id: 'projects.randomization.blockRule.order'}):
                                            formatMessage({id: 'projects.randomization.blockRule.reverse'})}
                                    </Descriptions.Item>
                                    {
                                        data.config.rule === 1?
                                            <Descriptions.Item label={formatMessage({id: 'projects.randomization.endNumber'})} >
                                                {data.config.endValue}
                                            </Descriptions.Item>:null
                                    }

                                    {
                                        data.design.type === 1?
                                            <Descriptions.Item label={formatMessage({id: 'projects.randomization.blockConfig'})} >
                                                <NoPaddingLeftList
                                                    dataSource={data.config.blocks}
                                                    renderItem={(item:any) => (
                                                        <List.Item>
                                                            {formatMessage({id: 'projects.randomization.blockLength'})}：{item.blockLength}，{formatMessage({id: 'projects.randomization.generateBlockNumber'})}：{item.blockNumber}
                                                        </List.Item>
                                                    )}
                                                />
                                            </Descriptions.Item>
                                            :
                                            <>
                                                <Descriptions.Item label={formatMessage({id: 'projects.randomization.probability'})} >
                                                    {data.config.probability}
                                                </Descriptions.Item>
                                                <Descriptions.Item label={formatMessage({id: 'projects.randomization.total'})} >
                                                    {data.config.total}
                                                </Descriptions.Item>
                                            </>

                                    }
                                    <Descriptions.Item label={formatMessage({id: 'projects.randomization.groupConfig'})} >
                                        <NoPaddingLeftList
                                            dataSource={data.design.groups}
                                            renderItem={(item:any) => (
                                                <List.Item>
                                                    {formatMessage({id: 'projects.randomization.group'})}：{item.name}，{formatMessage({id: 'projects.randomization.weight'})}：{item.ratio}
                                                </List.Item>
                                            )}
                                        />
                                    </Descriptions.Item>
                                </>

                        }
                        <Descriptions.Item label={formatMessage({id: 'projects.randomization.group'})} >
                            <NoPaddingLeftList
                                dataSource={data.design.groups.filter((item:any) => item.status !== 2)}
                                renderItem={(item:any) => (
                                    <List.Item>
                                        {item.name}
                                    </List.Item>
                                )}
                            />
                        </Descriptions.Item>

                        {
                            data.design.type === 1?
                                <Descriptions.Item label={formatMessage({id: 'randomization.config.factor'})} >
                                    <NoPaddingLeftList
                                        itemLayout="horizontal"
                                        dataSource={data.design.factors === null? []: data.design.factors}
                                        renderItem={(item:any,index) => (
                                            <List.Item>
                                                <List.Item.Meta
                                                    key={index}
                                                    title={item.label}
                                                    description={
                                                        itemOption(item)
                                                    }
                                                />
                                            </List.Item>
                                        )}
                                    />
                                </Descriptions.Item>
                            :
                                <Descriptions.Item label={formatMessage({id: 'randomization.config.factor'})} >
                                    <NoPaddingLeftList
                                        itemLayout="horizontal"
                                        dataSource={data.design.factors === null? []: data.design.factors}
                                        renderItem={(item:any,index) => (
                                            <List.Item>
                                                <List.Item.Meta
                                                    key={index}
                                                    title={item.label}
                                                    description={
                                                        itemOption(item)
                                                    }
                                                />
                                                {formatMessage({id: 'projects.randomization.weight'})}: {item.ratio}
                                            </List.Item>
                                        )}
                                    />
                                    {
                                        (props.attribute && (props.attribute.info.instituteLayered || props.attribute.info.countryLayered || props.attribute.info.regionLayered)) ?
                                            <>
                                                <List.Item.Meta
                                                    title={renderLayer()}
                                                />
                                                {formatMessage({id: 'projects.randomization.weight'})}: {data.design.ratio}
                                            </>
                                            : null
                                    }
                                </Descriptions.Item>
                        }
                    </Descriptions>:null
            }
        </>
    )
};

const NoPaddingLeftList = styled(List)`
    .ant-list-item{
        padding-left: 0;
    }
`
