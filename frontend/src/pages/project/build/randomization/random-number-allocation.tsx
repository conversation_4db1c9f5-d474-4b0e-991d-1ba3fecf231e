import React from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Col,
    Dropdown,
    Form,
    Input,
    InputNumber,
    message,
    Pagination,
    Row,
    Select,
    Space,
    Spin,
    Table, Tooltip
} from "antd";
import {permissions, permissionsCohort} from '../../../../tools/permission'
import {useAuth} from "../../../../context/auth";
import {useDebounceFn, useSafeState} from "ahooks";
import Footer from "../../../main/layout/footer";
import {useFetch} from "../../../../hooks/request";
import {
    activateInactivate,
    cleanFactor,
    getBlock,
    getFactor,
    getRandomListConfigure,
    getRandomNumberGroup
} from "../../../../api/randomization";
import {randomNumberStatus} from "../../../../data/data";
import {useGlobal} from "../../../../context/global";
import {RandomNumberAllocations} from "./random-number-allocations";
import {FormattedMessage, useIntl} from "react-intl";
import {CaretDownOutlined, CaretRightOutlined, DownOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {CustomConfirmModal} from "../../../../components/modal";
import {RandomNumberAllocationsLimit} from "./random-number-allocations-limit";

export const RandomNumberAllocation = (props: any) => {

    const g = useGlobal();

    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;

    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<any>([]);
    const [loading, setLoading] = useSafeState<any>(false);
    const [data, setData] = useSafeState<any>([]);
    const [sourceSite, setSourceSite] = useSafeState<any>(null);
    const [factor, setFactor] = useSafeState<any>([]);
    const [center, setCenter] = useSafeState<any>(false);
    const [other, setOther] = useSafeState<any>(false);
    const [currentPage, setCurrentPage] = useSafeState<any>(1);
    const [pageSize, setPageSize] = useSafeState<any>(20);
    const [total, setTotal] = useSafeState<any>(0);
    const [blockDetail, setBlockDetail] = useSafeState<any>({});
    const [subcolumn, setSubcolumn] = useSafeState<any>([]);
    const [randomType, setRandomType] = useSafeState<any>(0);
    const [isModalVisible, setIsModalVisible] = useSafeState<any>(false);
    const [block, setBlock] = useSafeState<any>(false);
    const [openBlock, setOpenBlock] = useSafeState<any>({});
    const [first, setFirst] = useSafeState<any>(true);
    const [open, setOpen] = useSafeState<boolean>(false);
    const [status,setStatus] = useSafeState<any>([])
    const [statusType,setStatusType] = useSafeState<any>(0)
    const [rangeType,setRangeType] = useSafeState<any>(1)

    const [searchForm] = Form.useForm();
    const { Option } = Select;

    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const attribute = props.attribute;
    const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
    const projectStatus = auth.project.status ? auth.project.status : 0
    const commonRender = (v: any) => v ? v : '-'

    const columns = [
        Table.SELECTION_COLUMN,
        Table.EXPAND_COLUMN,
        {
            title: formatMessage({id: 'projects.randomization.block'}),
            width: 100,
            dataIndex: 'block',
            render: commonRender
        },
        {title: formatMessage({id: 'projects.randomization.randomCount'}), dataIndex: 'count', key: 'count', width: g.lang === 'zh' ? 100 : 250},
        {
            title: formatMessage({id: 'common.site'}),
            dataIndex: 'site',
            render: commonRender
        },
        {
            title: formatMessage({id: 'projects.randomization.otherFactor'}),
            dataIndex: 'factors',
            width: 300,
            render:
                (value: any, record: any, index: any) => {
                    if (value) {
                        // return "aaa"
                        return value.map((item: any) =>
                            item.text
                        ).join(' ').trim() || '-'
                    } else {
                        return '-'
                    }
                }
        },
        {
            title: formatMessage({id: 'common.operation'}),
            dataIndex: 'operation',
            width: g.lang === 'zh' ? 150 : 300,
            render:
                (value: any, record: any, index: any) => {
                    let buttons = [];
                    if (record.factors && projectStatus !== 2 && permissionsCohort(auth.project.permissions,"operation.build.randomization.list.segmentation.clear",props.cohort?.status)) {
                        buttons.push(
                            <Button 
                                key="clear" 
                                loading={cleanFactorLoading} 
                                onClick={() => clean_factor(record.block)} 
                                size="small" 
                                type="link" 
                                style={{padding: 0}} 
                                disabled={lockConfig}
                            >
                                {formatMessage({id: 'projects.randomization.allocation.factor'})}
                            </Button>
                        );
                    }
                    if (permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.activate",props.cohort?.status)
                        && projectStatus !== 2 
                        && record.status?.includes(5)) {
                        buttons.push(
                            <Button
                                loading={activateInactivateLoading}
                                key="activate" 
                                onClick={() => activateDeactivateBlockFunc(0,record)}
                                size="small" 
                                type="link" 
                                style={{padding: 0}}
                            >
                                {formatMessage({id: 'common.active'})}
                            </Button>
                        );
                    }
                    if (permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.deactivate",props.cohort?.status)
                        && projectStatus !== 2
                        && record.status?.includes(1)) {
                        buttons.push(
                            <Button
                                key="deactivate"
                                loading={activateInactivateLoading}
                                onClick={() => activateDeactivateBlockFunc(1,record)}
                                size="small"
                                type="link"
                                style={{padding: 0}}
                            >
                                {formatMessage({id: 'common.inactivating'})}
                            </Button>
                        );
                    }

                    return buttons.length > 0 ? <Space>{buttons}</Space> : '-';
                }
        },

    ];
    const clean_factor = (id: any) => {
        setBlock(id)
       // setIsModalVisible(true)
       CustomConfirmModal({
            content: <span style={{fontWeight:500,fontSize:"16px",lineHeight:"24px",color:"#000000",opacity: 0.85}}>{formatMessage({id: 'projects.randomization.allocation.factorConfirm'})}</span>,
            centered: true,
            okText:formatMessage({ id: 'common.ok' }),
            onOk: cleanConfirm,

        })
    }

    const {runAsync: cleanFactorRun, loading: cleanFactorLoading} = useFetch(cleanFactor, {manual: true})
    const {runAsync: activateInactivateRun, loading: activateInactivateLoading} = useFetch(activateInactivate, {manual: true})
    const {runAsync: getBlockRun, loading: getBlockLoading} = useFetch(getBlock, {manual: true})
    const {
        runAsync: getRandomListConfigureRun
    } = useFetch(getRandomListConfigure, {manual: true})
    const {runAsync: getFactorRun} = useFetch(getFactor, {manual: true})
    const {
        runAsync: getRandomNumberGroupRun,
        loading: getRandomNumberGroupLoading
    } = useFetch(getRandomNumberGroup, {manual: true})


    const cleanConfirm = () => {
        cleanFactorRun({id: props.record, block}).then(
            (data: any) => {
                message.success(data.msg)
                list(sourceSite, false)
                getBlockDetail(true, {block: block}, false)
                setIsModalVisible(false)
            }
        )
    }
    const activateDeactivateBlockFunc = (operationType:any,record:any) => {
        let title = operationType === 0 ? formatMessage({id: 'projects.randomization.allocation.block.activate.tip'}) : formatMessage({id: 'projects.randomization.allocation.block.Inactivate.tip'})
        let content = operationType === 0 ? formatMessage({id: 'projects.randomization.allocation.block.activate.content'}) : formatMessage({id: 'projects.randomization.allocation.block.Inactivate.content'})
        CustomConfirmModal({
            title: title,
            content: content,
            centered: true,
            okText:formatMessage({ id: 'common.ok' }),
            cancelText:formatMessage({ id: 'common.cancel' }),
            onOk:()=>activateInactivateRun({randomListId: props.record, block:record.block,operationType:operationType,dataType:0}).then(
                (data: any) => {
                    message.success(data.msg)
                    list(sourceSite, false)
                    const trueNumbers = Object.keys(openBlock)
                        .filter(key => openBlock[key])
                        .map(Number);
                    getBlockDetail(true, false,trueNumbers )
                }
            ),
        })
    }

    const activateDeactivateNumberFunc = (operationType:any,record:any) => {
        let title = operationType === 0 ? formatMessage({id: 'projects.randomization.allocation.randomNumber.activate.tip'}) : formatMessage({id: 'projects.randomization.allocation.randomNumber.Inactivate.tip'})
        let content = operationType === 0 ? formatMessage({id: 'projects.randomization.allocation.randomNumber.activate.content'}) : formatMessage({id: 'projects.randomization.allocation.randomNumber.Inactivate.content'})
        CustomConfirmModal({
            title: title,
            content: content,
            centered: true,
            okText:formatMessage({ id: 'common.ok' }),
            cancelText:formatMessage({ id: 'common.cancel' }),
            onOk:()=>activateInactivateRun({randomListId: props.record, block:block,operationType:operationType,dataType:1,number:record.number}).then(
                (data: any) => {
                    message.success(data.msg)
                    list(sourceSite, false)
                    setOpenBlock((prevOpenBlock: Record<string, boolean>) => {
                        const trueNumbers = Object.keys(prevOpenBlock)
                            .filter(key => prevOpenBlock[key])
                            .map(Number);
                        getBlockDetail(true, false, trueNumbers);
                        return prevOpenBlock;
                    });
                }
            )
        })
    }

    const rowSelection: any = {
        type: "checkbox",
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            setSelectedRowKeys(selectedRowKeys);
        },
        selectedRowKeys: selectedRowKeys,
    };


    const list = (id: any, relist: any) => {
        if (first) {
            return
        }
        setSelectedRowKeys([]);
        setSourceSite(id);
        let currentPages = currentPage
        if (currentPage !== 1 && relist) {
            currentPages = 1
            setCurrentPage(1)
        } else {
            getBlockRun({
                randomListId: props.record,
                id: id ? id : "all", ...searchForm.getFieldsValue(),
                "begin": (currentPages - 1) * pageSize,
                "limit": pageSize
            }).then(
                (result: any) => {
                    setTotal(result.data.count)
                    setData(result.data.item)
                }
            )

        }
    };


    // 查询项目属性配置
    const get_project_attributes = React.useCallback(
        () => {
            setSourceSite(null)
            getRandomListConfigureRun({id: props.record}).then(
                (result: any) => {
                    let data: any = result.data
                    setFirst(false)
                    setCenter(data.config.checkSite)
                    if (data.design.factors != null && data.design.factors.length !== 0) {
                        getFactorRun({id: props.record}).then(
                            (ret: any) => {
                                if (ret.data) {
                                    setFactor(ret.data)
                                    setOther(data.config.checkOtherFactor)
                                }
                            }
                        )
                    }

                    setRandomType(data.design.type) // 区分最小化随机
                    if (data.design.type === 2) {
                        subcolumns.push({
                            title: formatMessage({id: 'projects.randomization.planNumber'}),
                            dataIndex: 'plan_number',
                            key: 'plan_number',
                            className:'bg-white'
                        })
                        getRandomNumberGroupRun({randomListId: props.record}, {"block": [0,],...searchForm.getFieldsValue()}).then(
                            (result: any) => {
                                setData(result.data[0]["item"])
                            }
                        )
                    } else {
                        getBlockRun({randomListId: props.record, id: "all", ...searchForm.getFieldsValue()}).then(
                            (result: any) => {
                                setTotal(result.data.count)
                                setData(result.data.item)
                            },
                        )
                    }
                    setSubcolumn(subcolumns);
                    if (data.design.factors !== undefined && data.design.factors != null) {
                        let subcolumn = subcolumns.concat(data.design.factors.map((value: any) => (
                            {"title": value.label, "dataIndex": value.name, "key": value.name, "render": commonRender,"className":'bg-white'}
                        )));
                        subcolumn = subcolumn.concat(endColumns)
                        setSubcolumn(subcolumn);
                    } else {
                        let subcolumn = subcolumns.concat(endColumns)
                        setSubcolumn(subcolumn);

                    }


                    setLoading(false);

                },
            )
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [customerId, projectId, envId, cohortId]
    );


    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(get_project_attributes, []);
    React.useEffect(() => {
        list(sourceSite, false)
        const trueNumbers = Object.keys(openBlock)
            .filter(key => openBlock[key])
            .map(Number);

        getBlockDetail(true, false,trueNumbers )
    }, [currentPage, pageSize,status,rangeType]);


    const {run: searchBlock} = useDebounceFn(() => {
        setCurrentPage(1);
        list(sourceSite, true);
        const trueNumbers = Object.keys(openBlock)
            .filter(key => openBlock[key])
            .map(Number);

        getBlockDetail(true, false,trueNumbers )
    }, {
        wait: 500,
    })


    function subjectStatusItem(value: any) {
        const colors = ["", "geekblue", "orange", "grey","grey","red"];
        // @ts-ignore
        return <Badge color={colors[value]} text={randomNumberStatus.find(it => (it.value === value)) ? randomNumberStatus.find(it => (it.value === value)).label : '-'}/>
    }

    function getSubject(value: any) {
        if (value && value.length > 0 && value[0].find((it: any) => it.name === "shortname")) {
            return value[0].find((it: any) => it.name === "shortname").value
        }
        return '-';
    }

    const getCountry = (value: any) => {
        if (value.length === 0) {
            return "NA"
        } else {
            let language = "en";
            switch (g.lang) {
                case 'en':
                    language = "en";
                    break;
                case 'zh':
                    language = "cn";
                    break;
                case 'ko':
                    language = "ko";
                    break;
            }
            return value[0][language]
        }
    }

    let subcolumns = [
        {title: formatMessage({id: 'projects.randomization.randomNumber'}), dataIndex: 'number', key: 'number',className:'bg-white'},
        {title: formatMessage({id: 'projects.randomization.groupList'}), dataIndex: 'group', key: 'group',className:'bg-white'},
    ];

    const endColumns = [
        {
            title: formatMessage({id: 'subject.number'}),
            dataIndex: 'subject',
            key: 'subject',
            className:'bg-white',
            render: (value: any, record: any, index: any) => (getSubject(value))
        },
        {
            title: formatMessage({id: 'common.site'}),
            dataIndex: 'site', 
            key: 'site', 
            className:'bg-white',
            render: commonRender
        },
        {
            title: formatMessage({id: 'projects.attributes.country'}),
            dataIndex: 'country',
            key: 'country',
            className:'bg-white',
            render: (value: any, record: any, index: any) => (getCountry(value))
        },
        {
            title: formatMessage({id: 'projects.attributes.regionLayered'}),
            dataIndex: 'region',
            key: 'region',
            className:'bg-white',
            render: commonRender
        },
        {
            title: formatMessage({id: 'common.status'}),
            dataIndex: 'status',
            key: 'status',
            className:'bg-white',
            render: (value: any, record: any, index: any) => (subjectStatusItem(value)),
        },
        {
            title: formatMessage({id: 'common.operation'}),
            dataIndex: 'operation',
            key: 'operation',
            className:'bg-white',
            render:
                (value: any, record: any, index: any) => {
                    let buttons = [];
                    if (permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.activate",props.cohort?.status)
                        && projectStatus !== 2
                        && record.status===5) {
                        buttons.push(
                            <Button
                                loading={activateInactivateLoading}
                                key="activate"
                                onClick={() => activateDeactivateNumberFunc(0,record)}
                                size="small"
                                type="link"
                                style={{padding: 0}}
                            >
                                {formatMessage({id: 'common.active'})}
                            </Button>
                        );
                    }
                    if (permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.deactivate",props.cohort?.status)
                        && projectStatus !== 2
                        && record.status===1) {
                        buttons.push(
                            <Button
                                key="deactivate"
                                loading={activateInactivateLoading}
                                onClick={() => activateDeactivateNumberFunc(1,record)}
                                size="small"
                                type="link"
                                style={{padding: 0}}
                            >
                                {formatMessage({id: 'common.inactivating'})}
                            </Button>
                        );
                    }

                    return buttons.length > 0 ? <Space>{buttons}</Space> : '-';
                },
        },
    ];
    const getBlockDetail = (bool: any, item: any, block: any) => {
        if (bool) {
            if (item) {
                setOpenBlock({...openBlock, [item.block]: true})
            }
            getRandomNumberGroupRun({randomListId: props.record}, {"block": block ? block : [item.block], ...searchForm.getFieldsValue()}).then(
                (result: any) => {
                    let table: any = {}
                    let data: any = result.data
                    if (data) {
                        data.forEach(
                            (it: any) => {
                                table[it._id] = it.item
                            }
                        )
                    }
                    setBlockDetail({...blockDetail, ...table})
                    setLoading(false);
                },
            )
        } else {
            setOpenBlock({...openBlock, [item.block]: false})
        }
    }
    const expandedRowRender = (record: any, index: any) => {

        return (
            <Table
                className="bg-white"
                columns={subcolumn}
                dataSource={blockDetail[record.block]}
                pagination={false}
            />
        );
    };

    const allocation_ref: any = React.useRef();
    const allocation_limit_ref: any = React.useRef();
    const showAllocation = (type: any) => {
        if (selectedRowKeys.length === 0) {
            message.warn(formatMessage({id: "menu.projects.project.build.randomization.tooltip"}))
        } else {
            allocation_ref.current.show(type, selectedRowKeys, props.record, center, other, factor, openBlock, data);

        }
    };

    const showAllocationLimit = () => {
        allocation_limit_ref.current.show();
    };

    
    const items = () => {
        if (attribute.info.isRandom === false && attribute.info.isCountryRandom === false && attribute.info.isRegionRandom === false){
            return [
                permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.site",props.cohort?.status) ? {
                    label: formatMessage({id: 'projects.randomization.distributionCenter'}),
                    key: 1,
                } : false,
                permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.country",props.cohort?.status) ? {
                    label: formatMessage({id: 'projects.randomization.distributionCountry'}),
                    key: 2,
                } : false,
                permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.region",props.cohort?.status) ? {
                    label: formatMessage({id: 'projects.randomization.distributionRegion'}),
                    key: 3,
                } : false,
                permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.factor",props.cohort?.status) ? {
                    label: formatMessage({id: 'projects.randomization.distributionFactor'}),
                    key: 0,
                } : false,
            ].filter(Boolean)
        }
        return [
            permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.site",props.cohort?.status) && attribute.info.isRandom === true  ? {
                label: formatMessage({id: 'projects.randomization.distributionCenter'}),
                key: 1,
            } : false,
            permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.country",props.cohort?.status) && attribute.info.isCountryRandom === true? {
                label: formatMessage({id: 'projects.randomization.distributionCountry'}),
                key: 2,
            } : false,
            permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.region",props.cohort?.status)&& attribute.info.isRegionRandom === true ? {
                label: formatMessage({id: 'projects.randomization.distributionRegion'}),
                key: 3,
            } : false,
            permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.factor",props.cohort?.status)&& attribute.info.isRegionRandom === true ? {
                label: formatMessage({id: 'projects.randomization.distributionFactor'}),
                key: 0,
            } : false,
        ].filter(Boolean)
    }
    return (
        <React.Fragment>
            <Spin spinning={loading}>
                <Form form={searchForm}>

                    {
                        randomType === 1 ?
                            <>
                                <Row justify='space-between'  style={{marginTop: 6, marginBottom: 16}}>
                                    <Col>
                                        <Space size={25}>
                                            <Form.Item label={formatMessage({id: 'common.between'})}>
                                                <Input.Group compact>
                                                    <Form.Item noStyle name="rangeType" initialValue={1}>
                                                        <Select onChange={(e)=>{
                                                            setRangeType(e)
                                                            if (e === 1){
                                                                searchForm.resetFields(["startStr","endStr"])
                                                            }else if (e === 2){
                                                                searchForm.resetFields(["start","end"])
                                                            }
                                                        }}  style={{ minWidth: 100}}>
                                                            <Option
                                                                value={1}>{formatMessage({id: "projects.randomization.block"})}</Option>
                                                            <Option
                                                                value={2}>{formatMessage({id: "projects.randomization.randomNumber"})}</Option>
                                                        </Select>
                                                    </Form.Item>
                                                    {
                                                        rangeType == 1 ?
                                                            <>
                                                                <Form.Item noStyle name="start">
                                                                    <InputNumber style={{zIndex: 2, marginLeft:'8px',width: 100}} controls={false} onChange={searchBlock} min={0}/>
                                                                </Form.Item>
                                                                <Input
                                                                    style={{
                                                                        backgroundColor: "#fff",
                                                                        width: 30,
                                                                        zIndex: 1,
                                                                        pointerEvents: 'none',
                                                                    }}
                                                                    placeholder="~"
                                                                    disabled
                                                                />
                                                                <Form.Item noStyle name="end">
                                                                    <InputNumber style={{zIndex: 2,width: 100}} controls={false} onChange={searchBlock} min={0}/>
                                                                </Form.Item>
                                                            </>
                                                            : null
                                                    }
                                                    {
                                                        rangeType == 2 ?
                                                            <>
                                                                <Form.Item noStyle name="startStr">
                                                                    <Input style={{ zIndex: 2,marginLeft:'8px',width: 100}}  onChange={searchBlock} />
                                                                </Form.Item>
                                                                <Input
                                                                    style={{
                                                                        backgroundColor: "#fff",
                                                                        width: 30,
                                                                        zIndex: 1,
                                                                        pointerEvents: 'none',

                                                                    }}
                                                                    placeholder="~"
                                                                    disabled
                                                                />
                                                                <Form.Item noStyle name="endStr">
                                                                    <Input style={{zIndex: 2,width: 100}} onChange={searchBlock} />
                                                                </Form.Item>
                                                            </>
                                                            : null
                                                    }

                                                </Input.Group>
                                            </Form.Item>

                                            <Form.Item label={
                                                <>
                                                    <FormattedMessage id="common.status"/>
                                                <Tooltip
                                                trigger={["hover", "click"]}
                                                overlayInnerStyle={{
                                                    width: 310,
                                                    fontSize: 12,
                                                    background: "#646566",
                                                }}
                                                placement="top"
                                                title={
                                                    <>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.1" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.2" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.3" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.4" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.5" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.6" />
                                                        </Row>
                                                        <p/>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.7" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.8" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.9" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.10" />
                                                        </Row>
                                                        <Row>
                                                            <FormattedMessage id="randomization.random.number.block.status.tip.11" />
                                                        </Row>
                                                    </>
                                                }
                                            >
                                                <svg
                                                    className="iconfont mouse"
                                                    width={12}
                                                    height={12}
                                                    style={{ marginLeft: 4 }}
                                                >
                                                    <use xlinkHref="#icon-xinxitishi" />
                                                </svg>
                                            </Tooltip>
                                                </>}>
                                                <Input.Group compact>
                                                    <Form.Item noStyle name={"statusType"}>
                                                        <Select
                                                            allowClear
                                                            style={{ minWidth: 100}}
                                                            onChange={(e) => {
                                                                setStatusType(e)
                                                                setStatus([])
                                                            }}
                                                            placeholder={formatMessage({id: "placeholder.select.common"})}
                                                        >
                                                            <Select.Option
                                                                value={1}>{formatMessage({id: "projects.randomization.block"})}</Select.Option>
                                                            <Select.Option
                                                                value={2}>{formatMessage({id: "projects.randomization.randomNumber"})}</Select.Option>
                                                        </Select>
                                                    </Form.Item>
                                                    {
                                                        statusType === 1 ?
                                                            <Form.Item noStyle name={"status"}>
                                                                <Select
                                                                    allowClear
                                                                    mode="multiple"
                                                                    style={{ minWidth: 200}}
                                                                    onChange={(e) => setStatus(e)}
                                                                    placeholder={formatMessage({id: "placeholder.select.common"})}
                                                                    showArrow={true}
                                                                    showSearch={false}
                                                                >
                                                                    <Select.Option
                                                                        value={1}>{formatMessage({id: "randomization.random.number.block.all.usable"})}</Select.Option>
                                                                    <Select.Option
                                                                        value={2}>{formatMessage({id: "randomization.random.number.block.partially.usable"})}</Select.Option>
                                                                    <Select.Option
                                                                        value={3}>{formatMessage({id: "randomization.random.number.block.used"})}</Select.Option>
                                                                    <Select.Option
                                                                        value={4}>{formatMessage({id: "randomization.random.number.block.invalid"})}</Select.Option>
                                                                    <Select.Option
                                                                        value={5}>{formatMessage({id: "randomization.random.number.not.available"})}</Select.Option>
                                                                </Select>
                                                            </Form.Item>
                                                            :
                                                            null
                                                    }
                                                    {
                                                        statusType === 2 ?
                                                            <Form.Item noStyle name={"status"}>
                                                                <Select
                                                                    allowClear
                                                                    mode="multiple"
                                                                    style={{ minWidth: 200}}
                                                                    onChange={(e) => setStatus(e)}
                                                                    placeholder={formatMessage({id: "placeholder.select.common"})}
                                                                >
                                                                    <Select.Option
                                                                        value={1}>{formatMessage({id: "randomization.random.number.unused"})}</Select.Option>
                                                                    <Select.Option
                                                                        value={5}>{formatMessage({id: "randomization.random.number.not.available"})}</Select.Option>
                                                                    <Select.Option
                                                                        value={3}>{formatMessage({id: "randomization.random.number.invalid"})}</Select.Option>
                                                                    <Select.Option
                                                                        value={2}>{formatMessage({id: "randomization.random.number.block.used"})}</Select.Option>

                                                                </Select>
                                                            </Form.Item>
                                                            :
                                                            null
                                                    }
                                                </Input.Group>
                                            </Form.Item>

                                        </Space>
                                    </Col>
                                    <Col>
                                        <Space>
                                            {
                                                (auth.project?.status === 1 ||
                                                    auth.project?.status === 2 ||
                                                    auth.project?.status === 3 ||
                                                    auth.project?.status === 4 ||
                                                    props.cohort?.status === 4) ? null :
                                                    <Button onClick={() => showAllocationLimit()}>
                                                        {formatMessage({id: 'menu.settings'})}
                                                    </Button>

                                            }
                                            {/*{*/}
                                            {/*    permissions(auth.project.permissions, "operation.build.randomization.list.segmentation.view")*/}
                                            {/*    && permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.factor", props.cohort?.status)*/}
                                            {/*    && !lockConfig*/}
                                            {/*    && projectStatus !== 2*/}
                                            {/*    && (*/}
                                            {/*        <Button*/}
                                            {/*            className="full-width"*/}
                                            {/*            onClick={() => !selectedRowKeys.length*/}
                                            {/*                ? message.warn(formatMessage({id: "menu.projects.project.build.randomization.tooltip"}))*/}
                                            {/*                : showAllocation(0)*/}
                                            {/*            }*/}
                                            {/*        >{formatMessage({id: 'projects.randomization.distributionFactor'})}</Button>*/}
                                            {/*    )*/}
                                            {/*}*/}
                                            {
                                                permissions(auth.project.permissions, "operation.build.randomization.list.segmentation.view") && projectStatus !== 2 &&
                                                (permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.site", props.cohort?.status)
                                                    || permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.country", props.cohort?.status)
                                                    || permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.region", props.cohort?.status)
                                                    || permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.factor", props.cohort?.status)
                                                )
                                                && (
                                                    <Dropdown
                                                        menu={{
                                                            items: items() as any,
                                                            onClick: ({key}) => showAllocation(+key)
                                                        }}
                                                        open={open}
                                                        onOpenChange={setOpen}
                                                        trigger={['hover']}
                                                    >
                                                        <Button
                                                            type="primary"
                                                        >
                                                            <Space>
                                                                <FormattedMessage
                                                                    id={"projects.randomization.distributions"}/>
                                                                <DownOutlined
                                                                    style={{transform: `rotate(${open ? "180deg" : 0})`}}/>
                                                            </Space>
                                                        </Button>
                                                    </Dropdown>
                                                )}
                                        </Space>
                                    </Col>
                                </Row>

                                <Table
                                    loading={getBlockLoading || getRandomNumberGroupLoading}
                                    size="small"
                                    rowSelection={rowSelection}
                                    columns={columns}
                                    dataSource={data}
                                    pagination={false}
                                    className="mar-top-5"
                                    scroll={{x: 'max-content', y: 'calc(100vh - 290px)'}}
                                    rowKey={(record) => (record.block)}
                                    expandable={{
                                        expandedRowRender,
                                        expandedRowClassName:()=>"bg-white",
                                        expandIcon: ({expanded, onExpand, record}) =>
                                            expanded ? (
                                                <CaretDownOutlined onClick={e => onExpand(record, e)}/>
                                            ) : (
                                                <CaretRightOutlined onClick={e => onExpand(record, e)}/>
                                            ),
                                        onExpand:(e, d) => {
                                            getBlockDetail(e, d, false)
                                        }
                                    }}
                                />
                            </>
                            :
                            <Table
                                columns={subcolumn}
                                size="small"
                                pagination={false}
                                dataSource={data}
                                loading={getBlockLoading || getRandomNumberGroupLoading}
                            />
                    }


                </Form>

            </Spin>
            {
                randomType === 1 ?
                    <Footer>
                        <Pagination
                            hideOnSinglePage={false}
                            className="text-right"
                            current={currentPage}
                            pageSize={pageSize}
                            pageSizeOptions={['10', '20', '50', '100', '500']}
                            total={total}
                            showSizeChanger
                            showTotal={(total, range) => (`${range[0]} - ${range[1]} / ${total}`)}
                            onChange={(page, pageSize) => {
                                setCurrentPage(page);
                            }}
                            onShowSizeChange={(current, size) => {
                                setCurrentPage(1);
                                setPageSize(size);
                            }}
                        />
                    </Footer> :
                    null
            }

            <RandomNumberAllocations bind={allocation_ref} refresh={list} blockrefresh={getBlockDetail} cohort={props.cohort}/>
            <RandomNumberAllocationsLimit bind={allocation_limit_ref} cohort={props.cohort} attribute={props.attribute}/>
        </React.Fragment>

    );
}

const CustomInputNumber = styled(InputNumber)`
    .ant-input-number-input-wrap {
        height: 32px;
        display: flex;
        align-items: center;
    }
` 
