import React from 'react';
import {Col, Form, message, Modal, Radio, Row, Select} from "antd";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {
    getGroupDistributionStatistics,
    getProjectAttribute,
    getProjectSite,
    getRegion,
    updateProjectSite
} from "../../../../api/randomization";
import {useIntl} from "react-intl";
import {getSiteCountry} from "../../../../api/project_site";
import {useGlobal} from "../../../../context/global";
import {CustomConfirmModal} from "../../../../components/modal";

export const RandomNumberAllocations = (props:any) => {
    const g = useGlobal()
    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;

    const {Option} = Select;
    const [type, setType] = useSafeState<any>(0);
    const [visible, setVisible] = useSafeState<any>(false);
    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<any>([]);
    const [blockData, setBlockData] = useSafeState<any>([]);

    const [isRandom, setIsRandom] = useSafeState<any>(false);
    const [attributeId, setAttributeId] = useSafeState<any>("");
    const [randomListId, setRandomListId] = useSafeState<any>("");
    const [option, setOption] = useSafeState<any>([]);
    const [regions, setRegions] = useSafeState<any>([])
    const [country, setCountry] = useSafeState<any>([])
    const [factor, setFactor] = useSafeState<any>([]);
    const [check, setCheck] = useSafeState<any>(false);
    const [form] = Form.useForm();
    const [title,setTitle] = useSafeState("")
    const [label,setLabel] = useSafeState("")
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const [openBlock, setOpenBlock] = useSafeState<any>({});


    const show = (type: any, blocks: any, id: any, center: any, other: any, factor: any, openBlock: any, blockData: any) => {
        switch (type) {
            case 0:
                setTitle(formatMessage({id: 'projects.randomization.distributionFactor'}))
                break
            case 1:
                setTitle(formatMessage({id: 'projects.randomization.distributionCenter'}))
                setLabel(formatMessage({id: 'common.site'}))
                break
            case 2:
                setTitle(formatMessage({id: 'projects.randomization.distributionCountry'}))
                setLabel(formatMessage({id: 'projects.attributes.country'}))
                break
            case 3:
                setTitle(formatMessage({id: 'projects.randomization.distributionRegion'}))
                setLabel(formatMessage({id: 'projects.attributes.regionLayered'}))
                break
        }
        setOpenBlock(openBlock)
        setType(type)
        getConfig()
        setSelectedRowKeys(blocks)
        setRandomListId(id)
        setFactor(factor)
        setVisible(true)
        setBlockData(blockData)
    }

    React.useImperativeHandle(props.bind, () => ({show}));

    const {runAsync: getProjectSiteRun} = useFetch(getProjectSite, {manual: true})
    const {runAsync: updateProjectSiteRun, loading: updateProjectSiteLoading} = useFetch(updateProjectSite, {manual: true})
    const {runAsync: getProjectAttributeRun} = useFetch(getProjectAttribute, {manual: true})
    const { runAsync: getRegionRun ,loading: getRegionLoading  } = useFetch(getRegion, { manual: true })
    const { runAsync: countryRun ,loading: countryLoading  } = useFetch(getSiteCountry, { manual: true })
    const { runAsync: getGroupDistributionStatisticsRun ,loading: getGroupDistributionStatisticsLoading} = useFetch(getGroupDistributionStatistics, { manual: true })


    const save = () => {
        form.validateFields().then(value => {
            let data = {...value}
            if (check) {
                const layered = factor.find((it:any) => (it.number === check)).layered
                layered.forEach((item:any) => {
                    data[item.name] = {"label": item.text, "value": item.value}
                })
            }

            if (value.region === undefined && value.country === undefined && value.site === undefined && check === false) {
                if (type === 1){
                    message.error(formatMessage({id: 'projects.randomization.nullSite'}));
                }else if (type === 2){
                    message.error(formatMessage({id: 'projects.randomization.nullCountry'}));
                }else if (type === 3){
                    message.error(formatMessage({id: 'projects.randomization.nullRegion'}));
                }else{
                    message.error(formatMessage({id: 'projects.randomization.nullFactor'}));

                }
                return
            }
            // updateProjectSiteRun({randomListId},{"block": selectedRowKeys, ...data}).then(
            //     (d: any) => {
            //         message.success(d.msg)
            //         setSelectedRowKeys([])
            //         props.refresh(null, true)
            //         let block :any= []
            //         selectedRowKeys.forEach(
            //            ( it:any) => {
            //                 if (openBlock[it]){
            //                     block.push(it)
            //                 }
            //             }
            //         )
            //         props.blockrefresh(true,null,block)
            //         onCancel()
            //     }
            // )
            confirmSave(data);
        })

    };

    const confirmSave = (data: any) => {
        const currentCount = blockData
            .filter((item: any) => selectedRowKeys.includes(item.block)) // 筛选出符合条件的对象
            .reduce((total: number, item: any) => total + item.count, 0); // 计算 count 的总和

        let target = "";
        let totalCount = 0;
        let bodyData = {};
        if (type === 0) {
            const layered = factor.find((it:any) => (it.number === data['factor'])).layered
            target = formatMessage({id: "randomization.config.factors"}).toLowerCase() + " " + layered
                .filter((item: any) => item.name !== "")
                .map((item: any) => `${item.label}:${item.text}  `).join("")
            bodyData = {type: 0, factors: layered}
        } else if (type === 1) { // 库房选项不生效
            const siteId = data["site"]
            let siteName = ""
            if (siteId != "0") {
                siteName = option.find((it: any) => (it.id === siteId)).name
            } else {
                // 库房选项不生效
                siteName = formatMessage({id: 'storehouse.name'})
                target = formatMessage({id: "common.site"}).toLowerCase() + " " + siteName + " "
                return confirmDetail(target, currentCount, totalCount, data)
            }
            target = formatMessage({id: "common.site"}).toLowerCase() + " " + siteName + " "

            bodyData = {type: 1, projectSiteId: siteId}
        } else if (type === 2) {
            const countryCode = data["country"];
            const countryName = country.find((it: any) => (it.code === countryCode))[languageHandler(g.lang)]
            target = formatMessage({id: "projects.attributes.country"}).toLowerCase() + " " + countryName + " "

            bodyData = {type: 2, country: countryCode}
        } else if (type === 3) {
            const regionId = data["region"];
            const regionName = regions.find((it: any) => (it.id === regionId)).name
            target = formatMessage({id: "projects.attributes.regionLayered"}).toLowerCase() + " " + regionName + " "

            bodyData = {type: 3, regionId: regionId}
        }

        getGroupDistributionStatisticsRun({randomListId: randomListId}, bodyData).then(
            (result: any) => {
                totalCount = result.data

                confirmDetail(target, currentCount, totalCount, data)
            }
        )
    };

    const confirmDetail = (target: string, currentCount: number, totalCount: number, data: any) => {
        let content = formatMessage({ id: "projects.randomization.distributionContent" },
            {
                target: target,
                currentCount: currentCount,
                totalCount: totalCount
            });

        CustomConfirmModal({
            title: formatMessage({ id: "projects.randomization.distributionConfirm" }),
            content: content,
            cancelText: formatMessage({ id: 'common.cancel' }),
            okText: formatMessage({ id: 'common.ok' }),
            onOk: () => {
                saveRandomizationDistribution(data);
            }
        })
    }


    const saveRandomizationDistribution = (data: any) => {
        updateProjectSiteRun({randomListId},{"block": selectedRowKeys, ...data}).then(
            (d: any) => {
                message.success(d.msg)
                setSelectedRowKeys([])
                props.refresh(null, true)
                let block :any= []
                selectedRowKeys.forEach(
                    ( it:any) => {
                        if (openBlock[it]){
                            block.push(it)
                        }
                    }
                )
                props.blockrefresh(true,null,block)
                onCancel()
            }
        )
    }

    const radioCheck = (value:any) => {
        if (value === check) {
            setCheck(false)
        } else {
            setCheck(value)
        }
    }

    const getConfig = React.useCallback(
        () => {
            getProjectSiteRun({customerId, projectId, envId}).then(
                (result:any) => {
                    if (result.data != null) {
                        setOption(result.data)
                    }
                }
            )
            getProjectAttributeRun({projectId, env: envId, cohort: cohortId, customer: customerId}).then(
                (result:any) => {
                    setAttributeId(result.data.id);
                    setIsRandom(result.data.info.isRandom);
                }
            )
            getRegionRun({
                envId: envId,
                cohortId: cohortId
            }).then((result:any) => setRegions(result.data))
            countryRun({
                envId: envId
            }).then((result:any) => setCountry(result.data))
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [customerId, props, projectId, envId]
    )

    const onCancel = () => {
        setVisible(false)
        form.resetFields()
        setCheck(false)
    }
    const languageHandler = (lang:any) => {
        let language = "en";
        switch (lang) {
            case 'en':
                language = "en";
                break;
            case 'zh':
                language = "cn";
                break;
            case 'ko':
                language = "ko";
                break;
        }
        return language
    };

    return (
        <Modal
            centered={true}
            maskClosable={false}
            
            destroyOnClose={true}
            title={title}
            width={"40%"}
            open={visible}
            onCancel={onCancel}
            onOk={save}
            okText={formatMessage({ id: 'common.ok' })}
            confirmLoading={updateProjectSiteLoading || getGroupDistributionStatisticsLoading}
            className='custom-small-modal'
        >
            <Form form={form}>
                <Row gutter={24}>
                    {
                            <Col xs={24} sm={24} md={24} lg={24}>
                                {type === 0 &&
                                    <Form.Item name="factor">
                                        <Row>
                                            <Radio.Group value={check}>
                                                {
                                                    factor.map((value: any) =>
                                                        <Radio
                                                            onClick={() => {
                                                                radioCheck(value.number)
                                                            }}
                                                            key={value.number}
                                                            value={value.number}>{value.layered.map((item: any) => item.label+":"+item.text + "  ")} </Radio>
                                                    )
                                                }
                                            </Radio.Group>
                                        </Row>
                                    </Form.Item>
                                }
                                {type === 1 &&
                                <Form.Item label={label} name="site">
                                    <Select allowClear showSearch={true}
                                            optionFilterProp="children"
                                            placeholder={formatMessage({id: 'placeholder.select.search'})}
                                            >
                                        <Select.Option
                                            value="0">{formatMessage({id: 'storehouse.name'})}</Select.Option>
                                        {option.map((value:any) =>
                                            <Option key={value.id}
                                                    value={value.id}>{value.number}-{value.name}</Option>
                                        )}
                                    </Select>
                                </Form.Item>
                                }
                                {type === 2 &&
                                    <Form.Item label={label} name="country">
                                        <Select allowClear showSearch={true}
                                                optionFilterProp="children"
                                                placeholder={formatMessage({id: 'placeholder.select.search'})}
                                        >
                                            {country.map((value:any) =>
                                                <Option key={value.code}
                                                        value={value.code}>{value[languageHandler(g.lang)]}</Option>
                                            )}
                                        </Select>
                                    </Form.Item>
                                }
                                {type === 3 &&
                                    <Form.Item label={label} name="region">
                                        <Select allowClear showSearch={true}
                                                optionFilterProp="children"
                                                placeholder={formatMessage({id: 'placeholder.select.search'})}
                                        >
                                            {regions.map((value:any) =>
                                                <Option key={value.id}
                                                        value={value.id}>{value.name}</Option>
                                            )}
                                        </Select>
                                    </Form.Item>
                                }
                            </Col>
                    }
                </Row>
            </Form>
        </Modal>
    );
}
