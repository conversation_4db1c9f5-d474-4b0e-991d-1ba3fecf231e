import React from "react";
import { <PERSON><PERSON>, <PERSON>, message, Row, Switch, Table } from "antd";
import { Add } from "./add";
import { permissions } from '../../../../tools/permission'
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { delProjectStoreHouse, storehousesMedicine } from "../../../../api/project_storehouse";
import { allStorehouses } from "../../../../api/storehouses";
import { CustomConfirmModal } from "components/modal";
import { InsertDivider } from "components/divider";
import { handlerRowSpan } from "../../../../utils/cell_merge";
import { Medicine_info } from "./medicine_info";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import {AuthButton} from "../../../common/auth-wrap";

export const StorehouseManage = (props) => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [data, setData] = useSafeState([]);              // 模拟数据
    const [storehouseOptions, setStorehouseOptions] = useSafeState([]);
    const storeHouseAdd = React.useRef();
    const medicineInfo = React.useRef();
    const auth = useAuth()
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env.id;
    const projectStatus = auth.project.status ? auth.project.status : 0

    const { runAsync: run_allStorehouses, loading: allStorehousesLoading } = useFetch(allStorehouses, { manual: true })
    const { runAsync: run_storehousesMedicine, loading: storehousesMedicineLoading } = useFetch(storehousesMedicine, { manual: true })
    const {
        runAsync: run_delProjectStoreHouse,
        loading: delProjectStoreHouseLoading
    } = useFetch(delProjectStoreHouse, { manual: true })
    React.useEffect(() => {
        list();
    }, []);

    const list = () => {
        setData([])
        run_allStorehouses({}).then((response) => {
            let data = response.data
            setStorehouseOptions(data && data.items ? data.items : [])
        })
        run_storehousesMedicine({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
            // attributeId: auth.attribute.id,
        }).then(
            (response) => {
                setData(response.data)
            })
    }

    const add = (data) => {
        storeHouseAdd.current.show(data);
    };
    const setting = (data) => {
        medicineInfo.current.show(data)
    }
    const remove = (id, name) => {
        CustomConfirmModal({
            title: formatMessage({ id: 'common.confirm.delete' }),
            okText: formatMessage({ id: 'common.ok' }),
            cancelText: formatMessage({ id: 'common.cancel' }),
            onOk: () => {
                run_delProjectStoreHouse({ id: id })
                    .then((result) => {
                        message.success(result.msg)
                        list()
                    })
            }
        });
    };

    return (
        <React.Fragment>
            <Row gutter={8} justify="space-between">
                <Col xs={24} sm={24} md={12} lg={6}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <svg className="iconfont" width={16} height={16}>
                            <use xlinkHref="#icon-quanbukufang" />
                        </svg>
                        <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px" }}><FormattedMessage id={'storehouse.all'} /></span>
                    </div>
                </Col>
                {projectStatus !== 2 &&
                    <Col>
                        {permissions(auth.project.permissions, "operation.build.storehouse.add") &&
                            <AuthButton style={{ marginLeft: 12 }} type="primary" onClick={add}><FormattedMessage
                                id="common.add" /></AuthButton>
                        }
                    </Col>
                }
            </Row>

            {
                permissions(auth.project.permissions, "operation.build.storehouse.view") ?
                    <Table
                        bordered
                        loading={storehousesMedicineLoading || allStorehousesLoading}
                        className="mar-top-10"
                        dataSource={data}
                        pagination={false}
                        scroll={{ x: "max-content" }}
                        rowKey={(record) => (record.id)}
                    >
                        <Table.Column title={<FormattedMessage id="projects.storehouse.name" />} dataIndex="name"
                            key="name" ellipsis width={300}
                            onCell={(_, index) => {
                                return handlerRowSpan(data, "id", index)
                            }
                            }
                            render={
                                (value, record, index) => (
                                    <div
                                        style={{ whiteSpace: 'pre-line', width: "290px" }}
                                    >
                                        {value}
                                    </div>
                                )
                            }
                        />
                        <Table.Column title={<FormattedMessage id="drug.configure.drugName" />} dataIndex="medicineName"
                            key="medicineName" ellipsis width={200}
                            render={(value, record, index) => {
                                if (value === "") {
                                    return "-"
                                } else {
                                    return value
                                }
                            }
                            }
                        />
                        <Table.Column title={<FormattedMessage id="storehouse.alert" />} dataIndex="alert"
                            key="alert" ellipsis width={100}
                            render={(value, record, index) => {
                                if (record.medicineName === "") {
                                    return "-"
                                } else {
                                    return value
                                }
                            }
                            }
                        />
                        <Table.Column title={<FormattedMessage id="projects.supplyPlan.validityReminder" />}
                            dataIndex="validityReminder"
                            key="validityReminder" ellipsis width={150}
                            render={(value, record, index) => {
                                if (record.medicineName === "") {
                                    return "-"
                                } else {
                                    return value
                                }
                            }
                            }
                        />
                        <Table.Column title={<FormattedMessage id="projects.storehouse.connected" />}
                            dataIndex="connected"
                            key="connected"
                            ellipsis
                            width={100}
                            render={
                                (value, record, index) => (
                                    <Switch
                                        size="small"
                                        checked={!!value}
                                        defaultChecked
                                        disabled
                                    />
                                )
                            }
                            onCell={(_, index) => {
                                return handlerRowSpan(data, "id", index)
                            }
                            }
                        />
                        <Table.Column
                            title={<FormattedMessage id="common.operation" />}
                            width={150}
                            fixed={"right"}
                            render={
                                (value, record, index) => {
                                    const btns = [];
                                    if (permissions(auth.project.permissions, "operation.build.storehouse.notice")) {
                                        btns.push(<AuthButton
                                            style={{ padding: 0 }}
                                            size="small" type="link"
                                            onClick={() => setting(record)}>
                                            <FormattedMessage id="common.setting" />
                                        </AuthButton>)
                                    }
                                    if (permissions(auth.project.permissions, "operation.build.storehouse.edit") && projectStatus !== 2) {
                                        btns.push(<AuthButton
                                            style={{ padding: 0 }}
                                            size="small" type="link"
                                            onClick={() => add(record)}><FormattedMessage
                                                id="common.edit" /></AuthButton>)

                                    }
                                    if ((permissions(auth.project.permissions, "operation.build.storehouse.delete") && projectStatus !== 2)) {
                                        btns.push(<AuthButton
                                            loading={delProjectStoreHouseLoading}
                                            style={{ padding: 0 }}
                                            size="small" type="link"
                                            onClick={() => remove(record.id, record.name)}><FormattedMessage
                                                id="common.delete" /></AuthButton>)
                                    }
                                    return InsertDivider(btns)
                                }
                            }
                            onCell={(_, index) => {
                                return handlerRowSpan(data, "id", index)
                            }
                            }
                        />
                    </Table>
                    :
                    null
            }
            <Add bind={storeHouseAdd} options={storehouseOptions} refresh={list} />
            <Medicine_info bind={medicineInfo} refresh={list} />
        </React.Fragment>
    )
}