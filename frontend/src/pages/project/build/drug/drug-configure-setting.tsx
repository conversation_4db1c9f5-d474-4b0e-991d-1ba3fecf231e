import React, {useEffect, useMemo, useState} from "react";
import { FormattedMessage, useIntl } from "react-intl";
import type { RadioChangeEvent, InputNumberProps } from "antd";
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  notification,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Table,
  Tooltip,
  Typography, TimePicker,
} from "antd";
import {
  PlusOutlined,
  CloseCircleFilled,
  QuestionCircleFilled, MinusCircleFilled
} from "@ant-design/icons";
import { getFormType } from "../../../../api/form";
import {
  getAllocationGroup,
  getGetUpDrugConfigureLabel,
  getIsBlindedRole,
  getSetUpDrugConfigure,
  getSetUpDrugConfigureVisitList,
  setUpDrugConfigure,
} from "../../../../api/drug";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import styled from "@emotion/styled";
import { useDrug } from "./context";
import { FormulaData } from "../../../../data/data";
import Draggable from "react-draggable";
import { Formula, WeightCol } from "./formula";
import { permissions, permissionsCohort } from "../../../../tools/permission";
import { arrayMove } from "@dnd-kit/sortable";
import { DragEndEvent } from "@dnd-kit/core";
import { TableDraggableSortable } from "../../../../components/TableDraggableSortable";
import { TableSortableRow } from "../../../../components/TableSortableRow";
import _ from "lodash";
import {Title} from "../../../../components/title";

export const DrugConfigureSetting = (props: any) => {
  const g = useGlobal();
  const auth = useAuth();
  const intl = useIntl();
  const drug = useDrug();
  const { formatMessage } = intl;
  const { Link } = Typography;

  const [visible, setVisible] = useSafeState<boolean>(false);
  const [form] = Form.useForm();
  const [doseForms, setDoseForms] = useSafeState<any[]>([]);
  const [selectType, setSelectType] = useSafeState<any>(1);
  const [doseFormId, setDoseFormId] = useSafeState<any>(null);

  const [isOpen, setIsOpen] = useState(false);
  const [send, setSend] = useSafeState(true);
  const [oldData, setOldData] = useSafeState(null);

  const [firstInitial, setFirstInitial] = useState(false);
  const [doseReduction, setDoseReduction] = useState(false);
  const [doseLevelListOpen, setDoseLevelListOpen] = useState(false);

  const [doseLevelListData, setDoseLevelListData] = useSafeState<any>([]);
  const [doseLevelGroups, setDoseLevelGroups] = useSafeState<any[]>([]);
  const [doseLevelLabels, setDoseLevelLabels] = useSafeState<any>(new Map());

  const [visitJudgmentListData, setVisitJudgmentListData] = useSafeState<any>([]);

  const [visitJudgmentGroups, setVisitJudgmentGroups] = useSafeState<any[]>([]);
  const [visitJudgmentLabels, setVisitJudgmentLabels] = useSafeState<any>(new Map());

  const [visitJudgmentEdit, setVisitJudgmentEdit] = useSafeState<any>(new Map());

  const [doseLabelList, setDoseLabelList] = useSafeState<any>([]);

  const [doseVisitList, setDoseVisitList] = useSafeState<any>([]);

  const [height, setHeight] = useSafeState<any>(window.innerHeight - 232);

  const [isAdd, setIsAdd] = useSafeState<any>(false);
  const [isEdit, setIsEdit] = useSafeState<any>(false);

  const [isBlindedRole, setIsBlindedRole] = useSafeState<any>(false);

  const [formRecordList, setFormRecordList] = useSafeState<any[]>([]);

  const projectId = auth.project.id;
  const envId = auth.env ? auth.env.id : null;
  const cohortId = props.cohort ? props.cohort.id : null;
  const customerId = auth.customerId;

  const [dtpRules, setDtpRules] = useSafeState(0);
  const [drugNameList, setDrugNameList] = useSafeState<any[]>([]);
  const [dispensingMethodList, setDispensingMethodList] = useSafeState<any[]>([
    {value: 1, label: formatMessage({id: 'logistics.send.site'})},
    {value: 2, label: formatMessage({id: 'logistics.send.site.subject'})},
    {value: 3, label: formatMessage({id: 'logistics.send.depot.subject'})},
  ]);

  const { runAsync: getFormTypeRun, loading: getFormTypeLoading } = useFetch(
    getFormType,
    { manual: true }
  );

  const { runAsync: getSetUpDrugConfigureRun, loading: getSetUpDrugConfigureLoading } = useFetch(
    getSetUpDrugConfigure,
    { manual: true }
  );

  const { runAsync: getSetUpDrugConfigureVisitListRun, loading: getSetUpDrugConfigureVisitListLoading } = useFetch(
    getSetUpDrugConfigureVisitList,
    { manual: true }
  );

  const {
    runAsync: getGetUpDrugConfigureLabelRun,
    loading: getGetUpDrugConfigureLabelLoading,
  } = useFetch(getGetUpDrugConfigureLabel, { manual: true });

  // const {
  //   runAsync: upDrugConfigureSetRun,
  //   loading: setUpDrugConfigureRunLoading,
  // } = useFetch(setUpDrugConfigure, { manual: true });


  const getNew = () => {
    setDispensingMethodList([
      {value: 1, label: formatMessage({id: 'logistics.send.site'})},
      {value: 2, label: formatMessage({id: 'logistics.send.site.subject'})},
      {value: 3, label: formatMessage({id: 'logistics.send.depot.subject'})},
    ]);
  }

const {runAsync: upDrugConfigureSetRun, loading: setUpDrugConfigureRunLoading} = useFetch(
  setUpDrugConfigure, 
    { 
        manual: true,
        onError: (err: any) => {
            // form.resetFields();
            err.json().then((data: any) =>
                notification.open({
                    message: (
                        <div
                            style={{
                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                fontSize: "14px",
                            }}
                        >
                            <CloseCircleFilled
                                style={{
                                    color: "#F96964",
                                    paddingRight: "8px",
                                }}
                            />
                            {formatMessage({ id: "common.operation.error" })}
                        </div>
                    ),
                    description: (
                        <div
                            style={{
                                paddingLeft: "20px",
                                color: "#646566",
                            }}
                        >
                            {data.msg}
                        </div>
                    ),
                    duration: 5,
                    placement: "top",
                    style: {
                        width: "720px",
                        // height: "88px",
                        background: "#FEF0EF",
                        borderRadius: "4px",
                    },
                })
            );
        },
      }
  );


  const { runAsync: getAllocationGroupRun, loading: getAllocationGroupLoading } = useFetch(
    getAllocationGroup,
    { manual: true }
  );

  const { runAsync: getIsBlindedRoleRun, loading: getIsBlindedRoleLoading } = useFetch(
    getIsBlindedRole,
    { manual: true }
  );


  const getBlindedRole = () => {
    getIsBlindedRoleRun({
      roleId: auth.project.permissions.role_id,
    }).then((result: any) => {
      setIsBlindedRole(result.data)
    });
  };

  const show = (dtpRules: any, drugNameList: any) => {
    setDtpRules(dtpRules);
    setDrugNameList(drugNameList);
    getBlindedRole();
    setUpDrugConfigureRun();
    // isEditFun();
  };

  const hide = () => {
    setDtpRules(0);
    setDrugNameList([]);
    setOldData(null);
    setSend(true);
    setVisible(false);
    setDoseForms([]);
    setSelectType(1);
    setIsOpen(false);
    setDoseFormId(null);
    setFirstInitial(false);
    setDoseReduction(false);
    setDoseLevelListData([]);
    setVisitJudgmentListData([]);
    setVisitJudgmentEdit(new Map());
    setDoseLevelLabels(new Map());
    setVisitJudgmentLabels(new Map());
    form.resetFields();
  };

  const doseLabelsRun = (groups: any) => {
    getGetUpDrugConfigureLabelRun({
      customerId: customerId,
      projectId: projectId,
      envId: envId,
      cohortId: cohortId,
      roleId: auth.project.permissions.role_id,
      groups: groups,
    }).then((result: any) => {
      let options:any = [];
      if (result.data !== null && result.data.length > 0) {
        for (let i = 0; i < result.data.length; i++) {
          options.push({
            name: result.data[i].name,
            id: result.data[i].id,
            group: result.data[i].group,
            isLabel: result.data[i].isLabel,
          });
        }
      }
      setDoseLabelList(options);
      if (form.getFieldValue("doseLevelList") !== null && form.getFieldValue("doseLevelList")?.length > 0) {
        for (let j = 0; j < form.getFieldValue("doseLevelList").length; j++) {
          let labels: any = [];
          if (options !== null && options.length > 0) {
            for (let i = 0; i < options.length; i++) {
              if (form.getFieldValue("doseLevelList")[j].group !== null && form.getFieldValue("doseLevelList")[j].group.length > 0) {
                if (form.getFieldValue("doseLevelList")[j].group.indexOf(options[i].group) !== -1) {
                  if (!options[i].isLabel) {
                    labels.push({
                      label: options[i].name,
                      value: options[i].id,
                    });
                  }
                }
              }
            }
          }
          updateField("doseLevelLabels", j, labels);
        }
      }
      if (form.getFieldValue("visitJudgmentList") !== null && form.getFieldValue("visitJudgmentList")?.length > 0) {
        for (let j = 0; j < form.getFieldValue("visitJudgmentList").length; j++) {
          let labels:any = [];
          if (options !== null && options.length > 0) {
            for (let i = 0; i < options.length; i++) {
              if (form.getFieldValue("visitJudgmentList")[j].group !== null && form.getFieldValue("visitJudgmentList")[j].group.length > 0) {
                if (form.getFieldValue("visitJudgmentList")[j].group.indexOf(options[i].group) !== -1) {
                  labels.push({
                    label: options[i].name,
                    value: options[i].id,
                  });
                }
              }
            }
          }
          updateField("visitJudgmentLabels", j, labels);
        }
      }
    });
  };

  const doseLevelLabelsRun = (index: any) => {
    let options:any = [];
    if (doseLabelList !== null && doseLabelList.length > 0) {
      for (let i = 0; i < doseLabelList.length; i++) {
        if (form.getFieldValue("doseLevelList")[index].group !== null && form.getFieldValue("doseLevelList")[index].group.length > 0) {
          if (form.getFieldValue("doseLevelList")[index].group.indexOf(doseLabelList[i].group) !== -1) {
            if (!doseLabelList[i].isLabel) {
              options.push({
                label: doseLabelList[i].name,
                value: doseLabelList[i].id,
              });
            }
          }
        } else {
          //删除组别要同时删除对应的标签\药物
          var doseLevelList: any[] = form.getFieldValue("doseLevelList");
          doseLevelList[index].doseDistribution = [];
          form.setFieldsValue({ doseLevelList });
          setDoseLevelListData(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : [])
        }
      }
    }
    updateField("doseLevelLabels", index, options);
    // console.log("options===" + JSON.stringify(Object.fromEntries(options.entries())))

    //删除组别要同时删除对应的标签\药物
    if (options !== null && options.length > 0) {
      let doseDistributionList:any = [];
      if (form.getFieldValue("doseLevelList")[index].doseDistribution !== null && form.getFieldValue("doseLevelList")[index].doseDistribution.length > 0) {
        for (let j = 0; j < form.getFieldValue("doseLevelList")[index].doseDistribution.length; j++) {
          for (let k = 0; k < options.length; k++) {
            if (form.getFieldValue("doseLevelList")[index].doseDistribution[j] === options[k].value) {
              doseDistributionList.push(options[k].value)
            }
          }
        }
      }
      var doseLevelList: any[] = form.getFieldValue("doseLevelList");
      doseLevelList[index].doseDistribution = doseDistributionList;
      form.setFieldsValue({ doseLevelList });
      setDoseLevelListData(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : [])
    } else {
      var doseLevelList: any[] = form.getFieldValue("doseLevelList");
      doseLevelList[index].doseDistribution = [];
      form.setFieldsValue({ doseLevelList });
      setDoseLevelListData(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : [])
    }

  };

  const visitJudgmentNamesRun = (index: any) => {
    // let result = false;
    if (doseLabelList !== null && doseLabelList.length > 0) {
      for (let i = 0; i < doseLabelList.length; i++) {
        if (form.getFieldValue("visitJudgmentList")[index].name !== null && (form.getFieldValue("visitJudgmentList")[index].group !== null && form.getFieldValue("visitJudgmentList")[index].group.length > 0) && form.getFieldValue("visitJudgmentList").length > 1) {
          const maxName = form.getFieldValue("visitJudgmentList")[index].name;
          let maxGroups:any = [];
          for (let i = 0; i < form.getFieldValue("visitJudgmentList")[index].group.length; i++) {
            maxGroups.push(form.getFieldValue("visitJudgmentList")[index].group[i])
          }
          for (let i = 0; i < form.getFieldValue("visitJudgmentList").length; i++) {
            if (i !== index) {
              for (let j = 0; j < form.getFieldValue("visitJudgmentList")[i].group.length; j++) {
                if (maxGroups.indexOf(form.getFieldValue("visitJudgmentList")[i].group[j]) !== -1 && form.getFieldValue("visitJudgmentList")[i].name === maxName) {
                  // result = true;
                  return message.error(formatMessage({
                    id: "drug.configure.setting.dose.form.list.name.selectedRepeatedly",
                  }));
                }
              }
            }
          }
        }
      }
    }
    // return result;
  };

  const visitJudgmentLabelsRun = (index: any) => {
    let options:any = [];
    if (doseLabelList !== null && doseLabelList.length > 0) {
      for (let i = 0; i < doseLabelList.length; i++) {
        if (form.getFieldValue("visitJudgmentList")[index].group !== null && form.getFieldValue("visitJudgmentList")[index].group.length > 0) {
          if (form.getFieldValue("visitJudgmentList")[index].group.indexOf(doseLabelList[i].group) !== -1) {
            options.push({
              label: doseLabelList[i].name,
              value: doseLabelList[i].id,
            });
          }
        } else {
          //删除组别要同时删除对应的标签\药物
          var visitJudgmentList: any[] = form.getFieldValue("visitJudgmentList");
          visitJudgmentList[index].doseDistribution = [];
          form.setFieldsValue({ visitJudgmentList });
          setVisitJudgmentListData(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList") : [])
        }
      }
    }
    updateField("visitJudgmentLabels", index, options);
    // console.log("options===" + JSON.stringify(Object.fromEntries(options.entries())))

    //删除组别要同时删除对应的标签\药物
    if (options !== null && options.length > 0) {
      let doseDistributionList:any = [];
      if (form.getFieldValue("visitJudgmentList")[index].doseDistribution !== null && form.getFieldValue("visitJudgmentList")[index].doseDistribution.length > 0) {
        for (let j = 0; j < form.getFieldValue("visitJudgmentList")[index].doseDistribution.length; j++) {
          for (let k = 0; k < options.length; k++) {
            if (form.getFieldValue("visitJudgmentList")[index].doseDistribution[j] === options[k].value) {
              doseDistributionList.push(options[k].value)
            }
          }
        }
      }
      var visitJudgmentList: any[] = form.getFieldValue("visitJudgmentList");
      visitJudgmentList[index].doseDistribution = doseDistributionList;
      form.setFieldsValue({ visitJudgmentList });
      setVisitJudgmentListData(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList") : [])
    } else {
      var visitJudgmentList: any[] = form.getFieldValue("visitJudgmentList");
      visitJudgmentList[index].doseDistribution = [];
      form.setFieldsValue({ visitJudgmentList });
      setVisitJudgmentListData(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList") : [])
    }

  };

  const updateField = (type: any, index: any, newValue: any) => {
    if (type === "doseLevelLabels") {
      setDoseLevelLabels((prevLabels: any) => {
        const newLabels = new Map(prevLabels);
        newLabels.set(index, newValue);
        return newLabels;
      });
    } else if (type === "visitJudgmentLabels") {
      setVisitJudgmentLabels((prevLabels: any) => {
        const newLabels = new Map(prevLabels);
        newLabels.set(index, newValue);
        return newLabels;
      });
    } else if (type === "visitJudgmentEdit") {
      setVisitJudgmentEdit((prevEdit: any) => {
        const newEdit = new Map(prevEdit);
        newEdit.set(index, newValue);
        return newEdit;
      });
    }
  };



  const getDoseVisitList = () => {
    getSetUpDrugConfigureVisitListRun({
      customerId,
      projectId,
      envId,
      cohortId: cohortId,
      doseFormId: form.getFieldValue("doseFormId"),
    }).then((result: any) => {
      let visits:any = []
      if (result.data !== null && result.data.length > 0) {
        for (let i = 0; i < result.data.length; i++) {
          // console.log(result.data[i]);
          visits.push({
            label: result.data[i].name,
            value: result.data[i].id,
          });
        }
      }
      setDoseVisitList(visits);
    });
  };

  const setUpDrugConfigureRun = () => {
    getSetUpDrugConfigureRun({
      customerId,
      projectId,
      envId,
      cohortId: cohortId,
      roleId: auth.project.permissions.role_id,
    }).then((result: any) => {
      setSend(true);
      setOldData(result.data);
      if (result.data.selectType !== 0) {
        form.setFieldsValue({ ...result.data });
        setIsOpen(result.data ? result.data.isOpen : false);
        setSelectType(result.data ? result.data.selectType : 1);
        setDoseFormId(result.data?.doseFormId);
        formTypeRun(result.data ? result.data.selectType : 1);
        setFormRecordList(result.data ? (result.data.formRecordList !== null ? result.data.formRecordList : []) : []);
        setDoseLevelListData(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : []);
        setVisitJudgmentListData(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList") : []);
        if (form.getFieldValue("selectType") === 2) {
          getDoseVisitList();
        } else {
          setFirstInitial(result.data ? result.data.isFirstInitial : false);
          setDoseReduction(result.data ? result.data.isDoseReduction : false);
          // setFrequency(form.getFieldValue("frequency"));
        }
      } else {
        form.setFieldsValue({ selectType });
        formTypeRun(1);
      }
      getGroups();
    });
  };

  const formChange = () => {
    const a = oldData;;
    // console.log("1===" + JSON.stringify(a)); 
    let b = form.getFieldsValue();
    // console.log("2===" + JSON.stringify(b)); 
    if (!compareObjects(a, b)) {
      setSend(false);
    } else {
      setSend(true);
    }
  };

  //比较两个JavaScript对象是否相同
  function compareObjects(obj1: any, obj2: any) {
    for (let key in obj1) {
      if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
        if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
          if (!arraysAreEqual(obj1[key], obj2[key])) {
            return false;
          }
        } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
          if (!compareObjects(obj1[key], obj2[key])) {
            return false;
          }
        } else {
          if (obj1[key] !== obj2[key]) {
            return false;
          }
        }
      }
    }
    return true;
  }

  //比较两个数组是否相同
  function arraysAreEqual(arr1: any, arr2: any) {
    // 检查数组长度是否相同
    if (arr1.length !== arr2.length) {
      return false;
    }

    const a = _.cloneDeep(arr1);
    const b = _.cloneDeep(arr2);
    // 将数组转换为字符串并比较
    const str1 = JSON.stringify(a.sort());
    const str2 = JSON.stringify(b.sort());

    return str1 === str2;
  }

  const doseLevelListDataRun = () => {
    setDoseFormId(form.getFieldValue("doseFormId"));
    if (formRecordList.length === 0) {
      setDoseLevelListData([]);
      form.setFieldsValue({ doseLevelList: [] });
      form.setFieldsValue({ isFirstInitial: false });
      form.setFieldsValue({ isDoseReduction: false });
      form.setFieldsValue({ frequency: null });
      setFirstInitial(false);
      setDoseReduction(false);
      setVisitJudgmentListData([]);
      form.setFieldsValue({ visitJudgmentList: [] });
    } else {
      var isExistence = false
      for (let i = 0; i < formRecordList.length; i++) {
        if (formRecordList[i].selectType === form.getFieldValue("selectType") && formRecordList[i].doseFormId === form.getFieldValue("doseFormId")) {
          setDoseLevelListData(
            (formRecordList[i].doseLevelList && formRecordList[i].doseLevelList !== null) ? formRecordList[i].doseLevelList : []
          );
          form.setFieldsValue({ doseLevelList: (formRecordList[i].doseLevelList && formRecordList[i].doseLevelList !== null) ? formRecordList[i].doseLevelList : [] });
          form.setFieldsValue({ isFirstInitial: formRecordList[i].isFirstInitial ? formRecordList[i].isFirstInitial : false });
          form.setFieldsValue({ isDoseReduction: formRecordList[i].isDoseReduction ? formRecordList[i].isDoseReduction : false });
          form.setFieldsValue({ frequency: formRecordList[i].frequency ? formRecordList[i].frequency : null });
          setFirstInitial(formRecordList[i].isFirstInitial ? formRecordList[i].isFirstInitial : false);
          setDoseReduction(formRecordList[i].isDoseReduction ? formRecordList[i].isDoseReduction : false);

          for (let j = 0; j < form.getFieldValue("doseLevelList").length; j++) {
            let labels:any = [];
            if (doseLabelList !== null && doseLabelList.length > 0) {
              for (let i = 0; i < doseLabelList.length; i++) {
                if (form.getFieldValue("doseLevelList")[j].group !== null && form.getFieldValue("doseLevelList")[j].group.length > 0) {
                  if (form.getFieldValue("doseLevelList")[j].group.indexOf(doseLabelList[i].group) !== -1) {
                    if (!doseLabelList[i].isLabel) {
                      labels.push({
                        label: doseLabelList[i].name,
                        value: doseLabelList[i].id,
                      });
                    }
                  }
                }
              }
            }
            updateField("doseLevelLabels", j, labels);
          }

          setVisitJudgmentListData(
            (formRecordList[i].visitJudgmentList && formRecordList[i].visitJudgmentList !== null) ? formRecordList[i].visitJudgmentList : []
          );
          form.setFieldsValue({ visitJudgmentList: (formRecordList[i].visitJudgmentList && formRecordList[i].visitJudgmentList !== null) ? formRecordList[i].visitJudgmentList : [] });
          for (let j = 0; j < form.getFieldValue("visitJudgmentList").length; j++) {
            let labels:any = [];
            if (doseLabelList !== null && doseLabelList.length > 0) {
              for (let i = 0; i < doseLabelList.length; i++) {
                if (form.getFieldValue("visitJudgmentList")[j].group !== null && form.getFieldValue("visitJudgmentList")[j].group.length > 0) {
                  if (form.getFieldValue("visitJudgmentList")[j].group.indexOf(doseLabelList[i].group) !== -1) {
                    labels.push({
                      label: doseLabelList[i].name,
                      value: doseLabelList[i].id,
                    });
                  }
                }
              }
            }
            updateField("visitJudgmentLabels", j, labels);
          }

          isExistence = true
        }
      }

      if (!isExistence) {
        setDoseLevelListData([]);
        form.setFieldsValue({ doseLevelList: [] });
        form.setFieldsValue({ isFirstInitial: false });
        form.setFieldsValue({ isDoseReduction: false });
        form.setFieldsValue({ frequency: null });
        setFirstInitial(false);
        setDoseReduction(false);
        setVisitJudgmentListData([]);
        form.setFieldsValue({ visitJudgmentList: [] });
      }

    }

    getDoseVisitList();

    // getSetUpDrugConfigureRun({
    //   customerId,
    //   projectId,
    //   envId,
    //   cohortId: cohortId,
    // }).then((result: any) => {
    //   // form.setFieldsValue({ ...result.data.doseLevelList });
    //   var doseLevelList: any[] = result.data.doseLevelList;
    //   form.setFieldsValue({ doseLevelList });
    //   setDoseLevelListData(form.getFieldValue("doseLevelList")?form.getFieldValue("doseLevelList"):[]);
    // });


  };

  //获取实验组信息
  const getGroups = () => {
    getAllocationGroupRun({
      customerId,
      projectId,
      envId,
      cohortId: cohortId,
      roleId: auth.project.permissions.role_id,
    }).then((result: any) => {
      const groups: any[] = [];
      const options: any[] = [];
      if (result.data !== null && result.data.length > 0) {
        result.data?.forEach((it: any) => {
          options.push({
            label: it,
            value: it,
          });
          groups.push(it);
        })
      }
      setDoseLevelGroups(options);
      setVisitJudgmentGroups(options);
      doseLabelsRun(groups);
    });
    setVisible(true);
  };

  const formTypeRun = (type: any) => {
    //获取剂量表单
    getFormTypeRun({
      customerId: auth.customerId,
      envId: auth.env.id,
      cohortId: cohortId,
      applicationType: 3,
    }).then((result: any) => {
      const options: any[] = [];
      if (result.data != null && result.data.length > 0) {
        result.data?.forEach((it: any) => {
          if (it.options !== null && it.options.length > 0) {
            const arr = [
              "form.control.type.options.one",
              "form.control.type.options.two",
              "form.control.type.options.three",
            ];
            // 使用map方法提取label属性组成新数组
            const labelArray = it.options.map((obj: any) => obj.label);
            // 取出labelArray中有而arr中没有的元素
            const differenceArray = labelArray.filter(
              (item: any) => !arr.includes(item)
            );
            if (
              differenceArray !== null &&
              differenceArray.length > 0 &&
              type === 2
            ) {
              options.push({
                label: it.variable,
                value: it.id,
              });
            }
            if (
              (differenceArray === null || differenceArray.length === 0) &&
              type === 1
            ) {
              options.push({
                label: it.variable,
                value: it.id,
              });
            }
          }
        });
      }
      setDoseForms(options);
    });
  };

  const onTypeChange = (e: RadioChangeEvent) => {
    setSelectType(e.target.value);
    getGroups();
    //获取剂量表单
    formTypeRun(e.target.value);
    // setUpDrugConfigureForm();
    setDoseFormId(null);
    setFirstInitial(false);
    setDoseReduction(false);
    form.setFieldsValue({ doseFormId: null });
    if (form.getFieldValue("doseFormId") === 2) {
      getDoseVisitList();
    }
  };

  const save = () => {
    //TODO 保存

    if (selectType != null && selectType !== undefined) {
      if (!isBlindedRole) {
        form
          .validateFields()
          .then((values: any) => {
            values.projectId = projectId;
            values.envId = envId;
            values.cohortId = cohortId;
            values.customerId = customerId;
            values.formRecordList = formRecordList
            upDrugConfigureSetRun({
              ...values,
            }).then((data: any) => {
              message.success(data.msg);
              props.refresh();
              setVisible(false);
              hide();
            });
          })
          .catch(() => {
           });
      } else {
        props.refresh();
        hide();
      }
    }
  };

  React.useImperativeHandle(props.bind, () => ({ show }));
  React.useEffect(() => getNew(), [g.lang])

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: g.lang === "zh" ? 4 : 5 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: g.lang === "zh" ? 25 : 25 },
    },
  };

  const doseLevelListEdit = () => {
    // @ts-ignore
    // drug_configure_setting.current.show(id, visitCycles);
    setDoseLevelListOpen(true);
  };

  const doseLevelListClose = () => {
    setDoseLevelListOpen(false);
    doseLevelListDataRun();
  };

  const onChangeIsFirstInitial = (e: any) => {
    setFirstInitial(e.target.checked);
    form.setFieldValue("isFirstInitial", e.target.checked);
  };
  const onChangeIsDoseReduction = (e: any) => {
    setDoseReduction(e.target.checked);
    form.setFieldValue("isDoseReduction", e.target.checked);
  };


    // 定义一个可以接收参数的验证函数
    const ipValidator = (index: any) => {
      return ({}) => ({
        validator: (rule: any, value: any, callback: any) => {
          if(form.getFieldValue("dtpIpList") !== null && form.getFieldValue("dtpIpList") !== undefined && form.getFieldValue("dtpIpList").length > 0){
            for (let i = 0; i < form.getFieldValue("dtpIpList").length; i++) {
              if(form.getFieldValue("dtpIpList")[i]?.ip === value && i !== index){
                return Promise.reject(formatMessage({ id: "subject.dispensing.selectedRepeatedly" }));
              }
            }
          }
          return Promise.resolve();
        },
      });
    };


  const deleteClick = (remove: any, fieldName: any, index: any) => {
    remove(fieldName);
  };

  const addClick = (add: any, field: any) => {
    add();
  };


  const onChangeDoseForm = (e: any) => {
    // setFormRecordList(result.data ? result.data.formRecordList : []);
    // console.log("kkkk===" + JSON.stringify(formRecordList) + "==" + form.getFieldValue("doseFormId"));
    setDoseFormId(form.getFieldValue("doseFormId"));
    if (formRecordList.length === 0) {
      setDoseLevelListData([]);
      form.setFieldsValue({ doseLevelList: [] });
      form.setFieldsValue({ isFirstInitial: false });
      form.setFieldsValue({ isDoseReduction: false });
      form.setFieldsValue({ frequency: null });
      setFirstInitial(false);
      setDoseReduction(false);
      setVisitJudgmentListData([]);
      form.setFieldsValue({ visitJudgmentList: [] });
    } else {
      var isExistence = false
      for (let i = 0; i < formRecordList.length; i++) {
        if (formRecordList[i].selectType === form.getFieldValue("selectType") && formRecordList[i].doseFormId === form.getFieldValue("doseFormId")) {
          setDoseLevelListData(
            (formRecordList[i].doseLevelList && formRecordList[i].doseLevelList !== null) ? formRecordList[i].doseLevelList : []
          );
          form.setFieldsValue({ doseLevelList: (formRecordList[i].doseLevelList && formRecordList[i].doseLevelList !== null) ? formRecordList[i].doseLevelList : [] });
          form.setFieldsValue({ isFirstInitial: formRecordList[i].isFirstInitial ? formRecordList[i].isFirstInitial : false });
          form.setFieldsValue({ isDoseReduction: formRecordList[i].isDoseReduction ? formRecordList[i].isDoseReduction : false });
          form.setFieldsValue({ frequency: formRecordList[i].frequency ? formRecordList[i].frequency : null });
          form.setFieldsValue({ groupType: formRecordList[i].groupType ? formRecordList[i].groupType : null });
          setFirstInitial(formRecordList[i].isFirstInitial ? formRecordList[i].isFirstInitial : false);
          setDoseReduction(formRecordList[i].isDoseReduction ? formRecordList[i].isDoseReduction : false);

          for (let j = 0; j < form.getFieldValue("doseLevelList").length; j++) {
            let labels:any = [];
            if (doseLabelList !== null && doseLabelList.length > 0) {
              for (let i = 0; i < doseLabelList.length; i++) {
                if (form.getFieldValue("doseLevelList")[j].group !== null && form.getFieldValue("doseLevelList")[j].group.length > 0) {
                  if (form.getFieldValue("doseLevelList")[j].group.indexOf(doseLabelList[i].group) !== -1) {
                    if (!doseLabelList[i].isLabel) {
                      labels.push({
                        label: doseLabelList[i].name,
                        value: doseLabelList[i].id,
                      });
                    }
                  }
                }
              }
            }
            updateField("doseLevelLabels", j, labels);
          }

          setVisitJudgmentListData(
            (formRecordList[i].visitJudgmentList && formRecordList[i].visitJudgmentList !== null) ? formRecordList[i].visitJudgmentList : []
          );
          form.setFieldsValue({ visitJudgmentList: (formRecordList[i].visitJudgmentList && formRecordList[i].visitJudgmentList !== null) ? formRecordList[i].visitJudgmentList : [] });
          for (let j = 0; j < form.getFieldValue("visitJudgmentList").length; j++) {
            let labels:any = [];
            if (doseLabelList !== null && doseLabelList.length > 0) {
              for (let i = 0; i < doseLabelList.length; i++) {
                if (form.getFieldValue("visitJudgmentList")[j].group !== null && form.getFieldValue("visitJudgmentList")[j].group.length > 0) {
                  if (form.getFieldValue("visitJudgmentList")[j].group.indexOf(doseLabelList[i].group) !== -1) {
                    labels.push({
                      label: doseLabelList[i].name,
                      value: doseLabelList[i].id,
                    });
                  }
                }
              }
            }
            updateField("visitJudgmentLabels", j, labels);
          }

          isExistence = true
        }
      }

      if (!isExistence) {
        setDoseLevelListData([]);
        form.setFieldsValue({ doseLevelList: [] });
        form.setFieldsValue({ isFirstInitial: false });
        form.setFieldsValue({ isDoseReduction: false });
        form.setFieldsValue({ frequency: null });
        setFirstInitial(false);
        setDoseReduction(false);
        setVisitJudgmentListData([]);
        form.setFieldsValue({ visitJudgmentList: [] });
      }

    }

    getDoseVisitList();
  };



  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }: any) => {
    const inputNode = inputType === "number" ? <InputNumber /> : <Input />;
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `Please Input ${title}!`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const addDoseLevel = () => {
    var doseLevelList: any[] = [];
    if (
      form.getFieldValue("doseLevelList") !== undefined &&
      form.getFieldValue("doseLevelList") !== null &&
      form.getFieldValue("doseLevelList").length > 0
    ) {
      doseLevelList = form.getFieldValue("doseLevelList");
    }
    doseLevelList.push({
      name: null,
      group: [],
      doseDistribution: [],
      initialDose: false,
    });
    form.setFieldsValue({ doseLevelList });
    setDoseLevelListData(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : []);
  };

  const deleteRowDoseLevel = (i: any) => {
    // 删除行数据
    // 更新data状态变量
    var doseLevelList: any[] = [];
    if (
      form.getFieldValue("doseLevelList") !== undefined &&
      form.getFieldValue("doseLevelList") !== null &&
      form.getFieldValue("doseLevelList").length > 0
    ) {
      doseLevelList = form.getFieldValue("doseLevelList");
    }
    doseLevelList.splice(i, 1);
    form.setFieldsValue({ doseLevelList });
    setDoseLevelListData(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : []);


    if (doseLabelList !== null && doseLabelList.length > 0) {
      if (form.getFieldValue("doseLevelList") !== null && form.getFieldValue("doseLevelList").length > 0) {
        for (let j = 0; j < form.getFieldValue("doseLevelList").length; j++) {
          let options:any = [];
          if (form.getFieldValue("doseLevelList")[j].group !== null && form.getFieldValue("doseLevelList")[j].group.length > 0) {
            for (let k = 0; k < doseLabelList.length; k++) {
              if (form.getFieldValue("doseLevelList")[j].group.indexOf(doseLabelList[k].group) !== -1) {
                if (!doseLabelList[i].isLabel) {
                  options.push({
                    label: doseLabelList[k].name,
                    value: doseLabelList[k].id,
                  });
                }
              }
            }
            updateField("doseLevelLabels", j, options);
          }
        }
      }
    }



    // deleteVisitConfigs(i);
  };

  const addVisitJudgment = () => {
    var visitJudgmentList: any[] = [];
    if (
      form.getFieldValue("visitJudgmentList") !== undefined &&
      form.getFieldValue("visitJudgmentList") !== null &&
      form.getFieldValue("visitJudgmentList").length > 0
    ) {
      visitJudgmentList = form.getFieldValue("visitJudgmentList");
    }
    visitJudgmentList.push({
      name: null,
      group: [],
      doseDistribution: [],
      visitInheritance: false,
    });
    form.setFieldsValue({ visitJudgmentList });
    setVisitJudgmentListData(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList") : []);
    updateField("visitJudgmentEdit", visitJudgmentList.length - 1, true);
  };

  const editRowVisitJudgment = (i: any) => {
    updateField("visitJudgmentEdit", i, true);
  };

  const cacelRowVisitJudgment = (i: any) => {
    updateField("visitJudgmentEdit", i, false);
  };


  const deleteRowVisitJudgment = (i: any) => {
    // 删除行数据
    // 更新data状态变量
    var visitJudgmentList: any[] = [];
    if (
      form.getFieldValue("visitJudgmentList") !== undefined &&
      form.getFieldValue("visitJudgmentList") !== null &&
      form.getFieldValue("visitJudgmentList").length > 0
    ) {
      visitJudgmentList = form.getFieldValue("visitJudgmentList");
    }
    visitJudgmentList.splice(i, 1);
    form.setFieldsValue({ visitJudgmentList });
    setVisitJudgmentListData(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList") : []);


    if (doseLabelList !== null && doseLabelList.length > 0) {
      if (form.getFieldValue("visitJudgmentList") !== null && form.getFieldValue("visitJudgmentList").length > 0) {
        for (let j = 0; j < form.getFieldValue("visitJudgmentList").length; j++) {
          let options:any = [];
          if (form.getFieldValue("visitJudgmentList")[j].group !== null && form.getFieldValue("visitJudgmentList")[j].group.length > 0) {
            for (let k = 0; k < doseLabelList.length; k++) {
              if (form.getFieldValue("visitJudgmentList")[j].group.indexOf(doseLabelList[k].group) !== -1) {
                options.push({
                  label: doseLabelList[k].name,
                  value: doseLabelList[k].id,
                });
              }
            }
            updateField("visitJudgmentList", j, options);
          }
        }
      }
    }

    // deleteVisitConfigs(i);
  };

  const onDoseLevelListDataEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      let oldIndex = Number(active.id);
      let newIndex = Number(over?.id);
      // const newData = arrayMove(prevData, oldIndex, newIndex);

      let prevData = form.getFieldValue("doseLevelList");
      const element = prevData.splice(oldIndex - 1, 1)[0]; // 从索引为4的位置移除一个元素
      prevData.splice(newIndex - 1, 0, element); // 在索引为7的位置插入元素
      form.setFieldsValue({ prevData });

      let fixedArray = new Array(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList").length : 0);
      for (let i = 0; i < fixedArray.length; i++) {
        fixedArray[i] = [];
        doseLevelLabels.forEach((value: any, key: any) => {
          if (key === i) {
            fixedArray[i] = value;
          }
        });
      }
      const array = fixedArray.splice(oldIndex - 1, 1)[0]; // 从索引为4的位置移除一个元素
      fixedArray.splice(newIndex - 1, 0, array); // 在索引为7的位置插入元素
      for (let i = 0; i < fixedArray.length; i++) {
        updateField("doseLevelLabels", i, fixedArray[i]);
      }
      // console.log("newData==" + JSON.stringify(prevData));
    }
  };

  const onVisitJudgmentListDataEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      let oldIndex = Number(active.id);
      let newIndex = Number(over?.id);
      // const newData = arrayMove(prevData, oldIndex, newIndex);

      let prevData = form.getFieldValue("visitJudgmentList");
      const element = prevData.splice(oldIndex - 1, 1)[0]; // 从索引为4的位置移除一个元素
      prevData.splice(newIndex - 1, 0, element); // 在索引为7的位置插入元素
      form.setFieldsValue({ prevData });

      let fixedArray = new Array(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList").length : 0);
      for (let i = 0; i < fixedArray.length; i++) {
        fixedArray[i] = [];
        visitJudgmentLabels.forEach((value: any, key: any) => {
          if (key === i) {
            fixedArray[i] = value;
          }
        });
      }
      const array = fixedArray.splice(oldIndex - 1, 1)[0]; // 从索引为4的位置移除一个元素
      fixedArray.splice(newIndex - 1, 0, array); // 在索引为7的位置插入元素
      for (let i = 0; i < fixedArray.length; i++) {
        updateField("visitJudgmentLabels", i, fixedArray[i]);
      }
      // console.log("newData==" + JSON.stringify(prevData));
    }
  };

  const onRadioChange = (index: any) => {
    var doseLevelList: any[] = [];
    if (
      form.getFieldValue("doseLevelList") !== undefined &&
      form.getFieldValue("doseLevelList") !== null &&
      form.getFieldValue("doseLevelList").length > 0
    ) {
      doseLevelList = form.getFieldValue("doseLevelList");
    }
    for (let i = 0; i < doseLevelList.length; i++) {
      if (i === index) {
        doseLevelList[i].initialDose = true;
      } else {
        doseLevelList[i].initialDose = false;
      }
    }
    form.setFieldsValue({ doseLevelList });
    setDoseLevelListData(form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : []);
  };

  const onSwitchChange = (checked: boolean, index: any) => {
    // console.log(`switch to ${checked}`);
    var visitJudgmentList: any[] = [];
    if (
      form.getFieldValue("visitJudgmentList") !== undefined &&
      form.getFieldValue("visitJudgmentList") !== null &&
      form.getFieldValue("visitJudgmentList").length > 0
    ) {
      visitJudgmentList = form.getFieldValue("visitJudgmentList");
    }
    for (let i = 0; i < visitJudgmentList.length; i++) {
      if (i === index) {
        visitJudgmentList[i].visitInheritance = checked;
        if (!checked) {
          visitJudgmentList[i].visitInheritanceCount = null;

        }
      }
    }
    form.setFieldsValue({ visitJudgmentList });
    setVisitJudgmentListData(form.getFieldValue("visitJudgmentList") ? form.getFieldValue("visitJudgmentList") : []);
  };

  const handleResize = () => {
    setHeight(window.innerHeight - 232);
  };
  React.useEffect(() => {
    // 监听
    window.addEventListener("resize", handleResize);
    // 销毁
    return () => window.removeEventListener("resize", handleResize);
  });


  const doseLevelColumns: any = [
    {
      title: formatMessage({ id: "common.serial" }),
      dataIndex: "index",
      key: "index",
      width: 70,
      ellipsis: true,
    },
    {
      title: (
        <span>
          {formatMessage({ id: "common.name" })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "name",
      key: "name",
      ellipsis: true,
      render: (value: any, record: any, i: any) => {
        return (
          // 显示编辑框
          <Form.Item
            name={["doseLevelList", i, "name"]}
            initialValue={value}
            // noStyle
            rules={[
              {
                required: true,
                message:
                  formatMessage({ id: "placeholder.input.common" }) +
                  formatMessage({ id: "common.name" }),
              },
            ]}
          >
            <Input
              placeholder={formatMessage({
                id: "placeholder.input.common",
              })}
              disabled={isBlindedRole ? true : false}
            />
          </Form.Item>
        );
      },
    },
    {
      title: (
        <span>
          {formatMessage({ id: "drug.configure.setting.dose.form.list.group" })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "group",
      key: "group",
      ellipsis: true,
      render: (value: any, record: any, i: any) => {
        return (
          // 显示编辑框
          <Form.Item
            name={["doseLevelList", i, "group"]}
            initialValue={value}
            // noStyle
            rules={[
              {
                required: true,
                message:
                  formatMessage({ id: "placeholder.input.common" }) +
                  formatMessage({
                    id: "drug.configure.setting.dose.form.list.group",
                  }),
              },
            ]}
          >
            {
              !isBlindedRole ?
                <Select
                  onChange={() => {
                    doseLevelLabelsRun(i);
                  }}
                  mode="multiple"
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  className="full-width"
                  options={doseLevelGroups}
                ></Select> :
                (Array(value.length).fill("**********").join(","))
            }

          </Form.Item>
        );
      },
    },
    {
      title: (
        <span>
          {formatMessage({
            id: "drug.configure.setting.dose.form.list.dose.distribution",
          })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "doseDistribution",
      key: "doseDistribution",
      ellipsis: true,
      width: g.lang === "en" ? 170 : 170,
      render: (value: any, record: any, i: any) => {
        return (
          // 显示编辑框
          <Form.Item
            name={["doseLevelList", i, "doseDistribution"]}
            initialValue={value}
            // noStyle
            rules={[
              {
                required: true,
                message:
                  formatMessage({ id: "placeholder.input.common" }) +
                  formatMessage({
                    id: "drug.configure.setting.dose.form.list.dose.distribution",
                  }),
              },
            ]}
          >
            {
              !isBlindedRole ?
                <Select
                  mode="multiple"
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  className="full-width"
                  options={doseLevelLabels.get(i)}
                ></Select> :
                <span>
                  {
                    [...value.filter((v: any) => v.indexOf('**********') >= 0),
                    doseLevelLabels.get(i)?.filter((item: any) => value.includes(item.value))?.map((it: any) => it.label)]
                      .join(',')
                  }
                </span>
            }
          </Form.Item>
        );
      },
    },
    {
      title: (
        <span>
          {formatMessage({
            id: "form.control.type.options.one",
          })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "initialDose",
      key: "initialDose",
      width: 120,
      render: (value: any, record: any, i: any) => {
        return (
          // 显示编辑框
          <Form.Item
            name={["doseLevelList", i, "initialDose"]}
            initialValue={value}
          // noStyle
          // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'form.control.type.options.one'}) }]}
          >
            <Radio.Group
              style={{ position: "absolute", left: "40%", top: "30%" }}
              onChange={() => onRadioChange(i)}
              value={
                form.getFieldValue("doseLevelList")[i]
                  .initialDose
              }
            >
              <Radio
                value={true}
                // disabled={isEdit?false:true}
                disabled={isBlindedRole ? true : false}
              >
              </Radio>
            </Radio.Group>
          </Form.Item>
        );
      },
    },
    {
      title: formatMessage({ id: "common.operation" }),
      dataIndex: "actions",
      key: "actions",
      fixed: "right",
      width: g.lang === "en" ? 100 : 70,
      render: (value: any, record: any, i: any) => (
        <>
          {permissionsCohort(
            auth.project.permissions,
            "operation.build.medicine.configuration.setting.delete",
            props.cohort?.status
          ) && !isBlindedRole && (

              // !form.getFieldValue("doseLevelList")[i].isUsed?
              <Button type="link" onClick={() => deleteRowDoseLevel(i)} style={{ paddingLeft: 0 }}>
                {formatMessage({
                  id: "common.delete",
                })}
              </Button>
              // :"-"
            )}
        </>
      ),
    },
  ].filter(Boolean);


  const visitJudgmentColumns: any = [
    {
      title: formatMessage({ id: "common.serial" }),
      dataIndex: "index",
      key: "index",
      width: 70,
      ellipsis: true,
    },
    {
      title: (
        <span>
          {formatMessage({ id: "drug.configure.setting.dose.form.list.name" })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "name",
      key: "name",
      width: 180,
      ellipsis: true,
      render: (value: any, record: any, i: any) => {
        return (
          // 显示编辑框
          <Form.Item
            name={["visitJudgmentList", i, "name"]}
            initialValue={value}
            // noStyle
            rules={[
              {
                required: true,
                message:
                  formatMessage({ id: "placeholder.input.common" }) +
                  formatMessage({ id: "drug.configure.setting.dose.form.list.name" }),
              },
            ]}
          >
            {/* <Input
              placeholder={formatMessage({
                id: "placeholder.input.common",
              })}
            /> */}
            {
              visitJudgmentEdit.get(i) ?
                <Select dropdownStyle={{maxWidth: 270 }}
                  onChange={() => {
                    visitJudgmentNamesRun(i);
                  }}
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  className="full-width"
                  // options={doseVisitList}
                >
                  {
                    doseVisitList?.map((it:any)=>

                            <Select.Option

                                value={it.value}>
                              <Tooltip
                                  placement="top"
                                  title={<Row>{it.label}</Row>}
                              >
                                {it.label}
                              </Tooltip>
                            </Select.Option>

                    )
                }


                </Select> :
                <span>{doseVisitList.filter((item: any) => item.value === value)[0]?.label ?doseVisitList.filter((item: any) => item.value === value)[0]?.label : "-"}</span>
            }
          </Form.Item>
        );
      },
    },
    {
      title: (
        <span>
          {formatMessage({ id: "drug.configure.setting.dose.form.list.group" })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "group",
      key: "group",
      ellipsis: true,
      render: (value: any, record: any, i: any) => {
        return (
          // 显示编辑框
          <Form.Item
            name={["visitJudgmentList", i, "group"]}
            initialValue={value}
            // noStyle
            rules={[
              {
                required: true,
                message:
                  formatMessage({ id: "placeholder.input.common" }) +
                  formatMessage({
                    id: "drug.configure.setting.dose.form.list.group",
                  }),
              },
            ]}
          >
            {
              visitJudgmentEdit.get(i) ?
                <Select
                  onChange={() => {
                    visitJudgmentLabelsRun(i);
                    visitJudgmentNamesRun(i);
                  }}
                  mode="multiple"
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  className="full-width"
                  options={visitJudgmentGroups}
                ></Select> :
                <span>
                  {
                      !value || value === "" || value === undefined || value?.length === 0 ?
                          "-"
                      :
                      !isBlindedRole ? (value?.join(",")) :
                        (Array(value.length).fill("**********").join(","))
                  }
                </span>
            }
          </Form.Item>
        );
      },
    },
    {
      title: (
        <span>
          {formatMessage({
            id: "drug.configure.setting.dose.form.list.dose.distribution",
          })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "doseDistribution",
      key: "doseDistribution",
      ellipsis: true,
      render: (value: any, record: any, i: any) => {
        return (
          // 显示编辑框
          <Form.Item
            name={["visitJudgmentList", i, "doseDistribution"]}
            initialValue={value}
            // noStyle
            rules={[
              {
                required: true,
                message:
                  formatMessage({ id: "placeholder.input.common" })
                  +
                  formatMessage({
                    id: "drug.configure.setting.dose.form.list.dose.distribution",
                  }),
              },
            ]}
          >
            {
              // !isBlindedRole?value:
              visitJudgmentEdit.get(i) ?
                <Select
                  mode="multiple"
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  className="full-width"
                  options={visitJudgmentLabels.get(i)}
                ></Select> :
                <span>
                  {
                    !value || value === "" || value === undefined || value?.length === 0 ?
                        "-"
                        :
                    !isBlindedRole ?
                      visitJudgmentLabels.get(i)?.filter((item: any) => value.includes(item.value))?.map((it: any) => it.label).join(",") :
                      [...value.filter((v: any) => v.indexOf('**********') >= 0),
                      visitJudgmentLabels.get(i)?.filter((item: any) => value.includes(item.value))?.map((it: any) => it.label)]
                        .join(',')
                  }
                </span>

              // <span>
              //   {
              //     console.log("kkk==" + JSON.stringify(visitJudgmentLabels.get(i).filter((item: any) => item.value === value)) + "===" + value)
              //   }
              // </span>
            }
          </Form.Item>
        );
      },
    },
    {
      title: (
        <span>
          {formatMessage({
            id: "drug.configure.setting.dose.form.list.visit.inheritance",
          })}
          {
            <span
              style={{
                color: "red",
                display: "inline-block",
                verticalAlign: "middle",
                marginTop: "2px",
                marginRight: "5px",
              }}
            >
              *
            </span>
          }
        </span>
      ),
      dataIndex: "inheritance",
      key: "inheritance",
      width: 150,
      render: (value: any, record: any, i: any) => {
        return (
            <Row>
              <Form.Item
                  name={["visitJudgmentList", i, "visitInheritance"]}
                  initialValue={value}
                  // noStyle
                  // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'form.control.type.options.one'}) }]}
              >
                {/* <Radio
                onChange={() =>
                  onRadioChange(i)
                }
              /> */}
                <Switch
                    size={"small"}
                    onChange={() =>
                        onSwitchChange(
                            form.getFieldValue("visitJudgmentList")[i].visitInheritance,
                            i
                        )
                    }
                    checked={
                      form.getFieldValue("visitJudgmentList")[i].visitInheritance
                    }
                    disabled={visitJudgmentEdit.get(i) ? false : true}
                    // onChange={onChange}
                ></Switch>
              </Form.Item>
              {
                visitJudgmentListData[i]?.visitInheritance &&

                  // value ?

                  <Form.Item
                      style={{marginLeft:6,}}
                      name={["visitJudgmentList", i, "visitInheritanceCount"]}
                      initialValue={value}
                      // noStyle
                      // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'form.control.type.options.one'}) }]}
                  >
                    {
                        visitJudgmentEdit.get(i) && form.getFieldValue("visitJudgmentList")[i].visitInheritance?
                            <InputNumber
                                addonAfter={formatMessage({id:"common.occasions"})}
                                size={"small"}
                                style={{ width:90}}
                                min={1}
                                disabled={!(visitJudgmentEdit.get(i) && form.getFieldValue("visitJudgmentList")[i].visitInheritance)}
                            ></InputNumber>
                            :
                            visitJudgmentListData[i].visitInheritanceCount?
                                visitJudgmentListData[i].visitInheritanceCount  + formatMessage({id:"common.occasions"})
                                :
                                "-"
                    }


                  </Form.Item>
                    // :
                    // "-"

              }

            </Row>


        );
      },
    },
    {
      title: formatMessage({ id: "common.operation" }),
      dataIndex: "actions",
      key: "actions",
      // fixed: "right",
      width: 120,
      render: (value: any, record: any, i: any) => (
        <>
          {
            !visitJudgmentEdit.get(i) ?
              <span>
                {permissionsCohort(
                  auth.project.permissions,
                  "operation.build.medicine.configuration.setting.edit",
                  props.cohort?.status
                ) && (
                    <Button style={{paddingLeft:0}} type="link" disabled={isBlindedRole ? true : false} onClick={() => editRowVisitJudgment(i)}>
                      {formatMessage({
                        id: "common.edit",
                      })}
                    </Button>
                  )}
              </span> :
              <Row>
                  {permissionsCohort(
                    auth.project.permissions,
                    "operation.build.medicine.configuration.setting.delete",
                    props.cohort?.status
                  ) && (

                      // !form.getFieldValue("visitJudgmentList")[i].isUsed?
                      <Button style={{paddingLeft:0}} type="link" onClick={() => deleteRowVisitJudgment(i)}>
                        {formatMessage({
                          id: "common.delete",
                        })}
                      </Button>
                      // :"-"
                    )}
                  {permissionsCohort(
                    auth.project.permissions,
                    "operation.build.medicine.configuration.setting.edit",
                    props.cohort?.status
                  ) && (
                      <Button style={{paddingLeft:0}} type="link" onClick={() => cacelRowVisitJudgment(i)}>
                        {formatMessage({
                          id: "common.cancel",
                        })}
                      </Button>
                    )}
              </Row>
          }

        </>
      ),
    },
  ].filter(Boolean);

  return (
    <React.Fragment>
      <Modal
        title={formatMessage({ id: "common.setting" })}
        open={visible}
        onCancel={hide}
        maskClosable={false}
        centered
        destroyOnClose
        className="custom-medium-modal-new-setting"
        // okButtonProps={
        //   {
        //     // loading: addConfigureLoading || updateConfigureRunLoading,
        //   }
        // }
        onOk={save}
        // okButtonProps={{ disabled: send }}
        okText={formatMessage({ id: "common.ok" })}
        style={{
          marginLeft: "-20px",
        }}
      >
        <StyleFrom
          labelWrap
          form={form}
          onValuesChange={formChange}
          layout="horizontal"
          {...formItemLayout}
          className="custom-modal-body"
        >
          {
            dtpRules === 1 &&
            <>
                <Title name={formatMessage({ id: "logistics.dispensing.dtpIp" })} />
                <Form.Item name="dtpIpList"
                  // style={{marginTop:12}}
                  // labelCol={{style:{ whiteSpace: "pre-wrap", textAlign:"right", height:"auto"}}}
                  label={
                    <span>
                      {formatMessage({ id: "shipment.medicine" })}
                      <Tooltip
                        title={formatMessage({
                          id: "projects.attributes.dtp.rule.ip.tooltip",
                        })}
                        trigger="hover"
                      >
                        <QuestionCircleFilled
                          style={{
                            marginLeft: "8px",
                            color: "#D0D0D0",
                          }}
                        />
                      </Tooltip>
                    </span>
                  }
                >
                  <Form.List name="dtpIpList" >
                      {(fields: any, { add, remove }, {errors}) => (
                          <>
                              {fields.map((field: any, index: any) => (
                                  <Row key={field.key}
                                        style={{marginBottom: "0px", display: 'flex'}}
                                  >
                                    <Form.Item
                                      {...field}
                                      name={[field.name, "ip"]}
                                      fieldKey={[field.fieldKey, "ip"]}
                                      rules={[
                                        {
                                          required: true,
                                          message: formatMessage({
                                            id: "placeholder.select.common",
                                          }),
                                        },
                                        ipValidator(index),
                                      ]}
                                      style={{flex: 1}}
                                    >
                                      <Select
                                        showArrow
                                        placeholder={formatMessage({
                                          id: "drug.configure.drugName",
                                        })}
                                        className="full-width"
                                        onChange={(value: any) => {
                                        }}
                                        allowClear
                                      >
                                        {
                                            (drugNameList || []).map(
                                                (it:any) =>
                                                    <Select.Option 
                                                        key={it.value} 
                                                        value={it.value}
                                                        disabled={isBlindedRole ? true : false}
                                                    >
                                                        {it.value}
                                                    </Select.Option>
                                            )
                                        }
                                      </Select>
                                    </Form.Item>
                                    <Form.Item



                                      {...field}
                                      name={[field.name, "dtpTypeList"]}
                                      fieldKey={[field.fieldKey, "dtpTypeList"]}
                                      rules={[
                                        {
                                          required: true,
                                          message: formatMessage({
                                            id: "placeholder.select.common",
                                          }),
                                        },
                                      ]}
                                      style={{flex: 1, marginLeft:12}}
                                    >
                                      <Select
                                        showArrow
                                        placeholder={formatMessage({
                                          id: "logistics.dispensing.method",
                                        })}
                                        className="full-width"
                                        onChange={(value: any) => {
                                        }}
                                        mode="multiple"
                                        allowClear
                                      >
                                        {
                                            (dispensingMethodList || []).map(
                                                (it:any) =>
                                                    <Select.Option 
                                                        key={it.value} 
                                                        value={it.value}
                                                        disabled={isBlindedRole ? true : false}
                                                    >
                                                        {it.label}
                                                    </Select.Option>
                                            )
                                        }
                                      </Select>
                                    </Form.Item>
                                    <Form.ErrorList errors={errors}/>
                                    {
                                      fields.length !== 1 &&
                                      <Tooltip placement="top" title={formatMessage({id: "common.delete"})}>
                                        <Row style={{height:30}}>
                                          <MinusCircleFilled style={{color: "#F96964",marginLeft: 24, marginRight: 12}} onClick={() => deleteClick(remove, field.name, index)}/>
                                        </Row>
                                      </Tooltip>

                                    }
                                  </Row>
                              ))}
                              <Form.Item style={{ marginBottom: 0 }}>
                                <Button
                                  type="dashed"
                                  onClick={() => addClick(add, fields)}
                                  block
                                  icon={<PlusOutlined />}
                                >
                                  {formatMessage({ id: "common.addTo" })}
                                </Button>
                              </Form.Item>
                          </>
                      )}
                  </Form.List>
              </Form.Item>
            </>
          }
          <Title name={formatMessage({ id: "drug.configure.setting.dose.form.doseAdjustment" })} />
          <Form.Item
            name="isOpen"
            label={formatMessage({
              id: "drug.configure.setting.dose.form.doseAdjustment",
            })}
          >
            <Switch
              size={"small"}
              onChange={() =>
                setIsOpen(form.getFieldValue("isOpen"))
              }
              checked={
                form.getFieldValue("isOpen")
              }
              disabled={isBlindedRole ? true : false}
            ></Switch>
          </Form.Item>

          {
            isOpen &&
            <>

              <Form.Item
                label={formatMessage({
                  id: "drug.configure.setting.dose.form.type",
                })}
                name="selectType"
                initialValue={1}
              >
                <Radio.Group disabled={isBlindedRole ? true : false} onChange={onTypeChange} value={selectType}>
                  <Space direction="vertical" style={{ marginTop: "4px" }}>
                    <Radio value={1} >
                      <span
                        style={{
                          fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "14px",
                          fontWeight: 400,
                          lineHeight: "19.6px",
                          textAlign: "left",
                          color: "#1D2129",
                        }}
                      >
                        {formatMessage({ id: "drug.configure.setting.dose.level" })}
                      </span>
                      <span
                        style={{
                          fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "12px",
                          fontWeight: 400,
                          lineHeight: "16.8px",
                          textAlign: "left",
                          marginLeft: "8px",
                          color: "#677282",
                        }}
                      >
                        {"(" +
                          formatMessage({
                            id: "drug.configure.setting.dose.level.desc",
                          }) +
                          ")"}
                      </span>
                    </Radio>
                    <Radio value={2}>
                      <span
                        style={{
                          fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "14px",
                          fontWeight: 400,
                          lineHeight: "19.6px",
                          textAlign: "left",
                          color: "#1D2129",
                        }}
                      >
                        {formatMessage({
                          id: "drug.configure.setting.dose.visit.judgment",
                        })}
                      </span>
                      <span
                        style={{
                          fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                          fontSize: "12px",
                          fontWeight: 400,
                          lineHeight: "16.8px",
                          textAlign: "left",
                          marginLeft: "8px",
                          color: "#677282",
                        }}
                      >
                        {"(" +
                          formatMessage({
                            id: "drug.configure.setting.dose.visit.judgment.desc",
                          }) +
                          ")"}
                      </span>
                    </Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                label={formatMessage({ id: "drug.configure.setting.dose.form" })}
                name="doseFormId"
                rules={[{ required: true }]}
              >
                <Select
                  // value={doseFormId}
                  onChange={onChangeDoseForm}
                  placeholder={formatMessage({
                    id: "placeholder.select.common",
                  })}
                  className="full-width"
                  options={doseForms}
                  disabled={isBlindedRole ? true : false}
                ></Select>
              </Form.Item>
              {doseFormId !== null && doseFormId !== "" &&
                (selectType === 1 ? (
                  <>
                    <Form.Item
                      label={formatMessage({
                        id: "drug.configure.setting.dose.level.set",
                      })}
                      name="doseLevelList"
                      rules={[{ required: true }]}
                    >
                      {permissions(
                        auth.project.permissions,
                        "operation.build.medicine.configuration.setting.list"
                      ) && (
                          <>
                            <Modal
                              forceRender={true}
                              className="custom-medium-modal"
                              title={
                                <span>
                                  <span
                                    style={{
                                      fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                      fontSize: "16px",
                                      fontWeight: 500,
                                      lineHeight: "24px",
                                      textAlign: "left",
                                    }}
                                  >
                                    {formatMessage({ id: "common.edit" })}
                                  </span>
                                  <span
                                    style={{
                                      fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                      fontSize: "16px",
                                      fontWeight: 400,
                                      lineHeight: "24px",
                                      textAlign: "left",
                                      marginRight: 6
                                    }}
                                  >
                                    -
                                    {formatMessage({
                                      id: "drug.configure.setting.dose.level.set",
                                    })}
                                  </span>
                                </span>
                              }
                              visible={doseLevelListOpen}
                              onCancel={doseLevelListClose}
                              destroyOnClose={true}
                              centered
                              footer={
                                <Row justify="end">
                                  <Col style={{ marginRight: "16px" }}>
                                    <Button onClick={doseLevelListClose}>
                                      {formatMessage({ id: "common.cancel" })}
                                    </Button>
                                  </Col>
                                  <Col>
                                    <Button
                                      type={"primary"}
                                      className="full-width"
                                      onClick={() => {
                                        form.validateFields()
                                          .then((values) => {
                                            // 验证通过的处理逻辑
                                            // console.log(values)
                                            if (values.selectType === 1 && !values.doseLevelList?.find((it: any) => it.initialDose === true)) {
                                              // console.log(formatMessage({id:"placeholder.select.common"})
                                              //     +formatMessage({id:"form.control.type.options.one"}))
                                              message.error(
                                                formatMessage({ id: "placeholder.select.common" })
                                                + formatMessage({ id: "form.control.type.options.one" })
                                              )
                                              return
                                            } else {
                                              setDoseLevelListOpen(false);
                                            }
                                          })
                                          .catch((errorInfo) => {
                                            // 验证不通过的处理逻辑
                                          });


                                      }}
                                    >
                                      {formatMessage({ id: "common.ok" })}
                                    </Button>
                                  </Col>
                                </Row>
                              }
                            >
                              <Row style={{ backgroundColor: "#165DFF1A", marginBottom: 16, paddingLeft: 12, paddingBottom: 12 }}>
                                <Col style={{ width: 24 }}>
                                  <svg className="iconfont" width={16} height={16} style={{ marginRight: 12, marginTop: 8 }} fill={"#165DFF"}>
                                    <use xlinkHref="#icon-xinxitishi1" ></use>
                                  </svg>
                                </Col>
                                <Col style={{ width: "calc(100% - 50px)", marginTop: 6 }}>
                                  {formatMessage({ id: "drug.configure.setting.dose.level.set.tip1" })}
                                </Col>

                              </Row>
                              <TableDraggableSortable
                                columns={doseLevelColumns}
                                dataSource={doseLevelListData.map(
                                  (item: any, index: number) => ({
                                    ...item,
                                    index: index + 1,
                                  })
                                )}
                                // rowKey={(record:any,index:any) => (index)}
                                rowKey="index"
                                onDragEnd={onDoseLevelListDataEnd}
                                onDragStart={() => { }}
                                modalRootId="modalRoot"
                              >
                                <Table
                                  key={Math.random()}
                                  size="small"
                                  columns={doseLevelColumns}
                                  dataSource={doseLevelListData.map(
                                    (item: any, index: number) => ({
                                      ...item,
                                      index: index + 1,
                                    })
                                  )}
                                  // rowKey={(record: any, index: any) => index}
                                  rowKey="index"
                                  // scroll={{ y: height }}
                                  // style={{ marginLeft: 14, marginRight: 14 }}
                                  pagination={false}
                                  className="custom-table"
                                  components={{
                                    body: {
                                      cell: EditableCell,
                                      row: (props: any) => (
                                        <TableSortableRow {...props} />
                                      ),
                                    },
                                  }}
                                >

                                </Table>
                              </TableDraggableSortable>
                              {permissions(
                                auth.project.permissions,
                                "operation.build.medicine.configuration.setting.add"
                              ) && (
                                  <Button
                                    block
                                    type="dashed"
                                    style={{
                                      height: 36,
                                      marginBottom: 8,
                                    }}
                                    className="mar-top-10"
                                    icon={<PlusOutlined />}
                                    onClick={() => addDoseLevel()}
                                    disabled={isBlindedRole ? true : false}
                                  >
                                    <span style={{ fontSize: 12 }}>
                                      {intl.formatMessage({
                                        id: "common.addTo",
                                      })}
                                    </span>
                                  </Button>
                                )}
                            </Modal>

                            <span>
                              {/* {form.getFieldValue("doseLevelList").forEach((item: any, index: any) => (
                            <div key={index}>{item}</div>
                        ))} */}
                              {
                                (form.getFieldValue("doseLevelList") !== undefined && form.getFieldValue("doseLevelList") !== null && form.getFieldValue("doseLevelList").length > 0) ? form.getFieldValue("doseLevelList").map((obj: any) => {
                                  if (obj.initialDose) {
                                    return obj.name + "*"
                                  } else {
                                    return obj.name
                                  }
                                }).join(', ') : "-"
                              }
                            </span>


                            {
                              permissionsCohort(
                                auth.project.permissions,
                                "operation.build.medicine.configuration.setting.edit",
                                props.cohort?.status
                              ) && <Link
                                className="mar-rgt-5"
                                style={{ marginLeft: "8px" }}
                                onClick={() => {
                                  // doseLevelListEdit;
                                  if (
                                    form.getFieldValue("doseLevelList") === undefined ||
                                    form.getFieldValue("doseLevelList") === null ||
                                    form.getFieldValue("doseLevelList").length === 0
                                  ) {
                                    form.setFieldsValue({
                                      ...form.getFieldsValue(),
                                      doseLevelList: [
                                        {
                                          name: null,
                                          group: [],
                                          doseDistribution: [],
                                          initialDose: false,
                                        },
                                      ],
                                    });
                                  }
                                  setDoseLevelListData(
                                    form.getFieldValue("doseLevelList") ? form.getFieldValue("doseLevelList") : []
                                  );
                                  setDoseLevelListOpen(true);
                                }}
                              >
                                {formatMessage({ id: "common.edit" })}
                              </Link>
                            }

                          </>
                        )}
                    </Form.Item>
                    <div
                      style={{
                        marginLeft: g.lang === "en" ? 156 : 126,
                        width: "600px",
                        height: "32px",
                        backgroundColor: "#F8F9FA",
                      }}
                    >
                      <Form.Item
                        style={{
                          display: "inline-block",
                          marginLeft: "8px",
                          // width: "220px",
                        }}
                        name="isFirstInitial"
                      >
                        <Checkbox
                          checked={firstInitial}
                          onChange={onChangeIsFirstInitial}
                          disabled={isBlindedRole ? true : false}
                        >
                          <span
                            style={{
                              fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                              fontSize: "14px",
                              fontWeight: 400,
                              lineHeight: "20px",
                              textAlign: "left",
                            }}
                          >
                            {formatMessage({
                              id: "drug.configure.setting.dose.form.list.isFirstInitial",
                            })}
                          </span>
                        </Checkbox>
                      </Form.Item>
                    </div>
                    <div
                      style={{
                        marginLeft: g.lang === "en" ? 156 : 126,
                        width: "600px",
                        height: "32px",
                        backgroundColor: "#F8F9FA",
                      }}
                    >
                      <Form.Item
                        style={{
                          display: "inline-block",
                          marginLeft: "8px",
                          // width: "260px",
                        }}
                        name="isDoseReduction"
                      >
                        <Checkbox
                          checked={doseReduction}
                          onChange={onChangeIsDoseReduction}
                          disabled={isBlindedRole ? true : false}
                        >
                          <span
                            style={{
                              fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                              fontSize: "14px",
                              fontWeight: 400,
                              lineHeight: "20px",
                              textAlign: "left",
                            }}
                          >
                            {formatMessage({
                              id: "drug.configure.setting.dose.form.list.isDoseReduction",
                            })}
                          </span>
                        </Checkbox>
                      </Form.Item>
                      {doseReduction && (
                        <Form.Item
                          style={{
                            display: "inline-block",
                            marginLeft: g.lang === "en" ? "-5px" : "0px",
                            width: "30px",
                          }}
                          name="frequency"
                        >
                          <InputNumber
                            size="small"
                            min={0}
                            placeholder={formatMessage({
                              id: "placeholder.input.common",
                            })}
                            max={99}
                            precision={0}
                            disabled={isBlindedRole ? true : false}
                          />
                        </Form.Item>
                      )}
                    </div>
                  </>
                ) : (
                  <Form.Item
                    label={formatMessage({
                      id: "drug.configure.setting.dose.visit.judgment",
                    })}
                    name="visitJudgmentList"
                    rules={[{ required: true }]}
                  >
                    {permissions(
                      auth.project.permissions,
                      "operation.build.medicine.configuration.setting.list"
                    ) && (
                        <>
                          <TableDraggableSortable
                            columns={visitJudgmentColumns}
                            dataSource={visitJudgmentListData.map(
                              (item: any, index: number) => ({
                                ...item,
                                index: index + 1,
                              })
                            )}
                            // rowKey={(record:any,index:any) => (index)}
                            rowKey="index"
                            onDragEnd={onVisitJudgmentListDataEnd}
                            onDragStart={() => { }}
                            modalRootId="modalRoot"
                          >
                            <Table
                              // key={Math.random()}
                              size="small"
                              columns={visitJudgmentColumns}
                              dataSource={visitJudgmentListData.map(
                                (item: any, index: number) => ({
                                  ...item,
                                  index: index + 1,
                                })
                              )}
                              // rowKey={(record:any,index:any) => (index)}
                              rowKey="index"
                              // scroll={{y: height}}
                              // scroll={{ x: "max-content" }}
                              // style={{marginLeft:14,marginRight:14}}
                              pagination={false}
                              className="custom-table"
                              components={{
                                body: {
                                  cell: EditableCell,
                                  row: (props: any) => (
                                    <TableSortableRow {...props} />
                                  ),
                                },
                              }}
                            ></Table>
                          </TableDraggableSortable>
                          {permissions(
                            auth.project.permissions,
                            "operation.build.medicine.configuration.setting.add"
                          ) && (
                              <Button
                                block
                                type="dashed"
                                style={{
                                  height: 36,
                                  marginBottom: 8,
                                }}
                                className="mar-top-10"
                                icon={<PlusOutlined />}
                                onClick={() => addVisitJudgment()}
                                disabled={isBlindedRole ? true : false}
                              >
                                <span style={{ fontSize: 12 }}>
                                  {intl.formatMessage({
                                    id: "common.addTo",
                                  })}
                                </span>
                              </Button>
                            )}
                        </>
                      )}
                  </Form.Item>
                ))}

            </>
          }

          {doseFormId !== null && doseFormId !== "" && selectType === 2 && visitJudgmentListData?.find(
                  (item: any, index: number) => item.visitInheritance
              ) &&
              <VisitInheritValue form={form} visitJudgmentGroups={visitJudgmentGroups} isBlindedRole={isBlindedRole}/>
          }
        </StyleFrom>
      </Modal>
    </React.Fragment>
  );
};



const StyleFrom = styled(Form)`
  .ant-form-item-label label {
    text-align: right;
  }
`;

const MyTable = styled(Table)`
  .ant-table-thead tr th:first-child,
  .ant-table-tbody .ant-table-row td:first-child {
    border-right: 1px solid #e3e4e6;
  }
`;


const VisitInheritValue = (props:any)=> {
  const {visitJudgmentGroups, form, isBlindedRole} = props
  const intl = useIntl();

  const [selectedGroupLabel, setSelectGroupLabel] = useSafeState<any>([])

  const updateSelectedGroupLabel = ()  => {
    setSelectGroupLabel(form.getFieldValue("groupType")?.filter((it: any) => !!it.group)?.map((it: any) => it?.group))
  }

  useEffect(() => {
    updateSelectedGroupLabel()
  }, [form])

  const groupLabel = useMemo(() => {
    return visitJudgmentGroups.map((it: any) => ({
      ...it,
      label : isBlindedRole?"******":it.label,
      disabled: selectedGroupLabel?.includes(it.value)
    }))
  }, [visitJudgmentGroups, selectedGroupLabel])

  return  <Form.Item label={intl.formatMessage({id:"drug.configure.setting.dose.form.list.visit.inheritance.dispensing"})}>
    {/*<Form.List name={""}>*/}
    <Form.List name={"groupType"}>
      {(fields: any, { add, remove }) => (
          <>
            {fields.map((field: any, index: any) => (
                <Row key={field.key} gutter={12} style={{display: 'flex', }} >
                  <Col style={{flex: 1}}>
                    <Form.Item

                        {...field}
                        name={[field.name, "group"]}
                        rules={[{ required: true, message:intl.formatMessage({id:"placeholder.select.common"}) }]}
                    >
                      <Select
                          disabled={isBlindedRole}
                          onChange={updateSelectedGroupLabel}
                          options={groupLabel}>

                      </Select>
                    </Form.Item>
                  </Col>
                  <Col style={{flex: 1}}>
                    <Form.Item
                        {...field}
                        name={[field.name, "type"]}
                        rules={[{ required: true, message:intl.formatMessage({id:"placeholder.select.common"}) }]}
                    >
                      <Select disabled={isBlindedRole}>
                        <Select.Option value={1}>
                          {intl.formatMessage({id:"drug.configure.setting.dose.form.list.visit.inheritance.stop"})}
                        </Select.Option>
                        <Select.Option value={2}>
                          {intl.formatMessage({id:"drug.configure.setting.dose.form.list.visit.inheritance.main"})}

                        </Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col style={{display: 'flex'}}>
                    {fields.length > 1 && !isBlindedRole && (
                        <Row style={{height: '30px'}}>
                          <MinusCircleFilled style={{color: "#F96964",marginLeft: 12, marginRight: 12}} onClick={() => {
                            remove(field.name);
                            updateSelectedGroupLabel()
                          }}/>
                        </Row>
                    )}
                  </Col>


                </Row>

            ))}
            {
            !isBlindedRole &&
              <Form.Item>
                <Button
                    type="dashed"
                    onClick={() => {
                      add()
                    }}
                    block
                    className="mar-top-10"
                    icon={<PlusOutlined />}
                >
                   <span style={{ fontSize: 12 }}>
                                      {intl.formatMessage({
                                        id: "common.addTo",
                                      })}
                                    </span>
                </Button>
              </Form.Item>
            }
          </>
      )}
    </Form.List>

    {/*</Form.List>*/}
  </Form.Item>
}