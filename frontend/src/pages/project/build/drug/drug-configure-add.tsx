import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Space,
  Switch,
  Table,
  Tooltip,
  notification,
} from "antd";
import {
  CloseCircleFilled,
  PlusOutlined,
  QuestionCircleFilled,
  CloseOutlined,
} from "@ant-design/icons";
import { addConfigure, updateConfigure, addConfigureVerify, updateConfigureVerify } from "../../../../api/drug";
import {
  getRandomization,
  getProjectAttribute,
} from "../../../../api/randomization";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { useSafeState } from "ahooks";
import { useGlobal } from "../../../../context/global";
import { TipInput, TipInputNumber } from "../../../../components/TipInput";
import styled from "@emotion/styled";
import { useDrug } from "./context";
import { FormulaData } from "../../../../data/data";
import Draggable from "react-draggable";
import { Formula, WeightCol } from "./formula";
import _ from "lodash";
import {getVisitSettings} from "../../../../api/visit";

export const DrugConfigureAdd = (props: any) => {
  const g = useGlobal();
  const auth = useAuth();
  const intl = useIntl();
  const drug = useDrug();
  const { formatMessage } = intl;

  const [visible, setVisible] = useSafeState<boolean>(false);
  const [form] = Form.useForm();
  const [id, setId] = useSafeState<any>(null);
  const [send, setSend] = useSafeState(true);
  const [oldData, setOldData] = useSafeState(null);
  const [configureId, setConfigureId] = useSafeState<any>(null);
  const [onlyId, setOnlyId] = useSafeState(null);
  const [initVisitCycles, setInitVisitCycles] = useSafeState<any[]>([]);
  const [visitCycles, setVisitCycles] = useSafeState<any[]>([]);
  const [routineVisit, setRoutineVisit] = useSafeState<any[]>([]);
  const [routineVisitMap, setRoutineVisitMap] = useSafeState<any>(new Map());
  const [groups, setGroups] = useSafeState<any[]>([]);
  const [rooms, setRooms] = useSafeState<any[]>([]);
  const [oldOpenSetting, setOldOpenSetting] = useSafeState<number>(1);
  const [openSetting, setOpenSetting] = useSafeState<number>(1);
  const [option, setOption] = useSafeState<any>({ parName: "", subName: "" });
  const [selectFormula, setSelectFormula] = useSafeState<any>(false);
  const [labelTipShow, setLabelTipShow] = useSafeState<any>(false);
  const [labelItemTipShow, setLabelItemTipShow] = useSafeState<any>(false);
  
  const [visitNames, setVisitNames] = useSafeState<any[]>([]);
  const [drugs, setDrugs] = useSafeState<any[]>([]);
  const [drugMap, setDrugMap] = useSafeState<any>(new Map());

  const projectId = auth.project.id;
  const envId = auth.env ? auth.env.id : null;
  const cohortId = props.cohort ? props.cohort.id : null;
  const roleId = auth.project.permissions.role_id;
  const connectAli = auth.project.connectAli;
  const customerId = auth.customerId;
  const room = auth.project.info.room;

  const [open, setOpen] = useState(false);
  const [attribute, setAttribute] = useSafeState(null);
  const [iconColor, setIconColor] = useSafeState("#999999");
  const [inputValue, setInputValue] = useState("");
  const [position, setPosition] = useState({ x: 500, y: 50 });
  const [codeValidate, setCodeValidate] = useSafeState<any>({});
  const [nameValidate, setNameValidate] = useSafeState<any>({});
  const [medicineValidate, setMedicineValidate] = useSafeState<any>(new Map());

  const [oldGroupSelect, setOldGroupSelect] = useSafeState<any>("N/A");

  const [outSideId, setOutSideId] = useSafeState<any>(null);

  const { runAsync: getRandomizationRun } = useFetch(getRandomization, {
    manual: true,
  });

  const {runAsync: getVisitSettingsRun, loading: getVisitSettingsLoading} = useFetch(getVisitSettings, {manual: true});


  const afterConfigureError: any = (e: any, params: any) => {
    const contentType = e.headers.get("content-type");
    if (contentType && contentType.indexOf("application/json") > -1) {
      e.json().then((result: any) => {
        switch (result.code) {
          case 1001:
            setCodeValidate({ validateStatus: "error", help: result.msg });
            break;
          default:
            message.error(result.msg).then();
            break;
        }
      });
    }
  };
  const { runAsync: updateConfigureRun, loading: updateConfigureRunLoading } =
    useFetch(updateConfigure, { manual: true, onError: afterConfigureError });
  const { runAsync: addConfigureRun, loading: addConfigureLoading } = useFetch(
    addConfigure,
    { manual: true, onError: afterConfigureError }
  );

  const { runAsync: updateConfigureVerifyRun, loading: updateConfigureVerifyRunLoading } =
    useFetch(
      updateConfigureVerify, 
      { 
        manual: true,
        onError: (err: any) => {
            // form.resetFields();
            err.json().then((data: any) =>
                notification.open({
                    message: (
                        <div
                            style={{
                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                fontSize: "14px",
                            }}
                        >
                            <CloseCircleFilled
                                style={{
                                    color: "#F96964",
                                    paddingRight: "8px",
                                }}
                            />
                            {formatMessage({ id: "common.operation.error" })}
                        </div>
                    ),
                    description: (
                        <div
                            style={{
                                paddingLeft: "20px",
                                color: "#646566",
                            }}
                        >
                            {data.msg}
                        </div>
                    ),
                    duration: 5,
                    placement: "top",
                    style: {
                        width: "720px",
                        // height: "88px",
                        background: "#FEF0EF",
                        borderRadius: "4px",
                    },
                })
            );
        },
      }
    );
  const { runAsync: addConfigureVerifyRun, loading: addConfigureVerifyLoading } = useFetch(
    addConfigureVerify,
    { 
      manual: true,
      onError: (err: any) => {
          // form.resetFields();
          err.json().then((data: any) =>
              notification.open({
                  message: (
                      <div
                          style={{
                              fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                              fontSize: "14px",
                          }}
                      >
                          <CloseCircleFilled
                              style={{
                                  color: "#F96964",
                                  paddingRight: "8px",
                              }}
                          />
                          {formatMessage({ id: "common.operation.error" })}
                      </div>
                  ),
                  description: (
                      <div
                          style={{
                              paddingLeft: "20px",
                              color: "#646566",
                          }}
                      >
                          {data.msg}
                      </div>
                  ),
                  duration: 5,
                  placement: "top",
                  style: {
                      width: "720px",
                      // height: "88px",
                      background: "#FEF0EF",
                      borderRadius: "4px",
                  },
              })
          );
      },
    }
  );

  const {
    runAsync: getProjectAttributeRun,
    loading: getProjectAttributeLoading,
  } = useFetch(getProjectAttribute, { manual: true });

  const show = (id: any, visitCycles: any, configure: any) => {
    setVisible(true);
    if (id) {
      setId(id);
    }
    setOldGroupSelect("N/A");
    setSend(true);
    getProjectAttributeRun({
      projectId,
      env: envId,
      cohort: cohortId ? cohortId : null,
      customer: customerId,
    }).then((result: any) => {
      let da: any = result.data;
      setAttribute(da);
      if (configure) {
        setOldGroupSelect(configure.group);
        setOldData(configure);
        drug.setGroupSelect(configure.group);
        form.setFieldsValue({ ...configure });
        setConfigureId(configure.id);
        setOnlyId(configure.onlyId);
        setOldOpenSetting(configure.openSetting);
        setOpenSetting(configure.openSetting);
        setSelectFormula(configure.isFormula);
        drug.setFormulaType(configure.calculationType);
        initFormulas(configure);
        if (configure.label !== "") {
          setLabelTipShow(true)
        }
        let values: any = configure.values
        if (values?.find((item: any) => item.label && item.label !== "") || configure?.openSetting === 2) {
          setLabelItemTipShow(true)
        }
        setVisitNames(configure.visitCycles);
        // form.setFieldsValue({
        //   ...form.getFieldsValue,
        //   visitCycles: configure.visitCycles,
        // });
      } else {
        //根据属性配置，判断开放配置默认值
        var blind = da ? da.info.blind : false;
        setOldOpenSetting(blind ? 1 : 2);
        setOpenSetting(blind ? 1 : 2);
        form.setFieldsValue({ ...form.getFieldsValue, values: [{}] });
        setLabelTipShow(false)
        setLabelItemTipShow(false)

      }
    });
    if (visitCycles) {
      setInitVisitCycles(visitCycles);
      onselectGroup(
        configure ? configure.group : "N/A",
        visitCycles,
        configure,
        configure
          ? { parName: configure.parName, subName: configure.subName }
          : { parName: "", subName: "" }
      );
    }
    //获取实验组信息
    getRandomizationRun({
      projectId,
      env: envId,
      cohort: cohortId,
      customer: customerId,
    }).then((result: any) => {
      const options = [
        {
          label: "N/A",
          value: "N/A",
          parName: "",
          subName: "",
        },
      ];
      if (result.data.groups != null && result.data.groups.length > 0) {
        result.data.groups?.forEach((it: any) => {
          if (it.subGroup != null && it.subGroup.length !== 0) {
            for (let i = 0; i < it.subGroup.length; i++) {
              options.push({
                label: it.name + " " + it.subGroup[i].name,
                value: it.name + " " + it.subGroup[i].name,
                parName: it.name,
                subName: it.subGroup[i].name,
              });
            }
          } else {
            options.push({
              label: it.name,
              value: it.name,
              parName: it.name,
              subName: "",
            });
          }
        });
      }
      setGroups(options);
    });
  };

  const hide = () => {
    setOldData(null);
    setSend(true);
    setOldOpenSetting(0);
    setOpenSetting(0);
    setVisible(false);
    form.resetFields();
    setConfigureId(null);
    setOnlyId(null);
    setRooms([]);
    drug.setFormulaType(0);
    setOption([]);
    drug.setConfigValues([]);
    setOldGroupSelect("N/A");
    drug.setGroupSelect("N/A");
    setCodeValidate({});
    setNameValidate({});
    setMedicineValidate(new Map());
    setSelectFormula(false);
    setOpen(false);
    setLabelTipShow(false);
    setVisitNames([]);
    setOutSideId(null);
    setRoutineVisit([]);
    setDrugs([]);
    setDrugMap(new Map());
  };

  const formChange = () => {
    if (configureId) {
      let a = _.cloneDeep(oldData);
      // console.log("1===" + JSON.stringify(a)); 
      let b = _.cloneDeep(form.getFieldsValue());
      // console.log("2===" + JSON.stringify(b)); 
      if (!compareObjects(a, b)) {
          setSend(false);
      } else {
          setSend(true);
      }
    }
  };

  //比较两个JavaScript对象是否相同
  function compareObjects(obj1: any, obj2: any) {
      for (let key in obj1) { 
          if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
              if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
              if (!arraysAreEqual(obj1[key], obj2[key])) {
                  return false;
              }
              } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                  if (!compareObjects(obj1[key], obj2[key])) {
                      return false;
                  }
              } else {
                  if (obj1[key] !== obj2[key]) {
                      return false;
                  }
              }
          }
      }
      return true;
  }

  //比较两个数组是否相同
  function arraysAreEqual(arr1: any, arr2: any) {
      // 检查数组长度是否相同
      if (arr1.length !== arr2.length) {
          return false;
      }
      
      const a = _.cloneDeep(arr1);
      const b = _.cloneDeep(arr2);
      // 将数组转换为字符串并比较
      const str1 = JSON.stringify(a.sort());
      const str2 = JSON.stringify(b.sort());

      return str1 === str2;
  }

  const changeSetFormulaType = (value: any) => {
    drug.setFormulaType(value);
    initFormulas(null);
  };

  const onselectGroup = (
    value: any,
    visitCycle: any,
    configure: any,
    option: any
  ) => {
    drug.setGroupSelect(value);

    let initVisitCycle = initVisitCycles;
    if (visitCycle) {
      initVisitCycle = visitCycle;
    }

    let visitCycles = initVisitCycle.filter((item: any) =>
      item.option?.find((it: any) => it === value)
    );
    setRoutineVisit(visitCycle);

    let vc: any = []
    if(form.getFieldValue("group") !== null && form.getFieldValue("group") !== undefined){
      vc = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined) && item.option?.find((it: any) => it === form.getFieldValue("group"))
      ); 
      setRoutineVisit(vc);
    } else {
      vc = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined) && item.option?.find((it: any) => it === value)
      );
      setRoutineVisit(visitCycles);
    }
    form.setFieldValue("routineVisitMappingList", null);

    getVisitSettingsRun({
        customerId,
        projectId,
        envId,
        cohortId: cohortId,
        roleId: roleId,
    }).then((result: any) => {
        if(result.data.isOpen){
          let out = {
            label: g.lang === "en"?result.data.nameEn:result.data.nameZh,
            option:[],
            value:result.data.id,
            out: true,
          }
          if(visitNames !== null && visitNames !== undefined && visitNames.length === 1 && visitNames[0] === result.data.id){
            setRoutineVisit(vc);
          }
          visitCycles.push(out);
          setOutSideId(result.data.id);
        }
        setVisitCycles(visitCycles);
        let visit = form.getFieldsValue().visitCycles;
        if (configure) {
          visit = configure.visitCycles;
        }
        setOption({ parName: option.parName, subName: option.subName });
        let visitCyclesValue = visit?.filter((item: any) =>
          visitCycles.find((it: any) => it.value === item)
        );
        form.setFieldsValue({
          ...form.getFieldsValue,
          visitCycles: visitCyclesValue,
        });
        initDug(configure);
        initVisit(configure, vc);
    });
  };


  const initVisit = (configure: any, routineVisit: any) => {
    let visit = (routineVisit || []).map((value: any) => ({
        value: value.value,
        label: value.label,
        option:value.option,
        disable: false,
    }))
    setRoutineVisit(visit);
    if(configure.routineVisitMappingList !== null && configure.routineVisitMappingList !== undefined){
      for (let w = 0; w < configure.routineVisitMappingList.length; w++) {
        if (configure.routineVisitMappingList[w].drugList !== null && configure.routineVisitMappingList[w].drugList !== undefined && configure.routineVisitMappingList[w].drugList.length > 0) {

          let dList: any[] = [];
          let dvList: any[] = [];
          if(configure.routineVisitMappingList !== null && configure.routineVisitMappingList !== undefined){
              for (let i = 0; i < configure.routineVisitMappingList.length; i++) {
                if (configure.routineVisitMappingList[i].visitList !== null && configure.routineVisitMappingList[i].visitList !== undefined && configure.routineVisitMappingList[i].visitList.length > 0) {
                  for (let k = 0; k < configure.routineVisitMappingList[i].visitList.length; k++) {
                    if(configure.routineVisitMappingList[i].visitList[k] !== null && configure.routineVisitMappingList[i].visitList[k] !== undefined){
                      if(configure.routineVisitMappingList[i].drugList !== null && configure.routineVisitMappingList[i].drugList !== undefined && configure.routineVisitMappingList[i].drugList.length > 0){
                        for (let j = 0; j < configure.routineVisitMappingList[i].drugList.length; j++) {
                          if(configure.routineVisitMappingList[i].drugList[j]?.drugName !== null && configure.routineVisitMappingList[i].drugList[j]?.drugName !== undefined){
                            if(i !== w){
                              dvList.push(configure.routineVisitMappingList[i].visitList[k] + configure.routineVisitMappingList[i].drugList[j].drugName);
                            }
                          }
                        }
                      }
                    }
                  }
                }
                if(configure.routineVisitMappingList[i].drugList !== null && configure.routineVisitMappingList[i].drugList !== undefined && configure.routineVisitMappingList[i].drugList.length > 0){
                  for (let j = 0; j < configure.routineVisitMappingList[i].drugList.length; j++) {
                    if(configure.routineVisitMappingList[i].drugList[j]?.drugName !== null && configure.routineVisitMappingList[i].drugList[j]?.drugName !== undefined){
                      if(i === w){
                        dList.push(configure.routineVisitMappingList[i].drugList[j]?.drugName);
                      }
                    }
                  }
                }
            }
          }

          let d = configure.routineVisitMappingList[w].drugList.map((item: any) => item.drugName);
          if(dList.length > 0 && dvList.length > 0){
            const updatedVisit = (visit).map((it: any) => {
              // 使用 some 方法来判断是否有匹配的项
              if (dList.some((item: any) => (dvList.includes(it.value + item) && d.includes(item)))) {
                return { ...it, disable: true };
              } else {
                return { ...it, disable: false };
              }
            });
            setRoutineVisitMap((prevRoutineVisit: any) => {
                const newRoutineVisit = new Map(prevRoutineVisit);
                newRoutineVisit.set(w, updatedVisit);
                return newRoutineVisit;
            });
          } else {
            setRoutineVisitMap((prevRoutineVisit: any) => {
                const newRoutineVisit = new Map(prevRoutineVisit);
                newRoutineVisit.set(w, visit);
                return newRoutineVisit;
            });
          }

        } else {
          setRoutineVisitMap((prevRoutineVisit: any) => {
              const newRoutineVisit = new Map(prevRoutineVisit);
              newRoutineVisit.set(w, visit);
              return newRoutineVisit;
          });
        }
      }
    }
  }

  const initDug = (configure: any) => {
    let medicine = (configure.values || []).map((value: any) => ({
        value: value.drugName,
        label: value.drugName,
        disable: false,
    }))
    setDrugs(medicine);
    let vList: any[] = [];
    let dList: any[] = [];
    if(configure.routineVisitMappingList !== null && configure.routineVisitMappingList !== undefined){
        for (let i = 0; i < configure.routineVisitMappingList.length; i++) {
          if (configure.routineVisitMappingList[i].visitList !== null && configure.routineVisitMappingList[i].visitList !== undefined && configure.routineVisitMappingList[i].visitList.length > 0) {
            for (let k = 0; k < configure.routineVisitMappingList[i].visitList.length; k++) {
              if(configure.routineVisitMappingList[i].visitList[k] !== null && configure.routineVisitMappingList[i].visitList[k] !== undefined){
                vList.push(configure.routineVisitMappingList[i].visitList[k]);
                if(configure.routineVisitMappingList[i].drugList !== null && configure.routineVisitMappingList[i].drugList !== undefined && configure.routineVisitMappingList[i].drugList.length > 0){
                  for (let j = 0; j < configure.routineVisitMappingList[i].drugList.length; j++) {
                    if(configure.routineVisitMappingList[i].drugList[j]?.drugName !== null && configure.routineVisitMappingList[i].drugList[j]?.drugName !== undefined){
                      dList.push(configure.routineVisitMappingList[i].visitList[k] + configure.routineVisitMappingList[i].drugList[j].drugName);
                    }
                  }
                }
              }
            }
          }
      }
    }

    if(configure.routineVisitMappingList !== null && configure.routineVisitMappingList !== undefined){
      for (let i = 0; i < configure.routineVisitMappingList.length; i++) {
        if (configure.routineVisitMappingList[i].visitList !== null && configure.routineVisitMappingList[i].visitList !== undefined && configure.routineVisitMappingList[i].visitList.length > 0) {
          let v = configure.routineVisitMappingList[i].visitList
          if(vList.length > 0 && dList.length > 0){
            const updatedMedicines = (medicine).map((it: any) => {
              // 使用 some 方法来判断是否有匹配的项
              if (vList.some((item) => (dList.includes(item + it.value) && v.includes(item)))) {
                return { ...it, disable: true };
              } else {
                return { ...it, disable: false };
              }
            });
            setDrugMap((prevDrug: any) => {
                const newDrug = new Map(prevDrug);
                newDrug.set(i, updatedMedicines);
                return newDrug;
            });
          } else {
            setDrugMap((prevDrug: any) => {
                const newDrug = new Map(prevDrug);
                newDrug.set(i, medicine);
                return newDrug;
            });
          }
        } else {
          setDrugMap((prevDrug: any) => {
              const newDrug = new Map(prevDrug);
              newDrug.set(i, medicine);
              return newDrug;
          });
        }
      }
    }
  }


  const getCustomDispensingNumberMax = (value: string) => {
    const arr = value.split(/[~,]/).map(Number);
    const max = Math.max(...arr);
    return max;
  };

  const checkLabel = (value: any) => {
    let check = true;
    if (
      openSetting === 1 &&
      (value.label === undefined || value.label === "")
    ) {
      value.values.map((item: any) => {
        if (item.label === undefined || item.label === "") {
          check = false;
        }
      });
    }
    return check;
  };

  const save = () => {
    if (nameValidate === null || nameValidate === undefined || Object.keys(nameValidate).length === 0) {
      if (configureId != null && configureId !== undefined) {
        form
          .validateFields()
          .then((values: any) => {
            const check = checkLabel(values);
            if (!check) {
              message.error(
                intl.formatMessage({
                  id: "drug.configure.label.config.duplicate.error",
                })
              );
              return;
            }
            let newValue: any[] = [];
            let hasCustomerDispensingNumber: boolean = false;
            let medicineNameList: any[] = [];
            values.values?.map((item: any, index: any) => {
              if(item.drugName !== undefined && item.drugName !== null && item.drugName !== ""){
                medicineNameList.push(item.drugName);
              }
              if (openSetting !== 3) {
                if (
                  item.customDispensingNumber?.includes(",") ||
                  item.customDispensingNumber?.includes("~")
                ) {
                  hasCustomerDispensingNumber = true;
  
                  item.isCount = true;
                  item.dispensingNumber = getCustomDispensingNumberMax(
                    item.customDispensingNumber
                  );
                } else {
                  item.dispensingNumber = Number(item.customDispensingNumber);
                  item.isCount = false;
                }
              } else if (openSetting !== 3) {
                item.isOpen = false;
              }
              newValue.push(item);
            });
            values.values = newValue;

            // let routineMedicineNameList: any[] = [];
            let hasEmptyValue = true;
            values.routineVisitMappingList?.map((item: any, index: any) => {
              if(item.drugList !== undefined && item.drugList !== null && item.drugList.length > 0){
                item.drugList?.map((it: any, i: any) => {
                  if(it.drugName !== undefined && it.drugName !== null && it.drugName !== ""){
                    // routineMedicineNameList.push(it.drugName);
                    if (medicineNameList.indexOf(it.drugName) === -1) {
                      hasEmptyValue = false;
                      updateField("medicineValidate", (index + "-" + i), { validateStatus: "error", help: formatMessage({id: "drug.configure.visitCycles.config.error"}) });
                    } else {
                      if (medicineValidate.has((index + "-" + i))) {
                        medicineValidate.delete((index + "-" + i));
                        // updateField("medicineValidate", (index + "-" + i), {});
                      }
                    }
                  }
                })
              }
            })

            // 计算差值
            // const difference = arrayDifference(routineMedicineNameList, medicineNameList);
            // console.log(difference.length)
            // if(difference.length > 0){
            //   setMedicineValidate({ validateStatus: "error", list: difference, help: formatMessage({id: "drug.configure.visitCycles.config.error"}) });
            // } else {
            //   setMedicineValidate({});
            // }
  
            if (
              values.label &&
              values.label !== "" &&
              hasCustomerDispensingNumber &&
              values.values?.length > 1
            ) {
              messageDrug();
              return;
            }
              values.parName = groups.find((it:any)=> it.value === values.group)?.parName
              values.subName = groups.find((it:any)=> it.value === values.group)?.subName
            if(hasEmptyValue){
              updateConfigureVerifyRun({
                id,
                configureId,
                drugConfigureInfo: {
                  ...values,
                  // ...option,
                  id: configureId,
                },
              }).then((data: any) => {
                updateConfigureRun({
                  id,
                  configureId,
                  drugConfigureInfo: {
                    ...values,
                    // ...option,
                    id: configureId,
                  },
                }).then((data: any) => {
                  message.success(data.msg);
                  props.refresh();
                  hide();
                });
              });
            }
          })
          .catch(() => { });
      } else {
        form
          .validateFields()
          .then((values) => {
            const check = checkLabel(values);
            if (!check) {
              message.error(
                intl.formatMessage({
                  id: "drug.configure.label.config.duplicate.error",
                })
              );
              return;
            }
            let newValue: any[] = [];
            let hasCustomerDispensingNumber: boolean = false;
            let medicineNameList: any[] = [];
            values.values?.map((item: any, index: any) => {
              if(item.drugName !== undefined && item.drugName !== null && item.drugName !== ""){
                medicineNameList.push(item.drugName);
              }
              if (openSetting !== 3) {
                if (
                  item.customDispensingNumber.includes(",") ||
                  item.customDispensingNumber.includes("~")
                ) {
                  hasCustomerDispensingNumber = true;
                  item.dispensingNumber = getCustomDispensingNumberMax(
                    item.customDispensingNumber
                  );
                  item.isCount = true;
                } else {
                  item.dispensingNumber = Number(item.customDispensingNumber);
                  item.isCount = false;
                }
              }
              newValue.push(item);
            });
            values.values = newValue;

            let hasEmptyValue = true;
            values.routineVisitMappingList?.map((item: any, index: any) => {
              if(item.drugList !== undefined && item.drugList !== null && item.drugList.length > 0){
                item.drugList?.map((it: any, i: any) => {
                  if(it.drugName !== undefined && it.drugName !== null && it.drugName !== ""){
                    // routineMedicineNameList.push(it.drugName);
                    if (medicineNameList.indexOf(it.drugName) === -1) {
                      hasEmptyValue = false;
                      updateField("medicineValidate", (index + "-" + i), { validateStatus: "error", help: formatMessage({id: "drug.configure.visitCycles.config.error"}) });
                    } else {
                      if (medicineValidate.has((index + "-" + i))) {
                        medicineValidate.delete((index + "-" + i));
                        // updateField("medicineValidate", (index + "-" + i), {});
                      }
                    }
                  }
                })
              }
            })


            if (
              values.label &&
              values.label !== "" &&
              hasCustomerDispensingNumber &&
              values.values?.length > 1
            ) {
              messageDrug();
              return;
            }

              values.parName = groups.find((it:any)=> it.value === values.group)?.parName
              values.subName = groups.find((it:any)=> it.value === values.group)?.subName
            if(hasEmptyValue){
              addConfigureVerifyRun({
                configures: [{ ...values, openSetting: openSetting }],
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                cohortId: cohortId,
              }).then((data: any) => {
                addConfigureRun({
                  configures: [{ ...values, openSetting: openSetting }],
                  customerId: customerId,
                  projectId: projectId,
                  envId: envId,
                  cohortId: cohortId,
                }).then((data: any) => {
                  message.success(data.msg);
                  props.refresh();
                  hide();
                });
              });
            }
          })
          .catch(() => { });
      }
    }
  };

  const updateField = (type: any, index: any, newValue: any) => {
    if(type === "medicineValidate"){
      setMedicineValidate((prevMedicineValidate: any) => {
            const newMedicineValidate = new Map(prevMedicineValidate);
            newMedicineValidate.set(index, newValue);
            return newMedicineValidate;
        });
    }
      
  };

  function arrayDifference(arr1: any, arr2: any) {
    return arr1.filter((item: any) => !arr2.includes(item));
  }

  const messageDrug = () => {
    notification.open({
      message: (
        <div
          style={{
            fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
            fontSize: "14px",
          }}
        >
          <CloseCircleFilled
            style={{
              color: "#F96964",
              paddingRight: "8px",
            }}
          />
          {formatMessage({ id: "common.operation.error" })}
        </div>
      ),
      description: (
        <div style={{ paddingLeft: "20px", color: "#646566" }}>
          {formatMessage({
            id: "drug.configure.label.config.error",
          })}
        </div>
      ),
      duration: 5,
      placement: "top",
      style: {
        width: "720px",
        background: "#FEF0EF",
        borderRadius: "4px",
      },
    });
  };

  React.useImperativeHandle(props.bind, () => ({ show }));

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: g.lang === "zh" ? 5 : 7 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: g.lang === "zh" ? 19 : 18 },
    },
  };

  const numberValidator = {
    validator: (rule: any, value: any, callback: any) => {
      const reg = /^(\d+|(\d+~\d+))(,(\d+|(\d+~\d+)))*$/;
      if (value && !reg.test(value)) {
        return Promise.reject(formatMessage({ id: "input.error.common" }));
      }
      if(customRange(value)){
        return Promise.reject(formatMessage({ id: "input.error.common" }));
      }
      return Promise.resolve();
    },
  };

  const selectOpen = (value: any) => {
    let visitCycle: any = []
    if(form.getFieldValue("group") !== null && form.getFieldValue("group") !== undefined){
      visitCycle = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined) && item.option?.find((it: any) => it === form.getFieldValue("group"))
      ); 
      setRoutineVisit(visitCycle);
    } else {
      visitCycle = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined)
      );
      setRoutineVisit(visitCycle);
    }
    if (value !== 3 && (form.getFieldValue("isFormula") === undefined || (form.getFieldValue("isFormula") !== null && form.getFieldValue("isFormula") !== undefined && form.getFieldValue("isFormula") === false))) {
      getVisitSettingsRun({
          customerId,
          projectId,
          envId,
          cohortId: cohortId,
          roleId: roleId,
      }).then((result: any) => {
          if(result.data.isOpen){
            let out = {
              label: g.lang === "en"?result.data.nameEn:result.data.nameZh,
              option:[],
              value:result.data.id,
              out: true,
            }
            visitCycle.push(out);
          }
          setVisitCycles(visitCycle);
          setOutSideId(result.data.id);
      });
    } else {
      setVisitCycles(visitCycle);
    }
    setOpenSetting(value);
    if(value === 3){
      if(form.getFieldValue("visitCycles") !== undefined && form.getFieldValue("visitCycles") !== null && form.getFieldValue("visitCycles").length === 1){
        if(form.getFieldValue("visitCycles")[0] === outSideId){
          setNameValidate({ validateStatus: "error", help: formatMessage({id: "drug.configure.visitCycles.name.err"}) });
        }
      }
    } else {
      setNameValidate({});
    }
    if (value === 3) {
      form.setFieldsValue({
        ...form.getFieldsValue,
        calculationType: null,
        customerCalculation: null,
      });
      setSelectFormula(false);
    }
    if (value !== 1) {
      let value: any = form.getFieldsValue().values;
      value?.forEach((it: any, index: number) => {
        value[index].label = "";
      });
      form.setFieldsValue({
        ...form.getFieldsValue,
        isFormula: false,
        calculationType: null,
        customerCalculation: null,
      });
      setSelectFormula(false);
    }
    if (value === 2) {
      setLabelItemTipShow(true)
    }
    if (!compareObjects(oldData,{
        ...form.getFieldsValue,
        isFormula: false,
        calculationType: null,
        customerCalculation: null,
      }) || oldOpenSetting !== value) {
        setSend(false);
    } else {
        setSend(true);
    }
  };

  const changeSepc = (value: any) => {
    drug.setConfigValues(form.getFieldsValue().values);
  };

  const deleteClick = (remove: any, fieldName: any) => {
    remove(fieldName);
    initFormulas(null);
  };

  const addClick = (add: any, field: any) => {
    add();
    initFormulas(null);
  };

  const initFormulas = (configure: any) => {
    let data: any = form.getFieldsValue();
    if (configure) {
      data = configure;
    }
    let formula: any = [];
    let bsa: boolean =
      data.calculationType &&
      (data.calculationType === 1 || data.calculationType === 2);
    if (bsa) {
      formula = [{}];
    }
    let isOpen: boolean = false;
    if (data.openSetting === 3) {
      isOpen = true;
    }
    data.values?.forEach((value: any, index: number) => {
      if (
        !value?.calculationInfo?.formulas ||
        (value?.calculationInfo?.formulas?.length === 0 && bsa)
      ) {
        data.values[index] = {
          ...data.values[index],
          calculationInfo: {
            ...data.values[index]?.calculationInfo,
            specifications: { unit: "mg" },
            formulas: formula,
          },
          isOpen: isOpen,
        };
      }
    });
    form.setFieldsValue({ ...data });
    drug.setConfigValues({ ...data.values });
    setMedicine(form.getFieldValue("values"));
    let kk = {
      values: form.getFieldValue("values"),
      routineVisitMappingList: form.getFieldValue("routineVisitMappingList")
    };
    initDug(kk);
    // numberRoutineValidator.then()
    form.validateFields(["routineVisitMappingList","drugList","customDispensingNumber"]).then();
    // initVisit(kk, routineVisit);
  };


  const dug = (index: any) => {
    let v: any[] = [];
    let vList: any[] = [];
    let dList: any[] = [];
    if(form.getFieldValue("routineVisitMappingList") !== null && form.getFieldValue("routineVisitMappingList") !== undefined){
        for (let i = 0; i < form.getFieldValue("routineVisitMappingList").length; i++) {
          if (form.getFieldValue("routineVisitMappingList")[i].visitList !== null && form.getFieldValue("routineVisitMappingList")[i].visitList !== undefined && form.getFieldValue("routineVisitMappingList")[i].visitList.length > 0) {
            if(i === index){
              v = form.getFieldValue("routineVisitMappingList")[i].visitList
            }
            for (let k = 0; k < form.getFieldValue("routineVisitMappingList")[i].visitList.length; k++) {
              if(form.getFieldValue("routineVisitMappingList")[i].visitList[k] !== null && form.getFieldValue("routineVisitMappingList")[i].visitList[k] !== undefined){
                vList.push(form.getFieldValue("routineVisitMappingList")[i].visitList[k]);
                if(form.getFieldValue("routineVisitMappingList")[i].drugList !== null && form.getFieldValue("routineVisitMappingList")[i].drugList !== undefined && form.getFieldValue("routineVisitMappingList")[i].drugList.length > 0){
                  for (let j = 0; j < form.getFieldValue("routineVisitMappingList")[i].drugList.length; j++) {
                    if(form.getFieldValue("routineVisitMappingList")[i].drugList[j]?.drugName !== null && form.getFieldValue("routineVisitMappingList")[i].drugList[j]?.drugName !== undefined){
                      dList.push(form.getFieldValue("routineVisitMappingList")[i].visitList[k] + form.getFieldValue("routineVisitMappingList")[i].drugList[j].drugName);
                    }
                  }
                }
              }
            }
          }
      }
    }
    // console.log("vList===" + JSON.stringify(vList));
    // console.log("dList===" + JSON.stringify(dList));
    // console.log("index===" + JSON.stringify(index));
    if(vList.length > 0 && dList.length > 0){
      const updatedMedicines = (drugs).map((it: any) => {
        // 使用 some 方法来判断是否有匹配的项
        if (vList.some((item) => (dList.includes(item + it.value) && v.includes(item)))) {
          return { ...it, disable: true };
        } else {
          return { ...it, disable: false };
        }
      });
      setDrugMap((prevDrug: any) => {
          const newDrug = new Map(prevDrug);
          newDrug.set(index, updatedMedicines);
          return newDrug;
      });
    } else {
      setDrugMap((prevDrug: any) => {
          const newDrug = new Map(prevDrug);
          newDrug.set(index, drugs);
          return newDrug;
      });
    }
    // setDrugs(updatedMedicines);
  }

  const setMedicine = (a: any) => {
    let medicine = (a || []).map((value: any) => ({
        value: value.drugName,
        label: value.drugName,
        disable: false,
    }))
    setDrugs(medicine);
  };

  const deleteRoutineClick = (remove: any, fieldName: any, index: any) => {
    remove(fieldName);
    initRoutine(null);
    rvm(index);
    dug(index);
  };

  const addRoutineClick = (add: any, field: any) => {
    add();
    initRoutine(null);
    let index = 0
    if(form.getFieldValue("routineVisitMappingList") !== null && form.getFieldValue("routineVisitMappingList") !== undefined){
      index = form.getFieldValue("routineVisitMappingList").length - 1;
    }
    rvm(index);
    dug(index);
  };

  const onChangeRoutineVisit = (index: any) => {
    initRoutine(null);
    rvm(index);
    dug(index);
  }

  const rvm = (index: any) => {
    let dList: any[] = [];
    let dvList: any[] = [];
    if(form.getFieldValue("routineVisitMappingList") !== null && form.getFieldValue("routineVisitMappingList") !== undefined){
        for (let i = 0; i < form.getFieldValue("routineVisitMappingList").length; i++) {
          if (form.getFieldValue("routineVisitMappingList")[i].visitList !== null && form.getFieldValue("routineVisitMappingList")[i].visitList !== undefined && form.getFieldValue("routineVisitMappingList")[i].visitList.length > 0) {
            for (let k = 0; k < form.getFieldValue("routineVisitMappingList")[i].visitList.length; k++) {
              if(form.getFieldValue("routineVisitMappingList")[i].visitList[k] !== null && form.getFieldValue("routineVisitMappingList")[i].visitList[k] !== undefined){
                if(form.getFieldValue("routineVisitMappingList")[i].drugList !== null && form.getFieldValue("routineVisitMappingList")[i].drugList !== undefined && form.getFieldValue("routineVisitMappingList")[i].drugList.length > 0){
                  for (let j = 0; j < form.getFieldValue("routineVisitMappingList")[i].drugList.length; j++) {
                    if(form.getFieldValue("routineVisitMappingList")[i].drugList[j]?.drugName !== null && form.getFieldValue("routineVisitMappingList")[i].drugList[j]?.drugName !== undefined){
                      if(i !== index){
                        dvList.push(form.getFieldValue("routineVisitMappingList")[i].visitList[k] + form.getFieldValue("routineVisitMappingList")[i].drugList[j].drugName);
                      }
                    }
                  }
                }
              }
            }
          }
          if(form.getFieldValue("routineVisitMappingList")[i].drugList !== null && form.getFieldValue("routineVisitMappingList")[i].drugList !== undefined && form.getFieldValue("routineVisitMappingList")[i].drugList.length > 0){
            for (let j = 0; j < form.getFieldValue("routineVisitMappingList")[i].drugList.length; j++) {
              if(form.getFieldValue("routineVisitMappingList")[i].drugList[j]?.drugName !== null && form.getFieldValue("routineVisitMappingList")[i].drugList[j]?.drugName !== undefined){
                if(i === index){
                  dList.push(form.getFieldValue("routineVisitMappingList")[i].drugList[j]?.drugName);
                }
              }
            }
          }
      }
    }

    // console.log("dList===" + JSON.stringify(dList));
    // console.log("dvList===" + JSON.stringify(dvList));
    // console.log("index===" + JSON.stringify(index));
    // console.log("routineVisit===" + JSON.stringify(routineVisit));
    if(dList.length > 0 && dvList.length > 0){
      const updatedVisit = (routineVisit).map((it: any) => {
        // 使用 some 方法来判断是否有匹配的项
        if (dList.some((item: any) => (dvList.includes(it.value + item)))) {
          return { ...it, disable: true };
        } else {
          return { ...it, disable: false };
        }
      });
      setRoutineVisitMap((prevRoutineVisit: any) => {
          const newRoutineVisit = new Map(prevRoutineVisit);
          newRoutineVisit.set(index, updatedVisit);
          return newRoutineVisit;
      });
    } else {
      setRoutineVisitMap((prevRoutineVisit: any) => {
          const newRoutineVisit = new Map(prevRoutineVisit);
          newRoutineVisit.set(index, routineVisit);
          return newRoutineVisit;
      });
    }
    // setDrugs(updatedMedicines);
  }

  function customRange(number: any) {
      let parts = number.split(","); // 以逗号为分隔符分割字符串
      let b = false
      for (let part of parts) {
          if (part.includes("~")) { // 如果字符串中包含波浪线
              let rangeParts = part.split("~"); // 以波浪线为分隔符分割字符串
              let start = parseInt(rangeParts[0], 10); // 起始数字
              let end = parseInt(rangeParts[1], 10); // 结束数字
              if(start >= end){
                b = true
              }
          }
      }
      return b;
  }

  function customDispensingNumberToSelectOption(number: any) {
      let parts = number.split(","); // 以逗号为分隔符分割字符串
      let data: any = [];
      for (let part of parts) {
          if (part.includes("~")) { // 如果字符串中包含波浪线
              let rangeParts = part.split("~"); // 以波浪线为分隔符分割字符串
              let start = parseInt(rangeParts[0], 10); // 起始数字
              let end = parseInt(rangeParts[1], 10); // 结束数字
              for (let i = start; i <= end; i++) {
                  if (i === 0) {
                      continue;
                  }
                  data.push(i);
              }
          } else {
              let num = parseInt(part, 10); // 将单个数字转换为整数
              if (num === 0) {
                  continue;
              }
              data.push(num);
          }
      }
      return data;
  }

  // 定义一个可以接收参数的验证函数
  const numberRoutineValidator = (index: any, childIndex: any) => {
    return ({}) => ({
      validator: (rule: any, value: any, callback: any) => {
        const reg = /^(\d+|(\d+~\d+))(,(\d+|(\d+~\d+)))*$/;
        if (value && !reg.test(value)) {
          return Promise.reject(formatMessage({ id: "input.error.common" }));
        }
        if(value && customRange(value)){
          return Promise.reject(formatMessage({ id: "input.error.common" }));
        }
        if(form.getFieldValue("routineVisitMappingList") !== null && form.getFieldValue("routineVisitMappingList") !== undefined && form.getFieldValue("routineVisitMappingList").length > 0){
          if(form.getFieldValue("routineVisitMappingList")[index] !== null && form.getFieldValue("routineVisitMappingList")[index] !== undefined){
            if(form.getFieldValue("routineVisitMappingList")[index].drugList !== null && form.getFieldValue("routineVisitMappingList")[index].drugList !== undefined && form.getFieldValue("routineVisitMappingList")[index].drugList.length > 0){
              if(form.getFieldValue("routineVisitMappingList")[index].drugList[childIndex] !== null && form.getFieldValue("routineVisitMappingList")[index].drugList[childIndex] !== undefined){
                if(form.getFieldValue("routineVisitMappingList")[index].drugList[childIndex].drugName !== null && form.getFieldValue("routineVisitMappingList")[index].drugList[childIndex].drugName !== undefined){
                  let drugName = form.getFieldValue("routineVisitMappingList")[index].drugList[childIndex].drugName;

                  for (let k = 0; k < form.getFieldValue("values").length; k++) {
                    if(form.getFieldValue("values")[k].drugName !== null && form.getFieldValue("values")[k].drugName !== undefined){
                      if(form.getFieldValue("values")[k].drugName === drugName && form.getFieldValue("values")[k].customDispensingNumber !== null && form.getFieldValue("values")[k].customDispensingNumber !== undefined &&
                          value !== null && value !== undefined){
                        let upper = customDispensingNumberToSelectOption(form.getFieldValue("values")[k].customDispensingNumber);
                        let lower = customDispensingNumberToSelectOption(value);
                        // console.log("upper==" + JSON.stringify(upper));
                        // console.log("lower==" + JSON.stringify(lower));
                        // let b = isSubset(lower, upper);
                        // if(!b){
                        //   return Promise.reject(formatMessage({ id: "input.error.common" }));
                        // }
                        // console.log("kkk===" + upper);
                        // console.log("www===" + lower);
                        if(upper.length === 1 && lower.length > 1 && labelTipShow){
                          return Promise.reject(formatMessage({ id: "input.error.common" }));
                        }
                        if(upper.length === 1){
                          let maxUpper = Math.max(...upper);
                          let maxLower = Math.max(...lower);
                          if(maxUpper < maxLower){
                            return Promise.reject(formatMessage({ id: "input.error.common" }));
                          }
                        }else {
                          let b = isSubset(lower, upper);
                          if(!b){
                            return Promise.reject(formatMessage({ id: "input.error.common" }));
                          }
                        }
                      }
                    }
                  }
  
                }
              }
            }
          }
        }
        return Promise.resolve();
      },
    });
  };

  function isSubset(subset: any, superset: any) {
    return subset.every((item: any) => superset.includes(item));
  }

  // const numberRoutineValidator(index: any, childIndex: any) => {
  //   validator: (rule: any, value: any, callback: any) => {
  //     const reg = /^(\d+|(\d+~\d+))(,(\d+|(\d+~\d+)))*$/;
  //     if (value && !reg.test(value)) {
  //       return Promise.reject(formatMessage({ id: "input.error.common" }));
  //     }
  //     if(customRange(value)){
  //       return Promise.reject(formatMessage({ id: "input.error.common" }));
  //     }
  //     return Promise.resolve();
  //   },
  // };

  const initRoutine = (configure: any) => {
    let data: any = form.getFieldsValue();
    if (configure) {
      data = configure;
    }
    data.routineVisitMappingList?.forEach((value: any, index: number) => {
      if (
        !value?.drugList ||
        (value?.drugList?.length === 0)
      ) {
        data.routineVisitMappingList[index] = {
          ...data.routineVisitMappingList[index],
          visitList: [],
          drugList:[{}]
        };
      }
    });
    form.setFieldsValue({ ...data });
    drug.setConfigValues({ ...data.values });
  };

  const selectVisitName = (value: any) => {
    let visitNameValue = form.getFieldValue("visitCycles");

    if(outSideId !== null && value === outSideId) {
      setVisitNames(Array(1).fill(outSideId));
      form.setFieldsValue({
        ...form.getFieldsValue,
        visitCycles: Array(1).fill(outSideId),
      });
    } else {
      if(visitNameValue !== null && visitNameValue !== undefined && visitNameValue.length > 0 && outSideId !== null){
        if(visitNameValue.indexOf(outSideId) !== -1) {
          const newArray = visitNameValue.filter((item: any) => item !== outSideId);
          setVisitNames(newArray);
          form.setFieldsValue({
            ...form.getFieldsValue,
            visitCycles: newArray,
          });
        } else {
          setVisitNames(visitNameValue);
          form.setFieldsValue({
            ...form.getFieldsValue,
            visitCycles: visitNameValue,
          });
        }
      } 
    }
  };

  // const changekk = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
  //   console.log('Change:', e.target.value);
  // };

  const changeVisitName = (value: any) => {
    setVisitNames(value);
    form.setFieldsValue({
      ...form.getFieldsValue,
      visitCycles: value,
    });
    if(openSetting === 3 && selectFormula &&value === outSideId){
      setNameValidate({ validateStatus: "error", help: formatMessage({id: "drug.configure.visitCycles.name.err"}) });
    } else {
      setNameValidate({});
    }
    if(form.getFieldValue("group") !== null && form.getFieldValue("group") !== undefined){
      let visitCycle = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined) && item.option?.find((it: any) => it === form.getFieldValue("group"))
      ); 
      setRoutineVisit(visitCycle);
    } else {
      let visitCycle = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined)
      );
      setRoutineVisit(visitCycle);
    }
  };

  const changePrecision = (value: any, index: any) => {
    value = value.target.checked;
    if (drug.configValues.length < index + 1) {
      drug.configValues.push([{ calculationInfo: { keepDecimal: value } }]);
    } else {
      drug.configValues[index].calculationInfo.keepDecimal = value;
    }
    if (!value) {
      let data: any = form.getFieldsValue();
      data.values?.forEach((value: any, i: number) => {
        if (i === index) {
          data.values[i].calculationInfo = {
            ...data.values[i].calculationInfo,
            precision: null,
          };
        }
      });
      form.setFieldsValue({ ...data });
    }
  };
  const changeAutomaticRecode = (value: any, index: any) => {
    value = value.target.checked;
    if (drug.configValues.length < index + 1) {
      drug.configValues.push([{ automaticRecode: value }]);
    } else {
      drug.configValues[index].automaticRecode = value
    }
    drug.configValues[index].automaticRecode = value
    if (!value) {
      let data: any = form.getFieldsValue();
      data.values[index].automaticRecodeSpec = null;
      form.setFieldsValue({ ...data });
    }
  };

  const handleMouseEnter = () => {
    setIconColor("#165DFF");
  };

  const handleMouseLeave = () => {
    setIconColor("#999999");
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (value !== inputValue) {
      setCodeValidate({});
    }
    setInputValue(value);
  };

  const changeFormula = (e: any) => {
    let visitCycle: any = []
    if(form.getFieldValue("group") !== null && form.getFieldValue("group") !== undefined){
      visitCycle = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined) && item.option?.find((it: any) => it === form.getFieldValue("group"))
      ); 
      setRoutineVisit(visitCycle);
    } else {
      visitCycle = visitCycles.filter((item: any) =>
        (item?.out !== true || item?.out === null || item?.out === undefined)
      );
      setRoutineVisit(visitCycle);
    }
    if (form.getFieldValue("isFormula") === undefined || (form.getFieldValue("isFormula") !== null && form.getFieldValue("isFormula") !== undefined && form.getFieldValue("isFormula") === false)) {
      getVisitSettingsRun({
          customerId,
          projectId,
          envId,
          cohortId: cohortId,
          roleId: roleId,
      }).then((result: any) => {
          if(result.data.isOpen){
            let out = {
              label: g.lang === "en"?result.data.nameEn:result.data.nameZh,
              option:[],
              value:result.data.id,
              out: true,
            }
            visitCycle.push(out);
          }
          setVisitCycles(visitCycle);
          setOutSideId(result.data.id);
      });
    } else {
      setVisitCycles(visitCycle);
    }
    setSelectFormula(e);
    if(e){
      if(form.getFieldValue("visitCycles") !== undefined && form.getFieldValue("visitCycles") !== null && form.getFieldValue("visitCycles").length === 1){
        if(form.getFieldValue("visitCycles")[0] === outSideId){
          setNameValidate({ validateStatus: "error", help: formatMessage({id: "drug.configure.visitCycles.name.err"}) });
        }
      }
    } else {
      setNameValidate({});
    }
    if (e) {
      form.setFieldsValue({
        ...form.getFieldsValue,
        calculationType: 5,
      });
    }
  };

  const changeOpen = (e: any, index: number) => {
    if (e.target.checked) {
      let value: any = form.getFieldsValue().values;
      value[index].isOpen = true
      let data: any = { ...form.getFieldsValue(), "values": value }
      form.setFieldsValue({ ...data });
    }
  };

  const closeOther = (e: any, index: number) => {
    if (!e.target.checked) {
      let value: any = form.getFieldsValue().values;
      value[index].isOther = false
      let data: any = { ...form.getFieldsValue(), "values": value }
      form.setFieldsValue({ ...data });
    }
  };

  const changeLabelTip = () => {
    let label: any = form.getFieldsValue().label
    if (label !== "") {
      setLabelTipShow(true)
    } else {
      setLabelTipShow(false)
    }
  };
  const changeItemLabelTip = () => {
    let values: any = form.getFieldsValue().values
    if (values?.find((item: any) => item.label && item.label !== "")) {
      setLabelItemTipShow(true)
    } else {
      setLabelItemTipShow(false)
    }
  };


  const childAddInit = (add:any, name:any, index: any) => {
      add();
      rvm(index);
      dug(index);
      initFormulas(null);
      // delete subjectCtx.drugSelect[name+""+childField]
  }


    const validator:any = {
        validator: (rule:any, value:any, callback:any) => {
            if (!value){
                return Promise.resolve();
            }
            if (value && value >= 0) {
                return Promise.resolve();
            }
            return Promise.reject(formatMessage({id: 'common.required.positiveInteger.zero'}));
        }
    }

  const removeChildInit = (name:any, childName:any, remove:any, index: any) => {
      rvm(index);
      dug(index);
      remove(childName);
      // setNumberSpec()
      // props.form.validateFields(["drugLabel"]).then()
  }


  return (
    <React.Fragment>
      <Modal
        title={
          <Row>
              <FormattedMessage id={!configureId ? "common.add" : "common.edit"} />
              {
                  configureId?
                  <Tooltip
                      trigger={["hover", "click"]}
                      overlayInnerStyle={{ fontSize: 12, background: "#575758", width: "320px"  }}
                      placement="bottom"
                      title={intl.formatMessage({ id: "form.onlyID" })+ ":  " + onlyId}
                  >
                      <svg className="iconfont" width={20} height={20} style={{ marginLeft: "8px", marginTop: "4px" }}>
                          <use xlinkHref="#icon-biaodanbianhao" fill="#adb2ba"></use>
                      </svg>
                  </Tooltip>:null
              }
          </Row>
      }
        open={visible}
        onCancel={hide}
        maskClosable={false}
        centered
        destroyOnClose
        className="custom-small-modal-new"
        style={{
          marginLeft: "-20px",
        }}
        footer={
          <Row
            justify={"space-between"}
          >
            <Col>
              {
                labelItemTipShow && !(openSetting === 2 && labelTipShow) &&
                <Row style={{
                  maxWidth: 400,
                  // marginLeft: g.lang === "zh" ? 125 : 175, marginBottom: 12,
                  borderRadius: '2px 2px 2px 2px',
                  backgroundColor: "#fff7e6",
                  padding: '8px',
                  height: "auto",
                  // alignItems: 'center',
                  textAlign: "left",
                  opacity: "0.1px",

                  color: "#677283",
                }}>
                  {
                    <Col>
                      <svg className="iconfont" width={16} height={16} style={{ marginBottom: "-3px", marginRight: 6, marginLeft: 4 }}>
                        <use xlinkHref="#icon-jinggao"></use>
                      </svg>
                      <span
                      // style={{textAlign:"left",}}
                      >
                        {
                          openSetting === 2 ?
                            !labelTipShow && formatMessage({ id: "drug.configure.ip.tip" })
                            :
                            formatMessage({ id: "drug.configure.sub-labels.tip" })
                        }
                      </span>

                    </Col>
                  }
                </Row>
              }
            </Col>
            <Col>
              <Button onClick={hide}>{formatMessage({ id: "common.cancel" })}</Button>
              <Button disabled={configureId?send:false} type={"primary"} onClick={save}>{formatMessage({ id: "common.ok" })}</Button>
            </Col>
          </Row>
        }
      >
        <StyleFrom
          labelWrap
          form={form}
          onValuesChange={formChange} 
          layout="horizontal"
          {...formItemLayout}
          className="custom-modal-body"
        >
          <Form.Item
            label={formatMessage({ id: "drug.configure.group" })}
            name="group"
            rules={[{ required: true }]}
          >
            <Select
              onChange={(value: any, option: any) => {
                onselectGroup(value, null, null, option);
              }}
              placeholder={formatMessage({
                id: "placeholder.select.common",
              })}
              className="full-width"
              options={groups}
            ></Select>
          </Form.Item>
          <Form.Item
            style={{ marginBottom: 0 }}
            label={
              <>
                {formatMessage({
                  id: "logistics.dispensing.method",
                })}
                <Tooltip
                  placement="top"
                  overlayStyle={{ maxWidth: "500px" }}
                  title={
                    <>
                      <Row>
                        {formatMessage({
                          id: "drug.configure.open.tip.one",
                        })}
                      </Row>
                      <Row>
                        {formatMessage({
                          id: "drug.configure.open.tip.two",
                        })}
                      </Row>
                      <Row>
                        {formatMessage({
                          id: "drug.configure.open.tip.three",
                        })}
                      </Row>
                      <Row>
                        {formatMessage({
                          id: "drug.configure.open.tip.four",
                        })}
                      </Row>
                      <Row>e.g:</Row>
                      <Row>
                        {formatMessage({
                          id: "drug.configure.open.tip.six",
                        })}
                      </Row>
                      <Row>
                        {formatMessage({
                          id: "drug.configure.open.tip.seven",
                        })}
                      </Row>
                    </>
                  }
                >
                  <QuestionCircleFilled
                    style={{
                      color: "#ADB2BA",
                      marginLeft: "8px",
                    }}
                  />
                </Tooltip>
              </>
            }
          >
            <Form.Item
              name="openSetting"
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: "drug.batch.treatmentDesign.openSetting",
                  }),
                },
              ]}
            >
              <Select
                placeholder={formatMessage({
                  id: "placeholder.select.common",
                })}
                onChange={selectOpen}
              >
                <Select.Option value={1}>
                  {formatMessage({
                    id: "drug.configure.label.config",
                  })}
                </Select.Option>
                <Select.Option value={2}>
                  {formatMessage({
                    id: "drug.configure.open.config",
                  })}
                </Select.Option>
                <Select.Option value={3}>
                  {formatMessage({
                    id: "drug.configure.formula.config",
                  })}
                </Select.Option>
              </Select>
            </Form.Item>
          </Form.Item>
          {(openSetting === 1 || openSetting === 2) && (
            <Form.Item
              label={formatMessage({ id: "drug.configure.formula.config" })}
              valuePropName="checked"
              name={"isFormula"}
            >
              <Switch size={"small"} onChange={changeFormula}></Switch>
            </Form.Item>
          )}
          {openSetting === 3 && (
            <Form.Item
              label={
                <>
                  {formatMessage({
                    id: "drug.configure.formula",
                  })}
                  <Tooltip
                    placement="top"
                    overlayStyle={{ maxWidth: "300px" }}
                    title={
                      <>
                        <Row>
                          {formatMessage({
                            id: "drug.configure.formula.tip.one",
                          })}
                        </Row>
                        <Row>
                          {formatMessage({
                            id: "drug.configure.formula.tip.two",
                          })}
                        </Row>
                        <Row>
                          {formatMessage({
                            id: "drug.configure.formula.tip.three",
                          })}
                        </Row>
                      </>
                    }
                  >
                    <QuestionCircleFilled
                      style={{
                        color: "#ADB2BA",
                        marginLeft: "8px",
                      }}
                    />
                  </Tooltip>
                </>
              }
              name="calculationType"
              rules={[
                {
                  required: true,
                  message: formatMessage({
                    id: "drug.batch.treatmentDesign.formula",
                  }),
                },
              ]}
            >
              <Select
                placeholder={formatMessage({
                  id: "placeholder.select.common",
                })}
                onChange={changeSetFormulaType}
              >
                {openSetting === 3 && (
                  <>
                    <Select.Option value={1}>
                      {formatMessage({
                        id: "drug.configure.formula.age",
                      })}
                    </Select.Option>
                    <Select.Option value={2}>
                      {formatMessage({
                        id: "drug.configure.formula.weight",
                      })}
                    </Select.Option>
                    <Select.Option value={3}>
                      {formatMessage({
                        id: "drug.configure.formula.bsa",
                      })}
                    </Select.Option>
                    <Select.Option value={4}>
                      {formatMessage({
                        id: "drug.configure.formula.other",
                      })}
                    </Select.Option>
                  </>
                )}

                {selectFormula && (
                  <Select.Option value={5}>
                    {formatMessage({
                      id: "drug.batch.treatmentDesign.treatment.design.custom.formula",
                    })}
                  </Select.Option>
                )}
              </Select>
            </Form.Item>
          )}
          {((openSetting === 3 && drug.formulaType === 4) || selectFormula) && (
            <Form.Item
              style={{ marginBottom: 0 }}
              label={formatMessage({
                id: "drug.batch.treatmentDesign.treatment.design.custom.formula",
              })}
            >
              <Row gutter={12}>
                <Col style={{flex:2}}>
                  <Form.Item
                    {...codeValidate}
                    name="customerCalculation"
                    rules={[
                      {
                        required: true,
                        message: formatMessage({
                          id: "common.required.prefix",
                        }),
                      },
                    ]}
                  >
                    <Input
                      placeholder={formatMessage({
                        id: "common.required.prefix",
                      })}
                      value={inputValue}
                      onChange={handleChange}
                      className={
                        iconColor === "#999999" && !open ? "" : "custom-input"
                      }
                      addonAfter={
                        <div
                          onMouseEnter={handleMouseEnter}
                          onMouseLeave={handleMouseLeave}
                          style={{
                            width: "100%",
                            height: "100%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            cursor: "pointer",
                          }}
                        >
                          <Tooltip
                            title={formatMessage({
                              id: "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions",
                            })}
                          >
                            <i
                              style={{
                                cursor: "pointer",
                              }}
                              onMouseEnter={handleMouseEnter}
                              onMouseLeave={handleMouseLeave}
                              onClick={() => {
                                setIconColor("#165DFF");
                                setOpen(true);
                              }}
                            >
                              <svg
                                className="iconfont"
                                width={16}
                                height={16}
                                style={{ marginBottom: "-3px" }}
                                fill={"#165DFF"}
                              >
                                <use xlinkHref="#icon-zidingyigongshitianxieshuoming"></use>
                              </svg>
                            </i>
                          </Tooltip>
                        </div>
                      }
                    />
                    {/* </Tooltip> */}
                  </Form.Item>
                </Col>

                {selectFormula && (
                  <Col className={"full-width"} style={{flex:1}}>
                    <Form.Item name={"customerCalculationSpec"}>
                      <Input
                        placeholder={formatMessage({
                          id: "drug.configure.spec",
                        })}
                        className={"full-width"}
                      />
                    </Form.Item>
                  </Col>
                )}
                  <Col style={{flex:1}}>
                      <Form.Item name={"keepDecimal"}
                        rules={[validator]}
                      >
                          <InputNumber
                              precision={0}
                              placeholder={formatMessage({
                                  id: "randomization.config.factor.calc.formula.keep.decimal.places",
                              })}
                              className={"full-width"}
                          />
                      </Form.Item>
                  </Col>
              </Row>
            </Form.Item>
          )}
          <Form.Item
            label={formatMessage({ id: "drug.configure" })}
            name="values"
            rules={[{ required: true }]}
          >
            <Form.List name="values">
              {(fields: any, { add, remove }) => (
                <>
                  {fields.map((field: any, index: any) => (
                    <CustomCol key={field.key}>
                      <Row>
                        <Space
                          key={field.key}
                          style={{ display: "flex" }}
                          align="baseline"
                        >
                          <Form.Item
                            {...field}
                            name={[field.name, "drugName"]}
                            fieldKey={[field.fieldKey, "drugName"]}
                            rules={[
                              {
                                required: true,
                                message: formatMessage({
                                  id: "drug.configure.drugName",
                                }),
                              },
                            ]}
                          >
                            {g.lang === "zh" ? (
                              <TipInput
                                trigger={["hover"]}
                                ellipsis
                                inputClassName={
                                  openSetting === 3 &&
                                    (drug.formulaType === 1 ||
                                      drug.formulaType === 2)
                                    ? "custom-tip-full-input"
                                    : "full-width"
                                }
                                placeholder={formatMessage({
                                  id: "drug.configure.drugName",
                                })}
                                disabled={drug.configValues[field.name]?.isCopyEditDelete}
                                onChange={initFormulas}
                                title
                              />
                            ) : (
                              <TipInput
                                trigger={["hover"]}
                                ellipsis
                                inputClassName={
                                  openSetting === 3 &&
                                    (drug.formulaType === 1 ||
                                      drug.formulaType === 2)
                                    ? "custom-tip-full-input-en"
                                    : "full-width"
                                }
                                placeholder={formatMessage({
                                  id: "drug.configure.drugName",
                                })}
                                disabled={drug.configValues[field.name]?.isCopyEditDelete}
                                onChange={initFormulas}
                                title
                              />
                            )}
                          </Form.Item>
                          <InputNumberRow>
                            {openSetting !== 3 && (
                              <Form.Item
                                {...field}
                                name={[field.name, "customDispensingNumber"]}
                                fieldKey={[
                                  field.fieldKey,
                                  "customDispensingNumber",
                                ]}
                                rules={[
                                  {
                                    required: true,
                                    message: formatMessage({
                                      id: "drug.configure.drugNumber",
                                    }),
                                  },
                                  numberValidator,
                                ]}
                              >
                                <TipInput
                                  style={{
                                    width: 88,
                                  }}
                                  trigger={["hover"]}
                                  ellipsis
                                  inputClassName="full-width"
                                  placeholder={formatMessage({
                                    id: "drug.configure.drugNumber",
                                  })}
                                  onChange={initFormulas}
                                  title
                                />
                              </Form.Item>
                            )}
                          </InputNumberRow>

                          {openSetting === 3 &&
                            (drug.formulaType === 3 ||
                              drug.formulaType === 4) && (
                              <div
                                style={{
                                  display: "flex",
                                  gap: "0px",
                                  marginLeft: -8,
                                }}
                              >
                                <Form.Item
                                  rules={[
                                    {
                                      required: true,
                                      message: formatMessage({
                                        id: "common.required.prefix",
                                      }),
                                    },
                                  ]}
                                  style={{ marginLeft: 0 }}
                                  name={[
                                    field.name,
                                    "calculationInfo",
                                    "specifications",
                                    "value",
                                  ]}
                                >
                                  <TipInputNumber
                                    tipStyle={{
                                      width: 170,
                                    }}
                                    trigger={["hover"]}
                                    placeholder={formatMessage({
                                      id: "drug.configure.formula.unit.capacity",
                                    })}
                                    title
                                    controls={false}
                                  />
                                </Form.Item>
                                <Form.Item
                                  name={[
                                    field.name,
                                    "calculationInfo",
                                    "specifications",
                                    "unit",
                                  ]}
                                  style={{ marginLeft: 0 }}
                                >
                                  <Select onChange={changeSepc}>
                                    <Select.Option value={"mg"}>
                                      mg
                                    </Select.Option>
                                    <Select.Option value={"ml"}>
                                      ml
                                    </Select.Option>
                                    <Select.Option value={"g"}>g</Select.Option>
                                  </Select>
                                </Form.Item>
                              </div>
                            )}

                          <Form.Item
                            {...field}
                            name={[field.name, "drugSpec"]}
                            fieldKey={[field.fieldKey, "drugSpec"]}
                            style={{
                              marginLeft:
                                openSetting === 3 &&
                                  (drug.formulaType === 3 ||
                                    drug.formulaType === 4)
                                  ? 0
                                  : openSetting === 3 &&
                                    (drug.formulaType === 1 ||
                                      drug.formulaType === 2)
                                    ? -8
                                    : 0,
                            }}
                          >
                            <TipInput
                              trigger={["hover"]}
                              ellipsis
                              className="full-width"
                              placeholder={formatMessage({
                                id: "drug.configure.drugSpecifications",
                              })}
                              title
                            />
                          </Form.Item>
                          {
                            // 阿里对接  包装规格
                            connectAli === 1 ? (
                              <Form.Item
                                {...field}
                                name={[field.name, "pkgSpecs"]}
                                fieldKey={[field.fieldKey, "pkgSpec"]}
                                rules={[
                                  {
                                    required: true,
                                    message: formatMessage({
                                      id: "common.required.prefix",
                                    }),
                                  },
                                ]}
                              >
                                <Input
                                  className="full-width"
                                  placeholder={formatMessage({
                                    id: "drug.configure.PkgSpecifications",
                                  })}
                                />
                              </Form.Item>
                            ) : null
                          }
                        </Space>
                      </Row>
                      {openSetting === 1 && (
                        <Form.Item
                          style={{ marginTop: 12 }}
                          {...field}
                          name={[field.name, "label"]}
                        >
                          <Input onChange={changeItemLabelTip}
                            placeholder={formatMessage({
                              id: "placeholder.input.common",
                            })}
                          ></Input>
                        </Form.Item>
                      )}
                      {openSetting === 3 &&
                        (drug.formulaType === 4 || drug.formulaType === 3) && (
                          <Form.Item
                            rules={[
                              {
                                required: true,
                                message: formatMessage({
                                  id: "common.required.prefix",
                                }),
                              },
                            ]}
                            style={{
                              marginTop: 12,
                              marginBottom: "8px",
                            }}
                            name={[
                              field.name,
                              "calculationInfo",
                              "unitCalculationStandard",
                            ]}
                          >
                            <InputNumber
                              placeholder={formatMessage({
                                id: "drug.configure.formula.standard",
                              })}
                              addonAfter={
                                (drug.configValues[field.name]?.calculationInfo
                                  ?.specifications?.unit
                                  ? drug.configValues[field.name]
                                    ?.calculationInfo?.specifications?.unit
                                  : "mg") + "/㎡"
                              }
                            />
                          </Form.Item>
                        )}
                      {openSetting === 3 &&
                        (drug.formulaType === 1 || drug.formulaType === 2) && (
                          <Formula field={field} form={form} />
                        )}
                      <Row style={{ marginTop: "12px" }}>
                        <Form.Item
                          {...field}
                          name={[field.name, "isOther"]}
                          fieldKey={[field.fieldKey, "isOther"]}
                          valuePropName="checked"
                        >
                          {
                            openSetting !== 1 &&
                            <Checkbox onChange={(e: any) => {
                              changeOpen(e, field.name)
                            }}>
                              {formatMessage({
                                id: "drug.other",
                              })}
                              <Tooltip
                                placement="top"
                                overlayStyle={{
                                  maxWidth: "300px",
                                }}
                                title={formatMessage({
                                  id: "drug.configure.other.tip",
                                })}
                              >
                                <QuestionCircleFilled
                                  style={{
                                    color: "#ADB2BA",
                                    marginLeft: "8px",
                                  }}
                                />
                              </Tooltip>
                            </Checkbox>
                          }

                        </Form.Item>
                        {
                          (openSetting === 1 || openSetting === 2) && selectFormula && <>
                            <Form.Item
                              {...field}
                              name={[field.name, "automaticRecode"]}
                              fieldKey={[field.fieldKey, "automaticRecode"]}
                              valuePropName="checked"
                            >
                              <Checkbox
                                onChange={(value) => {
                                  changeAutomaticRecode(value, field.name);
                                }}
                              >
                                {formatMessage({
                                  id: "drug.batch.treatmentDesign.treatment.design.automatic.recode",
                                })}
                              </Checkbox>
                            </Form.Item>
                            {
                              drug.configValues[field.name]?.automaticRecode &&
                              <Form.Item
                                {...field}
                                name={[
                                  field.name,
                                  "automaticRecodeSpec",
                                ]}
                                fieldKey={[
                                  field.fieldKey,
                                  "automaticRecodeSpec",
                                ]}
                                rules={[
                                  {
                                    required:
                                      drug.configValues[field.name]
                                        ?.automaticRecode,
                                    message: formatMessage({
                                      id: "common.required.prefix",
                                    }),
                                  },
                                ]}
                              >
                                <InputNumber
                                  min={1}
                                  size={"small"}
                                  controls={false}
                                  style={{
                                    width: 100,
                                  }}
                                  placeholder={formatMessage({
                                    id: "drug.configure.formula.unit",
                                  })}
                                ></InputNumber>
                              </Form.Item>
                            }

                          </>
                        }
                        {openSetting === 3 && (
                          <>
                            <Form.Item
                              {...field}
                              name={[field.name, "isOpen"]}
                              fieldKey={[field.fieldKey, "isOpen"]}
                              valuePropName="checked"
                            >
                              <Checkbox
                                onChange={(e: any) => {
                                  closeOther(e, field.name)
                                }}
                              >
                                {formatMessage({
                                  id: "projects.supplyPlan.drugOpen",
                                })}
                              </Checkbox>
                            </Form.Item>
                            {(drug.formulaType === 1 ||
                              drug.formulaType === 2) && (
                                <>
                                  <Form.Item
                                    {...field}
                                    name={[
                                      field.name,
                                      "calculationInfo",
                                      "keepDecimal",
                                    ]}
                                    fieldKey={[
                                      field.fieldKey,
                                      "calculationInfo",
                                      "keepDecimal",
                                    ]}
                                    valuePropName="checked"
                                  >
                                    <Checkbox
                                      onChange={(value) => {
                                        changePrecision(value, field.name);
                                      }}
                                    >
                                      {formatMessage({
                                        id: "drug.batch.treatmentDesign.treatment.design.keep.decimal.places",
                                      })}
                                    </Checkbox>
                                  </Form.Item>
                                  {drug.configValues[field.name]?.calculationInfo
                                    ?.keepDecimal && (
                                      <Form.Item
                                        {...field}
                                        name={[
                                          field.name,
                                          "calculationInfo",
                                          "precision",
                                        ]}
                                        fieldKey={[
                                          field.fieldKey,
                                          "calculationInfo",
                                          "precision",
                                        ]}
                                        rules={[
                                          {
                                            required:
                                              drug.configValues[field.name]
                                                ?.calculationInfo?.keepDecimal,
                                            message: formatMessage({
                                              id: "common.required.prefix",
                                            }),
                                          },
                                        ]}
                                      >
                                        <InputNumber
                                          min={1}
                                          size={"small"}
                                          controls={false}
                                          style={{
                                            width: 40,
                                          }}
                                        ></InputNumber>
                                      </Form.Item>
                                    )}
                                </>
                              )}
                          </>
                        )}
                      </Row>
                      {openSetting === 3 && (
                        <WeightCol field={field} attribute={attribute} />
                      )}
                      {
                        (form.getFieldValue("values")[field.name] !== undefined && form.getFieldValue("values")[field.name] !== null && !form.getFieldValue("values")[field.name]?.isCopyEditDelete)?
                          <CloseCircleFilled
                          className="delete-icon"
                          style={{ color: "#fe5b5a" }}
                          onClick={() => deleteClick(remove, field.name)}
                        />
                        :null
                      }
                    </CustomCol>
                  ))}
                  <Form.Item style={{ marginBottom: 0 }}>
                    <Button
                      type="dashed"
                      onClick={() => addClick(add, fields)}
                      block
                      icon={<PlusOutlined />}
                    >
                      <FormattedMessage id={"common.addTo"} />
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>
          <Form.Item
            label={formatMessage({id: "drug.configure.showAll"})}
            valuePropName="checked"
            name="showAll"
          >
            <Switch size={"small"}></Switch>
          </Form.Item>
          {openSetting !== 3 && (
            <Form.Item
              label={formatMessage({
                id: "drug.configure.drugLabel",
              })}
              name="label"
            >
              <Input
                onChange={changeLabelTip}
                placeholder={formatMessage({
                  id: "placeholder.input.common",
                })}
                allowClear
                className="full-width"
              />
            </Form.Item>

          )}
          {
            labelTipShow &&
            <Form.Item label={" "} colon={false}>
              {
                labelTipShow &&
                <Row style={{
                  // marginLeft: g.lang === "zh" ? 125 : 175, marginBottom: 12,
                  borderRadius: '2px 2px 2px 2px',
                  backgroundColor: "#fff7e6",
                  padding: '8px',
                  height: "auto",
                  alignItems: 'center',
                  opacity: "0.1px",
                  color: "#677283",
                }}>
                  {
                    <Col>
                      <svg className="iconfont" width={16} height={16} style={{ marginBottom: "-2px", marginRight: 6, marginLeft: 4 }}>
                        <use xlinkHref="#icon-jinggao"></use>
                      </svg>
                      {
                        formatMessage({ id: "drug.configure.labels.tip" })
                      }
                    </Col>
                  }
                </Row>
              }
            </Form.Item>
          }

          <Form.Item
            label={formatMessage({
              id: "drug.configure.visitName",
            })}
            name="visitCycles"
            {...nameValidate}
            rules={[{ required: true }]}
          >
            <Select
              showArrow
              placeholder={formatMessage({
                id: "placeholder.select.common",
              })}
              mode="multiple"
              className="full-width"
              options={visitCycles}
              onChange={(value: any) => {
                changeVisitName(value);
              }}
              onSelect={(value, option)=> selectVisitName(value)}
            ></Select>
          </Form.Item>

          {
            (
              visitNames !== null && 
              visitNames !== undefined &&
              visitNames.length > 0 &&
              outSideId !== null && 
              visitNames.indexOf(outSideId) !== -1 &&
              openSetting !== 3 &&
              !selectFormula

            ) &&

            
            <Form.Item
              label={formatMessage({ id: "drug.configure.routine.visit.mapping" })}
              name="routineVisitMappingList"
              rules={[{ required: true }]}
            >
              <Form.List name="routineVisitMappingList">
                {(fields: any, { add, remove }) => (
                  <>
                    {fields.map((field: any, index: any) => (
                      <CustomCol key={field.key}>
                          <Form.Item
                            {...field}
                            name={[field.name, "visitList"]}
                            fieldKey={[field.fieldKey, "visitList"]}
                            // rules={[{ required: true }]}
                            rules={[
                              {
                                required: true,
                                message: formatMessage({
                                  id: "drug.configure.visitName",
                                }),
                              },
                            ]}
                          >
                            <Select
                              showArrow
                              placeholder={formatMessage({
                                id: "placeholder.select.common",
                              })}
                              mode="multiple"
                              className="full-width"
                              onChange={(value: any) => {
                                onChangeRoutineVisit(index);
                              }}
                              allowClear
                            >
                              {
                                  (routineVisitMap?.get(index) || []).map(
                                      (it:any) =>
                                          <Select.Option 
                                              key={it.value} 
                                              value={it.value}
                                              disabled={it.disable}
                                          >
                                              {it.label}
                                          </Select.Option>
                                  )
                              }
                            </Select>
                          </Form.Item>
                        <Row>
                          <Space
                            key={field.key}
                            style={{ display: "flex" }}
                            align="baseline"
                          >
                            <Form.Item name={[field.name, "drugList"]}
                                       style={{marginTop:12}}
                                       labelCol={{style:{ whiteSpace: "pre-wrap", textAlign:"right", height:"auto"}}}
                            >
                                <Form.List name={[field.name, "drugList"]} >
                                    {(childFields, {add:childAdd, remove:childRemove}, {errors}) => (
                                        <>
                                            {childFields.map((childField:any, childIndex) => (
                                                <Row key={childField.key}
                                                     style={{marginBottom:12}}
                                                >
                                                  <Form.Item
                                                    {...childField}
                                                    {...medicineValidate?.get(index + "-" + childIndex)}
                                                    name={[childField.name, "drugName"]}
                                                    fieldKey={[childField.fieldKey, "drugName"]}
                                                    rules={[
                                                      {
                                                        required: true,
                                                        message: formatMessage({
                                                          id: "drug.configure.drugName",
                                                        }),
                                                      },
                                                    ]}
                                                    style={{width: 150,}}
                                                  >
                                                    <Select
                                                      showArrow
                                                      placeholder={formatMessage({
                                                        id: "placeholder.select.common",
                                                      })}
                                                      className="full-width"
                                                      onChange={(value: any) => {
                                                        dug(index);
                                                        rvm(index);
                                                      }}
                                                      allowClear
                                                    >
                                                      {
                                                          (drugMap?.get(index) || []).map(
                                                              (it:any) =>
                                                                  <Select.Option 
                                                                      key={it.value} 
                                                                      value={it.value}
                                                                      disabled={it.disable || drug.configValues[field.name]?.isCopyEditDelete}
                                                                  >
                                                                      {it.value}
                                                                  </Select.Option>
                                                          )
                                                      }
                                                    </Select>
                                                    {/* <TipInput
                                                      trigger={["hover"]}
                                                      ellipsis
                                                      inputClassName={"full-width"}
                                                      placeholder={formatMessage({
                                                        id: "drug.configure.drugName",
                                                      })}
                                                      // disabled={drug.configValues[field.name]?.isCopyEditDelete}
                                                      title
                                                    /> */}
                                                  </Form.Item>
                                                  <InputNumberRow style={{marginLeft:8, width: 150,}}>
                                                    {(
                                                      <Form.Item
                                                        {...childField}
                                                        name={[childField.name, "customDispensingNumber"]}
                                                        fieldKey={[
                                                          childField.fieldKey,
                                                          "customDispensingNumber",
                                                        ]}
                                                        rules={[
                                                          {
                                                            required: true,
                                                            message: formatMessage({
                                                              id: "drug.configure.drugNumber",
                                                            }),
                                                          },
                                                          numberRoutineValidator(index, childIndex),
                                                        ]}
                                                      >
                                                        <TipInput
                                                          trigger={["hover"]}
                                                          ellipsis
                                                          inputClassName="full-width"
                                                          placeholder={formatMessage({
                                                            id: "drug.configure.drugNumber",
                                                          })}
                                                          title
                                                          disabled={drug.configValues[field.name]?.isCopyEditDelete}
                                                        />
                                                      </Form.Item>
                                                    )}
                                                  </InputNumberRow>
                                                  <Form.ErrorList errors={errors}/>
                                                  {
                                                      <Tooltip placement="top" title={formatMessage({id: "common.add"})}>
                                                          <svg className="iconfont" width={16} height={16} onClick={() => {
                                                              childAddInit(childAdd, field.name, index)
                                                          }} style={{marginLeft: 16, marginTop: 10, cursor: "pointer"}} fill={"#999999"}>
                                                              <use xlinkHref="#icon-zengjia"></use>
                                                          </svg>
                                                      </Tooltip>
                                                  }
                                                  {
                                                      childFields.length !== 1 &&
                                                      <Tooltip placement="top" title={formatMessage({id: "common.delete"})}>
                                                          <svg className="iconfont" width={16} height={16} onClick={() => {
                                                              removeChildInit(field.name,childField.name, childRemove, index)
                                                          }} style={{marginLeft: 12, marginTop: 10, cursor: "pointer"}} fill={"#999999"}>
                                                              <use xlinkHref="#icon-shanchu"></use>
                                                          </svg>
                                                      </Tooltip>
                                                  }
                                                </Row>
                                            ))}
                                        </>
                                    )}
                                </Form.List>
                            </Form.Item>
                      
                          </Space>
                        </Row>
                        {
                          // (drug.configValues[field.name] !== undefined && !drug.configValues[field.name].isCopyEditDelete)?
                            <CloseCircleFilled
                            className="delete-icon"
                            style={{ color: "#fe5b5a" }}
                            onClick={() => deleteRoutineClick(remove, field.name, index)}
                          />
                          // :null
                        }
                      </CustomCol>
                    ))}
                    <Form.Item style={{ marginBottom: 0 }}>
                      <Button
                        type="dashed"
                        onClick={() => addRoutineClick(add, fields)}
                        block
                        icon={<PlusOutlined />}
                      >
                        <FormattedMessage id={"common.addTo"} />
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Form.Item>
          }



          {!room && (
            <Form.Item
              label={formatMessage({
                id: "drug.configure.roomNumber",
              })}
              name="roomNumbers"
            >
              <Select
                showArrow
                placeholder={formatMessage({
                  id: "placeholder.select.common",
                })}
                mode="tags"
                tokenSeparators={[","]}
                value={rooms}
                onChange={(v) => setRooms(v)}
                className="full-width"
              ></Select>
            </Form.Item>
          )}
        </StyleFrom>

        {open && (
          <Draggable>
            <DragWrap
              style={{
                top: position.y,
                left: position.x,
              }}
            >
              <RowHeader justify="space-between" align="middle">
                <Col style={{ fontWeight: 500, lineHeight: "24px" }}>
                  {formatMessage({
                    id: "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions",
                  })}
                </Col>
                <CloseOutlined
                  style={{ cursor: "pointer" }}
                  onClick={() => setOpen(false)}
                />
              </RowHeader>
              <div style={{ padding: "12px 12px 16px 12px" }}>
                <Row
                  style={{
                    color: "#677283",
                    fontSize: "10px",
                    marginBottom: 8,
                  }}
                >
                  {formatMessage({
                    id: "drug.batch.treatmentDesign.treatment.design.custom.formula.title",
                  })}
                </Row>

                <MyTable dataSource={FormulaData} pagination={false}>
                  <Table.Column
                    title={formatMessage({
                      id: "drug.batch.treatmentDesign.treatment.design.custom.formula.symbol",
                    })}
                    dataIndex={"symbol"}
                    key="symbol"
                  />
                  <Table.Column
                    title={formatMessage({
                      id: "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation",
                    })}
                    dataIndex={"illustrate"}
                    key="illustrate"
                  />
                  <Table.Column
                    title={formatMessage({
                      id: "drug.batch.treatmentDesign.treatment.design.custom.formula.eg",
                    })}
                    dataIndex={"eg"}
                    key="eg"
                  />
                </MyTable>
                <Row
                  style={{
                    color: "#677283",
                    fontSize: "10px",
                    marginTop: "10px",
                  }}
                >
                  {formatMessage({
                    id: "drug.batch.treatmentDesign.treatment.design.custom.formula.footer",
                  })}
                </Row>
              </div>
            </DragWrap>
          </Draggable>
        )}
      </Modal>
    </React.Fragment>
  );
};

const InputNumberRow = styled.div`
  .ant-input-number .ant-input-number-handler-wrap,
  .ant-input-number-focused .ant-input-number-handler-wrap {
    opacity: 1;
  }
`;

const CustomCol = styled(Col)`
  position: relative;
  background: #f8f9fa;
  padding: 16px 16px 8px 16px;
  margin-bottom: 12px;

  .ant-form-item {
    margin-bottom: 0;
  }

  .delete-icon {
    position: absolute;
    right: -3px;
    top: -3px;
  }
`;

const StyleFrom = styled(Form)`
  .ant-form-item-label label {
    text-align: right;
  }
`;

const DragWrap = styled.div`
  position: absolute;
  z-index: 1000;
  width: 350px;
  cursor: move;
  background: #fff;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
`;

const RowHeader = styled(Row)`
  border-bottom: 1px solid #e3e4e6;
  padding: 8px 12px;
`;

const MyTable = styled(Table)`
  .ant-table-thead {
    height: 24px;
    line-height: 24px;

    th {
      line-height: 24px !important;
      padding: 0 0 0 10px !important;
      font-size: 10px !important;
    }
  }

  tbody.ant-table-tbody > tr > td {
    line-height: 24px !important;
    font-size: 10px;
    color: #677283;
    padding: 0 0 0 10px !important;
  }
`;
