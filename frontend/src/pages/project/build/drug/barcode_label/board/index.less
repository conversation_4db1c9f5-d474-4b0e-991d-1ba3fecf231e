
.board-container {
  display: flex;
  flex-direction: column;
  // min-height: 420px;


  .drag-component {
    background-color: #F1F4F9;
    padding: 4px 15px;
    border-radius: 2px;
    border: 1px solid rgba(229, 233, 242, 1);
    cursor: pointer;
  }

  .drag-component:hover {
    background-color: #E8EFFF !important;
    border: 1px solid rgba(22, 93, 255, 1);
  }
}

.board-drag {
  // border: 1px solid rgba(235, 237, 240, 1);
  overflow: hidden;
  position: relative;
  padding: 26px 0 1px 26px;
  min-height: 160px;
  overflow-x: auto;

  // 外框尺寸显示样式
  .ruler-container {
    position: absolute;
    background-color: #f8f9fa;
    user-select: none;
    // border: 1px solid rgba(235, 237, 240, 1);

    &.ruler-horizontal-top {
      top: 0;
      left: 25px;
      right: 0;
      height: 24px;
      border-bottom: none;

      .ruler-scale {
        position: relative;
        height: 100%;

        .ruler-mark {
          position: absolute;
          bottom: 0;
          width: 1px;
          background-color: #666;

          .ruler-number {
            position: absolute;
            bottom: 7px;
            left: -14px;
            font-size: 10px;
            color: #666;
            font-family: 'Microsoft YaHei', sans-serif;
          }
        }
      }
    }

    &.ruler-vertical-left {
      top: 25px;
      left: 0;
      bottom: 0;
      width: 24px;
      border-right: none;

      .ruler-scale {
        position: relative;
        width: 100%;
        height: 100%;

        .ruler-mark {
          position: absolute;
          right: 0;
          height: 1px;
          background-color: #666;

          .ruler-number {
            position: absolute;
            right: 7px;
            top: -14px;
            font-size: 10px;
            color: #666;
            font-family: 'Microsoft YaHei', sans-serif;
            transform: rotate(-90deg);
            transform-origin: center;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.board-label {
  .drag-block {
    border: 1px solid #d9d9d9;
    height: 100%;
    user-select: none;
    word-break: keep-all;
    display: flex;
    align-items: center;
  }

  .drag-block-active {
    border: 1px solid rgba(22, 93, 255, 1);
    cursor: move;
    .drag-remove {
    }
  }

  .ant-input {
    border: none !important;
    padding: 0;
    display: flex;
    align-items: start;
    min-height: 0;
    word-break: break-all;
    overflow: hidden;
  }

  .ant-input-disabled {
    background-color: transparent;
    cursor: move;
  }

  .drag-input {
    word-break: break-all;
  }

  .drag-bar-code {
    display: flex;
    width: 100%;
  }

  .drag-ruler {

    .ruler-line {
      position: absolute;
      background-color: #1890ff;
      border-top: 1px dashed #1890ff;
      z-index: 1000;
      pointer-events: none;
      // 添加位置变化的动画过渡效果
      transition: top 0.1s ease-out, left 0.1s ease-out;
    }

    .ruler-label {
      position: absolute;
      font-family: 'Microsoft YaHei', sans-serif;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      background-color: #1890ff;
      color: white;
      padding: 2px 6px;
      border-radius: 2px;
      font-size: 10px;
      white-space: nowrap;
    }

    .ruler-marks {
      div {
        opacity: 0.8;
        background-color: #1890ff;
      }
    }

    // 水平尺样式
    .horizontal-ruler {
      height: 1px;
      left: 0;
      right: 0;
      .ruler-label {
        right: 0;
        top: -10px;
      }
    }

    // 垂直尺样式
    .vertical-ruler {
      width: 1px;
      top: 0;
      bottom: 0;
      .ruler-label {
        /*left: -25px;
        bottom: -25px;*/
      }
    }
  }

  // 距离指示器样式
  .distance-indicator {
    position: absolute;
    background-color: #ff4d4f;
    z-index: 1001;
    pointer-events: none;

    .distance-label {
      position: absolute;
      background-color: #ff4d4f;
      color: white;
      padding: 1px 4px;
      border-radius: 2px;
      font-size: 9px;
      white-space: nowrap;
    }

    .distance-arrow {
      position: absolute;
      width: 0;
      height: 0;
      /*border-left: 2px solid transparent;
      border-right: 2px solid transparent;
      border-bottom: 4px solid #ff4d4f;*/
    }

    &.distance-horizontal {
      border-top: 1px solid #ff4d4f;

      &::before, &::after {
        content: '';
        position: absolute;
        top: -1px;
        width: 1px;
        height: 3px;
        background-color: #ff4d4f;
      }

      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }

      .distance-label {
        left: 50%;
        top: -15px;
        transform: translateX(-50%);
      }
      .distance-arrow-left {
        top: -2px;
        left: 0;
      }
      .distance-arrow-right {
        top: -2px;
        right: 0;
      }
    }

    &.distance-vertical {
      border-left: 1px solid #ff4d4f;

      &::before, &::after {
        content: '';
        position: absolute;
        left: -1px;
        height: 1px;
        width: 3px;
        background-color: #ff4d4f;
      }

      &::before {
        top: 0;
      }

      &::after {
        bottom: 0;
      }

      .distance-label {
        left: 5px;
        top: 50%;
        transform: translateY(-50%);
      }
      .distance-arrow-left {
        top: 0;
        left: -2px;
      }
      .distance-arrow-right {
        bottom: 0;
        left: -2px;
      }
    }
  }
}

.board-operation {
  margin-top: 14px;
  background: #F8F8F8;
  position: relative;
  border: 1px solid rgba(227, 228, 230, 1);

  .field-attribute-zh {
    .ant-form-item-label {
      width: 60px !important;
    }
  }
  .field-attribute {
    padding: 16px 16px 0 16px;

    .ant-form-item-label {
      width: 80px;
    }
    .arco-color-picker {
      padding: 0 0 0 4px;
    }
    .arco-color-picker-preview {
      width: 32px;
      height: 32px;
      border: none;
    }
    .ant-radio-button-wrapper:focus-within {
      box-shadow: none;
    }

    .position-icon {
      cursor: pointer;
    }
    .position-icon-active {
      cursor: pointer;
      color: rgba(29, 33, 41, 1);
    }
  }
}

.drag-color-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(236, 236, 236, 0.6);
  margin: 0 12px;
  padding: 8px 0;

  .drag-color-preset {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
  }

  .drag-color-picker-clear {
    position: relative;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid rgba(5,5,5,0.06);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.1s;
  }
  .drag-color-picker-clear:hover {
    border-color: rgba(5,5,5,0.2);
  }

  .drag-color-picker-clear::after {
    content: "";
    position: absolute;
    top: 1px;
    left: -6px;
    display: block;
    width: 28px;
    height: 2px;
    transform-origin: calc(100% - 1px) 1px;
    transform: rotate(-45deg);
    background-color: #f5222d;
  }
}

.triangle-border {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;  /* 比内层大2px */
  border-right: 10px solid transparent;
  border-bottom: 10px solid #EBEDF0;
  position: absolute;
  top: -10px;
  left: 28px;
}

.triangle-inner {
  position: absolute;
  top: 2px;
  left: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #F8F8F8;
}

.preview-print {

  .drag-block {
    border-color: transparent !important;
  }

  .preview-label {
    page-break-inside: avoid;
    // overflow: auto;
  }
  .board-label {

  }

  .drag-remove-icon {
    visibility: hidden;
  }

  @media print {
    @page {
      size: auto;
      margin: 1cm 0 1cm 0;
      // margin: 1.3cm 0 2.75cm 0; // 打印页样式
    }
  }

}