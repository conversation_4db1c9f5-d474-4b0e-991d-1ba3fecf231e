import React, {useEffect, useState} from "react";
import {InputNumber} from "antd";
import {mm2px, px2mm} from "./util";
import {useIntl} from "react-intl";

export const PaperSizeInput = (props: { size: any; setSize: (args: any) => void }) => {

    const {formatMessage} = useIntl()
    const [size, setSize] = useState({width: null, height: null})


    useEffect(() => {
        setSize(props.size)
    }, [props.size])

    const onSizeChange = (values: any) => {
        setSize(values)
        props.setSize(values)
    }

    return <div style={{width: '320px', display: 'flex', alignItems: 'center'}}>
        <InputNumber
            value={size.width}
            onChange={value => onSizeChange({...size, width: value})}
            addonAfter={'mm'}
            style={{flex: 1}}
            min={1}
            precision={0}
            placeholder={formatMessage({id: 'barcode.label.attribute.width'})}
        />
        <span style={{margin: '0 8px'}}>*</span>
        <InputNumber
            value={size.height}
            onChange={value => onSizeChange({...size, height: value})}
            addonAfter={'mm'}
            style={{flex: 1}}
            min={1}
            precision={0}
            placeholder={formatMessage({id: 'barcode.label.attribute.height'})}
        />
    </div>
}


export const DragRuler = (props: any) => {

    const rightPosition = () => {
        const width = mm2px(props.field?.width || 0)
        const mm = px2mm(props.range.x[1] - props.rulerPosition.x - width)
        return mm < 0 ? 0 : mm
    }

    const topPosition = () => {
        return px2mm(props.rulerPosition.y)
    }


    return <div className={"drag-ruler"}>
        {/* 水平尺 */}
        <div className="ruler-line horizontal-ruler" style={{top: `${props.rulerPosition.y}px`}}>
            {/* 位置标签 */}
            <div className="ruler-label">
                {rightPosition()}mm
            </div>
            {/* 刻度标记 */}
            <div className="ruler-marks">
                {Array.from({length: Math.floor(props.labelSize.width / 5)}, (_, i) => (
                    <div
                        key={i}
                        style={{
                            position: 'absolute',
                            left: `${mm2px(i * 5)}px`,
                            top: '-3px',
                            width: '1px',
                            height: i % 2 === 0 ? '6px' : '3px',
                        }}
                    />
                ))}
            </div>
        </div>
        {/* 垂直尺 */}
        <div className="ruler-line vertical-ruler" style={{left: `${props.rulerPosition.x}px`}}>
            {/* 位置标签 */}
            <div className="ruler-label">
                {topPosition()}mm
            </div>
            {/* 刻度标记 */}
            <div className="ruler-marks">
                {Array.from({length: Math.floor(props.labelSize.height / 5)}, (_, i) => (
                    <div
                        key={i}
                        style={{
                            position: 'absolute',
                            top: `${mm2px(i * 5)}px`,
                            left: '-3px',
                            height: '1px',
                            width: i % 2 === 0 ? '6px' : '3px',
                        }}
                    />
                ))}
            </div>
        </div>
    </div>
}

export const DragRuleStatic = (props: any) => {
    return <div>
        <div
            className="ruler-container ruler-horizontal-top"
            style={{
                display: 'flex', position: 'absolute',
                top: `-1px`, left: `calc(-${mm2px(props.labelSize.width)}px - 1px)`, cursor: 'pointer',
                background: '#1890ff',
                height: '1px',
                width: `${props.labelSize.width * 2}mm`,
            }}
        >
            <div className="ruler-scale">
                {Array.from({length: Math.ceil(props.labelSize.width * 2 / 5) + 1}, (_, i) => (
                    <div
                        key={i}
                        className="ruler-mark"
                        style={{left: `${mm2px(i * 5)}px`, height: i % 2 === 0 ? '6px' : '3px', background: '#1890ff'}}
                    >
                    </div>
                ))}
            </div>
        </div>
        <div
            className="ruler-container ruler-vertical-left"
            style={{
                display: 'flex', position: 'absolute',
                top: `calc(-${mm2px(props.labelSize.height)}px - 1px)`, left: '-1px', cursor: 'pointer',
                background: '#1890ff',
                width: '1px',
                height: `${props.labelSize.height * 2}mm`,
            }}
        >
            <div className="ruler-scale">
                {Array.from({length: Math.ceil(props.labelSize.height * 2 / 5) + 1}, (_, i) => (
                    <div
                        key={i}
                        className="ruler-mark"
                        style={{top: `${mm2px(i * 5)}px`, width: i % 2 === 0 ? '6px' : '3px', background: '#1890ff'}}
                    ></div>
                ))}

            </div>
        </div>
    </div>
}


export const DragComponentDistance = ({distance, index}: any) => {
    return <div key={index}>
        {distance.direction === 'horizontal' && (
            <div
                className="distance-indicator distance-horizontal"
                style={{
                    left: `${distance.fromX}px`,
                    top: `${distance.y}px`,
                    width: `${distance.toX - distance.fromX}px`,
                    height: '1px',
                }}
            >
                {/* 距离标签 */}
                <div className={'distance-label'}>
                    {distance.distance.toFixed(1)}mm
                </div>
                {/* 箭头 */}
                <div className={'distance-arrow distance-arrow-left'}/>
                <div className={'distance-arrow distance-arrow-right'}/>
            </div>
        )}
        {distance.direction === 'vertical' && (
            <div
                className="distance-indicator distance-vertical"
                style={{
                    left: `${distance.x}px`,
                    top: `${distance.fromY}px`,
                    width: '1px',
                    height: `${distance.toY - distance.fromY}px`,
                }}
            >
                {/* 距离标签 */}
                <div className={'distance-label'}>
                    {distance.distance.toFixed(1)}mm
                </div>
                {/* 箭头 */}
                <div className={'distance-arrow distance-arrow-left'}/>
                <div className={'distance-arrow distance-arrow-right'}/>
            </div>
        )}
    </div>
}