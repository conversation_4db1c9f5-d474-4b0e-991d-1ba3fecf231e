interface coordinate {
    allowRange: [number, number], // 允许落点的范围
    positionStart: number, // 落点位置
    length: number // 组件长度
}

// 落点在区域内的长度
const innerAreaLength = (point: coordinate) => {
    const {allowRange, positionStart, length} = point
    const positionEnd = positionStart + length
    // 标签左外落点
    if (positionEnd <= allowRange[0]) return 0
    // 左边部分在标签内
    if (positionStart < allowRange[0]) return positionEnd - allowRange[0]
    // 完全在标签内
    if (positionEnd <= allowRange[1]) return length
    // 右边部分在标签内
    if (positionStart < allowRange[1]) return allowRange[1] - positionStart
    // 标签右外落点
    return 0
}

// 允许使用的位置
const allowPosition = (point: coordinate) => {
    const {allowRange, positionStart, length} = point
    const positionEnd = positionStart + length
    // 标签左外落点
    if (positionStart < allowRange[0]) return 0
    // 标签超过右边距
    if (positionEnd > allowRange[1]) return allowRange[1] - length - allowRange[0]
    // 组件完全在标签内，直接使用原坐标
    return positionStart - allowRange[0]
}

// 落点是否在区域内
export const innerArea = (x: coordinate, y: coordinate, ratio: number) => {
    const innerArea = innerAreaLength(x) * innerAreaLength(y)
    const componentArea = x.length * y.length
    return innerArea / componentArea >= ratio
}

// 添加的组件坐标
export const componentPosition = (x: coordinate, y: coordinate) => {
    return [allowPosition(x), allowPosition(y)]
}

export const px2mm = (px: number) => {
    return Math.round(px * 25.4 / 96 * 100) / 100
}

export const mm2px = (mm: number) => {
    return Math.round(mm * 96 / 25.4 * 100) / 100
}

export const position2number = (position: number) => {
    return parseInt(position.toFixed(0))
}

export const calcDistance = (currentPosition: any, currentField: any, otherField: any) => {
    const distance: any[] = []
    if (!currentField || !otherField) return distance
    // 拖拽组件信息
    const currentX = position2number(currentPosition.x)
    const currentY = position2number(currentPosition.y)
    const currentWidth = mm2px(currentField.width)
    const currentHeight = mm2px(currentField.height)
    // 其他组件信息
    const otherX = mm2px(otherField.marginLeft)
    const otherY = mm2px(otherField.marginTop)
    const otherWidth = mm2px(otherField.width)
    const otherHeight = mm2px(otherField.height)

    // 计算水平距离
    if (currentY < otherY + otherHeight && currentY + currentHeight > otherY) {
        // 垂直方向有重叠，计算水平距离
        if (currentX + currentWidth < otherX) {
            // 当前组件在左边
            distance.push({
                distance: px2mm(otherX - (currentX + currentWidth)),
                fromX: currentX + currentWidth,
                toX: otherX,
                y: Math.max(currentY, otherY) + Math.min(currentHeight, otherHeight) / 2,
                direction: 'horizontal'
            })
        } else if (otherX + otherWidth < currentX) {
            // 当前组件在右边
            distance.push({
                distance: px2mm(currentX - (otherX + otherWidth)),
                fromX: otherX + otherWidth,
                toX: currentX,
                y: Math.max(currentY, otherY) + Math.min(currentHeight, otherHeight) / 2,
                direction: 'horizontal'
            })
        }
    }

    // 计算垂直距离
    if (currentX < otherX + otherWidth && currentX + currentWidth > otherX) {
        // 水平方向有重叠，计算垂直距离
        if (currentY + currentHeight < otherY) {
            // 当前组件在上边
            distance.push({
                distance: px2mm(otherY - (currentY + currentHeight)),
                fromY: currentY + currentHeight,
                toY: otherY,
                x: Math.max(currentX, otherX) + Math.min(currentWidth, otherWidth) / 2,
                direction: 'vertical'
            })
        } else if (otherY + otherHeight < currentY) {
            // 当前组件在下边
            distance.push({
                distance: px2mm(currentY - (otherY + otherHeight)),
                fromY: otherY + otherHeight,
                toY: currentY,
                x: Math.max(currentX, otherX) + Math.min(currentWidth, otherWidth) / 2,
                direction: 'vertical'
            })
        }
    }
    return distance
}

// 检测边框对齐情况
export const checkBorderAlignment = (currentKey: string, currentPosition: any, fieldsInfo: any) => {
    const alignedBordersMap: {[key: string]: string[]} = {}
    const currentField = fieldsInfo[currentKey]
    if (!currentField) return alignedBordersMap

    const currentX = position2number(currentPosition.x)
    const currentY = position2number(currentPosition.y)
    const currentWidth = mm2px(currentField.width)
    const currentHeight = mm2px(currentField.height)

    // 对齐容差（像素）
    const tolerance = 1

    // 当前组件的四个边框位置
    const currentBorders = {
        top: currentY,
        right: currentX + currentWidth,
        bottom: currentY + currentHeight,
        left: currentX
    }

    Object.keys(fieldsInfo).forEach(key => {
        if (key === currentKey) return

        const otherField = fieldsInfo[key]
        const otherX = mm2px(otherField.marginLeft)
        const otherY = mm2px(otherField.marginTop)
        const otherWidth = mm2px(otherField.width)
        const otherHeight = mm2px(otherField.height)

        // 其他组件的四个边框位置
        const otherBorders = {
            top: otherY,
            right: otherX + otherWidth,
            bottom: otherY + otherHeight,
            left: otherX
        }

        // 检测每个边框是否对齐
        const alignedBordersList: string[] = []

        if (Math.abs(currentBorders.top - otherBorders.top) <= tolerance) {
            alignedBordersList.push('top')
        }
        if (Math.abs(currentBorders.right - otherBorders.right) <= tolerance) {
            alignedBordersList.push('right')
        }
        if (Math.abs(currentBorders.bottom - otherBorders.bottom) <= tolerance) {
            alignedBordersList.push('bottom')
        }
        if (Math.abs(currentBorders.left - otherBorders.left) <= tolerance) {
            alignedBordersList.push('left')
        }

        // 如果有对齐的边框，记录到结果中
        if (alignedBordersList.length > 0) {
            alignedBordersMap[key] = alignedBordersList
        }
    })

    return alignedBordersMap
}

export const getBindData = (data: any, customerName: string) => {
    if (!data) {
        return [{key: "customerName", value: customerName, tips: 'customers.name'}]
    }
    return [
        {key: "customerName", value: customerName, tips: 'customers.name'},
        {key: "productName", value: data['name'], tips: 'drug.list.name'},
        {key: "barcodeNumber", value: data['number'], tips: 'barcode'},
        {key: "batchNumber", value: data['batch_number'], tips: 'drug.list.batch'},
        {key: "expirationDate", value: data['expiration_date'], tips: 'drug.list.expireDate'},
    ]
}

export const fontFamilyOptions = [
    {label: 'barcode.label.attribute.fontFamilyMicrosoftYaHei', value: 'MicrosoftYaHei', default: 'sans-serif'},
    {label: 'barcode.label.attribute.fontFamilySong', value: 'SimSun', default: 'serif'},
    {label: 'barcode.label.attribute.fontFamilyArial', value: 'Arial', default: 'sans-serif'},
    {label: 'barcode.label.attribute.fontFamilyTimesNewRoman', value: 'TimesNewRoman', default: 'sans-serif'},
]