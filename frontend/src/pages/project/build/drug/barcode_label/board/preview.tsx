import {Modal} from "antd";
import {FormattedMessage} from "../../../../../common/multilingual/component";
import React, {useEffect, useImperativeHandle, useRef} from "react";
import {useSafeState} from "ahooks";
import {useReactToPrint} from "react-to-print";
import './index.less'
import Draggable from "react-draggable";
import {getBindData, mm2px} from "./util";
import {DragInput} from "./drag-input";
import _ from "lodash";
import {useAuth} from "../../../../../../context/auth";

export const PreviewLabel = (props: any) => {
    const [visible, setVisible] = useSafeState<boolean>(false)
    const [fields, setFields] = useSafeState<any[]>([])
    const [labelSize, setLabelSize] = useSafeState<any>({})

    useImperativeHandle(props.bind, () => ({show}))

    const show = (fields: any[], labelSize: any) => {
        setFields(fields)
        setLabelSize(labelSize)
        setVisible(true)
    }
    const hide = () => {
        setFields([])
        setVisible(false)
    }

    return <Modal
        open={visible}
        centered
        onCancel={hide}
        onOk={hide}
        title={<FormattedMessage id={'barcode.label.preview'}/>}
        closable
        forceRender
        className="custom-medium-modal"
        bodyStyle={{height: `calc(${labelSize?.height}mm + 48px)`, minHeight: '250px'}}
        maskClosable={false}
        footer={null}
    >
        <div style={{height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center'}} className={'preview-print'}>
            <div className={'preview-label'}><LabelPreview fields={fields} labelSize={labelSize} /></div>
        </div>
    </Modal>
}

export const PreviewPrint = (props: any) => {
    const auth = useAuth()
    const customerName = auth.project.customerName

    const [contentRef, setContentRef] = useSafeState<any>()
    const [styleElement, setStyleElement] = useSafeState<any>()
    const [printData, setPrintData] = useSafeState<any[]>([])

    useEffect(() => {
        const printStyle = `
           @media print {
             @page {
               width: ${props.printSize.width}mm;
               height: ${props.printSize.height}mm;
             }
           }
         `;
        const styleElement = document.createElement('style')
        styleElement.media = 'print'
        styleElement.appendChild(document.createTextNode(printStyle))
        setStyleElement(styleElement)
    }, [props.printSize])

    const reactToPrintFn = useReactToPrint({
        content: () => contentRef,
        onBeforePrint: () => {},
        onAfterPrint: () => {
            document.head.removeChild(styleElement)
        }
    })

    useImperativeHandle(props.bind, () => ({print}))

    const enrichData = (fields: any[], data: any[]) => {
        if (data.length === 0) return [fields]
        return data.map((it: any) => {
            const bindData = getBindData(it, customerName)
            return _.cloneDeep(fields).map((field: any) => {
                const bindValue = bindData.find(b => b.key === field.bind)?.value
                if (!!bindValue) field.value = bindValue
                return field
            })
        })
    }

    const print = (fields: any[], data: any[]) => {
        setPrintData([])
        document.head.appendChild(styleElement)
        setTimeout(() => {
            setPrintData(enrichData(fields, data))
            reactToPrintFn()
        }, 20)
    }

    return (
        <div style={{position: 'absolute', zIndex: -1, top: 0, height: 0, overflow: "auto"}}>
            <div ref={setContentRef} className={'preview-print'}>
                <div style={{display: 'flex', flexWrap: 'wrap'}}>
                    {printData.map((fields, index) =>
                        <div key={index} className={'preview-label'} style={{margin: '2px 0 3mm 3mm'}}>
                            <LabelPreview fields={fields} labelSize={props.labelSize} />
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export const PreviewPrintLabel = (props: any) => {
    const previewPrintRef = useRef<any>()
    const [template, setTemplate] = useSafeState<any>({})
    const [visible, setVisible] = useSafeState<boolean>(false)

    useImperativeHandle(props.bind, () => ({preview}))

    const preview = (data: any) => {
        setTemplate(data.template)
        setTimeout(() => {
            setVisible(true)
            previewPrintRef?.current?.print(data.template.fields, data.productList || [])
        }, 20)
        setTimeout(() => {
            setVisible(false)
        }, 200)
    }

    return <>
        {visible && <PreviewPrint bind={previewPrintRef} printSize={template.printSize} labelSize={template.labelSize}  />}
    </>

}


const LabelPreview = (props: any) => {
    return <div
        className={'board-label'}
        style={{
            position: 'relative',
            width: `calc(${props.labelSize.width}mm)`,
            height: `calc(${props.labelSize.height}mm)`,
            boxShadow: "0px 0px 0px 1px rgba(235, 237, 240, 1)",
        }}
    >
        {props.fields.map((it: any) =>
            <Draggable bounds="parent" defaultPosition={{x: mm2px(it.marginLeft), y: mm2px(it.marginTop)}} disabled>
                <div
                    key={it.key}
                    data-key={it.key}
                    style={{position: 'absolute', width: `${it.width}mm`, height: `${it.height}mm`}}
                    className={'drag-block'}
                >
                    <DragInput
                        field={it}
                        defaultSize={{width: it.width, height: it.height}}
                        range={{x: [0, 0], y: [0, 0]}}
                    />
                </div>
            </Draggable>
        )}
    </div>
}
