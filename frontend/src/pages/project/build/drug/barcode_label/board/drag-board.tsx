import React, {useEffect, useImperative<PERSON>andle, useRef} from "react";
import Draggable from "react-draggable";
import {<PERSON><PERSON>, Popconfirm, Space} from "antd";
import './index.less';
import {useDebounceFn, useSafeState} from "ahooks";
import {FieldAttribute} from "./drag-field-attribute";
import {BarcodeOutlined, CloseCircleTwoTone, AlignCenterOutlined} from "@ant-design/icons";
import {calcDistance, checkBorderAlignment, componentPosition, innerArea, mm2px, position2number, px2mm} from "./util";
import {createUUid} from "../../../../../../utils/uuid"
import {defaultConfig} from "./template";
import {useAtom} from "jotai";
import {barcodeLabelAtom} from "../context";
import _ from "lodash";
import {DragBarcode, DragInput} from "./drag-input";
import {FormattedMessage} from "../../../../../common/multilingual/component";
import {DragComponentDistance, DragRuler} from "./component";
import {useIntl} from "react-intl";
import {useGlobal} from "../../../../../../context/global";

export const DragBoard = (props: any) => {
    const g = useGlobal()
    const {formatMessage} = useIntl()
    // 标签范围
    const [labelRange, setLabelRange] = useSafeState<any>({x: [0, 0], y: [0, 0]})
    const [labelZeroRange, setLabelZeroRange] = useSafeState<any>({x: [0, 0], y: [0, 0]})
    // 活动字段
    const [activeKey, setActiveKey] = useSafeState<string>('')
    const [isMove, setIsMove]  = useSafeState<boolean>(false)
    // 水平尺状态
    const [showRuler, setShowRuler] = useSafeState<boolean>(false)
    const [rulerPosition, setRulerPosition] = useSafeState<{ x: number, y: number }>({x: 0, y: 0})
    // 距离显示状态
    const [componentDistances, setComponentDistances] = useSafeState<any[]>([])
    // 对齐高亮状态 - 存储对齐的边框信息
    const [alignedBorders, setAlignedBorders] = useSafeState<{[key: string]: string[]}>({})
    // 字段信息
    const [fieldsInfo, setFieldsInfo] = useSafeState<{[key: string]: any}>({})
    const [fields, setFields] = useSafeState<any[]>([])
    // 组件ref
    const dragBoardRef = useRef<HTMLDivElement>(null)
    const dragAreaRef = useRef<HTMLDivElement>(null)
    const dragBarcodeRef = useRef<HTMLDivElement>(null)
    const dragTextRef = useRef<HTMLDivElement>(null)
    // 标签ref
    const [, setLabelRef] = useAtom(barcodeLabelAtom)
    useImperativeHandle(props.bind, () => ({getFields}))

    // 强制刷新
    const [forceRefresh, setForceRefresh] = useSafeState<number>(0)

    useEffect(() => {
        setLabelRef(dragAreaRef.current)
    }, [])


    useEffect(() => {
        setTimeout(() => {
            const defaultOffset = {offsetTop: 0, offsetLeft: 0, clientWidth: 0, clientHeight: 0}
            const {offsetTop: boardTop, offsetLeft: boardLeft} = dragBoardRef?.current || defaultOffset
            const {
                offsetTop: dragTop, offsetLeft: dragLeft,
                clientWidth: width, clientHeight: height
            } = dragAreaRef?.current || defaultOffset
            const offsetTop = dragTop + boardTop
            const offsetLeft = dragLeft + boardLeft
            setLabelRange({
                x: [offsetLeft, offsetLeft + width] as [number, number],
                y: [offsetTop, offsetTop + height] as [number, number]
            })
            setLabelZeroRange({
                x: [0, width] as [number, number],
                y: [0, height] as [number, number]
            })
        }, 10)
    }, [props.labelSize])

    // 初始化字段
    useEffect(() => {
        setActiveKey('')
        hideAuxiliaryLine()
        const fields = props.defaultFields || []
        setFieldsInfo(fields.reduce((res: { [key: string]: any; }, item: any) => {
            res[item.key] = item
            return res
        }, {}))
        setTimeout(() => setFields(fields), 20)
    }, [props.defaultFields])

    useEffect(() => {
        if (!props.bindData || props.bindData.length === 0) return
        const newInfo = Object.keys(fieldsInfo).reduce((res: any, key) => {
            const item = fieldsInfo[key]
            const bindValue = props.bindData.find((it: any) => it.key === item.bind)?.value
            if (!!bindValue) item.value = bindValue
            res[key] = item
            return res
        }, {})
        setFieldsInfo(newInfo)
    }, [props.bindData])

    const defaultBarcodeNumber = () => {
        return props.bindData.find((it: any) => it.key === 'barcodeNumber')?.value || props.defaultBarcode
    }

    const getFields = () => {
        const keys = fields.map(it => it.key)
        return Object.keys(fieldsInfo).filter(it => keys.includes(it)).map(it => fieldsInfo[it])
    }

    // 计算组件间距离
    const calculateDistances = (currentKey: string, currentPosition: any) => {
        const currentField = fieldsInfo[currentKey]
        if (!currentField) return []
        const distances = fields.filter(it => it.key !== currentKey).flatMap(it => {
            return calcDistance(currentPosition, currentField, fieldsInfo[it.key])
        })
        const directionMap: any = {horizontal: 'toY', vertical: 'fromX'}
        return _.uniqWith(_.sortBy(distances, 'distance'), (one: any, two: any) => {
            return one.direction === two.direction && one[directionMap[one.direction]] === two[directionMap[one.direction]]
        })

    }

    const onStart = (e: any, position: any, key: string) => {
        setActiveKey(key)
        startAlignmentDetection(key, position)
        // 显示水平尺和垂直尺
        setShowRuler(true)
        setRulerPosition({x: position.x, y: position.y})
        // 计算并显示距离
        const distances = calculateDistances(key, position)
        setComponentDistances(distances)
    }

    const { run: onDrop } = useDebounceFn((e: any, position: any,  key: string) => {
        setIsMove(true)
        startAlignmentDetection(key, position)
        // 更新水平尺和垂直尺位置
        setRulerPosition({x: position.x, y: position.y})
        // 更新距离显示
        const distances = calculateDistances(key, position)
        setComponentDistances(distances)
    }, { wait: 40 })

    const onStop = (e: any, position: any, key: string) => {
        setIsMove(false)
        // 需要比onDrop中wait时间长
        setTimeout(hideAuxiliaryLine, 50)
        onForceChange(key, {
            marginLeft: px2mm(position2number(position.x)),
            marginTop: px2mm(position2number(position.y))
        })
    }

    // 隐藏辅助线
    const hideAuxiliaryLine = () => {
        setShowRuler(false)
        setAlignedBorders({})
        setComponentDistances([])
    }

    // 检测对齐
    const startAlignmentDetection = (key: string, position: any) => {
        // 获取当前拖拽组件的实时位置
        const dragElement = document.querySelector(`[data-key="${key}"]`)
        if (!dragElement) return
        const rect = dragElement.getBoundingClientRect()
        const boardRect = dragAreaRef.current?.getBoundingClientRect()
        if (boardRect) {
            const currentPosition = {
                x: rect.left - boardRect.left,
                y: rect.top - boardRect.top
            }
            // 检测边框对齐
            const alignedBordersMap = checkBorderAlignment(key, currentPosition, fieldsInfo)
            setAlignedBorders(alignedBordersMap)
        }
    }

    const onClickComponent = (field: any) => {
        setActiveKey(field.key)
    }

    const onFieldChange = (key: string, label: string, value: any) => {
        onChange(key, {[label]: value})
    }

    const onChange = (key: string, values: any) => {
        // 宽高变化时需要判断边距是否超出标签
        const newInfo = {...fieldsInfo[key], ...values}
        const info = _.update(fieldsInfo, [key], () => ({
            ...newInfo,
            marginTop: addOffset(newInfo.marginTop, newInfo.height, 0, labelZeroRange.y),
            marginLeft: addOffset(newInfo.marginLeft, newInfo.width, 0, labelZeroRange.x),
        }))
        setFieldsInfo({...info})
    }

    const onForceChange = (key: string, values: any) => {
        onChange(key, values)
        setForceRefresh(forceRefresh + 1)
    }

    const addOffset = (mm: number, length: number, offset: number, range: [number, number]) => {
        const positionPx = mm2px(mm || 0) + offset
        const lengthPx = mm2px(length)
        if (positionPx <= range[0]) return 0
        if (positionPx + lengthPx >= range[1]) return px2mm(range[1] - lengthPx)
        return px2mm(positionPx)
    }

    const keyCodeMap: {[key: number]: any} = {
        37: {direction: 'left', offset: {x: -1, y: 0}},
        38: {direction: 'up', offset: {x: 0, y: -1}},
        39: {direction: 'right', offset: {x: 1, y: 0}},
        40: {direction: 'down', offset: {x: 0, y: 1}},
    }

    useEffect(() => {
        const keyup = (e: any) => {
            const offset = keyCodeMap[e.keyCode]?.offset
            if (!!offset && !!activeKey) {
                const activeField = fieldsInfo[activeKey]
                onChange(activeKey, {
                    marginTop: addOffset(activeField.marginTop, activeField.height, offset.y, labelZeroRange.y),
                    marginLeft: addOffset(activeField.marginLeft, activeField.width, offset.x, labelZeroRange.x),
                })
            }
        }
        window.addEventListener("keyup", keyup)
        return () => {
            window.removeEventListener("keyup", keyup)
        }
    },[activeKey, forceRefresh])


    const getDragStyle = (field: any): any => {
        const info = fieldsInfo[field.key] || field
        const baseStyle = {
            position: 'absolute',
            width: `calc(${info.width}mm)`,
        }
        // 如果是输入框且处于编辑状态，并且高度未超过标签，使用自动高度
        const overflowY = info.height > px2mm(labelZeroRange.y[1])
        if (info.type === 'input' && !overflowY) {
            return {...baseStyle, minHeight: `calc(${info.height}mm)`, height: 'auto'}
        }
        // 其他情况使用固定高度
        return {...baseStyle, height: `calc(${info.height}mm)`}
    }

    const addComponent = (e: any, position: any, ref: any, type: string) => {
        const borderSize = type === 'barcode' ? {x: 3, y: 6} : {x: 3, y: 2}
        const value = type === 'barcode' ? defaultBarcodeNumber() : formatMessage({id: 'barcode.label.component.textBox'})
        const bind = type === 'barcode' ? 'barcodeNumber' : ''
        const width = ref?.current?.clientWidth + borderSize.x
        const height = ref?.current?.clientHeight + borderSize.y
        const {scrollLeft, scrollTop} = dragBoardRef?.current || {scrollLeft: 0, scrollTop: 0}
        const x = {
            allowRange: labelRange.x,
            positionStart: position2number(position.x) + scrollLeft,
            length: width + 2
        }
        const y = {
            allowRange: labelRange.y,
            positionStart: position2number(position.y) + scrollTop,
            length: height + 2
        }

        if (innerArea(x, y, 0.3)) {
            const point = componentPosition(x, y)
            const newField =  {
                ...defaultConfig, width: px2mm(width), height: px2mm(height),
                key: createUUid(), value: value, marginLeft: px2mm(point[0]), marginTop: px2mm(point[1]),
                type: type, bind: bind
            }
            setFields([...fields, newField])
            setFieldsInfo({...fieldsInfo, [newField.key]: newField})
            setActiveKey(newField.key)
        }
    }

    const removeComponent = (field: any) => {
        setFields(fields.filter(it => it.key !== field.key))
        setTimeout(() => {
            setActiveKey('')
            hideAuxiliaryLine()
        }, 10)
    }

    const clearDragBoard = () => {
        setFields([])
        setFieldsInfo({})
        setForceRefresh(forceRefresh + 1)
    }


    // 一键智能对齐功能 - 同时处理水平和垂直对齐
    const smartAlign = () => {
        if (fields.length < 2) return
        const tolerance = 2 // 容差范围，单位：mm
        const allFields = fields.map(field => fieldsInfo[field.key] || field)
        const updatedFieldsInfo = {...fieldsInfo}
        // 第一步：水平对齐（按 marginTop 分组）
        const horizontalGroups: any[][] = []
        allFields.forEach(field => {
            let addedToGroup = false
            for (let group of horizontalGroups) {
                // 检查是否在容差范围内
                const avgTop = group.reduce((sum, f) => sum + f.marginTop, 0) / group.length
                if (Math.abs(field.marginTop - avgTop) <= tolerance) {
                    group.push(field)
                    addedToGroup = true
                    break
                }
            }
            if (!addedToGroup) horizontalGroups.push([field])
        })
        // 对每个水平组内的组件进行对齐
        horizontalGroups.forEach((group, groupIndex) => {
            if (group.length > 1) {
                const avgTop = group.reduce((sum, f) => sum + f.marginTop, 0) / group.length
                group.forEach(field => {
                    const newMarginTop = Math.max(0, Math.min(avgTop, px2mm(labelZeroRange.y[1]) - field.height))
                    updatedFieldsInfo[field.key] = {
                        ...field,
                        marginTop: Math.round(newMarginTop * 100) / 100
                    }
                })
            }
        })
        // 第二步：垂直对齐（按 marginLeft 分组，使用更新后的字段信息）
        const verticalGroups: any[][] = []
        const fieldsForVertical = allFields.map(field => updatedFieldsInfo[field.key] || field)
        fieldsForVertical.forEach(field => {
            let addedToGroup = false
            for (let group of verticalGroups) {
                // 检查是否在容差范围内
                const avgLeft = group.reduce((sum, f) => sum + f.marginLeft, 0) / group.length
                if (Math.abs(field.marginLeft - avgLeft) <= tolerance) {
                    group.push(field)
                    addedToGroup = true
                    break
                }
            }
            if (!addedToGroup) verticalGroups.push([field])
        })
        // 对每个垂直组内的组件进行对齐
        verticalGroups.forEach((group, groupIndex) => {
            if (group.length > 1) {
                const avgLeft = group.reduce((sum, f) => sum + f.marginLeft, 0) / group.length
                group.forEach(field => {
                    const newMarginLeft = Math.max(0, Math.min(avgLeft, px2mm(labelZeroRange.x[1]) - field.width))
                    updatedFieldsInfo[field.key] = {
                        ...updatedFieldsInfo[field.key],
                        marginLeft: Math.round(newMarginLeft * 100) / 100
                    }
                })
            }
        })
        setFieldsInfo(updatedFieldsInfo)
        setForceRefresh(forceRefresh + 1)
    }

    const BarcodeDrag = <><BarcodeOutlined />  <FormattedMessage id={'barcode'} /></>
    const TextBoxDrag = <>Aa <FormattedMessage id={'barcode.label.component.textBox'} /></>

    return  <div className={'board-container'}>
        <div style={{width: '100%', display: 'flex', marginBottom: '16px', justifyContent: 'space-between'}}>
            <div style={{position: 'relative', display: 'flex'}}>
                <div>
                    <div className="drag-component">{BarcodeDrag}</div>
                    <div style={{position: 'absolute', top: 0, left: 0, zIndex: 3}}>
                        <Draggable
                            handle=".drag-handle"
                            position={{x: 0, y: 0}}
                            scale={1}
                            onStop={(e, position) => addComponent(e, position,  dragBarcodeRef, 'barcode')}
                        >
                            <div className={'drag-component drag-handle'} style={{background: 'transparent'}}>
                                {BarcodeDrag}
                            </div>
                        </Draggable>
                    </div>
                    <div ref={dragBarcodeRef} style={{position: 'absolute', top: 0, left: 0, zIndex: -2}}>
                        <DragBarcode value={defaultBarcodeNumber()} />
                    </div>
                </div>
                <div style={{marginLeft: '12px'}}>
                    <div className="drag-component" ref={dragTextRef}>{TextBoxDrag}</div>
                    <div style={{position: 'absolute', top: 0, left: 0, zIndex: 2}}>
                        <Draggable
                            handle=".drag-handle"
                            position={{x: g.lang === 'zh' ? 104 : 116, y: 0}}
                            scale={1}
                            onStop={(e, position) => addComponent(e, position, dragTextRef, 'input')}
                        >
                            <div className={'drag-component drag-handle'} style={{background: 'transparent'}}>
                                {TextBoxDrag}
                            </div>
                        </Draggable>
                    </div>
                </div>
            </div>
            <Space>
                {fields.length >= 2 && <Button onClick={smartAlign} type={'link'} style={{padding: 0}}>
                    <FormattedMessage id={'barcode.label.component.smartAlign'} />
                </Button>}
                {fields.length > 0 && <Button onClick={clearDragBoard} type={'link'} style={{padding: 0}}>
                    <FormattedMessage id={'common.pagination.empty'} />
                </Button>}
            </Space>

        </div>
        <div className={'board-drag'} ref={dragBoardRef}>
            {/* 顶部水平尺 */}
            <div className="ruler-container ruler-horizontal-top">
                <div className="ruler-scale">
                    {Array.from({length: Math.ceil(props.labelSize.width / 5) + 1}, (_, i) => (
                        <div
                            key={i}
                            className="ruler-mark"
                            style={{left: `${mm2px(i * 5)}px`, height: i % 2 === 0 ? '6px' : '3px'}}
                        >
                            {i % 2 === 0 && i > 0 && (<span className="ruler-number">{i * 5}</span>)}
                        </div>
                    ))}
                </div>
            </div>
            {/* 左侧垂直尺 */}
            <div className="ruler-container ruler-vertical-left">
                <div className="ruler-scale">
                    {Array.from({length: Math.ceil(props.labelSize.height / 5) + 1}, (_, i) => (
                        <div
                            key={i}
                            className="ruler-mark"
                            style={{top: `${mm2px(i * 5)}px`, width: i % 2 === 0 ? '6px' : '3px'}}
                        >
                            {i % 2 === 0 && i > 0 && (<span className="ruler-number">{i * 5}</span>)}
                        </div>
                    ))}
                </div>
            </div>
            {/* 拖拽框 */}
            <div
                ref={dragAreaRef}
                className={'board-label'}
                style={{
                    position: 'relative',
                    width: `calc(${props.labelSize.width}mm)`,
                    height: `calc(${props.labelSize.height}mm)`,
                    boxShadow: "0px 0px 0px 1px rgba(235, 237, 240, 1)",
                }}
            >
                {/* 水平尺 */}
                {showRuler && <DragRuler
                    rulerPosition={rulerPosition}
                    labelSize={props.labelSize}
                    range={labelZeroRange}
                    field={fieldsInfo[activeKey]}
                />}

                {/* 组件间距离显示 */}
                {componentDistances.map((distance, index) =>
                    <DragComponentDistance index={index} distance={distance} />
                )}

                {fields.map(it =>
                    <Draggable
                        bounds="parent"
                        handle=".handle"
                        position={{x: mm2px(fieldsInfo[it.key]?.marginLeft), y: mm2px(fieldsInfo[it.key]?.marginTop)}}
                        // defaultPosition={{x: mm2px(it.marginLeft), y: mm2px(it.marginTop)}}
                        // grid={[25, 25]}
                        // scale={1}
                        onStart={(e, position,) => onStart(e, position, it.key)}
                        onDrag={(e,  position) => onDrop(e, position, it.key)}
                        onStop={(e,  position) => onStop(e, position, it.key)}
                    >
                        <div
                            key={it.key}
                            data-key={it.key}
                            style={{
                                ...getDragStyle(it),
                                // 添加对齐边框高亮样式
                                ...(alignedBorders[it.key] ? {
                                    boxShadow: alignedBorders[it.key].map(border => {
                                        switch(border) {
                                            case 'top': return 'inset 0 1px 0 0 #52c41a'
                                            case 'right': return 'inset -1px 0 0 0 #52c41a'
                                            case 'bottom': return 'inset 0 -1px 0 0 #52c41a'
                                            case 'left': return 'inset 1px 0 0 0 #52c41a'
                                            default: return ''
                                        }
                                    }).filter(shadow => shadow).join(', ')
                                } : {})
                            }}
                            onClick={() => onClickComponent(it)}
                            className={it.key === activeKey ? 'drag-block drag-block-active handle' : 'drag-block handle'}
                        >
                            <DragInput
                                field={fieldsInfo[it.key] || it}
                                defaultSize={{
                                    width: fieldsInfo[it.key]?.manualWidth || it.width,
                                    height: fieldsInfo[it.key]?.manualHeight || it.height
                                }}
                                isMove={isMove}
                                activeKey={activeKey}
                                onChange={onFieldChange}
                                range={labelZeroRange}
                            />
                            {/* {it.key === activeKey && showRuler && <DragRuleStatic labelSize={props.labelSize} />}*/}
                            {it.key === activeKey &&  <Popconfirm
                                title={<FormattedMessage id={'barcode.label.component.delete'} />}
                                okText={<FormattedMessage id={'common.yes'} />}
                                cancelText={<FormattedMessage id={'common.no'} />}
                                onConfirm={() => removeComponent(it)}
                            >
                                 <span
                                     className={'drag-remove-icon'}
                                     style={{display: 'flex', position: 'absolute', top: -7, right: -7, cursor: 'pointer'}}
                                 >
                                    <CloseCircleTwoTone style={{fontSize: '14px', color: 'rgba(187, 187, 187, 1)'}} />
                                </span>
                            </Popconfirm>}
                        </div>
                    </Draggable>
                )}
            </div>
        </div>
        <div className={'board-operation'}>
            {/*triangle*/}
            <div className="triangle-border"><div className="triangle-inner"></div></div>
            {/*字段属性*/}
            <div className={g.lang === 'zh' ? 'field-attribute field-attribute-zh': 'field-attribute'}>
                <FieldAttribute
                    disabled={!activeKey}
                    fieldKey={activeKey}
                    fieldsInfo={fieldsInfo}
                    range={labelZeroRange}
                    onChange={onForceChange}
                    bindData={props.bindData || []}
                />
            </div>
        </div>
    </div>
}