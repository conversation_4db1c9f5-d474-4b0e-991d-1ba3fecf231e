import { FormattedMessage } from "../../../../common/multilingual/component";
import { Button, Form, Modal, Select, Space, message } from "antd";
import { useIntl } from "react-intl";
import { useSafeState } from "ahooks";
import { getBarcodeTask } from "../../../../../api/barcode"
import { useAuth } from "../../../../../context/auth";
import { useFetch } from "../../../../../hooks/request";
import React, { useImperativeHandle, useRef } from "react";
import { defaultTemplate } from "./board/template"
import { PaperSizeInput } from "./board/component"
import { DragBoard } from "./board/drag-board"
import { PreviewLabel, PreviewPrint } from "./board/preview"
import { addBarcodeLabel } from "../../../../../api/barcode_label";
import _ from "lodash";
import { getBindData } from "./board/util";
import { MedicineSelectModal } from "./medicine-modal"
import { useGlobal } from "../../../../../context/global";

export const BarcodeLabelAdd = (props: any) => {
    const g = useGlobal()
    const auth = useAuth()
    const intl = useIntl();
    const { formatMessage } = intl;

    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const customerName = auth.project.customerName
    const [visible, setVisible] = useSafeState<boolean>(false);
    const projectStatus = auth.project.status ? auth.project.status : 0
    const [medicineData, setMedicineData] = useSafeState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<React.Key[]>([]);
    const [printSize, setPrintSize] = useSafeState({})
    const [labelSize, setLabelSize] = useSafeState({})
    const [defaultFields, setDefaultFields] = useSafeState<any[]>([])
    const [correlationId, setCorrelationId] = useSafeState(null);

    // 绑定选择的研究产品数据
    const [bindData, setBindData] = useSafeState<any[]>([])

    const medicineSelectRef = useRef<any>()
    // 预览ref
    const previewLabelRef = useRef<any>()
    const previewPrintRef = useRef<any>()
    const dragBoardRef = useRef<any>()
    useImperativeHandle(props.bind, () => ({ show }))

    const show = () => {
        setVisible(true)
        getBarcodeTaskData("")
        setCorrelationId(null)
        initTemplate()
        setTimeout(() => {
            updateBindData(null)
        }, 20)
    }

    const hide = () => {
        setVisible(false)
        setMedicineData([])
        setCorrelationId(null)
        setSelectedRowKeys([])
    }

    // 初始化默认模板
    const initTemplate = () => {
        const template = _.cloneDeep(defaultTemplate)
        setPrintSize(template.printSize)
        setLabelSize(template.labelSize)
        setDefaultFields(template.fields)
    }


    const { runAsync: getBarcodeTaskRun } = useFetch(getBarcodeTask, { manual: true })
    const { run: doAddBarcodeLabel, loading: addLoading } = useFetch(addBarcodeLabel, {
        manual: true,
        onSuccess: () => {
            message.success(formatMessage({ id: 'barcode.label.addSuccessTips' }))
            setTimeout(() => {
                props.refresh && props.refresh()
                hide()
            }, 50)
        }
    })

    // 关联条形码任务改变的时候，需要重新查询研究产品数据
    const onChangeTask = (value: any) => {
        setCorrelationId(value)
        getBarcodeTaskData(value || "")
    }

    // 根据自动编码的任务ID,查询生成的研究产品
    const getBarcodeTaskData = (correlationId: any) => {
        if (!correlationId) {
            updateMedicineData(props.allTask)
            return
        }
        getBarcodeTaskRun({
            id: correlationId, envId: envId, roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            updateMedicineData(result.data?.datas || [])
        })
    }
    const updateMedicineData = (medicines: any[]) => {
        setSelectedRowKeys([])
        setMedicineData(medicines)
    }

    // 研究产品弹出框
    const medicineListClick = () => {
        medicineSelectRef.current?.show()
    }

    const medicineListConfirm = (selectedRowKeys: string[]) => {
        setSelectedRowKeys(selectedRowKeys)
        const data = medicineData.filter((it: any) => selectedRowKeys.includes(it._id)) || []
        updateBindData(data.length > 0 ? data[0] : null)
    }

    const updateBindData = (data: any) => {
        const bindData = getBindData(data, customerName).map(it =>
            renderItem(it.key, it.value, it.tips)
        )
        setBindData(bindData)
    }

    const onOk = () => {
        if (selectedRowKeys.length <= 0) {
            message
                .error(
                    formatMessage({
                        id: "barcode.label.selectDrug.info",
                    })
                )
                .then();
            return;
        }

        const fields = dragBoardRef.current?.getFields()
        const data = {
            correlationId: correlationId,
            correlationName: props.taskOption.find((it: any) => it.value === correlationId)?.label,
            productIds: selectedRowKeys,
            codeMethod: selectedRowKeys.length === 0 ? 1 : 2, // 不关联研究产品为手动编码
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            cohortId: "",
            template: {
                labelSize: labelSize,
                printSize: printSize,
                fields: fields,
            }
        }
        doAddBarcodeLabel(data)
    }

    const previewLabel = () => {
        const fields = dragBoardRef.current?.getFields()
        previewLabelRef?.current?.show(fields, labelSize)
    }

    const previewPrint = () => {
        const fields = dragBoardRef.current?.getFields()
        const data = medicineData.filter((it: any) => selectedRowKeys.includes(it._id)) || []
        previewPrintRef?.current?.print(fields, data)
    }

    return <Modal
        open={visible}
        centered
        onCancel={hide}
        onOk={onOk}
        title={<FormattedMessage id={'common.add'} />}
        closable
        className="custom-large-modal"
        bodyStyle={{ height: '70vh' }}
        maskClosable={false}
        footer={<div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '0 8px' }}>
            <Space size={'large'}>
                <Button type={'link'} style={{ padding: '0' }} onClick={previewLabel}>
                    <FormattedMessage id={'barcode.label.preview'} />
                </Button>
                <Button type={'link'} style={{ padding: '0' }} onClick={previewPrint}>
                    <FormattedMessage id={'barcode.label.printPreview'} />
                </Button>
            </Space>
            <Space size={'middle'}>
                <Button onClick={hide}><FormattedMessage id={'common.cancel'} /></Button>
                <Button type={'primary'} onClick={onOk} loading={addLoading}><FormattedMessage id={'common.ok'} /></Button>
            </Space>
        </div>}
    >
        <div style={{ display: 'flex', flexDirection: 'column' }}>
            <Form labelCol={{ span: g.lang === 'zh' ? 3 : 4 }}>
                <Form.Item label={formatMessage({ id: 'barcode.taskIDs' })}>
                    <Select
                        placeholder={formatMessage({ id: 'placeholder.select.common' })}
                        onChange={(value) => onChangeTask(value)}
                        allowClear
                        className="full-width" options={props.taskOption}
                        value={correlationId}
                    />
                </Form.Item>
                <Form.Item label={formatMessage({ id: 'shipment.drug' })} name="drug" rules={[{ required: true }]} >
                    <Button type={'link'} onClick={medicineListClick} style={{ padding: '4px 0' }}>
                        {(selectedRowKeys.length || 0) + "/" + medicineData.length}
                    </Button>
                </Form.Item>
                {selectedRowKeys?.length > 0 && <Form.Item label={<FormattedMessage id={'barcode.label.printCount'} />}>
                    {selectedRowKeys?.length || 0}
                </Form.Item>}
                <Form.Item label={<FormattedMessage id={'barcode.label.printPaperSize'} />}>
                    <PaperSizeInput size={printSize} setSize={setPrintSize} />
                </Form.Item>
                <Form.Item label={<FormattedMessage id={'barcode.label.size'} />}>
                    <PaperSizeInput size={labelSize} setSize={setLabelSize} />
                </Form.Item>
                <Form.Item label={<FormattedMessage id={'barcode.label.baseComponent'} />}>
                    <DragBoard
                        bind={dragBoardRef}
                        defaultBarcode={'82892910198833'}
                        labelSize={labelSize}
                        defaultFields={defaultFields}
                        bindData={bindData}
                    />
                </Form.Item>
            </Form>
            <MedicineSelectModal
                bind={medicineSelectRef}
                medicineData={medicineData}
                selectedRowKeys={selectedRowKeys}
                onConfirm={medicineListConfirm}
            />
            <PreviewLabel bind={previewLabelRef} labelSize={labelSize} />
            <PreviewPrint bind={previewPrintRef} printSize={printSize} labelSize={labelSize} />
        </div>
    </Modal>
}

const renderItem = (key: string, value: string, tips: string) => ({
    key: key,
    value: value,
    label: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ fontSize: '12px', color: '#646566' }}><FormattedMessage id={tips} />：</span>
            <span>{value}</span>
        </div>
    ),
})