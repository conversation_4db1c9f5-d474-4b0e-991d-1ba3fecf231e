import React, {useEffect, useRef} from "react"
import {FormattedMessage} from "../../../../common/multilingual/component"
import {Badge, Button, Dropdown, Input, message, Modal, Space, Spin, Table} from "antd";
import {useSafeState} from "ahooks";
import {BarcodeLabelAdd} from "./add";
import {PageContextProvider, usePage} from "context/page";
import {useFetch} from "../../../../../hooks/request";
import {getBarcodeLabelList, getPreviewBarcodeLabel, sendBarcodeLabel} from "../../../../../api/barcode_label";
import {useAuth} from "../../../../../context/auth";
import {PaginationView} from "../../../../../components/pagination_batch";
import {permissions} from "../../../../../tools/permission";
import {useIntl} from "react-intl";
import {PreviewLabel, PreviewPrintLabel} from "./board/preview";
import {getBarcodeTask, getBarcodeTaskList} from "../../../../../api/barcode";
import {InfoCircleFilled} from "@ant-design/icons";


export const BarcodeLabel = (props: any) => {

    const {formatMessage} = useIntl()
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId
    const projectId = auth.project.id;
    const pageCtx = usePage()

    const [search, setSearch] = useSafeState<any>({})

    const addRef = useRef<any>()
    const [data, setData] = useSafeState<any[]>([])

    const [taskOption, setTaskOption] = useSafeState<any>([])
    const [allTask, setAllTask] = useSafeState<any>([])

    const previewRef = useRef<any>()
    const labelPreviewRef = useRef<any>()

    const codeMethodOption = [
        {value: 1, label: <FormattedMessage id={'barcode.label.codeManual'} />},
        {value: 2, label: <FormattedMessage id={'barcode.label.codeAuto'} />},
    ]
    const statusOption = [
        {value: 1, label: <FormattedMessage id={'common.effective'} />, color: 'rgba(65, 204, 130, 1)'},
        {value: 2, label: <FormattedMessage id={'common.invalid'} />, color: 'rgba(192, 196, 204, 1)'},
    ]

    useEffect(() => {
        setSearch({})
    }, [])


    useFetch(() => getBarcodeTaskList({customerId, projectId, envId}), {
        onSuccess: (resp: any) => {
            const data = resp.data?.datas || []
            const options = data.map((it: any) => ({
                label: it.correlationId,
                value: it.id
            }));
            setTaskOption(options)
        }
    })

    useFetch(() => getBarcodeTask({
        id: "", envId: envId, roleId: auth.project.permissions.role_id,
    }), {
        onSuccess: (resp: any) => {
            setAllTask(resp.data?.datas || [])
        }
    })


    const columns = [
        {
            key: '#', width: 60, title: <FormattedMessage id={"common.serial"}/>, ellipsis: true,
            render: (value: any, record: any, index: number) => index + 1
        },
        {key: 'labelNumber', dataIndex: 'labelNumber', width: 120, title: <FormattedMessage id={"barcode.label.number"}/>, ellipsis: true},
        {
            key: 'codeMethod',
            dataIndex: 'codeMethod',
            width: 120,
            title: <FormattedMessage id={"projects.attributes.code.config.method"}/>,
            ellipsis: true,
            render: (text: number) => codeMethodOption.find(it => it.value === text)?.label || '-'
        },
        {
            key: 'correlationName',
            dataIndex: 'correlationName',
            width: 120,
            title: <FormattedMessage id={"barcode.taskIDs"}/>,
            ellipsis: true,
            render: (text: string) => text || '-'
        },
        {
            key: 'productCount',
            dataIndex: 'productCount',
            width: 120,
            title: <FormattedMessage id={"barcode.label.printCount"}/>,
            ellipsis: true,
            render: (text: number, record: any) => record.codeMethod === 1 ? '-' : text
        },
        {
            key: 'status',
            dataIndex: 'status',
            width: 120,
            title: <FormattedMessage id={"common.status"}/>,
            render: (text: any) => {
                const op = statusOption.find(it => it.value === text)
                return <Badge text={op?.label} color={op?.color} />
            }
        },
        {
            key: 'operator', width: 120, title: <FormattedMessage id={"common.operation"}/>,
            render: (_: any, record: any) => <Space size={'middle'}>
                {permissions(auth.project.permissions, "operation.build.medicine.barcode_label.preview") && <Dropdown
                    menu={{items: [
                        {key: '1', label: <FormattedMessage id={'barcode.label.preview'} />, onClick: () => previewLabel(record.id)},
                        {key: '2', label: <FormattedMessage id={'barcode.label.printPreview'} />, onClick: () => preview(record.id)}
                    ]}}
                >
                    <Button type={'link'} style={{padding: '4px 0'}}>
                        <FormattedMessage id={'form.preview'} />
                    </Button>
                </Dropdown>}
                {permissions(auth.project.permissions, "operation.build.medicine.barcode_label.send") && record.sendMeta?.status !== 1 && <Button
                    type={'link'} style={{padding: '4px 0'}}
                    onClick={() => send(record.id)}
                >
                    <FormattedMessage id={'barcode.label.send'} />
                </Button>}
            </Space>
        },
    ]

    const {run: getData, loading} = useFetch(() => getBarcodeLabelList({
            ...search,
            envId, projectId, customerId, cohortId: "",
            skip: (pageCtx.currentPage - 1) * pageCtx.pageSize, limit: pageCtx.pageSize
        }), {
        onSuccess: ((resp: any) => {
            if (resp && resp.code === 0) {
                const data = resp.data
                setData(data.data || [])
                pageCtx.setTotal(data.total || 0)
            }
        })
    })

    const {runAsync: getPreviewData, loading: previewLoading} = useFetch((id) => getPreviewBarcodeLabel(id, {
        envId, projectId, customerId, cohortId: ""
    }), {manual: true})

    const {runAsync: sendLabel, loading: sendLoading} = useFetch((id) => sendBarcodeLabel(id, {
        envId, projectId, customerId, cohortId: ""
    }), {manual: true})

    const add = () => {
        addRef?.current?.show()
    }

    const preview = (id: string) => {
        getPreviewData(id).then((resp: any) => {
            if (resp && resp.code === 0) {
                previewRef.current?.preview(resp.data)
            }
        })
    }

    const previewLabel = (id: string) => {
        getPreviewData(id).then((resp: any) => {
            if (resp && resp.code === 0) {
                const template = resp.data.template
                labelPreviewRef?.current?.show(template.fields, template.labelSize)
            }
        })
    }

    const send = (id: string) => {
        Modal.confirm({
            centered: true,
            title: formatMessage({id: "barcode.label.send.confirm"}),
            icon: <InfoCircleFilled style={{color: "#FFAE00"}}/>,
            okText: formatMessage({id: 'common.ok'}),
            cancelText: formatMessage({id: 'common.cancel'}),
            onOk: () => {
                sendLabel(id).then((resp: any) => {
                    if (resp && resp.code === 0) {
                        message.success(formatMessage({id: 'barcode.label.sendSuccess'}))
                        getData()
                    }
                })
            }
        })
    }


    return <React.Fragment>
        <Spin spinning={loading || sendLoading || previewLoading}>
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', margin: '12px 0'}}>
                <Input.Search
                    style={{width: '220px'}}
                    placeholder={formatMessage({id: 'barcode.label.number'})}
                    value={search.labelNumber}
                    onChange={e => setSearch({...search, labelNumber: e.target.value})}
                    onSearch={getData}
                />
                {permissions(auth.project.permissions, "operation.build.medicine.barcode_label.add") && <Button onClick={add}>
                    <FormattedMessage id={'common.add'}/>
                </Button>}
            </div>
            <Table
                dataSource={data}
                columns={columns}
                pagination={false}
            />
            <PaginationView refresh={getData} />
        </Spin>
        <PageContextProvider>
            <BarcodeLabelAdd
                bind={addRef}
                refresh={getData}
                taskOption={taskOption}
                allTask={allTask}
            />
        </PageContextProvider>
        <PreviewPrintLabel bind={previewRef} />
        <PreviewLabel bind={labelPreviewRef} />
    </React.Fragment>

}