import React from "react"
import {Button, Col, Form, Input, message, Modal, Row, Table, Tabs, notification} from "antd";
import {useDrug} from "./context";
import {FormattedMessage, useIntl} from "react-intl";
import styled from "@emotion/styled";
import {useFetch} from "../../../../hooks/request";
import {groupVisit, pushVisit} from "../../../../api/visit";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {CustomInfoModal} from "../../../../components/modal";
import {DateType} from "../../../../data/data";
import {useGlobal} from "../../../../context/global";
import { CheckCircleFilled  } from "@ant-design/icons";
import {permissions, permissionsCohort} from "../../../../tools/permission";


export const VisitView = (props: any) => {
    const intl = useIntl();
    const {formatMessage} = intl;
    const auth = useAuth()
    const [group, setGroup] = useSafeState([]);

    const drug = useDrug()

    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const {runAsync: groupVisitRun} = useFetch(groupVisit, {manual: true})
    const {runAsync: pushVisitRun} = useFetch(pushVisit, {manual: true})
    const getGroup = () => {
        groupVisitRun({envId, cohortId, roleId: auth.project.permissions.role_id}).then(
            (resp: any) => {
                setGroup(resp.data)
            }
        )
    }

    React.useEffect(getGroup, []);



    const hide = () => {
        drug.setVisitView(false)
        drug.setPushViewType(0)
    }

    return (
        <React.Fragment>
            <Modal
                zIndex={1000}

                forceRender
                open={drug.visitView}
                onCancel={hide}
                destroyOnClose={true}

                centered={true}
                className={"custom-large-modal"}
                maskClosable={false}
                
                // style={{ width: '1000px !important' }}
                footer={
                    drug.pushViewType === 0?
                    <Row gutter={8} justify="end">
                    <Col>
                        <Button onClick={hide}>{formatMessage({id: 'common.cancel'})}</Button>
                        <Button onClick={() => {drug.setVersionVisible(true)}} type={"primary"}>{formatMessage({id: 'common.ok'})}</Button>
                    </Col>
                </Row>
                :null}
                title={drug.pushViewType === 0?<FormattedMessage id={"visit.cycle.push"}/>:<FormattedMessage id={"common.view"}/>}
            >
                <Tabs destroyInactiveTabPane={true}
                      size="small" defaultActiveKey="1" tabPosition="top"
                >
                    {
                        group.map((value: any) => {
                                let group = (value.group + " " + value.subGroup).trim()
                                let key = (value.all_salt_name).trim()
                                return <Tabs.TabPane tab={group} key={key}>
                                    <GroupVisitTable data={drug.pushViewType === 1 ? drug.historyData : drug.data} group={drug.pushViewType === 0?group:key}/>
                                </Tabs.TabPane>
                            }
                        )
                    }

                </Tabs>


            </Modal>
            <ConfirmVersion refresh={props.refresh} />
        </React.Fragment>
    )
}

const GroupVisitTable = (props: any) => {
    const intl = useIntl();
    const {formatMessage} = intl;
    const [data, setData] = useSafeState([]);
    const auth = useAuth()
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;


    return <Table
        dataSource={props.data?.filter((value: any) => value.group?.find((item: any) => item === props.group))}
        pagination={false}
    >
        <Table.Column title={<FormattedMessage id="visit.cycle.visitNumber"/>} key="number" dataIndex="number"
                      ellipsis/>
        <Table.Column title={<FormattedMessage id="visit.cycle.window"/>} key="window" dataIndex="window" ellipsis
                      render={
                          (value, record: any) => (

                              !record.interval && !record.PeriodMin && !record.periodMax ?
                                  "-"
                                  :
                                  <>
                                      <Row>{record.interval} {DateType.find((it:any)=> it.value === record.unit)?.label}</Row>
                                      <Row>{record.PeriodMin} ~ {record.periodMax}{DateType.find((it:any)=> it.value === record.unit)?.label}</Row>
                                  </>
                          )
                      }
        />
        <Table.Column
            title={<FormattedMessage id="visit.cycle.dispensing"/>}
            key="dispensing"
            dataIndex="dispensing"
            ellipsis
            render={
                (value) => (
                    value ?
                        <OpenDiv>
                            {formatMessage({id: 'common.yes'})}
                        </OpenDiv>
                        :
                        <CloseDiv>
                            {formatMessage({id: 'common.no'})}
                        </CloseDiv>
                )
            }
        />
        <Table.Column
            title={<FormattedMessage id="visit.cycle.random"/>}
            key="random"
            dataIndex="random"
            ellipsis
            render={
                (value) => (
                    value ?
                        <OpenDiv>
                            {formatMessage({id: 'common.yes'})}
                        </OpenDiv>
                        :
                        <CloseDiv>
                            {formatMessage({id: 'common.no'})}
                        </CloseDiv>
                )
            }
        />

        {
            researchAttribute !== 1 &&
            <Table.Column
                title={<FormattedMessage id="visit.cycle.dtp"/>}
                key="dtp"
                dataIndex="dtp"
                ellipsis
                render={
                    (value) => (
                        value ?
                            <OpenDiv>
                                {formatMessage({id: 'common.yes'})}
                            </OpenDiv>
                            :
                            <CloseDiv>
                                {formatMessage({id: 'common.no'})}
                            </CloseDiv>
                    )
                }
            />
        }
        <Table.Column
            title={<FormattedMessage id="visit.cycle.replace"/>}
            key="replace"
            dataIndex="replace"
            ellipsis
            render={
                (value) => (
                    value ?
                        <OpenDiv>
                            {formatMessage({id: 'common.yes'})}
                        </OpenDiv>
                        :
                        <CloseDiv>
                            {formatMessage({id: 'common.no'})}
                        </CloseDiv>
                )
            }
        />
        <Table.Column
            title={<FormattedMessage id="drug.configure.setting.dose.form.doseAdjustment"/>}
            key="doseAdjustment"
            dataIndex="doseAdjustment"
            ellipsis
            render={
                (value) => (
                    value ?
                        <OpenDiv>
                            {formatMessage({id: 'common.yes'})}
                        </OpenDiv>
                        :
                        <CloseDiv>
                            {formatMessage({id: 'common.no'})}
                        </CloseDiv>
                )
            }
        />
    </Table>
}

const ConfirmVersion = (prop:any) => {
    const auth = useAuth()
    const gb = useGlobal();
    const drug = useDrug()
    const {formatMessage} = useIntl();
    const [form] = Form.useForm();
    const cancel = () => {
        form.resetFields()
        drug.setVersionVisible(false)
    }

    const {runAsync: pushVisitRun, loading: pushVisitLoading} = useFetch(pushVisit, {manual: true})

    const handleClick = () => {
        // 在这里定义跳转逻辑
        if(permissions(auth.project.permissions,"operation.build.medicine.configuration.list")){
          drug.setTabKey("2");
          notification.destroy();
        }
      };

    const push_visit = () => {
        form.validateFields().then(
            (value:any) => {
                pushVisitRun({id: drug.id, version:value.version, roleId: auth.project.permissions.role_id}).then(
                    (value: any) => {
                        drug.setVisitView(false)
                        drug.setVersionVisible(false)
                        drug.setPushViewType(0)
                        drug.setCanPush(true)
                        // message.success(value.msg)
                        prop.refresh();
                        form.resetFields();

                        //提示:发布成功，研究产品配置需重新关联确认访视名称，去关联
                        notification.open({
                            message: (
                              <div
                                style={{
                                  fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                  fontSize: "14px",
                                }}
                              >
                                <CheckCircleFilled 
                                  color="#2FC25B"
                                  style={{ color: "#2FC25B", paddingRight: "8px" }}
                                />
                                <span>{formatMessage({ id: "visit.cycle.management.is.group.visit.release.save" })}</span>
                                <span
                                    style={{
                                        color:"#165DFF",
                                        cursor: 'pointer', // 设置光标为手指形状
                                    }}
                                    onClick={handleClick} // 添加onClick事件处理器
                                >
                                    {formatMessage({ id: "visit.cycle.management.is.group.visit.release.save.treatmentDesign" })}
                                </span>
                              </div>
                            ),
                            // duration: 5,
                            placement: "top",
                            style: {
                              width: "720px",
                              height: gb.lang === "zh"?"55px":"75px",
                              background: "#E2F7EC",
                              borderStyle: "solid",
                              border: "1px",
                              borderColor: "#41CC82",
                              borderRadius: "4px",
                            },
                            // closeIcon: null, // 隐藏关闭按钮，用户必须手动关闭
                            duration: null, // 设置为null，让通知框不自动关闭
                          });
                    }
                )
            }
        )

    }
    return  <Modal destroyOnClose={true} width={500} title={formatMessage({id: 'visit.cycle.push.visit'})} open={drug.versionVisible} onOk={push_visit} okText={formatMessage({ id: 'common.ok' })} onCancel={cancel}
                   confirmLoading={pushVisitLoading} centered>
        <Form form={form} >
            <Form.Item label={formatMessage({id: 'visit.cycle.version.number'})} name="version" className="mar-ver-5"
                       rules={[{required: true}]}>
                <Input allowClear placeholder={formatMessage({ id: 'placeholder.input.common' })}/>
            </Form.Item>
            <span style={{color: "#677283", 
                fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                fontSize: "12px", 
                fontWeight: 400,
                lineHeight: "22px",
                letterSpacing: "0px",
                textAlign: "left",
                display: "inline-block",
                marginLeft: gb.lang === "zh" ? "96px" : "142px"
            }}
            >
                {formatMessage({id: 'visit.cycle.push.tip'})}
            </span>
        </Form>
    </Modal>
}

const OpenDiv = styled.div`
  height: 20px;
  width: 34px;
  border-radius: 2px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: center;
  background: #41CC821A;
  color: #41CC82;

`
const CloseDiv = styled.div`
  height: 20px;
  width: 34px;
  border-radius: 2px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: center;
  background: #F969641A;;
  color: #F96964;

`