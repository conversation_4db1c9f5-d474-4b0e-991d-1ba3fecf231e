import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button, Col, Row, Spin, Table, } from "antd";
import { permissions, permissionsCohort } from "../../../../tools/permission";
import moment from "moment";
import { BarcodeAdd } from "./barcode-add"
import { useFetch } from "../../../../hooks/request";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { downloadData, getBarcodeList } from "../../../../api/barcode";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";

export const BarcodeIndex = (props: any) => {
    const page = usePage();
    const [codeRule, setCodeRule] = useSafeState<any>(0);
    const [isOpenPackage, setIsOpenPackage] = useSafeState(false);
    const barcode_add = React.useRef();
    const intl = useIntl();
    const auth = useAuth()

    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const projectStatus = auth.project.status ? auth.project.status : 0
    const timeZone = (auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "" && auth.project.info.timeZoneStr !== undefined) ? Number(auth.project.info.timeZoneStr) : 8


    const { runAsync: getBarcodeListRun, loading: getBarcodeListLoading } = useFetch(getBarcodeList, { manual: true })
    const { runAsync: downloadDataRun, loading: downloadLoading } = useFetch(downloadData, { manual: true })

    const getBarcodeLists = () => {
        getBarcodeListRun({
            customerId,
            projectId,
            envId,
            cohortId,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
            roleId: auth.project.permissions.role_id
        }).then(
            (result: any) => {
                page.setTotal(result.data.total);
                page.setData(result.data.datas);
                setCodeRule(result.data.codeRule);
                setIsOpenPackage(result.data.packageIsOpen)
            }
        )
    };

    const add = () => {
        // @ts-ignore
        barcode_add.current.show();
    };
    const download = (barcodeId: any) => {
        downloadDataRun({ barcodeId }).then(() => { })
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(getBarcodeLists, [page.currentPage, page.pageSize]);
    // React.useImperativeHandle(props.bind, () => ({refresh: getBarcodeLists}));
    return (
        <React.Fragment>
            <Row gutter={8} justify="space-between" style={{ marginTop: 6, marginBottom: 16, }}>
                <Col></Col>
                <Col></Col>
                <Col>
                    {permissionsCohort(auth.project.permissions, "operation.build.medicine.barcode.add", props.cohort?.status) && projectStatus !== 2 ?
                        <Button type="primary" className="mar-lft-5" onClick={add} disabled={codeRule == 0}><FormattedMessage id="barcode.add" /></Button>
                        : null}
                </Col>
            </Row>
            <Spin spinning={getBarcodeListLoading}>
                <Table
                    className="mar-top-10"
                    size="small"
                    dataSource={page.data}
                    pagination={false}
                    rowKey={(record: any) => (record.id)}
                >
                    <Table.Column title={intl.formatMessage({ id: "common.serial" })} dataIndex="#" key="#" width={70}
                        render={(text, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)} />
                    {/*<Table.Column title={<FormattedMessage id="drug.configure.drugName"/>} key="drugName"*/}
                    {/*dataIndex="drugName"*/}
                    {/*align="center" ellipsis/>*/}
                    {/*<Table.Column title={<FormattedMessage id="drug.configure.drugSpecifications"/>} key="spec" width={250}*/}
                    {/*dataIndex="spec" align="center" ellipsis/>*/}
                    <Table.Column title={<FormattedMessage id="barcode.correlationID" />} key="correlationId" width={250}
                        dataIndex="correlationId" ellipsis />
                    <Table.Column title={<FormattedMessage id="storehouse.name" />} key="storehouseName" width={250}
                        dataIndex="storehouseName" ellipsis />
                    <Table.Column title={<FormattedMessage id="drug.list.expireDate" />} key="effectiveTime"
                        dataIndex="effectiveTime" ellipsis
                        render={
                            (value) => (
                                (value === undefined || value === null || value === 0) ? '-' : moment.unix(value).format('YYYY-MM-DD')
                            )} />
                    <Table.Column title={<FormattedMessage id="drug.list.batch" />} key="batchNumber"
                        dataIndex="batchNumber" ellipsis
                        render={
                            (value) => (
                                (value === undefined || value === null || value === "") ? '-' : value
                            )} />
                    <Table.Column title={<FormattedMessage id="barcode.count" />} key="count"
                        dataIndex="count" ellipsis />
                    <Table.Column title={<FormattedMessage id="barcode.available-count" />} key="availableCount"
                        dataIndex="availableCount" ellipsis />
                    {isOpenPackage && <Table.Column title={<FormattedMessage id="package.count" />} key="packageCount"
                        dataIndex="packageCount" ellipsis />}
                    <Table.Column title={<FormattedMessage id="common.created.at" />} key="creatTime"
                        dataIndex="creatTime" ellipsis
                        render={(value) => (
                            (value === null || value === 0) ? '' : moment.unix(value).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm'))} />
                    {
                        permissionsCohort(auth.project.permissions, "operation.build.medicine.barcode.export", props.cohort?.status) && (
                            <Table.Column
                                title={<FormattedMessage id="common.operation" />}
                                width={120}
                                render={
                                    (value, record: any) => (
                                        <Button size="small" type="link" loading={downloadLoading} onClick={() => download(record.id)}> <FormattedMessage
                                            id="common.export" /></Button>
                                    )
                                }
                            />
                        )
                    }
                </Table>
            </Spin>
            <PaginationView
                mode="SELECTABLE"
                clearDisplay={true}
                refresh={getBarcodeLists}
            />

            <BarcodeAdd bind={barcode_add} refresh={getBarcodeLists} cohort={props.cohort} />
        </React.Fragment>
    )
};