import React from "react";
import { MedicineOtherAdd } from "./medicine-other-add";
import {
    <PERSON><PERSON>, ConfigProvider, Empty, message, Row,
    Spin,
    Table,
} from "antd";
import { permissions } from "../../../../tools/permission";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import {
    deleteMedicineOther,
    getMedicineOther,
} from "../../../../api/drug_other";
import { storehouses as getStorehouses } from "../../../../api/project_storehouse";
import { useSafeState } from "ahooks";
import { CustomConfirmModal } from "../../../../components/modal";
import { InsertDivider } from "../../../../components/divider";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";
import EmptyImg from "images/empty.png";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton} from "../../../common/auth-wrap";


export const MedicineOther = (props: any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const page = usePage();
    const { formatMessage } = intl;

    // const [currentPage, setCurrentPage] = useSafeState(1);
    // const [pageSize, setPageSize] = useSafeState(20);
    // const [total, setTotal] = useSafeState(0);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [data, setData] = useSafeState<any>([]);
    const [storehouses, setStorehouses] = useSafeState<any>([]);
    const medicine_ref = React.useRef();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0;

    const { runAsync: getMedicineOtherRun, loading: getMedicineOtherLoading } =
        useFetch(getMedicineOther, { debounceWait: 300, manual: true });
    const { runAsync: deleteMedicineOtherRun } = useFetch(deleteMedicineOther, {
        debounceWait: 300,
        manual: true,
    });
    const { runAsync: getStorehousesRun, loading: getStorehousesLoading } =
        useFetch(getStorehouses, { manual: true });

    const list = () => {
        getMedicineOtherRun({
            customerId,
            projectId,
            envId,
            sendId: "",
            roleId: auth.project.permissions.role_id,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
        }).then((result: any) => {
            let data = result.data;
            if (data) {
                setPackageIsOpen(data.isOpenPackage);
                setData(fillTableCellEmptyPlaceholder(data.items ? data.items : []));
                page.setTotal(data.total);
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
        //获取仓库
        getStorehousesRun({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            let data = result.data;
            if (data != null) {
                const options = data.map((it: any) => ({
                    label: it.name,
                    value: it.id,
                }));
                setStorehouses(options);
            }
        });
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(list, [page.currentPage, page.pageSize]);

    const show = (item: any) => {
        if (item) {
            // @ts-ignore
            medicine_ref.current.show(storehouses, item);
        } else {
            // @ts-ignore
            medicine_ref.current.show(storehouses);
        }
    };
    const del = (record: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.delete" }),
            okText: formatMessage({ id: "common.ok" }),
            cancelText: formatMessage({ id: "common.cancel" }),
            onOk: () =>
                deleteMedicineOtherRun({
                    ...record,
                    envId: envId,
                }).then((data: any) => {
                    message.success(data.msg);
                    let curPage = Math.ceil((page.total - 1) / page.pageSize);
                    curPage = curPage > 1 ? curPage : 1;
                    page.setCurrentPage(curPage);
                    list();
                }),
        });
    };

    function renderStorehouseName(value: any) {
        // @ts-ignore
        return storehouses.find((it: any) => it.value === value)
            ? storehouses.find((it: any) => it.value === value).label
            : "";
    }

    return (
        <React.Fragment>
            <Spin spinning={getMedicineOtherLoading}>
                {permissions(
                    auth.project.permissions,
                    "operation.build.medicine.otherm.add"
                ) &&
                    projectStatus !== 2 && (
                        <Row
                            justify="end"
                            style={{ marginTop: 6, marginBottom: 16 }}
                        >
                            <AuthButton
                                type="primary"
                                onClick={() => {
                                    show(null);
                                }}
                            >
                                {formatMessage({ id: "common.add", allowComponent: true })}
                            </AuthButton>
                        </Row>
                    )}
                <ConfigProvider
                    renderEmpty={() => {
                        return (
                            <Empty
                                image={
                                    <img src={EmptyImg} style={{ width: 300, height: 213 }} />
                                }
                                imageStyle={{
                                    height: 240,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            />
                        );
                    }}
                >
                    <Table
                        className="mar-top-10"
                        dataSource={data}
                        pagination={false}
                        rowKey={(record: any) => record.id}
                    >
                        <Table.Column
                            title={<FormattedMessage id={"common.serial"} />}
                            dataIndex="#"
                            key="#"
                            width={70}
                            render={(text, record, index) =>
                                (page.currentPage - 1) * page.pageSize +
                                index +
                                1
                            }
                        />
                        <Table.Column
                            title={<FormattedMessage id="drug.other.name" />}
                            key="name"
                            dataIndex="name"
                            ellipsis
                        />
                        <Table.Column
                            title={<FormattedMessage id="drug.other.singleCount" />}
                            key="count"
                            dataIndex="count"
                            ellipsis
                        />
                        {packageIsOpen && <Table.Column
                            title={<FormattedMessage id="drug.other.packageCount" />}
                            key="packageCount"
                            dataIndex="packageCount"
                            ellipsis
                        />}
                        <Table.Column
                            title={<FormattedMessage id="drug.list.batch" />}
                            key="batchNumber"
                            dataIndex="batchNumber"
                            ellipsis
                        />
                        <Table.Column
                            title={
                                <FormattedMessage id="drug.list.expireDate" />
                            }
                            key="expirationDate"
                            dataIndex="expirationDate"
                            ellipsis
                        />
                        <Table.Column
                            title={<FormattedMessage id="storehouse.name" />}
                            key="storehouseId"
                            dataIndex="storehouseId"
                            ellipsis
                            render={(value, record, index) =>
                                renderStorehouseName(value)
                            }
                        />
                        {auth.project.status !== 2 ? (
                            <Table.Column
                                width={150}
                                title={
                                    <FormattedMessage id="common.operation" />
                                }
                                key="operation"
                                dataIndex="operation"
                                ellipsis
                                render={(value, record: any, index) => {
                                    const btns = [];
                                    if (
                                        permissions(
                                            auth.project.permissions,
                                            "operation.build.medicine.otherm.edit"
                                        )
                                    ) {
                                        btns.push(
                                            <AuthButton
                                                style={{ padding: 0 }}
                                                size="small"
                                                type="link"
                                                onClick={() => {
                                                    show(record);
                                                }}
                                            >
                                                <FormattedMessage id="common.edit" />
                                            </AuthButton>
                                        );
                                    }
                                    if (
                                        permissions(
                                            auth.project.permissions,
                                            "operation.build.medicine.otherm.delete"
                                        ) && record.orderIdsCount <= 0
                                    ) {
                                        btns.push(
                                            <AuthButton
                                                style={{ padding: 0 }}
                                                size="small"
                                                type="link"
                                                onClick={() => {
                                                    del(record);
                                                }}
                                            >
                                                <FormattedMessage id="common.delete" />
                                            </AuthButton>
                                        );
                                    }
                                    return InsertDivider(btns);
                                }}
                            />
                        ) : null}
                    </Table>
                </ConfigProvider>
                <PaginationView />
            </Spin>

            <MedicineOtherAdd bind={medicine_ref} refresh={list} />
        </React.Fragment>
    );
};
