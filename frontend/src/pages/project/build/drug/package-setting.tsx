import React, { cloneElement } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button, Form, message, Modal, notification, Select, Switch, Row, Col, Table, Checkbox, Tabs, } from "antd";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { useSafeState } from "ahooks";
import { PackageMixed } from "./package-mixed";
import { getDrugNames, getPackageConfigure, updatePackageConfigure, } from "../../../../api/drug";
import { useGlobal } from "../../../../context/global";
import { TipInputNumber } from "../../../../components/TipInput";
import { CheckCircleTwoTone, MinusCircleFilled, PlusOutlined, QuestionCircleFilled, InfoCircleFilled, CloseCircleFilled, } from "@ant-design/icons";
import styled from "@emotion/styled";
import { TableCellPopover } from "../../../../components/popover";
import * as _ from "lodash";
import { Divider } from "antd";

export const MedicinePacklistSetting = (props: any) => {
    const g = useGlobal();
    const auth = useAuth();
    const intl = useIntl();
    const { formatMessage } = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [submitting, setSubmitting] = useSafeState<any>(false);
    const [singleDrugNames, setSingleDrugNames] = useSafeState<any>(null);
    const [packageDrugNames, setPackageDrugNames] = useSafeState<any>(null);
    const [drugNameOptions, setDrugNameOptions] = useSafeState<any>([]);
    const [form] = Form.useForm();
    const [drugNames, setDrugNames] = useSafeState<any>([]);
    const [openDrugNames, setOpenDrugNames] = useSafeState<any>([]);
    const [otherDrugNames, setOtherDrugNames] = useSafeState<any>([]);
    const [blindDrugNames, setBlindDrugNames] = useSafeState<any>([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState<any>(false);
    const [isOpenUnProvideDate, setIsOpenUnProvideDate] = useSafeState<any>(false);
    const [isOpenApplication, setIsOpenApplication] = useSafeState<any>(false);
    const [supplyRatio, setSupplyRatio] = useSafeState<any>(false);
    const [selectedItemsIsOpenUnProvideDate, setSelectedItemsIsOpenUnProvideDate,] = useSafeState<any[]>([]);
    const [filteredOptionsIsOpenUnProvideDate, setFilteredOptionsIsOpenUnProvideDate,] = useSafeState<any[]>([]);
    const [oldPackageIsOpen, setOldPackageIsOpen] = useSafeState<any>(false);
    const [packageIsOpenDisabled, setPackageIsOpenDisabled] = useSafeState<any>(false);
    const [configureId, setConfigureId] = useSafeState<any>(null);
    const [packageData, setPackageData] = useSafeState<any[]>([]);
    const [fieldObj, setFieldObj] = useSafeState<any>(null);
    const [codeValidate, setCodeValidate] = useSafeState<any>({});
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId: any = auth.customerId;
    const package_mixed_edit = React.useRef();
    const [activeKey, setActiveKey] = useSafeState<string>("single");
    const { runAsync: getDrugNamesRun } = useFetch(getDrugNames, { manual: true, });
    const { runAsync: getPackageConfigureRun } = useFetch(getPackageConfigure, { manual: true, });

    const handleTabChange = (tabName: string) => {
        setActiveKey(tabName);
        var haveOrderOptions: any = [];
        form.getFieldValue("orderApplicationConfig").forEach((it: any) => {
            it.drugNames.forEach((names: any) => {
                haveOrderOptions.push(names.drugName);
            });
        });
        if (tabName === "single") {
            setDrugNameOptions(
                singleDrugNames.filter((o: any) => !haveOrderOptions.includes(o.value) && !otherDrugNames.includes(o.value))
            );
        } else if (tabName === "package") {
            var filterOptions: any = [];
            packageDrugNames.forEach((o: any) => {
                var haveFlag = false;
                var drugNames = o.value.split("、");
                drugNames.forEach((it: any) => {
                    if (haveOrderOptions.includes(it) || otherDrugNames.includes(it)) {
                        haveFlag = true;
                        return;
                    }
                });
                if (!haveFlag) {
                    filterOptions.push(o);
                }
            });
            setDrugNameOptions(filterOptions);
        }
    };


    const filteHaveOptions = () => {
        var haveOrderOptions: any = [];
        form.getFieldValue("orderApplicationConfig").forEach((it: any) => {
            it.drugNames.forEach((names: any) => {
                if (names.drugName !== undefined && names.drugName !== null && names.drugName !== "") {
                    haveOrderOptions.push(names.drugName);
                }
            });
        });

        if (singleDrugNames == null && packageDrugNames == null) {
            if (drugNames !== null && drugNames.length > 0) {
                setDrugNameOptions(
                    drugNames.filter((o: any) => !haveOrderOptions.includes(o.value) && !otherDrugNames.includes(o.value))
                );
            }
        } else {
            if (packageDrugNames !== null && packageDrugNames.length > 0) {
                var filterOptions: any = [];
                packageDrugNames.forEach((o: any) => {
                    var haveFlag = false;
                    var drugNames = o.value.split("、");
                    drugNames.forEach((it: any) => {
                        if (haveOrderOptions.includes(it) || otherDrugNames.includes(it)) {
                            haveFlag = true;
                        }
                    });
                    if (!haveFlag) {
                        filterOptions.push(o);
                    }
                });
                setDrugNameOptions(filterOptions);
                setActiveKey("package")
            }
            if (singleDrugNames !== null && singleDrugNames.length > 0) {
                setDrugNameOptions(
                    singleDrugNames.filter((o: any) => !haveOrderOptions.includes(o.value) && !otherDrugNames.includes(o.value))
                );
                setActiveKey("single");
            }
        }

    }

    const clickSelect = (e: any) => {
        filteHaveOptions()
    };

    // form.setFieldValue("orderApplicationConfig", [{ drugNames: [{ drugName: "药物1", number: 1 }, { drugName: "药物2", number: 2 }] }, { drugNames: [{ drugName: "药物3", number: 3 }, { drugName: "药物4", number: 4 }] }]);
    const handleSelectChange = (
        event: any,
        wrapIndex: number,
        fieldIndex: number
    ) => {
        const orderApplicationConfig = _.cloneDeep(
            form.getFieldValue("orderApplicationConfig")
        );
        var showDatas: any = [];
        if (event != null) {
            var splitDrugNames = event.split("、");
            //处理包装的数据
            if (splitDrugNames.length > 1) {
                orderApplicationConfig.forEach((it: any) => {
                    if (it.drugNames[0].drugName !== event) {
                        showDatas.push(it);
                    }
                });
                //包装的数据push
                var data: any = [];
                for (let index = 0; index < splitDrugNames.length; index++) {
                    const drugName = splitDrugNames[index];
                    data.push({ drugName: drugName });
                }

                showDatas.push({ drugNames: data });
                form.setFieldValue("orderApplicationConfig", showDatas);
            } else {
                orderApplicationConfig[wrapIndex].drugNames = [
                    {
                        drugName: event,
                        number:
                            orderApplicationConfig[wrapIndex].drugNames[fieldIndex].number,
                    },
                ];

                form.setFieldValue("orderApplicationConfig", orderApplicationConfig);
            }
        } else {
            orderApplicationConfig[wrapIndex].drugNames = [
                {
                    drugName: undefined,
                    number: undefined,
                },
            ];
            form.setFieldValue("orderApplicationConfig", orderApplicationConfig);
        }

        filteHaveOptions()


    };

    const show = () => {
        setVisible(true);
        //获取研究产品名称
        getDrugNamesRun({ customerId, envId }).then((result: any) => {
            let data = result.data;
            if (data.drugNames != null) {
                const options = data.drugNames.map((it: any) => ({
                    label: it,
                    value: it,
                }));
                setDrugNames(options);
                setBlindDrugNames(data.blindDrugNames);
                if (data.openDrugNames !== null && data.openDrugNames.length > 0) {
                    const openOptions = data.openDrugNames.map((it: any) => ({
                        label: it,
                        value: it,
                    }));
                    setOpenDrugNames(openOptions);
                } else {
                    setOpenDrugNames([]);
                }

                if (data.otherDrugNames !== null && data.otherDrugNames.length > 0) {
                    setDrugNameOptions(
                        options.filter((o: any) => !data.otherDrugNames.includes(o.value))
                    );
                    setOtherDrugNames(data.otherDrugNames);
                } else {
                    setDrugNameOptions(options)
                    setOtherDrugNames([]);
                }

                var haveOrderOptions: any = [];

                //获取配置信息
                getPackageConfigureRun({ customerId, projectId, envId }).then(
                    (result: any) => {
                        if (result.data != null) {
                            var otherDrugNames: any = [];
                            if (data.otherDrugNames !== null && data.otherDrugNames.length > 0) {
                                otherDrugNames = data.otherDrugNames
                            }
                            form.setFieldsValue(result.data);
                            if (result.data.isOpen) {
                                setPackageIsOpen(true);
                                setOldPackageIsOpen(true);
                            } else {
                                setPackageIsOpen(false);
                                setOldPackageIsOpen(false);
                            }
                            if (result.data.isOpenApplication) {
                                setIsOpenApplication(true);
                            } else {
                                setIsOpenApplication(false);
                            }
                            if (result.data.supplyRatio) {
                                setSupplyRatio(true);
                            } else {
                                setSupplyRatio(false);
                            }
                            if (result.data.orderApplicationConfig === null) {
                                form.setFieldValue("orderApplicationConfig", []);
                            } else {
                                var showDatas: any = [];
                                if (result.data.isOpenApplication && !result.data.supplyRatio) {
                                    result.data.orderApplicationConfig.forEach((it: any) => {
                                        var drugNames: any = [];
                                        it.drugNames.forEach((names: any) => {
                                            drugNames.push({ drugName: names.drugName, number: "" });
                                            haveOrderOptions.push(names.drugName);
                                        });
                                        showDatas.push({ drugNames: drugNames });
                                    });
                                    form.setFieldValue("orderApplicationConfig", showDatas);
                                }
                            }
                            if (result.data.isOpenUnProvideDate) {
                                setIsOpenUnProvideDate(true);
                            } else {
                                setIsOpenUnProvideDate(false);
                            }
                            setConfigureId(result.data.id);
                            setPackageData([...result.data?.mixedPackage]);
                            if (result.data.isOpen && result.data.mixedPackage != null) {
                                //判断比值是否允许修改，如果不允许修改的话，开关也不可以关闭
                                const haveLocked = result.data.mixedPackage.map(
                                    (it: any) => it.locked
                                );
                                if (haveLocked.includes("true")) {
                                    setPackageIsOpenDisabled(true);
                                }
                                var packageDrugNames: any = [];
                                //包装配置的所有药物
                                var packageAllDrugNames: any = [];
                                result.data?.mixedPackage.forEach((it: any) => {
                                    if (it.packageConfig.length > 0) {
                                        let drugNames: any = [];
                                        it.packageConfig.forEach((packageConfig: any) => {
                                            drugNames.push(packageConfig.name);
                                            packageAllDrugNames.push(packageConfig.name);
                                        });
                                        var nameString = _.join(drugNames, "、");
                                        packageDrugNames.push(nameString);
                                    }
                                });
                                if (packageDrugNames != null && packageDrugNames.length > 0) {
                                    const options = packageDrugNames.map((it: any) => ({
                                        label: it,
                                        value: it,
                                    }));
                                    setPackageDrugNames(options);

                                    var filterOptions: any = [];
                                    options.forEach((o: any) => {
                                        var haveFlag = false;
                                        var drugNames = o.value.split("、");
                                        drugNames.forEach((it: any) => {
                                            if (haveOrderOptions.includes(it)) {
                                                haveFlag = true;
                                                return;
                                            }
                                        });
                                        if (!haveFlag) {
                                            filterOptions.push(o);
                                        }
                                    });
                                    // setDrugNameOptions(
                                    //     filterOptions.filter((o: any) => !data.otherDrugNames.includes(o.value))
                                    // );


                                    if (data.otherDrugNames !== null && data.otherDrugNames.length > 0) {
                                        setDrugNameOptions(
                                            filterOptions.filter((o: any) => !data.otherDrugNames.includes(o.value))
                                        );
                                    } else {
                                        setDrugNameOptions(filterOptions)
                                    }

                                    //setDrugNameOptions(filterOptions);
                                }
                                //获取单品药物
                                var singleDrugNames = _.difference(
                                    data.drugNames,
                                    packageAllDrugNames
                                );
                                if (singleDrugNames != null && singleDrugNames.length > 0) {
                                    const options = singleDrugNames.map((it: any) => ({
                                        label: it,
                                        value: it,
                                    }));

                                    setSingleDrugNames(options.filter((o: any) => !otherDrugNames.includes(o.value)));
                                    // setDrugNameOptions(
                                    //     options.filter(
                                    //         (o: any) => !haveOrderOptions.includes(o.value)
                                    //     )
                                    // );
                                    setDrugNameOptions(
                                        options.filter((o: any) => !otherDrugNames.includes(o.value) && !haveOrderOptions.includes(o.value))
                                    );
                                }
                            } else {
                                // setDrugNameOptions(
                                //     options.filter((o: any) => !haveOrderOptions.includes(o.value))
                                // );

                                setDrugNameOptions(
                                    options.filter((o: any) => !otherDrugNames.includes(o.value) && !haveOrderOptions.includes(o.value))
                                );
                            }
                            if (result.data.unProvideDateConfig != null) {
                                const haveOptions = result.data.unProvideDateConfig.map(
                                    (it: any) => it.name
                                );
                                setSelectedItemsIsOpenUnProvideDate(haveOptions);
                                setFilteredOptionsIsOpenUnProvideDate(
                                    options.filter((o: any) => !haveOptions.includes(o.value))
                                );
                            }
                        }
                    }
                );
            }
        });
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        setSubmitting(false);
        setPackageIsOpen(false);
        setFieldObj([]);
        setIsOpenUnProvideDate(false);
        setPackageData([]);
        setCodeValidate({});
        setIsOpenApplication(false);
        setSupplyRatio(false);
    };

    const afterConfigureError: any = (e: any, params: any) => {
        const contentType = e.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") > -1) {
            e.json().then((result: any) => {
                if (result.code === 1000) {
                    setCodeValidate({ validateStatus: "error", help: result.msg });
                } else {
                    message.error(result.msg).then();
                }
            });
        }
    };

    const { runAsync: updatePackageConfigureRun } = useFetch(
        updatePackageConfigure,
        { manual: true, onError: afterConfigureError }
    );

    const save = () => {
        form.validateFields().then((values) => {
            var flag = true;
            //判断研究中心订单申请，是否只配置了盲态药物
            if (
                isOpenApplication &&
                values.orderApplicationConfig != null &&
                values.orderApplicationConfig.length > 0
            ) {
                let drugNames: any = [];
                _.forEach(values.orderApplicationConfig, function (value) {
                    _.forEach(value.drugNames, function (config) {
                        drugNames.push(config.drugName);
                    });
                });
                var unionDrugNames = _.union(drugNames);
                //保存失败，仅开放研究产品不适用于供应比例配置。
                var hasBlindDrug = _.intersection(unionDrugNames, blindDrugNames);
                if (hasBlindDrug.length < 1) {
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily:
                                        "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                    fontSize: "14px",
                                }}
                            >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({
                                    id: "drug.medicine.setting.application.err2",
                                })}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            background: "#FEF0EF",
                            borderRadius: "4px",
                            height: "40px",
                            borderStyle: "solid",
                            border: "1px",
                            borderColor: "#41CC82",
                            paddingTop: "10px",
                        },
                    });
                    flag = false;
                    return;
                }
                //仅添加单一盲态研究产品申请，可能会导致破盲
                if (unionDrugNames.length === 1) {
                    if (values.supplyRatio) {
                        var index = _.indexOf(blindDrugNames, unionDrugNames[0]);
                        if (index !== -1) {
                            Modal.confirm({
                                centered: true,
                                title: formatMessage({ id: "common.tips" }),
                                icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                content: formatMessage({
                                    id: "drug.medicine.setting.application.err1",
                                }),
                                onOk: () => {
                                    updatePackageConfigureRun({
                                        ...values,
                                        customerId: customerId,
                                        projectId: projectId,
                                        envId: envId,
                                        isOpen: packageIsOpen,
                                        id: configureId,
                                        mixedPackage: packageData,
                                    }).then((data: any) => {
                                        setCodeValidate({});
                                        var isOpen = form.getFieldsValue().isOpen;
                                        var messageInfo = "";
                                        if (!isOpen && oldPackageIsOpen) {
                                            //包装运输开-》关
                                            messageInfo = formatMessage({
                                                id: "drug.medicine.package.message",
                                            });
                                            //orm.resetFields();
                                            setFieldObj([]);
                                            setPackageIsOpen(isOpen);
                                        }
                                        setOldPackageIsOpen(isOpen);
                                        if (messageInfo !== "") {
                                            notification.open({
                                                message: (
                                                    <div
                                                        style={{
                                                            fontFamily:
                                                                "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                            fontSize: "14px",
                                                        }}
                                                    >
                                                        <CheckCircleTwoTone
                                                            twoToneColor="#2DA641"
                                                            style={{ paddingRight: "8px" }}
                                                        />
                                                        {formatMessage({ id: "common.success" })}
                                                    </div>
                                                ),
                                                description: (
                                                    <div
                                                        style={{
                                                            paddingLeft: "20px",
                                                            color: "#646566",
                                                        }}
                                                    >
                                                        {messageInfo}
                                                    </div>
                                                ),
                                                duration: 8,
                                                placement: "top",
                                                style: {
                                                    width: "720px",
                                                    height: "95px",
                                                    background: "#F0FAF2",
                                                    borderStyle: "solid",
                                                    border: "1px",
                                                    borderColor: "#41CC82",
                                                    borderRadius: "4px",
                                                },
                                            });
                                        } else {
                                            message.success(data.msg);
                                            setVisible(false);
                                        }
                                    });
                                },
                                cancelText: formatMessage({ id: "common.cancel" }),
                                okText: formatMessage({ id: "common.ok" }),
                            });
                            flag = false;
                            return;
                        }
                    } else {
                        notification.open({
                            message: (
                                <div
                                    style={{
                                        fontFamily:
                                            "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                        fontSize: "14px",
                                    }}
                                >
                                    <CloseCircleFilled
                                        style={{
                                            color: "#F96964",
                                            paddingRight: "8px",
                                        }}
                                    />
                                    {formatMessage({
                                        id: "drug.medicine.setting.application.err4",
                                    })}
                                </div>
                            ),
                            duration: 5,
                            placement: "top",
                            style: {
                                width: "720px",
                                background: "#FEF0EF",
                                borderRadius: "4px",
                                height: "40px",
                                borderStyle: "solid",
                                border: "1px",
                                borderColor: "#41CC82",
                                paddingTop: "10px",
                            },
                        });
                        flag = false;
                        return;
                    }
                }
                //判断供应比例是否和包装比例匹配
                if (values.supplyRatio) {
                    _.forEach(values.orderApplicationConfig, function (value) {
                        //查询匹配的配置
                        var drugNames = value.drugNames
                        if (drugNames.length > 1) { //混包的判断
                            var findDrugName = value.drugNames[0].drugName
                            _.forEach(packageData, function (packageInfo) {
                                var findIndex = _.findIndex(packageInfo.packageConfig, function (o: any) { return o.name === findDrugName; });
                                if (findIndex !== -1) { //包装配置
                                    //包装配置信息
                                    var packageMap = new Map<string, number>();
                                    _.forEach(value.drugNames, function (packageDrugNames) {
                                        packageMap.set(packageDrugNames.drugName, packageDrugNames.number)
                                    });
                                    //供应比例配置信息
                                    var supplyRatioMap = new Map<string, number>();
                                    _.forEach(packageInfo.packageConfig, function (packageConfig) {
                                        supplyRatioMap.set(packageConfig.name, packageConfig.number)
                                    });
                                    var ratio = 0
                                    packageMap.forEach((value, key) => {
                                        if (flag) {
                                            var supplyNumber: any = supplyRatioMap.get(key)
                                            var divide = value % supplyNumber
                                            if (divide === 0) {
                                                var ratio1 = value / supplyNumber
                                                if (ratio !== 0 && ratio1 !== ratio) {
                                                    notification.open({
                                                        message: (
                                                            <div
                                                                style={{
                                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                    fontSize: "14px",
                                                                }}
                                                            >
                                                                <CloseCircleFilled
                                                                    style={{
                                                                        color: "#F96964",
                                                                        paddingRight: "8px",
                                                                    }}
                                                                />
                                                                {formatMessage({ id: 'drug.medicine.setting.application.err2' })}
                                                            </div>
                                                        ),
                                                        duration: 3,
                                                        placement: "top",
                                                        style: {
                                                            width: "720px",
                                                            background: "#FEF0EF",
                                                            borderRadius: "4px",
                                                            height: "40px",
                                                            borderStyle: "solid",
                                                            border: "1px",
                                                            borderColor: "#41CC82",
                                                            paddingTop: "10px",
                                                        },
                                                    });
                                                    flag = false
                                                    return
                                                }
                                                ratio = ratio1
                                            } else {
                                                notification.open({
                                                    message: (
                                                        <div
                                                            style={{
                                                                fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                fontSize: "14px",
                                                            }}
                                                        >
                                                            <CloseCircleFilled
                                                                style={{
                                                                    color: "#F96964",
                                                                    paddingRight: "8px",
                                                                }}
                                                            />
                                                            {formatMessage({ id: 'drug.medicine.setting.application.err3' })}
                                                        </div>
                                                    ),
                                                    duration: 3,
                                                    placement: "top",
                                                    style: {
                                                        width: "720px",
                                                        background: "#FEF0EF",
                                                        borderRadius: "4px",
                                                        height: "40px",
                                                        borderStyle: "solid",
                                                        border: "1px",
                                                        borderColor: "#41CC82",
                                                        paddingTop: "10px",
                                                    },
                                                });
                                                flag = false
                                                return
                                            }
                                        }

                                    })
                                }
                            });
                        }

                    });
                }
            }

            if (flag) {
                updatePackageConfigureRun({
                    ...values,
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    isOpen: packageIsOpen,
                    id: configureId,
                    mixedPackage: packageData,
                }).then((data: any) => {
                    setCodeValidate({});
                    var isOpen = form.getFieldsValue().isOpen;
                    var messageInfo = "";
                    if (!isOpen && oldPackageIsOpen) {
                        //包装运输开-》关
                        messageInfo = formatMessage({
                            id: "drug.medicine.package.message",
                        });
                        //form.resetFields();
                        setFieldObj([]);
                        setPackageIsOpen(isOpen);
                    }
                    if (data.data.otherSpecific !== undefined && data.data.otherSpecific !== "") {
                        messageInfo = formatMessage({ id: "drug.medicine.package.other.message" }, { otherName: data.data.otherSpecific, otherCount: data.data.otherSpecificCount })
                    }
                    setOldPackageIsOpen(isOpen);
                    if (messageInfo !== "") {
                        notification.open({
                            message: (
                                <div
                                    style={{
                                        fontFamily:
                                            "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                        fontSize: "14px",
                                    }}
                                >
                                    <CheckCircleTwoTone
                                        twoToneColor="#2DA641"
                                        style={{ paddingRight: "8px" }}
                                    />
                                    {formatMessage({ id: "common.success" })}
                                </div>
                            ),
                            description: (
                                <div
                                    style={{
                                        paddingLeft: "20px",
                                        color: "#646566",
                                    }}
                                >
                                    {messageInfo}
                                </div>
                            ),
                            duration: 8,
                            placement: "top",
                            style: {
                                width: "720px",
                                height: "95px",
                                background: "#F0FAF2",
                                borderStyle: "solid",
                                border: "1px",
                                borderColor: "#41CC82",
                                borderRadius: "4px",
                            },
                        });
                    } else {
                        message.success(data.msg);
                        setVisible(false);
                    }
                });
            }
        })
            .catch(() => { });
    };

    //添加按钮
    const addPackageMixed = () => {
        const haveOptions: any = [];
        packageData.forEach((item: any) => {
            const options = item.packageConfig.map((it: any) => it.name);
            haveOptions.push(...options);
        });

        var oDrugNames: any = [];
        if (otherDrugNames !== null && otherDrugNames.length > 0) {
            const otherOptions = otherDrugNames.map((it: any) => ({
                label: it,
                value: it,
            }));
            oDrugNames = otherOptions.filter(
                (o: any) => !haveOptions.includes(o.value)
            );
        }

        var fdrugNames = openDrugNames.filter(
            (o: any) => !haveOptions.includes(o.value)
        );
        var drugNames = _.concat(oDrugNames, fdrugNames)
        // @ts-ignore
        package_mixed_edit.current.show(drugNames, blindDrugNames, otherDrugNames);
    };

    //编辑按钮
    const editPackageData = (record: any, index: any) => {
        const haveOptions: any = [];
        packageData.forEach((item: any) => {
            const options = item.packageConfig.map((it: any) => it.name);
            haveOptions.push(...options);
        });
        const otherOptions = otherDrugNames.map((it: any) => ({
            label: it,
            value: it,
        }));
        var oDrugNames = otherOptions.filter(
            (o: any) => !haveOptions.includes(o.value)
        );
        var fdrugNames = openDrugNames.filter(
            (o: any) => !haveOptions.includes(o.value)
        );
        var drugNames = _.concat(oDrugNames, fdrugNames)
        record.packageConfig.forEach((item: any) => {
            fdrugNames.push({
                label: item.name,
                value: item.name,
            });
        });
        // @ts-ignore
        package_mixed_edit.current.show(drugNames, blindDrugNames, otherDrugNames, record, index);
    };

    //删除按钮
    const delPackageData = (record: any, index: any) => {
        packageData.splice(index, 1);
        setPackageData([...packageData]);
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: { style: { width: g.lang === "en" ? "220px" : "140px" } },
    };

    const changeIsOpen = () => {
        var isOpen = form.getFieldsValue().isOpen;
        setPackageIsOpen(isOpen);
        if (!isOpen) {
            setPackageData([]);
        }
    };

    const changeUnprovide = () => {
        let isOpen = form.getFieldsValue().isOpenUnProvideDate;
        setIsOpenUnProvideDate(isOpen);
        setFilteredOptionsIsOpenUnProvideDate(drugNames);
    };

    //研究中心订单申请
    const changeApplication = () => {
        let isOpen = form.getFieldsValue().isOpenApplication;
        setIsOpenApplication(isOpen);
        if (!isOpen) { //关闭
            setSupplyRatio(false);
            form.setFieldValue("supplyRatio", false);
            form.setFieldValue("orderApplicationConfig", []);
            if (otherDrugNames !== null && otherDrugNames.length > 0) {
                setDrugNameOptions(
                    drugNames.filter((o: any) => !otherDrugNames.includes(o.value))
                );
            } else {
                setDrugNameOptions(drugNames)
            }
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue,
                orderApplicationConfig: [{}],
            });
        }
    };

    const changeSupplyRatio = () => {
        let isOpen = form.getFieldsValue().supplyRatio;
        setSupplyRatio(isOpen);
    };

    //不发放天数
    const UnProvideDate = () => {
        return (
            <>
                <Row>
                    <Col>
                        <Form.Item
                            label={formatMessage({
                                id: "projects.supplyPlan.unProvideDate",
                            })}
                            tooltip={{
                                title: formatMessage({
                                    id: "projects.supplyPlan.unProvideDate.tip",
                                }),
                                icon: <QuestionCircleFilled style={{ color: "#D0D0D0" }} />,
                            }}
                        >
                            <Row>
                                <Col>
                                    <Form.Item name="isOpenUnProvideDate" valuePropName="checked">
                                        <Switch size="small" onChange={changeUnprovide} />
                                    </Form.Item>
                                </Col>
                            </Row>
                            {isOpenUnProvideDate && (
                                <Row>
                                    <Col>
                                        <Form.Item label="" name="unProvideDateConfig">
                                            <Form.List name="unProvideDateConfig">
                                                {(fields: any, { add, remove }) => (
                                                    <>
                                                        {fields.map((field: any) => (
                                                            <Row key={field.key}>
                                                                <Form.Item
                                                                    {...field}
                                                                    name={[field.name, "name"]}
                                                                    fieldKey={[field.fieldKey, "name"]}
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message: formatMessage({
                                                                                id: "drug.configure.drugName",
                                                                            }),
                                                                        },
                                                                    ]}
                                                                    style={{ flex: 2 }}
                                                                >
                                                                    <CustomSelect
                                                                        placeholder={formatMessage({
                                                                            id: "placeholder.select.common",
                                                                        })}
                                                                        className="full-width"
                                                                        options={filteredOptionsIsOpenUnProvideDate}
                                                                        onChange={
                                                                            setSelectedItemsDataIsOpenUnProvideDate
                                                                        }
                                                                    ></CustomSelect>
                                                                </Form.Item>
                                                                <InputNumberRow>
                                                                    <Form.Item
                                                                        {...field}
                                                                        name={[field.name, "number"]}
                                                                        fieldKey={[field.fieldKey, "number"]}
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: formatMessage({
                                                                                    id: "projects.supplyPlan.unProvideDate",
                                                                                }),
                                                                            },
                                                                        ]}
                                                                    >
                                                                        <TipInputNumber
                                                                            trigger={["hover"]}
                                                                            inputClassName="full-width"
                                                                            placeholder={formatMessage({
                                                                                id: "common.required.prefix",
                                                                            })}
                                                                            precision={0}
                                                                            min={1}
                                                                            step={1}
                                                                            title
                                                                        />
                                                                    </Form.Item>
                                                                </InputNumberRow>
                                                                {fields.length > 1 && (
                                                                    <MinusCircleFilled
                                                                        style={{
                                                                            color: "#fe5b5a",
                                                                            marginLeft: "16px",
                                                                            marginRight: "16px",
                                                                        }}
                                                                        onClick={() => remove(field.name)}
                                                                    />
                                                                )}
                                                            </Row>
                                                        ))}
                                                        <Form.Item style={{ marginBottom: 0, width: 610 }}>
                                                            <Button
                                                                type="dashed"
                                                                onClick={() => add()}
                                                                block
                                                                icon={<PlusOutlined />}
                                                            >
                                                                <FormattedMessage id={"common.addTo"} />
                                                            </Button>
                                                        </Form.Item>
                                                    </>
                                                )}
                                            </Form.List>
                                        </Form.Item>
                                    </Col>
                                </Row>
                            )}
                        </Form.Item>
                    </Col>
                </Row>
            </>
        );
    };

    const setSelectedItemsDataIsOpenUnProvideDate = () => {
        const packageConfig = form.getFieldValue("unProvideDateConfig");
        if (packageConfig != null) {
            const haveOptions = packageConfig.map((it: any) => it.name);
            setSelectedItemsIsOpenUnProvideDate(haveOptions);
            setFilteredOptionsIsOpenUnProvideDate(
                drugNames.filter((o: any) => !haveOptions.includes(o.value))
            );
        }
    };

    return (
        <React.Fragment>
            <Modal
                className="custom-medium-modal"
                title={<FormattedMessage id={"common.settings"} />}
                visible={visible}
                onCancel={hide}
                maskClosable={false}
                onOk={save}
                confirmLoading={submitting}
                okText={formatMessage({ id: "common.ok" })}
                centered
            >
                <Form form={form} {...formItemLayout}>
                    {/* 不发放天数 */}
                    <UnProvideDate />
                    {/* 包装配置 */}
                    <Row style={{ marginTop: -16 }}>
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "drug.medicine.package.setting.isOpen",
                                })}
                            >
                                <Row>
                                    <Col>
                                        <Form.Item name="isOpen" valuePropName="checked">
                                            <Switch
                                                size="small"
                                                disabled={packageIsOpenDisabled}
                                                onChange={changeIsOpen}
                                            />
                                        </Form.Item>
                                    </Col>
                                    {packageIsOpen && (
                                        <span style={{ marginTop: 6, marginLeft: 12 }}>
                                            {formatMessage({
                                                id: "drug.medicine.package.setting.info",
                                            })}
                                        </span>
                                    )}
                                </Row>
                                {packageIsOpen && (
                                    <Row>
                                        <Col>
                                            <Form.Item label="" name="packageConfig">
                                                <Table
                                                    className="mar-top-5"
                                                    size="small"
                                                    dataSource={packageData}
                                                    pagination={false}
                                                    rowKey={(record) => record.id}
                                                >
                                                    <Table.Column
                                                        title={<FormattedMessage id="drug.list.name" />}
                                                        dataIndex="packageConfig"
                                                        key="packageConfig"
                                                        ellipsis
                                                        render={(value: any, record: any, index: any) => {
                                                            if (!value) return <></>;
                                                            const items: string[] = [];
                                                            (value as []).forEach((it: any) => {
                                                                items.push(it.name + "：" + it.number);
                                                            });
                                                            return (
                                                                <TableCellPopover
                                                                    title={
                                                                        <span
                                                                            style={{
                                                                                display: "flex",
                                                                                alignItems: "center",
                                                                            }}
                                                                        >
                                                                            <svg
                                                                                className="iconfont"
                                                                                width={16}
                                                                                height={16}
                                                                            >
                                                                                <use xlinkHref="#icon-quanbuxuanxing"></use>
                                                                            </svg>
                                                                            <span style={{ marginLeft: 8 }}>
                                                                                {intl.formatMessage({
                                                                                    id: "randomization.config.factor",
                                                                                })}
                                                                            </span>
                                                                        </span>
                                                                    }
                                                                    items={items}
                                                                />
                                                            );
                                                        }}
                                                    />
                                                    <Table.Column
                                                        title={
                                                            <FormattedMessage id="drug.medicine.package.mixed" />
                                                        }
                                                        dataIndex="isMixed"
                                                        key="isMixed"
                                                        ellipsis
                                                        render={(isMixed, record, index) => {
                                                            return isMixed === true
                                                                ? formatMessage({ id: "common.yes" })
                                                                : formatMessage({ id: "common.no" });
                                                        }}
                                                    />
                                                    <Table.Column
                                                        title={<FormattedMessage id="common.operation" />}
                                                        width={120}
                                                        render={(value, record: any, index) => (
                                                            <React.Fragment>
                                                                {/*{record.locked !== "true" && (*/}
                                                                <>
                                                                    <Button
                                                                        size="small"
                                                                        type="link"
                                                                        onClick={() =>
                                                                            editPackageData(record, index)
                                                                        }
                                                                    >
                                                                        <FormattedMessage id="common.edit" />
                                                                    </Button>
                                                                    <Button
                                                                        size="small"
                                                                        type="link"
                                                                        onClick={() =>
                                                                            delPackageData(record, index)
                                                                        }
                                                                    >
                                                                        <FormattedMessage id="common.delete" />
                                                                    </Button>
                                                                </>
                                                                {/*)}*/}
                                                            </React.Fragment>
                                                        )}
                                                    />
                                                </Table>
                                                <Button
                                                    type="dashed"
                                                    onClick={() => addPackageMixed()}
                                                    block
                                                    icon={<PlusOutlined />}
                                                >
                                                    <FormattedMessage id={"common.addTo"} />
                                                </Button>
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                )}
                            </Form.Item>
                        </Col>
                    </Row>
                    {/* 研究中心订单申请 */}
                    <Row style={{ marginTop: -16 }}>
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "project.setting.divider.approval.control",
                                })}
                                tooltip={{
                                    title: formatMessage({
                                        id: "project.setting.divider.approval.control.tip",
                                    }),
                                    icon: <QuestionCircleFilled style={{ color: "#D0D0D0" }} />,
                                }}
                            >
                                <Row>
                                    <Col>
                                        <Form.Item name="isOpenApplication" valuePropName="checked">
                                            <Switch size="small" onChange={changeApplication} />
                                        </Form.Item>
                                    </Col>
                                    {isOpenApplication && (
                                        <>
                                            <Divider
                                                type="vertical"
                                                style={{
                                                    border: "0.5px solid #f0f0f0",
                                                    marginTop: 10,
                                                    marginLeft: 25,
                                                }}
                                            />
                                            <Col style={{ marginLeft: 25 }}>
                                                <Form.Item name="supplyRatio" valuePropName="checked">
                                                    <Checkbox onChange={changeSupplyRatio}>
                                                        {formatMessage({
                                                            id: "drug.medicine.order.supply.ratio",
                                                        })}
                                                    </Checkbox>
                                                </Form.Item>
                                            </Col>
                                        </>
                                    )}
                                </Row>
                                {isOpenApplication && (
                                    <>
                                        <Form.List name="orderApplicationConfig">
                                            {(fields, { add, remove }) => (
                                                <div
                                                    style={{
                                                        display: "flex",
                                                        rowGap: 12,
                                                        flexDirection: "column",
                                                    }}
                                                >
                                                    {fields.map((field, wrapIndex) => (
                                                        <Row
                                                            key={field.key}
                                                            style={{
                                                                padding: "12px 16px 0 12px",
                                                                background: "#F8F9FA",
                                                                position: "relative",
                                                            }}
                                                        >
                                                            <div
                                                                style={{
                                                                    position: "absolute",
                                                                    right: "-4px",
                                                                    top: "-8px",
                                                                }}
                                                            >
                                                                {fields.length > 1 && (
                                                                    <CloseCircleFilled
                                                                        onClick={() => remove(field.name)}
                                                                        style={{ color: "#F96964" }}
                                                                    />
                                                                )}
                                                            </div>
                                                            <Form.Item style={{ margin: 0, flex: 1 }}>
                                                                <Form.List
                                                                    name={[field.name, "drugNames"]}
                                                                    initialValue={[{}]}
                                                                >
                                                                    {(subFields, subOpt) => (
                                                                        <div
                                                                            style={{
                                                                                display: "flex",
                                                                                flexDirection: "column",
                                                                            }}
                                                                        >
                                                                            {subFields.map((subField, fieldIndex) => (
                                                                                <Row key={subField.key}>
                                                                                    <Form.Item
                                                                                        {...subField}
                                                                                        name={[subField.name, "drugName"]}
                                                                                        rules={[
                                                                                            {
                                                                                                required: true,
                                                                                                message: formatMessage({
                                                                                                    id: "drug.configure.drugName",
                                                                                                }),
                                                                                            },
                                                                                        ]}
                                                                                        style={{
                                                                                            flex: 2,
                                                                                            margin: "0 0 16px 0",
                                                                                        }}
                                                                                    >
                                                                                        <CustomSelect
                                                                                            allowClear
                                                                                            placeholder={formatMessage({
                                                                                                id: "drug.configure.drugName",
                                                                                            })}
                                                                                            className="full-width"
                                                                                            onClick={(e) => clickSelect(e)}
                                                                                            onChange={(e) =>
                                                                                                handleSelectChange(
                                                                                                    e,
                                                                                                    wrapIndex,
                                                                                                    fieldIndex
                                                                                                )
                                                                                            }
                                                                                            options={drugNameOptions}
                                                                                            dropdownRender={(menu) => (
                                                                                                <>
                                                                                                    {singleDrugNames !== null &&
                                                                                                        packageDrugNames !== null &&
                                                                                                        singleDrugNames.length >
                                                                                                        0 &&
                                                                                                        packageDrugNames.length >
                                                                                                        0 && (
                                                                                                            <Tabs
                                                                                                                size="small"
                                                                                                                destroyInactiveTabPane
                                                                                                                style={{
                                                                                                                    margin:
                                                                                                                        "-10px 12px 0 12px",
                                                                                                                }}
                                                                                                                activeKey={activeKey}
                                                                                                                onChange={
                                                                                                                    handleTabChange
                                                                                                                }
                                                                                                                tabBarStyle={{
                                                                                                                    marginBottom: 0,
                                                                                                                }}
                                                                                                            >
                                                                                                                <Tabs.TabPane
                                                                                                                    tab={formatMessage({
                                                                                                                        id: "shipment.order.packageMethod.single",
                                                                                                                    })}
                                                                                                                    key="single"
                                                                                                                />
                                                                                                                <Tabs.TabPane
                                                                                                                    tab={formatMessage({
                                                                                                                        id: "shipment.order.packageMethod.package",
                                                                                                                    })}
                                                                                                                    key="package"
                                                                                                                />
                                                                                                            </Tabs>
                                                                                                        )}
                                                                                                    {cloneElement(menu)}
                                                                                                </>
                                                                                            )}
                                                                                        />
                                                                                    </Form.Item>
                                                                                    {!!supplyRatio && (
                                                                                        <InputNumberRow>
                                                                                            <Form.Item
                                                                                                {...subField}
                                                                                                name={[subField.name, "number"]}
                                                                                                rules={[
                                                                                                    {
                                                                                                        required: supplyRatio,
                                                                                                        message: formatMessage({
                                                                                                            id: "form.required",
                                                                                                        }),
                                                                                                    },
                                                                                                ]}
                                                                                                style={{ margin: 0 }}
                                                                                            >
                                                                                                <TipInputNumber
                                                                                                    disabled={!supplyRatio}
                                                                                                    trigger={["hover"]}
                                                                                                    inputClassName="full-width"
                                                                                                    placeholder={formatMessage({
                                                                                                        id: "drug.freeze.count",
                                                                                                    })}
                                                                                                    precision={0}
                                                                                                    min={1}
                                                                                                    step={1}
                                                                                                    title
                                                                                                />
                                                                                            </Form.Item>
                                                                                        </InputNumberRow>
                                                                                    )}
                                                                                </Row>
                                                                            ))}
                                                                        </div>
                                                                    )}
                                                                </Form.List>
                                                            </Form.Item>
                                                        </Row>
                                                    ))}
                                                    <Form.Item style={{ marginBottom: 0, width: 610 }}>
                                                        <Button
                                                            type="dashed"
                                                            onClick={() => add()}
                                                            block
                                                            icon={<PlusOutlined />}
                                                        >
                                                            <FormattedMessage id={"common.addTo"} />
                                                        </Button>
                                                    </Form.Item>
                                                </div>
                                            )}
                                        </Form.List>
                                    </>
                                )}
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
            <PackageMixed
                bind={package_mixed_edit}
                packageData={packageData}
                setPackageData={setPackageData}
            />
        </React.Fragment>
    );
};

const InputNumberRow = styled.div`
  margin-left: 8px;
  .ant-input-number .ant-input-number-handler-wrap,
  .ant-input-number-focused .ant-input-number-handler-wrap {
    opacity: 1;
  }
  .ant-input-number {
    border-radius: 0 2px 2px 0;
  }
`;

const CustomSelect = styled(Select)`
  .ant-select-selector {
    border-radius: 2px 0 0 2px !important;
  }
  .ant-select-item-option-active:hover {
    background: #000 !important;
  }
`;
