import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { DatePicker, Modal, Form, Input, InputNumber, message, Select, Radio, Switch } from "antd";
import moment from "moment";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { useSafeState } from "ahooks";
import { storehouses as getStorehouses } from "../../../../api/project_storehouse";
import { addBarcode, getBarcodeGroupRule } from "../../../../api/barcode";
import { useGlobal } from "../../../../context/global";
import { QuestionCircleFilled } from "@ant-design/icons";

export const BarcodeAdd = (props: any) => {

    const DatePickers: any = DatePicker

    const auth = useAuth()
    const intl = useIntl();
    const { formatMessage } = intl;
    const g = useGlobal()

    const [visible, setVisible] = useSafeState<any>(false);
    const [isPackageBarcode, setIsPackageBarcode] = useSafeState<boolean>(true);
    const [barcodeRuleDef, setBarcodeRuleDef] = useSafeState<any>(1);
    const [packageRuleDef, setPackageRuleDef] = useSafeState<any>(0);
    const [form] = Form.useForm();
    // const [drugNames, setDrugNames] = useSafeState<any>([]);
    // const [specs, setSpecs] = useSafeState<any>([]);
    const [storehouses, setStorehouses] = useSafeState<any>([]);
    // const [drugSpecs, setDrugSpecs] = useSafeState<any>(null);
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const customerId = auth.customerId;
    const [isOpenPackage, setIsOpenPackage] = useSafeState(false);
    // const {runAsync: getDrugNamesRun} = useFetch(getDrugNames, {manual: true})
    const { runAsync: storehousesRun } = useFetch(getStorehouses, { manual: true })
    const { runAsync: addBarcodeRun, loading: addBarcodeLoading } = useFetch(addBarcode, { manual: true })
    const { runAsync: getBarcodeGroupRuleRun, loading: getBarcodeGroupRuleLoading } = useFetch(getBarcodeGroupRule, { manual: true })

    const show = () => {
        //获取仓库
        storehousesRun({ customerId: customerId, projectId: projectId, envId: envId, roleId: auth.project.permissions.role_id }).then(
            (result: any) => {
                if (result.data != null) {
                    const options = result.data.map((it: any) => ({
                        label: it.name,
                        value: it.id
                    }));
                    setStorehouses(options);
                }
            }
        )
        //获取之前的生成规则
        getBarcodeGroupRuleRun({ customerId: customerId, projectId: projectId, envId: envId, cohortId: cohortId }).then(
            (result: any) => {
                if (result.data != null) {
                    var groupRule = result.data
                    setPackageRuleDef(groupRule.packageRule)
                    setBarcodeRuleDef(groupRule.barcodeRule)
                    //这儿groupRule.IsPackageBarcode的值是返回的包装配置是否打开
                    setIsOpenPackage(groupRule.isPackageBarcode)
                    form.setFieldsValue({ "barcodeRule": groupRule.barcodeRule, "packageRule": groupRule.packageRule })
                    setVisible(true);
                }
            }
        )
    };

    const hide = () => {
        setVisible(false);
        setIsPackageBarcode(true);
        form.resetFields();
    };

    const save = () => {
        form.validateFields().then((data) => {
            addBarcodeRun({ customerId, projectId, envId, cohortId }, {
                // drugName: form.getFieldsValue().drugName,
                // spec: form.getFieldsValue().spec ? form.getFieldsValue().spec : "",
                storehouseId: form.getFieldsValue().storehouseId ? form.getFieldsValue().storehouseId : "",
                effectiveTime: form.getFieldsValue().effectiveTime ? moment(form.getFieldsValue().effectiveTime).format('YYYY-MM-DD') : "",
                batchNumber: form.getFieldsValue().batchNumber ? form.getFieldsValue().batchNumber : "",
                count: form.getFieldsValue().count,
                prefix: form.getFieldsValue().prefix ? form.getFieldsValue().prefix : "",
                isPackageBarcode: isPackageBarcode,
                barcodeRule: form.getFieldsValue().barcodeRule !== undefined ? form.getFieldsValue().barcodeRule : 1,
                packageRule: form.getFieldsValue().packageRule !== undefined ? form.getFieldsValue().packageRule : 0,
            }).then(
                (data: any) => {
                    message.success(data.msg);
                    props.refresh();
                    hide();
                }
            )
        }).catch(() => { })
    };


    // const changeDrugName = (drugName:any) => {
    //     setSpecs(null);
    //     form.setFields([{name: "spec", value: null}])
    //     const drugSpec = drugSpecs[drugName];
    //     if (drugSpec != null && drugSpecs !== undefined) {
    //         const options = drugSpec.map((it:any) => ({
    //             label: it,
    //             value: it
    //         }));
    //         setSpecs(options);
    //     }
    //
    // }

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 6 : 7 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 18 : 18 },
        },
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                className="custom-small-modal"
                title={<FormattedMessage id={"barcode.add"} />}
                visible={visible}
                onCancel={hide}
                maskClosable={false}
                onOk={save}
                confirmLoading={addBarcodeLoading && getBarcodeGroupRuleLoading}
                okText={formatMessage({ id: "common.ok" })}
                centered
            >
                <Form form={form} layout="horizontal" {...formItemLayout}>
                    {/*<Form.Item label={formatMessage({id: 'drug.configure.drugName'})} name="drugName"*/}
                    {/*rules={[{required: true}]}>*/}
                    {/*<Select placeholder={formatMessage({id: 'placeholder.select.common'})} className="full-width" options={drugNames} onChange={v => changeDrugName(v)}>*/}
                    {/*</Select>*/}
                    {/*</Form.Item>*/}
                    {/*<Form.Item label={formatMessage({id: 'drug.configure.drugSpecifications'})} name="spec"*/}
                    {/*>*/}
                    {/*<Select placeholder={formatMessage({id: 'placeholder.select.common'})} className="full-width" options={specs}>*/}
                    {/*</Select>*/}
                    {/*</Form.Item>*/}
                    <Form.Item label={formatMessage({ id: 'storehouse.name' })} name="storehouseId"
                        rules={[{ required: true }]}>
                        <Select placeholder={formatMessage({ id: 'placeholder.select.common' })} className="full-width" options={storehouses}>
                        </Select>
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'drug.list.expireDate' })} name="effectiveTime"
                        rules={[{ required: true }]}>
                        <DatePickers placeholder={formatMessage({ id: 'placeholder.select.common' })} className="full-width" />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'drug.list.batch' })} name="batchNumber"
                        rules={[{ required: true }]}
                    >
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear className="full-width" />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'barcode.count' })} name="count" rules={[{ required: true }]}
                    >
                        <InputNumber placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'shortCode.prefix' })} name="prefix"
                    >
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'barcode.rule' })} name="barcodeRule"
                    >
                        <Radio.Group defaultValue={barcodeRuleDef}>
                            <Radio value={0}>{formatMessage({ id: "projects.randomization.blockRule.order" })}</Radio>
                            <Radio value={1}>{formatMessage({ id: "projects.randomization.blockRule.reverse" })}</Radio>
                        </Radio.Group>
                    </Form.Item>
                    {isOpenPackage &&
                        <>
                            <Form.Item label={formatMessage({ id: 'barcode.package' })} name="isPackageBarcode"
                                tooltip={{
                                    title: formatMessage({
                                        id: "barcode.isPackage.tip",
                                    }),
                                    icon: <QuestionCircleFilled style={{ color: "#D0D0D0" }} />,
                                }}
                            >
                                <Switch onChange={(v: any) => setIsPackageBarcode(v)} defaultChecked
                                    size={"small"}
                                ></Switch>
                            </Form.Item>
                            {isPackageBarcode && <Form.Item label={formatMessage({ id: 'barcode.package.rule' })} name="packageRule"
                            >
                                <Radio.Group defaultValue={packageRuleDef}>
                                    <Radio value={0}>{formatMessage({ id: "projects.randomization.blockRule.order" })}</Radio>
                                    <Radio value={1}>{formatMessage({ id: "projects.randomization.blockRule.reverse" })}</Radio>
                                </Radio.Group>
                            </Form.Item>}

                        </>
                    }

                </Form>
            </Modal>
        </React.Fragment >
    )
};