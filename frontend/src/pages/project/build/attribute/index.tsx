import { AttributeProvider } from "./context";
import { Attribute } from "./attribute";
import { Tabs } from "antd";
import { useAuth } from "../../../../context/auth";

export const Index = () => {
  const auth = useAuth();
  const cohorts = auth.env?.cohorts;
  const projectType = auth.project.info.type;

  return (
    <AttributeProvider>
      {projectType === 1 ? (
        <Attribute />
      ) : (
        <Tabs
          destroyInactiveTabPane={true}
          size="small"
          defaultActiveKey="1"
          tabPosition="top"
          items={cohorts.map((cohort: any, cohort_index: number) => {
            const id = cohort.id;
            return {
              label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
              key: id,
              children: <Attribute cohort={cohort} cohortId={id} />,
            };
          })}
          tabBarStyle={{ margin: "12px 24px 8px 24px" }}
        />
      )}
    </AttributeProvider>
  );
};
