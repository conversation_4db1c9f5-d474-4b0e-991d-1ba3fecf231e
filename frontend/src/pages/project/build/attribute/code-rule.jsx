import React, {useEffect} from "react";
import {Col, Form, Radio, Row} from "antd";
import {permissionsCohort} from "../../../../tools/permission";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {Title as CustomTitle} from "../../../../components/title";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";

export const CodeRule = (props) => {
  const { formatMessage } = useTranslation()
  const auth = useAuth()
  const projectStatus = auth.project.status ? auth.project.status : 0
  const [disabled, setDisabled] = useSafeState(true)

  useEffect(() => {
    const config = props.codeRuleConfig
    const hasPermission = permissionsCohort(
        auth.project.permissions,
        "operation.build.code-rule.edit",
        props.cohort ? props.cohort.status : 0
    )
    setDisabled(config.codeConfigInit || !hasPermission || projectStatus === 2)
  }, [props.codeRuleConfig])


  return (
    <React.Fragment>
      <div style={{ marginBottom: 16 }}>
        <CustomTitle name={<FormattedMessage id={"projects.attributes.code.config"} />}/>
      </div>
        <Row>
          <Col>
            <Form.Item label={<FormattedMessage id={"projects.attributes.code.config.method"} />}>
              <Radio.Group
                  disabled={disabled || props.disabled}
                  value={props.codeRuleConfig.codeRule}
                  onChange={(e) => {
                    props.onChange(e.target.value)
                  }}
              >
                <Radio key={'rule-manual'} value={0}>
                  <FormattedMessage id="projects.attributes.code.rule.manual" />
                </Radio>
                <Radio key={'rule-auto'} value={1}>
                  <FormattedMessage id="projects.attributes.code.rule.auto" />
                </Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
    </React.Fragment>
  )
}
