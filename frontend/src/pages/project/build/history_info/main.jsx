import React from "react";
import { Col, Input, Row, Table } from "antd";
import moment from "moment";
import { HistoryTable } from "../../../common/history-list-table";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { useSafeState } from "ahooks";
import { getOperationList } from "../../../../api/history";
import { usePage } from "../../../../context/page";
import DatePicker from "../../../../components/DatePicker";
import { SearchOutlined } from "@ant-design/icons";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import {useGlobal} from "../../../../context/global";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";

export const HistoryListMain = (props) => {
    const g = useGlobal()
    const auth = useAuth()
    const page = usePage()
    const intl = useTranslation();
    const { formatMessage } = intl;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
    const { runAsync, loading } = useFetch(getOperationList, { manual: true })
    const DatePickers = DatePicker
    const [startTime, setStartTime] = useSafeState(null);
    const [endTime, setEndTime] = useSafeState(null);
    const [search, setSearch] = useSafeState(false);


    const getList = (info) => {
        let id = envId
        runAsync({
            oid: id,
            projectId: projectId,
            envId: envId,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
            // attributeId: auth.attribute.id,
            startTime,
            endTime,
            module: "operation_log.module.barcode,operation_log.module.barcode_add,operation_log.module.supply,operation_log.module.supply_detail,operation_log.module.random_design,operation_log.module.project_site,operation_log.module.project_storehouse,operation_log.module.project_storehouse_medicine_alert,operation_log.module.visitCycle,operation_log.module.visitSetting,"
                + "operation_log.module.attribute,operation_log.module.drug_configure,operation_log.module.barcoed_add,operation_log.module.medicinesList_uploadMedicine,operation_log.module.medicinesList_uploadPacklist,operation_log.module.medicinesList_delete,"
                + "operation_log.module.updateBatch,operation_log.module.otherMedicines,operation_log.module.form,operation_log.module.simulate_random,operation_log.module.push,operation_log.module.region,operation_log.module.notifications,operation_log.module.configure_export,operation_log.module.package_configure,operation_log.module.project_basic_information,operation_log.module.subjects,operation_log.module.drug_configure_setting,"
                + "operation_log.module.examine,operation_log.module.update,operation_log.module.release,operation_log.module.barcode_label",
            roleId: auth.project.permissions.role_id,
            keyword: info ? info.trim() : ""
        }).then((result) => {
            let data = result.data
            page.setTotal(data.total);
            page.setData(fillTableCellEmptyPlaceholder(data.items));
        });

    }


    const onChange = (type, dateString) => {
        let offset_GMT = new Date().getTimezoneOffset();
        let time = moment(dateString).valueOf() / 1000 - offset_GMT * 60 - timeZone * 60 * 60
        if (type === "start") {
            setStartTime(time)
        }
        if (type === "end") {
            setEndTime(time)
        }

    }

    const searchList = () => {
        getList(search)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(getList, [page.currentPage, page.pageSize, startTime, endTime,g.lang]);
    let componentRef = React.useRef();
    return (
        <>

            <Row justify={"space-between"}>
                <div style={{ width: "200px", display: "inline-block" }}>
                    <i className="iconfont icon-quanbuguiji" style={{ marginRight: 8 }}></i>
                    <span style={{ color: "#1D2129", fontWeight: 600 }}>{formatMessage({ id: "history.all", allowComponent: true })} </span>
                </div>
            </Row>
            <Row>
                <div style={{ textAlign: "left", display: "inline-block", marginTop: "16px", marginBottom: "16px" }}>
                    <Col style={{ textAlign: "left" }}>
                        <FormattedMessage id="common.time" />：
                        <DatePickers placeholder={formatMessage({ id: "common.time.start" })} style={{ width: "200px" }} showTime onChange={(value, dateString) => { onChange("start", dateString, value) }} /> <span style={{ padding: "4px" }}>~</span> <DatePickers placeholder={formatMessage({ id: "common.time.end" })} style={{ width: "200px" }} showTime onChange={(value, dateString) => { onChange("end", dateString) }} />
                        <Input style={{ width: "200px", marginLeft: "12px" }}
                            onChange={(value) => { setSearch(value.target.value) }}
                            placeholder={formatMessage({ id: "history.user.search" })}
                            suffix={
                                <div>
                                    <SearchOutlined onClick={searchList} style={{ color: "#BFBFBF" }} />
                                </div>
                            }
                        />
                    </Col>
                </div>
            </Row>
            <Table
                loading={loading}
                className="mar-top-10"
                dataSource={page.data}
                scroll={{ x: 'max-content', y: 'calc(100vh - 230px)' }}
                pagination={false}
                rowKey={(record) => (record.id)}
            >
                <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={100} ellipsis
                    render={(text, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)} />
                <Table.Column title={<FormattedMessage id="common.operator" />} key="user" dataIndex="user"
                    width={350} align="left" ellipsis
                    render={
                        (value, record, index) => (
                            record.user_name
                        )
                    } />
                <Table.Column
                    title={<FormattedMessage id="common.operation.time" />}
                    key="time_str"
                    dataIndex="time_str"
                    align="left"
                    width={200}
                    ellipsis
                />
                <Table.Column title={<FormattedMessage id="common.operation.type" />} key="operation_type"
                    dataIndex="operation_type" align="left" ellipsis width={150}
                />
                <Table.Column title={<FormattedMessage id="common.operation.content" />} key="fields"
                    dataIndex="fields" align="left" ellipsis
                    render={
                        (value, record, index) => (
                            value && value.map(item =>
                                <Row key={item}>{item}</Row>
                            )
                        )
                    }
                />
            </Table>

            <div
                style={{ display: "none" }}
            >
                <div
                    ref={el => (componentRef = el)}>
                    <HistoryTable keys="menu.projects.project.build.history" data={page.data} loading={loading} />
                </div>
            </div>
        </>
    );
};