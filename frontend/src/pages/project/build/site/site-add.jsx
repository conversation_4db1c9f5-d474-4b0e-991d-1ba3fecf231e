import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import {
    Button,
    Cascader,
    Checkbox,
    Col,
    Divider,
    Form,
    Input,
    message,
    Modal,
    notification,
    Radio,
    Row,
    Select,
    Space,
    Spin,
    Switch,
    Tabs
} from "antd";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { useFetch } from "../../../../hooks/request";
import { getApplicableSiteSupplyPlan } from "../../../../api/supply_plan";
import { add, getBatchGroupNum, getProjectSiteByTz, getStoreListOption } from "../../../../api/project_site";
import { getSiteConfig } from "../../../../api/site";
import { useGlobal } from "../../../../context/global";
import styled from "@emotion/styled";
import { CustomConfirmModal } from "../../../../components/modal";
import { nilObjectId } from "../../../../data/data";
import { TipInput, TipInputNumber } from "components/TipInput";
import { CloseCircleFilled, InfoCircleFilled, MinusCircleFilled, PlusOutlined } from '@ant-design/icons';
import { getProjectAttributes, getRegion } from "../../../../api/randomization";
import { getDrugNames, getIsBlindedRole, getPackageConfigure } from "../../../../api/drug";
import { roleIsBind } from "../../../../api/roles";
import * as _ from "lodash";
import { BatchGroupSite } from "./batch_group_site";


export const SiteAdd = (props) => {
    const auth = useAuth();
    const intl = useIntl();
    const { formatMessage } = intl;
    const [form] = Form.useForm();
    const g = useGlobal()
    const [visible, setVisible] = useSafeState(false);
    const [id, setId] = useSafeState(null);
    const [blindDrugNames, setBlindDrugNames] = useSafeState([]);
    const [singleDrugNames, setSingleDrugNames] = useSafeState(null);
    const [packageDrugNames, setPackageDrugNames] = useSafeState(null);
    const [packageData, setPackageData] = useSafeState([]);
    const [drugNameOptions, setDrugNameOptions] = useSafeState([]);
    const [siteConfigData, setSiteConfigData] = useSafeState([]);
    const [siteSupplyPlanData, setSiteSupplyPlanData] = useSafeState([]);
    const [storehousesData, setStorehousesData] = useSafeState([]);
    const [isOpenOrderSupplyRatio, setIsOpenOrderSupplyRatio] = useSafeState(false);
    const [orderApplicationConfig, setOrderApplicationConfig] = useSafeState([]);
    const [checkStatus, setCheckStatus] = useSafeState(false);
    const projectId = auth.project ? auth.project.id : null;
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;
    const customerId = auth.customerId;
    const envId = auth.env ? auth.env.id : null;
    const [timeZone, setTimeZone] = useSafeState("");
    const [tz, setTz] = useSafeState("");
    const [locationStr, setLocationStr] = useSafeState("")
    const [dmpId, setDmpId] = useSafeState("")
    const [aliSiteId, setAliSiteId] = useSafeState("")
    const [zh, setZh] = useSafeState("")
    const [en, setEn] = useSafeState("")
    const [countries, setCountries] = useSafeState([]);
    const [supplyRatio, setSupplyRatio] = useSafeState(false);
    const [isBlindRole, setIsBlindRole] = useSafeState(false);
    const [regions, setRegions] = useSafeState([])
    const [batchLabel, setBatchLabel] = useSafeState([])
    const [groupLabel, setGroupLabel] = useSafeState([])
    const [batchGroupAlarm, setBatchGroupAlarm] = useSafeState(false)
    const [IPInheritance, setIPInheritance] = useSafeState(false);
    const [regionLayeredFlag, setRegionLayeredFlag] = useSafeState(null);
    const { runAsync: run_getStoreListOption, loading: getStoreListOptionLoading } = useFetch(getStoreListOption, { manual: true });
    const { runAsync: run_add, loading: submitting } = useFetch(add, { manual: true });
    const { runAsync: getApplicableSiteSupplyPlanRun, loading: getApplicableSiteSupplyPlanLoading } = useFetch(getApplicableSiteSupplyPlan, { manual: true })
    const { runAsync: run_getSiteConfig, loading: getSiteConfigLoading } = useFetch(getSiteConfig, { manual: true });
    const { runAsync: getRegionRun } = useFetch(getRegion, { manual: true })
    const { runAsync: run_getProjectSiteByTz } = useFetch(getProjectSiteByTz, { manual: true });
    const { runAsync: getDrugNamesRun } = useFetch(getDrugNames, { manual: true })
    const { runAsync: getPackageConfigureRun } = useFetch(getPackageConfigure, { manual: true });
    const { runAsync: getBatchGroupNumRun, loading: getBatchGroupNumLoading } = useFetch(getBatchGroupNum, { manual: true });
    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);
    const [blindRole, setBlindRole] = useSafeState(true);
    const { runAsync: getIsBlindedRoleRun, loading: getIsBlindedRoleLoading } = useFetch(
        getIsBlindedRole,
        { manual: true }
    );
    const changeCountry = (o) => {
        setDmpId(o.dmpId)
        // setAliSiteId(o.aliSiteId)
        setZh(o.zh)
        setEn(o.en)
        if (o.timeZone) {
            setTimeZone(o.timeZone)
        }
        if (o.tz) {
            setTz(o.tz);
            getTz(o.tz, o.timeZone);
        }
        form.setFieldsValue({ country: o.countryCode });
        formChange();
    }

    const handleTabChange = (tabName) => {
        var haveOrderOptions = []
        form.getFieldValue("orderApplicationConfig").forEach((it) => {
            it.drugNames.forEach((names) => {
                haveOrderOptions.push(names.drugName)
            })
        });
        if (tabName === "single") {
            setDrugNameOptions(
                singleDrugNames.filter(
                    (o) =>
                        !haveOrderOptions.includes(o.value)
                )
            );
        } else if (tabName === "package") {
            var filterOptions = []
            packageDrugNames.forEach((o) => {
                var haveFlag = false
                var drugNames = o.value.split("、");
                drugNames.forEach((it) => {
                    if (haveOrderOptions.includes(it)) {
                        haveFlag = true
                        return
                    }
                });
                if (!haveFlag) {
                    filterOptions.push(o)
                }
            }
            )
            setDrugNameOptions(filterOptions);
        }
    };

    const clickSelect = (e) => {
        var haveOrderOptions = []
        form.getFieldValue("orderApplicationConfig").forEach((it) => {
            it.drugNames.forEach((names) => {
                haveOrderOptions.push(names.drugName)
            })
        });
        if (singleDrugNames !== null && singleDrugNames.length > 0) {
            setDrugNameOptions(
                singleDrugNames.filter(
                    (o) =>
                        !haveOrderOptions.includes(o.value)
                )
            );
        }
    }

    const formChange = () => {
        if (id) {
            const a = _.cloneDeep(oldData);
            let storehouseId = null;
            if (a.storehouseId && a.storehouseId !== nilObjectId && a.storehouseId[0] !== nilObjectId) {
                storehouseId = a.storehouseId[0];
            }
            a.storehouseId = storehouseId;
            let supplyPlanId = null;
            if (a.supplyPlanId && a.supplyPlanId !== nilObjectId) {
                supplyPlanId = a.supplyPlanId;
            }
            a.supplyPlanId = supplyPlanId;
            let supplyRatio = false;
            if (a.supplyRatio && a.supplyRatio !== undefined) {
                supplyRatio = a.supplyRatio;
            }
            a.supplyRatio = supplyRatio;
            let regionId = null;
            if (a.regionId && a.regionId !== nilObjectId) {
                regionId = a.regionId;
            }
            a.regionId = regionId;
            let active = a.active === 2;
            a.active = active;
            if (a.contactGroup === undefined || a.contactGroup === null) {
                a.contactGroup = [];
            } else {
                // 声明一个新数组来存储修改后的对象
                let modifiedArray = [];
                // 遍历原始数组 a.contactGroup
                for (let i = 0; i < a.contactGroup.length; i++) {
                    // 获取当前对象
                    let currentObj = a.contactGroup[i];
                    // 修改 isdefault 属性
                    if (currentObj.isdefault === 1) {
                        currentObj.isdefault = true;
                    } else if (currentObj.isdefault === 0) {
                        currentObj.isdefault = false;
                    }
                    // 将修改后的对象添加到新数组中
                    modifiedArray.push(currentObj);
                }
                a.contactGroup = modifiedArray;
            }
            // console.log("1===" + JSON.stringify(a)); 
            const b = _.cloneDeep(form.getFieldsValue());
            if (b.contactGroup === undefined || b.contactGroup === null) {
                b.contactGroup = [];
            } else {
                // 声明一个新数组来存储修改后的对象
                let modifiedArray = [];
                // 遍历原始数组 a.contactGroup
                for (let i = 0; i < b.contactGroup.length; i++) {
                    // 获取当前对象
                    let currentObj = b.contactGroup[i];
                    // 修改 isdefault 属性
                    if (currentObj.isdefault === 1) {
                        currentObj.isdefault = true;
                    } else if (currentObj.isdefault === 0) {
                        currentObj.isdefault = false;
                    }
                    // 将修改后的对象添加到新数组中
                    modifiedArray.push(currentObj);
                }
                b.contactGroup = modifiedArray;
            }
            let bStorehouseId = null
            if (b.storehouseId && b.storehouseId !== nilObjectId && b.storehouseId !== nilObjectId) {
                bStorehouseId = b.storehouseId;
            }
            b.storehouseId = bStorehouseId;
            let bSupplyPlanId = null;
            if (b.supplyPlanId && b.supplyPlanId !== nilObjectId) {
                bSupplyPlanId = b.supplyPlanId;
            }
            b.supplyPlanId = bSupplyPlanId;
            let bRegionId = null;
            if (b.regionId && b.regionId !== nilObjectId) {
                bRegionId = b.regionId;
            }
            b.regionId = bRegionId;
            let bName = null;
            if (b.name && b.name !== undefined) {
                bName = b.name;
            }
            b.name = bName
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1, obj2) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (key === "orderApplicationConfig") {
                        if (!arrayIsEqual(obj1[key], obj2[key])) {
                            return false;
                        }
                    } else {
                        if (!arraysAreEqual(obj1[key], obj2[key])) {
                            return false;
                        }
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }


    function arrayIsEqual(arr1, arr2) {

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 深拷贝并排序第一个数组
        const sortedArr1 = JSON.parse(JSON.stringify(convertToCamelCase(a))).sort();
        // 深拷贝并排序第二个数组
        const sortedArr2 = JSON.parse(JSON.stringify(b)).sort();


        // console.log("1===" + JSON.stringify(sortedArr1)); 
        // console.log("2===" + JSON.stringify(sortedArr2)); 

        if (sortedArr1.length !== sortedArr2.length) {
            return false;
        }

        for (let i = 0; i < sortedArr1.length; i++) {
            if (!objectIsEqual(sortedArr1[i], sortedArr2[i])) {
                return false;
            }
        }

        return true;
    }

    function convertToCamelCase(obj) {
        if (Array.isArray(obj)) {
            return obj.map(item => convertToCamelCase(item));
        } else if (typeof obj === 'object' && obj !== null) {
            const newObj = {};
            for (let key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    const newKey = key.replace(/_([a-z])/g, (match, char) => char.toUpperCase());
                    newObj[newKey] = convertToCamelCase(obj[key]);
                }
            }
            return newObj;
        } else {
            return obj;
        }
    }

    function objectIsEqual(obj1, obj2) {
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);

        // console.log("1==" + JSON.stringify(keys1));
        // console.log("2==" + JSON.stringify(keys2));
        if (keys1.length !== keys2.length) {
            return false;
        }

        for (let key of keys1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arrayIsEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }

        return true;
    }


    //比较两个数组是否相同
    function arraysAreEqual(arr1, arr2) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());
        return str1 === str2;
    }

    // form.setFieldValue("orderApplicationConfig", [{ drugNames: [{ drugName: "药物1", number: 1 }, { drugName: "药物2", number: 2 }] }, { drugNames: [{ drugName: "药物3", number: 3 }, { drugName: "药物4", number: 4 }] }]);
    const handleSelectChange = (event, field) => {
        var orderApplicationConfig = form.getFieldValue("orderApplicationConfig")
        var showDatas = []
        if (event != null) {
            var splitDrugNames = event.split("、")
            //处理包装的数据
            if (splitDrugNames.length > 1) {
                orderApplicationConfig.forEach((it) => {
                    if (it.drugNames[0].drugName !== event) {
                        showDatas.push(it)
                    }
                });
                //包装的数据push
                var data = []
                for (let index = 0; index < splitDrugNames.length; index++) {
                    const drugName = splitDrugNames[index];
                    data.push({ drugName: drugName })
                }
                showDatas.push({ drugNames: data })
                form.setFieldValue("orderApplicationConfig", showDatas);
            }
        } else {
            orderApplicationConfig.forEach((it) => {
                if (it.drugNames[0].drugName !== null) {
                    showDatas.push(it)
                } else {
                    showDatas.push({ drugNames: [{ drugName: "", number: "" }] })
                }
            });
            form.setFieldValue("orderApplicationConfig", showDatas);
        }
    };

    const languageHandler = (lang) => {
        let language = "en";
        switch (lang) {
            case 'en':
                language = "en";
                break;
            case 'zh':
                language = "cn";
                break;
            case 'ko':
                language = "ko";
                break;
        }
        return language
    };

    //获取研究产品名称
    const getDrugName = () => {
        //获取研究产品名称
        getDrugNamesRun({ customerId, envId, types: "2" }).then((result) => {
            let data = result.data;
            //  console.log(data);
            if (data.drugNames != null) {
                const options = data.drugNames.map((it) => ({
                    label: it,
                    value: it,
                }));
                setBlindDrugNames(data.blindDrugNames);
                setDrugNameOptions(options)

                var haveOrderOptions = []

                //获取配置信息
                getPackageConfigureRun({ customerId, projectId, envId }).then(
                    (result) => {
                        if (result.data != null) {
                            setPackageData([...result.data?.mixedPackage]);
                            if (result.data.isOpenApplication && result.data.supplyRatio) {
                                setIsOpenOrderSupplyRatio(true)
                            } else {
                                setIsOpenOrderSupplyRatio(false)
                            }
                            if (result.data.orderApplicationConfig === null) {
                                setOrderApplicationConfig([]);
                            } else {
                                var showDatas = []
                                if (result.data.isOpenApplication && !result.data.supplyRatio) {
                                    result.data.orderApplicationConfig.forEach((it) => {
                                        var drugNames = []
                                        it.drugNames.forEach((names) => {
                                            drugNames.push({ drugName: names.drugName, number: "" })
                                        })
                                        showDatas.push({ drugNames: drugNames })
                                    });
                                    setOrderApplicationConfig(showDatas);
                                } else {
                                    setOrderApplicationConfig(result.data.orderApplicationConfig);
                                }
                            }
                            if (
                                result.data.isOpen &&
                                result.data.mixedPackage != null
                            ) {
                                var packageDrugNames = []
                                //包装配置的所有药物
                                var packageAllDrugNames = []
                                result.data?.mixedPackage.forEach((it) => {
                                    if (it.packageConfig.length > 0) {
                                        let drugNames = [];
                                        it.packageConfig.forEach((packageConfig) => {
                                            drugNames.push(packageConfig.name)
                                            packageAllDrugNames.push(packageConfig.name)
                                        })
                                        var nameString = _.join(drugNames, "、")
                                        packageDrugNames.push(nameString)
                                    }
                                });
                                if (packageDrugNames != null) {
                                    const options = packageDrugNames.map((it) => ({
                                        label: it,
                                        value: it
                                    }));
                                    setPackageDrugNames(options)

                                    var filterOptions = []
                                    options.forEach((o) => {
                                        var haveFlag = false
                                        var drugNames = o.value.split("、");
                                        drugNames.forEach((it) => {
                                            if (haveOrderOptions.includes(it)) {
                                                haveFlag = true
                                                return
                                            }
                                        });
                                        if (!haveFlag) {
                                            filterOptions.push(o)
                                        }
                                    }
                                    )
                                    setDrugNameOptions(filterOptions);
                                }
                                //获取单品药物
                                var singleDrugNames = _.difference(data.drugNames, packageAllDrugNames)
                                if (singleDrugNames != null) {
                                    const options = singleDrugNames.map((it) => ({
                                        label: it,
                                        value: it
                                    }));
                                    setSingleDrugNames(options)
                                    setDrugNameOptions(
                                        options.filter(
                                            (o) =>
                                                !haveOrderOptions.includes(o.value)
                                        )
                                    );
                                }
                            }
                        }
                    }
                );
            }
        });

    };

    //获取研究中心订单申请，是否打开供应比例，比例的配置
    const getOrderApplicationConfig = () => {
        //获取配置信息
        getPackageConfigureRun({ customerId, projectId, envId }).then(
            (result) => {
                if (result.data != null) {
                    if (result.data.isOpenApplication && result.data.supplyRatio) {
                        setIsOpenOrderSupplyRatio(true)
                    } else {
                        setIsOpenOrderSupplyRatio(false)
                    }
                    if (result.data.orderApplicationConfig === null) {
                        setOrderApplicationConfig([]);
                    } else {
                        var showDatas = []
                        if (result.data.isOpenApplication && !result.data.supplyRatio) {
                            result.data.orderApplicationConfig.forEach((it) => {
                                var drugNames = []
                                it.drugNames.forEach((names) => {
                                    drugNames.push({ drugName: names.drugName, number: "" })
                                })
                                showDatas.push({ drugNames: drugNames })
                            });
                            setOrderApplicationConfig(showDatas);
                        } else {
                            setOrderApplicationConfig(result.data.orderApplicationConfig);
                        }
                    }
                }
            }
        );

    }

    const { runAsync: run_isBlindRole } = useFetch(roleIsBind, { manual: true })
    const isBindRoleBool = () => {
        run_isBlindRole({
            customerId: customerId,
            envId: envId,
            projectId: projectId,
            roleId: auth.project.permissions.role_id
        }).then(
            (result) => {
                const data = result.data
                setIsBlindRole(data);
            }
        )
    };


    const { runAsync: runGetProjectAttributesAttr } = useFetch(getProjectAttributes, { manual: true });

    const show = (data, countries, regionLayeredFlag) => {
        isBindRoleBool();
        setSend(true);
        getDrugName();
        //查询项目的配置
        // getOrderApplicationConfig();
        getSupplyPlan(data);
        getStorehouses();
        getRegions();
        siteConfig();
        setCountries(countries)
        setRegionLayeredFlag(regionLayeredFlag)

        runGetProjectAttributesAttr({ env: auth.env.id }).then(
            (res) => {
                if (res?.data) {
                    setIPInheritance(res?.data?.find((item) => item.info.IPInheritance === true))
                }
            }
        )

        if (data) {
            getIsBlindedRoleRun({
                roleId: auth.project.permissions.role_id,
            }).then((result) => {
                setBlindRole(result.data)
            });

            getBatchGroupNumFun(data.id);
            setOldData(data);
            setAliSiteId(data.aliSiteId)
            setZh(data.cn)
            setEn(data.en)
            setTimeZone(data.time_zone)
            setTz(data.tz);
            getTz(data.tz, data.time_zone);
            // setCheckStatus()
            setId(data.id);
            setSend(true);
            let storehouseId = null
            if (data.storehouseId && data.storehouseId !== nilObjectId && data.storehouseId[0] !== nilObjectId) {
                storehouseId = data.storehouseId[0]
            }
            let supplyPlanId = null
            if (data.supplyPlanId && data.supplyPlanId !== nilObjectId) {
                supplyPlanId = data.supplyPlanId
            }
            let regionId = null
            if (data.regionId && data.regionId !== nilObjectId) {
                regionId = data.regionId
            }
            setCheckStatus(data.active === 2)
            form.setFieldsValue({
                ...data,
                storehouseId: storehouseId,
                supplyPlanId: supplyPlanId,
                regionId: regionId,
                active: data.active === 2
            });
            setSupplyRatio(data.supplyRatio)
            if (data.supplyRatio && data.orderApplicationConfig != null && data.orderApplicationConfig.length > 0) {
                var showDatas = []
                data.orderApplicationConfig.forEach((it) => {
                    var drugNames = []
                    it.drug_names.forEach((names) => {
                        drugNames.push({ drugName: names.drug_name, number: names.number })
                    })
                    showDatas.push({ drugNames: drugNames })
                });
                form.setFieldValue("orderApplicationConfig", showDatas);
            }
            setBatchGroupAlarm(data.batchGroupAlarm)
        } else {
            form.setFieldsValue({ active: false, deleted: 2 });
        }
        setVisible(true);
    };


    const siteConfig = () => {
        run_getSiteConfig({}).then((result) => setSiteConfigData(result.data))
    }

    const getSupplyPlan = (data) => {
        let siteId = nilObjectId
        if (data) {
            siteId = data.id
        }
        getApplicableSiteSupplyPlanRun({ envId: envId, siteId: siteId }).then(
            (result) => {
                setSiteSupplyPlanData(result.data.optionalSupplyPlan)
            }
        )
    };

    //change供应比例
    const changeSupplyRatio = () => {
        let isOpen = form.getFieldsValue().supplyRatio;
        setSupplyRatio(isOpen);
        if (isOpen) {
            form.setFieldValue("orderApplicationConfig", orderApplicationConfig)
        } else {
            form.setFieldValue("orderApplicationConfig", [])
        }
    }


    const getStorehouses = () => {
        const params = {
            "projectId": projectId,
            "customerId": customerId,
            "envId": envId,
        };
        run_getStoreListOption({ ...params }).then(
            (result) => {
                setStorehousesData(result.data)
            }
        )
    };

    const getTz = (value, s) => {
        run_getProjectSiteByTz({
            tz: value,
            timeZone:s,
        }).then((result) => {
            setLocationStr(result.data)
        })
    };

    const getBatchGroupNumFun = (id) => {
        getBatchGroupNumRun({ projectId: projectId, envId: envId, siteId: id }).then(
            (res) => {
                setBatchLabel(res.data.batch ? res.data.batch : [])
                setGroupLabel(res.data.group ? res.data.group : [])
            }
        )
    }


    const getRegions = () => {
        getRegionRun({
            envId: envId,
        })
            .then((result) => setRegions(result.data))
    };

    const hide = () => {
        setIsOpenOrderSupplyRatio(false)
        setSupplyRatio(false)
        setCheckStatus(false)
        setDmpId("")
        setAliSiteId("")
        setZh("")
        setEn("");
        setOldData(null);
        setSend(true);
        setTimeZone("");
        setTz("");
        setLocationStr(null);
        setVisible(false);
        form.resetFields();
        setId(null);
        // setSiteConfigData([]);
        setSiteSupplyPlanData([]);
        setStorehousesData([]);
        setBatchLabel([])
        setGroupLabel([])
    };

    const save = () => {
        form.validateFields().then(values => {
            if (values.deleted === 1) {
                CustomConfirmModal({
                    title: formatMessage({ id: 'tip.site.disable.title' }),
                    content: formatMessage({ id: 'tip.site.disable' }),
                    okText: formatMessage({ id: 'common.ok' }),
                    onOk: () => {
                        saveForm(values)
                    }
                });
            } else {
                saveForm(values)
            }
        })
    }

    const changeContactDefault = (index) => {
        var contactGroup = form.getFieldValue("contactGroup");
        for (let i = 0; i < contactGroup.length; i++) {
            if (i !== index) {
                contactGroup[i].isdefault = 0
            } else {
                if (contactGroup[index].isdefault === true) {
                    contactGroup[i].isdefault = 1
                } else {
                    contactGroup[i].isdefault = 0
                }
            }
        }
        form.setFieldValue("contactGroup", contactGroup)
    }

    //保存
    const saveForm = (values) => {
        //过滤联系人信息
        let contactGroup = [];
        if (values.contactGroup !== undefined && values.contactGroup != null && values.contactGroup.length > 0) {
            contactGroup = values.contactGroup.filter(
                (it) => (it.contacts !== undefined && it.contacts !== "") || (it.phone !== undefined && it.phone !== "") || (it.email !== undefined && it.email !== "") || (it.address !== undefined && it.address !== ""),
            );
        }

        //判断有效的联系人数据中，是否选择了默认联系人
        if (contactGroup.length > 0) {
            var haveDefalut = contactGroup.some(contact => contact.isdefault === 1)
            if (!haveDefalut) {
                message.error(formatMessage({ id: 'drug.medicine.setting.application.err5' }));
                return
            }
        }


        var flag = true;
        //判断研究中心订单申请，是否只配置了盲态药物
        if (values.supplyRatio && values.orderApplicationConfig != null && values.orderApplicationConfig.length > 0) {
            let drugNames = []
            _.forEach(values.orderApplicationConfig, function (value) {
                _.forEach(value.drugNames, function (config) {
                    drugNames.push(config.drugName)
                });
            });
            var unionDrugNames = _.union(drugNames)
            //保存失败，仅开放研究产品不适用于供应比例配置。
            var hasBlindDrug = _.intersection(unionDrugNames, blindDrugNames)
            if (hasBlindDrug.length < 1) {
                message.error(formatMessage({ id: 'drug.medicine.setting.application.err2' }));
                return
            }
            //仅添加单一盲态研究产品申请，可能会导致破盲
            if (unionDrugNames.length === 1) {
                if (values.supplyRatio) {
                    var index = _.indexOf(blindDrugNames, unionDrugNames[0]);
                    if (index !== -1) {
                        Modal.confirm({
                            centered: true,
                            title: formatMessage({ id: "common.tips" }),
                            icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                            content: formatMessage({
                                id: "drug.medicine.setting.application.err1",
                            }),
                            onOk: () => {
                                run_add({
                                    ...values,
                                    timeZone,
                                    tz,
                                    zh,
                                    en,
                                    dmpId,
                                    active: values.active ? 2 : 1,
                                    id: id,
                                    customerId: customerId,
                                    projectId: projectId,
                                    envId: envId,
                                    storehouseId: [values.storehouseId],
                                    contactGroup: contactGroup,
                                    roleId: auth.project.permissions.role_id,
                                }).then((result) => {
                                    hide();
                                    props.refresh();
                                    message.success(result.msg)
                                })
                            },
                            cancelText: formatMessage({ id: "common.cancel" }),
                            okText: formatMessage({ id: "common.ok" }),
                        });
                        flag = false;
                        return;
                    }
                } else {
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily:
                                        "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                    fontSize: "14px",
                                }}
                            >
                                <CloseCircleFilled
                                    style={{
                                        color: "#F96964",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({
                                    id: "drug.medicine.setting.application.err4",
                                })}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            background: "#FEF0EF",
                            borderRadius: "4px",
                            height: "40px",
                            borderStyle: "solid",
                            border: "1px",
                            borderColor: "#41CC82",
                            paddingTop: "10px",
                        },
                    });
                    flag = false;
                    return;
                }
            }
            //判断供应比例是否和包装比例匹配
            if (values.supplyRatio) {
                _.forEach(values.orderApplicationConfig, function (value) {
                    //查询匹配的配置
                    var drugNames = value.drugNames
                    if (drugNames.length > 1) { //混包的判断
                        var findDrugName = value.drugNames[0].drugName
                        _.forEach(packageData, function (packageInfo) {
                            var findIndex = _.findIndex(packageInfo.packageConfig, function (o) { return o.name === findDrugName; });
                            if (findIndex !== -1) { //包装配置
                                //包装配置信息
                                var packageMap = new Map();
                                _.forEach(value.drugNames, function (packageDrugNames) {
                                    packageMap.set(packageDrugNames.drugName, packageDrugNames.number)
                                });
                                //供应比例配置信息
                                var supplyRatioMap = new Map();
                                _.forEach(packageInfo.packageConfig, function (packageConfig) {
                                    supplyRatioMap.set(packageConfig.name, packageConfig.number)
                                });
                                var ratio = 0
                                packageMap.forEach((value, key) => {
                                    if (flag) {
                                        var supplyNumber = supplyRatioMap.get(key)
                                        var divide = value % supplyNumber
                                        if (divide === 0) {
                                            var ratio1 = value / supplyNumber
                                            if (ratio !== 0 && ratio1 !== ratio) {
                                                notification.open({
                                                    message: (
                                                        <div
                                                            style={{
                                                                fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                fontSize: "14px",
                                                            }}
                                                        >
                                                            <CloseCircleFilled
                                                                style={{
                                                                    color: "#F96964",
                                                                    paddingRight: "8px",
                                                                }}
                                                            />
                                                            {formatMessage({ id: 'drug.medicine.setting.application.err2' })}
                                                        </div>
                                                    ),
                                                    duration: 3,
                                                    placement: "top",
                                                    style: {
                                                        width: "720px",
                                                        background: "#FEF0EF",
                                                        borderRadius: "4px",
                                                        height: "40px",
                                                        borderStyle: "solid",
                                                        border: "1px",
                                                        borderColor: "#41CC82",
                                                        paddingTop: "10px",
                                                    },
                                                });
                                                flag = false
                                                return
                                            }
                                            ratio = ratio1
                                        } else {
                                            notification.open({
                                                message: (
                                                    <div
                                                        style={{
                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                            fontSize: "14px",
                                                        }}
                                                    >
                                                        <CloseCircleFilled
                                                            style={{
                                                                color: "#F96964",
                                                                paddingRight: "8px",
                                                            }}
                                                        />
                                                        {formatMessage({ id: 'drug.medicine.setting.application.err3' })}
                                                    </div>
                                                ),
                                                duration: 3,
                                                placement: "top",
                                                style: {
                                                    width: "720px",
                                                    background: "#FEF0EF",
                                                    borderRadius: "4px",
                                                    height: "40px",
                                                    borderStyle: "solid",
                                                    border: "1px",
                                                    borderColor: "#41CC82",
                                                    paddingTop: "10px",
                                                },
                                            });
                                            flag = false
                                            return
                                        }
                                    }
                                })
                            }
                        });
                    }

                });
            }
        }


        if (flag) {
            run_add({
                ...values,
                timeZone,
                tz,
                zh,
                en,
                dmpId,
                active: values.active ? 2 : 1,
                id: id,
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                storehouseId: [values.storehouseId],
                contactGroup: contactGroup,
                roleId: auth.project.permissions.role_id,
            }).then((result) => {
                hide();
                props.refresh();
                message.success(result.msg)
            })
        }

    }

    const formItemLayout = {
        labelCol: { style: { width: g.lang === "en" ? "155px" : "110px" } },
    };

    const setSupply = (e) => {
        if (e) {
            if (form.getFieldsValue().storehouseId == null || form.getFieldsValue().supplyPlanId == null) {
                form.setFieldValue("active", false);
                formChange();
                return message.error(formatMessage({ id: "projects.site.no.supplyPlan.store" }));
            }

        }
        form.setFieldValue("active", e);
        setCheckStatus(e);
    }

    const changeSupplyStore = (e) => {
        if (!e) {
            form.setFieldValue("active", false);
            setCheckStatus(false);
        }
    }

    React.useImperativeHandle(props.bind, () => ({ show }));
    return (
        <Modal
            className="custom-medium-modal-site-add"
            title={<FormattedMessage id={!id ? "common.addTo" : "common.edit"} />}
            open={visible}
            onCancel={hide}
            centered
            maskClosable={false}

            destroyOnClose={true}
            footer={
                <Spin
                    spinning={getApplicableSiteSupplyPlanLoading || getSiteConfigLoading || getStoreListOptionLoading}>
                    <Row justify="end">
                        <Col>

                        </Col>
                        <Col>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                            <Button disabled={id ? send : false} onClick={save} type="primary" loading={submitting}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                </Spin>
            }
        >
            <Spin spinning={getApplicableSiteSupplyPlanLoading || getSiteConfigLoading || getStoreListOptionLoading}>
                <Form form={form} onValuesChange={formChange} {...formItemLayout}>
                    <Form.Item label={formatMessage({ id: 'projects.site.number' })} name="number" rules={[
                        { required: true },
                        {
                            validator: (rule, value, callback) => {
                                if (value && value !== "") {
                                    const reg = /^[A-Za-z0-9]+$/
                                    if (!reg.test(value)) {
                                        return Promise.reject(formatMessage({ id: 'input.error.only.number.letter' }));
                                    }
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                        style={{ marginBottom: "16px" }}>
                        <Input placeholder={intl.formatMessage({ id: "placeholder.input.common" })} className="full-width"
                            allowClear maxLength={10} showCount />
                    </Form.Item>

                    {
                        aliSiteId === null || aliSiteId === "" || aliSiteId === undefined ?
                            <Form.Item label={formatMessage({ id: 'projects.site.name.standard' })} name="name"
                                rules={[{ required: true }]}
                                style={{ marginBottom: "16px" }}>
                                <Select
                                    placeholder={intl.formatMessage({ id: "placeholder.select.common" })}
                                    allowClear className="full-width"
                                    showSearch
                                    optionFilterProp={"children"}
                                    filterOption={(input, option) => {
                                        const childrenText = option.props.children;
                                        if (childrenText?.length === 2 && typeof childrenText[1] === 'string') {
                                            return childrenText[1].trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0;
                                        }
                                        return false;
                                    }}
                                    onChange={(e, o) => {
                                        changeCountry(o)
                                    }}>
                                    {
                                        siteConfigData ?
                                            siteConfigData.filter(it => g.lang === 'zh' ? it.cn : it.en).map(
                                                (it, index) => (
                                                    <Select.Option value={it[languageHandler(g.lang)]} key={index + it.cn}
                                                        countryCode={it.countryCode}
                                                        timeZone={it.timeZone}
                                                        dmpId={it._id}
                                                        zh={it.cn}
                                                        en={it.en}
                                                        tz={it.tz}
                                                    > {it[languageHandler(g.lang)]}</Select.Option>
                                                )
                                            )
                                            :
                                            null
                                    }
                                </Select>
                            </Form.Item>
                            :
                            <Form.Item label={formatMessage({ id: 'projects.site.name.standard' })} name="name"
                                rules={[{ required: true }]}
                                style={{ marginBottom: "16px" }}>
                                <Input placeholder={intl.formatMessage({ id: "placeholder.input.common" })} className="full-width"
                                    allowClear disabled />
                            </Form.Item>
                    }

                    <Form.Item label={formatMessage({ id: 'projects.site.short.name' })} name="shortName"
                        style={{ marginBottom: "16px" }}>
                        <Input placeholder={intl.formatMessage({ id: "placeholder.input.common" })} className="full-width"
                            allowClear />
                    </Form.Item>
                    <SelectDiv>
                        <Form.Item label={formatMessage({ id: 'common.country' })} name="country"
                            style={{ marginBottom: "16px" }}>
                            {
                                form.getFieldsValue().country ?
                                    <Cascader
                                        suffixIcon={null}
                                        bordered={false}
                                        disabled
                                        options={countries}
                                    />
                                    :
                                    <span>-</span>
                            }
                        </Form.Item>
                    </SelectDiv>
                    <Form.Item label={formatMessage({ id: 'common.timezone' })} name="location"
                        style={{ marginBottom: "16px" }}>
                        {
                            locationStr !== "" ? <span>{locationStr}</span> :
                                (form.getFieldsValue().location ? <span>{form.getFieldsValue().location}</span> : <span>-</span>)
                        }
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'common.status' })} name="deleted"
                        style={{ marginBottom: "16px" }}>
                        <Radio.Group>
                            <Radio value={2} style={{ marginRight: "50px" }}><FormattedMessage
                                id="common.effective" /></Radio>
                            <Radio value={1}><FormattedMessage id="common.invalid" /></Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'projects.attributes.regionLayered' })}
                        name="regionId"
                        rules={[{ required: regionLayeredFlag }]}
                        style={{ marginBottom: "16px" }}>
                        <Select className="full-width" allowClear
                            placeholder={intl.formatMessage({ id: "placeholder.select.common" })}>
                            {
                                regions ?
                                    regions.map(
                                        (it, index) => (
                                            <Select.Option key={it.id}
                                                value={it.id}>{it.name}</Select.Option>
                                        )
                                    )
                                    :
                                    null
                            }
                        </Select>
                    </Form.Item>
                    {/* 供应比例 */}
                    {!isBlindRole &&
                        <Form.Item
                            style={{ marginBottom: "0px" }}
                            label={formatMessage({
                                id: "drug.medicine.order.supply.ratio",
                            })}
                        >
                            <Row>
                                <Col>
                                    <Form.Item
                                        name="supplyRatio"
                                        valuePropName="checked"
                                    >
                                        <Switch size="small" onChange={changeSupplyRatio} disabled={!isOpenOrderSupplyRatio} />
                                    </Form.Item>
                                </Col>
                            </Row>
                            {supplyRatio &&
                                <>
                                    <Form.List name="orderApplicationConfig" >
                                        {(fields, { add, remove }) => (
                                            <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
                                                {fields.map((field) => (

                                                    <Space
                                                        key={field.key}
                                                        style={{ display: "flex", columnGap: 0, background: '#F8F9FA' }}
                                                        direction="vertical"
                                                    >
                                                        <div style={{
                                                            display: 'flex',
                                                            float: 'right',
                                                            marginRight: -4,
                                                            marginTop: -4,
                                                            marginBottom: 0,
                                                        }}>
                                                            <CloseCircleFilled onClick={() => remove(field.name)}
                                                                style={{ color: "#F96964" }} />
                                                        </div>
                                                        <Form.Item style={{ marginLeft: 16, marginRight: 16, marginTop: 0, marginBottom: 0 }}>
                                                            <Form.List name={[field.name, 'drugNames']} initialValue={[{}]}>
                                                                {(subFields, subOpt) => (
                                                                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                                                                        {
                                                                            subFields.map((subField) => (
                                                                                <Space
                                                                                    key={subField.key}
                                                                                    style={{ display: "flex", columnGap: 0 }}
                                                                                    align="baseline"
                                                                                >
                                                                                    <Form.Item
                                                                                        {...subField}
                                                                                        name={[subField.name, "drugName"]}
                                                                                        rules={[
                                                                                            {
                                                                                                required: true,
                                                                                                message: formatMessage({
                                                                                                    id: "drug.configure.drugName",
                                                                                                }),
                                                                                            },
                                                                                        ]}
                                                                                        style={{ width: 280, }}
                                                                                    >
                                                                                        <Select allowClear placeholder={formatMessage({ id: 'drug.configure.drugName' })} className="full-width" onClick={(e) => clickSelect(e)} onChange={(e) => handleSelectChange(e, field)}>
                                                                                            {singleDrugNames !== null && packageDrugNames !== null && singleDrugNames.length > 0 && packageDrugNames.length > 0 &&
                                                                                                <div style={{ height: 32 }}>
                                                                                                    <Tabs
                                                                                                        size="small"
                                                                                                        tabPosition="top"
                                                                                                        destroyInactiveTabPane={true} style={{ marginTop: -10 }} defaultActiveKey="single" onChange={handleTabChange}>
                                                                                                        <Tabs.TabPane tab={formatMessage({ id: "shipment.order.packageMethod.single" })} key="single"></Tabs.TabPane>
                                                                                                        <Tabs.TabPane tab={formatMessage({ id: "shipment.order.packageMethod.package" })} key="package"></Tabs.TabPane>
                                                                                                    </Tabs>
                                                                                                </div>
                                                                                            }
                                                                                            {
                                                                                                drugNameOptions.map((e) => <Select.Option value={e.value} >{e.label}</Select.Option>)
                                                                                            }
                                                                                        </Select>
                                                                                    </Form.Item>
                                                                                    <InputNumberRow>
                                                                                        <Form.Item
                                                                                            {...subField}
                                                                                            name={[
                                                                                                subField.name,
                                                                                                "number",
                                                                                            ]}
                                                                                            rules={[
                                                                                                {
                                                                                                    required: supplyRatio,
                                                                                                    message:
                                                                                                        formatMessage(
                                                                                                            {
                                                                                                                id: "drug.medicine.order.supply.ratio",
                                                                                                            }
                                                                                                        ),
                                                                                                },
                                                                                            ]}
                                                                                        >
                                                                                            <TipInputNumber
                                                                                                disabled={!supplyRatio}
                                                                                                trigger={["hover"]}
                                                                                                inputClassName="full-width"
                                                                                                placeholder={formatMessage(
                                                                                                    {
                                                                                                        id: "common.required.prefix",
                                                                                                    }
                                                                                                )}
                                                                                                precision={0}
                                                                                                min={1}
                                                                                                step={1}
                                                                                                title
                                                                                            />
                                                                                        </Form.Item>
                                                                                    </InputNumberRow>
                                                                                </Space>
                                                                            ))
                                                                        }
                                                                    </div>
                                                                )}
                                                            </Form.List>
                                                        </Form.Item>
                                                    </Space>
                                                ))}

                                                <Form.Item style={{ marginBottom: 0 }}>
                                                    <Button
                                                        type="dashed"
                                                        onClick={() => add()}
                                                        block
                                                        icon={<PlusOutlined />}
                                                    >
                                                        <FormattedMessage
                                                            id={"common.addTo"}
                                                        />
                                                    </Button>
                                                </Form.Item>

                                            </div>
                                        )}
                                    </Form.List>
                                </>}

                        </Form.Item>
                    }

                    {
                        researchAttribute === 0 ?
                            <Form.Item label={formatMessage({ id: 'projects.site.supply' })} name="active"
                                style={{ marginBottom: "16px" }}>
                                <Switch
                                    size="small"
                                    checked={checkStatus}
                                    onChange={setSupply}
                                >

                                </Switch>
                            </Form.Item>
                            :
                            null
                    }
                    {
                        researchAttribute === 0 ?
                            <Form.Item label={formatMessage({ id: 'menu.projects.project.build.plan' })}
                                name="supplyPlanId"
                                style={{ marginBottom: "16px" }}>
                                <Select className="full-width" allowClear
                                    onChange={(e) => {
                                        changeSupplyStore(e)
                                    }}
                                    placeholder={intl.formatMessage({ id: "placeholder.select.common" })}>
                                    {
                                        siteSupplyPlanData ?
                                            siteSupplyPlanData.map(
                                                (it, index) => (
                                                    <Select.Option key={it.id}
                                                        value={it.id}>{it.name}</Select.Option>
                                                )
                                            )
                                            :
                                            null
                                    }
                                </Select>
                            </Form.Item>
                            :
                            null
                    }

                    <Form.Item label={formatMessage({ id: 'projects.storehouse.name' })} name="storehouseId"
                        style={{ marginBottom: "16px" }}>
                        <Select className="full-width" allowClear
                            onChange={(e) => {
                                changeSupplyStore(e)
                            }}
                            placeholder={intl.formatMessage({ id: "placeholder.select.common" })}>
                            {
                                storehousesData ?
                                    storehousesData.map(
                                        (it, index) => (
                                            <Select.Option key={it.id}
                                                value={it.id}>{it.name}</Select.Option>
                                        )
                                    )
                                    :
                                    null
                            }
                        </Select>
                    </Form.Item>

                    <Form.Item label={formatMessage({ id: 'common.contacts' })} name="contactGroup">
                        <Form.List name="contactGroup">
                            {(fields, { add, remove }) => (
                                <>

                                    {fields.map((field) => (
                                        <>
                                            <Space key={field.key} style={{ display: 'flex', background: '#F8F9FA' }}
                                                direction="vertical">
                                                <Row>
                                                    <Col span={8}>
                                                        <Form.Item style={{ width: 200, marginBottom: 0, marginLeft: 16 }}
                                                            {...field}
                                                            valuePropName='checked'
                                                            name={[field.name, 'isdefault']}
                                                        >
                                                            <Checkbox onChange={() => changeContactDefault(field.name)}>
                                                                {formatMessage({ id: 'projects.site.isDefault.contact' })}
                                                            </Checkbox>
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={8} offset={8}>
                                                        <div style={{
                                                            display: 'flex',
                                                            float: 'right',
                                                            marginRight: -4,
                                                            marginTop: -4,
                                                            marginBottom: 0,
                                                        }}>
                                                            <CloseCircleFilled onClick={() => remove(field.name)}
                                                                style={{ color: "#F96964" }} />
                                                        </div>
                                                    </Col>
                                                </Row>
                                                <Divider
                                                    style={{ margin: "0px 0px 0px 0px", borderWidth: "0.5px" }}></Divider>
                                                <Form.Item style={{ marginLeft: 16, marginRight: 16 }}
                                                    {...field}
                                                    name={[field.name, 'contacts']}
                                                //rules={[{ required: true, message: formatMessage({id: 'placeholder.input.common'}) }]}
                                                >
                                                    <TipInput trigger={['hover']} maxLength={50} showCount allowClear
                                                        ellipsis inputClassName="full-width"
                                                        placeholder={formatMessage({ id: 'common.contacts' })}
                                                        title />
                                                </Form.Item>
                                                <Form.Item style={{ marginLeft: 16, marginRight: 16 }}
                                                    {...field}
                                                    name={[field.name, 'phone']}
                                                //rules={[{ required: true, message: formatMessage({id: 'placeholder.input.common'}) }]}
                                                >
                                                    <TipInput trigger={['hover']} allowClear maxLength={50} showCount
                                                        className="full-width"
                                                        placeholder={formatMessage({ id: 'projects.contact.information' })}
                                                        title />
                                                </Form.Item>
                                                <Form.Item style={{ marginLeft: 16, marginRight: 16 }}
                                                    {...field}
                                                    name={[field.name, 'email']}

                                                >
                                                    <TipInput trigger={['hover']} allowClear maxLength={100} showCount
                                                        ellipsis className="full-width"
                                                        placeholder={formatMessage({ id: 'common.email' })} title />
                                                </Form.Item>
                                                <Form.Item style={{ marginLeft: 16, marginRight: 16 }}
                                                    {...field}
                                                    name={[field.name, 'address']}
                                                //rules={[{ required: true, message: formatMessage({id: 'placeholder.input.common'}) }]}
                                                >
                                                    <TipInput trigger={['hover']} allowClear maxLength={500} showCount
                                                        ellipsis className="full-width"
                                                        placeholder={formatMessage({ id: 'common.address' })}
                                                        title />
                                                </Form.Item>
                                            </Space>

                                            <div>
                                                <p />
                                            </div>
                                        </>
                                    ))}
                                    <Form.Item style={{ paddingTop: 16 }}>
                                        <Button type="dashed" onClick={() => add()} block
                                            icon={<PlusOutlined />}><FormattedMessage id={'common.addTo'} />
                                        </Button>
                                    </Form.Item>
                                </>
                            )}
                        </Form.List>
                    </Form.Item>
                    {
                        IPInheritance &&
                        <Form.Item label={formatMessage({ id: "projects.attributes.visit.inheritance" })}>
                            <Form.Item name={"batchGroupAlarmOpen"} valuePropName="checked">
                                <Switch
                                    size="small"
                                    onChange={(v) => {
                                        setBatchGroupAlarm(v)
                                    }}
                                >

                                </Switch>
                            </Form.Item>
                            {
                                batchGroupAlarm && !blindRole &&
                                <BatchGroupSite types={1} form={form} batchLabel={batchLabel} groupLabel={groupLabel} />

                            }
                        </Form.Item>
                    }

                    {/* <Form.Item label={formatMessage({id: 'common.contacts'})} name="contacts"
                               style={{marginBottom: "16px"}}>
                        <Input placeholder={intl.formatMessage({id: "placeholder.input.common"})} className="full-width"
                               allowClear maxLength={50} showCount/>
                    </Form.Item>

                    <Form.Item label={formatMessage({id: 'projects.contact.information'})} name="phone" maxLength={50}
                               style={{marginBottom: "16px"}}>
                        <Input placeholder={intl.formatMessage({id: "placeholder.input.common"})} className="full-width"
                               allowClear maxLength={50} showCount/>
                    </Form.Item>

                    <Form.Item label={formatMessage({id: 'common.email'})} name="email" style={{marginBottom: "16px"}}>
                        <Input placeholder={intl.formatMessage({id: "placeholder.input.common"})} className="full-width"
                               allowClear maxLength={100} showCount/>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'common.address'})} name="address"
                               style={{marginBottom: "0px"}}>
                        <Input.TextArea
                            placeholder={intl.formatMessage({id: "placeholder.input.common"})}
                            className="full-width"
                            allowClear
                            maxLength={500}
                            showCount/>
                    </Form.Item> */}
                </Form>
            </Spin>
        </Modal >
    )
};

const SelectDiv = styled.div`
  .ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    color: #1d2129;
  }
`
const InputNumberRow = styled.div`
    .ant-input-number .ant-input-number-handler-wrap,
    .ant-input-number-focused .ant-input-number-handler-wrap {
        opacity: 1;
    }
`;

const SiteAddModal = styled(Modal)`
  .custom-medium-modal-site-add,
  .custom-medium-modal .ant-modal-content {
    width: 800px !important;
  }
`