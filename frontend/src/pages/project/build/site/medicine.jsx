import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, message, Row, Select, Space, Spin, Table,Tooltip, } from "antd";
import { FormattedMessage, useIntl } from "react-intl";
import { useAuth } from "../../../../context/auth";
import { useSite } from "./context";
import { useFetch } from "../../../../hooks/request";
import { getApplicableSiteSupplyPlan, getSupplyPlanMedicineList } from "../../../../api/supply_plan";
import { useSafeState } from "ahooks"
import { updateSupplyPlan } from "../../../../api/project_site";
import { permissions } from "../../../../tools/permission";
import { useGlobal } from "../../../../context/global";
import {PlusOutlined, QuestionCircleFilled} from "@ant-design/icons";


export const SupplyPlanMedicine = (props) => {

    const auth = useAuth();
    const g = useGlobal();
    const envId = auth.env ? auth.env.id : null;
    const ctx = useSite();
    const intl = useIntl();
    const { formatMessage } = intl;
    const [form] = Form.useForm();
    const [height, setHeight] = useSafeState(window.innerHeight - 232);
    const [data, setData] = useSafeState([]);
    const [supplyPlanOption, setSupplyPlanOption] = useSafeState([])
    const [selectSupplyPlanId, setSelectSupplyPlanId] = useSafeState(null)
    const {
        runAsync: getSupplyPlanMedicineListRun,
        loading: getSupplyPlanMedicineListLoading
    } = useFetch(getSupplyPlanMedicineList, { manual: true })
    const {
        runAsync: getApplicableSiteSupplyPlanRun,
        loading: getApplicableSiteSupplyPlanLoading
    } = useFetch(getApplicableSiteSupplyPlan, { manual: true })
    const { runAsync: updateSupplyPlanRun, loading: updateSupplyPlanLoading } = useFetch(updateSupplyPlan, { manual: true })

    const hide = () => {
        form.resetFields()
        ctx.setCurrentSupplyId(null)
        ctx.setCurrentSiteId(null)
        setSelectSupplyPlanId(null)
        ctx.setCurrentSupplyName("")
        setData([])
        ctx.setMedicineVisit(false);
        ctx.setListRefresh(ctx.listRefresh + 1)
    };

    const save = () => {
        let supplyPlanId = form.getFieldValue("supplyPlanId");
        updateSupplyPlanRun({
            id: ctx.currentSiteId,
            supplyPlanId: supplyPlanId,
            envId: envId,
        }).then((result) => {
            message.success(result.msg)
            hide()
        })
    }

    useEffect(() => {
        if (ctx.currentSupplyId != null) {
            form.setFieldsValue({ "supplyPlanId": ctx.currentSupplyId })
            getSupplyPlanMedicineListRun({
                id: ctx.currentSupplyId,
                roleId: auth.project.permissions.role_id
            }).then(
                (result) => {
                    let ret = convect(result.data === null ? [] : result.data)
                    setData(ret)
                }
            )
        }
    }, [ctx.currentSupplyId])

    useEffect(() => {
        if (selectSupplyPlanId != null) {
            getSupplyPlanMedicineListRun({
                id: selectSupplyPlanId,
                roleId: auth.project.permissions.role_id
            }).then(
                (result) => {
                    let ret = convect(result.data === null ? [] : result.data)
                    setData(ret)
                }
            )
        }
    }, [selectSupplyPlanId])

    useEffect(() => {
        if (ctx.currentSupplyId != null) {
            getApplicableSiteSupplyPlanRun({ envId: envId, siteId: ctx.currentSiteId }).then((result) => {
                setSupplyPlanOption(result.data.optionalSupplyPlan)
            })
        }
    }, [ctx.currentSiteId])

    const handleResize = () => {
        setHeight(window.innerHeight - 232);
    };
    React.useEffect(
        () => {
            // 监听
            window.addEventListener("resize", handleResize);
            // 销毁
            return () => window.removeEventListener("resize", handleResize);
        }
    );

    const convect = (data) => {
        let ret = []
        data.forEach((value) => {
            ret.push({ ...value.info, "id": value.id, "supplyPlanId": value.supplyPlanId })
        })
        return ret
    }

    return <Drawer
        title={formatMessage({ id: 'menu.projects.project.build.plan' }) + "-" + ctx.currentSupplyName}
        width={"100%"}
        onClose={hide}
        visible={ctx.medicineVisit}

        maskClosable={false}
        footer={
            permissions(auth.project.permissions, "operation.build.site.supply-plan.edit") ?
                <Spin spinning={getSupplyPlanMedicineListLoading}>
                    <Row style={{ paddingLeft: 24 }}>
                        <Col span={24} style={{ textAlign: 'right' }}>
                            <Space direction='horizontal'>
                                <Button onClick={hide} loading={updateSupplyPlanLoading}>
                                    <FormattedMessage id="common.cancel" />
                                </Button>
                                <Button type="primary" onClick={save} loading={updateSupplyPlanLoading}>
                                    <FormattedMessage id="common.ok" />
                                </Button>
                            </Space>
                        </Col>
                    </Row>
                </Spin> : null
        }
    >
        <Spin spinning={getSupplyPlanMedicineListLoading || getApplicableSiteSupplyPlanLoading}>
            {
                supplyPlanOption ?
                    <Form style={{ paddingLeft: 14 }} form={form}>
                        <Form.Item label={formatMessage({ id: 'supply.plan.current' })} name='supplyPlanId'
                        >
                            <Select bordered={false} dropdownMatchSelectWidth={false} style={{ width: "auto" }} onChange={(v) => setSelectSupplyPlanId(v)}>
                                {
                                    supplyPlanOption.map(o => <Select.Option value={o.id}>{o.name}</Select.Option>)
                                }
                            </Select>
                        </Form.Item>
                    </Form> : null
            }
            <Table
                // style={{ paddingLeft: 14 }}
                size="small"
                dataSource={data}
                rowKey={(record) => (record.id)}
                pagination={false}
                scroll={{ y: height }}
                style={{marginLeft:14,marginRight:14}}
                className='custom-table'

            >
                <Table.Column width={70} title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#"
                    render={(value, record, index) => (index + 1)} />
                <Table.Column width={150} title={formatMessage({ id: 'shipment.medicine' })} dataIndex="medicineName"
                    key="medicineName" ellipsis />
                <Table.Column 
                    width={g.lang==="zh"?170:180}
                    title={
                        <span>
                            <span>
                                {formatMessage({id: 'projects.supplyPlan.autoSupplySize'})}
                            </span>
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={
                                        <>
                                        <Row>{formatMessage({id:"projects.supplyPlan.secondSupply.tip"})}</Row>
                                        <Row>{formatMessage({id:"projects.supplyPlan.forecast.tip"})}</Row>
                                        <Row>{formatMessage({id:"projects.supplyPlan.buffer.tip"})}</Row>
                                        <Row>{formatMessage({id:"projects.supplyPlan.na.tip"})}</Row>
                                        </>
                                    }
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="autoSupplySize" key="autoSupplySize"
                    render={
                        (array, record) => {
                            let data = []
                            if (array?.findIndex((value) => value === 0) !== -1) {
                                data.push(<Row>NA</Row>)
                            }
                            if (array?.find((value) => value === 1)) {
                                data.push(<Row>{formatMessage({ id: 'shipment.mode.max' })}</Row>)
                            }
                            if (array?.find((value) => value === 2)) {
                                data.push(<Row>{formatMessage({ id: 'shipment.mode.reSupply' })}</Row>)
                            }
                            if (array?.find((value) => value === 3)) {
                                data.push(<Row>{formatMessage({ id: 'shipment.mode.forecast' })}</Row>)
                            }
                            return <>{data.map(value => value)}</>
                        }
                    }
                />
                <Table.Column width={g.lang==="zh"?110:230} title={formatMessage({ id: 'projects.supplyPlan.initSupply' })}
                    dataIndex="initSupply" key="initSupply" ellipsis />
                <Table.Column
                    width={g.lang==="zh"?160:210}
                    title={
                        <span>
                            {formatMessage({id: 'projects.supplyPlan.warning.site'})}
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={
                                        <>
                                            <Row>{formatMessage({id:"projects.supplyPlan.warning.tip"})}</Row>
                                        </>
                                    }
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    } 
                    dataIndex="warning"
                    key="warning" ellipsis 
                />
                <Table.Column 
                    width={g.lang==="zh"?168:267}
                    title={
                        <span>
                            {formatMessage({id: 'projects.supplyPlan.warning.dispensing'})}
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={
                                        <>
                                            <Row>{formatMessage({id:"projects.supplyPlan.warning.dispensing.tip"})}</Row>
                                        </>
                                    }
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="dispensingAlarm"
                    key="dispensingAlarm" ellipsis 
                />
                <Table.Column width={g.lang==="zh"?110:110} title={formatMessage({ id: 'projects.supplyPlan.buffer' })} dataIndex="buffer"
                    key="buffer" ellipsis />
                <Table.Column width={g.lang==="zh"?96:114}  title={formatMessage({ id: 'projects.supplyPlan.secondSupply' })}
                    dataIndex="secondSupply" key="secondSupply" ellipsis />
                <Table.Column 
                    width={g.lang==="zh"?120:141}
                    title={
                        <span>
                            <span>
                                {formatMessage({id: 'projects.supplyPlan.unDistributionDate'})}
                            </span>
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={formatMessage({id:"projects.supplyPlan.unDistributionDate.tip"})}
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="unDistributionDate" 
                    key="unDistributionDate" 
                    ellipsis 
                />
                <Table.Column
                    width={g.lang==="zh"?120:215} 
                    title={
                        <span>
                            <span>
                                {formatMessage({id: 'projects.supplyPlan.unProvideDate'})}
                            </span>
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={formatMessage({id:"projects.supplyPlan.unProvideDate.tip"})}
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="unProvideDate" 
                    key="unProvideDate" 
                    ellipsis 
                />
                <Table.Column 
                    width={g.lang==="zh"?120:140} 
                    title={
                        <span>
                            <span>
                                {formatMessage({id: 'projects.supplyPlan.notCountedDate'})}
                            </span>
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={formatMessage({id:"projects.supplyPlan.notCountedDate.tip"})}
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="notCountedDate" 
                    key="notCountedDate" 
                    ellipsis 
                />
                <Table.Column width={g.lang==="zh"?100:160}  title={formatMessage({ id: 'projects.supplyPlan.validityReminder' })}
                    dataIndex="validityReminder" key="validityReminder" ellipsis />
                <Table.Column 
                    width={g.lang==="zh"?220:220}
                    title={
                        <span>
                            {formatMessage({id: 'projects.supplyPlan.forecast'})}
                        </span>
                    }
                    dataIndex="forecast" 
                    key="forecast" 
                    ellipsis 
                    render={(value, record, index) => {
                        return (
                            // 显示文本
                            ((record.forecastMin !== 0 || record.forecastMax !== 0) && record.autoSupplySize?.find((it)=> it === 3)) ? record.forecastMin + "~" + record.forecastMax : "-"
                        );
                    }}
                />
                <Table.Column width={g.lang==="zh"?200:200}  title={formatMessage({ id: 'projects.supplyPlan.supplyMode' })}
                    dataIndex="supplyMode" key="supplyMode" ellipsis
                    render={
                        (value) => {
                            if (value === 1) {
                                return formatMessage({ id: 'projects.supplyPlan.allSupply' })
                            }
                            if (value === 2) {
                                return formatMessage({ id: 'projects.supplyPlan.singleSupply' })
                            }
                            if (value === 3) {
                                return formatMessage({ id: 'projects.supplyPlan.allSupplyAndMedicine' })
                            }
                            if (value === 4) {
                                return formatMessage({ id: 'projects.supplyPlan.singleSupplyAndMedicine' })
                            }
                            return ""
                        }
                    }
                />
            </Table>
        </Spin>
    </Drawer>;

}


