import { CodeRuleProvider } from "./context";
import { CodeRule } from "./code-rule";
import { Tabs } from "antd";
import { useAuth } from "../../../../context/auth";

export const Index = () => {
    const auth = useAuth();
    const cohorts = auth.env?.cohorts;
    const projectType = auth.project.info.type;


    return (
        <CodeRuleProvider>
            {projectType === 1 ? (
                <CodeRule />
            ) : (
                <Tabs
                    destroyInactiveTabPane={true}
                    size="small"
                    defaultActiveKey="1"
                    tabPosition="top"
                    items={cohorts.map((cohort: any, cohort_index: number) => {
                        const id = cohort.id;
                        return {
                            label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                            key: id,
                            children: <CodeRule cohort={cohort} cohortId={id} />,
                        };
                    })}
                ></Tabs>
            )}
        </CodeRuleProvider>
    );
};
