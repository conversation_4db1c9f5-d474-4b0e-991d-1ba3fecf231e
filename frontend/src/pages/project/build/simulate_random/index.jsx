import React from 'react';
import {<PERSON><PERSON>, Col, Dropdown, Menu, message, Row, Spin, Table, Tooltip} from 'antd';
import {InsertDivider} from "components/divider";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import {Add} from "./add";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {CaretDownOutlined, CaretRightOutlined} from '@ant-design/icons';
import {TooltipParagraph} from "components/TooltipParagraph";
import {downloadList, list, run} from "../../../../api/simulate_random";
import {useGlobal} from "../../../../context/global";
import {Overview} from "./overview";
import {
    detailVisible<PERSON>tom,
    groupsAtom,
    overviewKey<PERSON>tom,
    overviewVisibleAtom,
    selectData<PERSON>tom,
    selectDetail<PERSON>tom,
    selectResultD<PERSON>il<PERSON>tom,
    title<PERSON>tom
} from "./ctx";
import {useAtom} from "jotai/index";
import {Detail} from "./detail";
import styled from "@emotion/styled";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton, clickFilter} from "../../../common/auth-wrap";

export const SimulateRandom = (props) => {
    const g = useGlobal();
    const [data, setData] = useSafeState([]);
    const intl = useTranslation();
    const {formatMessage} = intl;
    const auth = useAuth()
    const projectId = auth.project.id;
    const projectType = auth.project.info.type;
    const envId = auth.env ? auth.env.id : null;
    const cohorts = auth.env ? auth.env.cohorts : null;
    const projectStatus = auth.project.status ? auth.project.status : 0
    const customerId = auth.customerId;
    const simulateRandomAdd = React.useRef();

    const [, setOverviewVisible] = useAtom(overviewVisibleAtom)
    const [, setDetailVisible] = useAtom(detailVisibleAtom)
    const [, setSelectDetailData] = useAtom(selectDetailAtom)
    const [, setTitle] = useAtom(titleAtom)
    const [, setGroups] = useAtom(groupsAtom)
    const [, setSelectData] = useAtom(selectDataAtom)
    const [, setSelectResultDetail] = useAtom(selectResultDetailAtom)
    const [, setOverviewKey] = useAtom(overviewKeyAtom)
    const [refresh, setRefresh] = useSafeState(0)
    const {runAsync: run_list, loading} = useFetch(list, {manual: true})
    const {runAsync: run_run,loading:runLoading} = useFetch(run, {manual: true})
    const {runAsync: run_downloadList, loading: downloadLoading} = useFetch(downloadList, {manual: true})
    const [overview, setOverview] = useSafeState(false);


    const onList = () => {
        run_list({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
        }).then(
            (response) => {
                let data = response.data
                setData(data);
            }
        );
    }


    const onAdd = (data) => {
        simulateRandomAdd.current.show(data);
    };
    const onRun = (record) => {
        run_run({id: record.id}).then((result) =>{
            setRefresh(refresh +1 )
            message.success(result.msg)
        } )
    }


    const expandedDetail = (resultRecord) => {
        let projectOverview = resultRecord.simulationRandomResult.avgSDOverview.projectOverView
        let simulateDetails = resultRecord.simulationRandomResult.unbalancedDetails
        const group = []
        if (simulateDetails != null && simulateDetails.length >0){
            let simulateDetail = resultRecord.simulationRandomResult.unbalancedDetails[0]
            if (simulateDetail.groupCounts != null) {
                for (let i = 0; i < simulateDetail.groupCounts.length; i++) {
                    let name = simulateDetail.groupCounts[i].group;
                    group.push({title: name, dataIndex: 'run', key:  'run', className: 'bg-white'
                        , render: (text, record, index) => (
                            record.groupCounts.find((item)=>{return item.group === name})?record.groupCounts.find((item)=>{return item.group === name}).count:0
                        )});
                }
            }
        }
        const columns = [
            {
                title: <FormattedMessage id={"common.serial"} />,
                className: 'bg-white',
                fixed: "left",
                dataIndex: '#',
                key: '#',
                width: 70,
                render: (text, record, index) => (index + 1)
            },
            {
                title: <FormattedMessage id={'simulate_random.run.count'} />,
                width: 150,
                dataIndex: 'run',
                key: 'run',
                className: 'bg-white',
                fixed: "left",
            },
            ...group,

        ]
        if (permissions(auth.project.permissions,"operation.build.simulate-random.factor")){
            columns.push(
                {
                    title: <FormattedMessage id={'common.operation'} />,
                    width: 100,
                    align: "center",
                    className: 'bg-white',
                    fixed: "right",
                    render: (text, record, index) =>
                        (<AuthButton size="small" type="link" onClick={() => onDetail(resultRecord,record)}>
                            <FormattedMessage id="common.details"/>
                        </AuthButton>)
                },
            )
        }
        return <Spin spinning={false}>
            <Table rowKey={(resultRecord) => (resultRecord.id)} columns={columns} dataSource={simulateDetails}
                   pagination={false}/>
            <Col>{formatMessage({id: 'simulate_random.detail.meanStandard', allowComponent: true})}
                {

                    projectOverview.avgSDs?.map(
                        (it) =>
                            "【" + it.group + ": " + it.avg + "±" + it.sd + "】"
                    )
                }
            </Col>
        </Spin>
    }



    const onDetail = (resultRecord,record) => {
        setGroups(record?.groups)
        setSelectDetailData(record)
        setDetailVisible(true)
        setSelectResultDetail(resultRecord)
    }

    const onDownloadList = (id) => {
        run_downloadList({id: id}).catch(() => message.error(formatMessage({id: 'common.download.fail'})))
    }

    const overviewItems = () => {
        return [
            // permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.site",props.cohort?.status) && attribute.info.isRandom === true  ? {
            //     label: formatMessage({id: 'projects.randomization.distributionCenter'}),
            //     key: 1,
            // } : false,
            // permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.country",props.cohort?.status) && attribute.info.isCountryRandom === true? {
            //     label: formatMessage({id: 'projects.randomization.distributionCountry'}),
            //     key: 2,
            // } : false,
            // permissionsCohort(auth.project.permissions, "operation.build.randomization.list.segmentation.region",props.cohort?.status)&& attribute.info.isRegionRandom === true ? {
            //     label: formatMessage({id: 'projects.randomization.distributionRegion'}),
            //     key: 3,
            // } : false,
            {
                label: formatMessage({id: 'simulate_random.overview.avgsd', allowComponent: true}),
                key: 1,
            },
            {
                label: formatMessage({id: 'simulate_random.overview.min', allowComponent: true}),
                key: 2,
            },
            {
                label: formatMessage({id: 'simulate_random.overview.unbalanced.run.count', allowComponent: true}),
                key: 3,
            }
        ].filter(Boolean)
    }
    const showTab = (key, record) => {
        setGroups(record?.simulationRandomResult.groups)
        setSelectData(record)
        if (key === 1) {
            setOverviewKey(1)
            setTitle(formatMessage({id: 'simulate_random.overview.avgsd'}))
        } else if (key === 2) {
            setOverviewKey(2)
            setTitle(formatMessage({id: 'simulate_random.overview.min'}))
        } else if (key === 3) {
            setOverviewKey(3)
            setTitle(formatMessage({id: 'simulate_random.overview.unbalanced.run.count'}))
        }
        setOverviewVisible(true)
    }
    // 操作列渲染
    const operationButtons = (record) => {
        const btns = [];
        const moreOperationsMeunItems = [];

        if (permissionsCohort(auth.project.permissions, "operation.build.simulate-random.edit", record.cohort.status) && projectStatus !== 2) {
            btns.push(<AuthButton style={{paddingLeft: 0, paddingRight: 0}} size="small" type="link"
                              onClick={() => onAdd(record)}>
                <FormattedMessage id="common.edit"/>
            </AuthButton>)
        }

        if (permissionsCohort(auth.project.permissions, "operation.build.simulate-random.run", record.cohort.status) && projectStatus !== 2) {
            btns.push(<AuthButton style={{paddingLeft: 0, paddingRight: 0}} size="small" type="link"
                              onClick={() => onRun(record)}>
                <FormattedMessage id="simulate_random.run"/>
            </AuthButton>)
        }
        if (permissions(auth.project.permissions,"operation.build.simulate-random.site")){
            btns.push(
                <Dropdown
                    menu={{
                        items: overviewItems(),
                        onClick: clickFilter(({key}) => {
                            showTab(+key, record)
                            setOverview(false)
                        })
                    }}
                    trigger={['hover']}
                    onOpenChange={(v) => {setOverview(v)}}
                >
                    <AuthButton  style={{paddingLeft: 0, paddingRight: 0, }} size="small" type="link">
                        <ArrowBlock>
                            <FormattedMessage id="simulate_random.overview" />
                            <svg className="iconfont"  width={10} height={10} style={{marginLeft: '4px'}}>
                                <use xlinkHref="#icon-xiajiantou"></use>
                            </svg>
                            {/*{overview ?
                                <svg className="iconfont"  width={10} height={10} style={{marginLeft: '4px'}}>
                                    <use xlinkHref="#icon-shangjiantou"></use>
                                </svg>: <span>
                                <svg className="iconfont" width={10} height={10} style={{marginLeft: '4px'}}>
                                    <use xlinkHref="#icon-xiajiantou"></use>
                                </svg>
                            </span>}*/}
                        </ArrowBlock>
                    </AuthButton>
                </Dropdown>
            )
        }

        if (permissions(auth.project.permissions, "operation.build.simulate-random.download") && auth.project.info.research_attribute === 1) {
            btns.length < 3 ? btns.push(<AuthButton style={{paddingLeft: 0, paddingRight: 0}} size="small" type="link"
                                                loading={downloadLoading} onClick={() => {
                    onDownloadList(record.id)
                }}>
                    <FormattedMessage id="common.download"/>
                </AuthButton>) :
                moreOperationsMeunItems.push(<Menu.Item key={"download"} loading={downloadLoading}
                                                        onClick={() => onDownloadList(record.id)}><FormattedMessage
                    id="common.download"/></Menu.Item>)
        }

        // return InsertDivider(btns)
        return <div style={{display: "flex", alignItems: "center"}}>
            {/*{InsertDivider(btns)}*/}
            {btns.length === 0 ? <>-</> : <>{btns.map((it, index) => (<span key={index} style={{marginRight: 12}}>{it}</span>))}</>}
            {moreOperationsMeunItems.length !== 0 && <Dropdown overlay={
                <Menu>
                    {moreOperationsMeunItems.map((item) => (item))}
                </Menu>
            }>
                <span className="dropdown-button"><i className="iconfont icon-gengduo-changgui"/></span>
            </Dropdown>}
        </div>
    }


    function renderCohort(cohortId) {
        if (!cohortId) {
            return null;
        }

        const cohort = cohorts.find(it => (cohortId === it.id));
        return <TooltipParagraph outStyle={{width: 200}} tooltip={cohort?.name}>
            {cohort?.name}
        </TooltipParagraph>;

    }

    React.useEffect(() => {
        onList();
    }, [refresh]);

    return (
        <React.Fragment>
            <Row gutter={8} justify="space-between">
                <Col xs={24} sm={24} md={12} lg={6}>
                    <div style={{display: 'flex', alignItems: 'center'}}>
                        <svg className="iconfont" width={16} height={16}>
                            <use xlinkHref="#icon-monisuiji-quanbusuiji"/>
                        </svg>
                        <span style={{
                            color: "#1D2129",
                            fontWeight: 600,
                            paddingLeft: "8px"
                        }}>{formatMessage({id: 'simulate_random.all', allowComponent: true})}</span>
                    </div>
                </Col>
                <Spin spinning={loading||runLoading}>
                    {
                        loading||runLoading ?
                            null
                            :
                            <Col>
                                {(permissions(auth.project.permissions, "operation.build.simulate-random.add") && projectStatus !== 2) ?
                                    <AuthButton type="primary" onClick={onAdd}><FormattedMessage id="common.add"/></AuthButton>
                                    : null
                                }
                            </Col>
                    }
                </Spin>
            </Row>
            <Spin spinning={loading||runLoading}>
                <Table
                    loading={loading||runLoading}
                    className="mar-top-10"
                    scroll={{x: "max-content"}}
                    dataSource={data}
                    pagination={false}
                    style={{overflowX: "auto"}}
                    rowKey={(record) => (record.id)}
                    expandable={{
                        expandedRowRender: record => expandedDetail(record),
                        expandIcon: ({expanded, onExpand, record}) =>
                            expanded ? (
                                <CaretDownOutlined onClick={e => onExpand(record, e)}/>
                            ) : (
                                <CaretRightOutlined onClick={e => onExpand(record, e)}/>
                            ),
                        columnWidth: 45,
                    }}
                >
                    <Table.Column title={<FormattedMessage id={"common.serial"} />} dataIndex="#" key="#" width={70}
                                  render={(text, record, index) => (index + 1)}
                    />
                    {projectType === 2 &&
                        <Table.Column title={<FormattedMessage id="projects.second"/>} dataIndex="cohortId"
                                      key="cohortId" render={(value, record, index) => (renderCohort(value))}
                                      ellipsis width={150}/>}
                    {projectType === 3 && <Table.Column title={<FormattedMessage id="report.attributes.random.stage"/>}
                                                        dataIndex="cohortId"
                                                        key="cohortId"
                                                        render={(value, record, index) => (renderCohort(value))}
                                                        ellipsis width={150}/>}
                    <Table.Column title={<FormattedMessage id="simulate_random.name"/>} dataIndex="name"
                                  key="name"
                        // fixed="left"
                                  ellipsis width={150}/>
                    <Table.Column title={<FormattedMessage id="projects.randomization.list"/>}
                                  dataIndex="simulateRandomList"
                                  key="simulateRandomList"
                                  ellipsis
                                  width={200}
                                  render={(value, record, index) => {
                                      if (value === null || value.length === 0) {
                                          return <span className={'.ant-steps-step-description'}>-</span>
                                      } else if (value.length === 1) {
                                          return (
                                              <span className={'.ant-steps-step-description'}>
                                        {value[0]}
                                    </span>
                                          );
                                      } else {
                                          return (
                                              <>
                                                  <Tooltip
                                                      placement="top"
                                                      title={
                                                          <>
                                                              {
                                                                  value.map((v) => {
                                                                      return <div>{v}</div>
                                                                  })
                                                              }
                                                          </>
                                                      }
                                                  >
                                            <span className={'.ant-steps-step-description'}>
                                                {
                                                    value[0] + "..."
                                                }
                                            </span>
                                                  </Tooltip>
                                              </>
                                          )
                                      }
                                  }}
                    />
                    <Table.Column title={<FormattedMessage id="simulate_random.site.count"/>}
                                  dataIndex="siteQuantity" width={100} key="siteQuantity" ellipsis/>
                    <Table.Column title={<FormattedMessage id="simulate_random.country.count"/>}
                                  dataIndex="countryQuantity" width={100} key="countryQuantity" ellipsis/>
                    <Table.Column title={<FormattedMessage id="simulate_random.region.count"/>}
                                  dataIndex="regionQuantity" width={100} key="regionQuantity" ellipsis/>
                    <Table.Column title={<FormattedMessage id="simulate_random.run.count"/>}
                                  dataIndex="runQuantity"
                                  key="runQuantity" width={100} ellipsis/>
                    <Table.Column title={<FormattedMessage id="simulate_random.subject.count"/>}
                                  dataIndex="subjectQuantity" key="subjectQuantity" width={100} ellipsis/>
                    <Table.Column
                        title={<FormattedMessage id="common.operation"/>}
                        width={g.lang === "zh" ? 200 : 220}
                        fixed="right"
                        render={
                            (value, record, index) => {
                                return operationButtons(record)
                            }
                        }
                    />
                </Table>
            </Spin>
            <Add bind={simulateRandomAdd} refresh={onList}/>
            <Overview/>
            <Detail/>

        </React.Fragment>
    )
}

const ArrowBlock = styled.div`
  &:hover > svg {
    transform: rotate(180deg);
  }
`