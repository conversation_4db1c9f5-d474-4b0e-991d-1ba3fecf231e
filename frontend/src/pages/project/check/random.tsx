import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    DatePicker,
    Descriptions,
    Form,
    List,
    Pagination,
    Row,
    Select,
    Spin,
    Table,
    Typography
} from "antd";
import {useIntl} from "react-intl";
import moment from "moment";
import {InfoCircleTwoTone, SearchOutlined} from "@ant-design/icons";
import {useSafeState} from "ahooks";
import Footer from "../../main/layout/footer";
import {useFetch} from "../../../hooks/request";
import {getRandomization} from "../../../api/randomization";
import {checkRandomList, getProjectSite} from "../../../api/check";
import {useAuth} from "../../../context/auth";


export const Random = () => {
    const RangePicker:any= DatePicker.RangePicker
    const [currentPage, setCurrentPage] = useSafeState<any>(1);
    const [pageSize, setPageSize] = useSafeState<any>(20);
    const [total, setTotal] = useSafeState<any>(0);
    const [errorCount, setErrorCount] = useSafeState<any>(0);
    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;
    const [form] = Form.useForm();
    const [randomization, setRandomization] = useSafeState<any>({});
    const [data, setData] = useSafeState<any>([]);
    const [columns, setColumns] = useSafeState<any>([]);
    const {Text} = Typography;
    const [envId, setEnvId] = useSafeState<any>(auth.env ? auth.env.id : null);
    const [cohortId, setCohortId] = useSafeState<any>(auth.cohort ? auth.cohort.id : null);
    const [customerId, setCustomerId] = useSafeState<any>(auth.customerId);
    const [projectId, setProjectId] = useSafeState<any>(auth.project.id);
    const [cohortOption, setCohortOption] = useSafeState<any>([]);
    const [siteOption, setSiteOption] = useSafeState<any>([]);
    const [project, setProject] = useSafeState<any>([]);

    const {runAsync: getRandomizationRun, loading: getRandomizationLoading} = useFetch(getRandomization, {manual: true})
    const {runAsync: checkRandomListRun, loading: checkRandomListLoading} = useFetch(checkRandomList, {manual: true})
    const {runAsync: getProjectSiteRun, loading: getProjectSiteLoading} = useFetch(getProjectSite, {manual: true})


    const getList = () => {
        setData([])
        setColumns([])
        form.validateFields().then(values => {
            getRandomizationRun({projectId, env: envId, cohort: cohortId ? cohortId : null, customer: customerId}).then(
                (d: any) => {
                    setRandomization(d.data)
                }
            )
            let request = {...values}
            if (form.getFieldsValue().time) {
                // @ts-ignore
                const startAt = parseInt(String(moment(form.getFieldsValue().time[0]).startOf("day").format("x") / 1000))
                // @ts-ignore
                const endAt = parseInt(String(moment(form.getFieldsValue().time[1]).endOf("day").format("x") / 1000));

                request["time"] = [startAt, endAt]
            }
            request["skip"] = currentPage
            request["limit"] = pageSize
            checkRandomListRun({
                envId: envId,
                cohortId: cohortId,
                projectId: projectId,
                customerId: customerId,
                ...request
            }).then(
                (result: any) => {
                    let json = result.data
                    const results = json["data"]
                    const group: any = []
                    if (results) {
                        const factors = new Set()
                        for (let i = 0; i < results.length; i++) {
                            if (results[i].factors) {
                                for (let j = 0; j < results[i].factors.length; j++) {
                                    results[i][results[i].factors[j].label] = results[i].factors[j].text
                                    factors.add(results[i].factors[j].label)
                                }
                            }
                        }
                        factors.forEach(label => {
                            group.push({width: 10, title: label, dataIndex: label, key: label});
                        })
                    }
                    const columns = [
                        {
                            title: '#',
                            dataIndex: '#',
                            key: '#',
                            width: 20,
                            render: (text: any, record: any, index: any) => ((currentPage-1)*pageSize+ index + 1)
                        },
                        {
                            title: formatMessage({id: 'subject.number'}),
                            width: 10,
                            dataIndex: 'subject',
                            key: 'subject',
                            render: (text: any, record: any, index: any) =>
                                ((record.randomGroup !== record.blindRandomGroup || record.randomNumber !== record.blindRandomNumber) ?
                                        <div color="#FF0000">{text}</div> :
                                        text
                                )
                        },
                        ...group,
                        {
                            title: formatMessage({id: 'check.affiliated.site'}),
                            width: 10,
                            dataIndex: 'siteName',
                            key: 'siteName',
                            render: (text: any, record: any, index: any) =>
                                (record.siteNumber + '-' + text)
                        },
                        {
                            title: formatMessage({id: 'check.random.group'}),
                            width: 10,
                            dataIndex: 'randomGroup',
                            key: 'randomGroup'
                        },
                        {
                            title: formatMessage({id: 'projects.randomization.randomNumber'}),
                            width: 10,
                            dataIndex: 'randomNumber',
                            key: 'randomNumber'
                        },
                        {
                            title: formatMessage({id: 'check.blind.random.group'}),
                            width: 10,
                            dataIndex: 'blindRandomGroup',
                            key: 'blindRandomGroup'
                        },
                        {
                            title: formatMessage({id: 'check.blind.random.number'}),
                            width: 10,
                            dataIndex: 'blindRandomNumber',
                            key: 'blindRandomNumber'
                        },
                        {
                            title: formatMessage({id: 'check.random.time'}),
                            width: 10,
                            dataIndex: 'randomTime',
                            key: 'randomTime',
                            render: (text: any, record: any, index: any) => {
                                const timezone = record.utc ? record.utc.substr(4) : 8
                                return (((text === null || text === 0) ? '' : moment.unix(text).utc().add(timezone, "hour").format('YYYY-MM-DD HH:mm:ss')) + (record.utc ? '(' + record.utc + ')' : '(UTC+8)'))
                            }
                        }
                    ]
                    setData(results)
                    setColumns(columns)
                    setTotal(json["total"])
                    setErrorCount(json["errorCount"])
                }
            )
        })

    };
    const getProject = () => {
        getProjectSiteRun().then(
            (result: any) => {
                let data = result.data
                if (data) {
                    setProject(data)
                    if (data.findIndex((value: any) => envId === value.env_id) !== -1) {
                        const cohorts = data.find((value: any) => envId === value.env_id).cohorts
                        const site = data.find((value: any) => envId === value.env_id).site
                        setCohortOption(cohorts)
                        setSiteOption(site)
                        form.setFieldsValue(
                            {
                                env: envId,
                                cohort: cohortId,
                                site: [],
                                time: [moment().subtract(10, "days"), moment()]
                            })
                        getList()
                    }
                }
            }
        )
    }
    const projectChange = (e: any) => {
        form.setFieldsValue({cohort: [], site: []})
        if (e) {
            const project_id = project.find((value: any) => e === value.env_id).project_id
            const customer_id = project.find((value: any) => e === value.env_id).customer_id
            const env_id = project.find((value: any) => e === value.env_id).env_id
            const cohorts = project.find((value: any) => e === value.env_id).cohorts
            const site = project.find((value: any) => e === value.env_id).site
            setCohortOption(cohorts)
            setSiteOption(site)
            setProjectId(project_id)
            setCustomerId(customer_id)
            setEnvId(env_id)
            setData([])
            if (cohorts.length === 0) {
                setCohortId(null)
                form.setFieldsValue({site: []})
            } else {
                setCohortId(cohorts[0].id)
                form.setFieldsValue({site: [], cohort: cohorts[0].id})
            }
        } else {
            setCohortOption([])
            setSiteOption([])
            setProjectId(null)
            setCustomerId(null)
            setEnvId(null)
            setData(null)
            setCohortId(null)
        }

    }
    const cohortChange = (e: any) => {
        setCohortId(e)
    }
    React.useEffect(getProject, []);
    React.useEffect(getList, [envId, cohortId, currentPage, pageSize]);


    return (
        <React.Fragment>
            <Form form={form} >
                <Row gutter={8} justify="start">
                    <Col className="mar-all-10">
                        <Form.Item name="env" label={formatMessage({id: 'check.select.project'})}
                                   rules={[{required: true}]}>
                            <Select allowClear style={{width: 200}} onChange={(e) => {
                                projectChange(e)
                            }}>
                                {
                                    project.map((value: any) =>
                                        <Select.Option title={value.number + "-" + value.name} key={value.env_id}
                                                       value={value.env_id}>{value.number}-{value.name}</Select.Option>
                                    )
                                }
                            </Select>
                        </Form.Item>
                    </Col>
                    {
                        cohortOption.length > 0 ?
                            <Col className="mar-all-10">
                                <Form.Item name="cohort" label={formatMessage({id: 'check.cohort'})}
                                           rules={[{required: true}]}>
                                    <Select allowClear style={{width: 200}} onChange={(e) => {
                                        cohortChange(e)
                                    }}>
                                        {
                                            cohortOption.map((value: any) =>
                                                <Select.Option title={value.name} key={value.id}
                                                               value={value.id}>{value.name}</Select.Option>
                                            )
                                        }
                                    </Select>
                                </Form.Item>
                            </Col>
                            :
                            null
                    }

                    <Col className="mar-all-10">
                        <Form.Item name="site" label={formatMessage({id: 'check.select.site'})}
                        >
                            <Select placeholder={formatMessage({id: 'common.sites'})} mode="multiple" allowClear
                                    style={{width: 200}} maxTagCount='responsive'>
                                {
                                    siteOption.map((value: any) =>
                                        <Select.Option title={value.number + "-" + value.name} key={value._id}
                                                       value={value._id}>{value.number}-{value.name}</Select.Option>
                                    )
                                }
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col className="mar-all-10">
                        <Form.Item name="time" label={formatMessage({id: 'check.select.time'})}
                        >
                            <RangePicker
                                placeholder={[formatMessage({id: 'check.select.unlimited'}), formatMessage({id: 'check.select.unlimited'})]}/>
                        </Form.Item>
                    </Col>
                    <Col className="mar-all-10">
                        <Button onClick={getList}><SearchOutlined style={{ color: "#BFBFBF" }} /> {formatMessage({id: 'check.search'})}</Button>
                    </Col>
                </Row>
            </Form>
            <Card title={<div><InfoCircleTwoTone/> {formatMessage({id: 'projects.randomization.configure'})}</div>}
                  headStyle={{backgroundColor: "#EDECECEC"}}
                  bodyStyle={{backgroundColor: "#EDECECEC"}}>
                <Descriptions>
                    <Descriptions.Item label={formatMessage({id: 'randomization.config.type'})}>
                        {randomization.type === 1 ?
                            formatMessage({id: 'projects.randomization.blockRandom'})
                            :
                            randomization.type === 2 ?
                                formatMessage({id: 'projects.randomization.minimize'})
                                :
                                ""
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label={formatMessage({id: 'projects.randomization.group'})}>
                        <List
                            dataSource={randomization.groups ? randomization.groups : []}
                            renderItem={(item: any) => (
                                <>
                                    {formatMessage({id: 'randomization.config.code'})}:{item.code}, {formatMessage({id: 'common.name'})}:{item.name}<br/>
                                </>
                            )}
                        />
                    </Descriptions.Item>
                </Descriptions>
                <Descriptions>
                    <Descriptions.Item label={formatMessage({id: 'randomization.config.factor'})}>
                        {
                            randomization.factors ? randomization.factors.map((item: any) => (
                                <Row>
                                    {item.label}:
                                    <List
                                        dataSource={item.options ? item.options : []}
                                        renderItem={(i: any) => (
                                            <div style={{marginLeft: "5px", marginRight: "100px"}}>
                                                {i.label}<br/>
                                            </div>
                                        )}
                                    />
                                </Row>
                            )) : null
                        }
                    </Descriptions.Item>
                </Descriptions>
            </Card>
            <Spin spinning={getRandomizationLoading || checkRandomListLoading}>
                <Text strong={true}>{formatMessage({id: 'dynamic.monitoring.total.random'})}:{total}{' '}</Text>
                <Text strong={true}>{formatMessage({id: 'dynamic.monitoring.random.error'})}:{<div
                    color="#FF0000">{errorCount}</div>}</Text>
                <Table
                    size="small"
                    dataSource={data}
                    pagination={false}
                    rowKey={(record) => (record.id)}
                    columns={columns}
                >
                </Table>
            </Spin>
            <Footer>
                <Pagination
                    hideOnSinglePage={false}
                    className="text-right"
                    current={currentPage}
                    pageSize={pageSize}
                    pageSizeOptions={['10', '20', '50', '100']}
                    total={total}
                    showSizeChanger
                    showTotal={(total, range) => (`${range[0]} - ${range[1]} / ${total}`)}
                    onChange={(page, pageSize) => {
                        setCurrentPage(page);
                    }}
                    onShowSizeChange={(current, size) => {
                        setCurrentPage(1);
                        setPageSize(size);
                    }}
                />
            </Footer>
        </React.Fragment>
    )
}