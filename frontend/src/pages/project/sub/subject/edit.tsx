import React, {useEffect} from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {Button, Checkbox, Col, Form, Input, InputNumber, message, Modal, Radio, Row, Select, Spin, Switch} from "antd";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {updateActualFactor,} from "../../../../api/subject";
import styled from "@emotion/styled";
import DatePicker from "../../../../components/DatePicker";
import dayjs from "dayjs";
import {useSubject} from "./context";
import {useGlobal} from "../../../../context/global";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";
import {useForm} from "antd/es/form/Form.js";
import {useAtom} from "jotai";
import {editVisibleAtom} from "./ctx";
import {AuthButton} from "../../../common/auth-wrap";

export const Edit = (props: any) => {
    const g = useGlobal();
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const DatePickers: any = DatePicker;
    const subjectCtx = useSubject()
    const [form] = useForm()
    const roleId = auth.project.permissions.role_id;
    const projectType = auth.project.info.type;
    const [editVisible, setEditVisible] = useAtom(editVisibleAtom)
    const fields = subjectCtx.selectRecord?.form.fields
    const { runAsync: updateActualFactorRun, loading: updateActualFactorLoading } = useFetch(updateActualFactor, {
        manual: true,
    });
    const hide = () => {
        setEditVisible(false)
        form.resetFields()
    };
    useEffect(() => {
        if (subjectCtx.selectRecord !== null && subjectCtx.selectRecord !== undefined){
            handleSubjectData(subjectCtx.selectRecord,subjectCtx.selectRecord?.form?.fields)

        }
    }, [subjectCtx.selectRecord,editVisible]);
    const handleSubjectData = (subjectData: any, fields: any) => {
        form.resetFields()
        let res: any = {};
        subjectData.actualInfo?.forEach((info: any) => {
            fields?.forEach((field: any) => {
                if (field.isCalc === false) {
                    if (info.name === field.name && field.type === "datePicker") {
                        res[info.name] = info.value ? dayjs(info.value,"YYYY-MM-DD") : undefined;
                    } else if (
                        info.name === field.name &&
                        field.type === "timePicker"
                    ) {
                        res[info.name] = info.value
                            ? dayjs(info.value, "YYYY-MM-DD HH:mm:ss")
                            : undefined;
                    } else if (info.name === field.name) {
                        res[info.name] = info.value;
                    }
                }

            });
        });
        if (subjectData.joinTime !== null && subjectData.joinTime !== "") {
            res["joinTime"] =  dayjs(subjectData.joinTime, "YYYY-MM-DD")
        }
        if (subjectData.joinTimeForTime !== null && subjectData.joinTimeForTime !== 0){
            res["joinTime"] = dayjs(subjectData.joinTimeForTime * 1000).add(subjectCtx.selectRecord?.timeZone,"hour")
        }
        form.setFieldsValue(res);
    };
    const save = ()=>{
        form.validateFields().then((values: any) => {
            let fromData = handleFromData(subjectCtx.selectRecord?.form?.fields, values);
        updateActualFactorRun({id: subjectCtx.selectRecord?.id, roleId:roleId,...fromData})
                .then((result: any)=>{
                    message.success(result.msg).then()
                    props.refresh()
                    hide()
            })
        })
    }
    // 处理存入数据库的数据（日期）
    const handleFromData = (fields: any, fromData: any) => {
        let actualInfo: any = [];
        fields.filter((field: any) => (field.isCustomFormula === true || field.stratification === true ) && (field.status == null || field.status === 1) ).forEach((field: any) => {
            if (field.type === "datePicker" && field.isCalc === false) {
                let value = fromData[field.name]
                    ? dayjs(fromData[field.name]).format("YYYY-MM-DD")
                    : "";
                // 日期类型
                actualInfo.push({"name":field.name,"value":value})
            } else if (field.type === "timePicker" && field.isCalc === false) {
                // 时间类型
                let value = fromData[field.name]
                    ? dayjs(fromData[field.name]).format("YYYY-MM-DD HH:mm:ss")
                    : "";
                actualInfo.push({"name":field.name,"value":value})
            }else {
                actualInfo.push({"name":field.name,"value":fromData[field.name]})
            }
        });
        if (fromData["joinTime"] != null) {
            //计算时间戳
            fromData["joinTime"] = dayjs(fromData["joinTime"]).unix()
        }
        fromData["actualInfo"] = actualInfo
        return fromData;
    };
    const handlePrecision = (field:any) => {
        if (field.formatType === "numberLength"){
            return 0
        }else if (field.formatType === "decimalLength"){
            if (field.length) {
                // 将数字转换为字符串
                var str = field.length.toString();
                // 使用split()方法将整数部分和小数部分分开
                var parts = str.split('.');
                return  parts[1];
            }
        }else {
            return 0
        }
    };

    const handleStep = (field:any) => {
        if (field.formatType === "numberLength"){
            return 1
        }else if (field.formatType === "decimalLength"){
            if (field.length) {
                // 将数字转换为字符串
                var str = field.length.toString();
                // 使用split()方法将整数部分和小数部分分开
                var parts = str.split('.');

                return  10 ** (-parts[1]);
            }
        }else {
            return 1
        }
    };
    return (
        <>
            <Modal
                className="custom-medium-modal"
                title={
                    subjectCtx.selectRecord?.attribute.info.random ?
                        <>
                            <span style={{fontWeight: 500}}>{formatMessage({id: "subject.register.edit"}) + "-"}</span>
                            <span style={{fontWeight: 500}}>{formatMessage({id: "projects.attributes.random.minimize.calc.actual.factor"})}</span>
                        </> : <>
                            <span style={{fontWeight: 500}}>{formatMessage({id: "subject.register.edit"})}</span>
                        </>}
                open={editVisible}
                onCancel={hide}
                centered
                maskClosable={false}
                keyboard={false}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{ marginRight: "12px" }}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            {
                                <AuthButton
                                    show
                                    previewProps={{disabledClick: true}}
                                    onClick={save}
                                    type="primary"
                                    loading={updateActualFactorLoading}
                                >
                                    <FormattedMessage id="common.ok" />
                                </AuthButton>
                            }
                        </Col>
                    </Row>
                }
            >
                <StyleFrom
                    form={form}
                    layout={"vertical"}
                >

                    <Spin spinning={false}>
                        <Form.Item
                            label={formatMessage({id:"subject.number"})}
                        >
                            <span>{subjectCtx.selectRecord?.shortname}</span>
                        </Form.Item>
                        {!(subjectCtx.selectRecord?.attribute?.info.random) ? (
                            <Form.Item
                                rules={[{ required: true}]}
                                label={formatMessage({id:"check.select.time"})}
                                name={"joinTime"}
                            >
                                <DatePickers
                                    showTime
                                    disabledDate={(current: any) => {
                                        return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                                    }}
                                ></DatePickers>
                            </Form.Item>
                        ) : null}

                        {subjectCtx.selectRecord?.group !== ""
                            && fields?.filter((field: any, index: number)=>{
                                return (field.isCustomFormula === true || field.stratification === true) && field.status !== 2
                            }).map((field: any, index: number) => (
                                <>
                                    {field.type === "input" && field.calcType == null ? (
                                        <div>
                                            {field.name === "shortname" ?
                                                (
                                                    <Form.Item
                                                        label={(field.labelEn === "" && field.label === "") ? (
                                                            <FormattedMessage
                                                                id={"subject.number"}/>) : ((field.labelEn !== "" && field.label === "") ? (g.lang === "en" ? field.labelEn : formatMessage({id: "subject.number"})) : ((field.labelEn === "" && field.label !== "") ? field.label : ((g.lang === "en") ? field.labelEn : field.label)))}
                                                        name={field.name}
                                                        style={{
                                                            marginBottom:
                                                                index + 1 !==
                                                                fields.length
                                                                    ? "16px"
                                                                    : "0px",
                                                        }}
                                                    >
                                                        <span style={{marginTop: 6, marginLeft: 6, fontWeight: 500}}>
                                                            {
                                                                projectType === 3 && !inRandomIsolation(auth.project.info.number) && subjectCtx.selectRecord?.reRandomInfo != null && subjectCtx.selectRecord?.reRandomInfo.length > 0 ?
                                    subjectCtx.selectRecord?.reRandomInfo[0].value
                                    :
                                    subjectCtx.selectRecord?.shortname
                                                            }
                                                        </span>
                                                    </Form.Item>
                                                ) : (
                                                    <Form.Item
                                                        style={{
                                                            marginBottom:
                                                                index + 1 !==
                                                                fields.length
                                                                    ? "16px"
                                                                    : "0px",
                                                        }}
                                                        rules={[
                                                            {
                                                                required:
                                                                    field.required,
                                                            },
                                                            {
                                                                max: field.length,
                                                                message:
                                                                    formatMessage(
                                                                        {
                                                                            id: "form.control.type.format.limit",
                                                                        },
                                                                        {
                                                                            length: field.length,
                                                                        }
                                                                    ),
                                                            },
                                                        ]}
                                                        label={field.label}
                                                        name={field.name}
                                                    >
                                                        <Input
                                                            allowClear
                                                            className="full-width"
                                                        />
                                                    </Form.Item>
                                                )}
                                        </div>
                                    ):null }
                                    {field.type === "inputNumber" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[
                                                { required: field.required },
                                                {
                                                    validator: (_, value) => {
                                                        if (value !== null && value != undefined) {
                                                            if (field.range && field.range.min && field.range.min > value) {
                                                                return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                            }
                                                            if (field.range && field.range.max && field.range.max < value) {
                                                                return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                            }
                                                            if (field.formatType !== undefined && field.formatType === "decimalLength") {
                                                                if (field.length) {
                                                                    // 将数字转换为字符串
                                                                    var str = field.length.toString();
                                                                    // 使用split()方法将整数部分和小数部分分开
                                                                    var parts = str.split('.');
                                                                    //取整数部分
                                                                    let numberPart = parts[0];
                                                                    var valueDecimalPart = value.toString().split('.');
                                                                    if (valueDecimalPart && valueDecimalPart[0] && valueDecimalPart[0].length > numberPart) {
                                                                        return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                    }
                                                                    if (valueDecimalPart && valueDecimalPart[1] && parts[1] && valueDecimalPart[1].length > parts[1]) {
                                                                        return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                    }
                                                                }
                                                            } else if (field.formatType !== undefined && field.formatType === "numberLength") {
                                                                if (
                                                                    field.length &&
                                                                    value != null &&
                                                                    value.toString()
                                                                        .length >
                                                                    field.length
                                                                ) {
                                                                    return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                }
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                },
                                            ]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <InputNumber
                                                min={0}
                                                precision={handlePrecision(field)}
                                                step={handleStep(field)}
                                                className="full-width"
                                                placeholder={formatMessage({ id: 'common.required.prefix' })}

                                            />
                                        </Form.Item>
                                    ) : null}
                                    {field.type === "textArea" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[
                                                { required: field.required },
                                                {
                                                    max: field.length,
                                                    message: formatMessage(
                                                        {
                                                            id: "form.control.type.format.limit",
                                                        },
                                                        { length: field.length }
                                                    ),
                                                },
                                            ]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <Input.TextArea
                                                allowClear
                                                className="full-width"
                                                placeholder={formatMessage({ id: 'common.required.prefix' })}

                                            />
                                        </Form.Item>
                                    ) : null}

                                    {field.type === "select" ?
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <Select
                                                className="full-width"
                                                options={field.options}
                                                placeholder={formatMessage({ id: 'placeholder.select.common' })}

                                            ></Select>
                                        </Form.Item>
                                        : null
                                    }

                                    {field.type === "checkbox" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <Checkbox.Group
                                                options={field.options}
                                            ></Checkbox.Group>
                                        </Form.Item>
                                    ) : null}

                                    {field.type === "radio" ?
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <Radio.Group
                                                options={field.options}
                                            ></Radio.Group>
                                        </Form.Item>
                                        : null
                                    }

                                    {field.type === "switch" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                            valuePropName="checked"
                                        >
                                            <Switch
                                                defaultChecked={false}
                                            />
                                        </Form.Item>
                                    ) : null}
                                    {field.type === "datePicker" && field.calcType == null ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <DatePickers
                                                disabledDate={(current: any) => {
                                                    if (
                                                        field.range?.min &&
                                                        field.range?.max
                                                    ) {
                                                        return (
                                                            dayjs().year(
                                                                field.range.min
                                                            ) > current ||
                                                            current >
                                                            dayjs().year(
                                                                field.range
                                                                    .max
                                                            )
                                                        );
                                                    }
                                                    return (
                                                        current &&
                                                        current <
                                                        dayjs()
                                                            .year(1920)
                                                            .month(0)
                                                            .date(0)
                                                    );
                                                }}
                                                picker={field.datePicker}
                                                format={field.dateFormat}
                                                className="full-width"
                                            ></DatePickers>
                                        </Form.Item>
                                    ) : null}
                                    {field.type === "timePicker" ? (
                                        <Form.Item
                                            style={{
                                                marginBottom:
                                                    index + 1 !== fields.length
                                                        ? "16px"
                                                        : "0px",
                                            }}
                                            rules={[{ required: field.required }]}
                                            label={field.label}
                                            name={field.name}
                                        >
                                            <DatePickers
                                                showTime
                                                className="full-width"
                                                format={field.timeFormat}
                                            />
                                        </Form.Item>
                                    ) : null}
                                </>
                            ))}
                    </Spin>
                </StyleFrom>
            </Modal>
        </>
    );
};

const StyleFrom = styled(Form)`
    .ant-form-item-label label {
        height: auto;
        color: #4e5969;
        opacity: 0.8;
    }
`;
const TitleContent = styled.div`
    height: 32px;
    width: 552px;
    left: 444px;
    top: 322px;
    border-radius: 2px;
    background: rgba(255, 174, 0, 0.06);
    border: 0.5px solid rgba(255, 174, 0, 0.5);
    border-radius: 2px;
`;