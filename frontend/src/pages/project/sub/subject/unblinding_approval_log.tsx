import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import { Button, Collapse, Empty, Form, message, Row, Spin, Steps, Tooltip, } from "antd";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { resendSms } from "../../../../api/subject";
import { unblindedApprovalStatus } from "../../../../data/data";
import { useGlobal } from "../../../../context/global";
import { useSubject } from "./context";
import moment from "moment";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import { TitleIcon } from "components/title";
import styled from "@emotion/styled";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import React from "react";

import EmptyImg from 'images/empty.png'
import ReactToPrint from "react-to-print";
export const UnBlindingApprovalLog = () => {
    const intl = useTranslation();
    const { formatMessage } = intl;
    const ctx = useSubject();
    const auth = useAuth();
    const g = useGlobal();
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;
    const activePanel = ctx.unblindingDetailsSign === "2" ?     // 紧急揭盲
        ctx.currentRecord &&
            ctx.currentRecord.urgentUnblindingApprovals &&
            ctx.currentRecord.urgentUnblindingApprovals.length === 1 ?
            ctx.currentRecord.urgentUnblindingApprovals[0].number : ""
        :
        ctx.currentRecord &&
            ctx.currentRecord.pvUrgentUnblindingApprovals &&
            ctx.currentRecord.pvUrgentUnblindingApprovals.length === 1 ?
            ctx.currentRecord.pvUrgentUnblindingApprovals[0].number : ""

    const {
        runAsync: resendSmsRun,
        loading: resendSmsLoading
    } = useFetch(resendSms, { manual: true })
    const onResendSms = (subjectId: any, number: any, unblindingDetailsSign: any) => {
        resendSmsRun({ subjectId: subjectId, approvalNumber: number, unblindingDetailsSign: unblindingDetailsSign })
            .then((result: any) => {
                message.success(result.msg)
            })
    }

    // 空状态
    if (ctx.unblindingDetailsSign === "2") {      // 紧急揭盲
        if (!ctx.currentRecord || !ctx.currentRecord.urgentUnblindingApprovals || ctx.currentRecord.urgentUnblindingApprovals.length <= 0) {
            return <Empty
                image={<img src={EmptyImg} alt='empty' />}>
            </Empty>
        }
    } else {
        if (!ctx.currentRecord || !ctx.currentRecord.pvUrgentUnblindingApprovals || ctx.currentRecord.pvUrgentUnblindingApprovals.length <= 0) {
            return <Empty
                image={<img src={EmptyImg} alt='empty' />}>
            </Empty>
        }
    }

    const approvals = ctx.unblindingDetailsSign === "2" ? ctx.currentRecord.urgentUnblindingApprovals : ctx.currentRecord.pvUrgentUnblindingApprovals;

    return (
        <>
            {
                (ctx.unblindingDetailsSign === "2" && permissions(auth.project.permissions, "operation.subject.unblinding-print")) ||
                    (ctx.unblindingDetailsSign === "3" && permissions(auth.project.permissions, "operation.subject.unblinding-pv-print"))

                    ?
                    <div style={{ paddingLeft: "92%", paddingBottom: 8 }}>
                        <ReactToPrint
                            trigger={() => {
                                return <Button>{<FormattedMessage id={"common.print"} />}</Button>;
                            }}
                            content={() => ctx.componentRef}
                        />
                    </div>
                    :
                    null
            }


            <div ref={el => (ctx.componentRef = el)}>
                <Collapse
                    defaultActiveKey={activePanel}
                    expandIconPosition="right"
                    expandIcon={(p) => {
                        if (p.isActive) {
                            return <span >{intl.formatMessage({ id: "common.collapse" })}<CaretUpOutlined style={{ marginLeft: 4 }} /></span>
                        }
                        return <span style={{ marginRight: 4 }}>{intl.formatMessage({ id: "common.expand" })}<CaretDownOutlined style={{ marginLeft: 4 }} /></span>
                    }}

                >
                    {
                        approvals?.map((it: any) =>
                            <Collapse.Panel
                                key={it.number}
                                header={
                                    <span style={{ color: "#1D2129", fontWeight: 600, fontSize: 14 }}>{it.number}</span>
                                }
                            >
                                <StepsContainer>
                                    <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                                        <TitleIcon />
                                        <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                                        <span style={{ marginLeft: 8, color: "#1D2129" }}>{it.number}</span>
                                    </div>
                                    <Steps progressDot current={it.status === 0 ? 0 : 1}>
                                        <Steps.Step
                                            title={formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}
                                            description={
                                                <>
                                                    <Row>
                                                        <span className={'.ant-steps-step-description'}>
                                                            {it.applicationByEmail}
                                                        </span>
                                                    </Row>
                                                    <Row>
                                                        <span className={'.ant-steps-step-description'}>
                                                            {moment.unix(it.applicationTime).utc().add(ctx.currentRecord.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                        </span>
                                                    </Row>
                                                </>
                                            } />
                                        <Steps.Step title={
                                            it.status === 0 ?
                                                <>
                                                    <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} />/
                                                    <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} />
                                                </> :
                                                it.status === 1 ?
                                                    <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} /> :
                                                    it.status === 2 ? <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} /> :
                                                        null
                                        }
                                            description={
                                                it.status === 0 ?
                                                    <>
                                                        <Tooltip
                                                            placement="top"
                                                            title={
                                                                <>
                                                                    {
                                                                        ctx.unblindingDetailsSign === "2" ?
                                                                            ctx.currentRecord?.unblindingApprovalUser != null && ctx.currentRecord?.unblindingApprovalUser.length > 0 ?
                                                                                ctx.currentRecord?.unblindingApprovalUser?.map((ccu: any) =>
                                                                                    <Row>
                                                                                        <span className={'.ant-steps-step-description'}>
                                                                                            {ccu.approvalName}/{ccu.approvalPhone}
                                                                                        </span>
                                                                                    </Row>
                                                                                )
                                                                                :
                                                                                null
                                                                            :
                                                                            ctx.currentRecord?.pvUnblindingApprovalUser != null && ctx.currentRecord?.pvUnblindingApprovalUser.length > 0 ?
                                                                                ctx.currentRecord?.pvUnblindingApprovalUser?.map((ccu: any) =>
                                                                                    <Row>
                                                                                        <span className={'.ant-steps-step-description'}>
                                                                                            {ccu.approvalName}/{ccu.approvalPhone}
                                                                                        </span>
                                                                                    </Row>
                                                                                )
                                                                                :
                                                                                null
                                                                    }
                                                                </>
                                                            }
                                                        >
                                                            <>
                                                                {
                                                                    ctx.unblindingDetailsSign === "2" ?
                                                                        ctx.currentRecord?.unblindingApprovalUser != null && ctx.currentRecord?.unblindingApprovalUser.length > 0 ?
                                                                            ctx.currentRecord?.unblindingApprovalUser?.map((ccu: any, index: number) =>
                                                                                index < 3 ?
                                                                                    <Row>
                                                                                        <span className={'.ant-steps-step-description'}>
                                                                                            {ccu.approvalName}/{ccu.approvalPhone}/{index}
                                                                                        </span>
                                                                                    </Row>
                                                                                    :
                                                                                    index == 3 ?
                                                                                        <Row>
                                                                                            <span className={'.ant-steps-step-description'}>
                                                                                                ...
                                                                                            </span>
                                                                                        </Row>
                                                                                        :
                                                                                        null
                                                                            )
                                                                            :
                                                                            null
                                                                        :
                                                                        ctx.currentRecord?.pvUnblindingApprovalUser != null && ctx.currentRecord?.pvUnblindingApprovalUser.length > 0 ?
                                                                            ctx.currentRecord?.pvUnblindingApprovalUser?.map((ccu: any, index: number) =>
                                                                                index < 3 ?
                                                                                    <Row>
                                                                                        <span className={'.ant-steps-step-description'}>
                                                                                            {ccu.approvalName}/{ccu.approvalPhone}
                                                                                        </span>
                                                                                    </Row>
                                                                                    :
                                                                                    index == 3 ?
                                                                                        <Row>
                                                                                            <span className={'.ant-steps-step-description'}>
                                                                                                ...
                                                                                            </span>
                                                                                        </Row>
                                                                                        :
                                                                                        null
                                                                            )
                                                                            :
                                                                            null
                                                                }
                                                            </>
                                                        </Tooltip>
                                                    </>
                                                    :
                                                    <div style={{ color: "#4E5969", marginLeft: -20 }}>
                                                        <span className={'.ant-steps-step-description'}>
                                                            {it.approvalByEmail}
                                                        </span>
                                                        <Row>
                                                            <span className={'.ant-steps-step-description'}>
                                                                {it.approvalTime !== 0 && moment.unix(it.approvalTime).utc().add(ctx.currentRecord.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                            </span>
                                                        </Row>
                                                    </div>
                                            }
                                        />
                                    </Steps>
                                </StepsContainer>
                                <Form
                                    size="small"
                                    labelCol={{ span: g.lang === 'en' ? 7 : 4 }}
                                >
                                    <Form.Item
                                        label={ctx.currentRecord.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? ctx.currentRecord.attribute.info.subjectReplaceText : ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
                                        <span>{ctx.currentRecord.shortname}</span>
                                    </Form.Item>
                                    <Form.Item
                                        label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                                        <span>{ctx.currentRecord.randomNumber}</span>
                                    </Form.Item>
                                    <Form.Item
                                        label={formatMessage({ id: 'subject.unblinding.reason' })}>
                                        <span>{it.reasonStr}</span>
                                        {it.remark !== "" ?
                                            <span>{' '}{it.remark}</span>
                                            : null}
                                    </Form.Item>
                                    <Form.Item
                                        label={formatMessage({ id: 'subject.unblinding.application.type' })}>
                                        <div style={{ display: "flex" }}>
                                            <span>{it.approvalType === 1 ? <FormattedMessage
                                                id={"common.approval.confirm"} /> :
                                                it.approvalType === 2 ? <FormattedMessage
                                                    id={"project.setting.checkbox.unblinded-code"} /> : null}</span>
                                            <span>{" "}</span>
                                            <span>
                                                {/*紧急揭盲发送短信按钮*/}
                                                {
                                                    (researchAttribute === 0 ?
                                                        permissionsCohort(auth.project.permissions, "operation.subject.unblinding-sms",ctx.currentRecord?.cohort?ctx.currentRecord.cohort.status:0) && (it.approvalType === 1 && auth.project.info.unblinding_sms === 1 && it.status === 0) && ctx.unblindingDetailsSign === "2"
                                                            ?
                                                            <Spin spinning={resendSmsLoading}>
                                                                <Button size={'small'} type={"link"} onClick={() => onResendSms(ctx.currentRecord.id, it.number, ctx.unblindingDetailsSign)}><FormattedMessage id={'subject.unblinding.application.resend'} /></Button>
                                                            </Spin>
                                                            :
                                                            null
                                                        :
                                                        permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-sms",ctx.currentRecord?.cohort?ctx.currentRecord.cohort.status:0)) && (it.approvalType === 1 && auth.project.info.unblinding_sms === 1 && it.status === 0) && ctx.unblindingDetailsSign === "2"
                                                        ?
                                                        <Spin spinning={resendSmsLoading}>
                                                            <Button size={'small'} type={"link"} onClick={() => onResendSms(ctx.currentRecord.id, it.number, ctx.unblindingDetailsSign)}><FormattedMessage id={'subject.unblinding.application.resend'} /></Button>
                                                        </Spin>
                                                        :
                                                        null
                                                }

                                                {/*pv揭盲发送短信按钮*/}
                                                {
                                                    researchAttribute === 0 && permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-sms",ctx.currentRecord?.cohort?ctx.currentRecord.cohort.status:0) && (it.approvalType === 1 && auth.project.info.pv_unblinding_sms === 1 && it.status === 0) && ctx.unblindingDetailsSign === "3" ?
                                                        <Spin spinning={resendSmsLoading}>
                                                            <Button size={'small'} type={"link"} onClick={() => onResendSms(ctx.currentRecord.id, it.number, ctx.unblindingDetailsSign)}><FormattedMessage id={'subject.unblinding.application.resend'} /></Button>
                                                        </Spin>
                                                        : null
                                                }
                                            </span>
                                        </div>
                                    </Form.Item>
                                    {
                                        it.status !== 0 ?
                                            <Form.Item
                                                label={formatMessage({ id: 'common.approval.confirm' })}>
                                                <span>{unblindedApprovalStatus.find(i => i.value === it.status)?.label}</span>
                                            </Form.Item> : null
                                    }
                                    {
                                        it.status === 2 ?
                                            <Form.Item
                                                style={{ marginBottom: 8 }}
                                                label={formatMessage({ id: 'common.reason' })}>
                                                <span>{it.rejectReason}</span>
                                            </Form.Item>
                                            : null
                                    }
                                </Form>
                            </Collapse.Panel>
                        )
                    }
                </Collapse>
            </div>
        </>
    )
}

const StepsContainer = styled.div`
    margin-bottom: 18px;
    padding: 16px 12px;
    border-radius: 2px;
    background: rgba(227,228,230,0.2);
    .ant-steps-label-vertical .ant-steps-item-content .ant-steps-item-description {
        text-align: left !important;
        width: 200px !important;
    }
    .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {
        background-color: #C8C9CC;
    }
`
