import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {
    <PERSON>ert,
    Button,
    Col,
    Empty,
    Form,
    Input,
    message,
    Modal,
    Radio,
    Result,
    Row,
    Select,
    Steps,
    Tabs,
    Tooltip
} from "antd";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { urgentUnblindingApplication, urgentUnblindingApproval } from "../../../../api/subject";
import { useSafeState, useUpdateEffect } from "ahooks";
import { useGlobal } from "../../../../context/global";
import { useSubject } from "./context";
import moment from "moment";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import { UnBlindingApprovalLog } from "./unblinding_approval_log";
import styled from "@emotion/styled";
import { TitleIcon } from "../../../../components/title";
import { UnblindingDetails } from "./unblinding_details";
import ReactToPrint from "react-to-print";
import EmptyImg from "../../../../images/empty.png";
import React from "react";

export const UnblindingApproval = (props) => {
    const g = useGlobal()
    const auth = useAuth();
    const ctx = useSubject()
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [title, setTitle] = useSafeState("");
    const [form] = Form.useForm();
    const [approvalForm] = Form.useForm();
    const [reasonType, setReasonType] = useSafeState(-1)
    const [approvalType, setApprovalType] = useSafeState(0)
    const [approvalAgree, setApprovalAgree] = useSafeState(0)
    const [applicationResultVisible, setApplicationResultVisible] = useSafeState(false)
    const [approvalResultVisible, setApprovalResultVisible] = useSafeState(false)
    const [approvalNumber, setApprovalNumber] = useSafeState("")
    const [applicationTime, setApplicationTime] = useSafeState("")
    const [approvalTime, setApprovalTime] = useSafeState("")
    const [approvalUserOne, setApprovalUserOne] = useSafeState("")
    const [approvalUserTwo, setApprovalUserTwo] = useSafeState("")
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;
    const [pendingApproval, setPendingApproval] = useSafeState()
    const [codeValidate, setCodeValidate] = useSafeState({})
    const [passwordValidate, setPasswordValidate] = useSafeState({})
    const tab4Footer = {
        footer:
            <Row justify="end">
                <Col>
                    <div>
                        <ReactToPrint
                            trigger={() => {
                                return <Button>{<FormattedMessage id={"common.print"} />}</Button>;
                            }}
                            content={() => ctx.componentRef}
                        />
                    </div>
                </Col>
            </Row>
    }

    const tab1 = ctx.unblindingDetailsSign === "2" ?     // 紧急揭盲
        (researchAttribute === 0 ?
            permissionsCohort(auth.project.permissions, "operation.subject.unblinding-application",ctx.currentRecord?.cohort.status) :
            permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-application",ctx.currentRecord?.cohort.status))
        && ctx.currentRecord && ctx.currentRecord.urgentUnblindingStatus === 0 && (!ctx.currentRecord.urgentUnblindingApprovals || ctx.currentRecord.urgentUnblindingApprovals.every(i => i.status !== 0))
        :                                               // pv揭盲
        permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-application",ctx.currentRecord?.cohort.status)
        && ctx.currentRecord && ctx.currentRecord.pvUnblindingStatus !== 1 && (!ctx.currentRecord.pvUrgentUnblindingApprovals || ctx.currentRecord.pvUrgentUnblindingApprovals.every(i => i.status !== 0))

    const tab2 = ctx.unblindingDetailsSign === "2" ?     // 紧急揭盲
        (researchAttribute === 0 ?
            permissionsCohort(auth.project.permissions, "operation.subject.unblinding-approval",ctx.currentRecord?.cohort.status) :
            permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-approval",ctx.currentRecord?.cohort.status))
        && ctx.currentRecord && ctx.currentRecord.urgentUnblindingStatus === 0 && ctx.currentRecord.urgentUnblindingApprovals && ctx.currentRecord.urgentUnblindingApprovals.find(i => i.status === 0)
        :
        permissionsCohort(auth.project.permissions, "operation.subject.unblinding-pv-approval",ctx.currentRecord?.cohort.status)
        && ctx.currentRecord && ctx.currentRecord.pvUnblindingStatus !== 1 && ctx.currentRecord.pvUrgentUnblindingApprovals && ctx.currentRecord.pvUrgentUnblindingApprovals.find(i => i.status === 0)

    const tab4 = ctx.currentRecord && ((ctx.unblindingDetailsSign === "2" && ctx.currentRecord.status === 4 || ctx.currentRecord.status === 6 || ctx.currentRecord.status === 9) || (ctx.unblindingDetailsSign === "3" && (ctx.currentRecord.status === 3 || ctx.currentRecord.status === 4 || ctx.currentRecord.status === 6 || ctx.currentRecord.status === 9) && ctx.currentRecord.pvUnblindingStatus === 1))

    const tab3 = ctx.unblindingDetailsSign === "2" ?     // 紧急揭盲
        (researchAttribute === 0 ?
            permissions(auth.project.permissions, "operation.subject.unblinding-log") :
            permissions(auth.project.permissions, "operation.subject-dtp.unblinding-log"))
        && ctx.currentRecord && auth.project.info.unblinding_control && auth.project.info.unblinding_control === 1
        :                                                // pv揭盲
        permissions(auth.project.permissions, "operation.subject.unblinding-pv-log")
        && ctx.currentRecord && auth.project.info.unblinding_control && auth.project.info.unblinding_control === 1

    const [footer, setFooter] = useSafeState({ footer: null })
    const afterUrgentUnblindingApplicationError = (e, params) => {
        const contentType = e.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") > -1) {
            e.json().then(
                (result) => {
                    switch (result.code) {
                        case 1000:
                            setCodeValidate({ validateStatus: "error", help: result.msg })
                            break;
                        case 1001:
                            setPasswordValidate({ validateStatus: "error", help: result.msg })
                            break;
                        case 1003:
                            setPasswordValidate({ validateStatus: "error", help: result.msg[0] })
                            setCodeValidate({ validateStatus: "error", help: result.msg[1] })
                            break;
                        default:
                            message.error(result.msg).then()
                            break;
                    }
                }
            );
        }
    }

    const {
        runAsync: urgentUnblindingApplicationRun,
        loading: urgentUnblindingApplicationLoading
    } = useFetch(urgentUnblindingApplication, { manual: true, onError: afterUrgentUnblindingApplicationError })

    const {
        runAsync: urgentUnblindingApprovalRun,
        loading: urgentUnblindingApprovalLoading
    } = useFetch(urgentUnblindingApproval, { manual: true })



    const hide = () => {
        ctx.setCurrentRecord(null)
        setFooter({ footer: null })
        setApprovalType(0)
        setApprovalAgree(0)
        setReasonType(-1)
        setPendingApproval()
        ctx.setUnblindingApprovalVisible(false)
        ctx.setPvUnblindingApprovalVisible(false)
        ctx.setUnblindingDetailsSign("")
        setApplicationResultVisible(false)
        setApprovalResultVisible(false)
        ctx.setRefresh(ctx.refresh + 1)
        form.resetFields()
        approvalForm.resetFields()
        setApplicationTime("")
        setApprovalUserOne("")
        setApprovalUserTwo("")
        setCodeValidate({})
    };

    // 揭盲申请
    const applicationOrApprovalOrUnblinding = () => {
        if (pendingApproval) {
            approvalForm.validateFields()
                .then(values => {
                    urgentUnblindingApprovalRun({
                        projectId: auth.project.id,
                        envId: auth.env.id,
                        attributeId: ctx.currentRecord.attribute.id,
                        subjectId: ctx.currentRecord.id,
                        approvalNumber: pendingApproval.number,
                        roleId: auth.project.permissions.role_id,
                        unblindingSign: ctx.unblindingDetailsSign,
                        ...values
                    }).then((result) => {
                        const resp = result.data;
                        setApprovalTime(moment.unix(resp.approvalTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss'));
                        setApprovalResultVisible(true);
                    })
                })
                .catch(error => {
                })
        } else {
            form.validateFields()
                .then(values => {
                    // pv揭盲
                    if (ctx.unblindingDetailsSign === "3") {
                        values.approvalType = 1
                    }
                    setCodeValidate({})
                    setPasswordValidate({})
                    urgentUnblindingApplicationRun({
                        projectId: auth.project.id,
                        envId: auth.env.id,
                        attributeId: ctx.currentRecord.attribute.id,
                        subjectId: ctx.currentRecord.id,
                        unblindingSign: ctx.unblindingDetailsSign,
                        roleId: auth.project.permissions.role_id,
                        ...values
                    }).then((result) => {
                        if (approvalType === 2) {
                            ctx.setUnblindingApprovalVisible(false)
                            ctx.setPvUnblindingApprovalVisible(false)
                            message.success(formatMessage({ id: 'subject.unblinding.success' }))
                            ctx.setCurrentRecord(null)
                            ctx.setRefresh(ctx.refresh + 1)
                        } else {
                            const resp = result.data
                            setApprovalNumber(resp.approvalNumber)
                            setApplicationTime(moment.unix(resp.applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss'))
                            setApplicationResultVisible(true)
                        }
                    })
                })
        }
        //可审批用户
        // approvedUsers();
    };

    // 组装可审批用户
    const approvedUsers = () => {
        // 展示可揭盲的用户信息
        if (ctx.unblindingDetailsSign === "2") {   // 紧急揭盲
            if (ctx.currentRecord?.unblindingApprovalUser != null && ctx.currentRecord?.unblindingApprovalUser.length > 0) {
                let uau = [];
                for (let i = 0; i < ctx.currentRecord?.unblindingApprovalUser.length; i++) {
                    let namePhone = ctx.currentRecord?.unblindingApprovalUser[i].approvalName + "/" + ctx.currentRecord?.unblindingApprovalUser[i].approvalPhone;
                    uau.push(namePhone);
                    if (i == 2) {
                        break
                    }
                }
                if (ctx.currentRecord?.unblindingApprovalUser.length > 3) {
                    uau.push("...");
                }
                setApprovalUserOne(
                    ctx.currentRecord?.unblindingApprovalUser.map((ccu) =>
                        <Row>
                            <span className={'.ant-steps-step-description'}>
                                {ccu.approvalName}/{ccu.approvalPhone}
                            </span>
                        </Row>
                    )
                )
                setApprovalUserTwo(
                    uau.map((ccu) =>
                        <Row>
                            <span className={'.ant-steps-step-description'}>
                                {ccu}
                            </span>
                        </Row>
                    )
                )
            }
        } else if (ctx.unblindingDetailsSign === "3") {  // pv揭盲
            if (ctx.currentRecord?.pvUnblindingApprovalUser != null && ctx.currentRecord?.pvUnblindingApprovalUser.length > 0) {
                let puau = [];
                for (let i = 0; i < ctx.currentRecord?.pvUnblindingApprovalUser.length; i++) {
                    let namePhone = ctx.currentRecord?.pvUnblindingApprovalUser[i].approvalName + "/" + ctx.currentRecord?.pvUnblindingApprovalUser[i].approvalPhone;
                    puau.push(namePhone);
                    if (i == 2) {
                        break
                    }
                }
                if (ctx.currentRecord?.pvUnblindingApprovalUser.length > 3) {
                    puau.push("...");
                }
                setApprovalUserOne(
                    ctx.currentRecord?.pvUnblindingApprovalUser.map((ccu) =>
                        <Row>
                            <span className={'.ant-steps-step-description'}>
                                {ccu.approvalName}/{ccu.approvalPhone}
                            </span>
                        </Row>
                    )
                )
                setApprovalUserTwo(
                    puau.map((ccu) =>
                        <Row>
                            <span className={'.ant-steps-step-description'}>
                                {ccu}
                            </span>
                        </Row>
                    )
                )
            }
        }
    }

    useUpdateEffect(
        () => {
            if (ctx.unblindingApprovalVisible && ctx.currentRecord.attribute) {  // 紧急揭盲
                if (ctx.currentRecord && ctx.currentRecord.urgentUnblindingApprovals && ctx.currentRecord.urgentUnblindingApprovals.find(i => i.status === 0)) {
                    setTitle(formatMessage({ id: 'subject.unblinding.approval' }));
                    setPendingApproval(ctx.currentRecord.urgentUnblindingApprovals.find(i => i.status === 0));
                    // console.log(ctx.currentRecord.urgentUnblindingApprovals.find(i => i.status === 0))
                    if (ctx.currentRecord.urgentUnblindingApprovals.find(i => i.status === 0)) {
                        setApplicationTime(moment.unix(ctx.currentRecord.urgentUnblindingApprovals.find(i => i.status === 0).applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss'))
                    }
                } else {
                    if (ctx.unblindingDetailsSign) {
                        setTitle(formatMessage({ id: 'subject.unblinding.urgent' }))
                    } else {
                        setTitle(formatMessage({ id: 'subject.unblinding.application' }));
                    }
                }
                setFooter(tab1 ? {} : tab2 ? {} : tab4 ? tab4Footer : { footer: null })
            } else if (ctx.pvUnblindingApprovalVisible && ctx.currentRecord.attribute) {      // pv揭盲
                if (ctx.currentRecord && ctx.currentRecord.pvUrgentUnblindingApprovals && ctx.currentRecord.pvUrgentUnblindingApprovals.find(i => i.status === 0)) {
                    setTitle(formatMessage({ id: 'subject.unblinding.approval' }));
                    setPendingApproval(ctx.currentRecord.pvUrgentUnblindingApprovals.find(i => i.status === 0));
                    if (ctx.currentRecord.pvUrgentUnblindingApprovals.find(i => i.status === 0)) {
                        setApplicationTime(moment.unix(ctx.currentRecord.pvUrgentUnblindingApprovals.find(i => i.status === 0).applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss'))
                    }
                } else {
                    if (ctx.unblindingDetailsSign) {
                        setTitle(formatMessage({ id: 'subject.unblinding.pv' }))
                    } else {
                        setTitle(formatMessage({ id: 'subject.unblinding.application' }));
                    }
                }
                setFooter(tab1 ? {} : tab2 ? {} : tab4 ? tab4Footer : { footer: null })
            }
            //可审批用户
            approvedUsers();
        },
        [ctx.unblindingApprovalVisible, ctx.currentRecord?.attribute, ctx.unblindingDetailsSign, ctx.pvUnblindingApprovalVisible]
    );

    return (
        <>
            <Modal
                width={'800px'}
                title={title}
                open={ctx.unblindingApprovalVisible || ctx.pvUnblindingApprovalVisible}
                onCancel={hide}
                onOk={applicationOrApprovalOrUnblinding}
                okText={formatMessage({ id: 'common.ok' })}
                destroyOnClose={true}
                centered
                maskClosable={false}

                confirmLoading={urgentUnblindingApplicationLoading || urgentUnblindingApprovalLoading}
                {...footer}
            >
                {
                    ctx.currentRecord && (tab1 || tab2 || tab3 || tab4) ?
                        <Tabs destroyInactiveTabPane={true} onChange={(key) => {
                            if (key === "3") {
                                setFooter({ footer: null })
                            } else if (key === "4") {
                                setFooter(tab4Footer)
                            } else {
                                setFooter({})
                            }
                        }}>
                            {
                                tab1
                                    ?
                                    <Tabs.TabPane
                                        tab={
                                            ctx.unblindingDetailsSign === "2" ?
                                                formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding' }) :
                                                formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding-pv' })
                                        }
                                        key="1">
                                        <Alert message={formatMessage({ id: 'subject.unblinding.application.alert' })}
                                            type="warning"
                                            showIcon />
                                        <Form form={form} labelCol={{ span: g.lang === 'en' ? 8 : 4 }}>
                                            {auth.project.info.type === 2 && (
                                                <Form.Item
                                                    label={formatMessage({
                                                        id: "projects.second",
                                                    })}
                                                >
                                                    {ctx.currentRecord.cohortName}
                                                </Form.Item>
                                            )}
                                            {auth.project.info.type === 3 && (
                                                <Form.Item
                                                    label={formatMessage({
                                                        id: "common.stage",
                                                    })}
                                                >
                                                    {ctx.currentRecord.cohortName}
                                                </Form.Item>
                                            )}
                                            <Form.Item label={ctx.currentRecord.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? ctx.currentRecord.attribute.info.subjectReplaceText : ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
                                                <span>{ctx.currentRecord.shortname}</span>
                                            </Form.Item>
                                            <Form.Item
                                                label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                                                <span>{ctx.currentRecord.randomNumber}</span>
                                            </Form.Item>
                                            <Form.Item label={formatMessage({ id: 'subject.unblinding.reason' })}
                                                name="reasonStr"
                                                rules={[{ required: true }]} >
                                                <Select onChange={(e) => {
                                                    setReasonType(e)
                                                    form.resetFields(["remark"])
                                                }
                                                }>
                                                    {ctx.currentRecord.attribute?.info.unblindingReasonConfig.map(v => <Select.Option key={v.reason} value={v.reason}>{v.reason}</Select.Option>)}
                                                </Select>
                                            </Form.Item>
                                            {
                                                ctx.currentRecord.attribute?.info.unblindingReasonConfig.find(e => e.reason === reasonType)?.allowRemark ?
                                                    <Form.Item
                                                        label={formatMessage({ id: 'subject.unblinding.reason.remark' })}
                                                        name="remark" rules={[{ required: true }]} >
                                                        <Input.TextArea allowClear className="full-width" />
                                                    </Form.Item> : null
                                            }

                                            <Form.Item
                                                {...passwordValidate}
                                                label={formatMessage({ id: 'common.password' })} name="password"
                                                rules={[{ required: true }]} >
                                                <Input.Password allowClear className="full-width" />
                                            </Form.Item>


                                            {
                                                ctx.unblindingDetailsSign === "2" ?
                                                    <Form.Item
                                                        label={formatMessage({ id: 'subject.unblinding.application.type' })}
                                                        name="approvalType" rules={[{ required: true }]}>
                                                        <Radio.Group onChange={e => setApprovalType(e.target.value)}>

                                                            <>
                                                                {
                                                                    (auth.project.info.unblinding_sms === 1 ||
                                                                        auth.project.info.unblinding_process === 1) ?
                                                                        <Radio value={1}><FormattedMessage
                                                                            id={'common.approval.confirm'} /></Radio>
                                                                        :
                                                                        null
                                                                }
                                                                {
                                                                    auth.project.info.unblinding_code === 1 ?
                                                                        <Radio value={2}><FormattedMessage
                                                                            id={'project.setting.checkbox.unblinded-code'} /></Radio>
                                                                        :
                                                                        null
                                                                }
                                                            </>
                                                        </Radio.Group>
                                                    </Form.Item>
                                                    :
                                                    ((auth.project.info.pv_unblinding_sms === 1 || auth.project.info.pv_unblinding_process === 1) ?
                                                        <span style={{ paddingLeft: 50 }}>
                                                            <FormattedMessage id={'subject.unblinding.application.type'} /> ：
                                                            <FormattedMessage id={'common.approval.confirm'} />
                                                        </span>
                                                        :
                                                        null
                                                    )
                                            }
                                            {
                                                approvalType === 2 ?
                                                    <Form.Item
                                                        {...codeValidate}
                                                        label={formatMessage({ id: 'subject.unblinding.unblinded.code.confirm' })}
                                                        name="unblindingCode" rules={[{ required: true }]}>
                                                        <Input.Password allowClear className="full-width" />
                                                    </Form.Item>
                                                    :
                                                    null
                                            }
                                        </Form>
                                    </Tabs.TabPane>
                                    : null
                            }
                            {
                                tab2
                                    ?
                                    <Tabs.TabPane
                                        tab={ctx.unblindingDetailsSign === "2" ?
                                            formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding' })
                                            :
                                            formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding-pv' })
                                        }
                                        key="2">
                                        {pendingApproval ?
                                            <>
                                                <StepsContainer>
                                                    <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                                                        <TitleIcon />
                                                        <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                                                        <span style={{ marginLeft: 8, color: "#1D2129" }}>{pendingApproval.number}</span>
                                                    </div>
                                                    <Steps progressDot current={pendingApproval.status === 0 ? 0 : 1}>
                                                        <Steps.Step
                                                            title={formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}
                                                            description={
                                                                <>
                                                                    <Row>
                                                                        {pendingApproval.applicationByEmail}
                                                                    </Row>
                                                                    <Row>
                                                                        {moment.unix(pendingApproval.applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                                    </Row>
                                                                </>
                                                            } />
                                                        <Steps.Step title={
                                                            pendingApproval.status === 0 ?
                                                                <>
                                                                    <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} />/
                                                                    <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} />
                                                                </>
                                                                :
                                                                pendingApproval.status === 1 ? <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} /> :
                                                                    pendingApproval.status === 2 ? <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} /> :
                                                                        null
                                                        }
                                                            description={
                                                                <>
                                                                    <Tooltip
                                                                        placement="top"
                                                                        title={
                                                                            <>
                                                                                {approvalUserOne}
                                                                            </>
                                                                        }
                                                                    >
                                                                        <>
                                                                            {approvalUserTwo}
                                                                        </>
                                                                    </Tooltip>
                                                                </>
                                                            } />
                                                    </Steps>
                                                </StepsContainer>

                                                <Form form={approvalForm} labelCol={{ span: g.lang === 'en' ? 6 : 4 }}>
                                                    <Form.Item label={ctx.currentRecord.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? ctx.currentRecord.attribute.info.subjectReplaceText : ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
                                                        <span>{ctx.currentRecord.shortname}</span>
                                                    </Form.Item>
                                                    <Form.Item
                                                        label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                                                        <span>{ctx.currentRecord.randomNumber}</span>
                                                    </Form.Item>
                                                    <Form.Item
                                                        label={formatMessage({ id: 'subject.unblinding.reason' })}>
                                                        <span>{pendingApproval.reasonStr}</span>
                                                        {pendingApproval.remark !== "" ?
                                                            <span>{' '}{pendingApproval.remark}</span>
                                                            : null}
                                                    </Form.Item>
                                                    <Form.Item
                                                        name={'agree'}
                                                        label={formatMessage({ id: 'common.approval.confirm' })}
                                                        rules={[{ required: true }]}
                                                    >
                                                        <Radio.Group onChange={e => setApprovalAgree(e.target.value)}>
                                                            <Radio value={1}><FormattedMessage
                                                                id={'subject.unblinding.approval.agree'} />
                                                            </Radio>
                                                            <Radio value={2}>
                                                                <FormattedMessage
                                                                    id={'subject.unblinding.approval.reject'} />
                                                            </Radio>
                                                        </Radio.Group>
                                                    </Form.Item>
                                                    {
                                                        approvalAgree === 2 ?
                                                            <Form.Item
                                                                name={'rejectReason'}
                                                                label={formatMessage({ id: 'common.reason' })}
                                                                rules={[{ required: true }]}>
                                                                <Input.TextArea allowClear className="full-width" />
                                                            </Form.Item> : null
                                                    }
                                                </Form>

                                            </>
                                            : null}
                                    </Tabs.TabPane>
                                    : null
                            }
                            {
                                tab4 && ctx.currentRecord.pvUnblindingStatus === 1 && ctx.unblindingDetailsSign === "3"
                                    ?
                                    <Tabs.TabPane
                                        tab={formatMessage({ id: 'subject.unblinding.pv' })}
                                        key="4">
                                        <UnblindingDetails data={ctx.currentRecord} />
                                    </Tabs.TabPane>
                                    : null
                            }
                            {
                                tab4 && ctx.currentRecord.urgentUnblindingStatus === 1 && ctx.unblindingDetailsSign === "2"
                                    ?
                                    <Tabs.TabPane
                                        tab={formatMessage({ id: 'subject.unblinding.urgent' }) }
                                        key="4">
                                        <UnblindingDetails data={ctx.currentRecord} />
                                    </Tabs.TabPane>
                                    : null
                            }
                            {
                                tab3
                                    ?
                                    <Tabs.TabPane
                                        tab={formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.approval-log' })}
                                        key="3">
                                        <UnBlindingApprovalLog />
                                    </Tabs.TabPane>
                                    : null
                            }

                        </Tabs>
                        : <Empty
                            image={<img src={EmptyImg} alt='empty' />}>
                        </Empty>
                }

            </Modal>

            <Modal
                destroyOnClose={true}
                footer={null}
                width={'600px'}
                onCancel={hide}
                centered
                maskClosable={false}

                title={ctx.unblindingDetailsSign === "2" ?
                    formatMessage({ id: 'project.task.urgent-unblinding.title' })
                    :
                    formatMessage({ id: 'subject.unblinding.application.result.title.pv' })
                }
                visible={applicationResultVisible}
            >
                <Result
                    status="success"
                    title={formatMessage({ id: 'subject.dispensing.apply.success' })}
                    subTitle={ctx.unblindingDetailsSign === "2" ?
                        formatMessage({ id: 'subject.unblinding.application.info' })
                        :
                        formatMessage({ id: 'subject.unblinding.application.info.pv' })
                    }
                />
                <StepsContainer>
                    <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                        <TitleIcon />
                        <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                        <span style={{ marginLeft: 8, color: "#1D2129" }}>{approvalNumber}</span>
                    </div>
                    <Steps progressDot current={0}>
                        <Steps.Step title={formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })} description={
                            <>
                                <Row>
                                    <span className={'.ant-steps-step-description'}>
                                        {auth.user.info.name}/{auth.user.info.phone}
                                    </span>
                                </Row>
                                <Row>
                                    <span className={'.ant-steps-step-description'}>
                                        {applicationTime}
                                    </span>
                                </Row>
                            </>
                        } />
                        <Steps.Step title={formatMessage({ id: 'common.toBeApproved' })} description={
                            <>
                                <Tooltip
                                    placement="top"
                                    title={
                                        <>
                                            {approvalUserOne}
                                        </>
                                    }
                                >
                                    <>
                                        {approvalUserTwo}
                                    </>
                                </Tooltip>
                            </>
                        } />
                    </Steps>
                </StepsContainer>
            </Modal>
            {
                pendingApproval ?
                    <Modal
                        destroyOnClose={true}
                        footer={null}
                        width={'600px'}
                        onCancel={hide}
                        onOk={hide}
                        okText={formatMessage({ id: 'common.ok' })}
                        title={formatMessage({ id: 'project.task.urgent-unblinding.title' })}
                        visible={approvalResultVisible}
                        centered
                        maskClosable={false}

                    >
                        <Result
                            status="success"
                            title={formatMessage({ id: 'subject.unblinding.approval.success' })}
                        />
                        <StepsContainer>
                            <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                                <TitleIcon />
                                <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                                <span style={{ marginLeft: 8, color: "#1D2129" }}>{pendingApproval.number}</span>
                            </div>
                            <Steps progressDot current={approvalAgree === 0 ? 0 : 1}>
                                <Steps.Step title={<div style={{ fontSize: 14, }}>{formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}</div>} description={
                                    <>
                                        <Row>
                                            <span className={'.ant-steps-step-description'}>
                                                {pendingApproval.applicationByEmail}
                                            </span>
                                        </Row>
                                        <Row>
                                            <span className={'.ant-steps-step-description'}>
                                                {applicationTime}
                                            </span>
                                        </Row>
                                    </>
                                } />
                                <Steps.Step title={
                                    <div style={{ fontSize: 14, }}>
                                        {
                                            approvalAgree === 1 ? <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} /> :
                                                <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} />
                                        }
                                    </div>
                                }
                                    description={
                                        <div style={{ fontSize: 12, color: "#4E5969", alignItems: "center", width: 170 }}>
                                            <div>
                                                {auth.user.info.name}/{auth.user.info.phone}
                                            </div>
                                            <div>
                                                {approvalTime}
                                            </div>
                                        </div>


                                    } />
                            </Steps>
                        </StepsContainer>
                    </Modal>
                    : null
            }

        </>
    )
};

const StepsContainer = styled.div`
    margin-bottom: 18px;
    padding: 16px 12px;
    border-radius: 2px;
    background: rgba(227,228,230,0.2);
    .ant-steps-label-vertical .ant-steps-item-content .ant-steps-item-description {
        text-align: left !important;
        width: 200px !important;
    }
    .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {
        background-color: #C8C9CC;
    }
`
