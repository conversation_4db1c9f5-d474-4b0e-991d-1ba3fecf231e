import React from "react";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import { <PERSON><PERSON>, Button, Col, Form, Input, message, Modal, Radio, Row, Select } from "antd";
import { InfoCircleTwoTone } from "@ant-design/icons";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { updateStatus } from "../../../../api/subject";
import { useSafeState } from "ahooks";
import { useGlobal } from "../../../../context/global";
import { getProjectAttribute } from "../../../../api/randomization";
import { useSubject } from "./context";
import { values } from "lodash";


export const Unblinding = (props: any) => {
    const ctx = useSubject()
    const g = useGlobal()
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [visible, setVisible] = useSafeState<any>(false);
    const [confirmVisible, setConfirmVisible] = useSafeState<any>(false);
    const [isSponsor, setIsSponsor] = useSafeState<any>(null);
    const [id, setId] = useSafeState<any>("");
    const [title, setTitle] = useSafeState<any>("");
    const [sign, setSign] = useSafeState<any>("0");
    const [fieldsData, setFieldsData] = useSafeState<any>([]);
    const [record, setRecord] = useSafeState<any>({});
    const [data, setData] = useSafeState<any>({});
    const [form] = Form.useForm();
    const [reasonType, setReasonType] = useSafeState(-1)
    const { runAsync: getProjectAttributeRun } = useFetch(getProjectAttribute, { manual: true })
    const { runAsync: updateStatusRun, loading: updateStatusLoading } = useFetch(updateStatus, { manual: true })
    const [attribute, setAttribute] = useSafeState<any>(null)

    const show = (record: any, type: any, fieldsData: any) => {
        setVisible(true);
        setId(record.id);
        setSign(type);
        setFieldsData(fieldsData)
        setRecord(record)
        if (type === "2") {
            setTitle(formatMessage({ id: 'subject.unblinding.urgent' }));
        } else if (type === "3") {
            setTitle(formatMessage({ id: 'subject.unblinding.pv' }));
        }
        getAttribute(record.projectId, record.envId, record.cohortId, record.customerId)
    };

    const getAttribute = (projectId: any, envId: any, cohortId: any, customerId: any) => {
        getProjectAttributeRun({ projectId, env: envId, cohort: cohortId, customer: customerId }).then(
            (result: any) => {
                setAttribute(result.data);
            }
        )
    }

    const hide = () => {
        setVisible(false);
        setReasonType(-1)
        setIsSponsor(null);
        form.resetFields();
    };

    // 受试者揭盲
    const save = () => {
        form.validateFields()
            .then(values => {
                setData(values)
                setConfirmVisible(true)

            })
            .catch(error => { })
    };

    // const continue_unblinding = () => {
    //     setRealVisibleModal(false)
    //     setConfirmVisible(true)
    // }
    // const register_unblinding = () => {
    //     setRealVisibleModal(false)
    //     // setConfirmVisible(true)
    //     hide()
    //     dispensing()
    //
    // }

    // 发药
    // const dispensing = () => {
    //     dispensing_pt.current.show(record,fieldsData,"1",true);
    // };

    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "195px": "140px" } }
    }
    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <>
            <Modal
                width={'800px'}
                className="drawer-width-percent"
                title={title}
                visible={visible}
                onCancel={hide}
                destroyOnClose={true}
                centered
                maskClosable={false}

                footer={
                    <Row justify="end">
                        <Col style={{ marginRight: "16px" }}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            <Button onClick={save} type="primary" loading={updateStatusLoading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                {
                    sign === "2" ?
                        <Alert message={formatMessage({ id: 'subject.unblinding.application.alert' })}
                            type="warning"
                            showIcon /> : null
                }
                <Form form={form} labelCol={{
                    span:
                        sign === "2" ?
                            g.lang === 'en' ? 6 : 5
                            :
                            g.lang === 'en' ? 5 : 3
                }}>
                    {(auth.project.info.type === 2 || auth.project.info.type === 3) ?
                        <Form.Item label={formatMessage({ id: auth.project.info.type === 2 ? 'projects.second' : 'common.stage' })}>{record.cohortName}</Form.Item>
                        : null}
                    <Form.Item
                        label={
                            attribute
                                ? ((attribute.info.subjectReplaceText === "" && attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((attribute.info.subjectReplaceText !== "" && attribute.info.subjectReplaceTextEn === "") ? attribute.info.subjectReplaceText : ((attribute.info.subjectReplaceText === "" && attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? attribute.info.subjectReplaceTextEn : attribute.info.subjectReplaceText))))
                                : formatMessage({ id: "subject.number" })
                        }
                    >
                        <span>{record.shortname}</span>
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                        <span>{record.randomNumber}</span>
                    </Form.Item>
                    {
                        sign === "2" ?
                            <>
                                <Form.Item className="label-wrap-style" label={formatMessage({ id: 'subject.unblinding.sponsor' })} name="isSponsor" rules={[{ required: true }]} >
                                    <Radio.Group onChange={(e) => { setIsSponsor(e.target.value) }} >
                                        <Radio value={true}><FormattedMessage id="common.yes" /></Radio>
                                        <Radio value={false}><FormattedMessage id="common.no" /></Radio>
                                    </Radio.Group>
                                </Form.Item>
                                {isSponsor != null && isSponsor === false ?
                                    <Form.Item label={formatMessage({ id: 'subject.unblinding.remark' })} name="remark" rules={[{ required: true }]}  >
                                        <Input.TextArea allowClear className="full-width" />
                                    </Form.Item>
                                    : null}
                            </>
                            : null
                    }
                    <Form.Item label={formatMessage({ id: 'subject.unblinding.reason' })} name="reasonStr" rules={[{ required: true }]} >
                        <Select onChange={(e) => {
                            setReasonType(e)
                            form.resetFields(["reason"])
                        }}>
                            {attribute?.info.unblindingReasonConfig.map((v: any) => <Select.Option key={v.reason} value={v.reason}>{v.reason}</Select.Option>)}
                        </Select>
                    </Form.Item>
                    {
                        attribute?.info.unblindingReasonConfig.find((e: any) => e.reason === reasonType)?.allowRemark ?
                            <Form.Item label={formatMessage({ id: 'subject.unblinding.reason.remark' })} name="reason" rules={[{ required: true }]} >
                                <Input.TextArea allowClear className="full-width" />
                            </Form.Item> : null
                    }

                    <Form.Item label={formatMessage({ id: 'common.password' })} name="password" rules={[{ required: true }]} >
                        <Input.Password allowClear className="full-width" />
                    </Form.Item>
                </Form>
            </Modal>
            {/*登记实际使用药物*/}
            {/*<Modal*/}
            {/*    visible={realVisibleModal}*/}
            {/*    // title={formatMessage({ id: 'subject.unblinding.confirmTip' })}*/}
            {/*    onCancel={() => {setRealVisibleModal(false)}}*/}
            {/*    footer={null}*/}
            {/*    destroyOnClose={true}*/}

            {/*>*/}
            {/*    <Row className="mar-all-10"><InfoCircleTwoTone  className="mar-top-5 mar-rgt-5"/>  {formatMessage({ id: 'subject.unblinding.real.title' })}</Row>*/}
            {/*    <Row className="mar-all-10">{formatMessage({ id: 'subject.unblinding.real.true' })}<Link onClick={() => {continue_unblinding()}}>{formatMessage({ id: 'subject.unblinding.real.continue' })}</Link></Row>*/}
            {/*    <Row className="mar-all-10">{formatMessage({ id: 'subject.unblinding.real.false' })}<Link onClick={() => {register_unblinding()}}>{formatMessage({ id: 'subject.unblinding.real.register' })}</Link></Row>*/}

            {/*</Modal>*/}

            <Modal
                visible={confirmVisible}
                title={formatMessage({ id: 'subject.unblinding.confirmTip' })}
                centered
                maskClosable={false}

                onOk={() => {
                    updateStatusRun({
                        "id": id,
                        "sign": sign,
                        ...data,
                    }).then(
                        (resp: any) => {
                            message.success(resp.msg)
                            props.refresh();
                            hide();
                        }
                    )
                    setConfirmVisible(false)
                }}
                onCancel={() => {
                    setConfirmVisible(false)
                }}
                okText={formatMessage({ id: 'common.ok' })}
                destroyOnClose={true}
            >
                <Row className={"full-width"}><Alert className={"full-width"} message={formatMessage({ id: 'common.confirm.tip' })} type="info" showIcon /></Row>
                <Form className="mar-top-5 mar-rgt-5" size="small" {...formItemLayout}  labelWrap>
                    {(auth.project.info.type === 2 || auth.project.info.type === 3) ?
                        <Form.Item label={formatMessage({ id: auth.project.info.type === 2 ? 'projects.second' : 'projects.third' })}>{record.cohortName}</Form.Item>
                        : null}
                    {
                        fieldsData.map((value: any, index: any) =>
                            value.name === "shortname" || value.stratification ?
                                // <Form.Item key={value.name} label={value.label}>{record[value.name]}</Form.Item>
                                <Form.Item key={value.name} label={(value.name === "shortname" && value.labelEn === "" && value.label === "") ? (<FormattedMessage id={"subject.number"} />) : ((value.name === "shortname" && value.labelEn !== "" && value.label === "") ? value.labelEn : ((value.name === "shortname" && value.labelEn === "" && value.label !== "") ? value.label : ((value.name === "shortname" && g.lang === "en") ? value.labelEn : value.label)))}>
                                    {record[value.name === "shortname" ? value.name : (value.label === "" ? value.name : value.label)]}
                                </Form.Item>
                                :
                                null

                        )
                    }
                    {
                        visible && attribute?.info.isRandomNumber ?
                            <Form.Item key="randomNumber" label={formatMessage({ id: 'projects.randomization.randomNumber' })}>{record.randomNumber}</Form.Item>
                            :
                            null
                    }
                </Form>
            </Modal>
        </>
    )
};