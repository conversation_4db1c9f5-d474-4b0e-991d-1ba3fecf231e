import React from 'react';
import {Modal} from "antd";
import {FormattedMessage} from "../../../common/multilingual/component";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {getDispensingRoomInfo, postRecordRoomInfo} from "../../../../api/dispensing";
import {useAuth} from "../../../../context/auth";
import { useGlobal } from "../../../../context/global";

export const DispensingRoom = (props:any) => {
    const [visible, setVisible] = useSafeState<any>(false);
    const [postTrue, setPostTrue] = useSafeState<any>(true);
    const [data, setData] = useSafeState<any>({});
    const auth = useAuth()
    const g = useGlobal();
    const show = (dispensingId:any) => {

        // 实时请求数据、 展示的数据响应回后端
        list(dispensingId)
        setVisible(true);
    }

    React.useImperativeHandle(props.bind, () => ({show}));

    const onCancel = () => {
        setData({})
        setVisible(false);
        setPostTrue(true)

    }
    const handleOk = () => {
        setData({})
        setVisible(false);
        setPostTrue(true)
    }

    const {runAsync: getDispensingRoomInfoRun, loading: getDispensingRoomInfoLoading} = useFetch(getDispensingRoomInfo, {manual: true})
    const {runAsync: postRecordRoomInfoRun, loading: postRecordRoomInfoLoading} = useFetch(postRecordRoomInfo, {manual: true})


    // 实时请求数据、 展示的数据响应回后端
    const list = (id:any) => {
        getDispensingRoomInfoRun({id, roleId:auth.project.permissions.role_id}).then(
            (result:any) => {
                let data :any = result.data
                postRecordRoomInfoRun({id,roleId:auth.project.permissions.role_id}, data).then(
                    () => {
                        setData(data)
                    }
                ).catch(
                    () => {
                        setPostTrue(false)
                    }
                )
            }
        ).catch(
            () => {
                setPostTrue(false)
            }
        )
    }

    return (

        <Modal title={<FormattedMessage id="subject.dispensing.subjectInfo" />} visible={visible} footer={null} onCancel={onCancel} destroyOnClose={true}>
            {
                visible?
                    postTrue?
                        <div className="mar-all-10">
                            <p>{auth.attribute?((auth.attribute.info.subjectReplaceText === "" && auth.attribute.info.subjectReplaceTextEn === "")?(<FormattedMessage id={"subject.number"} />):((auth.attribute.info.subjectReplaceText !== "" && auth.attribute.info.subjectReplaceTextEn === "")?auth.attribute.info.subjectReplaceText:((auth.attribute.info.subjectReplaceText === "" && auth.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?auth.attribute.info.subjectReplaceTextEn:<FormattedMessage id={"subject.number"} />):(g.lang === "en"?auth.attribute.info.subjectReplaceTextEn:auth.attribute.info.subjectReplaceText)))):<FormattedMessage id="subject.number" /> }：{data.subjectNo}</p>
                            <p><FormattedMessage id="projects.randomization.randomNumber" />：{data.number}</p>
                            <p><FormattedMessage id="subject.dispensing.drugNumber" />：{data.dispensingMedicines}</p>
                            <p><FormattedMessage id="drug.configure.roomNumber" />：{data.room}</p>
                            <p><FormattedMessage id="common.operation.time" />：{data.dispensingTime}</p>
                        </div>
                        :
                        <p>Please refresh the page and try again</p>
                :null
            }
        </Modal>
    )
}
