import React, {useEffect} from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {Collapse, Descriptions, Row, Spin, Table} from "antd";
import moment from "moment";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {getSubject} from "../../../../api/subject";
import {getDispensing} from "../../../../api/dispensing";
import {useSubject} from "./context";
import {nilObjectId} from "../../../../data/data";
import {CustomerTooltip} from "./dispensing";
import styled from "@emotion/styled";
import {useGlobal} from "../../../../context/global";
import { Title as CustomTitle } from "components/title";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";

export const UnblindingDetails = (props: any) => {

    const auth = useAuth();
    const intl = useTranslation();
    const ctx = useSubject();
    const g = useGlobal()

    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const projectType = auth.project.info.type;
    const {formatMessage} = intl;
    const {Panel} = Collapse;

    const [shortname, setShortname] = useSafeState<any>("");
    const [subjectData, setSubjectData] = useSafeState<any>({});
    const [dispensing, setDispensing] = useSafeState<any>([]);   //访视记录历史

    const {runAsync: getDispensingRun, loading: getDispensingLoading} = useFetch(getDispensing, {manual: true})
    const {runAsync: getSubjectRun, loading: getSubjectLoading} = useFetch(getSubject, {manual: true})


    useEffect(() => {
        if (ctx.currentRecord) {
            if(ctx.currentRecord.attribute.info.dispensing === true){
                getDispensingRun({id: ctx.currentRecord.id}).then(
                    (result: any) => {
                        let ddd = handleData(result.data?.info)
                        setDispensing(ddd)
                    }
                )
            }
            get_subject(ctx.currentRecord.id);
        }
    }, [ctx.currentRecord])


    // 查询单个受试者
    const get_subject = (id: any) => {
        setSubjectData([]);
        getSubjectRun({id}).then(
            (result: any) => {
                setSubjectData(result.data);
                setShortname(result.data.info[0].value);
            }
        )
    };


    const handleData = (rsp: any) => {
        let data: any = []
        rsp.forEach((it: any) => {
                  // 未编号相同名称合并
                let otherDispensingMedicinesCount :any[] = []
                it.otherDispensingMedicines?.map((it:any)=>{
                    let i = otherDispensingMedicinesCount.findIndex((item:any)=> it.name === item.name)
                    if ( i !== -1) {
                        otherDispensingMedicinesCount[i].count = it.count + otherDispensingMedicinesCount[i].count
                    }else{
                        otherDispensingMedicinesCount.push({name:it.name, count:it.count})
                    }
                })
                it.otherDispensingMedicinesCount = otherDispensingMedicinesCount
                if (it.realOtherDispensingMedicines?.length >0 || it.realDispensingMedicines?.length > 0){
                    it.cancelMedicinesHistory?.forEach(
                        (medicine: any) => {
                            if (medicine.realMedicineId && medicine.realMedicineId !== nilObjectId) {
                                let realMedicines: any = []
                                it.realDispensingMedicines.forEach(
                                    (real: any) => {
                                        if (medicine.medicineId === real.realMedicineId) {
                                            realMedicines.push(real)
                                        }
                                    }
                                )
                                // vis = false
                                data.push({
                                    ...it,
                                    "dispensingMedicinesSystem": [medicine],
                                    "realDispensingMedicines": realMedicines
                                })
                            }
                        }
                    )
                    it.otherMedicinesHistory?.forEach(
                        (medicine: any) => {
                            if (medicine.type ===8 || medicine.type ===9){
                                medicine.beInfo.count = medicine.count
                                let otherSystemMedicines = [medicine.beInfo]
                                data.push({
                                    ...it,
                                    "otherDispensingMedicinesSystem": otherSystemMedicines,
                                    "realOtherDispensingMedicines": [medicine]
                                })
                            }

                        }
                    )

                }else{
                    data.push({...it,})
                }
            }
        )
        return data
    }

    return (
        <>
            <Spin spinning={getDispensingLoading || getSubjectLoading}>
                <div ref={el => (ctx.componentRef = el)}>
                    <div style={{paddingBottom: 10}}>
                        <CustomTitle name={intl.formatMessage({ id: "subject.dispensing.subject" })}/>
                    </div>
                    <Descriptions layout="horizontal" column={1} bordered size="small" className="full-width">
                        {
                            auth.project.info.type !== 1
                                ?
                                <Descriptions.Item label={auth.project.info.type === 2? <FormattedMessage id={"projects.second"}/>:<FormattedMessage id={"common.stage"}/>}>{auth.env.cohorts?.find((item:any)=>item.id === subjectData.cohortId)?.name}</Descriptions.Item>
                                :
                                null
                        }
                        {
                            ctx.unblindingDetailsSign === "2"  && auth.project.info.unblinding_control !== 1 ?
                                <>
                                    <Descriptions.Item label={<FormattedMessage
                                        id={"subject.unblinding.sponsor"}/>}>{subjectData.isSponsor ?
                                        <FormattedMessage id={"common.yes"}/> :
                                        <FormattedMessage id={"common.no"}/>}</Descriptions.Item>
                                    {
                                        !subjectData.isSponsor ?
                                            <Descriptions.Item label={<FormattedMessage
                                                id={"subject.unblinding.remark"}/>}>{subjectData.remark!==""?subjectData.remark:"-"}</Descriptions.Item>
                                            : null
                                    }
                                </>
                                : null
                        }
                        <Descriptions.Item label={ctx.currentRecord?.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "")?ctx.currentRecord.attribute.info.subjectReplaceText:((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?ctx.currentRecord.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?ctx.currentRecord.attribute.info.subjectReplaceTextEn:ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
                            {shortname}
                        </Descriptions.Item>

                        {
                            ctx.currentRecord?.attribute && ctx.currentRecord.attribute.info.isRandomNumber
                                ?
                                <Descriptions.Item label={<FormattedMessage
                                    id={"projects.randomization.randomNumber"}/>}>{subjectData.randomNumber}</Descriptions.Item>
                                :
                                null
                        }
                        {/* TODO 在随机*/}
                        {
                            projectType === 3 && !inRandomIsolation(auth.project.info.number)?
                                <Descriptions.Item label={<FormattedMessage id={"group.cohort"}/>}>{subjectData.parGroupName}</Descriptions.Item>
                                :
                                <Descriptions.Item label={<FormattedMessage id={"projects.randomization.groupList"}/>}>{subjectData.parGroupName}</Descriptions.Item>
                        }

                        {
                            subjectData.subGroupName !== ""?
                                <Descriptions.Item label={<FormattedMessage
                                    id={"randomization.config.subGroup"}/>}>{subjectData.subGroupName}</Descriptions.Item>
                                :null
                        }
                        {
                            ctx.unblindingDetailsSign !== "2" ?
                                <Descriptions.Item label={<FormattedMessage
                                    id={"subject.unblinding.reason"}/>}>{subjectData.pvUnblindingReasonStr}</Descriptions.Item>
                                :
                                <Descriptions.Item label={<FormattedMessage
                                    id={"subject.unblinding.reason"}/>}>{subjectData.urgentUnblindingReasonStr}</Descriptions.Item>
                        }
                        {
                            ctx.unblindingDetailsSign === "2" && subjectData.urgentUnblindingReason !== "" ?
                                <Descriptions.Item label={<FormattedMessage
                                    id={"subject.unblinding.reason.remark"}/>}>{subjectData.urgentUnblindingReason}</Descriptions.Item> : null
                        }
                        {
                            ctx.unblindingDetailsSign === "3" && subjectData.pvUnblindingReason !== "" ?
                                <Descriptions.Item label={<FormattedMessage
                                    id={"subject.unblinding.reason.remark"}/>}>{subjectData.pvUnblindingReason}</Descriptions.Item> : null
                        }
                    </Descriptions>
                    {
                        ctx.currentRecord?.attribute.info.dispensing !== false?
                        <>

                            <div style={{paddingTop: 10}}>
                                <CustomTitle name={intl.formatMessage({ id: "subject.dispensing.subjectInfo" })}/>
                            </div>
                            <StyleTable
                                className="mar-top-10"
                                size="small"
                                dataSource={dispensing}
                                pagination={false}
                                rowKey={(record:any) => (record.id)}
                            >
                                <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={45}
                                    render={(text, record, index) => (index + 1)}/>

                                {/* TODO 在随机 */}
                                {
                                    projectType === 3 && !inRandomIsolation(auth.project.info.number)?
                                        <Table.Column
                                            title={intl.formatMessage({ id: "check.cohort" })}
                                            dataIndex="cohortName"
                                            key="cohortName"
                                        />
                                        :null
                                }
                                <Table.Column title={formatMessage({id: 'visit.cycle.visitName'})} key="visitInfo"
                                    dataIndex="visitInfo"
                                    render={(value, record: any, index) => (
                                        !record.visitSign ?
                                            value.name
                                            :
                                            record.reissue === 1 ?
                                                value.name + "(" + formatMessage({id: 'subject.dispensing.reissue'}) + ")"
                                                :
                                                value.name + "(" + formatMessage({id: 'subject.dispensing.visitSign'}) + ")"
                                    )
                                    }
                                />
                                <Table.Column title={formatMessage({id: "shipment.medicine"})}

                                    key="dispensingMedicines" dataIndex="dispensingMedicines"
                                    render={
                                        (value, record:any, index) => (
                                            <React.Fragment>
                                                {
                                                    value && value.map(
                                                        (it: any) =>
                                                            <Row key={it.number}>{it.name}({it.number})</Row>
                                                    )

                                                }
                                                {
                                                    record.otherDispensingMedicinesCount?.map(
                                                        (it: any) =>
                                                    <CustomerTooltip key={it.name} it={it} record={record.otherDispensingMedicines}/>

                                                    )
                                                }
                                                {
                                                    !(value?.length >0 || record.otherDispensingMedicinesCount?.length>0) && "-"
                                                }
                                            </React.Fragment>
                                        )
                                    }
                                />
                                <Table.Column title={formatMessage({id: 'common.operation.time'})}
                                    key="DispensingTime"
                                    dataIndex="dispensingTime"
                                    render={
                                        (value, record: any, index) => (
                                            (value === null || value === 0 || value === "") || record.status !== 2 ? '-' : (moment.unix(value).utc().add(props.data.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss') + (props.data.timeZone > 0 ? "(UTC+" : "(UTC") + props.data.timeZone + ")")
                                        )
                                    }
                                />
                                <Table.Column title={formatMessage({id: 'subject.dispensing.form.number'})}
                                    key="dispensingMedicinesSystem" dataIndex="dispensingMedicinesSystem"
                                    render={
                                        (value, record:any, index) => (
                                            <React.Fragment>
                                                {
                                                    value && value.map(
                                                        (it: any) =>
                                                            it.realMedicineId !== nilObjectId ?
                                                                <Row
                                                                    key={it.number}>{it.name}({it.number})</Row>
                                                                :
                                                                null
                                                    )

                                                }
                                                {
                                                    record.otherDispensingMedicinesSystem?.map(
                                                        (it: any) =>
                                                            <CustomerTooltip key={it.name} it={it} record={record.otherDispensingMedicinesSystem}/>
                                                    )
                                                }
                                                {
                                                    !(value?.length >0 || record.otherDispensingMedicinesSystem?.length>0) && "-"
                                                }
                                            </React.Fragment>
                                        )
                                    }
                                />
                                <Table.Column title={formatMessage({id: 'subject.dispensing.realDispensing'})}
                                    key="realDispensingMedicines" dataIndex="realDispensingMedicines"
                                    render={
                                        (value, record:any, index) => (
                                            <div>
                                                {
                                                    !record.otherDispensingMedicinesSystem && value?.map(
                                                        (it: any) =>
                                                            <Row key={it.medicineId}>
                                                                {it.name}({it.number})
                                                            </Row>
                                                    )
                                                }
                                                {
                                                    record.otherDispensingMedicinesSystem && record.realOtherDispensingMedicines?.map(
                                                        (it: any) =>
                                                            <CustomerTooltip key={it.name} it={it} record={record.realOtherDispensingMedicines}/>
                                                    )
                                                }
                                                {
                                                    !(value?.length >0 || record.realOtherDispensingMedicines?.length>0) && "-"
                                                }
                                            </div>

                                        )
                                    }
                                />
                            </StyleTable>
                        </>
                        :
                        null
                    }
                </div>
            </Spin>
        </>
    )
};

const StyleTable = styled(Table)`
  thead.ant-table-thead > tr > th {
    line-height: 20px !important;
  }
`