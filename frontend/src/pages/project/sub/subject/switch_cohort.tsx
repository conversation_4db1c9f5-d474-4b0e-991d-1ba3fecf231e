import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Button, Col, Form, Modal, Row, Select, message} from "antd";
import {useFetch} from "../../../../hooks/request";
import {useSafeState} from "ahooks";
import {switchCohort} from "../../../../api/subject";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import {CustomConfirmModal} from "../../../../components/modal";

export const SwitchCohort = (props:any) => {
    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;
    const [visible, setVisible] = useSafeState<any>(false);
    const [id, setId] = useSafeState<any>("");
    const [record, setRecord] = useSafeState<any>({});
    const cohorts = auth.env?.cohorts? auth.env.cohorts.filter((item: any) => item.type === 0) : [];
    const [form] = Form.useForm();

    const show = (record:any) => {
        setVisible(true);
        setId(record.id);
        setRecord(record);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
    };

    const {runAsync:switchCohortRun, loading:switchCohortLoading} = useFetch(switchCohort, {manual: true})


    // 受试者转运
    const save = () => {
        form.validateFields().then(values => {
            CustomConfirmModal({
                title: formatMessage({id:"subject.confirm.switch.cohort"}),
                content: formatMessage({id:"subject.confirm.switch.cohort.content"}),
                cancelText: formatMessage({id: 'common.cancel'}),
                okText: formatMessage({id: 'common.ok'}),
                onOk: () => {
                    switchCohortRun({
                        "id": id,
                        "shortname":record.shortname,
                        ...values,
                    }).then(
                        (result: any) => {
                            message.success(result.msg).then();
                            props.refresh();
                            hide();
                        }
                    )
                }
            })
        }).catch(() => { })
    };

    React.useImperativeHandle(props.bind, () => ({show}));
    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "150px": "150px"} },
    };

    return (
        <React.Fragment>
            <Modal
                className="custom-little-modal"
                title={<FormattedMessage id="subject.switch.cohort" />}
                open={visible}
                centered
                maskClosable={false}
                onCancel={hide}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            <Button onClick={ save } type="primary" loading={switchCohortLoading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                <Form form={form} {...formItemLayout}>
                    <Form.Item
                        label={
                            record.attribute?
                            ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                            :formatMessage({id:"subject.number"})
                        }
                        className="mar-ver-5"
                    >
                        {record.shortname}
                    </Form.Item>  
                    <Form.Item 
                        label={formatMessage({id: 'subject.current.cohort'})}  
                        className="mar-ver-5"
                    >
                        {record.cohortName}
                    </Form.Item>          
                    <Form.Item 
                        label={formatMessage({id: 'subject.new.cohort'})} 
                        name="newCohort" 
                        rules={[{required: true}]} 
                        className="mar-ver-5"
                    >
                        <Select style={{ width: 220 }} placeholder={formatMessage({ id: 'placeholder.select.common' })}>
                            {
                                cohorts?.filter((item:any)=>item.type === 0).map((item: any) => {
                                    if (item.id !== record.cohortId) {
                                        return    <Select.Option value={item.id}>{item.name}</Select.Option>
                                    }
                                })
                            }
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};