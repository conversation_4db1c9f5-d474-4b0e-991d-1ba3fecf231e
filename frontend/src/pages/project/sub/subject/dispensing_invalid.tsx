import React from 'react';
import {Col, DatePicker, Form, Input, InputNumber, message, Modal, notification, Radio, Row, Select, Space} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {CustomConfirmModal} from "../../../../components/modal";
import {useGlobal} from "../../../../context/global";
import {useAuth} from "../../../../context/auth";
import TextArea from "antd/es/input/TextArea";
import {
    invalidDispensing,
} from "../../../../api/dispensing";

export const DispensingInvalid = (props:any) => {
    const auth = useAuth();
    const {list,subjectRefresh } = props
    const intl = useTranslation();
    const {formatMessage} = intl;
    const [invalidFrom] = Form.useForm();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const roleId = auth.project.permissions.role_id;
    const [id, setId] = useSafeState<any>(null);

    const {
        runAsync: invalidDispensingRun,
        loading: invalidDispensingLoading,
    } = useFetch(invalidDispensing, { manual: true });


    React.useImperativeHandle(props.bind, () => ({show}));
    const [invalidDispensingVisible, setInvalidDispensingVisible] = useSafeState<any>(false);

    const show = (id:any) => {
        setId(id);
        setInvalidDispensingVisible(true)
    }
    const save = (response:any) => {
        invalidFrom.validateFields().then(
            (value:any) => {
                CustomConfirmModal({
                    title: formatMessage({ id: "subject.dispensing.invalidTip" }),
                    content: formatMessage({ id: "subject.dispensing.confirmInvalid" }),
                    okText: formatMessage({ id: "common.ok" }),
                    onOk: () =>
                        invalidDispensingRun({ id: id, roleId: roleId, remark: value.remark }).then((response: any) => {
                            message.success(response.msg);
                            invalidFrom.resetFields();
                            setInvalidDispensingVisible(false);
                            props.refresh();
                            subjectRefresh()
                        }),
                });
            }
        )
    }

    const g = useGlobal();

    const layout = {
        labelCol: { style: {   width: g.lang === "en"? "60px": "50px" } }
    };
    return (

        <Modal
            centered
            className="custom-small-modal"
            title={formatMessage({id: 'subject.dispensing.invalid'})}
            visible={invalidDispensingVisible || invalidDispensingLoading}
            onOk={save}
            okText={formatMessage({ id: 'common.ok' })}
            cancelText={formatMessage({ id: 'common.cancel' })}
            onCancel={() => {
                setInvalidDispensingVisible(false)
                invalidFrom.resetFields()
            }}
            confirmLoading={invalidDispensingLoading}
            destroyOnClose={true}
            closable={false}
            maskClosable={false}
        >
            <Form form={invalidFrom} {...layout}>
                <Form.Item style={{marginBottom: "0px"}} name="remark"
                           label={formatMessage({id: 'common.remark'})}>
                    <TextArea placeholder={formatMessage({id: 'common.required.prefix'})} allowClear
                              className="full-width" autoSize={{minRows: 2, maxRows: 6}} maxLength={500}/>
                </Form.Item>
            </Form>
        </Modal>
    )
}
