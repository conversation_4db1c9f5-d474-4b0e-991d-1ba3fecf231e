import React from "react";
import { VisitCycleProvider } from "./visit_context";
import { PageContextProvider } from "../../../../context/page";
import { isBindRole } from "../../../../api/roles";
import { useFetch } from "../../../../hooks/request";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { permissions } from "../../../../tools/permission";
import { ProjectCalendar } from "./calendar";
import { ProjectSiteVisitStatistics } from "./site_visit_statistics";
import { ProjectSiteVisitMatter } from "./site_visit_matter";
import {AuthWrap} from "../../../common/auth-wrap";

export const VisitCycle = () => {
  const auth = useAuth();
  const customerId = auth.customerId;
  const projectId = auth.project.id;
  const envId = auth.env ? auth.env.id : null;
  const roleId = auth.project.permissions.role_id;

  const [isBlind, setIsBlind] = useSafeState(true);

  const { runAsync: run_isBlindRole } = useFetch(isBindRole, { manual: true });
  const isBindRoleBool = () => {
    run_isBlindRole({
      customerId: customerId,
      envId: envId,
      projectId: projectId,
      roleId: roleId,
    }).then((result: any) => {
      const data = result.data;
      setIsBlind(data);
    });
  };

  React.useEffect(() => {
    isBindRoleBool();
  }, [roleId]);

  return (
    <VisitCycleProvider>
      <div>
        <AuthWrap show={permissions(auth.project.permissions, "operation.project.subject.visit.cycle.view")}>
          <ProjectSiteVisitStatistics />
        </AuthWrap>
      </div>
      <div style={{ display: "flex", borderTop: "1px solid #E0E1E2" }}>
        <div style={{ flex: 1, padding: "8px 16px 0 16px" }}>
          <AuthWrap show={permissions(auth.project.permissions, "operation.project.subject.visit.cycle.view")}>
              <div className="mar-top-8">
                <ProjectCalendar />
              </div>
          </AuthWrap>
        </div>
        <div style={{ width: "280px" }}>
          <AuthWrap show={permissions(auth.project.permissions, "operation.project.subject.visit.cycle.view")}>
              <PageContextProvider>
                <ProjectSiteVisitMatter />
              </PageContextProvider>
          </AuthWrap>
        </div>
      </div>
    </VisitCycleProvider>
    // <VisitCycleProvider>
    //     <Row>
    //         <Col span={24}>
    //             {
    //                 permissions(auth.project.permissions, "operation.project.subject.visit.cycle.view")
    //                 && <ProjectSiteVisitStatistics/>
    //             }
    //         </Col>
    //     </Row>
    //     <Row>
    //         <Col span={16}>
    //             {
    //                 (auth.attribute && permissions(auth.project.permissions, "operation.project.subject.visit.cycle.view")) ?
    //                 <div className="mar-top-8">
    //                     <ProjectCalendar/>
    //                 </div>:null
    //             }
    //         </Col>
    //         <Col span={8}>
    //             {
    //                 (auth.attribute && permissions(auth.project.permissions, "operation.project.subject.visit.cycle.view")) ?
    //                 <div className="mar-top-8">
    //                     <ProjectSiteVisitMatter/>
    //                 </div>:null
    //             }
    //         </Col>
    //     </Row>
    // </VisitCycleProvider>
  );
};
