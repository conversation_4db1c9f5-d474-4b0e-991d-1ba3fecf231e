import {useSafeState} from "ahooks";
import {message, Tag} from "antd";
import React from "react";
import {useIntl} from "react-intl";
import copy from 'copy-to-clipboard'

export const UnblindingCodeTag = (props) => {
    const [tagStyle, setTagStyle] = useSafeState({backgroundColor: "#F4F4F5", color: "#1D2129"})
    const [content,setContent] = useSafeState(props.code)
    const intl = useIntl();
    const {formatMessage} = intl;
    return <Tag
        onMouseOver={()=>{
            setTagStyle({backgroundColor:"rgba(22, 93, 255, 0.1)",color:"#165DFF"})
            setContent(formatMessage({id:'common.copy'}))
        }}
        onMouseOut={()=>{
            setTagStyle({backgroundColor:"#F4F4F5",color:"#1D2129"})
            setContent(props.code)
        }}
        onClick={()=>{
            copy(props.code)
            message.success(formatMessage({id:'common.copy.ok'}))
        }}
        style={{
            ...tagStyle,
            webkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
            border:"none",
            height: "28px",
            width: "98px",
            left: "0px",
            top: "0px",
            borderRadius: "2px",
            textAlign:"center",
            lineHeight: "30px",
            fontSize:"14px"
        }}>
        {content}
    </Tag>
}