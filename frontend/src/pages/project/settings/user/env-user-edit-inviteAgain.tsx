import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {
    Button,
    Checkbox,
    Col,
    Form,
    Input,
    message as antd_message,
    Modal,
    Popover,
    Row,
    Spin,
    Tag,
    Tooltip,
    Select,
    Radio,
    Space,
} from "antd";
import {usePage} from "context/page";
import {useFetch} from "../../../../hooks/request";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {permissions} from "../../../../tools/permission";
import {useGlobal} from "../../../../context/global";
import {
    reauthorizationProjectEnvironmentUser,
    resendInviteEmail,
} from "../../../../api/projects";
import {useProjectUser} from "./context";


export const EnvUserInviteAgain = (props:any) => {

    const ctx = useProjectUser();
    const page = usePage();
    const auth = useAuth();
    const projectStatus = auth.project.status ? auth.project.status : 0

    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [unbind,setUnbind] = useSafeState(true);
    const [form] = Form.useForm();
    const [customerId, setCustomerId] = useSafeState(null);
    const [projectId, setProjectId] = useSafeState(null);
    const [envId, setEnvId] = useSafeState(null);
    const [userId, setUserId] = useSafeState(null);
    const [key, setKey] = useSafeState(null);

    const {runAsync: reauthorizationProjectEnvironmentUserRun,loading: reauthorizationProjectEnvironmentUserLoading} = useFetch(reauthorizationProjectEnvironmentUser, {manual: true})
    const {runAsync: resendInviteEmailRun, loading: resendInviteEmailLoading} = useFetch(resendInviteEmail, {manual: true})


    const show = (data:any) => {
        setCustomerId(data.customerId);
        setProjectId(data.projectId);
        setEnvId(data.envId);
        setUserId(data.userId);
        if(data.unbind !== null && data.unbind !== undefined){
            setUnbind(data.unbind);
        }
        setKey(data.key);
        setVisible(true);
    };

    const hide = () => {
        setCustomerId(null);
        setProjectId(null);
        setEnvId(null);
        setUserId(null);
        setKey(null);
        setVisible(false);
        setUnbind(true);
        form.resetFields();
    };

    // 提交数据
    const save = () => {
        form.validateFields()
            .then(values => {
                const emailLanguage = form.getFieldValue("emailLanguage");
                if (key) {
                    if (key === 1) {
                        reauthorizationProjectEnvironmentUserRun(
                            {customerId: customerId, projectId: projectId, envId: envId, userId: userId, emailLanguage: emailLanguage}).then(
                            (resp: any) => {
                                antd_message.success(formatMessage({id: 'common.success'}))
                                // antd_message.success(resp.msg);
                                // let curPage = Math.ceil((page.total - 1) / page.pageSize)
                                // curPage = curPage > 1 ? curPage : 1
                                // page.setCurrentPage(curPage)
                                ctx.setDoSearch(ctx.doSearch + 1)
                                hide()
                            })
                    } else if (key === 2) {
                        resendInviteEmailRun({customerId: customerId, userId: userId,envId:envId, emailLanguage: emailLanguage}).then(
                            (result: any) => {
                                antd_message.success(formatMessage({id: 'common.success'}))
                                // antd_message.success(result.msg)
                                hide()
                            }
                        )
                    }
                }
            })
            .catch(error => {
            })
    }
    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "120px": "88px"} },
    };

    return (
        <>
            <Modal
                className={"custom-small-modal"}
                title={key === 1?<FormattedMessage id="user.settings.reauthorization" />:<FormattedMessage id="user.settings.invite-again" />}
                open={visible}
                onCancel={hide}
                centered
                maskClosable={false}
                destroyOnClose={true}
                okText={formatMessage({id: 'common.ok'})}
                okButtonProps={{loading: reauthorizationProjectEnvironmentUserLoading || resendInviteEmailLoading}}
                onOk={save}
            >
                <Form form={form} {...formItemLayout}>
                    {
                        <Form.Item
                            label={formatMessage({ id: 'common.email.language' })}
                            name="emailLanguage"
                            style={{marginBottom:24}}
                            rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common.email.language' })}]}
                            // initialValue={"zh"}
                        >
                            {/* <Select 
                                className="full-width" 
                                options={language}
                            >
                            </Select> */}
                            <Radio.Group 
                                className="full-width"
                                disabled={props.disabled}
                            >
                                <Space >
                                    <Col
                                        style={{ marginRight: 24 }}
                                    >
                                        <Radio value={"zh"}>
                                            {
                                                intl.formatMessage({id: "common.email.language.zh"})
                                            }
                                        </Radio>
                                    </Col>
                                    <Col
                                        style={{ marginRight: 24 }}
                                    >
                                        <Radio value={"en"}>
                                            {
                                                intl.formatMessage({id: "common.email.language.en"})
                                            }
                                        </Radio>
                                    </Col>
                                </Space>

                            </Radio.Group>
                        </Form.Item>
                    }
                </Form>
            </Modal>
        </>
    )
};
