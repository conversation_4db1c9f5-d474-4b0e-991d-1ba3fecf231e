import {useSafeState, useUpdateEffect} from "ahooks";
import { useProjectUser } from "../context";
import {useEffect} from "react";

interface useCacheTableProp {
    dataSource: any[];
}
export const useCacheTable = (props: useCacheTableProp) => {
    const pageCtx = useProjectUser();
    // 当前table渲染的数据
    const [tableData, setTableData] = useSafeState<any[]>([]);
    const [dateLength, setDataLength] = useSafeState<number>(0);
    useEffect(() => {
        pageCtx.setTotal(props.dataSource?.length);
        if (props.dataSource?.length !== dateLength) {
            setDataLength(props.dataSource?.length);
            pageCtx.setCurrentPage(1);
            pageCtx.setPageSize(10);
            setTableData(props.dataSource.slice(0, 10));
        }
    }, [props.dataSource])

    useUpdateEffect(() => {
        // 页码更新，从缓存中更新当前的表格
        const start = (pageCtx.currentPage - 1) * pageCtx.pageSize;
        const end = pageCtx.currentPage * pageCtx.pageSize;
        setTableData(props.dataSource.slice(start, end));
    }, [props.dataSource, pageCtx]);
    return tableData;
}