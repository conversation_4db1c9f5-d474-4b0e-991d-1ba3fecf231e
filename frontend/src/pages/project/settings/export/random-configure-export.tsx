import React, {useEffect} from 'react';
import {FormattedMessage, useIntl} from "react-intl";
import {Card, Col, Collapse, Descriptions, Divider, List, Row, Spin, Table, Tabs, Tooltip,} from "antd";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {downloadReport, getProjectAttribute, getRandomization} from "../../../../api/randomization";
import {getConfigureList} from "../../../../api/drug";
import {getVisitList} from "../../../../api/visit";
import {QuestionCircleFilled} from "@ant-design/icons";
import {Title as CustomTitle, Title} from "../../../../components/title";
import styled from "@emotion/styled";
import {useGlobal} from "../../../../context/global";

export const RandomConfigureExport = (props:any) => {
    const g = useGlobal();
    const {Panel} = Collapse;

    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;
    const [attribute, setAttribute] = useSafeState<any>({});
    const [randomization, setRandomization] = useSafeState<any>({});
    const [group, setGroup] = useSafeState<any>([]);
    const [medicine, setMedicine] = useSafeState<any>([]);
    const [visitCycles, setVisitCycles] =  useSafeState<any>([]);
    const cohortId = props.cohort ? props.cohort.id : null;


    const {runAsync: getProjectAttributeRun, loading:getProjectAttributeLoading} = useFetch(getProjectAttribute, {manual: true})
    const {runAsync: getRandomizationRun, loading:getRandomizationLoading} = useFetch(getRandomization, {manual: true})
    const {runAsync:getConfigureListRun, loading:getConfigureListLoading} = useFetch(getConfigureList, {manual: true})
    const {runAsync: getVisitListRun, loading:getVisitListLoading} = useFetch(getVisitList, {manual: true})

    useEffect(() => {list()},[g.lang])

    const list = () => {
        getProjectAttributeRun({projectId:auth.project.id, env: auth.env.id, cohort: cohortId, customer: auth.customerId}).then(
            (result:any) => {
                setAttribute(result.data.info)
            }
        )

        getRandomizationRun({projectId:auth.project.id, env: auth.env.id, cohort: cohortId, customer: auth.customerId,roleId:auth.project.permissions.role_id}).then(
            (result:any) => {
                let factors = result.data.factors?.filter((i:any)=>i.status !== 2);
                result.data.factors = factors
                setRandomization(result.data)
                if (result.data.groups != null && result.data.groups.length > 0) {
                    let groups :any = [];
                    result.data.groups.forEach((value:any)=>{
                        if (value.subGroup != null && value.subGroup.length > 0){
                            for (let i = 0; i < value.subGroup.length; i++) {
                                groups.push({
                                    id:value.id,
                                    name: value.name + " " + value.subGroup[i].name,
                                    segmentLength: value.subGroup[i].segmentLength,
                                })
                            }
                        }else {
                            groups.push({
                                id:value.id,
                                name: value.name,
                                segmentLength: value.segmentLength,
                            })
                        }
                    })
                    setGroup(groups)
                }

            }
        )
        //获取访视周期
        getVisitListRun({customerId:auth.customerId, projectId:auth.project.id,envId:auth.env.id,cohortId: cohortId}).then(
            (result:any) => {
                let ret :any = result.data
                let vs: any = []
                if(ret.infos!=null){
                    const options = ret.infos.map((it:any) => ({
                        label: it.name,
                        value: it.id
                    }));
                    vs = options;
                }
                if(ret.setInfo!=null && ret.setInfo.isOpen){
                    const options = {
                        label: g.lang === "zh" ?ret.setInfo.nameZh:ret.setInfo.nameEn,
                        value: ret.setInfo.id
                    };
                    vs.push(options);
                }
                setVisitCycles(vs);
                //研究产品配置列表查询
                getConfigureListRun({customerId:auth.customerId,envId:auth.env.id,cohortId: cohortId,roleId:auth.project.permissions.role_id}).then(
                    (result:any) => {
                        setMedicine(result.data.configures);
                    }
                )
            }
        )

    }
    function renderVisitName(value:any) {
        return visitCycles.find((it:any) => (it.value === value))? visitCycles.find((it:any) => (it.value === value)).label : null;
    }

    const itemOption = (item:any) => {
        if (item.options === null){
            item.options = []
        }
        return item.options.map((value:any) => (
                <Row key={value.value}>{value.label}</Row>
            )
        )
    }

    return (
        <CustomerDiv>

            <div style={{ marginBottom: 16 }}>
                <CustomTitle
                    name={intl.formatMessage({
                        id: "menu.projects.project.build.attributes",
                    })}
                />
            </div>
            <Card title={formatMessage({id:'projects.attributes.systemRules'})} headStyle={{background:"#F1F3F6", height:"46px"}}>
                <Descriptions
                    layout="vertical"
                    bordered
                    column={4}
                    size="small"
                    // labelStyle={radioStyle}
                >
                    <Descriptions.Item  label={formatMessage({id: 'projects.attributes.random'})}>
                        {attribute.random? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}
                    </Descriptions.Item>
                    <Descriptions.Item   label={formatMessage({id: 'projects.attributes.dispensing'})}>
                        {attribute.dispensing? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}
                    </Descriptions.Item>
                    <Descriptions.Item  label={formatMessage({id: 'projects.attributes.blind'})}>
                        {attribute.blind? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}
                    </Descriptions.Item>
                    <Descriptions.Item  label={formatMessage({id: 'projects.attributes.instituteLayer'})}>
                        {attribute.instituteLayered? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}
                    </Descriptions.Item>
                </Descriptions>
            </Card>

            <Card style={{marginTop:12, marginBottom:12}} title={formatMessage({id:'projects.attributes.subject.numberRules'})} headStyle={{background:"#F1F3F6", height:"46px"}}>
                <Descriptions
                    layout="vertical"
                    bordered
                    column={4}
                    size="small"
                    // labelStyle={radioStyle}
                >
                    <Descriptions.Item  label={formatMessage({id: 'projects.attributes.prefix'})}>
                        {attribute.prefix? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}
                    </Descriptions.Item>
                    {
                        attribute.prefix && attribute.prefixExpression?
                            <Descriptions.Item  label={formatMessage({id: 'projects.attributes.prefix'})}>
                                {attribute.prefixExpression}
                            </Descriptions.Item>
                            :
                            null
                    }
                    <Descriptions.Item  label={formatMessage({id: 'projects.attributes.subject.replace.text'})}>
                        {attribute.subjectReplaceText}
                    </Descriptions.Item>
                    <Descriptions.Item  label={formatMessage({id: 'form.control.type.maximum'})}>
                        {attribute.digit}
                    </Descriptions.Item>
                    <Descriptions.Item  label={formatMessage({id:'form.control.type.exact'})}>
                        {
                            attribute.accuracy === 1 ?
                                formatMessage({id:'form.control.type.le'})
                                :
                                formatMessage({id:'form.control.type.eq'})
                        }
                    </Descriptions.Item>
                </Descriptions>
            </Card>
            <Card style={{marginTop:12, marginBottom:12}} title={formatMessage({id:'projects.attributes.other.numberRules'})} headStyle={{background:"#F1F3F6", height:"46px"}}>
                <Descriptions
                    layout="vertical"
                    bordered
                    column={4}
                    size="small"
                    // labelStyle={radioStyle}
                >

                    <Descriptions.Item  label={formatMessage({id: 'projects.attributes.isFreeze'})}>
                        {attribute.isFreeze? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}
                    </Descriptions.Item>
                    <Descriptions.Item  label={<FormattedMessage id="projects.attributes.subject.isRandom"/>}>
                        {attribute.isRandom? formatMessage({ id: 'common.yes' }) : formatMessage({ id: 'common.no' })}
                    </Descriptions.Item>
                </Descriptions>
            </Card>



            <div style={{ marginBottom: 16 }}>
                <CustomTitle
                    name={intl.formatMessage({
                        id: "projects.randomization.configure",
                    })}
                />
            </div>
            <Card style={{marginTop:12, marginBottom:12}} title={formatMessage({id:'projects.randomization.design'})} headStyle={{background:"#F1F3F6", height:"46px"}}>
                <Descriptions
                    layout="vertical"
                    bordered
                    column={4}
                    size="small"
                    // labelStyle={radioStyle}
                >
                    <Descriptions.Item  label={formatMessage({id: 'randomization.config.type'})}>
                        {
                            randomization.type ===1 ?
                                formatMessage({ id: 'projects.randomization.blockRandom' })
                                :
                                randomization.type ===2 ?
                                    formatMessage({ id: 'projects.randomization.minimize' })
                                    :
                                    ""
                        }
                    </Descriptions.Item>
                    <Descriptions.Item  label={formatMessage({id: 'projects.randomization.group'})}>
                        <List
                            dataSource={group}
                            renderItem={(item:any) => (
                                <List.Item>
                                    {item.name}
                                </List.Item>
                            )}
                        />
                    </Descriptions.Item>
                    <Descriptions.Item  label={formatMessage({id: 'randomization.config.factor'})}>
                        <List
                            itemLayout="horizontal"
                            dataSource={randomization.factors === null? []: randomization.factors}
                            renderItem={(data:any,index) => (
                                <List.Item>
                                    <List.Item.Meta
                                        key={index}
                                        title={data.label}
                                        description={
                                            itemOption(data)
                                        }
                                    />
                                </List.Item>
                            )}
                        />
                    </Descriptions.Item>
                </Descriptions>
            </Card>


            <div style={{ marginBottom: 16 }}>
                <CustomTitle
                    name={intl.formatMessage({
                        id: "menu.projects.project.build.careDesign",
                    })}
                />
            </div>
            <Card style={{marginTop:12, marginBottom:12}} title={formatMessage({id:'menu.projects.project.build.drug'})} headStyle={{background:"#F1F3F6", height:"46px"}}>
                <Table
                    size="small"
                    dataSource={medicine}
                    pagination={false}
                    rowKey={(record) => (record.id)}
                >
                    <Table.Column title={<FormattedMessage id="common.serial"/>} dataIndex="#" key="#" width={55} render={(text, record, index) => (index + 1)}/>
                    <Table.Column title={<FormattedMessage id="drug.configure.group"/>} key="group" dataIndex="group" align="left" ellipsis/>
                    <Table.Column
                        // width="45%"
                        title={
                            <span>
                                                        <span>
                                                            {formatMessage({id: 'drug.configure.drugName'})}
                                                        </span>
                                                        <span style={{marginLeft:"4px"}}>
                                                            <Tooltip title={
                                                                <span>{formatMessage({id:"configuration.export.treatment.design.shipment.other.drug"})}</span>
                                                            }
                                                                     overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                                            >
                                                                <span>
                                                                    <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                                                </span>
                                                            </Tooltip>
                                                        </span>
                                                </span>
                        }
                        key="values"
                        dataIndex="values"
                        align="left"
                        render={
                            (value, record:any, index) =>(
                                value.map((it:any) => {
                                    return <span>
                                                            {
                                                                it.isOther?
                                                                    <span style={{color:"#f59a23"}}>{"* "}</span>:
                                                                    null
                                                            }

                                        <span>
                                                                {it.drugName + "/" +(record.openSetting !== 3 ?
                                                                        it.isCount?it.customDispensingNumber:it.dispensingNumber
                                                                        :
                                                                        ""
                                                                ) + "/" + it.drugSpec + " "}
                                                            </span>
                                                        </span>
                                    // return it.drugName + "/" + it.dispensingNumber + "/" + it.drugSpec +",      "
                                })
                            )
                        }
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.configure.visitName"/>}
                        key="visitCycles"
                        dataIndex="visitCycles"
                        align="left"
                        render={
                            (value, record, index) =>(
                                value === null ? "" : 
                                (
                                    value.map((it:any) => {
                                        return renderVisitName(it);
                                    }).join(',')
                                )
                            )
                        }
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.configure.drugLabel"/>}
                        key="label"
                        dataIndex="label"
                        align="left"
                        ellipsis
                        render={
                            (value, record :any , index) => {
                                let labels = []
                                record?.values.forEach((item:any) => {
                                    if (item.label && (item.label !== undefined || item.label !== "")){
                                        labels.push(item.label)
                                    }
                                })
                                if (value !== "") {
                                    labels.push(value)
                                }
                                return labels.join(",")
                            }
                        }
                    />
                </Table>
            </Card>
        </CustomerDiv>
    )
}


const CustomerDiv = styled.div`
  .ant-card-head-title {
    padding-top: 10px;
  }

`