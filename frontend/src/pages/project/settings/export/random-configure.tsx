import React, {useEffect} from 'react';
import {FormattedMessage, useIntl} from "react-intl";
import {<PERSON><PERSON>, Card, Col, Collapse, Descriptions, List, Row, Spin, Table, Tabs} from "antd";
import {permissions} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {downloadReport, getProjectAttribute, getRandomization} from "../../../../api/randomization";
import {getConfigureList} from "../../../../api/drug";
import {getVisitList} from "../../../../api/visit";
import {RandomConfigureExport} from "./random-configure-export";

export const RandomConfigure = (props:any) => {
    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;
    const cohorts = auth.env?.cohorts;
    const projectType = auth.project.info.type;
    const {runAsync: downloadReportRun, loading:downloadReportLoading} = useFetch(downloadReport, {manual: true})
    // const download_report = () => {
    //     downloadReportRun({
    //         env_id: auth.env.id,
    //         cohort_id: auth.cohort? auth.cohort.id:null,
    //         customer_id: auth.customerId,
    //         project_id: auth.project.id,
    //         roleId: auth.project.permissions.role_id
    //     }).then()
    // }
    return (
        <React.Fragment>

            {projectType === 1 ? (
                <RandomConfigureExport />
                    ) : (
                        <Tabs
                            destroyInactiveTabPane={true}
                            size="small"
                            defaultActiveKey="1"
                            tabPosition="top"
                            items={cohorts.map(
                                (cohort: any, cohort_index: number) => {
                                    const id = cohort.id;
                                    return {
                                        label: cohort.type === 1?cohort.name + " - " + cohort.re_random_name:cohort.name,
                                        key: id,
                                        children: (
                                            <RandomConfigureExport cohort={cohort} />
                                        ),
                                    };
                                }
                            )}
                        ></Tabs>
                    )}


        </React.Fragment>
    )
}