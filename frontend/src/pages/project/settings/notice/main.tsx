import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, message, <PERSON>, <PERSON>, Tooltip} from "antd";
import {RoleList} from "./role_list";
import {useEffect, useState} from "react";
import {useNotice} from "./context";
import {list} from "../../../../api/projects_roles";
import {getNoticeConfig, setNoticeConfig, getNoticeConfigVerify} from "../../../../api/notice";
import {useFetch} from "hooks/request";

import {Result} from "../../../../types/result";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {permissions} from "../../../../tools/permission";
import styled from "@emotion/styled";
import {useGlobal} from "../../../../context/global";
import { EditPrompt } from "./edit_prompt";
import React from "react";
import {AuthButton} from "../../../common/auth-wrap";
import {FormattedMessage} from "../../../common/multilingual/component";

export const Main = () => {
    const g = useGlobal()
    const ctx = useNotice();
    const auth = useAuth();
    const [formData, setFormData] = useSafeState<any>({});
    const [isEdit, setIsEdit] = useState(false);
    const [config, setConfig] = useSafeState<any[]>([]);
    const { runAsync, loading, refresh } = useFetch(getNoticeConfig, {
        manual: true,
    });
    const { runAsync: listRun, loading: listLoading } = useFetch(list, {
        manual: true,
    });

    const edit_prompt: any = React.useRef();
    
    const getFieldName = (key: string) => {
        switch (key) {
            case "notice.basic.settings":
                return "basicSettings";
            case "notice.subject.add":
                return "rolesAdd";
            case "notice.subject.random":
                return "rolesRandom";
            case "notice.subject.signOut":
                return "rolesSubSignOut";
            case "notice.subject.replace":
                return "rolesSubReplace";
            case "notice.subject.screen":
                return "rolesSubScreen";
            case "notice.subject.update":
                return "rolesSubUpdate";
            case "notice.subject.dispensing":
                return "rolesDispensing";
            case "notice.subject.alarm":
                return "rolesSubjectAlarm";
            case "notice.subject.unblinding":
                return "rolesUnblinding";
            case "notice.medicine.isolation":
                return "rolesIsolation";
            case "notice.medicine.order":
                return "rolesOrder";
            case "notice.medicine.reminder":
                return "rolesReminder";
            case "notice.medicine.alarm":
                return "rolesAlarm";
            case "notice.storehouse.alarm":
                return "rolesStorehouseAlarm";
            case "notice.order.timeout":
                return "rolesTimeout";
            case "notice.subject.alert.threshold":
                return "rolesSubjectAlertThreshold";
            case "notice.subject.medicine.alarm":
                return "rolesSubjectMedicineAlarm";
            case "notice.subject.medicine.capping":
                return "rolesSubjectMedicineCapping";
            case "notice.medicine.un_provide_date":
                return "rolesMedicineUnProvideDate";
            default:
                return "";
        }
    };
    const researchAttribute = auth.project.info.research_attribute
        ? auth.project.info.research_attribute
        : 0;

    const listNotice = () => {
        runAsync({
            customerId: auth.customerId,
            projectId: auth.project.id,
            envId: auth.env.id,
        }).then((result: any) => {
            if (result.code === 0) {
                let newValues: any = {};
                result.data.forEach((noticeConfig: any) => {
                    let key = noticeConfig.key;
                    newValues[getFieldName(key)] = noticeConfig.roles;
                    newValues[getFieldName(key) + "Content"] =
                        noticeConfig.fieldsConfig;
                    if (noticeConfig.excludeRecipientList !== undefined && noticeConfig.excludeRecipientList !== null && noticeConfig.excludeRecipientList.length > 0 ) {
                        newValues[getFieldName(key) + "excludeRecipientList"] =
                        noticeConfig.excludeRecipientList;
                    } else {
                        newValues[getFieldName(key) + "excludeRecipientList"] = [];
                    }
                    if (noticeConfig.unbindEmailList !== undefined && noticeConfig.unbindEmailList !== null && noticeConfig.unbindEmailList.length > 0 ) {
                        newValues[getFieldName(key) + "unbindEmailList"] =
                        noticeConfig.unbindEmailList;
                    } else {
                        newValues[getFieldName(key) + "unbindEmailList"] = [];
                    }
                    
                    if (key === "notice.basic.settings") {
                        if(noticeConfig.automatic !== undefined && noticeConfig.automatic !== null){
                            if(noticeConfig.automatic === 3){
                                newValues["automatic"] = [1,2];
                            } else {
                                newValues["automatic"] = [noticeConfig.automatic];
                            }
                        } else {
                            newValues["automatic"] = [];
                        }
                        if(noticeConfig.manual !== undefined && noticeConfig.manual !== null){
                            if(noticeConfig.manual === 3){
                                newValues["manual"] = [1,2];
                            } else {
                                newValues["manual"] = [noticeConfig.manual];
                            }
                        } else {
                            newValues["manual"] = [];
                        }
                    }
                    if (key === "notice.order.timeout") {
                        newValues["timeoutDays"] = noticeConfig.timeoutDays;
                        newValues["sendDays"] = noticeConfig.sendDays;
                    }
                    if (key === "notice.subject.dispensing") {
                        newValues["stateDispensing"] = noticeConfig.state;
                        //newValues["contentDispensing"] = noticeConfig.fieldsConfig
                    }
                    if (key === "notice.medicine.isolation") {
                        newValues["stateIsolation"] = noticeConfig.state;
                    }
                    if (key === "notice.medicine.order") {
                        newValues["stateOrder"] = noticeConfig.state;
                    }
                    if (key === "notice.medicine.alarm") {
                        newValues["stateAlarm"] = noticeConfig.state;
                        newValues["forecastTime"] = noticeConfig.forecastTime;
                    }
                    if (key === "notice.subject.update") {
                        newValues["stateSubjectUpdate"] = noticeConfig.state;
                        newValues["fieldsConfigSubjectUpdate"] = noticeConfig.fieldsConfig;
                    }
                    if (key === "notice.subject.screen") {
                        newValues["stateSubjectScreen"] = noticeConfig.state;
                    }
                });
                ctx.form.setFieldsValue({ ...newValues });
                setFormData({ ...newValues });
            }
        });
    };

    const [key, setKey] = useSafeState("notice.basic.settings");
    const [keyName, setKeyName] = useSafeState("basicSettings");

    useEffect(
        () => {
           // if (auth.isRandomDispensing.random && !auth.isRandomDispensing.dispensing ) {
           if (!auth.isRandomDispensing.dispensing) {
                setConfig([
                    "notice.basic.settings",
                    "notice.subject.add",
                    "notice.subject.random",
                    "notice.subject.signOut",
                    "notice.subject.replace",
                    "notice.subject.screen",
                    "notice.subject.update",
                    "notice.subject.alarm",
                    "notice.subject.unblinding",
                    "notice.subject.alert.threshold",
                ]);
            }else{
                setConfig([
                    "notice.basic.settings",
                    "notice.subject.add",
                    "notice.subject.random",
                    "notice.subject.signOut",
                    "notice.subject.replace",
                    "notice.subject.screen",
                    "notice.subject.update",
                    "notice.subject.dispensing",
                    "notice.subject.alarm",
                    "notice.subject.unblinding",
                    "notice.medicine.isolation",
                    "notice.medicine.order",
                    "notice.medicine.reminder",
                    "notice.medicine.alarm",
                    "notice.storehouse.alarm",
                    "notice.order.timeout",
                    "notice.subject.alert.threshold",
                    "notice.subject.medicine.alarm",
                    "notice.subject.medicine.capping",
                    "notice.medicine.un_provide_date",

                ]);
            };
            listNotice();
            listRun({
                projectId: auth.project.id,
                template: researchAttribute === 1 ? 2 : 1,
            }).then((result) => {
                const res = result as Result;
                ctx.setRoles(res.data);
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [auth.isRandomDispensing, key, keyName]
    );

    const switchMenu = (key: string) => {
        ctx.form.setFieldsValue({ ...formData });
        setFormData({ ...formData });
        let newForm = {};
        if (key === `notice.subject.dispensing`) {
            newForm = { ...formData, state: formData.stateDispensing };
            ctx.form.setFieldsValue({ ...newForm });
            setFormData({ ...newForm });
        } else if (key === `notice.medicine.isolation`) {
            newForm = { ...formData, state: formData.stateIsolation };
            ctx.form.setFieldsValue({ ...newForm });
            setFormData({ ...newForm });
        } else if (key === `notice.medicine.order`) {
            newForm = { ...formData, state: formData.stateOrder };
            ctx.form.setFieldsValue({ ...newForm });
            setFormData({ ...newForm });
        } else if (key === `notice.subject.update`) {
            newForm = { ...formData, state: formData.stateSubjectUpdate };
            ctx.form.setFieldsValue({ ...newForm });
            setFormData({ ...newForm });
        } else if (key === `notice.subject.screen`) {
            newForm = { ...formData, state: formData.stateSubjectScreen };
            ctx.form.setFieldsValue({ ...newForm });
            setFormData({ ...newForm });
        }
        if (key === `notice.medicine.alarm`) {
            newForm = { ...formData, state: formData.stateAlarm };
            if (formData.stateAlarm?.find((it: any) => it === "order.forecast_title")) {
                ctx.setShowForecast(true)
            } else {
                ctx.setShowForecast(false)
            }
            ctx.form.setFieldsValue({ ...newForm });
        } else {
            newForm = { ...formData };
            ctx.form.setFieldsValue({ ...newForm });
            setFormData({ ...newForm });
        }
        setKey(key);
        if (formData[getFieldName(key) + "excludeRecipientList"] !== undefined && formData[getFieldName(key) + "excludeRecipientList"] !== null && formData[getFieldName(key) + "excludeRecipientList"].length > 0 ) {
            ctx.setEmailList(formData[getFieldName(key) + "excludeRecipientList"]);
        } else {
            ctx.setEmailList([]);
        }
        if (formData[getFieldName(key) + "unbindEmailList"] !== undefined && formData[getFieldName(key) + "unbindEmailList"] !== null && formData[getFieldName(key) + "unbindEmailList"].length > 0 ) {
            ctx.setUnbindEmailList(formData[getFieldName(key) + "unbindEmailList"]);
        } else {
            ctx.setUnbindEmailList([]);
        }
        
        setKeyName(getFieldName(key));
    };

    const switchMenuHide = (key: string) => {
        runAsync({
            customerId: auth.customerId,
            projectId: auth.project.id,
            envId: auth.env.id,
        }).then((result: any) => {
            if (result.code === 0) {
                let newValues: any = {};
                result.data.forEach((noticeConfig: any) => {
                    let key = noticeConfig.key;
                    newValues[getFieldName(key)] = noticeConfig.roles;
                    newValues[getFieldName(key) + "Content"] =
                        noticeConfig.fieldsConfig;
                    if (noticeConfig.excludeRecipientList !== undefined && noticeConfig.excludeRecipientList !== null && noticeConfig.excludeRecipientList.length > 0 ) {
                        newValues[getFieldName(key) + "excludeRecipientList"] =
                        noticeConfig.excludeRecipientList;
                    } else {
                        newValues[getFieldName(key) + "excludeRecipientList"] = [];
                    }
                    if (noticeConfig.unbindEmailList !== undefined && noticeConfig.unbindEmailList !== null && noticeConfig.unbindEmailList.length > 0 ) {
                        newValues[getFieldName(key) + "unbindEmailList"] =
                        noticeConfig.unbindEmailList;
                    } else {
                        newValues[getFieldName(key) + "unbindEmailList"] = [];
                    }
                    
                    if (key === "notice.basic.settings") {
                        if(noticeConfig.automatic !== undefined && noticeConfig.automatic !== null){
                            if(noticeConfig.automatic === 3){
                                newValues["automatic"] = [1,2];
                            } else {
                                newValues["automatic"] = [noticeConfig.automatic];
                            }
                        } else {
                            newValues["automatic"] = [];
                        }
                        if(noticeConfig.manual !== undefined && noticeConfig.manual !== null){
                            if(noticeConfig.manual === 3){
                                newValues["manual"] = [1,2];
                            } else {
                                newValues["manual"] = [noticeConfig.manual];
                            }
                        } else {
                            newValues["manual"] = [];
                        }
                    }
                    if (key === "notice.order.timeout") {
                        newValues["timeoutDays"] = noticeConfig.timeoutDays;
                        newValues["sendDays"] = noticeConfig.sendDays;
                    }
                    if (key === "notice.subject.dispensing") {
                        newValues["stateDispensing"] = noticeConfig.state;
                        //newValues["contentDispensing"] = noticeConfig.fieldsConfig
                    }
                    if (key === "notice.medicine.isolation") {
                        newValues["stateIsolation"] = noticeConfig.state;
                    }
                    if (key === "notice.medicine.order") {
                        newValues["stateOrder"] = noticeConfig.state;
                    }
                    if (key === "notice.medicine.alarm") {
                        newValues["stateAlarm"] = noticeConfig.state;
                        newValues["forecastTime"] = noticeConfig.forecastTime;
                    }
                    if (key === "notice.subject.update") {
                        newValues["stateSubjectUpdate"] = noticeConfig.state;
                        newValues["fieldsConfigSubjectUpdate"] = noticeConfig.fieldsConfig;
                    }
                    if (key === "notice.subject.screen") {
                        newValues["stateSubjectScreen"] = noticeConfig.state;
                    }
                });
                ctx.form.setFieldsValue({ ...newValues });
                setFormData({ ...newValues });

                // console.log("qq==" + JSON.stringify(key));
                // console.log("qq==" + JSON.stringify(getFieldName(key)));
                // console.log("qq==" + JSON.stringify(formData));
                // console.log("ss==" + JSON.stringify(formData[getFieldName(key) + "excludeRecipientList"]));
                // console.log("ww==" + JSON.stringify(ctx.emailList));
                ctx.form.setFieldsValue({ ...newValues });
                setFormData({ ...newValues });
                let newForm = {};
                if (key === `notice.subject.dispensing`) {
                    newForm = { ...newValues, state: newValues.stateDispensing };
                    ctx.form.setFieldsValue({ ...newForm });
                    setFormData({ ...newForm });
                } else if (key === `notice.medicine.isolation`) {
                    newForm = { ...newValues, state: newValues.stateIsolation };
                    ctx.form.setFieldsValue({ ...newForm });
                    setFormData({ ...newForm });
                } else if (key === `notice.medicine.order`) {
                    newForm = { ...newValues, state: newValues.stateOrder };
                    ctx.form.setFieldsValue({ ...newForm });
                    setFormData({ ...newForm });
                } else if (key === `notice.subject.update`) {
                    newForm = { ...newValues, state: newValues.stateSubjectUpdate };
                    ctx.form.setFieldsValue({ ...newForm });
                    setFormData({ ...newForm });
                } else if (key === `notice.subject.screen`) {
                    newForm = { ...newValues, state: newValues.stateSubjectScreen };
                    ctx.form.setFieldsValue({ ...newForm });
                    setFormData({ ...newForm });
                }
                if (key === `notice.medicine.alarm`) {
                    newForm = { ...newValues, state: newValues.stateAlarm };
                    if (newValues.stateAlarm?.find((it: any) => it === "order.forecast_title")) {
                        ctx.setShowForecast(true)
                    } else {
                        ctx.setShowForecast(false)
                    }
                    ctx.form.setFieldsValue({ ...newForm });
                } else {
                    newForm = { ...newValues };
                    ctx.form.setFieldsValue({ ...newForm });
                    setFormData({ ...newForm });
                }
                setKey(key);
                // console.log("kk==" + JSON.stringify(formData))
                if (newValues[getFieldName(key) + "excludeRecipientList"] !== undefined && newValues[getFieldName(key) + "excludeRecipientList"] !== null && newValues[getFieldName(key) + "excludeRecipientList"].length > 0 ) {
                    ctx.setEmailList(newValues[getFieldName(key) + "excludeRecipientList"]);
                    // console.log("11==" + JSON.stringify(ctx.emailList));
                } else {
                    ctx.setEmailList([]);
                    // console.log("22==" + JSON.stringify(ctx.emailList));
                }
                // console.log("ss==" + JSON.stringify(newValues[getFieldName(key) + "excludeRecipientList"]));
                // console.log("ww==" + JSON.stringify(ctx.emailList));
                if (newValues[getFieldName(key) + "unbindEmailList"] !== undefined && newValues[getFieldName(key) + "unbindEmailList"] !== null && newValues[getFieldName(key) + "unbindEmailList"].length > 0 ) {
                    ctx.setUnbindEmailList(newValues[getFieldName(key) + "unbindEmailList"]);
                } else {
                    ctx.setUnbindEmailList([]);
                }
                
                setKeyName(getFieldName(key));

            }
        });
    };

    function cancel() {
        switchMenuHide(key);
        ctx.form.setFieldsValue({ ...formData });
    }
    // const { run, loading: saveLoading } = useFetch(setNoticeConfig, {
    //     manual: true,
    //     onSuccess: (result: any, params) => {
    //         message.success(result.msg);
    //         listNotice();
    //     },
    //     onError: (error) => {
    //         message.error(error.message);
    //     },
    // });

    const {runAsync:run, loading:saveLoading} = useFetch(setNoticeConfig, {manual: true});

    const {runAsync:getNoticeConfigVerifyRun, loading:getNoticeConfigVerifyLoading} = useFetch(getNoticeConfigVerify, {manual: true});

    

    const hide = () => {
        // listNotice();
        setIsEdit(false);
        ctx.setDisabled(false);
        switchMenuHide(key);
        ctx.setImportExclusiveReceivers([]);
        ctx.setImportExclusiveReceiversFile(undefined);
        ctx.setImportExclusiveReceiversPreview(false);
    };

    function save(key: string) {
        ctx.form.validateFields().then((values: any) => {
            let automatic = 0;
            if(values.automatic !== null && values.automatic !== undefined && values.automatic.length !== 0){
                if(values.automatic.length === 2){
                    automatic = 3;
                } else {
                    automatic = values.automatic[0];
                }
            }
            let manual = 0;
            if(values.manual !== null && values.manual !== undefined && values.manual.length !== 0){
                if(values.manual.length === 2){
                    manual = 3;
                } else {
                    manual = values.manual[0];
                }
            }
            // 合并并去重
            const mergedArray = (ctx.emailList?ctx.emailList:[]).concat(ctx.importExclusiveReceivers?ctx.importExclusiveReceivers:[]).reduce((acc: any[], item: any) => {
                if (!acc.includes(item)) {
                    acc.push(item);
                }
                return acc;
            }, []);

            if(mergedArray.length > 0){
                getNoticeConfigVerifyRun({},{
                    customerId: auth.customerId,
                    projectId: auth.project.id,
                    envId: auth.env.id,
                    key: key,
                    roles: values[keyName],
                    automatic: automatic,
                    manual: manual,
                    timeoutDays: values.timeoutDays,
                    forecastTime: values.forecastTime,
                    sendDays: values.sendDays,
                    state: values.state,
                    fieldsConfig: values[keyName + "Content"],
                    excludeRecipientList: mergedArray,
                    unbindEmailList:[],
                }).then(
                    (resp:any) => {
                        let data = resp.data
                        if (data !== undefined && data !== null && data.length > 0) {
                            edit_prompt.current.show(
                                auth.customerId,
                                auth.project.id,
                                auth.env.id,
                                key,
                                values[keyName],
                                automatic,
                                manual,
                                values.timeoutDays,
                                values.forecastTime,
                                values.sendDays,
                                values.state,
                                values[keyName + "Content"],
                                mergedArray,
                                data,
                            );
                        } else {
                            run({
                                customerId: auth.customerId,
                                projectId: auth.project.id,
                                envId: auth.env.id,
                                key: key,
                                roles: values[keyName],
                                automatic: automatic,
                                manual: manual,
                                timeoutDays: values.timeoutDays,
                                forecastTime: values.forecastTime,
                                sendDays: values.sendDays,
                                state: values.state,
                                fieldsConfig: values[keyName + "Content"],
                                excludeRecipientList: mergedArray,
                                unbindEmailList:[],
                            }).then(
                                (result:any) => {
                                    message.success(result.msg);
                                    hide();
                                }
                            )
                        }
                    }
                )
            }else {
                run({
                    customerId: auth.customerId,
                    projectId: auth.project.id,
                    envId: auth.env.id,
                    key: key,
                    roles: values[keyName],
                    automatic: automatic,
                    manual: manual,
                    timeoutDays: values.timeoutDays,
                    forecastTime: values.forecastTime,
                    sendDays: values.sendDays,
                    state: values.state,
                    fieldsConfig: values[keyName + "Content"],
                    excludeRecipientList: mergedArray,
                    unbindEmailList:[],
                }).then(
                    (result:any) => {
                        message.success(result.msg);
                        hide();
                    }
                )
            }

        });
    }
    return (
        <Row style={{ flex: 1 }} justify="space-between">
            <Col
                style={{
                    width: g.lang === "zh"? "164px" : "230px",
                    borderRight: "1px solid #E3E4E6",
                    height: "calc(100vh - 58px)",
                    overflowY: "auto",
                }}
            >
                <MenuCustomer>
                    <Menu selectedKeys={[key]}>
                        {config.map((it: any) => (
                            <Menu.Item
                                key={it}
                                onClick={() => {
                                    switchMenu(it);
                                    setIsEdit(false);
                                    ctx.setDisabled(false);
                                    ctx.setImportExclusiveReceivers([]);
                                    ctx.setImportExclusiveReceiversFile(undefined);
                                    ctx.setImportExclusiveReceiversPreview(false);

                                }}
                            >
                                <Tooltip
                                    title={<FormattedMessage id={it} />}
                                >
                                    <span><FormattedMessage id={it} /></span>
                                </Tooltip>
                            </Menu.Item>
                        ))}
                    </Menu>
                </MenuCustomer>
            </Col>
            <Col
                style={{
                    marginTop: 12,
                    position: "relative",
                    flex: 1,
                }}
            >
                <Spin spinning={listLoading || loading}>
                    <div
                        style={{
                            marginLeft: 24,
                            height: "calc(100vh - 147px)",
                            overflowY: "auto",
                        }}
                    >
                        {config.map(
                            (it: any) =>
                                it === key && (
                                    <RoleList
                                        key={it}
                                        refresh={refresh}
                                        keys={key}
                                        name={keyName}
                                        disabled={!isEdit}
                                    />
                                )
                        )}
                    </div>
                </Spin>

                <FooterRow justify="end">
                    {auth.project.status !== 2 &&
                        permissions(
                            auth.project.permissions,
                            "operation.build.settings.notice.edit"
                        ) &&
                        !isEdit && (
                            <Col className="text-right">
                                <AuthButton
                                    onClick={() => {
                                        setIsEdit(true)
                                        ctx.setDisabled(true);
                                    }}
                                    type="primary"
                                >
                                    <FormattedMessage id="common.edit" />
                                </AuthButton>
                            </Col>
                        )}
                    {isEdit && (
                        <Col className="text-right">
                            <AuthButton
                                className="mar-rgt-10"
                                onClick={() => {
                                    setIsEdit(false);
                                    ctx.setDisabled(false);
                                    cancel();
                                }}
                            >
                                <FormattedMessage id="common.cancel" />
                            </AuthButton>
                            <AuthButton
                                loading={saveLoading || getNoticeConfigVerifyLoading}
                                type="primary"
                                onClick={() => {
                                    save(key);
                                }}
                            >
                                <FormattedMessage id="common.save" />
                            </AuthButton>
                        </Col>
                    )}
                </FooterRow>
            </Col>
            <EditPrompt bind={edit_prompt} refresh={hide}/>
        </Row>
    );
};

const MenuCustomer = styled.div`
    .ant-menu-inline,
    .ant-menu-vertical,
    .ant-menu-vertical-left {
        border-right: 0px !important;
    }
`;

const FooterRow = styled(Row)`
    position: absolute;
    width: 100%;
    bottom: 0;
    right: 0;
    border-top: 1px solid #e3e4e6;
    padding: 16px 23px;
    background: #fff;
`;
