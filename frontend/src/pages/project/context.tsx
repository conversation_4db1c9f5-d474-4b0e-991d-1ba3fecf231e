import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const ProjectContext = React.createContext<
    {
        learnVisible: boolean;
        setLearnVisible: (data: boolean) => void;
        mustLearn: boolean;
        setMustLearn: (data: boolean) => void;
        learnToken: any;
        setLearnToken: (data: any) => void;
        orderStatus: any;
        setOrderStatus: (data: any) => void;
    } | null
>(null)

export const ProjectContextProvider = ({ children }: { children: ReactNode }) => {
    const [learnVisible, setLearnVisible] = useSafeState<boolean>(false);
    const [mustLearn, setMustLearn] = useSafeState<boolean>(true);
    const [learnToken,setLearnToken] = useSafeState<any>("");
    const [orderStatus,setOrderStatus] = useSafeState<any>(0);

    return (
        <ProjectContext.Provider
            value={
                {
                    learnVisible, setLearnVisible,
                    learnToken,setLearnToken,
                    must<PERSON>earn,setMust<PERSON>earn,
                    orderStatus,setOrderStatus,
                }
            }
        >
            {children}
        </ProjectContext.Provider>
    )
};

export const useProject = () => {
    const context = React.useContext(ProjectContext);
    if (!context) {
        throw new Error("useProject must be used in HomeContextProvider");
    }
    return context;
};

