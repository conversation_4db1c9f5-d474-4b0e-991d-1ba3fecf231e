import React from "react";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import { Button, Col, Descriptions, Divider, Form, Input, message, Modal, Radio, Row, Table, } from "antd";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../context/auth";
import { useFetch } from "../../../hooks/request";
import { Title } from "components/title";
import { PageContextProvider } from "context/page";
import { useCacheTable } from "hooks/cache-table";
import { PaginationView } from "components/pagination";
import { useGlobal } from "../../../context/global";
import {
    approvalProcessStatus,
    orderAddTaskStatus,
    shipmentMode,
} from "../../../data/data";
import moment from "moment";
import { updateApprovalTask } from "../../../api/approval_process";
import ReactToPrint from "react-to-print";
import { getApplicableSiteSupplyPlan } from "../../../api/supply_plan";
import { permissions } from "../../../tools/permission";
import { ApprovalDetailPrint } from "../supply/shipment/approval_detail_print";
import { useSubject } from "./context";
import { fillTableCellEmptyPlaceholder } from "../../../components/table";


export const ApprovalProcessDetail = (props: any) => {
    const intl = useTranslation();
    const home = useSubject();
    const g = useGlobal();
    const { formatMessage } = intl;
    const auth = useAuth();
    const [visible, setVisible] = useSafeState(false);
    const [id, setId] = useSafeState(null);
    const [mode, setMode] = useSafeState(1);
    const [submitting, setSubmitting] = useSafeState(false);
    const [send, setSend] = useSafeState("");
    const [receive, setReceive] = useSafeState("");
    const [record, setRecord] = useSafeState("");
    const [contacts, setContacts] = useSafeState("");
    const [supplyCount, setSupplyCount] = useSafeState("");
    const [phone, setPhone] = useSafeState("");
    const [email, setEmail] = useSafeState("");
    const [address, setAddress] = useSafeState("");
    const [status, setStatus] = useSafeState(0);
    const [approvalStatus, setApprovalStatus] = useSafeState(0);
    const [reason, setReason] = useSafeState("");
    const [estimatedCompletionTime, setEstimatedCompletionTime] =
        useSafeState(null);
    const [approvalTime, setApprovalTime] = useSafeState(null);
    const [expectedArrivalTime, setExpectedArrivalTime] = useSafeState(null);
    const [tableData, setTableData] = useSafeState([]);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [supplyName, setSupplyName] = useSafeState("");
    const [drugNames, setDrugNames] = useSafeState([]);
    const [blindCount, setBlindCount] = useSafeState(0);
    const [openDrugCount, setOpenDrugCount] = useSafeState([]);
    const [form] = Form.useForm();
    const timeZone =
        (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "")
            ? Number(auth.project.info.timeZoneStr)
            : 8;
    const projectNumber = auth.project.info.number;
    const envId = auth.env ? auth.env.id : null;
    const envName = auth.env ? auth.env.name : "";
    const [packageIsOpen, setPackageIsOpen] = useSafeState<any>(false);
    let componentRef: any = React.useRef();

    const show = (
        item: any,
        sites: any[],
        institutes: any[],
        packageIsOpen: any
    ) => {
        setRecord(item);
        setId(item.id);
        setVisible(true);
        //获取供应计划
        getSupplyPlanList(item.data.receiveId, item.data.supplyId);
        const storehouse = institutes.find(
            (it) => item.data.sendId === it.value
        );
        if (storehouse != null) {
            setSend(storehouse.label);
        }
        const site = sites.find((it) => item.data.receiveId === it.value);
        if (site != null) {
            setReceive(site.label);
        }
        setContacts(item.data.contacts);
        setPhone(item.data.phone);
        setEmail(item.data.email);
        setAddress(item.data.address);
        setDrugNames(item.data.drugNames);
        setTableData(item.data.detailData);
        setOtherTableData(item.data.otherDetailData);
        setMode(item.data.mode);
        setStatus(item.status);
        setEstimatedCompletionTime(item.estimatedCompletionTime);
        setApprovalTime(item.approvalTime);
        setExpectedArrivalTime(item.data.expectedArrivalTime);
        setReason(item.reason);
        setApprovalStatus(item.approvalStatus);
        setPackageIsOpen(packageIsOpen);
        setSupplyCount(item.data.supplyCount);
        setBlindCount(item.data.blindCount);
        setOpenDrugCount(item.data.openDrugCount);
    };

    //获取供应计划
    const { runAsync: run_getSupplyPlanList } = useFetch(
        getApplicableSiteSupplyPlan,
        { manual: true }
    );
    const getSupplyPlanList = (receiveId: any, suppyId: any) => {
        run_getSupplyPlanList({
            envId: envId,
            siteId: receiveId,
        }).then((result: any) => {
            const data = result.data;
            if (data != null) {
                if (data.optionalSupplyPlan.length > 0) {
                    var supplyPlan = data.optionalSupplyPlan.filter(
                        (it: any) => it.id === suppyId
                    );
                    if (supplyPlan.length > 0) {
                        setSupplyName(supplyPlan[0].name);
                    }
                }
            }
        });
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        setSubmitting(false);
    };

    const { runAsync: run_updateApprovalTask } = useFetch(updateApprovalTask, {
        manual: true,
    });
    const save = () => {
        form.validateFields()
            .then((values) => {
                setSubmitting(true);
                run_updateApprovalTask({
                    id: id,
                    ...values,
                }).then(
                    (result: any) => {
                        home.setTaskUpdate(home.taskUpdate + 1);
                        message.success(result.msg);
                        props.refresh();
                        hide();
                    },
                    (data) => {
                        hide();
                    }
                );
            })
            .catch((errors) => {
                setSubmitting(false);
            });
    };

    const changeApprovalStatus = (status: any) => {
        setApprovalStatus(status);
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                className="custom-large-modal"
                title={
                    formatMessage({ id: "project.task.addOrder.title" }) +
                    ` - ${projectNumber}[${envName}]`
                }
                centered={true}
                visible={visible}
                onCancel={hide}
                bodyStyle={{ paddingTop: "0px", overflowX: "hidden" }}
                width={1000}
                footer={
                    status === 0 &&
                    permissions(
                        auth.project.permissions,
                        "operation.supply.shipment.approval"
                    ) && (
                        <Row justify="end">
                            <Col>
                                <Button onClick={hide}>
                                    <FormattedMessage id="common.cancel" />
                                </Button>
                                <Button
                                    onClick={save}
                                    type="primary"
                                    loading={submitting}
                                >
                                    <FormattedMessage id="common.ok" />
                                </Button>
                            </Col>
                        </Row>
                    )
                }
            >
                <Row style={{ margin: "16px 0" }}>
                    <Col span={8}>
                        <Form.Item
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <svg
                                        className="iconfont"
                                        width={18}
                                        height={18}
                                        style={{ marginRight: "5px" }}
                                    >
                                        <use xlinkHref="#icon-renwuzhuangtai"></use>
                                    </svg>
                                    <FormattedMessage id="project.task.status" />
                                </div>
                            }
                            className="mar-ver-5"
                        >
                            {status === 0 && (
                                <div
                                    style={{
                                        background: "#f6f6f8",
                                        color: "#ADB2BA",
                                        fontStyle: "normal",
                                        width:
                                            g.lang === "en" ? "96px" : "65px",
                                        fontWeight: 400,
                                    }}
                                >
                                    <span
                                        style={{
                                            marginLeft: "4px",
                                            marginRight: "4px",
                                        }}
                                    >
                                        <svg
                                            className="iconfont"
                                            width={12}
                                            height={12}
                                        >
                                            <use xlinkHref="#icon-weikaishi"></use>
                                        </svg>
                                    </span>
                                    {
                                        orderAddTaskStatus.find(
                                            (it) => it.value === status
                                        )?.label
                                    }
                                </div>
                            )}
                            {status === 1 && (
                                <div
                                    style={{
                                        background: "#e3f8ec",
                                        color: "#41CC82",
                                        fontStyle: "normal",
                                        width:
                                            g.lang === "en" ? "96px" : "65px",
                                        fontWeight: 400,
                                    }}
                                >
                                    <span
                                        style={{
                                            marginLeft: "4px",
                                            marginRight: "4px",
                                        }}
                                    >
                                        <svg
                                            className="iconfont"
                                            width={12}
                                            height={12}
                                        >
                                            <use xlinkHref="#icon-yiwancheng1"></use>
                                        </svg>
                                    </span>
                                    {
                                        orderAddTaskStatus.find(
                                            (it) => it.value === status
                                        )?.label
                                    }
                                </div>
                            )}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <svg
                                        className="iconfont"
                                        width={18}
                                        height={18}
                                        style={{ marginRight: "5px" }}
                                    >
                                        <use xlinkHref="#icon-yuqiwancheng"></use>
                                    </svg>
                                    <FormattedMessage id="project.task.estimatedCompletionTime" />
                                </div>
                            }
                            className="mar-ver-5"
                        >
                            {estimatedCompletionTime === null ||
                                estimatedCompletionTime === 0
                                ? ""
                                : moment
                                    .unix(estimatedCompletionTime)
                                    .utc()
                                    .add(timeZone, "hour")
                                    .format("YYYY-MM-DD")}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item
                            label={
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <svg
                                        className="iconfont"
                                        width={18}
                                        height={18}
                                        style={{ marginRight: "5px" }}
                                    >
                                        <use xlinkHref="#icon-shijiwancheng"></use>
                                    </svg>
                                    <FormattedMessage id="project.task.approvalTime" />
                                </div>
                            }
                            className="mar-ver-5"
                        >
                            {approvalTime === null || approvalTime === 0
                                ? "-"
                                : moment
                                    .unix(approvalTime)
                                    .utc()
                                    .add(timeZone, "hour")
                                    .format("YYYY-MM-DD HH:mm:ss")}
                        </Form.Item>
                    </Col>
                </Row>
                <Divider
                    style={{
                        margin: "0px 0px 8px -24px",
                        borderWidth: "4px",
                        width: "1000px",
                        background: "#F9FAFB !important",
                    }}
                ></Divider>
                <div style={{ marginBottom: 12 }}>
                    <Title
                        name={formatMessage({
                            id: "shipment.basic.information",
                        })}
                    ></Title>
                </div>
                <Descriptions
                    column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
                >
                    <Descriptions.Item
                        label={formatMessage({ id: "shipment.send" })}
                        labelStyle={{
                            minWidth: g.lang === "zh" ? "96px" : "136px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {send}
                    </Descriptions.Item>
                    <Descriptions.Item
                        label={formatMessage({ id: "shipment.receive" })}
                        labelStyle={{
                            minWidth: "100px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {receive}
                    </Descriptions.Item>
                    <Descriptions.Item
                        label={formatMessage({
                            id: "projects.supplyPlan.supplyMode",
                        })}
                        labelStyle={{
                            minWidth: g.lang === "zh" ? "96px" : "136px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {shipmentMode.find((it) => mode === it.value)?.label}
                    </Descriptions.Item>
                    {mode !== 5 &&
                        <>
                            <Descriptions.Item
                                label={formatMessage({ id: "drug.configure.drugName" })}
                                labelStyle={{
                                    minWidth: "100px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {drugNames.join(", ")}
                            </Descriptions.Item>
                            <Descriptions.Item
                                label={formatMessage({ id: "shipment.supply" })}
                                labelStyle={{
                                    minWidth: g.lang === "zh" ? "86px" : "126px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {supplyName}
                            </Descriptions.Item>
                            <Descriptions.Item
                                label={formatMessage({ id: "shipment.supply.count" })}
                                labelStyle={{
                                    minWidth: g.lang === "zh" ? "96px" : "136px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {supplyCount ? supplyCount : "-"}
                            </Descriptions.Item>
                        </>
                    }
                    <Descriptions.Item
                        label={formatMessage({ id: "common.contacts" })}
                        labelStyle={{
                            minWidth: "100px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {(contacts ? contacts : "-") +
                            "/" +
                            (phone ? phone : "-") +
                            "/" +
                            (email ? email : "-") +
                            "/" +
                            (address ? address : "-")}
                    </Descriptions.Item>
                    <Descriptions.Item
                        label={formatMessage({ id: "shipment.expectedArrivalTime" })}
                        labelStyle={{
                            minWidth: g.lang === "zh" ? "96px" : "136px",
                            justifyContent: "flex-end",
                            color: "#4E5969",
                        }}
                    >
                        {expectedArrivalTime === null || expectedArrivalTime === ""
                            ? "-"
                            : expectedArrivalTime}
                    </Descriptions.Item>
                </Descriptions>

                {mode === 5 &&
                    <>
                        <div style={{ marginBottom: 12 }}>
                            <Title
                                name={formatMessage({
                                    id: "drug.medicine",
                                })}
                            ></Title>
                        </div>
                        <Descriptions
                            column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
                        >
                            <Descriptions.Item
                                label={formatMessage({
                                    id: "shipment.blindCount",
                                })}
                                labelStyle={{
                                    minWidth: g.lang === "zh" ? "96px" : "136px",
                                    justifyContent: "flex-end",
                                    color: "#4E5969",
                                }}
                            >
                                {blindCount}
                            </Descriptions.Item>
                            <Descriptions.Item
                                label=""
                            >
                                <div></div>
                            </Descriptions.Item>
                            {openDrugCount.map((it: any) => (
                                <Descriptions.Item
                                    label={it.drugName}
                                    labelStyle={{
                                        minWidth: g.lang === "zh" ? "96px" : "136px",
                                        justifyContent: "flex-end",
                                        color: "#4E5969",
                                    }}
                                >
                                    {it.number}
                                </Descriptions.Item>
                            ))
                            }
                        </Descriptions>
                    </>
                }


                {mode !== 5 && tableData != null && tableData.length > 0 && (
                    <div>
                        <div style={{ marginBottom: 12 }}>
                            <Title
                                name={formatMessage({ id: "shipment.medicine" })}
                            ></Title>
                        </div>
                        <DrugGroupCacheTable
                            packageIsOpen={packageIsOpen}
                            dataSource={tableData}
                        ></DrugGroupCacheTable>
                    </div>
                )}
                {mode !== 5 && otherTableData != null && otherTableData.length > 0 && (
                    <div>
                        <div style={{ marginBottom: 12 }}>
                            <Title
                                name={formatMessage({ id: "drug.other" })}
                            ></Title>
                        </div>
                        <DrugGroupCacheTable
                            packageIsOpen={packageIsOpen}
                            dataSource={otherTableData}
                        ></DrugGroupCacheTable>
                    </div>
                )}

                {status !== 0 ? (
                    <>
                        <div>
                            <Row>
                                <Col flex="auto">
                                    <Title
                                        name={formatMessage({
                                            id: "shipment.approval.confirm.title",
                                        })}
                                    ></Title>
                                </Col>
                                <Col flex="100px">
                                    {permissions(
                                        auth.project.permissions,
                                        "operation.supply.shipment.approval.print"
                                    ) && (
                                            <ReactToPrint
                                                trigger={() => {
                                                    return (
                                                        <Button
                                                            type={"text"}
                                                            style={{
                                                                color: "#165DFF",
                                                                display: "flex",
                                                                alignItems:
                                                                    "center",
                                                            }}
                                                        >
                                                            <svg
                                                                className="iconfont"
                                                                width={16}
                                                                height={16}
                                                                style={{
                                                                    marginRight:
                                                                        "8px",
                                                                }}
                                                            >
                                                                <use xlinkHref="#icon-dayin"></use>
                                                            </svg>
                                                            {
                                                                <FormattedMessage id={"common.print"} />
                                                            }
                                                        </Button>
                                                    );
                                                }}
                                                content={() => componentRef}
                                            />
                                        )}
                                </Col>
                            </Row>
                            <Row>
                                <Form.Item
                                    label={formatMessage({
                                        id: "shipment.approval.confirm",
                                    })}
                                    className="mar-ver-5"
                                >
                                    {approvalStatus === 1 ||
                                        approvalStatus === 2
                                        ? approvalProcessStatus.find(
                                            (it) =>
                                                it.value === approvalStatus
                                        )?.label
                                        : ""}
                                </Form.Item>
                            </Row>
                            {approvalStatus === 2 ? (
                                <Row>
                                    <Form.Item
                                        label={formatMessage({id: "common.reason"})}
                                        className="mar-ver-5"
                                    >
                                        {reason}
                                    </Form.Item>
                                </Row>
                            ) : null}
                        </div>
                    </>
                ) : null}

                {status === 0 ? (
                    <>
                        <div>
                            <div style={{ marginBottom: 12 }}>
                                <Title
                                    name={formatMessage({
                                        id: "shipment.approval.confirm.title",
                                    })}
                                ></Title>
                            </div>
                            {permissions(
                                auth.project.permissions,
                                "operation.supply.shipment.approval"
                            ) ? (
                                <>
                                    <Form form={form}>
                                        <Form.Item
                                            label={formatMessage({
                                                id: "shipment.approval.confirm",
                                            })}
                                            name="approvalStatus"
                                            rules={[{ required: true }]}
                                            className="mar-ver-5"
                                        >
                                            <Radio.Group
                                                onChange={(e) =>
                                                    changeApprovalStatus(
                                                        e.target.value
                                                    )
                                                }
                                            >
                                                <Radio value={1}>
                                                    <FormattedMessage id="project.task.approvalStatus.pass" />
                                                </Radio>
                                                <Radio value={2}>
                                                    <FormattedMessage id="project.task.approvalStatus.reject" />
                                                </Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                        {approvalStatus === 2 ? (
                                            <Form.Item
                                                label={formatMessage({id: "common.reason"})}
                                                name="reason"
                                                rules={[{ required: true }]}
                                                className="full-width"
                                            >
                                                <Input.TextArea
                                                    allowClear
                                                    autoSize={{
                                                        minRows: 2,
                                                        maxRows: 10,
                                                    }}
                                                />
                                            </Form.Item>
                                        ) : null}
                                    </Form>
                                </>
                            ) : (
                                <>
                                    <Form form={form}>
                                        <Form.Item
                                            label={formatMessage({
                                                id: "shipment.approval.confirm",
                                            })}
                                            name="approvalStatus"
                                            className="mar-ver-5"
                                        >
                                            <Radio.Group
                                                onChange={(e) =>
                                                    changeApprovalStatus(
                                                        e.target.value
                                                    )
                                                }
                                                disabled
                                            >
                                                <Radio value={1}>
                                                    <FormattedMessage id="project.task.approvalStatus.pass" />
                                                </Radio>
                                                <Radio value={2}>
                                                    <FormattedMessage id="project.task.approvalStatus.reject" />
                                                </Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                    </Form>
                                </>
                            )}
                        </div>
                    </>
                ) : null}
            </Modal>
            <div style={{ display: "none" }}>
                <div ref={(el) => (componentRef = el)}>
                    <ApprovalDetailPrint
                        record={record}
                        send={send}
                        receive={receive}
                        detailData={tableData}
                        otherTableData={otherTableData}
                        mode={mode}
                        drugNames={drugNames}
                        supplyName={supplyName}
                        packageIsOpen={packageIsOpen}
                        supplyCount={supplyCount}
                        expectedArrivalTime={expectedArrivalTime}
                        blindCount={blindCount}
                        openDrugCount={openDrugCount}
                    />
                </div>
            </div>
        </React.Fragment>
    );
};

interface DrugGroupCacheTableProp {
    dataSource: any[];
    packageIsOpen: any;
}
const DrugGroupCacheTable = (props: DrugGroupCacheTableProp) => {
    return (
        <PageContextProvider>
            <DrugGroupTable {...props}></DrugGroupTable>
        </PageContextProvider>
    );
};

function renderPackageMethod(packageMethod: Boolean) {
    return packageMethod === true ? (
        <FormattedMessage id="shipment.order.packageMethod.package" />
    ) : (
        <FormattedMessage id="shipment.order.packageMethod.single" />
    );
}

const DrugGroupTable = (props: DrugGroupCacheTableProp) => {
    var tableData = useCacheTable({ dataSource: props.dataSource });
    tableData = fillTableCellEmptyPlaceholder(tableData)
    const intl = useTranslation();
    return (
        <>
            <Table
                className="mar-top-5"
                pagination={false}
                rowKey={(record: any) => record._id}
                dataSource={tableData}
            >
                <Table.Column
                    title={<FormattedMessage id="drug.configure.drugName" />}
                    dataIndex={"name"}
                    key="name"
                    ellipsis
                    width={180}
                />
                <Table.Column
                    title={<FormattedMessage id="drug.list.expireDate" />}
                    dataIndex={"expirationDate"}
                    key="expirationDate"
                    ellipsis
                />
                <Table.Column
                    title={<FormattedMessage id="drug.list.batch" />}
                    dataIndex={"batchNumber"}
                    key="batchNumber"
                    ellipsis
                />
                <Table.Column
                    title={
                        <FormattedMessage id="shipment.order.availableCount" />
                    }
                    dataIndex={"useCount"}
                    key="useCount"
                    ellipsis
                />
                {props.packageIsOpen && (
                    <Table.Column
                        title={
                            <FormattedMessage id="shipment.order.package.method" />
                        }
                        dataIndex={"packageMethod"}
                        key="packageMethod"
                        ellipsis
                        render={(value, record, index) =>
                            renderPackageMethod(value)
                        }
                    />
                )}
                <Table.Column
                    width={150}
                    title={<FormattedMessage id="drug.freeze.count" />}
                    dataIndex={"count"}
                    key="count"
                    ellipsis
                />
            </Table>
            <PaginationView></PaginationView>
        </>
    );
};
