import React, { useEffect, useState } from "react";
import { Card, Col, List, Pagination, Row, Select, Typography } from "antd";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import { useFetch } from "../../../hooks/request";
import { useAuth } from "../../../context/auth";
import { usePage } from "../../../context/page";
import { useSafeState } from "ahooks";
import moment from "moment";
import { getApprovalProcessList } from "../../../api/approval_process";
import { sitesAndStorehouses } from "../../../api/project_site";
import { ApprovalProcessDetail } from "./approval_detail";
import { UnblindingApprovalProcessDetail } from "./unblinding_approval_detail";
import { useGlobal } from "../../../context/global";
import { TitleExtra } from "components/titleExtra";

export const ProjectApprovalTask = () => {
    const g = useGlobal()
    const intl = useTranslation();
    const { formatMessage } = intl;
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const [currentCount, setCurrentCount] = useSafeState(0)
    const [allCount, setAllCount] = useSafeState(0)
    const [current, setCurrent] = useSafeState(1)
    const [status, setStatus] = useSafeState(0);
    const [sites, setSites] = useSafeState<any[]>([]);
    const [institutes, setInstitutes] = useSafeState<any[]>([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState<any>(false);
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8
    const detail_ref = React.useRef<any>();
    const unblinding_detail_ref = React.useRef<any>();
    const [isHover, setIsHover] = useState(false);

    const page = usePage()
    const pageChange = (pageNum: any) => {
        setCurrent(pageNum)
    }

    const { runAsync: run_getApprovalProcessList, loading: allLoading } = useFetch(getApprovalProcessList, { manual: true })
    const { runAsync: run_sitesAndStorehouses } = useFetch(sitesAndStorehouses, { manual: true })

    const list = () => {
        page.setData([]);
        //订单列表查询
        run_getApprovalProcessList(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                // cohortId: cohortId,
                status: status,
                start: (current - 1) * 10,
                limit: 10
            }).then((result: any) => {
                const data = result.data
                if (data != null) {
                    setCurrentCount(data.currentTotal)
                    setAllCount(data.total)
                    page.setTotal(data.currentTotal)
                    if (data.items != null) {
                        page.setData(data.items)
                    }
                    setPackageIsOpen(data.packageIsOpen)
                }
            });
        //获取中心和仓库信息
        run_sitesAndStorehouses(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
            }).then(
                (result: any) => {
                    const data = result.data
                    if (data != null) {
                        let institutes: any[] = [];
                        if (data.storehouse != null) {
                            data.storehouse.forEach((value: any) => {
                                institutes.push(value)
                            });
                        }
                        if (data.site != null) {
                            setSites(data.site)
                            data.site.forEach((value: any) => {
                                institutes.push(value)
                            });
                        }
                        setInstitutes(institutes)

                    }
                }
            )
    }

    // 状态onChange事件
    const statusOnChange = (value: any) => {
        setStatus(value);
        setCurrent(1);
    };

    const handleMouseEnter = (value: any) => {
        setIsHover(true);
    };
    const handleMouseLeave = (value: any) => {
        setIsHover(false);
    };

    const showDetail = (item: any) => {
        if (item.type === 1) {
            detail_ref.current.show(item, sites, institutes, packageIsOpen);
        } else {
            unblinding_detail_ref.current.show(item);
        }

    };

    useEffect(list, [projectId, envId, status, current, page.pageSize, g.lang]);

    return (
        <>
            <Card
                headStyle={{ padding: "0 5px 0 16px" }}
                bodyStyle={{ padding: "16px" }}
                title={<TitleExtra name={formatMessage({ id: 'project.task', allowComponent: true })} extra={<div style={{
                    color: "#677283",
                    fontStyle: "normal",
                    fontWeight: 400,
                    alignItems: "center",
                    display: "flex",
                    fontSize: "14px",
                    marginLeft: "0px",
                }}>(<span style={{ color: "#F96964" }}>{currentCount}</span>/{allCount})</div>} />}
                extra={<div>
                    <Select
                        defaultValue={0}
                        className="task-select"
                        popupClassName="task-popup"
                        style={{ width: intl.locale === "zh" ? "90px" : "115px", color: isHover ? '#165DFF !important' : '#677283 !important', cursor: " pointer", }}
                        bordered={false}
                        onChange={(v: any) => statusOnChange(v)}
                        onMouseEnter={(v: any) => handleMouseEnter(v)}
                        onMouseLeave={(v: any) => handleMouseLeave(v)
                    }>
                        <Select.Option value={100}>
                            <div
                                style={{
                                    color: "#677283",
                                    fontStyle: "normal",
                                    fontWeight: 400,
                                    alignItems: "center",
                                    display: "flex",
                                }}
                            >
                                <FormattedMessage id="common.all" />
                            </div>
                        </Select.Option>
                        <Select.Option value={0}>
                            <div
                                style={{
                                    color: "#677283",
                                    fontStyle: "normal",
                                    fontWeight: 400,
                                    alignItems: "center",
                                    display: "flex",
                                }}
                            >
                                <FormattedMessage id="project.task.status.notStarted" />
                            </div>
                        </Select.Option>
                        <Select.Option value={1}>
                            <div
                                style={{
                                    color: "#677283",
                                    fontStyle: "normal",
                                    fontWeight: 400,
                                    alignItems: "center",
                                    display: "flex",
                                }}
                            >
                                <FormattedMessage id="projects.status.finish" />
                            </div>
                        </Select.Option>
                    </Select>
                </div>}
            >
                {/*<Spin spinning={allLoading}/>*/}
                <List
                    loading={allLoading}
                    size="small"
                    dataSource={page.data}
                    renderItem={(item: any) => (
                        <Row
                            align="middle"
                            justify="space-between"
                            style={{ cursor: "pointer", marginBottom: "20px" }}
                            onClick={() => showDetail(item)}
                        >
                            <Col span={16}>
                                <Typography.Text className="hover-color" style={{ fontSize: "14px" }}>{formatMessage({ id: item.name })}</Typography.Text>
                            </Col>
                            <Col>
                                <Typography.Text type="secondary" style={{ color: "#ADB2BA", fontSize: "12px" }}>
                                    {moment.unix(item.estimatedCompletionTime).utc().add(timeZone, "hour").format('YYYY-MM-DD')}
                                </Typography.Text>
                            </Col>
                        </Row>
                    )}
                />
                <Pagination size="small" className="text-right" showSizeChanger={false} onChange={pageChange}
                    hideOnSinglePage={true} total={page.total} current={current} />
            </Card>
            <ApprovalProcessDetail bind={detail_ref} refresh={list} />
            <UnblindingApprovalProcessDetail bind={unblinding_detail_ref} refresh={list} />
        </>
    )
};
