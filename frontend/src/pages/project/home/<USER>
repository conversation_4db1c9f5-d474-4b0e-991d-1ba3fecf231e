import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  List,
  message,
  Pagination,
  Row,
  Tooltip,
  Typography,
} from "antd";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import React, { useEffect } from "react";
import { useFetch } from "../../../hooks/request";
import { all } from "../../../api/project_dynamics";
import { useAuth } from "../../../context/auth";
import { usePage } from "../../../context/page";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import { permissions } from "../../../tools/permission";
import { Title } from "components/title";
import { useGlobal } from "../../../context/global";
import styled from "@emotion/styled";
import {AuthButton} from "../../common/auth-wrap";

export const ProjectDynamics = () => {
  const g = useGlobal();
  const intl = useTranslation();
  const { formatMessage } = intl;
  const auth = useAuth();
  const envId = auth.env ? auth.env.id : null;
  const page = usePage();
  const { runAsync: run_all, loading: allLoading } = useFetch(all, {
    manual: true,
  });
  const timeZone =
    (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8;
  const navigate = useNavigate();
  const pageChange = (pageNum: any, pageSize: any) => {
    if (pageNum === 0) {
      pageNum = 1;
    }
    page.setCurrentPage(pageNum);
  };

  const jump = (item: any) => {
    let split = item.content.split("<a>" + item.highlight + "</a>");
    let jumperFunc = () => {
      message.error(formatMessage({ id: "no.permission" }));
    };
    switch (item.typeTran) {
      case "project_dynamics_type_enter_site":
        if (
          permissions(
            auth.project.permissions,
            "operation.build.settings.user.view"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/settings/user");
          };
        }
        break;
      case "project_dynamics_type_bind_storehouse":
        if (
          permissions(
            auth.project.permissions,
            "operation.build.settings.user.view"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/settings/user");
          };
        }
        break;
      case "project_dynamics_type_role_assignment":
        if (
          permissions(
            auth.project.permissions,
            "operation.build.settings.user.view"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/settings/user");
          };
        }
        break;
      case "project_dynamics_type_overtime":
        if (
          permissions(
            auth.project.permissions,
            "operation.supply.shipment.list"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/supply/shipment");
          };
        }
        break;
      case "project_dynamics_type_overtime_recovery":
        if (
          permissions(
            auth.project.permissions,
            "operation.supply.recovery.list"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/supply/drug-recovery");
          };
        }
        break;
      case "project_dynamics_type_emergency_unblinding":
        if (
          permissions(auth.project.permissions, "operation.subject.view-list")
        ) {
          jumperFunc = () => {
            navigate("/project/sub/subject");
          };
        }
        break;
      case "project_dynamics_type_emergency_unblinding_pv":
          if (
              permissions(auth.project.permissions, "operation.subject.view-list")
          ) {
              jumperFunc = () => {
                  navigate("/project/sub/subject");
              };
          }
          break;
      case "project_dynamics_type_alert_storehouse":
        if (
          permissions(
            auth.project.permissions,
            "operation.supply.storehouse.medicine.summary"
          )
        ) {
          jumperFunc = () => {
            navigate("/project/supply/storehouse");
          };
        }
        break;
    }
    let btn = (
      <AuthButton
        show
        previewProps={{disabledClick: true}}
        style={{ border: "0px", padding: "0px" }}
        type={"link"}
        onClick={jumperFunc}
      >
        {item.highlight}
      </AuthButton>
    );
    if (item.typeTran === "project_dynamics_type_forecast") {
      btn = (
        <Tooltip
          title={
            <div>
              {item?.tooltip.map((value: any) => (
                <Row>
                  【{g.lang === "en" ? value.name_en : value.name}】{" "}
                  <FormattedMessage
                    id={"projects.sitePharmacy.forecast"}
                  ></FormattedMessage>
                  :{value.date}
                </Row>
              ))}
            </div>
          }
        >
          <Button style={{ border: "0px", padding: "0px" }} type={"link"}>
            {item.highlight}
          </Button>
        </Tooltip>
      );
    }
    return (
      <p style={{ marginBottom: "0px" }}>
        <Typography.Text>{split[0]}</Typography.Text>
        {btn}
        <Typography.Text>{split[1]}</Typography.Text>
      </p>
    );
  };

  useEffect(() => {
    run_all({
      envId: envId,
      // cohortId: cohortId,
      start: page.currentPage,
      limit: 10,
    }).then((result: any) => {
      page.setTotal(result.data.total);
      page.setData(result.data.data);
    });
  }, [page.currentPage, g.lang]);
  return (
    <>
      <Card
        headStyle={{ padding: "0 16px" }}
        bodyStyle={{ padding: "2px 6px 16px 6px" }}
        title={<Title name={formatMessage({ id: "project_dynamics", allowComponent: true })} />}
      >
        {/*<Spin spinning={allLoading}/>*/}
        <MyList
          loading={allLoading}
          dataSource={page.data}
          pagination={
            page.total > 10
              ? {
                  size: "small",
                  total: page.total,
                  pageSize: 10,
                  current: page.currentPage,
                  onChange: pageChange,
                }
              : undefined
          }
          renderItem={(item: any) => (
            <List.Item>
              <Row>
                <Col>
                  {jump(item)}
                  <Typography.Text
                    type="secondary"
                    style={{ color: "#ADB2BA" }}
                  >
                    {moment
                      .unix(item.time)
                      .utc()
                      .add(timeZone, "hour")
                      .format("YYYY-MM-DD HH:mm:ss")}
                  </Typography.Text>
                </Col>
              </Row>
            </List.Item>
          )}
        />
      </Card>
    </>
  );
};

const MyList = styled(List)`
  .ant-list-pagination {
    margin-top: 12px;

    .ant-pagination-item {
      border: none;

      &.ant-pagination-item-active {
        border: none;
        background: none;

        > a {
          color: #165dff;
        }
      }
    }
  }
`;
