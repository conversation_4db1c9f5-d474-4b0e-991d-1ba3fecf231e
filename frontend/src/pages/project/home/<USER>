import React, { useEffect } from "react";
import { Col, Row, Spin } from "antd";
import { useFetch } from "../../../hooks/request";
import { useAuth } from "../../../context/auth";
import { useSafeState } from "ahooks";
import styled from "@emotion/styled";
import { getProjectTimeZone } from "../../../api/projects";
import { useGlobal } from "../../../context/global";

import labaURL from "images/laba.png";
import {FormattedMessage, useTranslation} from "../../common/multilingual/component";


export const ProjectTimeZone = () => {
  const intl = useTranslation();
  const { formatMessage } = intl;
  const g = useGlobal();
  const auth = useAuth();
  const projectId = auth.project.id;

  const [timeZone, setTimeZone] = useSafeState<any>("");


  const { runAsync: run_getProjectTimeZone, loading: timeZoneLoading } =
    useFetch(getProjectTimeZone, { manual: true });

  const projectTimeZoneData = () => {
    //订单列表查询
    run_getProjectTimeZone({
      projectId: projectId,
    }).then((result: any) => {
      const data = result.data;
      if (data != null) {
        setTimeZone(data);
      }
    });
  };

  useEffect(projectTimeZoneData, [projectId,g.lang]);


 
  return (
    <Row justify={"end"} style={{ position: "relative",  borderRadius: "10px", }}>
      <Col>

      </Col>
      <Col style={{ position: "relative",  borderRadius: "10px", backgroundColor:"#fff" }}>
        <Spin spinning={timeZoneLoading}>
          <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
          >
            <span>
              <span style={{ marginLeft:16, marginRight: "8px", width: 30, height: 30 }}>
                <img style={{ width: 30, height: 30 }} src={labaURL} />
              </span>
              <span style={{
                fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                fontSize: "12px",
                fontWeight: 400,
                lineHeight: "16.8px",
                textAlign: "left",
                color: "#677283",
                marginRight:16,
              }}>
                <FormattedMessage id={"project.overview.timeZone.home"} />
                {timeZone}
              </span>
            </span>
          </div>
        </Spin>
      </Col>
    </Row>
  );
};

const Divider = styled.div`
  height: 40px;
  width: 0.5px;
  background: #e3e4e6;
  margin-right: 16px;
`;

const Butt = styled.div`
  font-family: "PingFang SC";
  position: absolute;
  z-index: 5;
  top: 50%;
  transform: translate(-50%, -50%);
  right: -20px;
  line-height: 20px;
  font-size: 12px;
  box-shadow: 0px 0px 12px 0px #2f313640;
  border-radius: 36px;
`;

const CohortName = styled.div`
  width: 60px; /* 固定宽度 */
  height: 45px;
  text-overflow: ellipsis; /* 省略显示 */
  //display: block;
  overflow: hidden;
`;

const CustomerCarousel = styled.div`
  .ant-carousel .slick-dots {
    visibility: hidden !important;
  }
`;
