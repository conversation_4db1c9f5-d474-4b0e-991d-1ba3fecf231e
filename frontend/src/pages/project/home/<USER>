import {Form, Row, Select, Tooltip} from "antd";
import {useFetch} from "../../../hooks/request";
import {getProjectAttribute} from "../../../api/randomization";
import {useAuth} from "../../../context/auth";
import {useEffect} from "react";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import {
    QuestionCircleFilled
} from "@ant-design/icons";
import React from "react";
import {forEach} from "lodash";

interface CohortSelectProp {
    cohortId: any;
    setCohortId: any;
    attribute: any;
    setAttribute: any;
    cohortIds: any;
}

export const CohortSelect = (props: CohortSelectProp) => {
    const {cohortId, setCohortId, setAttribute, cohortIds} = props;
    const {runAsync} = useFetch(getProjectAttribute, {manual: true});
    const auth = useAuth();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const intl = useTranslation();
    const { formatMessage } = intl;

    const getAttribute = () => {
        if (cohortIds.length === 0 || (cohortIds && cohortId)) {
            runAsync({projectId: projectId, env: envId, cohort: cohortId, customer: customerId}).then(
                (result: any) => {
                    setAttribute(result.data)
                }
            )
        }
    };

    const statusItem = (it: any) => {
        switch (it.status) {
            //1草稿 2入组 3完成入组 4停止 5入组已满
            case 1:
                return (
                    <Tooltip
                        overlayInnerStyle={{ minWidth: 30 }}
                        placement="top"
                        title={formatMessage({ id: "projects.envs.cohorts.status1" })}
                    >
                        <svg className="iconfont mouse" style={{marginBottom: -3}} width={16} height={16}>
                            <use xlinkHref="#icon-caogao"/>
                        </svg>
                    </Tooltip>
                )
            case 2:
                return (
                    <Tooltip
                        overlayInnerStyle={{ minWidth: 30 }}
                        placement="top"
                        title={formatMessage({ id: "projects.envs.cohorts.status2" })}
                    >
                        <svg className="iconfont mouse"  style={{marginBottom: -3}} width={16} height={16}>
                            <use xlinkHref="#icon-ruzu"/>
                        </svg>
                    </Tooltip>
                )
            case 3:
                return (
                    <Tooltip
                        overlayInnerStyle={{ minWidth: 60 }}
                        placement="top"
                        title={formatMessage({ id: "projects.envs.cohorts.status3" })}
                    >
                        <svg className="iconfont mouse"  style={{marginBottom: -3}} width={16} height={16}>
                            <use xlinkHref="#icon-wanchengyanjiu"/>
                        </svg>
                    </Tooltip>
                )
            case 4:
                return (
                    <Tooltip
                        overlayInnerStyle={{ width: 60 }}
                        placement="top"
                        title={formatMessage({ id: "projects.envs.cohorts.status4" })}
                    >
                        <svg className="iconfont mouse"  style={{marginBottom: -3}} width={16} height={16}>
                            <use xlinkHref="#icon-tingzhi"/>
                        </svg>
                    </Tooltip>
                )
            case 5:
                return (
                    <Tooltip
                        overlayInnerStyle={{ width: 60 }}
                        placement="top"
                        title={formatMessage({ id: "projects.envs.cohorts.status5" })}
                    >
                        <svg className="iconfont mouse"  style={{marginBottom: -3}} width={16} height={16}>
                            <use xlinkHref="#icon-ruzuyiman"/>
                        </svg>
                    </Tooltip>
                )
        }
    };


    useEffect(() => {
        if (cohortIds.length !== 0) {
            setCohortId(cohortIds[0].id)
        }
    }, [])


    useEffect(getAttribute, [cohortId])

    return <>
        {
            cohortIds.length !== 0 &&
            <Row>
                <Form.Item label={formatMessage({id:"common.name", allowComponent: true})}>
                    <Select 
                        value={cohortId} 
                        // style={{width: 200}} 
                        style={{ width: 220, marginRight: 12 }}
                        onChange={(v) => {
                            setCohortId(v)
                        }}
                        getPopupContainer={(triggerNode: any) => triggerNode.parentElement}
                    >
                        {cohortIds?.map((it: any) =>
                            <>
                                <Select.Option 
                                    value={it.id} 
                                    title={it.type ===1?it.name + " - "+it.re_random_name:it.name}
                                >
                                    {statusItem(it)}
                                    <span style={{marginLeft: 8}}> {it.type ===1?it.name + " - "+it.re_random_name:it.name}</span>
                                    {/* <div>
                                        {statusItem(it)}
                                        <span style={{marginLeft: 8}}> {it.name}</span>
                                    </div> */}
                                </Select.Option>
                            </>
                        )}
                    </Select>
                </Form.Item>
            </Row>
        }
    </>

}