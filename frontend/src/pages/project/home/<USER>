import {Card, Col, message, Row, Spin, Tooltip} from "antd";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import React, {useEffect} from "react";
import {useFetch} from "../../../hooks/request";
import {analysis} from "../../../api/project_dynamics";
import {useAuth} from "../../../context/auth";
import {useSafeState} from "ahooks";
import ReactEcharts from "echarts-for-react";
import {Title} from "components/title";
import {useGlobal} from "../../../context/global";
import {useSubject} from "./context";
import {useNavigate} from "react-router-dom";
import {useProject} from "../context";
import {subjectVisitSummary} from "../../../api/subject_visit";
import {permissions} from "../../../tools/permission";
import {clickFilter} from "../../common/auth-wrap";

export const ProjectAnalysis = () => {
    const auth = useAuth()
    const home = useSubject()
    const project = useProject()
    const intl = useTranslation();
    const { formatMessage } = intl;
    const envId = auth.env ? auth.env.id : null;
    const [orderOption, setOrderOption] = useSafeState<any>({});
    const [workOption, setWorkOption] = useSafeState<any>({});
    const [subjectVisitOption, setSubjectVisitOption] = useSafeState<any>({});

    const {runAsync: run_analysis, loading: analysisLoading} = useFetch(analysis, {manual: true})
    const {runAsync: run_subjectVisitSummary, loading: subjectVisitSummaryLoading} = useFetch(subjectVisitSummary, {manual: true})
    const g = useGlobal()

    const get_subjectVisitSummary = () => {
        run_subjectVisitSummary({
            envId: envId,
            roleId: auth.project.permissions.role_id,
            types:1,
        }).then(
            (result:any) => {

                let outSizeNotCompleted = result.data.outSizeNotCompleted
                let outSizeCompleted = result.data.outSizeCompleted
                let allOutSize = result.data.outSizeCompleted + result.data.outSizeNotCompleted
                let data = [
                    {
                        url:true,
                        value: outSizeNotCompleted,
                        name: formatMessage({id:"project.analysis.subject.visit.outSizeNotCompleted"}) +":"+ outSizeNotCompleted
                    },
                    {
                        value: outSizeCompleted,
                        name: formatMessage({id:"project.analysis.subject.visit.outSizeCompleted"}) +":"+ outSizeCompleted
                    },
                ]

                setSubjectVisitOption({
                    color: [
                        '#F96964',
                        '#165DFF',
                    ],
                    title: {
                        text: formatMessage({id:"project.analysis.subject.visit"}),
                        subtext: allOutSize !== 0? allOutSize : "-",
                        textStyle: {
                            color: '#677283',
                            fontWeight:400,
                            fontSize: 12
                        },
                        subtextStyle:{
                            color: '#1D2129',
                            fontWeight:500,
                            fontSize: 20
                        },
                        left: 'center',
                        bottom: 120,
                    },
                    tooltip: {
                        trigger: "item",
                        confine: true,
                    },
                    series: [
                        {
                            type: 'pie',
                            data: data,
                            radius: ['40%', '65%']
                        }
                    ],
                })
            }
        )
    }


    useEffect(() => {
        run_analysis({
            envId: envId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            let total = result.data.orderCount
            let todoTaskCount = result.data.todoTaskCount
            let data = [
                {
                    url:true,
                    value: result.data.orderTimeOutCount,
                    name: formatMessage({id:"project.analysis.order.timeout"}) +":"+ result.data.orderTimeOutCount
                },
                {
                    value: result.data.orderCount,
                    name: formatMessage({id:"project.analysis.order.all"}) +":"+ result.data.orderCount
                },
            ]
            let work = [
                {
                    value: result.data.workTimeOutCount,
                    name: formatMessage({id:"project.analysis.task.timeout"}) +":"+ result.data.workTimeOutCount
                },
                {
                    value: result.data.workCount,
                    name: formatMessage({id:"project.analysis.task.all"}) +":"+ result.data.workCount
                },
            ]
            if (result.data.orderTimeOutCount === 0 && result.data.orderCount === 0){
                data = []
                total = "-"
            }
            if (result.data.workTimeOutCount === 0 && result.data.workCount === 0){
                work = []
            }
            setOrderOption({
                color: [
                    '#F96964',
                    '#165DFF',
                ],
                title: {
                    text: formatMessage({id:"project.analysis.order.all"}),
                    subtext:total,
                    textStyle: {
                        color: '#677283',
                        fontWeight:400,
                        fontSize: 12
                    },
                    subtextStyle:{
                        color: '#1D2129',
                        fontWeight:500,
                        fontSize: 20
                    },
                    left: 'center',
                    bottom: 120,
                },
                tooltip: {
                    trigger: "item",
                    confine: true,
                },
                series: [
                    {
                        type: 'pie',
                        data: data,
                        radius: ['40%', '65%']
                    }
                ]
            })
            setWorkOption({
                color: [
                    '#F96964',
                    '#165DFF',
                ],
                title: {
                    text: formatMessage({id:"project.analysis.task.prepare"}),
                    subtext: todoTaskCount !== 0 ?todoTaskCount : "-",
                    textStyle: {
                        color: '#677283',
                        fontWeight:400,
                        fontSize: 12
                    },
                    subtextStyle:{
                        color: '#1D2129',
                        fontWeight:500,
                        fontSize: 20
                    },
                    left: 'center',
                    bottom: 120,
                },
                tooltip: {
                    trigger: "item",
                    confine: true,
                },
                series: [
                    {
                        type: 'pie',
                        data: work,
                        radius: ['40%', '65%']
                    }
                ]
            })
        })
        get_subjectVisitSummary()
    }, [g.lang, home.taskUpdate])
    const navigate = useNavigate();
    const clickEvent = (p:any) => {
        if (p.data.url && auth.project.info.research_attribute === 0) {
            project.setOrderStatus(10)
            navigate( "/project/supply/shipment");
        }
    }

    const clickEventVisit = (p:any) => {
        if (permissions(auth.project.permissions, "operation.project.subject.visit.cycle.view")) {
            navigate( "/project/sub/visit-cycle");
        }else{
            message.error(
                formatMessage({
                    id: "operation.subject.cohort.status.no.permission",
                })
            );
        }
    }

    const onEvents = {
        'click': clickFilter(clickEvent),
    }

    const onEventsVisit = {
        'click': clickFilter(clickEventVisit),
    }

    return (
        <>
            <Card
                headStyle={{ padding: "0 16px" }}
                bodyStyle={{ padding: "16px" }}
                title={
                <Row style={{ display: 'flex', alignItems: 'center' }}>
                <Title name={formatMessage({id: 'project.analysis', allowComponent: true})}/>
                    {/*<Tooltip title={formatMessage({ id: 'project.analysis.info' })} trigger="hover"><QuestionCircleFilled style={{marginLeft:"4px",color:"#D0D0D0"}} /></Tooltip>*/}
                    <Tooltip
                        trigger={["hover", "click"]}
                        overlayInnerStyle={{ fontSize: 12, background: "#646566" }}
                        placement="top"
                        title={
                            <FormattedMessage id="project.analysis.info" />
                        }>
                        <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4}} >
                            <use xlinkHref="#icon-xinxitishi"/>
                        </svg>
                    </Tooltip>
                </Row>

            }
            >
                <Spin spinning={analysisLoading}>
                    <Row gutter={24}>
                        <Col span={8}>
                            <ReactEcharts onEvents={onEvents} style={{flexGrow: 1}}  option={orderOption}/>
                        </Col>
                        <Col span={8}>
                            <ReactEcharts style={{flexGrow: 1}} option={workOption} />
                        </Col>
                        <Col span={8}>
                            <ReactEcharts onEvents={onEventsVisit} style={{flexGrow: 1}} option={subjectVisitOption} />
                        </Col>
                    </Row>
                </Spin>
            </Card>
        </>
    )
};
