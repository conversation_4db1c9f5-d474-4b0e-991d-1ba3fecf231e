import React from "react";
import { Card, Col, Empty, Row, Select } from "antd";
import { FormattedMessage, useTranslation } from "../../common/multilingual/component";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../context/auth";
import { useSubject } from "./context";
import { useGlobal } from "../../../context/global";
import { orderAddTaskStatus } from "../../../data/data";
import { getApprovalProcessList } from "../../../api/approval_process";
import { useFetch } from "../../../hooks/request";
import moment from "moment";
import { ApprovalProcessDetail } from "./approval_detail";
import { sitesAndStorehouses } from "../../../api/project_site";
import EmptyImg from 'images/empty.png';

// TODO 无引用，是否删除？
export const ApprovalTask = () => {
    const g = useGlobal()
    const auth = useAuth();
    const intl = useTranslation();
    const ctx = useSubject();
    const { formatMessage } = intl;
    const detail_ref = React.useRef<any>();

    const [data, setData] = useSafeState<any>([]);
    const [status, setStatus] = useSafeState(0);
    const [sites, setSites] = useSafeState<any[]>([]);
    const [institutes, setInstitutes] = useSafeState<any[]>([]);

    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const envId = auth.env.id;
    const timeZone = (auth.project.info.timeZoneStr !== undefined && auth.project.info.timeZoneStr !== null && auth.project.info.timeZoneStr !== "") ? Number(auth.project.info.timeZoneStr) : 8

    const { runAsync: run_getApprovalProcessList } = useFetch(getApprovalProcessList, { manual: true })
    const { runAsync: run_sitesAndStorehouses } = useFetch(sitesAndStorehouses, { manual: true })
    const list = () => {
        //订单列表查询
        run_getApprovalProcessList(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                // cohortId: cohortId,
                status: status,
            }).then((result: any) => {
                const data = result
                if (data != null) {
                    setData(result.data);
                }
                //setLoading(false);
            });
        //获取中心和仓库信息
        run_sitesAndStorehouses(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
            }).then(
                (result: any) => {
                    const data = result.data
                    if (data != null) {
                        let institutes: any[] = [];
                        if (data.storehouse != null) {
                            data.storehouse.forEach((value: any) => {
                                institutes.push(value)
                            });
                        }
                        if (data.site != null) {
                            setSites(data.site)
                            data.site.forEach((value: any) => {
                                institutes.push(value)
                            });
                        }
                        setInstitutes(institutes)

                    }
                }
            )
    }


    const showDetail = (item: any) => {
        detail_ref.current.show(item, sites, institutes);
    };


    // 状态onChange事件
    const statusOnChange = (value: any) => {
        setStatus(value);
        list();
    };


    React.useEffect(list, [projectId, envId, status, ctx.refresh, g.lang]);

    return (
        <React.Fragment>
            <Row>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <svg className="iconfont" width={16} height={16}>
                        <use xlinkHref="#icon-xiangmurenwu" />
                    </svg>
                    <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px" }}>{formatMessage({ id: "project.task" })}</span>
                </div>
                <div className="ver-center" style={{ marginLeft: "auto" }}>
                    <div>
                        <Select
                            style={{ width: 220, marginRight: 12, }}
                            allowClear defaultValue={0}
                            onChange={(v) => statusOnChange(v)}
                        >
                            <Select.Option value={100}><FormattedMessage id="common.all" /></Select.Option>
                            <Select.Option value={0}><FormattedMessage id="project.task.status.notStarted" /></Select.Option>
                            <Select.Option value={1}><FormattedMessage id="projects.status.finish" /></Select.Option>
                        </Select>
                    </div>
                </div>
            </Row>
            {data != null && data.length > 0 ?
                <Row gutter={[66, 44]} style={{ paddingTop: "18px" }}>
                    {
                        data?.map(
                            (item: any) => {
                                return <Col className="gutter-row" span={8} >
                                    <Card onClick={() => showDetail(item)} activeTabKey={item.id} hoverable style={{ height: "120px" }} >
                                        <Row  >
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <svg className="iconfont" width={26} height={26}>
                                                    <use xlinkHref="#icon-yanjiuzhongxindingdan" />
                                                </svg>
                                                <span style={{ marginLeft: "12px", fontSize: "16px" }}>{formatMessage({ id: item.name })}</span>
                                                <div style={{ display: 'flex', alignItems: 'center', fontSize: "12px", justifyContent: "flex-end", width: window.innerWidth >= 1600 ? 300 : window.innerWidth >= 1200 ? 200 : 100 }}>
                                                    {item.status === 0 && <div style={{ float: "right", borderRadius: "2px", background: "#f5f6f7", color: "#ADB2BA", fontStyle: "normal", lineHeight: "18px", width: g.lang === "en" ? "90px" : "60px", fontWeight: 400 }}>
                                                        <div style={{ display: 'flex', alignItems: 'center' }} >
                                                            <svg className="iconfont" width={12} height={12} style={{ marginLeft: "4px", marginRight: "4px" }}>
                                                                <use xlinkHref="#icon-weikaishi" />
                                                            </svg>{orderAddTaskStatus.find(it => (it.value === item.status))?.label}
                                                        </div>
                                                    </div>}
                                                    {item.status === 1 && <div style={{ float: "right", borderRadius: "2px", background: "#E2F7EC", color: "#41CC82", fontStyle: "normal", width: g.lang === "en" ? "90px" : "60px", fontWeight: 400 }}>
                                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                                            <svg className="iconfont" width={12} height={12} style={{ marginLeft: "4px", marginRight: "4px" }}>
                                                                <use xlinkHref="#icon-yiwancheng1" />
                                                            </svg>{orderAddTaskStatus.find(it => (it.value === item.status))?.label}
                                                        </div>
                                                    </div>}
                                                </div>
                                            </div>


                                        </Row>
                                        <Row style={{ fontSize: "12px", color: "#4E5969", paddingTop: "12px", paddingLeft: "38px" }}>
                                            <span style={{ display: 'flex', alignItems: 'center' }}>
                                                <svg className="iconfont" width={12} height={12}>
                                                    <use xlinkHref="#icon-shijian" />
                                                </svg>
                                            </span>
                                            <span style={{ marginLeft: "4px" }}> <FormattedMessage id="project.task.approval.deadline" />：
                                                {
                                                    (item.estimatedCompletionTime === null || item.estimatedCompletionTime === 0) ? '' : moment.unix(item.estimatedCompletionTime).utc().add(timeZone, "hour").format('YYYY-MM-DD')
                                                }
                                            </span>
                                        </Row>

                                    </Card>
                                </Col>
                            }
                        )
                    }
                </Row>
                : <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%', height: '100%' }}><Empty image={<img src={EmptyImg} alt='empty' />} imageStyle={{ width: "300px", height: "213px" }} /></div>
            }
            <ApprovalProcessDetail bind={detail_ref} refresh={list} />
        </React.Fragment>
    )
};

