package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// PerformanceTest MongoDB 聚合查询性能测试
type PerformanceTest struct {
	client   *mongo.Client
	database *mongo.Database
}

// TestResult 测试结果
type TestResult struct {
	TestName     string
	Duration     time.Duration
	RecordCount  int
	MemoryUsage  int64
	Success      bool
	ErrorMessage string
}

// NewPerformanceTest 创建性能测试实例
func NewPerformanceTest(uri, dbName string) (*PerformanceTest, error) {
	client, err := mongo.Connect(context.Background(), options.Client().ApplyURI(uri))
	if err != nil {
		return nil, err
	}

	database := client.Database(dbName)
	return &PerformanceTest{
		client:   client,
		database: database,
	}, nil
}

// Close 关闭连接
func (pt *PerformanceTest) Close() {
	pt.client.Disconnect(context.Background())
}

// TestOriginalQuery 测试原始查询性能
func (pt *PerformanceTest) TestOriginalQuery(subjects []primitive.ObjectID) TestResult {
	start := time.Now()
	
	match := bson.M{"_id": bson.M{"$in": subjects}, "deleted": bson.M{"$ne": true}}
	
	// 原始聚合管道
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		{{Key: "$lookup", Value: bson.M{
			"from": "history",
			"let":  bson.M{"id": "$dispensing._id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$oid", "$$id"}}}},
				bson.M{"$sort": bson.D{{"time", -1}}},
			},
			"as": "history",
		}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$lookup", Value: bson.M{"from": "region", "localField": "project_site.region_id", "foreignField": "_id", "as": "region"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
	}

	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}

	cursor, err := pt.database.Collection("subject").Aggregate(context.Background(), pipeline, opt)
	if err != nil {
		return TestResult{
			TestName:     "原始查询",
			Duration:     time.Since(start),
			Success:      false,
			ErrorMessage: err.Error(),
		}
	}
	defer cursor.Close(context.Background())

	// 使用原始的 cursor.All 方法
	var results []bson.M
	err = cursor.All(context.Background(), &results)
	if err != nil {
		return TestResult{
			TestName:     "原始查询",
			Duration:     time.Since(start),
			Success:      false,
			ErrorMessage: err.Error(),
		}
	}

	return TestResult{
		TestName:    "原始查询",
		Duration:    time.Since(start),
		RecordCount: len(results),
		Success:     true,
	}
}

// TestOptimizedQuery 测试优化后的查询性能
func (pt *PerformanceTest) TestOptimizedQuery(subjects []primitive.ObjectID) TestResult {
	start := time.Now()
	
	match := bson.M{"_id": bson.M{"$in": subjects}, "deleted": bson.M{"$ne": true}}
	
	// 优化后的聚合管道
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		// 优化：先进行 project_site lookup
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$unwind", Value: "$project_site"}},
		// 优化：使用 pipeline 进行预过滤
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing",
			"let":  bson.M{"subject_id": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
				bson.M{"$sort": bson.D{{"serial_number", 1}}},
			},
			"as": "dispensing",
		}}},
		{{Key: "$unwind", Value: "$dispensing"}},
		// 优化：region lookup 使用 pipeline
		{{Key: "$lookup", Value: bson.M{
			"from": "region",
			"let":  bson.M{"region_id": "$project_site.region_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$_id", "$$region_id"}}}},
				bson.M{"$project": bson.M{"name": 1}},
			},
			"as": "region",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
		// 优化：限制 history 数据量
		{{Key: "$lookup", Value: bson.M{
			"from": "history",
			"let":  bson.M{"dispensing_id": "$dispensing._id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$oid", "$$dispensing_id"}}}},
				bson.M{"$sort": bson.D{{"time", -1}}},
				bson.M{"$limit": 100},
				bson.M{"$project": bson.M{
					"time": 1,
					"key":  1,
					"user": 1,
					"uid":  1,
					"data": 1,
				}},
			},
			"as": "history",
		}}},
		{{Key: "$sort", Value: bson.D{{"_id", 1}}}},
	}

	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
		BatchSize:    func() *int32 { i := int32(1000); return &i }(),
	}

	cursor, err := pt.database.Collection("subject").Aggregate(context.Background(), pipeline, opt)
	if err != nil {
		return TestResult{
			TestName:     "优化查询",
			Duration:     time.Since(start),
			Success:      false,
			ErrorMessage: err.Error(),
		}
	}
	defer cursor.Close(context.Background())

	// 使用流式处理
	var results []bson.M
	batchSize := 500
	batch := make([]bson.M, 0, batchSize)

	for cursor.Next(context.Background()) {
		var item bson.M
		if err := cursor.Decode(&item); err != nil {
			return TestResult{
				TestName:     "优化查询",
				Duration:     time.Since(start),
				Success:      false,
				ErrorMessage: err.Error(),
			}
		}

		batch = append(batch, item)

		if len(batch) >= batchSize {
			results = append(results, batch...)
			batch = batch[:0]
		}
	}

	if len(batch) > 0 {
		results = append(results, batch...)
	}

	if err := cursor.Err(); err != nil {
		return TestResult{
			TestName:     "优化查询",
			Duration:     time.Since(start),
			Success:      false,
			ErrorMessage: err.Error(),
		}
	}

	return TestResult{
		TestName:    "优化查询",
		Duration:    time.Since(start),
		RecordCount: len(results),
		Success:     true,
	}
}

// RunPerformanceComparison 运行性能对比测试
func (pt *PerformanceTest) RunPerformanceComparison(subjects []primitive.ObjectID) {
	fmt.Println("=== MongoDB 聚合查询性能对比测试 ===")
	fmt.Printf("测试数据量: %d 个 subject\n", len(subjects))
	fmt.Println()

	// 测试原始查询
	fmt.Println("1. 测试原始查询性能...")
	originalResult := pt.TestOriginalQuery(subjects)
	
	// 等待一段时间，避免缓存影响
	time.Sleep(2 * time.Second)
	
	// 测试优化查询
	fmt.Println("2. 测试优化查询性能...")
	optimizedResult := pt.TestOptimizedQuery(subjects)

	// 输出结果
	fmt.Println("\n=== 测试结果 ===")
	pt.printTestResult(originalResult)
	pt.printTestResult(optimizedResult)

	// 性能对比
	if originalResult.Success && optimizedResult.Success {
		fmt.Println("\n=== 性能对比 ===")
		improvement := float64(originalResult.Duration-optimizedResult.Duration) / float64(originalResult.Duration) * 100
		fmt.Printf("性能提升: %.2f%%\n", improvement)
		fmt.Printf("时间节省: %v\n", originalResult.Duration-optimizedResult.Duration)
	}
}

// printTestResult 打印测试结果
func (pt *PerformanceTest) printTestResult(result TestResult) {
	fmt.Printf("\n%s:\n", result.TestName)
	fmt.Printf("  执行时间: %v\n", result.Duration)
	fmt.Printf("  记录数量: %d\n", result.RecordCount)
	fmt.Printf("  执行状态: %v\n", result.Success)
	if !result.Success {
		fmt.Printf("  错误信息: %s\n", result.ErrorMessage)
	}
}

// 示例用法
func main() {
	// 连接 MongoDB
	pt, err := NewPerformanceTest("mongodb://localhost:27017", "your_database_name")
	if err != nil {
		log.Fatal("连接 MongoDB 失败:", err)
	}
	defer pt.Close()

	// 准备测试数据（替换为实际的 subject IDs）
	subjects := []primitive.ObjectID{
		// 添加实际的 subject ObjectIDs
	}

	if len(subjects) == 0 {
		fmt.Println("请添加测试用的 subject IDs")
		return
	}

	// 运行性能对比测试
	pt.RunPerformanceComparison(subjects)
}
