// MongoDB 索引优化脚本
// 用于优化 report_export_dispensing.go 中聚合查询的性能

print("开始创建优化索引...");

// 1. subject 集合索引优化
print("优化 subject 集合索引...");

// 主查询索引：根据 _id 和 deleted 状态查询
db.subject.createIndex(
    {"_id": 1, "deleted": 1}, 
    {name: "idx_subject_id_deleted", background: true}
);

// project_site 关联索引
db.subject.createIndex(
    {"project_site_id": 1}, 
    {name: "idx_subject_project_site", background: true}
);

// info 字段索引：用于 unwind 和匹配 shortname
db.subject.createIndex(
    {"info.name": 1, "info.value": 1}, 
    {name: "idx_subject_info", background: true}
);

// 环境和群组索引
db.subject.createIndex(
    {"env_id": 1, "cohort_id": 1}, 
    {name: "idx_subject_env_cohort", background: true}
);

// 2. dispensing 集合索引优化
print("优化 dispensing 集合索引...");

// 主要关联索引：subject_id 和 serial_number
db.dispensing.createIndex(
    {"subject_id": 1, "serial_number": 1}, 
    {name: "idx_dispensing_subject_serial", background: true}
);

// 状态索引：用于状态过滤
db.dispensing.createIndex(
    {"status": 1}, 
    {name: "idx_dispensing_status", background: true}
);

// 复合索引：subject_id, status, serial_number
db.dispensing.createIndex(
    {"subject_id": 1, "status": 1, "serial_number": 1}, 
    {name: "idx_dispensing_subject_status_serial", background: true}
);

// 访视信息索引
db.dispensing.createIndex(
    {"visit_info.name": 1}, 
    {name: "idx_dispensing_visit", background: true}
);

// 发药时间索引
db.dispensing.createIndex(
    {"dispensing_time": 1}, 
    {name: "idx_dispensing_time", background: true}
);

// 3. project_site 集合索引优化
print("优化 project_site 集合索引...");

// 主键索引（通常已存在）
db.project_site.createIndex(
    {"_id": 1}, 
    {name: "idx_project_site_id", background: true}
);

// region 关联索引
db.project_site.createIndex(
    {"region_id": 1}, 
    {name: "idx_project_site_region", background: true}
);

// 项目和环境索引
db.project_site.createIndex(
    {"project_id": 1, "env_id": 1}, 
    {name: "idx_project_site_project_env", background: true}
);

// 中心编号索引
db.project_site.createIndex(
    {"number": 1}, 
    {name: "idx_project_site_number", background: true}
);

// 4. history 集合索引优化
print("优化 history 集合索引...");

// 主要查询索引：oid 和时间排序
db.history.createIndex(
    {"oid": 1, "time": -1}, 
    {name: "idx_history_oid_time", background: true}
);

// 用户和时间索引
db.history.createIndex(
    {"uid": 1, "time": -1}, 
    {name: "idx_history_uid_time", background: true}
);

// 操作类型索引
db.history.createIndex(
    {"key": 1}, 
    {name: "idx_history_key", background: true}
);

// 5. region 集合索引优化
print("优化 region 集合索引...");

// 主键索引（通常已存在）
db.region.createIndex(
    {"_id": 1}, 
    {name: "idx_region_id", background: true}
);

// 名称索引
db.region.createIndex(
    {"name": 1}, 
    {name: "idx_region_name", background: true}
);

// 6. attribute 集合索引优化
print("优化 attribute 集合索引...");

// 环境索引
db.attribute.createIndex(
    {"env_id": 1}, 
    {name: "idx_attribute_env", background: true}
);

// 群组索引
db.attribute.createIndex(
    {"cohort_id": 1}, 
    {name: "idx_attribute_cohort", background: true}
);

// 环境和群组复合索引
db.attribute.createIndex(
    {"env_id": 1, "cohort_id": 1}, 
    {name: "idx_attribute_env_cohort", background: true}
);

// 7. visit_cycle 集合索引优化
print("优化 visit_cycle 集合索引...");

// 环境索引
db.visit_cycle.createIndex(
    {"env_id": 1}, 
    {name: "idx_visit_cycle_env", background: true}
);

// 群组索引
db.visit_cycle.createIndex(
    {"cohort_id": 1}, 
    {name: "idx_visit_cycle_cohort", background: true}
);

// 8. form 集合索引优化
print("优化 form 集合索引...");

// 环境和群组索引
db.form.createIndex(
    {"env_id": 1, "cohort_id": 1}, 
    {name: "idx_form_env_cohort", background: true}
);

// 字段应用类型索引
db.form.createIndex(
    {"fields.application_type": 1}, 
    {name: "idx_form_fields_app_type", background: true}
);

// 9. customer_report_title 集合索引优化
print("优化 customer_report_title 集合索引...");

// 环境索引
db.customer_report_title.createIndex(
    {"env_id": 1}, 
    {name: "idx_customer_report_title_env", background: true}
);

// 群组索引
db.customer_report_title.createIndex(
    {"cohort_id": 1}, 
    {name: "idx_customer_report_title_cohort", background: true}
);

// 环境和群组复合索引
db.customer_report_title.createIndex(
    {"env_id": 1, "cohort_id": 1}, 
    {name: "idx_customer_report_title_env_cohort", background: true}
);

// 10. logistics_company_code 集合索引优化
print("优化 logistics_company_code 集合索引...");

// 代码索引
db.logistics_company_code.createIndex(
    {"code": 1}, 
    {name: "idx_logistics_company_code", background: true}
);

print("索引创建完成！");

// 检查索引创建结果
print("\n=== 索引创建结果检查 ===");

print("\nsubject 集合索引:");
db.subject.getIndexes().forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("\ndispensing 集合索引:");
db.dispensing.getIndexes().forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("\nproject_site 集合索引:");
db.project_site.getIndexes().forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("\nhistory 集合索引:");
db.history.getIndexes().forEach(function(index) {
    print("  - " + index.name + ": " + JSON.stringify(index.key));
});

print("\n索引优化完成！建议在生产环境部署前先在测试环境验证性能提升效果。");
